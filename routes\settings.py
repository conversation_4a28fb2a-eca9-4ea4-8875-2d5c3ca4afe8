from flask import Blueprint, render_template, request, jsonify, flash, redirect, url_for, send_file, current_app
from flask_login import login_required, current_user
from app import db
from models import PaymentMethod
from utils.error_logger import error_logger
import os
import json
import shutil
import sqlite3
import time
import threading
import glob
from datetime import datetime, timedelta
from werkzeug.utils import secure_filename

# محاولة استيراد مكتبة schedule
try:
    import schedule
    SCHEDULE_AVAILABLE = True
except ImportError:
    SCHEDULE_AVAILABLE = False

settings_blueprint = Blueprint('settings', __name__)

def merge_dicts(default_dict, override_dict):
    """دمج قاموسين مع الحفاظ على الهيكل الهرمي"""
    result = default_dict.copy()

    for key, value in override_dict.items():
        if key in result and isinstance(result[key], dict) and isinstance(value, dict):
            result[key] = merge_dicts(result[key], value)
        else:
            result[key] = value

    return result

# قائمة لتخزين مهام النسخ الاحتياطي المجدولة
scheduled_backups = []

# إعدادات النسخ الاحتياطي الافتراضية
default_backup_settings = {
    'auto_backup': False,
    'backup_frequency': 'daily',  # daily, weekly, monthly
    'backup_time': '00:00',
    'backup_day': 1,  # يوم الأسبوع (1-7) أو يوم الشهر (1-31)
    'backup_path': 'backups',
    'keep_backups': 5  # عدد النسخ الاحتياطية للاحتفاظ بها
}

def load_settings():
    """تحميل إعدادات النظام"""
    settings = {}

    # Business info defaults
    settings['business'] = {
        'name': 'اسم متجرك',
        'address': 'عنوان المتجر',
        'phone': 'رقم الهاتف',
        'email': 'البريد الإلكتروني',
        'tax_number': 'الرقم الضريبي',
        'website': 'الموقع الإلكتروني',
        'logo': '/static/img/nobara-logo.svg'
    }

    # Receipt settings defaults
    settings['receipt'] = {
        'header': 'شكراً لتسوقكم معنا',
        'footer': 'نتطلع لزيارتكم مرة أخرى',
        'show_tax': True,
        'show_discount': True,
        'print_duplicate': True,
        'show_logo': True,
        'show_barcode': True,
        'show_phone': True,
        'show_address': True,
        'show_tax_number': True,
        'show_customer_signature': False,
        'receipt_size': '80mm',
        'custom_width': 80,
        'custom_height': 200
    }

    # Barcode settings defaults
    settings['barcode'] = {
        'barcode_type': 'CODE128',
        'barcode_height': 50,
        'barcode_width': 2,
        'show_text': True,
        'font_size': 10,
        'margin': 10
    }

    # Backup settings defaults
    settings['backup'] = {
        'backup_path': os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'backups'),
        'auto_backup': True,
        'backup_interval': 'daily',
        'backup_count': 7
    }

    # Tax settings defaults
    settings['tax'] = {
        'enable_tax': True,
        'tax_rate': 15.0,
        'tax_inclusive': False,
        'tax_name': 'ضريبة القيمة المضافة',
        'show_tax_number': True,
        'tax_notes': '',
        'types': [
            {
                'name': 'ضريبة القيمة المضافة',
                'rate': 15.0,
                'description': 'الضريبة الأساسية على القيمة المضافة',
                'is_active': True
            }
        ]
    }

    # Display settings defaults
    settings['display'] = {
        'theme': 'light',
        'rtl': True,
        'currency': 'ج.م',
        'currency_position': 'after',
        'date_format': 'yyyy-MM-dd',
        'items_per_page': 10
    }

    # Printing settings defaults
    settings['printing'] = {
        'default_printer': '',
        'barcode_printer': '',
        'paper_size': '80mm',
        'copies': 1,
        'auto_print_receipt': True,
        'auto_print_payment': False,
        'auto_print_barcode': False,
        'show_print_preview': True,
        'print_logo': True
    }

    # Barcode settings defaults
    settings['barcode'] = {
        'barcode_type': 'CODE128',
        'barcode_width': 40,
        'barcode_height': 20,
        'show_text': True,
        'show_product_name': True,
        'show_price': True,
        'enable_scale': False,
        'scale_prefix': '20',
        'barcode_length': 13,
        'weight_position': 'end',
        'weight_digits': 5,
        'decimal_places': 3
    }

    # Backup settings defaults
    settings['backup'] = {
        'auto_backup': True,
        'backup_frequency': 'daily',
        'backup_time': '23:00',
        'backup_day': 1,
        'backup_path': 'backups',
        'keep_backups': 7
    }

    # Try to load settings from file if exists
    settings_file = os.path.join('instance', 'settings.json')
    if os.path.exists(settings_file):
        try:
            with open(settings_file, 'r', encoding='utf-8') as f:
                stored_settings = json.load(f)
                # Merge stored settings with defaults
                settings = merge_dicts(settings, stored_settings)
        except Exception as e:
            flash(f'خطأ في تحميل الإعدادات: {str(e)}', 'warning')

    return settings

@settings_blueprint.route('/settings')
@login_required
def index():
    """صفحة الإعدادات الرئيسية"""
    # التحقق من صلاحية المستخدم الحالي
    if current_user.role != 'admin':
        flash('ليس لديك صلاحية الوصول إلى الإعدادات', 'danger')
        return redirect(url_for('dashboard.home'))

    # تحميل إعدادات النظام
    settings = load_settings()

    return render_template('settings/index.html', current_user=current_user, settings=settings, now=datetime.now())

@settings_blueprint.route('/settings/store-info')
@login_required
def store_info():
    # Check if current user is admin
    if current_user.role != 'admin':
        flash('ليس لديك صلاحية الوصول إلى الإعدادات', 'danger')
        return redirect(url_for('dashboard.home'))

    settings = load_settings()
    return render_template('settings/store_info.html', current_user=current_user, settings=settings)

@settings_blueprint.route('/settings/invoice-settings')
@login_required
def invoice_settings():
    # Check if current user is admin
    if current_user.role != 'admin':
        flash('ليس لديك صلاحية الوصول إلى الإعدادات', 'danger')
        return redirect(url_for('dashboard.home'))

    settings = load_settings()
    return render_template('settings/invoice_settings.html', current_user=current_user, settings=settings, now=datetime.now())

@settings_blueprint.route('/settings/tax-settings')
@login_required
def tax_settings():
    # Check if current user is admin
    if current_user.role != 'admin':
        flash('ليس لديك صلاحية الوصول إلى الإعدادات', 'danger')
        return redirect(url_for('dashboard.home'))

    settings = load_settings()
    return render_template('settings/tax_settings.html', current_user=current_user, settings=settings)

@settings_blueprint.route('/settings/print-settings')
@login_required
def print_settings():
    # Check if current user is admin
    if current_user.role != 'admin':
        flash('ليس لديك صلاحية الوصول إلى الإعدادات', 'danger')
        return redirect(url_for('dashboard.home'))

    settings = load_settings()

    # Get available printers
    printers = []
    try:
        import win32print
        printers = [printer[2] for printer in win32print.EnumPrinters(2)]
    except:
        printers = ["Default Printer"]

    return render_template('settings/print_settings.html', current_user=current_user, settings=settings, printers=printers)

@settings_blueprint.route('/settings/barcode-settings')
@login_required
def barcode_settings():
    # Check if current user is admin
    if current_user.role != 'admin':
        flash('ليس لديك صلاحية الوصول إلى الإعدادات', 'danger')
        return redirect(url_for('dashboard.home'))

    settings = load_settings()

    # التأكد من وجود قسم barcode في الإعدادات
    if 'barcode' not in settings:
        settings['barcode'] = {
            'barcode_type': 'CODE128',
            'barcode_width': 40,
            'barcode_height': 20,
            'show_text': True,
            'show_product_name': True,
            'show_price': True,
            'show_weight': True,
            'show_date': True,
            'show_time': True,
            'show_store_name': True,
            'font_family': 'Arial',
            'font_size_main': 10,
            'font_size_product_name': 12,
            'font_size_price': 11,
            'enable_scale': False,
            'scale_prefix': '20',
            'barcode_length': 13,
            'weight_position': 'end',
            'weight_digits': 5,
            'decimal_places': 3,
            'weight_format': 'weight_x_price',
            'label_size': 'medium',
            'custom_label_width': 50,
            'custom_label_height': 30,
            'label_padding': 5,
            'label_orientation': 'landscape',
            'labels_per_row': 2,
            'labels_per_page': 10,
            'label_margin': 2
        }

    # التأكد من وجود قسم business في الإعدادات
    if 'business' not in settings:
        settings['business'] = {
            'name': 'اسم المتجر',
            'address': 'عنوان المتجر',
            'phone': 'رقم الهاتف',
            'email': 'البريد الإلكتروني',
            'tax_number': 'الرقم الضريبي',
            'website': 'الموقع الإلكتروني'
        }

    # إضافة متغير التاريخ والوقت الحالي
    now = datetime.now()

    return render_template('settings/barcode_settings.html', current_user=current_user, settings=settings, now=now)

@settings_blueprint.route('/settings/payment-methods')
@login_required
def payment_methods():
    # Check if current user is admin
    if current_user.role != 'admin':
        flash('ليس لديك صلاحية الوصول إلى الإعدادات', 'danger')
        return redirect(url_for('dashboard.home'))

    # Get payment methods
    payment_methods_list = PaymentMethod.query.all()

    # Convert payment methods to dictionaries to make them JSON serializable
    payment_methods = [method.to_dict() for method in payment_methods_list]

    return render_template('settings/payment_methods.html', current_user=current_user, payment_methods=payment_methods)

@settings_blueprint.route('/settings/backup-settings')
@login_required
def backup_settings():
    # Check if current user is admin
    if current_user.role != 'admin':
        flash('ليس لديك صلاحية الوصول إلى الإعدادات', 'danger')
        return redirect(url_for('dashboard.home'))

    settings = load_settings()

    # التأكد من وجود قسم النسخ الاحتياطي
    if 'backup' not in settings:
        settings['backup'] = default_backup_settings

    # Get backup information
    backup_path = settings['backup']['backup_path']
    if not os.path.exists(backup_path):
        os.makedirs(backup_path)

    backups = []
    backup_count = 0
    backup_size = 0
    last_backup_date = None

    for file in os.listdir(backup_path):
        if file.endswith('.db') or file.endswith('.sql'):
            file_path = os.path.join(backup_path, file)
            file_size = os.path.getsize(file_path)
            file_date = datetime.fromtimestamp(os.path.getmtime(file_path))

            backups.append({
                'filename': file,
                'name': file.split('.')[0],
                'size': file_size,
                'date': file_date
            })

            backup_count += 1
            backup_size += file_size

            if last_backup_date is None or file_date > last_backup_date:
                last_backup_date = file_date

    # Convert backup size to human readable format
    if backup_size < 1024:
        backup_size = f"{backup_size} bytes"
    elif backup_size < 1024 * 1024:
        backup_size = f"{backup_size / 1024:.2f} KB"
    elif backup_size < 1024 * 1024 * 1024:
        backup_size = f"{backup_size / (1024 * 1024):.2f} MB"
    else:
        backup_size = f"{backup_size / (1024 * 1024 * 1024):.2f} GB"

    # Sort backups by date (newest first)
    backups.sort(key=lambda x: x['date'], reverse=True)

    return render_template('settings/backup_settings.html', current_user=current_user,
                          settings=settings, backup_settings=settings['backup'], backups=backups, backup_count=backup_count,
                          backup_size=backup_size, last_backup_date=last_backup_date)

@settings_blueprint.route('/settings/external-db')
@login_required
def external_db():
    # Check if current user is admin
    if current_user.role != 'admin':
        flash('ليس لديك صلاحية الوصول إلى الإعدادات', 'danger')
        return redirect(url_for('dashboard.home'))

    settings = load_settings()

    # التأكد من وجود قسم external_db في الإعدادات
    if 'external_db' not in settings:
        settings['external_db'] = {
            'enabled': False,
            'db_type': 'mysql',
            'db_host': 'localhost',
            'db_port': '3306',
            'db_name': '',
            'db_user': '',
            'db_password': '',
            'table_prefix': '',
            'use_ssl': False,
            'sync_data': True,
            'sync_interval': 30
        }

    # تمرير الإعدادات بالشكل الصحيح
    return render_template('settings/external_db.html', current_user=current_user, settings={'database': settings['external_db']})

@settings_blueprint.route('/settings/update_external_db', methods=['POST'])
@login_required
def update_external_db():
    # Check if current user is admin
    if current_user.role != 'admin':
        return jsonify({'error': 'Unauthorized'}), 403

    try:
        # Load current settings file if exists
        settings_file = os.path.join('instance', 'settings.json')
        settings = {}
        if os.path.exists(settings_file):
            with open(settings_file, 'r', encoding='utf-8') as f:
                settings = json.load(f)

        # Create external_db section if it doesn't exist
        if 'external_db' not in settings:
            settings['external_db'] = {}

        # Update external_db settings from form data
        settings['external_db']['enabled'] = 'enable_external_db' in request.form
        settings['external_db']['db_type'] = request.form.get('db_type', 'mysql')
        settings['external_db']['db_host'] = request.form.get('db_host', '')
        settings['external_db']['db_port'] = request.form.get('db_port', '')
        settings['external_db']['db_name'] = request.form.get('db_name', '')
        settings['external_db']['db_user'] = request.form.get('db_user', '')
        settings['external_db']['db_password'] = request.form.get('db_password', '')
        settings['external_db']['table_prefix'] = request.form.get('table_prefix', '')
        settings['external_db']['use_ssl'] = 'use_ssl' in request.form
        settings['external_db']['sync_data'] = 'sync_data' in request.form
        settings['external_db']['sync_interval'] = request.form.get('sync_interval', '30')

        # Ensure instance directory exists
        os.makedirs('instance', exist_ok=True)

        # Save updated settings
        with open(settings_file, 'w', encoding='utf-8') as f:
            json.dump(settings, f, ensure_ascii=False, indent=2)

        return jsonify({'success': True})

    except Exception as e:
        return jsonify({'error': str(e)}), 500

@settings_blueprint.route('/settings/test_db_connection', methods=['POST'])
@login_required
def test_db_connection():
    """اختبار الاتصال بقاعدة البيانات الخارجية"""
    # Check if current user is admin
    if current_user.role != 'admin':
        return jsonify({'success': False, 'error': 'ليس لديك صلاحية للقيام بهذه العملية'}), 403

    try:
        # الحصول على بيانات الاتصال من الطلب
        data = request.json
        db_type = data.get('db_type', 'mysql')
        db_host = data.get('db_host', '')
        db_port = data.get('db_port', '')
        db_name = data.get('db_name', '')
        db_user = data.get('db_user', '')
        db_password = data.get('db_password', '')

        # التحقق من البيانات المطلوبة
        if not db_host or not db_name or not db_user:
            return jsonify({
                'success': False,
                'error': 'يرجى إدخال جميع البيانات المطلوبة'
            })

        # محاولة الاتصال بقاعدة البيانات
        try:
            # تحديد نوع قاعدة البيانات
            if db_type == 'mysql':
                # استيراد مكتبة MySQL
                try:
                    import pymysql
                    conn = pymysql.connect(
                        host=db_host,
                        port=int(db_port) if db_port else 3306,
                        user=db_user,
                        password=db_password,
                        database=db_name
                    )
                    conn.close()
                except ImportError:
                    return jsonify({
                        'success': False,
                        'error': 'مكتبة PyMySQL غير مثبتة. يرجى تثبيتها أولاً.'
                    })
            elif db_type == 'postgresql':
                # استيراد مكتبة PostgreSQL
                try:
                    import psycopg2
                    conn = psycopg2.connect(
                        host=db_host,
                        port=int(db_port) if db_port else 5432,
                        user=db_user,
                        password=db_password,
                        dbname=db_name
                    )
                    conn.close()
                except ImportError:
                    return jsonify({
                        'success': False,
                        'error': 'مكتبة psycopg2 غير مثبتة. يرجى تثبيتها أولاً.'
                    })
            elif db_type == 'mssql':
                # استيراد مكتبة SQL Server
                try:
                    import pyodbc
                    conn_str = f"DRIVER={{ODBC Driver 17 for SQL Server}};SERVER={db_host},{db_port if db_port else '1433'};DATABASE={db_name};UID={db_user};PWD={db_password}"
                    conn = pyodbc.connect(conn_str)
                    conn.close()
                except ImportError:
                    return jsonify({
                        'success': False,
                        'error': 'مكتبة pyodbc غير مثبتة. يرجى تثبيتها أولاً.'
                    })
            else:
                return jsonify({
                    'success': False,
                    'error': f'نوع قاعدة البيانات غير مدعوم: {db_type}'
                })

            # تم الاتصال بنجاح
            return jsonify({
                'success': True,
                'message': 'تم الاتصال بقاعدة البيانات بنجاح'
            })

        except Exception as e:
            return jsonify({
                'success': False,
                'error': f'فشل الاتصال بقاعدة البيانات: {str(e)}'
            })

    except Exception as e:
        return jsonify({
            'success': False,
            'error': f'حدث خطأ: {str(e)}'
        })

@settings_blueprint.route('/settings/license-info')
@login_required
def license_info():
    # Check if current user is admin
    if current_user.role != 'admin':
        flash('ليس لديك صلاحية الوصول إلى الإعدادات', 'danger')
        return redirect(url_for('dashboard.home'))

    # Get license information
    license_info = {
        'name': 'Nobara POS',
        'version': '1.0.0',
        'license_key': 'XXXX-XXXX-XXXX-XXXX',
        'licensed_to': 'ENG/ Fouad Saber',
        'email': '<EMAIL>',
        'phone': '01020073527',
        'expiry_date': datetime.now() + timedelta(days=365),
        'is_valid': True
    }

    return render_template('settings/license_info.html', current_user=current_user, license_info=license_info)

@settings_blueprint.route('/settings/activate_license', methods=['POST'])
@login_required
def activate_license():
    # Check if current user is admin
    if current_user.role != 'admin':
        return jsonify({'error': 'Unauthorized'}), 403

    try:
        # Get license key from form
        license_key = request.form.get('license_key', '')

        # TODO: Implement actual license activation
        # For now, just return success

        return jsonify({
            'success': True,
            'message': 'تم تفعيل الترخيص بنجاح',
            'license_info': {
                'name': 'Nobara POS',
                'version': '1.0.0',
                'license_key': license_key,
                'licensed_to': 'ENG/ Fouad Saber',
                'email': '<EMAIL>',
                'phone': '01020073527',
                'expiry_date': (datetime.now() + timedelta(days=365)).strftime('%Y-%m-%d'),
                'is_valid': True
            }
        })

    except Exception as e:
        return jsonify({'error': str(e)}), 500



@settings_blueprint.route('/settings/update', methods=['POST'])
@login_required
def update():
    # Check if current user is admin
    if current_user.role != 'admin':
        return jsonify({'error': 'Unauthorized'}), 403

    try:
        # Load current settings file if exists
        settings_file = os.path.join('instance', 'settings.json')
        settings = {}
        if os.path.exists(settings_file):
            with open(settings_file, 'r', encoding='utf-8') as f:
                settings = json.load(f)

        # Get settings section to update
        section = request.form.get('section')
        if not section:
            return jsonify({'error': 'Missing section parameter'}), 400

        # Create section if it doesn't exist
        if section not in settings:
            settings[section] = {}

        # Update settings from form data
        form_data = request.form.to_dict()
        for key, value in form_data.items():
            if key != 'section':
                # Convert boolean values
                if value.lower() in ['true', 'false']:
                    value = value.lower() == 'true'
                # Convert numeric values
                elif value.replace('.', '', 1).isdigit():
                    if '.' in value:
                        value = float(value)
                    else:
                        value = int(value)

                settings[section][key] = value

        # Ensure instance directory exists
        os.makedirs('instance', exist_ok=True)

        # Save updated settings
        with open(settings_file, 'w', encoding='utf-8') as f:
            json.dump(settings, f, ensure_ascii=False, indent=2)

        flash('تم تحديث الإعدادات بنجاح', 'success')
        return jsonify({'success': True})

    except Exception as e:
        return jsonify({'error': str(e)}), 500

@settings_blueprint.route('/settings/update_store_info', methods=['POST'])
@login_required
def update_store_info():
    # Check if current user is admin
    if current_user.role != 'admin':
        return jsonify({'error': 'Unauthorized'}), 403

    try:
        # Load current settings file if exists
        settings_file = os.path.join('instance', 'settings.json')
        settings = {}
        if os.path.exists(settings_file):
            with open(settings_file, 'r', encoding='utf-8') as f:
                settings = json.load(f)

        # Create business section if it doesn't exist
        if 'business' not in settings:
            settings['business'] = {}

        # Update business info from form data
        settings['business']['name'] = request.form.get('name', '')
        settings['business']['address'] = request.form.get('address', '')
        settings['business']['phone'] = request.form.get('phone', '')
        settings['business']['email'] = request.form.get('email', '')
        settings['business']['tax_number'] = request.form.get('tax_number', '')
        settings['business']['website'] = request.form.get('website', '')

        # Ensure instance directory exists
        os.makedirs('instance', exist_ok=True)

        # Save updated settings
        with open(settings_file, 'w', encoding='utf-8') as f:
            json.dump(settings, f, ensure_ascii=False, indent=2)

        return jsonify({'success': True})

    except Exception as e:
        return jsonify({'error': str(e)}), 500

@settings_blueprint.route('/settings/update_invoice_settings', methods=['POST'])
@login_required
def update_invoice_settings():
    # Check if current user is admin
    if current_user.role != 'admin':
        return jsonify({'error': 'Unauthorized'}), 403

    try:
        # Load current settings file if exists
        settings_file = os.path.join('instance', 'settings.json')
        settings = {}
        if os.path.exists(settings_file):
            with open(settings_file, 'r', encoding='utf-8') as f:
                settings = json.load(f)

        # Create receipt section if it doesn't exist
        if 'receipt' not in settings:
            settings['receipt'] = {}

        # Update receipt settings from form data
        settings['receipt']['header'] = request.form.get('header', '')
        settings['receipt']['footer'] = request.form.get('footer', '')
        settings['receipt']['show_tax'] = 'show_tax' in request.form
        settings['receipt']['show_discount'] = 'show_discount' in request.form
        settings['receipt']['print_duplicate'] = 'print_duplicate' in request.form
        settings['receipt']['show_logo'] = 'show_logo' in request.form
        settings['receipt']['show_barcode'] = 'show_barcode' in request.form
        settings['receipt']['show_phone'] = 'show_phone' in request.form
        settings['receipt']['show_address'] = 'show_address' in request.form
        settings['receipt']['show_tax_number'] = 'show_tax_number' in request.form
        settings['receipt']['show_customer_signature'] = 'show_customer_signature' in request.form
        settings['receipt']['receipt_size'] = request.form.get('receipt_size', '80mm')

        # إذا كان الحجم مخصصًا، قم بتخزين الأبعاد المخصصة
        if settings['receipt']['receipt_size'] == 'custom':
            try:
                settings['receipt']['custom_width'] = int(request.form.get('custom_width', 80))
                settings['receipt']['custom_height'] = int(request.form.get('custom_height', 200))
            except ValueError:
                settings['receipt']['custom_width'] = 80
                settings['receipt']['custom_height'] = 200

        # Ensure instance directory exists
        os.makedirs('instance', exist_ok=True)

        # Save updated settings
        with open(settings_file, 'w', encoding='utf-8') as f:
            json.dump(settings, f, ensure_ascii=False, indent=2)

        return jsonify({'success': True})

    except Exception as e:
        return jsonify({'error': str(e)}), 500

@settings_blueprint.route('/settings/update_tax_settings', methods=['POST'])
@login_required
def update_tax_settings():
    # Check if current user is admin
    if current_user.role != 'admin':
        return jsonify({'error': 'Unauthorized'}), 403

    try:
        # Load current settings file if exists
        settings_file = os.path.join('instance', 'settings.json')
        settings = {}
        if os.path.exists(settings_file):
            with open(settings_file, 'r', encoding='utf-8') as f:
                settings = json.load(f)

        # Create tax section if it doesn't exist
        if 'tax' not in settings:
            settings['tax'] = {}

        # Update tax types if provided
        tax_types_json = request.form.get('tax_types_json', '[]')
        try:
            tax_types = json.loads(tax_types_json)
            settings['tax']['types'] = tax_types

            # Update tax name from the first active tax type
            for tax_type in tax_types:
                if tax_type.get('is_active', False):
                    settings['tax']['tax_name'] = tax_type.get('name', 'ضريبة القيمة المضافة')
                    settings['tax']['tax_rate'] = float(tax_type.get('rate', 0))
                    break
        except Exception as e:
            print(f"Error parsing tax types: {str(e)}")

        # Ensure instance directory exists
        os.makedirs('instance', exist_ok=True)

        # Save updated settings
        with open(settings_file, 'w', encoding='utf-8') as f:
            json.dump(settings, f, ensure_ascii=False, indent=2)

        return jsonify({'success': True})

    except Exception as e:
        return jsonify({'error': str(e)}), 500

@settings_blueprint.route('/settings/upload_logo', methods=['POST'])
@login_required
def upload_logo():
    # Check if current user is admin
    if current_user.role != 'admin':
        return jsonify({'error': 'Unauthorized'}), 403

    try:
        # Check if image was uploaded
        if 'logo' not in request.files:
            return jsonify({'error': 'No file part'}), 400

        file = request.files['logo']

        # Check if file was selected
        if file.filename == '':
            return jsonify({'error': 'No selected file'}), 400

        # Check if file is allowed
        allowed_extensions = {'png', 'jpg', 'jpeg', 'gif'}
        if not file.filename.rsplit('.', 1)[1].lower() in allowed_extensions:
            return jsonify({'error': 'File type not allowed'}), 400

        # Create static/uploads directory if it doesn't exist
        upload_dir = os.path.join('static', 'uploads')
        os.makedirs(upload_dir, exist_ok=True)

        # Generate unique filename
        filename = f"logo_{datetime.now().strftime('%Y%m%d%H%M%S')}.{file.filename.rsplit('.', 1)[1].lower()}"
        filepath = os.path.join(upload_dir, filename)

        # Save file
        file.save(filepath)

        # Update settings
        settings_file = os.path.join('instance', 'settings.json')
        settings = {}
        if os.path.exists(settings_file):
            with open(settings_file, 'r', encoding='utf-8') as f:
                settings = json.load(f)

        # Create business section if it doesn't exist
        if 'business' not in settings:
            settings['business'] = {}

        # Update logo path
        settings['business']['logo'] = f'/{filepath}'

        # Save updated settings
        with open(settings_file, 'w', encoding='utf-8') as f:
            json.dump(settings, f, ensure_ascii=False, indent=2)

        return jsonify({'success': True, 'logo_path': f'/{filepath}'})

    except Exception as e:
        return jsonify({'error': str(e)}), 500

@settings_blueprint.route('/settings/update_print_settings', methods=['POST'])
@login_required
def update_print_settings():
    # Check if current user is admin
    if current_user.role != 'admin':
        return jsonify({'error': 'Unauthorized'}), 403

    try:
        # Load current settings file if exists
        settings_file = os.path.join('instance', 'settings.json')
        settings = {}
        if os.path.exists(settings_file):
            with open(settings_file, 'r', encoding='utf-8') as f:
                settings = json.load(f)

        # Create printing section if it doesn't exist
        if 'printing' not in settings:
            settings['printing'] = {}

        # Update printing settings from form data
        settings['printing']['default_printer'] = request.form.get('default_printer', '')
        settings['printing']['barcode_printer'] = request.form.get('barcode_printer', '')
        settings['printing']['paper_size'] = request.form.get('paper_size', '80mm')
        settings['printing']['copies'] = int(request.form.get('copies', 1))
        settings['printing']['auto_print_receipt'] = 'auto_print_receipt' in request.form
        settings['printing']['auto_print_payment'] = 'auto_print_payment' in request.form
        settings['printing']['auto_print_barcode'] = 'auto_print_barcode' in request.form
        settings['printing']['show_print_preview'] = 'show_print_preview' in request.form
        settings['printing']['print_logo'] = 'print_logo' in request.form

        # Ensure instance directory exists
        os.makedirs('instance', exist_ok=True)

        # Save updated settings
        with open(settings_file, 'w', encoding='utf-8') as f:
            json.dump(settings, f, ensure_ascii=False, indent=2)

        return jsonify({'success': True})

    except Exception as e:
        return jsonify({'error': str(e)}), 500

@settings_blueprint.route('/settings/update_barcode_settings', methods=['POST'])
@login_required
def update_barcode_settings():
    # Check if current user is admin
    if current_user.role != 'admin':
        return jsonify({'error': 'Unauthorized'}), 403

    try:
        # Load current settings file if exists
        settings_file = os.path.join('instance', 'settings.json')
        settings = {}
        if os.path.exists(settings_file):
            with open(settings_file, 'r', encoding='utf-8') as f:
                settings = json.load(f)

        # Create barcode section if it doesn't exist
        if 'barcode' not in settings:
            settings['barcode'] = {}

        # Update barcode settings from form data
        settings['barcode']['barcode_type'] = request.form.get('barcode_type', 'CODE128')
        settings['barcode']['barcode_width'] = int(request.form.get('barcode_width', 40))
        settings['barcode']['barcode_height'] = int(request.form.get('barcode_height', 20))
        settings['barcode']['show_text'] = 'show_text' in request.form
        settings['barcode']['show_product_name'] = 'show_product_name' in request.form
        settings['barcode']['show_price'] = 'show_price' in request.form
        settings['barcode']['show_weight'] = 'show_weight' in request.form
        settings['barcode']['show_date'] = 'show_date' in request.form
        settings['barcode']['show_time'] = 'show_time' in request.form
        settings['barcode']['show_store_name'] = 'show_store_name' in request.form

        # إعدادات الخط
        settings['barcode']['font_family'] = request.form.get('font_family', 'Arial')
        settings['barcode']['font_size_main'] = int(request.form.get('font_size_main', 10))
        settings['barcode']['font_size_product_name'] = int(request.form.get('font_size_product_name', 12))
        settings['barcode']['font_size_price'] = int(request.form.get('font_size_price', 11))

        # إعدادات الميزان
        settings['barcode']['enable_scale'] = 'enable_scale' in request.form
        settings['barcode']['scale_prefix'] = request.form.get('scale_prefix', '20')
        settings['barcode']['barcode_length'] = int(request.form.get('barcode_length', 13))
        settings['barcode']['weight_position'] = request.form.get('weight_position', 'end')
        settings['barcode']['weight_digits'] = int(request.form.get('weight_digits', 5))
        settings['barcode']['decimal_places'] = int(request.form.get('decimal_places', 3))
        settings['barcode']['weight_format'] = request.form.get('weight_format', 'weight_x_price')

        # إعدادات مقاس الليبل
        settings['barcode']['label_size'] = request.form.get('label_size', 'medium')

        # إذا كان المقاس مخصصًا، قم بتخزين الأبعاد المخصصة
        if settings['barcode']['label_size'] == 'custom':
            try:
                settings['barcode']['custom_label_width'] = int(request.form.get('custom_label_width', 50))
                settings['barcode']['custom_label_height'] = int(request.form.get('custom_label_height', 30))
            except ValueError:
                settings['barcode']['custom_label_width'] = 50
                settings['barcode']['custom_label_height'] = 30

        settings['barcode']['label_padding'] = int(request.form.get('label_padding', 5))
        settings['barcode']['label_orientation'] = request.form.get('label_orientation', 'landscape')
        settings['barcode']['labels_per_row'] = int(request.form.get('labels_per_row', 2))
        settings['barcode']['labels_per_page'] = int(request.form.get('labels_per_page', 10))
        settings['barcode']['label_margin'] = int(request.form.get('label_margin', 2))

        # Ensure instance directory exists
        os.makedirs('instance', exist_ok=True)

        # Save updated settings
        with open(settings_file, 'w', encoding='utf-8') as f:
            json.dump(settings, f, ensure_ascii=False, indent=2)

        return jsonify({'success': True})

    except Exception as e:
        return jsonify({'error': str(e)}), 500

@settings_blueprint.route('/settings/test_receipt_print')
@login_required
def test_receipt_print():
    # Check if current user is admin
    if current_user.role != 'admin':
        return jsonify({'error': 'Unauthorized'}), 403

    try:
        # Load settings
        settings = load_settings()

        # Get printer name
        printer_name = settings['printing']['default_printer']

        # Create test receipt content
        receipt_content = """
        ===== اختبار الطباعة =====

        اسم المتجر: {}
        التاريخ: {}

        هذا اختبار للطباعة

        شكراً لاستخدامك نظام نوبارا
        """.format(settings['business']['name'], datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'))

        # TODO: Implement actual printing functionality
        # For now, just return success

        return jsonify({'success': True, 'message': 'تم إرسال اختبار الطباعة بنجاح'})

    except Exception as e:
        return jsonify({'error': str(e)}), 500

@settings_blueprint.route('/settings/test_barcode_print')
@login_required
def test_barcode_print():
    # Check if current user is admin
    if current_user.role != 'admin':
        return jsonify({'error': 'Unauthorized'}), 403

    try:
        # Load settings
        settings = load_settings()

        # Get printer name
        printer_name = settings['printing']['barcode_printer'] or settings['printing']['default_printer']

        # TODO: Implement actual barcode printing functionality
        # For now, just return success

        return jsonify({'success': True, 'message': 'تم إرسال اختبار طباعة الباركود بنجاح'})

    except Exception as e:
        return jsonify({'error': str(e)}), 500

@settings_blueprint.route('/settings/refresh_printers')
@login_required
def refresh_printers():
    # Check if current user is admin
    if current_user.role != 'admin':
        return jsonify({'error': 'Unauthorized'}), 403

    try:
        # Get available printers
        printers = []
        try:
            import win32print
            printers = [printer[2] for printer in win32print.EnumPrinters(2)]
        except:
            printers = ["Default Printer"]

        return jsonify({'success': True, 'printers': printers})

    except Exception as e:
        return jsonify({'error': str(e)}), 500

@settings_blueprint.route('/settings/connect_scale', methods=['POST'])
@login_required
def connect_scale():
    """الاتصال بميزان الباركود"""
    if current_user.role != 'admin':
        return jsonify({'success': False, 'message': 'ليس لديك صلاحية للقيام بهذه العملية'})

    try:
        port = request.form.get('port', 'COM1')
        baud_rate = int(request.form.get('baud_rate', 9600))

        # هنا يمكن إضافة كود الاتصال بالميزان الفعلي
        # مثال:
        # import serial
        # ser = serial.Serial(port=port, baudrate=baud_rate, timeout=1)
        # if ser.is_open:
        #     ser.close()
        # ser.open()

        # للتجربة، نفترض أن الاتصال نجح
        # في التطبيق الفعلي، يجب التحقق من الاتصال بالميزان

        # حفظ إعدادات الاتصال في ملف الإعدادات
        settings = load_settings()
        if 'scale_connection' not in settings:
            settings['scale_connection'] = {}

        settings['scale_connection']['port'] = port
        settings['scale_connection']['baud_rate'] = baud_rate
        settings['scale_connection']['is_connected'] = True

        # حفظ الإعدادات
        settings_file = os.path.join('instance', 'settings.json')
        with open(settings_file, 'w', encoding='utf-8') as f:
            json.dump(settings, f, ensure_ascii=False, indent=4)

        return jsonify({
            'success': True,
            'message': 'تم الاتصال بالميزان بنجاح'
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'فشل الاتصال بالميزان: {str(e)}'
        })

@settings_blueprint.route('/settings/test_scale')
@login_required
def test_scale():
    """اختبار قراءة الميزان"""
    if current_user.role != 'admin':
        return jsonify({'success': False, 'message': 'ليس لديك صلاحية للقيام بهذه العملية'})

    try:
        # تحميل إعدادات الاتصال بالميزان
        settings = load_settings()
        if 'scale_connection' not in settings or not settings['scale_connection'].get('is_connected', False):
            return jsonify({
                'success': False,
                'message': 'الميزان غير متصل. يرجى الاتصال بالميزان أولاً.'
            })

        # هنا يمكن إضافة كود قراءة البيانات من الميزان الفعلي
        # مثال:
        # import serial
        # port = settings['scale_connection']['port']
        # baud_rate = settings['scale_connection']['baud_rate']
        # ser = serial.Serial(port=port, baudrate=baud_rate, timeout=1)
        # data = ser.readline().decode('utf-8').strip()

        # للتجربة، نقوم بإنشاء بيانات وهمية
        import random

        # إنشاء وزن عشوائي بين 0.1 و 5 كجم
        weight = round(random.uniform(0.1, 5), 3)

        # سعر وهمي للكيلو
        price_per_kg = 35.0

        # حساب الإجمالي
        total = round(weight * price_per_kg, 2)

        # إنشاء باركود وهمي للميزان
        # الصيغة: بادئة الميزان (2 رقم) + كود المنتج (4 أرقام) + الوزن (5 أرقام) + رقم التحقق (1 رقم)
        product_code = '1234'  # كود المنتج
        weight_digits = str(int(weight * 1000)).zfill(5)  # تحويل الوزن إلى أرقام (بالجرام)
        barcode = f"20{product_code}{weight_digits}0"  # 0 هو رقم التحقق الوهمي

        return jsonify({
            'success': True,
            'data': {
                'weight': str(weight),
                'price': str(price_per_kg),
                'total': str(total),
                'barcode': barcode
            }
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'فشل اختبار الميزان: {str(e)}'
        })

@settings_blueprint.route('/settings/generate_barcode_sample')
@login_required
def generate_barcode_sample():
    # Check if current user is admin
    if current_user.role != 'admin':
        return jsonify({'error': 'Unauthorized'}), 403

    try:
        # Load settings
        settings = load_settings()

        # TODO: Implement barcode generation
        # For now, just return success

        return jsonify({'success': True, 'barcode_url': '/static/img/barcode-sample.png'})

    except Exception as e:
        return jsonify({'error': str(e)}), 500

@settings_blueprint.route('/settings/add_payment_method', methods=['POST'])
@login_required
def add_payment_method():
    # Check if current user is admin
    if current_user.role != 'admin':
        return jsonify({'error': 'Unauthorized'}), 403

    try:
        # Get form data
        name = request.form.get('name')
        code = request.form.get('code')
        description = request.form.get('description', '')
        icon = request.form.get('icon', 'ri-money-dollar-circle-line')
        color = request.form.get('color', 'blue')
        is_active = request.form.get('is_active') == '1'
        requires_reference = request.form.get('requires_reference') == '1'

        # خصائص المعاملات المالية
        payment_category = request.form.get('payment_category', 'cash')
        affects_cash_register = request.form.get('affects_cash_register') == '1'
        is_credit = request.form.get('is_credit') == '1'
        allow_partial_payment = request.form.get('allow_partial_payment') == '1'
        requires_approval = request.form.get('requires_approval') == '1'

        # Validate required fields
        if not name or not code:
            return jsonify({'error': 'Name and code are required'}), 400

        # Check if code already exists
        existing_method = PaymentMethod.query.filter_by(code=code).first()
        if existing_method:
            return jsonify({'error': 'Payment method code already exists'}), 400

        # Create new payment method
        payment_method = PaymentMethod(
            name=name,
            code=code,
            description=description,
            icon=icon,
            color=color,
            is_active=is_active,
            is_default=False,
            requires_reference=requires_reference,
            payment_category=payment_category,
            affects_cash_register=affects_cash_register,
            is_credit=is_credit,
            allow_partial_payment=allow_partial_payment,
            requires_approval=requires_approval
        )

        # Save to database
        db.session.add(payment_method)
        db.session.commit()

        return jsonify({'success': True, 'message': 'Payment method added successfully'})

    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 500

@settings_blueprint.route('/settings/update_payment_method/<int:payment_method_id>', methods=['PUT'])
@login_required
def update_payment_method(payment_method_id):
    # Check if current user is admin
    if current_user.role != 'admin':
        return jsonify({'error': 'Unauthorized'}), 403

    try:
        # Get payment method
        payment_method = PaymentMethod.query.get(payment_method_id)
        if not payment_method:
            return jsonify({'error': 'Payment method not found'}), 404

        # Get form data
        name = request.form.get('name')
        code = request.form.get('code')
        description = request.form.get('description', '')
        icon = request.form.get('icon', 'ri-money-dollar-circle-line')
        color = request.form.get('color', 'blue')
        is_active = request.form.get('is_active') == '1'
        requires_reference = request.form.get('requires_reference') == '1'

        # خصائص المعاملات المالية
        payment_category = request.form.get('payment_category', 'cash')
        affects_cash_register = request.form.get('affects_cash_register') == '1'
        is_credit = request.form.get('is_credit') == '1'
        allow_partial_payment = request.form.get('allow_partial_payment') == '1'
        requires_approval = request.form.get('requires_approval') == '1'

        # Validate required fields
        if not name or not code:
            return jsonify({'error': 'Name and code are required'}), 400

        # Check if code already exists (excluding current method)
        existing_method = PaymentMethod.query.filter(PaymentMethod.code == code, PaymentMethod.id != payment_method_id).first()
        if existing_method:
            return jsonify({'error': 'Payment method code already exists'}), 400

        # Update payment method
        payment_method.name = name
        payment_method.code = code
        payment_method.description = description
        payment_method.icon = icon
        payment_method.color = color
        payment_method.is_active = is_active
        payment_method.requires_reference = requires_reference

        # تحديث خصائص المعاملات المالية
        payment_method.payment_category = payment_category
        payment_method.affects_cash_register = affects_cash_register
        payment_method.is_credit = is_credit
        payment_method.allow_partial_payment = allow_partial_payment
        payment_method.requires_approval = requires_approval

        # Save to database
        db.session.commit()

        return jsonify({'success': True, 'message': 'Payment method updated successfully'})

    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 500

@settings_blueprint.route('/settings/delete_payment_method/<int:payment_method_id>', methods=['DELETE'])
@login_required
def delete_payment_method(payment_method_id):
    # Check if current user is admin
    if current_user.role != 'admin':
        return jsonify({'error': 'Unauthorized'}), 403

    try:
        # Get payment method
        payment_method = PaymentMethod.query.get(payment_method_id)
        if not payment_method:
            return jsonify({'error': 'Payment method not found'}), 404

        # Check if it's a default payment method
        if payment_method.is_default:
            return jsonify({'error': 'Cannot delete default payment method'}), 400

        # Delete payment method
        db.session.delete(payment_method)
        db.session.commit()

        return jsonify({'success': True, 'message': 'Payment method deleted successfully'})

    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 500

@settings_blueprint.route('/settings/set_default_payment_method/<int:payment_method_id>', methods=['POST'])
@login_required
def set_default_payment_method(payment_method_id):
    # Check if current user is admin
    if current_user.role != 'admin':
        return jsonify({'error': 'Unauthorized'}), 403

    try:
        # Get payment method
        payment_method = PaymentMethod.query.get(payment_method_id)
        if not payment_method:
            return jsonify({'error': 'Payment method not found'}), 404

        # Reset all default payment methods
        default_methods = PaymentMethod.query.filter_by(is_default=True).all()
        for method in default_methods:
            method.is_default = False

        # Set new default payment method
        payment_method.is_default = True

        # Save to database
        db.session.commit()

        return jsonify({'success': True, 'message': 'Default payment method set successfully'})

    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 500

@settings_blueprint.route('/settings/backup')
@login_required
def backup():
    # Check if current user is admin
    if current_user.role != 'admin':
        flash('ليس لديك صلاحية إجراء النسخ الاحتياطي', 'danger')
        return redirect(url_for('settings.index'))

    # تحميل إعدادات النسخ الاحتياطي
    backup_settings = load_backup_settings()

    # الحصول على قائمة النسخ الاحتياطية الموجودة
    backup_files = get_backup_files(backup_settings['backup_path'])

    return render_template('settings/backup.html',
                          current_user=current_user,
                          backup_settings=backup_settings,
                          backup_files=backup_files)

@settings_blueprint.route('/settings/backup/create', methods=['POST'])
@login_required
def create_backup():
    # Check if current user is admin
    if current_user.role != 'admin':
        return jsonify({'error': 'ليس لديك صلاحية إجراء النسخ الاحتياطي'}), 403

    try:
        # تحميل إعدادات النسخ الاحتياطي
        backup_settings = load_backup_settings()

        # إنشاء نسخة احتياطية
        backup_file = create_db_backup(backup_settings['backup_path'])

        # حذف النسخ الاحتياطية القديمة إذا تجاوز العدد المحدد
        cleanup_old_backups(backup_settings['backup_path'], backup_settings['keep_backups'])

        return jsonify({
            'success': True,
            'message': 'تم إنشاء النسخة الاحتياطية بنجاح',
            'backup_file': os.path.basename(backup_file)
        })

    except Exception as e:
        return jsonify({'error': str(e)}), 500

@settings_blueprint.route('/settings/backup/download/<filename>')
@login_required
def download_backup(filename):
    # Check if current user is admin
    if current_user.role != 'admin':
        flash('ليس لديك صلاحية تنزيل النسخ الاحتياطية', 'danger')
        return redirect(url_for('settings.backup'))

    # تحميل إعدادات النسخ الاحتياطي
    backup_settings = load_backup_settings()

    # التحقق من وجود الملف
    backup_path = os.path.join(backup_settings['backup_path'], filename)
    if not os.path.exists(backup_path):
        flash('ملف النسخة الاحتياطية غير موجود', 'danger')
        return redirect(url_for('settings.backup'))

    return send_file(backup_path, as_attachment=True)

@settings_blueprint.route('/settings/backup/delete/<filename>', methods=['POST'])
@login_required
def delete_backup(filename):
    # Check if current user is admin
    if current_user.role != 'admin':
        return jsonify({'error': 'ليس لديك صلاحية حذف النسخ الاحتياطية'}), 403

    try:
        # تحميل إعدادات النسخ الاحتياطي
        backup_settings = load_backup_settings()

        # التحقق من وجود الملف
        backup_path = os.path.join(backup_settings['backup_path'], filename)
        if not os.path.exists(backup_path):
            return jsonify({'error': 'ملف النسخة الاحتياطية غير موجود'}), 404

        # حذف الملف
        os.remove(backup_path)

        return jsonify({
            'success': True,
            'message': 'تم حذف النسخة الاحتياطية بنجاح'
        })

    except Exception as e:
        return jsonify({'error': str(e)}), 500

@settings_blueprint.route('/settings/backup/update', methods=['POST'])
@login_required
def update_backup_settings():
    # Check if current user is admin
    if current_user.role != 'admin':
        return jsonify({'error': 'ليس لديك صلاحية تحديث إعدادات النسخ الاحتياطي'}), 403

    try:
        # تحميل إعدادات النسخ الاحتياطي الحالية
        backup_settings = load_backup_settings()

        # تحديث الإعدادات من البيانات المرسلة
        backup_settings['auto_backup'] = 'auto_backup' in request.form
        backup_settings['backup_frequency'] = request.form.get('backup_frequency', 'daily')
        backup_settings['backup_time'] = request.form.get('backup_time', '00:00')
        backup_settings['backup_day'] = int(request.form.get('backup_day', 1))
        backup_settings['backup_path'] = request.form.get('backup_path', 'backups')
        backup_settings['keep_backups'] = int(request.form.get('keep_backups', 5))

        # التأكد من وجود مجلد النسخ الاحتياطية
        os.makedirs(backup_settings['backup_path'], exist_ok=True)

        # حفظ الإعدادات
        save_backup_settings(backup_settings)

        # إعادة جدولة النسخ الاحتياطي التلقائي
        schedule_auto_backup(backup_settings)

        return jsonify({
            'success': True,
            'message': 'تم تحديث إعدادات النسخ الاحتياطي بنجاح'
        })

    except Exception as e:
        return jsonify({'error': str(e)}), 500

@settings_blueprint.route('/settings/backup/restore', methods=['POST'])
@login_required
def restore_backup():
    # Check if current user is admin
    if current_user.role != 'admin':
        return jsonify({'error': 'ليس لديك صلاحية استعادة النسخ الاحتياطية'}), 403

    try:
        # التحقق من وجود ملف
        if 'backup_file' not in request.files:
            return jsonify({'error': 'لم يتم تحديد ملف النسخة الاحتياطية'}), 400

        file = request.files['backup_file']

        # التحقق من اختيار ملف
        if file.filename == '':
            return jsonify({'error': 'لم يتم تحديد ملف النسخة الاحتياطية'}), 400

        # التحقق من نوع الملف
        if not file.filename.endswith('.db'):
            return jsonify({'error': 'نوع الملف غير صالح، يجب أن يكون ملف قاعدة بيانات SQLite'}), 400

        # حفظ الملف مؤقتًا
        temp_path = os.path.join('instance', 'temp_restore.db')
        file.save(temp_path)

        # التحقق من صحة ملف قاعدة البيانات
        try:
            conn = sqlite3.connect(temp_path)
            cursor = conn.cursor()
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
            tables = cursor.fetchall()
            conn.close()

            if not tables:
                os.remove(temp_path)
                return jsonify({'error': 'ملف قاعدة البيانات غير صالح أو فارغ'}), 400
        except:
            os.remove(temp_path)
            return jsonify({'error': 'ملف قاعدة البيانات غير صالح'}), 400

        # إنشاء نسخة احتياطية قبل الاستعادة
        backup_settings = load_backup_settings()
        create_db_backup(backup_settings['backup_path'], 'pre_restore')

        # استعادة قاعدة البيانات
        db_path = 'instance/fouad-pos.db'

        # إغلاق اتصالات قاعدة البيانات
        db.session.close()
        db.engine.dispose()

        # نسخ ملف الاستعادة إلى مكان قاعدة البيانات الأصلية
        shutil.copy2(temp_path, db_path)

        # حذف الملف المؤقت
        os.remove(temp_path)

        return jsonify({
            'success': True,
            'message': 'تم استعادة قاعدة البيانات بنجاح، سيتم إعادة تشغيل التطبيق'
        })

    except Exception as e:
        return jsonify({'error': str(e)}), 500

def load_settings():
    """تحميل إعدادات النظام"""
    settings = {}

    # Business info defaults
    settings['business'] = {
        'name': 'اسم متجرك',
        'address': 'عنوان المتجر',
        'phone': 'رقم الهاتف',
        'email': 'البريد الإلكتروني',
        'tax_number': 'الرقم الضريبي',
        'website': 'الموقع الإلكتروني',
        'logo': '/static/img/nobara-logo.svg'
    }

    # Receipt settings defaults
    settings['receipt'] = {
        'header': 'شكراً لتسوقكم معنا',
        'footer': 'نتطلع لزيارتكم مرة أخرى',
        'show_tax': True,
        'show_discount': True,
        'print_duplicate': True,
        'show_phone': True,
        'show_address': True,
        'show_logo': True,
        'show_barcode': True,
        'receipt_size': '80mm'
    }

    # Tax settings defaults
    settings['tax'] = {
        'enable_tax': True,
        'tax_rate': 15.0,
        'tax_inclusive': False,
        'tax_name': 'ضريبة القيمة المضافة'
    }

    # Display settings defaults
    settings['display'] = {
        'theme': 'light',
        'rtl': True,
        'currency': 'ج.م',
        'currency_position': 'after',
        'date_format': 'yyyy-MM-dd',
        'items_per_page': 10
    }

    # Try to load settings from file if exists
    settings_file = os.path.join('instance', 'settings.json')
    if os.path.exists(settings_file):
        try:
            with open(settings_file, 'r', encoding='utf-8') as f:
                stored_settings = json.load(f)
                # Merge stored settings with defaults
                settings = merge_dicts(settings, stored_settings)
        except Exception as e:
            flash(f'خطأ في تحميل الإعدادات: {str(e)}', 'warning')

    return settings

def get_available_backups(backup_path):
    """الحصول على قائمة النسخ الاحتياطية المتاحة"""
    backups = []

    # التأكد من وجود مجلد النسخ الاحتياطية
    os.makedirs(backup_path, exist_ok=True)

    # البحث عن ملفات النسخ الاحتياطية
    backup_files = glob.glob(os.path.join(backup_path, '*.db'))

    for file_path in backup_files:
        filename = os.path.basename(file_path)
        file_stats = os.stat(file_path)

        # تحويل حجم الملف إلى صيغة مقروءة
        size_bytes = file_stats.st_size
        if size_bytes < 1024:
            size_str = f"{size_bytes} بايت"
        elif size_bytes < 1024 * 1024:
            size_str = f"{size_bytes / 1024:.2f} كيلوبايت"
        else:
            size_str = f"{size_bytes / (1024 * 1024):.2f} ميجابايت"

        # تحويل تاريخ التعديل
        mod_time = datetime.fromtimestamp(file_stats.st_mtime)
        date_str = mod_time.strftime('%Y-%m-%d %H:%M:%S')

        backups.append({
            'filename': filename,
            'path': file_path,
            'size': size_str,
            'date': date_str,
            'timestamp': file_stats.st_mtime
        })

    # ترتيب النسخ الاحتياطية حسب التاريخ (الأحدث أولاً)
    backups.sort(key=lambda x: x['timestamp'], reverse=True)

    return backups

def load_backup_settings():
    """تحميل إعدادات النسخ الاحتياطي"""
    settings_file = os.path.join('instance', 'settings.json')
    settings = {}

    if os.path.exists(settings_file):
        try:
            with open(settings_file, 'r', encoding='utf-8') as f:
                settings = json.load(f)
        except:
            pass

    # التأكد من وجود قسم النسخ الاحتياطي
    if 'backup' not in settings:
        settings['backup'] = default_backup_settings
    else:
        # دمج الإعدادات الافتراضية مع الإعدادات المخزنة
        for key, value in default_backup_settings.items():
            if key not in settings['backup']:
                settings['backup'][key] = value

    # التأكد من وجود مجلد النسخ الاحتياطية
    os.makedirs(settings['backup']['backup_path'], exist_ok=True)

    return settings['backup']

def save_backup_settings(backup_settings):
    """حفظ إعدادات النسخ الاحتياطي"""
    settings_file = os.path.join('instance', 'settings.json')
    settings = {}

    if os.path.exists(settings_file):
        try:
            with open(settings_file, 'r', encoding='utf-8') as f:
                settings = json.load(f)
        except:
            pass

    # تحديث إعدادات النسخ الاحتياطي
    settings['backup'] = backup_settings

    # حفظ الإعدادات
    with open(settings_file, 'w', encoding='utf-8') as f:
        json.dump(settings, f, ensure_ascii=False, indent=2)

def create_db_backup(backup_dir, prefix='auto'):
    """إنشاء نسخة احتياطية من قاعدة البيانات"""
    # التأكد من وجود مجلد النسخ الاحتياطية
    os.makedirs(backup_dir, exist_ok=True)

    # مسار قاعدة البيانات الأصلية
    db_path = 'instance/fouad-pos.db'

    # اسم ملف النسخة الاحتياطية
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    backup_filename = f"{prefix}_backup_{timestamp}.db"
    backup_path = os.path.join(backup_dir, backup_filename)

    # نسخ قاعدة البيانات
    shutil.copy2(db_path, backup_path)

    return backup_path

def get_backup_files(backup_dir):
    """الحصول على قائمة ملفات النسخ الاحتياطية"""
    # التأكد من وجود مجلد النسخ الاحتياطية
    os.makedirs(backup_dir, exist_ok=True)

    # الحصول على قائمة الملفات
    backup_files = []
    for filename in os.listdir(backup_dir):
        if filename.endswith('.db'):
            file_path = os.path.join(backup_dir, filename)
            file_size = os.path.getsize(file_path)
            file_date = datetime.fromtimestamp(os.path.getmtime(file_path))

            backup_files.append({
                'filename': filename,
                'size': format_size(file_size),
                'date': file_date.strftime('%Y-%m-%d %H:%M:%S')
            })

    # ترتيب الملفات حسب التاريخ (الأحدث أولاً)
    backup_files.sort(key=lambda x: x['date'], reverse=True)

    return backup_files

def format_size(size_bytes):
    """تنسيق حجم الملف"""
    if size_bytes < 1024:
        return f"{size_bytes} بايت"
    elif size_bytes < 1024 * 1024:
        return f"{size_bytes / 1024:.2f} كيلوبايت"
    elif size_bytes < 1024 * 1024 * 1024:
        return f"{size_bytes / (1024 * 1024):.2f} ميجابايت"
    else:
        return f"{size_bytes / (1024 * 1024 * 1024):.2f} جيجابايت"

def cleanup_old_backups(backup_dir, keep_count):
    """حذف النسخ الاحتياطية القديمة"""
    # الحصول على قائمة ملفات النسخ الاحتياطية
    backup_files = []
    for filename in os.listdir(backup_dir):
        if filename.endswith('.db'):
            file_path = os.path.join(backup_dir, filename)
            backup_files.append((file_path, os.path.getmtime(file_path)))

    # ترتيب الملفات حسب التاريخ (الأقدم أولاً)
    backup_files.sort(key=lambda x: x[1])

    # حذف الملفات القديمة إذا تجاوز العدد المحدد
    if len(backup_files) > keep_count:
        for file_path, _ in backup_files[:-keep_count]:
            os.remove(file_path)

def schedule_auto_backup(backup_settings):
    """جدولة النسخ الاحتياطي التلقائي"""
    global scheduled_backups

    # التحقق من وجود مكتبة schedule
    if not SCHEDULE_AVAILABLE:
        current_app.logger.warning("مكتبة schedule غير متوفرة. لن يتم تفعيل النسخ الاحتياطي التلقائي.")
        return

    # إلغاء المهام المجدولة السابقة
    for job in scheduled_backups:
        schedule.cancel_job(job)
    scheduled_backups = []

    # إذا كان النسخ الاحتياطي التلقائي مفعل
    if backup_settings['auto_backup']:
        # تحديد وقت النسخ الاحتياطي
        backup_time = backup_settings['backup_time']

        # إنشاء وظيفة النسخ الاحتياطي
        def auto_backup_job():
            try:
                # إنشاء نسخة احتياطية
                create_db_backup(backup_settings['backup_path'])

                # حذف النسخ الاحتياطية القديمة
                cleanup_old_backups(backup_settings['backup_path'], backup_settings['keep_backups'])

                # تسجيل النسخ الاحتياطي في السجل
                current_app.logger.info(f"تم إنشاء نسخة احتياطية تلقائية في {datetime.now()}")
            except Exception as e:
                current_app.logger.error(f"خطأ أثناء إنشاء النسخة الاحتياطية التلقائية: {str(e)}")

        # جدولة النسخ الاحتياطي حسب التكرار
        if backup_settings['backup_frequency'] == 'daily':
            job = schedule.every().day.at(backup_time).do(auto_backup_job)
            scheduled_backups.append(job)
        elif backup_settings['backup_frequency'] == 'weekly':
            days = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday']
            day = days[backup_settings['backup_day'] - 1]
            job = getattr(schedule.every(), day).at(backup_time).do(auto_backup_job)
            scheduled_backups.append(job)
        elif backup_settings['backup_frequency'] == 'monthly':
            # جدولة النسخ الاحتياطي الشهري (سيتم التحقق من اليوم في الوظيفة)
            def monthly_backup_check():
                # التحقق من اليوم الحالي
                if datetime.now().day == backup_settings['backup_day']:
                    auto_backup_job()

            job = schedule.every().day.at(backup_time).do(monthly_backup_check)
            scheduled_backups.append(job)

        current_app.logger.info(f"تم جدولة النسخ الاحتياطي التلقائي ({backup_settings['backup_frequency']})")

        # بدء خيط لتشغيل المهام المجدولة يتم في app.py


@settings_blueprint.route('/settings/error-logs')
@login_required
def error_logs():
    """عرض سجلات الأخطاء"""
    # Check if current user is admin
    if current_user.role != 'admin':
        flash('ليس لديك صلاحية الوصول إلى سجلات الأخطاء', 'danger')
        return redirect(url_for('dashboard.home'))

    # الحصول على معلمات التصفية
    search = request.args.get('search', '')
    start_date = request.args.get('start_date', '')
    end_date = request.args.get('end_date', '')
    log_type = request.args.get('log_type', '')
    source = request.args.get('source', '')
    user_id = request.args.get('user_id', '')
    ip_address = request.args.get('ip_address', '')
    module = request.args.get('module', '')
    sort_by = request.args.get('sort_by', 'timestamp')
    sort_order = request.args.get('sort_order', 'desc')
    limit = int(request.args.get('limit', 100))
    page = int(request.args.get('page', 1))
    offset = (page - 1) * limit

    # الحصول على سجلات الأخطاء
    logs = error_logger.get_error_logs(
        limit=limit,
        offset=offset,
        search=search,
        start_date=start_date,
        end_date=end_date,
        log_type=log_type,
        source=source,
        user_id=user_id,
        ip_address=ip_address,
        module=module,
        sort_by=sort_by,
        sort_order=sort_order
    )

    # الحصول على إجمالي عدد السجلات (للتصفح)
    total_logs = len(error_logger.get_error_logs(
        search=search,
        start_date=start_date,
        end_date=end_date,
        log_type=log_type,
        source=source,
        user_id=user_id,
        ip_address=ip_address,
        module=module
    ))

    # حساب عدد الصفحات
    total_pages = (total_logs + limit - 1) // limit

    # الحصول على إحصائيات الأخطاء
    error_count = len(error_logger.get_error_logs(log_type='error'))
    warning_count = len(error_logger.get_error_logs(log_type='warning'))
    info_count = len(error_logger.get_error_logs(log_type='info'))
    critical_count = len(error_logger.get_error_logs(log_type='critical'))

    # حساب النسب المئوية
    total_count = error_count + warning_count + info_count + critical_count
    if total_count > 0:
        error_percent = (error_count / total_count) * 100
        warning_percent = (warning_count / total_count) * 100
        info_percent = (info_count / total_count) * 100
    else:
        error_percent = warning_percent = info_percent = 0

    # الحصول على آخر تحديث
    last_update = datetime.now().strftime('%Y-%m-%d %H:%M:%S')

    # الحصول على قائمة المستخدمين والوحدات للفلترة
    from models import User
    users = User.query.all()

    # استخراج الوحدات من السجلات
    modules = set()
    for log in error_logger.get_error_logs(limit=1000):
        if log.get('module'):
            modules.add(log.get('module'))

    return render_template(
        'settings/error_logs.html',
        current_user=current_user,
        logs=logs,
        search=search,
        start_date=start_date,
        end_date=end_date,
        log_type=log_type,
        source=source,
        user_id=user_id,
        ip_address=ip_address,
        module=module,
        sort_by=sort_by,
        sort_order=sort_order,
        limit=limit,
        page=page,
        total_pages=total_pages,
        total_logs=total_logs,
        error_count=error_count,
        warning_count=warning_count,
        info_count=info_count,
        critical_count=critical_count,
        error_percent=error_percent,
        warning_percent=warning_percent,
        info_percent=info_percent,
        last_update=last_update,
        users=users,
        modules=modules
    )


@settings_blueprint.route('/settings/clear-error-logs', methods=['POST'])
@login_required
def clear_error_logs():
    """مسح سجلات الأخطاء"""
    # Check if current user is admin
    if current_user.role != 'admin':
        return jsonify({'success': False, 'error': 'ليس لديك صلاحية مسح سجلات الأخطاء'}), 403

    try:
        # مسح سجلات الأخطاء
        success = error_logger.clear_error_logs()

        if success:
            # تسجيل النشاط
            if hasattr(current_user, 'log_activity'):
                current_user.log_activity(
                    action='clear_error_logs',
                    module='settings',
                    description='تم مسح سجلات الأخطاء',
                    ip_address=request.remote_addr
                )

            return jsonify({'success': True})
        else:
            return jsonify({'success': False, 'error': 'فشل مسح سجلات الأخطاء'})

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})


@settings_blueprint.route('/settings/download-error-logs')
@login_required
def download_error_logs():
    """تنزيل سجلات الأخطاء"""
    # Check if current user is admin
    if current_user.role != 'admin':
        flash('ليس لديك صلاحية تنزيل سجلات الأخطاء', 'danger')
        return redirect(url_for('dashboard.home'))

    try:
        # الحصول على معلمات التصفية
        search = request.args.get('search', '')
        start_date = request.args.get('start_date', '')
        end_date = request.args.get('end_date', '')
        log_type = request.args.get('log_type', '')
        format = request.args.get('format', 'csv')  # csv أو json

        # الحصول على سجلات الأخطاء (بدون حد)
        logs = error_logger.get_error_logs(
            limit=10000,  # حد كبير لتضمين معظم السجلات
            offset=0,
            search=search,
            start_date=start_date,
            end_date=end_date,
            log_type=log_type
        )

        # تنسيق التاريخ للاستخدام في اسم الملف
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')

        if format == 'json':
            # تصدير بتنسيق JSON
            output = json.dumps(logs, ensure_ascii=False, indent=2)
            mimetype = 'application/json'
            filename = f'error_logs_{timestamp}.json'
        else:
            # تصدير بتنسيق CSV
            output = StringIO()
            writer = csv.writer(output)

            # كتابة الترويسة
            writer.writerow(['التاريخ والوقت', 'النوع', 'الرسالة', 'التفاصيل'])

            # كتابة البيانات
            for log in logs:
                writer.writerow([
                    log['timestamp'],
                    log['level'],
                    log['message'],
                    '\n'.join(log['details'])
                ])

            output = output.getvalue()
            mimetype = 'text/csv'
            filename = f'error_logs_{timestamp}.csv'

        # تسجيل النشاط
        if hasattr(current_user, 'log_activity'):
            current_user.log_activity(
                action='download_error_logs',
                module='settings',
                description=f'تم تنزيل سجلات الأخطاء بتنسيق {format}',
                ip_address=request.remote_addr
            )

        # إرجاع الملف للتنزيل
        return Response(
            output,
            mimetype=mimetype,
            headers={
                'Content-Disposition': f'attachment; filename={filename}',
                'Content-Type': f'{mimetype}; charset=utf-8'
            }
        )

    except Exception as e:
        flash(f'حدث خطأ أثناء تنزيل سجلات الأخطاء: {str(e)}', 'danger')
        return redirect(url_for('settings.error_logs'))

