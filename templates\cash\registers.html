{% extends 'base.html' %}

{% block title %}إدارة الخزائن{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0 text-gray-800">إدارة الخزائن</h1>
        <div>
            <a href="{{ url_for('cash.cash_index') }}" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> العودة
            </a>
            <a href="{{ url_for('cash.add_cash_register') }}" class="btn btn-primary">
                <i class="fas fa-plus"></i> إضافة خزينة
            </a>
        </div>
    </div>

    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">الخزائن</h6>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-bordered" id="dataTable" width="100%" cellspacing="0">
                    <thead>
                        <tr>
                            <th>#</th>
                            <th>الاسم</th>
                            <th>الوصف</th>
                            <th>الرصيد الحالي</th>
                            <th>الحالة</th>
                            <th>تاريخ الإنشاء</th>
                            <th>آخر تحديث</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for register in cash_registers %}
                        <tr>
                            <td>{{ register.id }}</td>
                            <td>{{ register.name }}</td>
                            <td>{{ register.description }}</td>
                            <td>{{ register.current_balance|round(2) }}</td>
                            <td>
                                {% if register.is_active %}
                                <span class="badge badge-success">نشط</span>
                                {% else %}
                                <span class="badge badge-danger">غير نشط</span>
                                {% endif %}
                                {% if register.is_default %}
                                <span class="badge badge-info">افتراضي</span>
                                {% endif %}
                            </td>
                            <td>{{ register.created_at.strftime('%Y-%m-%d %H:%M') }}</td>
                            <td>{{ register.updated_at.strftime('%Y-%m-%d %H:%M') }}</td>
                            <td>
                                <a href="{{ url_for('cash.edit_cash_register', register_id=register.id) }}" class="btn btn-sm btn-info">
                                    <i class="fas fa-edit"></i> تعديل
                                </a>
                                {% if not register.transactions and not register.shifts %}
                                <button type="button" class="btn btn-sm btn-danger" data-toggle="modal" data-target="#deleteModal{{ register.id }}">
                                    <i class="fas fa-trash"></i> حذف
                                </button>
                                {% endif %}
                            </td>
                        </tr>

                        <!-- Delete Modal -->
                        <div class="modal fade" id="deleteModal{{ register.id }}" tabindex="-1" role="dialog" aria-labelledby="deleteModalLabel{{ register.id }}" aria-hidden="true">
                            <div class="modal-dialog" role="document">
                                <div class="modal-content">
                                    <div class="modal-header">
                                        <h5 class="modal-title" id="deleteModalLabel{{ register.id }}">تأكيد الحذف</h5>
                                        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                            <span aria-hidden="true">&times;</span>
                                        </button>
                                    </div>
                                    <div class="modal-body">
                                        هل أنت متأكد من حذف الخزينة "{{ register.name }}"؟
                                    </div>
                                    <div class="modal-footer">
                                        <button type="button" class="btn btn-secondary" data-dismiss="modal">إلغاء</button>
                                        <form action="{{ url_for('cash.delete_cash_register', register_id=register.id) }}" method="post">
                                            <button type="submit" class="btn btn-danger">حذف</button>
                                        </form>
                                    </div>
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    $(document).ready(function() {
        $('#dataTable').DataTable({
            "language": {
                "url": "//cdn.datatables.net/plug-ins/1.10.24/i18n/Arabic.json"
            }
        });
    });
</script>
{% endblock %}
