<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تقرير المبيعات التفصيلي - نظام إدارة المبيعات</title>
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <!-- Remixicon -->
    <link href="https://cdn.jsdelivr.net/npm/remixicon@2.5.0/fonts/remixicon.css" rel="stylesheet">
    <style>
        .report-card {
            transition: all 0.3s ease;
        }
        .report-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 15px rgba(0, 0, 0, 0.1);
        }
    </style>
</head>
<body class="bg-gray-50">
    <div class="flex h-screen overflow-hidden">
        <!-- Sidebar -->
        {% include 'partials/sidebar.html' %}

        <!-- Main Content -->
        <div class="flex-1 flex flex-col overflow-hidden">
            <!-- Top Navigation -->
            {% include 'partials/topnav.html' %}

            <!-- Content Area -->
            <main class="flex-1 overflow-y-auto p-6">
                <div class="mb-6 flex items-center justify-between">
                    <div>
                        <h1 class="text-2xl font-bold text-gray-800">تقرير المبيعات التفصيلي</h1>
                        <p class="text-gray-600">عرض تفاصيل جميع المبيعات والطلبات</p>
                    </div>
                    <div class="flex space-x-2 space-x-reverse">
                        <a href="{{ url_for('reports.sales_report') }}" class="px-4 py-2 bg-indigo-100 text-indigo-700 rounded-md hover:bg-indigo-200 transition-all">
                            <i class="ri-bar-chart-line mr-1"></i>
                            تقرير المبيعات الملخص
                        </a>
                        <a href="{{ url_for('reports.sales_index') }}" class="px-4 py-2 bg-blue-100 text-blue-700 rounded-md hover:bg-blue-200 transition-all">
                            <i class="ri-arrow-right-line"></i>
                            العودة لتقارير المبيعات
                        </a>
                        <a href="{{ url_for('reports.index') }}" class="px-4 py-2 bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200 transition-all">
                            <i class="ri-home-line"></i>
                            الرئيسية
                        </a>
                    </div>
                </div>

                <!-- Filters -->
                <div class="bg-white p-4 rounded-lg shadow-sm mb-6">
                    <form action="{{ url_for('reports.sales_detailed_report') }}" method="get" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
                        <div>
                            <label for="date_from" class="block text-sm font-medium text-gray-700 mb-1">من تاريخ</label>
                            <input type="date" id="date_from" name="date_from" value="{{ date_from }}"
                                   class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500">
                        </div>
                        <div>
                            <label for="date_to" class="block text-sm font-medium text-gray-700 mb-1">إلى تاريخ</label>
                            <input type="date" id="date_to" name="date_to" value="{{ date_to }}"
                                   class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500">
                        </div>
                        <div>
                            <label for="payment_method" class="block text-sm font-medium text-gray-700 mb-1">طريقة الدفع</label>
                            <select id="payment_method" name="payment_method" class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500">
                                <option value="">الكل</option>
                                {% for method in available_payment_methods %}
                                <option value="{{ method }}" {% if method == payment_method %}selected{% endif %}>
                                    {% if method == 'cash' %}نقدي{% elif method == 'card' %}بطاقة{% else %}{{ method }}{% endif %}
                                </option>
                                {% endfor %}
                            </select>
                        </div>
                        <div>
                            <label for="status" class="block text-sm font-medium text-gray-700 mb-1">الحالة</label>
                            <select id="status" name="status" class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500">
                                <option value="">الكل</option>
                                {% for s in available_statuses %}
                                <option value="{{ s }}" {% if s == status %}selected{% endif %}>
                                    {% if s == 'completed' %}مكتمل{% elif s == 'pending' %}معلق{% elif s == 'cancelled' %}ملغي{% else %}{{ s }}{% endif %}
                                </option>
                                {% endfor %}
                            </select>
                        </div>
                        <div>
                            <label for="customer_id" class="block text-sm font-medium text-gray-700 mb-1">العميل</label>
                            <select id="customer_id" name="customer_id" class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500">
                                <option value="">الكل</option>
                                {% for customer in customers %}
                                <option value="{{ customer.id }}" {% if customer.id|string == customer_id %}selected{% endif %}>{{ customer.name }}</option>
                                {% endfor %}
                            </select>
                        </div>
                        <div>
                            <label for="product_id" class="block text-sm font-medium text-gray-700 mb-1">المنتج</label>
                            <select id="product_id" name="product_id" class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500">
                                <option value="">الكل</option>
                                {% for product in products %}
                                <option value="{{ product.id }}" {% if product.id|string == product_id %}selected{% endif %}>{{ product.name }}</option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="flex items-end">
                            <button type="submit" class="bg-indigo-600 text-white px-4 py-2 rounded-md hover:bg-indigo-700 transition-all">
                                <i class="ri-filter-3-line ml-1"></i>
                                تطبيق الفلتر
                            </button>
                        </div>
                        <div class="flex items-end">
                            <a href="{{ url_for('reports.export_sales_report', date_from=date_from, date_to=date_to, payment_method=payment_method, status=status, export_type='detailed') }}"
                               class="bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700 transition-all">
                                <i class="ri-file-excel-line ml-1"></i>
                                تصدير التقرير
                            </a>
                        </div>
                    </form>
                </div>

                <!-- Orders Table -->
                <div class="bg-white rounded-lg shadow-sm overflow-hidden">
                    <h2 class="text-lg font-bold text-gray-800 p-6 border-b">تفاصيل الطلبات</h2>
                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        رقم الفاتورة
                                    </th>
                                    <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        التاريخ
                                    </th>
                                    <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        العميل
                                    </th>
                                    <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        طريقة الدفع
                                    </th>
                                    <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        الحالة
                                    </th>
                                    <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        الإجمالي
                                    </th>
                                    <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        التفاصيل
                                    </th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                {% for order in orders.items %}
                                <tr class="hover:bg-gray-50">
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                        {{ order.invoice_number }}
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                        {{ order.created_at.strftime('%Y-%m-%d %H:%M') }}
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                        {{ order.customer.name if order.customer else 'عميل عادي' }}
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                        {% if order.payment_method == 'cash' %}
                                            نقدي
                                        {% elif order.payment_method == 'card' %}
                                            بطاقة
                                        {% else %}
                                            {{ order.payment_method }}
                                        {% endif %}
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm">
                                        {% if order.status == 'completed' %}
                                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">
                                                مكتمل
                                            </span>
                                        {% elif order.status == 'pending' %}
                                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-yellow-100 text-yellow-800">
                                                معلق
                                            </span>
                                        {% elif order.status == 'cancelled' %}
                                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 text-red-800">
                                                ملغي
                                            </span>
                                        {% else %}
                                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-gray-100 text-gray-800">
                                                {{ order.status }}
                                            </span>
                                        {% endif %}
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                        {{ "%.2f"|format(order.total) }} ج.م
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                        <button type="button" onclick="toggleOrderDetails({{ order.id }})" class="text-indigo-600 hover:text-indigo-900">
                                            <i class="ri-eye-line"></i> عرض
                                        </button>
                                    </td>
                                </tr>
                                <!-- Order Details Row -->
                                <tr id="order-details-{{ order.id }}" class="hidden bg-gray-50">
                                    <td colspan="7" class="px-6 py-4">
                                        <div class="border-t border-b border-gray-200 py-3">
                                            <h4 class="font-medium text-gray-700 mb-2">تفاصيل الطلب #{{ order.invoice_number }}</h4>
                                            <div class="grid grid-cols-4 gap-4 mb-3">
                                                <div>
                                                    <span class="text-xs text-gray-500 block">المجموع الفرعي</span>
                                                    <span class="font-medium">{{ "%.2f"|format(order.subtotal) }} ج.م</span>
                                                </div>
                                                <div>
                                                    <span class="text-xs text-gray-500 block">الخصم</span>
                                                    <span class="font-medium">{{ "%.2f"|format(order.discount) }} ج.م</span>
                                                </div>
                                                <div>
                                                    <span class="text-xs text-gray-500 block">الضريبة</span>
                                                    <span class="font-medium">{{ "%.2f"|format(order.tax) }} ج.م</span>
                                                </div>
                                                <div>
                                                    <span class="text-xs text-gray-500 block">الإجمالي</span>
                                                    <span class="font-medium text-indigo-600">{{ "%.2f"|format(order.total) }} ج.م</span>
                                                </div>
                                            </div>

                                            <!-- Order Items -->
                                            <div class="mt-3">
                                                <h5 class="text-sm font-medium text-gray-700 mb-2">المنتجات</h5>
                                                <table class="min-w-full divide-y divide-gray-200">
                                                    <thead class="bg-gray-100">
                                                        <tr>
                                                            <th scope="col" class="px-3 py-2 text-right text-xs font-medium text-gray-500">
                                                                المنتج
                                                            </th>
                                                            <th scope="col" class="px-3 py-2 text-right text-xs font-medium text-gray-500">
                                                                الكمية
                                                            </th>
                                                            <th scope="col" class="px-3 py-2 text-right text-xs font-medium text-gray-500">
                                                                السعر
                                                            </th>
                                                            <th scope="col" class="px-3 py-2 text-right text-xs font-medium text-gray-500">
                                                                الإجمالي
                                                            </th>
                                                        </tr>
                                                    </thead>
                                                    <tbody class="bg-white divide-y divide-gray-200">
                                                        {% for item in order.items %}
                                                        <tr>
                                                            <td class="px-3 py-2 text-xs text-gray-500">
                                                                {{ item.product.name }}
                                                            </td>
                                                            <td class="px-3 py-2 text-xs text-gray-500">
                                                                {{ item.quantity }}
                                                            </td>
                                                            <td class="px-3 py-2 text-xs text-gray-500">
                                                                {{ "%.2f"|format(item.price) }} ج.م
                                                            </td>
                                                            <td class="px-3 py-2 text-xs text-gray-500">
                                                                {{ "%.2f"|format(item.total) }} ج.م
                                                            </td>
                                                        </tr>
                                                        {% endfor %}
                                                    </tbody>
                                                </table>
                                            </div>
                                        </div>
                                    </td>
                                </tr>
                                {% else %}
                                <tr>
                                    <td colspan="7" class="px-6 py-4 text-center text-gray-500">
                                        لا توجد طلبات للفترة المحددة
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    {% if orders.pages > 1 %}
                    <div class="px-6 py-4 bg-gray-50 border-t border-gray-200">
                        <div class="flex items-center justify-between">
                            <div class="text-sm text-gray-500">
                                عرض {{ orders.items|length }} من إجمالي {{ orders.total }} طلب
                            </div>
                            <div class="flex space-x-1 space-x-reverse">
                                {% if orders.has_prev %}
                                <a href="{{ url_for('reports.sales_detailed_report', page=orders.prev_num, date_from=date_from, date_to=date_to, payment_method=payment_method, status=status, customer_id=customer_id, product_id=product_id) }}"
                                   class="px-3 py-1 bg-white border border-gray-300 rounded-md text-sm text-gray-700 hover:bg-gray-50">
                                    السابق
                                </a>
                                {% endif %}

                                {% for page_num in orders.iter_pages(left_edge=1, right_edge=1, left_current=2, right_current=2) %}
                                    {% if page_num %}
                                        {% if page_num == orders.page %}
                                        <span class="px-3 py-1 bg-indigo-600 border border-indigo-600 rounded-md text-sm text-white">
                                            {{ page_num }}
                                        </span>
                                        {% else %}
                                        <a href="{{ url_for('reports.sales_detailed_report', page=page_num, date_from=date_from, date_to=date_to, payment_method=payment_method, status=status, customer_id=customer_id, product_id=product_id) }}"
                                           class="px-3 py-1 bg-white border border-gray-300 rounded-md text-sm text-gray-700 hover:bg-gray-50">
                                            {{ page_num }}
                                        </a>
                                        {% endif %}
                                    {% else %}
                                        <span class="px-3 py-1 text-gray-500">...</span>
                                    {% endif %}
                                {% endfor %}

                                {% if orders.has_next %}
                                <a href="{{ url_for('reports.sales_detailed_report', page=orders.next_num, date_from=date_from, date_to=date_to, payment_method=payment_method, status=status, customer_id=customer_id, product_id=product_id) }}"
                                   class="px-3 py-1 bg-white border border-gray-300 rounded-md text-sm text-gray-700 hover:bg-gray-50">
                                    التالي
                                </a>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    {% endif %}
                </div>
            </main>
        </div>
    </div>

    <script>
        function toggleOrderDetails(orderId) {
            const detailsRow = document.getElementById(`order-details-${orderId}`);
            if (detailsRow.classList.contains('hidden')) {
                detailsRow.classList.remove('hidden');
            } else {
                detailsRow.classList.add('hidden');
            }
        }
    </script>
</body>
</html>
