from flask import Blueprint, jsonify, request
from models import Product, Customer, Supplier, Order, OrderItem, Purchase, Category, Warehouse, Inventory, Notification
from app import db
from sqlalchemy import func, and_, or_
from datetime import datetime, timedelta
from flask_login import current_user
import logging
import os
import json

# إعداد التسجيل
logger = logging.getLogger(__name__)

api_blueprint = Blueprint('api', __name__)

@api_blueprint.route('/api/products/search', methods=['GET'])
def search_products():
    """البحث عن المنتجات بالاسم أو الباركود"""
    query = request.args.get('query', '')

    if not query:
        return jsonify([])

    # البحث في الاسم أو الباركود
    products = Product.query.filter(
        (Product.name.ilike(f'%{query}%')) |
        (Product.barcode == query)
    ).limit(10).all()

    return jsonify([{
        'id': product.id,
        'name': product.name,
        'barcode': product.barcode,
        'price': product.price,
        'cost_price': product.cost_price,
        'stock_quantity': product.stock_quantity
    } for product in products])

@api_blueprint.route('/api/products/barcode/<barcode>', methods=['GET'])
def get_product_by_barcode(barcode):
    """الحصول على منتج بواسطة الباركود"""
    product = Product.query.filter_by(barcode=barcode).first()

    if not product:
        return jsonify({'error': 'المنتج غير موجود'}), 404

    return jsonify({
        'id': product.id,
        'name': product.name,
        'barcode': product.barcode,
        'price': product.price,
        'cost_price': product.cost_price,
        'stock_quantity': product.stock_quantity
    })

@api_blueprint.route('/api/customers/search', methods=['GET'])
def search_customers():
    """البحث عن العملاء بالاسم أو رقم الهاتف"""
    query = request.args.get('query', '')

    if not query:
        return jsonify([])

    # البحث في الاسم أو رقم الهاتف
    customers = Customer.query.filter(
        (Customer.name.ilike(f'%{query}%')) |
        (Customer.phone.ilike(f'%{query}%'))
    ).limit(10).all()

    return jsonify([{
        'id': customer.id,
        'name': customer.name,
        'phone': customer.phone,
        'email': customer.email
    } for customer in customers])

@api_blueprint.route('/api/suppliers/search', methods=['GET'])
def search_suppliers():
    """البحث عن الموردين بالاسم أو رقم الهاتف"""
    query = request.args.get('query', '')

    if not query:
        return jsonify([])

    # البحث في الاسم أو رقم الهاتف
    suppliers = Supplier.query.filter(
        (Supplier.name.ilike(f'%{query}%')) |
        (Supplier.phone.ilike(f'%{query}%'))
    ).limit(10).all()

    return jsonify([{
        'id': supplier.id,
        'name': supplier.name,
        'phone': supplier.phone,
        'email': supplier.email
    } for supplier in suppliers])

@api_blueprint.route('/api/dashboard', methods=['GET'])
def dashboard_data():
    """الحصول على بيانات لوحة التحكم"""
    try:
        # بيانات المبيعات اليومية للوحة التحكم
        today = datetime.now().date()
        today_start = datetime.combine(today, datetime.min.time())
        today_end = datetime.combine(today, datetime.max.time())

        # إجمالي المبيعات اليوم
        daily_sales = db.session.query(func.sum(Order.total)).filter(
            Order.created_at.between(today_start, today_end),
            Order.status == 'completed'
        ).scalar() or 0

        # عدد الطلبات اليوم
        daily_orders_count = db.session.query(func.count(Order.id)).filter(
            Order.created_at.between(today_start, today_end),
            Order.status == 'completed'
        ).scalar() or 0

        # إجمالي المبيعات هذا الأسبوع
        week_start = today - timedelta(days=today.weekday())
        week_start = datetime.combine(week_start, datetime.min.time())
        week_end = today_end

        weekly_sales = db.session.query(func.sum(Order.total)).filter(
            Order.created_at.between(week_start, week_end),
            Order.status == 'completed'
        ).scalar() or 0

        # عدد الطلبات هذا الأسبوع
        weekly_orders_count = db.session.query(func.count(Order.id)).filter(
            Order.created_at.between(week_start, week_end),
            Order.status == 'completed'
        ).scalar() or 0

        # إجمالي المبيعات هذا الشهر
        month_start = datetime(today.year, today.month, 1)
        month_start = datetime.combine(month_start, datetime.min.time())
        month_end = today_end

        monthly_sales = db.session.query(func.sum(Order.total)).filter(
            Order.created_at.between(month_start, month_end),
            Order.status == 'completed'
        ).scalar() or 0

        # عدد الطلبات هذا الشهر
        monthly_orders_count = db.session.query(func.count(Order.id)).filter(
            Order.created_at.between(month_start, month_end),
            Order.status == 'completed'
        ).scalar() or 0

        # إجمالي المبيعات منذ البداية
        total_sales = db.session.query(func.sum(Order.total)).filter(
            Order.status == 'completed'
        ).scalar() or 0

        # إجمالي عدد الطلبات
        total_orders = db.session.query(func.count(Order.id)).filter(
            Order.status == 'completed'
        ).scalar() or 0

        # مشتريات اليوم
        today_purchases = db.session.query(func.sum(Purchase.total)).filter(
            Purchase.created_at.between(today_start, today_end),
            Purchase.status == 'received'
        ).scalar() or 0

        # إجمالي المشتريات
        total_purchases = db.session.query(func.sum(Purchase.total)).filter(
            Purchase.status == 'received'
        ).scalar() or 0

        # حساب الأرباح (افتراضي للعرض)
        # في التطبيق الحقيقي يجب حساب الربح بناءً على سعر التكلفة وسعر البيع
        profit = total_sales * 0.25  # افتراض أن هامش الربح 25%

        # المنتجات منخفضة المخزون وغير المتوفرة
        products = Product.query.filter_by(is_active=True).all()
        low_stock_products = 0
        out_of_stock_products = 0
        total_products = len(products)
        active_products = total_products  # All products are active by our filter

        low_stock_products_list = []

        for product in products:
            if product.stock_quantity <= 0:
                out_of_stock_products += 1
            elif product.stock_quantity <= product.minimum_stock:
                low_stock_products += 1
                low_stock_products_list.append({
                    'name': product.name,
                    'barcode': product.barcode,
                    'warehouse': 'المخزن الرئيسي',  # يمكن تحديث هذا لاحقًا
                    'quantity': product.stock_quantity,
                    'min_quantity': product.minimum_stock
                })

        # عدد العملاء
        customers_count = db.session.query(func.count(Customer.id)).scalar() or 0

        # عدد الموردين
        suppliers_count = db.session.query(func.count(Supplier.id)).scalar() or 0

        # عدد المخازن
        warehouses_count = db.session.query(func.count(Warehouse.id)).filter(
            Warehouse.is_active == True
        ).scalar() or 0

        # آخر المبيعات
        recent_sales = Order.query.filter(
            Order.status == 'completed'
        ).order_by(Order.created_at.desc()).limit(10).all()

        recent_sales_list = []
        for sale in recent_sales:
            customer_name = sale.customer.name if sale.customer else 'عميل نقدي'
            recent_sales_list.append({
                'invoice_number': sale.invoice_number,
                'customer_name': customer_name,
                'total': float(sale.total),
                'status': sale.status,
                'date': sale.created_at.strftime('%Y-%m-%d')
            })

        # المنتجات الأكثر مبيعاً
        top_products_query = db.session.query(
            Product,
            func.sum(OrderItem.quantity).label('total_sold'),
            func.sum(OrderItem.total).label('total_sales')
        ).join(OrderItem).group_by(Product).order_by(func.sum(OrderItem.quantity).desc()).limit(5)

        top_products_list = []
        for product, total_sold, total_sales in top_products_query:
            category_name = product.category.name if product.category else 'بدون تصنيف'
            top_products_list.append({
                'name': product.name,
                'category': category_name,
                'price': float(product.price),
                'total_sold': total_sold,
                'total_sales': float(total_sales)
            })

        # بيانات الرسم البياني للمبيعات
        days = 7  # عدد الأيام الافتراضي
        end_date = today
        start_date = end_date - timedelta(days=days-1)
        date_range = [(start_date + timedelta(days=i)) for i in range(days)]

        sales_data = []
        orders_data = []
        dates_labels = []

        for date in date_range:
            date_start = datetime.combine(date, datetime.min.time())
            date_end = datetime.combine(date, datetime.max.time())

            # المبيعات لهذا اليوم
            day_sales = db.session.query(func.sum(Order.total)).filter(
                Order.created_at.between(date_start, date_end),
                Order.status == 'completed'
            ).scalar() or 0

            # عدد الطلبات لهذا اليوم
            day_orders = db.session.query(func.count(Order.id)).filter(
                Order.created_at.between(date_start, date_end),
                Order.status == 'completed'
            ).scalar() or 0

            sales_data.append(float(day_sales))
            orders_data.append(day_orders)
            dates_labels.append(date.strftime('%Y-%m-%d'))

        # بيانات الرسم البياني لتوزيع المنتجات حسب التصنيف
        categories_query = db.session.query(
            Category.name,
            func.count(Product.id)
        ).outerjoin(Product).group_by(Category.name).all()

        categories_labels = []
        categories_values = []

        for category_name, count in categories_query:
            if category_name:
                categories_labels.append(category_name)
                categories_values.append(count)

        # إضافة المنتجات بدون تصنيف
        uncategorized_count = Product.query.filter(Product.category_id == None).count()
        if uncategorized_count > 0:
            categories_labels.append('بدون تصنيف')
            categories_values.append(uncategorized_count)

        # تجميع البيانات
        dashboard_data = {
            'sales': {
                'daily': float(daily_sales),
                'weekly': float(weekly_sales),
                'monthly': float(monthly_sales),
                'total': float(total_sales),
                'daily_orders': daily_orders_count,
                'weekly_orders': weekly_orders_count,
                'monthly_orders': monthly_orders_count,
                'total_orders': total_orders
            },
            'purchases': {
                'daily': float(today_purchases),
                'total': float(total_purchases)
            },
            'profit': {
                'total': float(profit)
            },
            'inventory': {
                'total_products': total_products,
                'active_products': active_products,
                'low_stock': low_stock_products,
                'out_of_stock': out_of_stock_products
            },
            'customers': {
                'total': customers_count
            },
            'suppliers': {
                'total': suppliers_count
            },
            'warehouses': {
                'total': warehouses_count
            },
            'recent_sales': recent_sales_list,
            'top_products': top_products_list,
            'low_stock_products': low_stock_products_list,
            'sales_chart': {
                'dates': dates_labels,
                'sales': sales_data,
                'orders': orders_data
            },
            'categories_chart': {
                'categories': categories_labels,
                'values': categories_values
            }
        }

        return jsonify(dashboard_data)
    except Exception as e:
        logger.error(f"خطأ في الحصول على إحصائيات لوحة التحكم: {str(e)}")
        return jsonify({'error': f'فشل في الحصول على إحصائيات لوحة التحكم: {str(e)}'}), 500

@api_blueprint.route('/api/sales-chart-data', methods=['GET'])
def sales_chart_data():
    """الحصول على بيانات الرسم البياني للمبيعات"""
    try:
        days = int(request.args.get('days', 30))

        # الحصول على نطاق التاريخ
        end_date = datetime.now().date()
        start_date = end_date - timedelta(days=days-1)
        date_range = [(start_date + timedelta(days=i)) for i in range(days)]

        sales_data = []
        orders_data = []
        dates_labels = []

        for date in date_range:
            date_start = datetime.combine(date, datetime.min.time())
            date_end = datetime.combine(date, datetime.max.time())

            # المبيعات لهذا اليوم
            day_sales = db.session.query(func.sum(Order.total)).filter(
                Order.created_at.between(date_start, date_end),
                Order.status == 'completed'
            ).scalar() or 0

            # عدد الطلبات لهذا اليوم
            day_orders = db.session.query(func.count(Order.id)).filter(
                Order.created_at.between(date_start, date_end),
                Order.status == 'completed'
            ).scalar() or 0

            sales_data.append(float(day_sales))
            orders_data.append(day_orders)
            dates_labels.append(date.strftime('%Y-%m-%d'))

        return jsonify({
            'dates': dates_labels,
            'sales': sales_data,
            'orders': orders_data
        })
    except Exception as e:
        logger.error(f"خطأ في الحصول على بيانات الرسم البياني للمبيعات: {str(e)}")
        return jsonify({'error': f'فشل في الحصول على بيانات الرسم البياني للمبيعات: {str(e)}'}), 500

@api_blueprint.route('/api/notifications/count', methods=['GET'])
def notifications_count():
    """الحصول على عدد الإشعارات غير المقروءة"""
    try:
        if not current_user.is_authenticated:
            return jsonify({'count': 0})

        # عدد الإشعارات غير المقروءة
        unread_count = Notification.query.filter_by(
            user_id=current_user.id,
            is_read=False
        ).count()

        return jsonify({'count': unread_count})
    except Exception as e:
        logger.error(f"خطأ في الحصول على عدد الإشعارات: {str(e)}")
        return jsonify({'error': f'فشل في الحصول على عدد الإشعارات: {str(e)}'}), 500

@api_blueprint.route('/api/notifications/recent', methods=['GET'])
def recent_notifications():
    """الحصول على الإشعارات الأخيرة"""
    try:
        if not current_user.is_authenticated:
            return jsonify({'notifications': [], 'unread_count': 0})

        # عدد الإشعارات غير المقروءة
        unread_count = Notification.query.filter_by(
            user_id=current_user.id,
            is_read=False
        ).count()

        # الإشعارات الأخيرة
        notifications = Notification.query.filter_by(
            user_id=current_user.id
        ).order_by(Notification.created_at.desc()).limit(10).all()

        notifications_list = []
        for notification in notifications:
            notifications_list.append({
                'id': notification.id,
                'title': notification.title,
                'message': notification.message,
                'type': notification.type,
                'is_read': notification.is_read,
                'link': notification.link,
                'created_at': notification.created_at.strftime('%Y-%m-%d %H:%M')
            })

        return jsonify({
            'notifications': notifications_list,
            'unread_count': unread_count
        })
    except Exception as e:
        logger.error(f"خطأ في الحصول على الإشعارات الأخيرة: {str(e)}")
        return jsonify({'error': f'فشل في الحصول على الإشعارات الأخيرة: {str(e)}'}), 500

@api_blueprint.route('/api/notifications/mark-read/<int:notification_id>', methods=['POST'])
def mark_notification_read(notification_id):
    """تحديد إشعار كمقروء"""
    try:
        if not current_user.is_authenticated:
            return jsonify({'error': 'غير مصرح لك بتنفيذ هذه العملية'}), 401

        notification = Notification.query.filter_by(
            id=notification_id,
            user_id=current_user.id
        ).first()

        if not notification:
            return jsonify({'error': 'الإشعار غير موجود'}), 404

        notification.is_read = True
        db.session.commit()

        return jsonify({'success': True})
    except Exception as e:
        logger.error(f"خطأ في تحديد الإشعار كمقروء: {str(e)}")
        return jsonify({'error': f'فشل في تحديد الإشعار كمقروء: {str(e)}'}), 500

@api_blueprint.route('/api/notifications/mark-all-read', methods=['POST'])
def mark_all_notifications_read():
    """تحديد جميع الإشعارات كمقروءة"""
    try:
        if not current_user.is_authenticated:
            return jsonify({'error': 'غير مصرح لك بتنفيذ هذه العملية'}), 401

        Notification.query.filter_by(
            user_id=current_user.id,
            is_read=False
        ).update({'is_read': True})

        db.session.commit()

        return jsonify({'success': True})
    except Exception as e:
        logger.error(f"خطأ في تحديد جميع الإشعارات كمقروءة: {str(e)}")
        return jsonify({'error': f'فشل في تحديد جميع الإشعارات كمقروءة: {str(e)}'}), 500

@api_blueprint.route('/api/settings', methods=['GET'])
def get_settings():
    """الحصول على إعدادات النظام"""
    try:
        settings_file = os.path.join('instance', 'settings.json')
        settings = {}

        if os.path.exists(settings_file):
            try:
                with open(settings_file, 'r', encoding='utf-8') as f:
                    settings = json.load(f)
            except Exception as e:
                logger.error(f"خطأ في قراءة ملف الإعدادات: {str(e)}")

        return jsonify(settings)
    except Exception as e:
        logger.error(f"خطأ في الحصول على إعدادات النظام: {str(e)}")
        return jsonify({'error': f'فشل في الحصول على إعدادات النظام: {str(e)}'}), 500

@api_blueprint.route('/api/category-distribution', methods=['GET'])
def category_distribution():
    """الحصول على توزيع المنتجات حسب التصنيف"""
    try:
        # بيانات الرسم البياني لتوزيع المنتجات حسب التصنيف
        categories_query = db.session.query(
            Category.name,
            func.count(Product.id)
        ).outerjoin(Product).group_by(Category.name).all()

        categories_labels = []
        categories_values = []

        for category_name, count in categories_query:
            if category_name:
                categories_labels.append(category_name)
                categories_values.append(count)

        # إضافة المنتجات بدون تصنيف
        uncategorized_count = Product.query.filter(Product.category_id == None).count()
        if uncategorized_count > 0:
            categories_labels.append('بدون تصنيف')
            categories_values.append(uncategorized_count)

        return jsonify({
            'categories': categories_labels,
            'values': categories_values
        })
    except Exception as e:
        logger.error(f"خطأ في الحصول على توزيع المنتجات حسب التصنيف: {str(e)}")
        return jsonify({'error': f'فشل في الحصول على توزيع المنتجات حسب التصنيف: {str(e)}'}), 500
