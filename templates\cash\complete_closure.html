{% extends "base.html" %}

{% block title %}إكمال الإغلاق اليومي{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">إكمال الإغلاق اليومي</h3>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h4 class="card-title">ملخص الإغلاق</h4>
                                </div>
                                <div class="card-body">
                                    <table class="table table-bordered">
                                        <tr>
                                            <th>الخزينة</th>
                                            <td>{{ closure.cash_register_name }}</td>
                                        </tr>
                                        <tr>
                                            <th>تاريخ الإغلاق</th>
                                            <td>{{ closure.closure_date }}</td>
                                        </tr>
                                        <tr>
                                            <th>الرصيد الافتتاحي</th>
                                            <td>{{ closure.opening_balance }}</td>
                                        </tr>
                                        <tr>
                                            <th>إجمالي المبيعات</th>
                                            <td>{{ closure.total_sales }}</td>
                                        </tr>
                                        <tr>
                                            <th>إجمالي المشتريات</th>
                                            <td>{{ closure.total_purchases }}</td>
                                        </tr>
                                        <tr>
                                            <th>إجمالي الإيداعات</th>
                                            <td>{{ closure.total_deposits }}</td>
                                        </tr>
                                        <tr>
                                            <th>إجمالي السحوبات</th>
                                            <td>{{ closure.total_withdrawals }}</td>
                                        </tr>
                                        <tr>
                                            <th>الرصيد المتوقع</th>
                                            <td>{{ closure.expected_balance }}</td>
                                        </tr>
                                    </table>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <form method="POST" class="needs-validation" novalidate>
                                <div class="card">
                                    <div class="card-header">
                                        <h4 class="card-title">إكمال الإغلاق</h4>
                                    </div>
                                    <div class="card-body">
                                        <div class="form-group">
                                            <label for="closing_balance">الرصيد الختامي</label>
                                            <input type="number" step="0.01" class="form-control" id="closing_balance" 
                                                   name="closing_balance" value="{{ closure.expected_balance }}" required>
                                            <div class="invalid-feedback">
                                                يرجى إدخال الرصيد الختامي
                                            </div>
                                            <small class="form-text text-muted">
                                                الرصيد المتوقع: {{ closure.expected_balance }}
                                            </small>
                                        </div>
                                        <div class="form-group mt-3">
                                            <label for="notes">ملاحظات</label>
                                            <textarea class="form-control" id="notes" name="notes" rows="3">{{ closure.notes }}</textarea>
                                        </div>
                                        <div class="alert alert-warning mt-3">
                                            <i class="fas fa-exclamation-triangle"></i>
                                            تنبيه: بعد إكمال الإغلاق، سيتم تحديث رصيد الخزينة تلقائياً.
                                        </div>
                                    </div>
                                    <div class="card-footer">
                                        <button type="submit" class="btn btn-success">
                                            <i class="fas fa-check"></i> إكمال الإغلاق
                                        </button>
                                        <a href="{{ url_for('cash.closure_details', closure_id=closure.id) }}" class="btn btn-secondary">
                                            <i class="fas fa-times"></i> إلغاء
                                        </a>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // التحقق من صحة النموذج
    const form = document.querySelector('form');
    form.addEventListener('submit', function(event) {
        if (!form.checkValidity()) {
            event.preventDefault();
            event.stopPropagation();
        }
        form.classList.add('was-validated');
    });
    
    // حساب الفرق تلقائياً
    const closingBalanceInput = document.getElementById('closing_balance');
    const expectedBalance = {{ closure.expected_balance }};
    
    closingBalanceInput.addEventListener('input', function() {
        const difference = this.value - expectedBalance;
        const differenceElement = document.getElementById('balance_difference');
        
        if (differenceElement) {
            differenceElement.textContent = difference.toFixed(2);
            differenceElement.className = difference < 0 ? 'text-danger' : 'text-success';
        }
    });
});
</script>
{% endblock %} 