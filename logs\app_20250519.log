2025-05-19 01:04:29,746 - app - INFO - استخدام قاعدة البيانات المحلية: sqlite:///c:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\instance\fouad_pos.db
2025-05-19 01:04:30,861 - nobara - INFO - تم بدء تشغيل التطبيق
2025-05-19 01:04:31,827 - app - WARNING - مكتبة schedule غير متوفرة. لن يتم تفعيل النسخ الاحتياطي التلقائي.
2025-05-19 01:04:32,287 - app - WARNING - تم تخطي تهيئة النسخ الاحتياطي التلقائي لأن مكتبة schedule غير متوفرة
2025-05-19 01:04:32,292 - app - INFO - عمود warehouse_id موجود بالفعل في جدول order
2025-05-19 01:04:34,093 - security - WARNING - No JWT secret provided. Generated a new one. Please set JWT_SECRET in your environment.
2025-05-19 01:04:34,355 - __main__ - INFO - جاري بدء التطبيق على العنوان: http://************:5000
2025-05-19 01:04:42,836 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://************:5000
2025-05-19 01:04:42,838 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-19 01:04:42,855 - werkzeug - INFO -  * Restarting with stat
2025-05-19 01:04:45,321 - app - INFO - استخدام قاعدة البيانات المحلية: sqlite:///c:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\instance\fouad_pos.db
2025-05-19 01:04:45,402 - nobara - INFO - تم بدء تشغيل التطبيق
2025-05-19 01:04:45,898 - app - WARNING - مكتبة schedule غير متوفرة. لن يتم تفعيل النسخ الاحتياطي التلقائي.
2025-05-19 01:04:45,958 - app - WARNING - تم تخطي تهيئة النسخ الاحتياطي التلقائي لأن مكتبة schedule غير متوفرة
2025-05-19 01:04:45,961 - app - INFO - عمود warehouse_id موجود بالفعل في جدول order
2025-05-19 01:04:46,159 - security - WARNING - No JWT secret provided. Generated a new one. Please set JWT_SECRET in your environment.
2025-05-19 01:04:46,364 - __main__ - INFO - جاري بدء التطبيق على العنوان: http://************:5000
2025-05-19 01:04:46,988 - werkzeug - WARNING -  * Debugger is active!
2025-05-19 01:04:47,548 - werkzeug - INFO -  * Debugger PIN: 692-333-133
2025-05-19 01:04:47,831 - werkzeug - INFO - ************ - - [19/May/2025 01:04:47] "[32mGET / HTTP/1.1[0m" 302 -
2025-05-19 01:04:47,839 - werkzeug - INFO - ************ - - [19/May/2025 01:04:47] "[32mGET / HTTP/1.1[0m" 302 -
2025-05-19 01:04:48,041 - werkzeug - INFO - ************ - - [19/May/2025 01:04:48] "GET /login HTTP/1.1" 200 -
2025-05-19 01:04:48,048 - werkzeug - INFO - ************ - - [19/May/2025 01:04:48] "GET /login HTTP/1.1" 200 -
2025-05-19 01:04:48,348 - werkzeug - INFO - ************ - - [19/May/2025 01:04:48] "GET /static/css/nobara-pro.css?v=1.0.0 HTTP/1.1" 200 -
2025-05-19 01:04:48,448 - werkzeug - INFO - ************ - - [19/May/2025 01:04:48] "GET /static/js/notifications-system.js?v=1.0.0 HTTP/1.1" 200 -
2025-05-19 01:04:48,584 - werkzeug - INFO - ************ - - [19/May/2025 01:04:48] "GET /static/uploads/logo_20250518120852.jpeg HTTP/1.1" 200 -
2025-05-19 01:04:48,743 - werkzeug - INFO - ************ - - [19/May/2025 01:04:48] "GET /static/js/system-manager.js?v=1.0.0 HTTP/1.1" 200 -
2025-05-19 01:04:48,804 - werkzeug - INFO - ************ - - [19/May/2025 01:04:48] "GET /static/css/dashboard-pro.css?v=1.0.1 HTTP/1.1" 200 -
2025-05-19 01:04:49,009 - werkzeug - INFO - ************ - - [19/May/2025 01:04:49] "[36mGET /static/css/nobara-pro.css?v=1.0.0 HTTP/1.1[0m" 304 -
2025-05-19 01:04:49,017 - werkzeug - INFO - ************ - - [19/May/2025 01:04:49] "[36mGET /static/css/dashboard-pro.css?v=1.0.1 HTTP/1.1[0m" 304 -
2025-05-19 01:04:49,114 - werkzeug - INFO - ************ - - [19/May/2025 01:04:49] "[36mGET /static/js/notifications-system.js?v=1.0.0 HTTP/1.1[0m" 304 -
2025-05-19 01:04:49,116 - werkzeug - INFO - ************ - - [19/May/2025 01:04:49] "[36mGET /static/js/system-manager.js?v=1.0.0 HTTP/1.1[0m" 304 -
2025-05-19 01:04:49,116 - werkzeug - INFO - ************ - - [19/May/2025 01:04:49] "[36mGET /static/uploads/logo_20250518120852.jpeg HTTP/1.1[0m" 304 -
2025-05-19 01:04:49,594 - werkzeug - INFO - ************ - - [19/May/2025 01:04:49] "GET /api/settings HTTP/1.1" 200 -
2025-05-19 01:04:49,805 - werkzeug - INFO - ************ - - [19/May/2025 01:04:49] "GET /api/settings HTTP/1.1" 200 -
2025-05-19 01:04:50,099 - werkzeug - INFO - ************ - - [19/May/2025 01:04:50] "[32mGET /api/notifications/recent HTTP/1.1[0m" 302 -
2025-05-19 01:04:50,102 - werkzeug - INFO - ************ - - [19/May/2025 01:04:50] "[32mGET /api/notifications/count HTTP/1.1[0m" 302 -
2025-05-19 01:04:50,123 - werkzeug - INFO - ************ - - [19/May/2025 01:04:50] "[32mGET /api/notifications/recent HTTP/1.1[0m" 302 -
2025-05-19 01:04:50,130 - werkzeug - INFO - ************ - - [19/May/2025 01:04:50] "[32mGET /api/notifications/count HTTP/1.1[0m" 302 -
2025-05-19 01:04:50,333 - werkzeug - INFO - ************ - - [19/May/2025 01:04:50] "GET /login?next=/api/notifications/count HTTP/1.1" 200 -
2025-05-19 01:04:50,338 - werkzeug - INFO - ************ - - [19/May/2025 01:04:50] "GET /login?next=/api/notifications/recent HTTP/1.1" 200 -
2025-05-19 01:04:50,398 - werkzeug - INFO - ************ - - [19/May/2025 01:04:50] "GET /login?next=/api/notifications/count HTTP/1.1" 200 -
2025-05-19 01:04:50,419 - werkzeug - INFO - ************ - - [19/May/2025 01:04:50] "GET /login?next=/api/notifications/recent HTTP/1.1" 200 -
2025-05-19 01:04:50,515 - nobara - ERROR - استثناء: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.
معلومات الطلب: {"url": "http://************:5000/favicon.ico", "method": "GET", "headers": {"Host": "************:5000", "Connection": "keep-alive", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Accept": "image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8", "Referer": "http://************:5000/login", "Accept-Encoding": "gzip, deflate", "Accept-Language": "en-US,en;q=0.9", "Cookie": "NEXT_LOCALE=en"}, "args": {}, "form": {}, "cookies": {"NEXT_LOCALE": "en"}, "remote_addr": "************", "user_agent": null}
تتبع الاستثناء:
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\app.py", line 917, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\app.py", line 891, in dispatch_request
    self.raise_routing_exception(req)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\app.py", line 500, in raise_routing_exception
    raise request.routing_exception  # type: ignore[misc]
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\ctx.py", line 362, in match_request
    result = self.url_adapter.match(return_rule=True)  # type: ignore
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\werkzeug\routing\map.py", line 629, in match
    raise NotFound() from None
werkzeug.exceptions.NotFound: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.

2025-05-19 01:04:50,564 - werkzeug - INFO - ************ - - [19/May/2025 01:04:50] "[35m[1mGET /favicon.ico HTTP/1.1[0m" 500 -
2025-05-19 01:04:50,597 - nobara - ERROR - استثناء: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.
معلومات الطلب: {"url": "http://************:5000/favicon.ico", "method": "GET", "headers": {"Host": "************:5000", "Connection": "keep-alive", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Accept": "image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8", "Referer": "http://************:5000/login", "Accept-Encoding": "gzip, deflate", "Accept-Language": "en-US,en;q=0.9", "Cookie": "NEXT_LOCALE=en"}, "args": {}, "form": {}, "cookies": {"NEXT_LOCALE": "en"}, "remote_addr": "************", "user_agent": null}
تتبع الاستثناء:
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\app.py", line 917, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\app.py", line 891, in dispatch_request
    self.raise_routing_exception(req)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\app.py", line 500, in raise_routing_exception
    raise request.routing_exception  # type: ignore[misc]
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\ctx.py", line 362, in match_request
    result = self.url_adapter.match(return_rule=True)  # type: ignore
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\werkzeug\routing\map.py", line 629, in match
    raise NotFound() from None
werkzeug.exceptions.NotFound: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.

2025-05-19 01:04:50,618 - werkzeug - INFO - ************ - - [19/May/2025 01:04:50] "[35m[1mGET /favicon.ico HTTP/1.1[0m" 500 -
2025-05-19 01:05:19,817 - werkzeug - INFO - ************ - - [19/May/2025 01:05:19] "[32mGET /api/notifications/recent HTTP/1.1[0m" 302 -
2025-05-19 01:05:19,833 - werkzeug - INFO - ************ - - [19/May/2025 01:05:19] "GET /login?next=/api/notifications/recent HTTP/1.1" 200 -
2025-05-19 01:05:20,166 - werkzeug - INFO - ************ - - [19/May/2025 01:05:20] "[32mGET /api/notifications/recent HTTP/1.1[0m" 302 -
2025-05-19 01:05:20,355 - werkzeug - INFO - ************ - - [19/May/2025 01:05:20] "GET /login?next=/api/notifications/recent HTTP/1.1" 200 -
2025-05-19 01:05:21,014 - werkzeug - INFO - ************ - - [19/May/2025 01:05:21] "[32mPOST /login HTTP/1.1[0m" 302 -
2025-05-19 01:05:21,924 - werkzeug - INFO - ************ - - [19/May/2025 01:05:21] "GET /home HTTP/1.1" 200 -
2025-05-19 01:05:22,071 - werkzeug - INFO - ************ - - [19/May/2025 01:05:22] "[36mGET /static/css/nobara-pro.css?v=1.0.0 HTTP/1.1[0m" 304 -
2025-05-19 01:05:22,087 - werkzeug - INFO - ************ - - [19/May/2025 01:05:22] "[36mGET /static/css/dashboard-pro.css?v=1.0.1 HTTP/1.1[0m" 304 -
2025-05-19 01:05:22,092 - werkzeug - INFO - ************ - - [19/May/2025 01:05:22] "[36mGET /static/js/notifications-system.js?v=1.0.0 HTTP/1.1[0m" 304 -
2025-05-19 01:05:22,104 - werkzeug - INFO - ************ - - [19/May/2025 01:05:22] "[36mGET /static/uploads/logo_20250518120852.jpeg HTTP/1.1[0m" 304 -
2025-05-19 01:05:22,107 - werkzeug - INFO - ************ - - [19/May/2025 01:05:22] "[36mGET /static/js/system-manager.js?v=1.0.0 HTTP/1.1[0m" 304 -
2025-05-19 01:05:22,399 - werkzeug - INFO - ************ - - [19/May/2025 01:05:22] "GET /static/js/dashboard-manager.js?v=1.0.0 HTTP/1.1" 200 -
2025-05-19 01:05:22,425 - werkzeug - INFO - ************ - - [19/May/2025 01:05:22] "GET /static/js/dashboard-pro.js?v=1.0.0 HTTP/1.1" 200 -
2025-05-19 01:05:23,407 - werkzeug - INFO - ************ - - [19/May/2025 01:05:23] "GET /api/settings HTTP/1.1" 200 -
2025-05-19 01:05:23,552 - werkzeug - INFO - ************ - - [19/May/2025 01:05:23] "GET /api/notifications/count HTTP/1.1" 200 -
2025-05-19 01:05:23,587 - werkzeug - INFO - ************ - - [19/May/2025 01:05:23] "GET /api/category-distribution HTTP/1.1" 200 -
2025-05-19 01:05:23,592 - werkzeug - INFO - ************ - - [19/May/2025 01:05:23] "GET /api/settings HTTP/1.1" 200 -
2025-05-19 01:05:23,657 - werkzeug - INFO - ************ - - [19/May/2025 01:05:23] "GET /api/sales-chart-data?days=30 HTTP/1.1" 200 -
2025-05-19 01:05:23,669 - werkzeug - INFO - ************ - - [19/May/2025 01:05:23] "GET /api/dashboard HTTP/1.1" 200 -
2025-05-19 01:05:23,802 - werkzeug - INFO - ************ - - [19/May/2025 01:05:23] "GET /api/dashboard HTTP/1.1" 200 -
2025-05-19 01:05:24,040 - werkzeug - INFO - ************ - - [19/May/2025 01:05:24] "GET /api/notifications/count HTTP/1.1" 200 -
2025-05-19 01:05:24,073 - werkzeug - INFO - ************ - - [19/May/2025 01:05:24] "GET /api/notifications/recent HTTP/1.1" 200 -
2025-05-19 01:05:24,137 - werkzeug - INFO - ************ - - [19/May/2025 01:05:24] "GET /api/notifications/count HTTP/1.1" 200 -
2025-05-19 01:05:24,193 - werkzeug - INFO - ************ - - [19/May/2025 01:05:24] "GET /api/notifications/recent HTTP/1.1" 200 -
2025-05-19 01:05:24,894 - nobara - ERROR - استثناء: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.
معلومات الطلب: {"url": "http://************:5000/favicon.ico", "method": "GET", "headers": {"Host": "************:5000", "Connection": "keep-alive", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Accept": "image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8", "Referer": "http://************:5000/home", "Accept-Encoding": "gzip, deflate", "Accept-Language": "en-US,en;q=0.9", "Cookie": "NEXT_LOCALE=en; session=.eJwljjkOg0AMAP_iOgXeZX3wGWR8KGkhVFH-HiKk6UYjzQfW2vN4wvLez3zA-gpYoIUJa8xWlUzKKK6utnFFKAt3n-Vi7m38bbXWg7bJEVWtUUrVlRUOVdx0cHRKVJPm0yROaMboETg6uZaTqURzpogMsuxwjZxH7vcNwvcHu5gvhg.aCpZoQ.Fua7Rtb1AeNOrdFp5eWeqbxDwUQ"}, "args": {}, "form": {}, "cookies": {"NEXT_LOCALE": "en", "session": ".eJwljjkOg0AMAP_iOgXeZX3wGWR8KGkhVFH-HiKk6UYjzQfW2vN4wvLez3zA-gpYoIUJa8xWlUzKKK6utnFFKAt3n-Vi7m38bbXWg7bJEVWtUUrVlRUOVdx0cHRKVJPm0yROaMboETg6uZaTqURzpogMsuxwjZxH7vcNwvcHu5gvhg.aCpZoQ.Fua7Rtb1AeNOrdFp5eWeqbxDwUQ"}, "remote_addr": "************", "user_agent": null}
تتبع الاستثناء:
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\app.py", line 917, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\app.py", line 891, in dispatch_request
    self.raise_routing_exception(req)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\app.py", line 500, in raise_routing_exception
    raise request.routing_exception  # type: ignore[misc]
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\ctx.py", line 362, in match_request
    result = self.url_adapter.match(return_rule=True)  # type: ignore
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\werkzeug\routing\map.py", line 629, in match
    raise NotFound() from None
werkzeug.exceptions.NotFound: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.

2025-05-19 01:05:24,906 - werkzeug - INFO - ************ - - [19/May/2025 01:05:24] "[35m[1mGET /favicon.ico HTTP/1.1[0m" 500 -
2025-05-19 01:05:45,122 - werkzeug - INFO - ************ - - [19/May/2025 01:05:45] "GET /products HTTP/1.1" 200 -
2025-05-19 01:05:46,472 - werkzeug - INFO - ************ - - [19/May/2025 01:05:46] "GET /static/img/nobara-logo.svg HTTP/1.1" 200 -
2025-05-19 01:05:48,101 - werkzeug - INFO - ************ - - [19/May/2025 01:05:48] "GET /api/notifications/count HTTP/1.1" 200 -
2025-05-19 01:05:48,258 - nobara - ERROR - استثناء: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.
معلومات الطلب: {"url": "http://************:5000/favicon.ico", "method": "GET", "headers": {"Host": "************:5000", "Connection": "keep-alive", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Accept": "image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8", "Referer": "http://************:5000/products", "Accept-Encoding": "gzip, deflate", "Accept-Language": "en-US,en;q=0.9", "Cookie": "NEXT_LOCALE=en; session=.eJwljjkOg0AMAP_iOgXeZX3wGWR8KGkhVFH-HiKk6UYjzQfW2vN4wvLez3zA-gpYoIUJa8xWlUzKKK6utnFFKAt3n-Vi7m38bbXWg7bJEVWtUUrVlRUOVdx0cHRKVJPm0yROaMboETg6uZaTqURzpogMsuxwjZxH7vcNwvcHu5gvhg.aCpZoQ.Fua7Rtb1AeNOrdFp5eWeqbxDwUQ"}, "args": {}, "form": {}, "cookies": {"NEXT_LOCALE": "en", "session": ".eJwljjkOg0AMAP_iOgXeZX3wGWR8KGkhVFH-HiKk6UYjzQfW2vN4wvLez3zA-gpYoIUJa8xWlUzKKK6utnFFKAt3n-Vi7m38bbXWg7bJEVWtUUrVlRUOVdx0cHRKVJPm0yROaMboETg6uZaTqURzpogMsuxwjZxH7vcNwvcHu5gvhg.aCpZoQ.Fua7Rtb1AeNOrdFp5eWeqbxDwUQ"}, "remote_addr": "************", "user_agent": null}
تتبع الاستثناء:
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\app.py", line 917, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\app.py", line 891, in dispatch_request
    self.raise_routing_exception(req)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\app.py", line 500, in raise_routing_exception
    raise request.routing_exception  # type: ignore[misc]
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\ctx.py", line 362, in match_request
    result = self.url_adapter.match(return_rule=True)  # type: ignore
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\werkzeug\routing\map.py", line 629, in match
    raise NotFound() from None
werkzeug.exceptions.NotFound: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.

2025-05-19 01:05:48,296 - werkzeug - INFO - ************ - - [19/May/2025 01:05:48] "[35m[1mGET /favicon.ico HTTP/1.1[0m" 500 -
2025-05-19 01:05:50,601 - werkzeug - INFO - ************ - - [19/May/2025 01:05:50] "[32mGET /product-import HTTP/1.1[0m" 302 -
2025-05-19 01:05:50,669 - werkzeug - INFO - ************ - - [19/May/2025 01:05:50] "GET /products HTTP/1.1" 200 -
2025-05-19 01:05:50,795 - werkzeug - INFO - ************ - - [19/May/2025 01:05:50] "[36mGET /static/img/nobara-logo.svg HTTP/1.1[0m" 304 -
2025-05-19 01:05:51,309 - werkzeug - INFO - ************ - - [19/May/2025 01:05:51] "GET /api/notifications/count HTTP/1.1" 200 -
2025-05-19 01:05:51,377 - nobara - ERROR - استثناء: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.
معلومات الطلب: {"url": "http://************:5000/favicon.ico", "method": "GET", "headers": {"Host": "************:5000", "Connection": "keep-alive", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Accept": "image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8", "Referer": "http://************:5000/products", "Accept-Encoding": "gzip, deflate", "Accept-Language": "en-US,en;q=0.9", "Cookie": "NEXT_LOCALE=en; session=.eJwlkMGKwzAMRH_F-JJLKbGd2FY-o-xttxTZkklCmiyOcyr993WzIN5IDAODXvKRFtxH3uXw_ZKiVJGc85blRf4crdV0Mp0M4hT-0LhzN4No5mmdUV_zsZbpydcvfv4uWPjGiTOvkcUWZo6lESPuYt0ElpKncBQWTZnKwo28v--X2iTzPsqh5IPrNZEcpCb0DqjDlNhZcMpHiIDBJSJw3pnY-Tqd0f3HTVobsqGNSgGgtuxTqrGkegAVoHdkLCtAr2Pb-mgVolORSPXGRkjRInjS0VkiJots6hcex875v42S7z9O7Vzk.aCpZvg.nQCch2PODCXcSsrQNnBiYErR7lc"}, "args": {}, "form": {}, "cookies": {"NEXT_LOCALE": "en", "session": ".eJwlkMGKwzAMRH_F-JJLKbGd2FY-o-xttxTZkklCmiyOcyr993WzIN5IDAODXvKRFtxH3uXw_ZKiVJGc85blRf4crdV0Mp0M4hT-0LhzN4No5mmdUV_zsZbpydcvfv4uWPjGiTOvkcUWZo6lESPuYt0ElpKncBQWTZnKwo28v--X2iTzPsqh5IPrNZEcpCb0DqjDlNhZcMpHiIDBJSJw3pnY-Tqd0f3HTVobsqGNSgGgtuxTqrGkegAVoHdkLCtAr2Pb-mgVolORSPXGRkjRInjS0VkiJots6hcex875v42S7z9O7Vzk.aCpZvg.nQCch2PODCXcSsrQNnBiYErR7lc"}, "remote_addr": "************", "user_agent": null}
تتبع الاستثناء:
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\app.py", line 917, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\app.py", line 891, in dispatch_request
    self.raise_routing_exception(req)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\app.py", line 500, in raise_routing_exception
    raise request.routing_exception  # type: ignore[misc]
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\ctx.py", line 362, in match_request
    result = self.url_adapter.match(return_rule=True)  # type: ignore
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\werkzeug\routing\map.py", line 629, in match
    raise NotFound() from None
werkzeug.exceptions.NotFound: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.

2025-05-19 01:05:51,412 - werkzeug - INFO - ************ - - [19/May/2025 01:05:51] "[35m[1mGET /favicon.ico HTTP/1.1[0m" 500 -
2025-05-19 01:07:25,985 - werkzeug - INFO -  * Detected change in 'c:\\Users\\<USER>\\Desktop\\ملف البرنامج\\PythonCashierSystem.v2 - Copy - Copy\\PythonCashierSystem\\routes\\products.py', reloading
2025-05-19 01:07:30,207 - werkzeug - INFO -  * Restarting with stat
2025-05-19 01:08:25,588 - app - INFO - استخدام قاعدة البيانات المحلية: sqlite:///c:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\instance\fouad_pos.db
2025-05-19 01:08:26,442 - nobara - INFO - تم بدء تشغيل التطبيق
2025-05-19 01:08:27,725 - app - WARNING - مكتبة schedule غير متوفرة. لن يتم تفعيل النسخ الاحتياطي التلقائي.
2025-05-19 01:08:27,782 - app - WARNING - تم تخطي تهيئة النسخ الاحتياطي التلقائي لأن مكتبة schedule غير متوفرة
2025-05-19 01:08:27,803 - app - INFO - عمود warehouse_id موجود بالفعل في جدول order
2025-05-19 01:08:42,721 - security - WARNING - No JWT secret provided. Generated a new one. Please set JWT_SECRET in your environment.
2025-05-19 01:08:43,354 - __main__ - INFO - جاري بدء التطبيق على العنوان: http://************:5000
2025-05-19 01:09:04,504 - werkzeug - WARNING -  * Debugger is active!
2025-05-19 01:09:05,689 - werkzeug - INFO -  * Debugger PIN: 692-333-133
2025-05-19 01:09:11,994 - werkzeug - INFO - ************ - - [19/May/2025 01:09:11] "[32mGET / HTTP/1.1[0m" 302 -
2025-05-19 01:09:16,718 - werkzeug - INFO - ************ - - [19/May/2025 01:09:16] "GET /home HTTP/1.1" 200 -
2025-05-19 01:09:17,935 - werkzeug - INFO - ************ - - [19/May/2025 01:09:17] "[36mGET /static/css/dashboard-pro.css?v=1.0.1 HTTP/1.1[0m" 304 -
2025-05-19 01:09:17,938 - werkzeug - INFO - ************ - - [19/May/2025 01:09:17] "[36mGET /static/css/nobara-pro.css?v=1.0.0 HTTP/1.1[0m" 304 -
2025-05-19 01:09:18,087 - werkzeug - INFO - ************ - - [19/May/2025 01:09:18] "[36mGET /static/js/notifications-system.js?v=1.0.0 HTTP/1.1[0m" 304 -
2025-05-19 01:09:18,104 - werkzeug - INFO - ************ - - [19/May/2025 01:09:18] "[36mGET /static/js/system-manager.js?v=1.0.0 HTTP/1.1[0m" 304 -
2025-05-19 01:09:18,331 - werkzeug - INFO - ************ - - [19/May/2025 01:09:18] "[36mGET /static/uploads/logo_20250518120852.jpeg HTTP/1.1[0m" 304 -
2025-05-19 01:09:18,431 - werkzeug - INFO - ************ - - [19/May/2025 01:09:18] "[36mGET /static/js/dashboard-manager.js?v=1.0.0 HTTP/1.1[0m" 304 -
2025-05-19 01:09:20,716 - werkzeug - INFO - ************ - - [19/May/2025 01:09:20] "[36mGET /static/js/dashboard-pro.js?v=1.0.0 HTTP/1.1[0m" 304 -
2025-05-19 01:09:20,813 - werkzeug - INFO - ************ - - [19/May/2025 01:09:20] "[32mGET /product-import HTTP/1.1[0m" 302 -
2025-05-19 01:09:21,471 - werkzeug - INFO - ************ - - [19/May/2025 01:09:21] "GET /products HTTP/1.1" 200 -
2025-05-19 01:09:21,674 - werkzeug - INFO - ************ - - [19/May/2025 01:09:21] "[36mGET /static/img/nobara-logo.svg HTTP/1.1[0m" 304 -
2025-05-19 01:09:22,724 - werkzeug - INFO - ************ - - [19/May/2025 01:09:22] "GET /api/notifications/count HTTP/1.1" 200 -
2025-05-19 01:09:23,457 - nobara - ERROR - استثناء: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.
معلومات الطلب: {"url": "http://************:5000/favicon.ico", "method": "GET", "headers": {"Host": "************:5000", "Connection": "keep-alive", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Accept": "image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8", "Referer": "http://************:5000/products", "Accept-Encoding": "gzip, deflate", "Accept-Language": "en-US,en;q=0.9", "Cookie": "NEXT_LOCALE=en; session=.eJzFkEFqxDAMRa9ivMkmDLGd2FaOUbprh0G2ZJIhkxTHWQ1z93rSQxTE-xJCfPGf8pYW3Cfe5fj1lKJUkZzzlmUrv4_OajqZTgZxCr9p3NmbUTT3eb2jvuRjLfODL5_8-Fmw8AcnzrxGFlu4cyyNmHAX6yawlDyHo7BoylwWbuT11f6n-bWtMWTeJzmWfHCdZpKj1ITeAfWYEjsLTvkIETC4RATOOxN7X6s3enhvk9aGbOiiUgCoLfuU6llSA4AKMDgylhWg17HrfLQK0alIpAZjI6RoETzp6CwRk0U2NYXbsXP--0bJ1y_MQ4XH.aCpakA.X4tuCEm29NZWfbsNnPfw4TBrYSI"}, "args": {}, "form": {}, "cookies": {"NEXT_LOCALE": "en", "session": ".eJzFkEFqxDAMRa9ivMkmDLGd2FaOUbprh0G2ZJIhkxTHWQ1z93rSQxTE-xJCfPGf8pYW3Cfe5fj1lKJUkZzzlmUrv4_OajqZTgZxCr9p3NmbUTT3eb2jvuRjLfODL5_8-Fmw8AcnzrxGFlu4cyyNmHAX6yawlDyHo7BoylwWbuT11f6n-bWtMWTeJzmWfHCdZpKj1ITeAfWYEjsLTvkIETC4RATOOxN7X6s3enhvk9aGbOiiUgCoLfuU6llSA4AKMDgylhWg17HrfLQK0alIpAZjI6RoETzp6CwRk0U2NYXbsXP--0bJ1y_MQ4XH.aCpakA.X4tuCEm29NZWfbsNnPfw4TBrYSI"}, "remote_addr": "************", "user_agent": null}
تتبع الاستثناء:
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\app.py", line 917, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\app.py", line 891, in dispatch_request
    self.raise_routing_exception(req)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\app.py", line 500, in raise_routing_exception
    raise request.routing_exception  # type: ignore[misc]
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\ctx.py", line 362, in match_request
    result = self.url_adapter.match(return_rule=True)  # type: ignore
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\werkzeug\routing\map.py", line 629, in match
    raise NotFound() from None
werkzeug.exceptions.NotFound: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.

2025-05-19 01:09:23,522 - werkzeug - INFO - ************ - - [19/May/2025 01:09:23] "[35m[1mGET /favicon.ico HTTP/1.1[0m" 500 -
2025-05-19 01:09:23,919 - werkzeug - INFO - ************ - - [19/May/2025 01:09:23] "GET /api/notifications/count HTTP/1.1" 200 -
2025-05-19 01:09:23,923 - werkzeug - INFO - ************ - - [19/May/2025 01:09:23] "GET /api/settings HTTP/1.1" 200 -
2025-05-19 01:09:24,072 - werkzeug - INFO - ************ - - [19/May/2025 01:09:24] "GET /api/category-distribution HTTP/1.1" 200 -
2025-05-19 01:09:24,116 - werkzeug - INFO - ************ - - [19/May/2025 01:09:24] "GET /api/sales-chart-data?days=30 HTTP/1.1" 200 -
2025-05-19 01:09:24,208 - werkzeug - INFO - ************ - - [19/May/2025 01:09:24] "GET /api/settings HTTP/1.1" 200 -
2025-05-19 01:09:24,400 - werkzeug - INFO - ************ - - [19/May/2025 01:09:24] "GET /api/dashboard HTTP/1.1" 200 -
2025-05-19 01:09:24,550 - werkzeug - INFO - ************ - - [19/May/2025 01:09:24] "GET /api/dashboard HTTP/1.1" 200 -
2025-05-19 01:09:33,398 - werkzeug - INFO - ************ - - [19/May/2025 01:09:33] "GET /api/notifications/count HTTP/1.1" 200 -
2025-05-19 01:09:33,577 - werkzeug - INFO - ************ - - [19/May/2025 01:09:33] "GET /api/notifications/recent HTTP/1.1" 200 -
2025-05-19 01:09:33,764 - nobara - ERROR - استثناء: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.
معلومات الطلب: {"url": "http://************:5000/favicon.ico", "method": "GET", "headers": {"Host": "************:5000", "Connection": "keep-alive", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Accept": "image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8", "Referer": "http://************:5000/home", "Accept-Encoding": "gzip, deflate", "Accept-Language": "en-US,en;q=0.9", "Cookie": "NEXT_LOCALE=en; session=.eJzFkEFqxDAMRa9ivMkmDLGd2FaOUbprh0G2ZJIhkxTHWQ1z93rSQxTE-xJCfPGf8pYW3Cfe5fj1lKJUkZzzlmUrv4_OajqZTgZxCr9p3NmbUTT3eb2jvuRjLfODL5_8-Fmw8AcnzrxGFlu4cyyNmHAX6yawlDyHo7BoylwWbuT11f6n-bWtMWTeJzmWfHCdZpKj1ITeAfWYEjsLTvkIETC4RATOOxN7X6s3enhvk9aGbOiiUgCoLfuU6llSA4AKMDgylhWg17HrfLQK0alIpAZjI6RoETzp6CwRk0U2NYXbsXP--0bJ1y_MQ4XH.aCpakA.X4tuCEm29NZWfbsNnPfw4TBrYSI"}, "args": {}, "form": {}, "cookies": {"NEXT_LOCALE": "en", "session": ".eJzFkEFqxDAMRa9ivMkmDLGd2FaOUbprh0G2ZJIhkxTHWQ1z93rSQxTE-xJCfPGf8pYW3Cfe5fj1lKJUkZzzlmUrv4_OajqZTgZxCr9p3NmbUTT3eb2jvuRjLfODL5_8-Fmw8AcnzrxGFlu4cyyNmHAX6yawlDyHo7BoylwWbuT11f6n-bWtMWTeJzmWfHCdZpKj1ITeAfWYEjsLTvkIETC4RATOOxN7X6s3enhvk9aGbOiiUgCoLfuU6llSA4AKMDgylhWg17HrfLQK0alIpAZjI6RoETzp6CwRk0U2NYXbsXP--0bJ1y_MQ4XH.aCpakA.X4tuCEm29NZWfbsNnPfw4TBrYSI"}, "remote_addr": "************", "user_agent": null}
تتبع الاستثناء:
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\app.py", line 917, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\app.py", line 891, in dispatch_request
    self.raise_routing_exception(req)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\app.py", line 500, in raise_routing_exception
    raise request.routing_exception  # type: ignore[misc]
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\ctx.py", line 362, in match_request
    result = self.url_adapter.match(return_rule=True)  # type: ignore
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\werkzeug\routing\map.py", line 629, in match
    raise NotFound() from None
werkzeug.exceptions.NotFound: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.

2025-05-19 01:09:33,901 - werkzeug - INFO - ************ - - [19/May/2025 01:09:33] "[35m[1mGET /favicon.ico HTTP/1.1[0m" 500 -
2025-05-19 01:09:33,951 - werkzeug - INFO - ************ - - [19/May/2025 01:09:33] "GET /api/notifications/count HTTP/1.1" 200 -
2025-05-19 01:09:34,012 - werkzeug - INFO - ************ - - [19/May/2025 01:09:34] "GET /api/notifications/recent HTTP/1.1" 200 -
2025-05-19 01:09:34,409 - werkzeug - INFO - ************ - - [19/May/2025 01:09:34] "GET /products HTTP/1.1" 200 -
2025-05-19 01:09:34,618 - werkzeug - INFO - ************ - - [19/May/2025 01:09:34] "[36mGET /static/img/nobara-logo.svg HTTP/1.1[0m" 304 -
2025-05-19 01:09:35,634 - werkzeug - INFO - ************ - - [19/May/2025 01:09:35] "GET /api/notifications/count HTTP/1.1" 200 -
2025-05-19 01:09:35,665 - nobara - ERROR - استثناء: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.
معلومات الطلب: {"url": "http://************:5000/favicon.ico", "method": "GET", "headers": {"Host": "************:5000", "Connection": "keep-alive", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Accept": "image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8", "Referer": "http://************:5000/products", "Accept-Encoding": "gzip, deflate", "Accept-Language": "en-US,en;q=0.9", "Cookie": "NEXT_LOCALE=en; session=.eJzFkEFqxDAMRa9ivMkmDLGd2FaOUbprh0G2ZJIhkxTHWQ1z93rSQxTE-xJCfPGf8pYW3Cfe5fj1lKJUkZzzlmUrv4_OajqZTgZxCr9p3NmbUTT3eb2jvuRjLfODL5_8-Fmw8AcnzrxGFlu4cyyNmHAX6yawlDyHo7BoylwWbuT11f6n-bWtMWTeJzmWfHCdZpKj1ITeAfWYEjsLTvkIETC4RATOOxN7X6s3enhvk9aGbOiiUgCoLfuU6llSA4AKMDgylhWg17HrfLQK0alIpAZjI6RoETzp6CwRk0U2NYXbsXP--0bJ1y_MQ4XH.aCpakA.X4tuCEm29NZWfbsNnPfw4TBrYSI"}, "args": {}, "form": {}, "cookies": {"NEXT_LOCALE": "en", "session": ".eJzFkEFqxDAMRa9ivMkmDLGd2FaOUbprh0G2ZJIhkxTHWQ1z93rSQxTE-xJCfPGf8pYW3Cfe5fj1lKJUkZzzlmUrv4_OajqZTgZxCr9p3NmbUTT3eb2jvuRjLfODL5_8-Fmw8AcnzrxGFlu4cyyNmHAX6yawlDyHo7BoylwWbuT11f6n-bWtMWTeJzmWfHCdZpKj1ITeAfWYEjsLTvkIETC4RATOOxN7X6s3enhvk9aGbOiiUgCoLfuU6llSA4AKMDgylhWg17HrfLQK0alIpAZjI6RoETzp6CwRk0U2NYXbsXP--0bJ1y_MQ4XH.aCpakA.X4tuCEm29NZWfbsNnPfw4TBrYSI"}, "remote_addr": "************", "user_agent": null}
تتبع الاستثناء:
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\app.py", line 917, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\app.py", line 891, in dispatch_request
    self.raise_routing_exception(req)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\app.py", line 500, in raise_routing_exception
    raise request.routing_exception  # type: ignore[misc]
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\ctx.py", line 362, in match_request
    result = self.url_adapter.match(return_rule=True)  # type: ignore
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\werkzeug\routing\map.py", line 629, in match
    raise NotFound() from None
werkzeug.exceptions.NotFound: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.

2025-05-19 01:09:35,899 - werkzeug - INFO - ************ - - [19/May/2025 01:09:35] "[35m[1mGET /favicon.ico HTTP/1.1[0m" 500 -
2025-05-19 01:09:40,750 - werkzeug - INFO - ************ - - [19/May/2025 01:09:40] "[32mGET /product-import HTTP/1.1[0m" 302 -
2025-05-19 01:09:40,862 - werkzeug - INFO - ************ - - [19/May/2025 01:09:40] "GET /products HTTP/1.1" 200 -
2025-05-19 01:09:41,090 - werkzeug - INFO - ************ - - [19/May/2025 01:09:41] "[36mGET /static/img/nobara-logo.svg HTTP/1.1[0m" 304 -
2025-05-19 01:09:41,808 - werkzeug - INFO - ************ - - [19/May/2025 01:09:41] "GET /api/notifications/count HTTP/1.1" 200 -
2025-05-19 01:09:41,853 - nobara - ERROR - استثناء: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.
معلومات الطلب: {"url": "http://************:5000/favicon.ico", "method": "GET", "headers": {"Host": "************:5000", "Connection": "keep-alive", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Accept": "image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8", "Referer": "http://************:5000/products", "Accept-Encoding": "gzip, deflate", "Accept-Language": "en-US,en;q=0.9", "Cookie": "NEXT_LOCALE=en; session=.eJzlkcFqwzAMhl_F-JJLKLGd2FYeY-y2lSJbMklJk-E4p9J3n5s9xkB8v4QQv5Ce8pYW3Cfe5fj1lKJUkZzzlmUrv4_OajqZTgZxCr9p3JmbUTT3eb2jvuRjLfODL5_8-Fmw8AcnzrxGFlu4cyyNmHAX6yawlDyHo7BoylwWbuT11f5b82tbf5B5n-RY8sG1mkmOUhN6B9RjSuwsOOUjRMDgEhE470zsfY3e6OHdTVobsqGLSgGgtuxTqmNJDQAqwODIWFaAXseu89EqRKcikRqMjZCiRfCko7NETBbZ1Cvcjp3z3zZKvn4BQauuqg.aCpapA.syIFF8GtVlKatRV_7xF8SdlHXA4"}, "args": {}, "form": {}, "cookies": {"NEXT_LOCALE": "en", "session": ".eJzlkcFqwzAMhl_F-JJLKLGd2FYeY-y2lSJbMklJk-E4p9J3n5s9xkB8v4QQv5Ce8pYW3Cfe5fj1lKJUkZzzlmUrv4_OajqZTgZxCr9p3JmbUTT3eb2jvuRjLfODL5_8-Fmw8AcnzrxGFlu4cyyNmHAX6yawlDyHo7BoylwWbuT11f5b82tbf5B5n-RY8sG1mkmOUhN6B9RjSuwsOOUjRMDgEhE470zsfY3e6OHdTVobsqGLSgGgtuxTqmNJDQAqwODIWFaAXseu89EqRKcikRqMjZCiRfCko7NETBbZ1Cvcjp3z3zZKvn4BQauuqg.aCpapA.syIFF8GtVlKatRV_7xF8SdlHXA4"}, "remote_addr": "************", "user_agent": null}
تتبع الاستثناء:
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\app.py", line 917, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\app.py", line 891, in dispatch_request
    self.raise_routing_exception(req)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\app.py", line 500, in raise_routing_exception
    raise request.routing_exception  # type: ignore[misc]
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\ctx.py", line 362, in match_request
    result = self.url_adapter.match(return_rule=True)  # type: ignore
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\werkzeug\routing\map.py", line 629, in match
    raise NotFound() from None
werkzeug.exceptions.NotFound: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.

2025-05-19 01:09:41,923 - werkzeug - INFO - ************ - - [19/May/2025 01:09:41] "[35m[1mGET /favicon.ico HTTP/1.1[0m" 500 -
2025-05-19 01:10:01,197 - werkzeug - INFO - ************ - - [19/May/2025 01:10:01] "GET /products/create HTTP/1.1" 200 -
2025-05-19 01:10:01,680 - werkzeug - INFO - ************ - - [19/May/2025 01:10:01] "[36mGET /static/img/nobara-logo.svg HTTP/1.1[0m" 304 -
2025-05-19 01:10:02,315 - nobara - ERROR - استثناء: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.
معلومات الطلب: {"url": "http://************:5000/favicon.ico", "method": "GET", "headers": {"Host": "************:5000", "Connection": "keep-alive", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Accept": "image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8", "Referer": "http://************:5000/products/create", "Accept-Encoding": "gzip, deflate", "Accept-Language": "en-US,en;q=0.9", "Cookie": "NEXT_LOCALE=en; session=.eJzlkcFqwzAMhl_F-JJLKLGd2FYeY-y2lSJbMklJk-E4p9J3n5s9xkB8v4QQv5Ce8pYW3Cfe5fj1lKJUkZzzlmUrv4_OajqZTgZxCr9p3JmbUTT3eb2jvuRjLfODL5_8-Fmw8AcnzrxGFlu4cyyNmHAX6yawlDyHo7BoylwWbuT11f5b82tbf5B5n-RY8sG1mkmOUhN6B9RjSuwsOOUjRMDgEhE470zsfY3e6OHdTVobsqGLSgGgtuxTqmNJDQAqwODIWFaAXseu89EqRKcikRqMjZCiRfCko7NETBbZ1Cvcjp3z3zZKvn4BQauuqg.aCpapA.syIFF8GtVlKatRV_7xF8SdlHXA4"}, "args": {}, "form": {}, "cookies": {"NEXT_LOCALE": "en", "session": ".eJzlkcFqwzAMhl_F-JJLKLGd2FYeY-y2lSJbMklJk-E4p9J3n5s9xkB8v4QQv5Ce8pYW3Cfe5fj1lKJUkZzzlmUrv4_OajqZTgZxCr9p3JmbUTT3eb2jvuRjLfODL5_8-Fmw8AcnzrxGFlu4cyyNmHAX6yawlDyHo7BoylwWbuT11f5b82tbf5B5n-RY8sG1mkmOUhN6B9RjSuwsOOUjRMDgEhE470zsfY3e6OHdTVobsqGLSgGgtuxTqmNJDQAqwODIWFaAXseu89EqRKcikRqMjZCiRfCko7NETBbZ1Cvcjp3z3zZKvn4BQauuqg.aCpapA.syIFF8GtVlKatRV_7xF8SdlHXA4"}, "remote_addr": "************", "user_agent": null}
تتبع الاستثناء:
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\app.py", line 917, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\app.py", line 891, in dispatch_request
    self.raise_routing_exception(req)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\app.py", line 500, in raise_routing_exception
    raise request.routing_exception  # type: ignore[misc]
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\ctx.py", line 362, in match_request
    result = self.url_adapter.match(return_rule=True)  # type: ignore
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\werkzeug\routing\map.py", line 629, in match
    raise NotFound() from None
werkzeug.exceptions.NotFound: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.

2025-05-19 01:10:02,318 - werkzeug - INFO - ************ - - [19/May/2025 01:10:02] "GET /api/notifications/count HTTP/1.1" 200 -
2025-05-19 01:10:02,385 - werkzeug - INFO - ************ - - [19/May/2025 01:10:02] "[35m[1mGET /favicon.ico HTTP/1.1[0m" 500 -
2025-05-19 01:11:05,970 - werkzeug - INFO - ************ - - [19/May/2025 01:11:05] "GET /products/create HTTP/1.1" 200 -
2025-05-19 01:11:15,194 - werkzeug - INFO - ************ - - [19/May/2025 01:11:15] "GET /products HTTP/1.1" 200 -
2025-05-19 01:11:15,423 - werkzeug - INFO - ************ - - [19/May/2025 01:11:15] "[36mGET /static/img/nobara-logo.svg HTTP/1.1[0m" 304 -
2025-05-19 01:11:16,186 - werkzeug - INFO - ************ - - [19/May/2025 01:11:16] "GET /api/notifications/count HTTP/1.1" 200 -
2025-05-19 01:11:16,205 - nobara - ERROR - استثناء: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.
معلومات الطلب: {"url": "http://************:5000/favicon.ico", "method": "GET", "headers": {"Host": "************:5000", "Connection": "keep-alive", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Accept": "image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8", "Referer": "http://************:5000/products", "Accept-Encoding": "gzip, deflate", "Accept-Language": "en-US,en;q=0.9", "Cookie": "NEXT_LOCALE=en; session=.eJzlkcFqwzAMhl_F-JJLKLGd2FYeY-y2lSJbMklJk-E4p9J3n5s9xkB8v4QQv5Ce8pYW3Cfe5fj1lKJUkZzzlmUrv4_OajqZTgZxCr9p3JmbUTT3eb2jvuRjLfODL5_8-Fmw8AcnzrxGFlu4cyyNmHAX6yawlDyHo7BoylwWbuT11f5b82tbf5B5n-RY8sG1mkmOUhN6B9RjSuwsOOUjRMDgEhE470zsfY3e6OHdTVobsqGLSgGgtuxTqmNJDQAqwODIWFaAXseu89EqRKcikRqMjZCiRfCko7NETBbZ1Cvcjp3z3zZKvn4BQauuqg.aCpapA.syIFF8GtVlKatRV_7xF8SdlHXA4"}, "args": {}, "form": {}, "cookies": {"NEXT_LOCALE": "en", "session": ".eJzlkcFqwzAMhl_F-JJLKLGd2FYeY-y2lSJbMklJk-E4p9J3n5s9xkB8v4QQv5Ce8pYW3Cfe5fj1lKJUkZzzlmUrv4_OajqZTgZxCr9p3JmbUTT3eb2jvuRjLfODL5_8-Fmw8AcnzrxGFlu4cyyNmHAX6yawlDyHo7BoylwWbuT11f5b82tbf5B5n-RY8sG1mkmOUhN6B9RjSuwsOOUjRMDgEhE470zsfY3e6OHdTVobsqGLSgGgtuxTqmNJDQAqwODIWFaAXseu89EqRKcikRqMjZCiRfCko7NETBbZ1Cvcjp3z3zZKvn4BQauuqg.aCpapA.syIFF8GtVlKatRV_7xF8SdlHXA4"}, "remote_addr": "************", "user_agent": null}
تتبع الاستثناء:
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\app.py", line 917, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\app.py", line 891, in dispatch_request
    self.raise_routing_exception(req)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\app.py", line 500, in raise_routing_exception
    raise request.routing_exception  # type: ignore[misc]
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\ctx.py", line 362, in match_request
    result = self.url_adapter.match(return_rule=True)  # type: ignore
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\werkzeug\routing\map.py", line 629, in match
    raise NotFound() from None
werkzeug.exceptions.NotFound: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.

2025-05-19 01:11:16,337 - werkzeug - INFO - ************ - - [19/May/2025 01:11:16] "[35m[1mGET /favicon.ico HTTP/1.1[0m" 500 -
2025-05-19 01:11:35,771 - werkzeug - INFO - ************ - - [19/May/2025 01:11:35] "GET /products HTTP/1.1" 200 -
2025-05-19 01:11:35,860 - werkzeug - INFO - ************ - - [19/May/2025 01:11:35] "[36mGET /static/img/nobara-logo.svg HTTP/1.1[0m" 304 -
2025-05-19 01:11:36,461 - werkzeug - INFO - ************ - - [19/May/2025 01:11:36] "GET /api/notifications/count HTTP/1.1" 200 -
2025-05-19 01:11:36,471 - nobara - ERROR - استثناء: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.
معلومات الطلب: {"url": "http://************:5000/favicon.ico", "method": "GET", "headers": {"Host": "************:5000", "Connection": "keep-alive", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Accept": "image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8", "Referer": "http://************:5000/products", "Accept-Encoding": "gzip, deflate", "Accept-Language": "en-US,en;q=0.9", "Cookie": "NEXT_LOCALE=en; session=.eJzlkcFqwzAMhl_F-JJLKLGd2FYeY-y2lSJbMklJk-E4p9J3n5s9xkB8v4QQv5Ce8pYW3Cfe5fj1lKJUkZzzlmUrv4_OajqZTgZxCr9p3JmbUTT3eb2jvuRjLfODL5_8-Fmw8AcnzrxGFlu4cyyNmHAX6yawlDyHo7BoylwWbuT11f5b82tbf5B5n-RY8sG1mkmOUhN6B9RjSuwsOOUjRMDgEhE470zsfY3e6OHdTVobsqGLSgGgtuxTqmNJDQAqwODIWFaAXseu89EqRKcikRqMjZCiRfCko7NETBbZ1Cvcjp3z3zZKvn4BQauuqg.aCpapA.syIFF8GtVlKatRV_7xF8SdlHXA4"}, "args": {}, "form": {}, "cookies": {"NEXT_LOCALE": "en", "session": ".eJzlkcFqwzAMhl_F-JJLKLGd2FYeY-y2lSJbMklJk-E4p9J3n5s9xkB8v4QQv5Ce8pYW3Cfe5fj1lKJUkZzzlmUrv4_OajqZTgZxCr9p3JmbUTT3eb2jvuRjLfODL5_8-Fmw8AcnzrxGFlu4cyyNmHAX6yawlDyHo7BoylwWbuT11f5b82tbf5B5n-RY8sG1mkmOUhN6B9RjSuwsOOUjRMDgEhE470zsfY3e6OHdTVobsqGLSgGgtuxTqmNJDQAqwODIWFaAXseu89EqRKcikRqMjZCiRfCko7NETBbZ1Cvcjp3z3zZKvn4BQauuqg.aCpapA.syIFF8GtVlKatRV_7xF8SdlHXA4"}, "remote_addr": "************", "user_agent": null}
تتبع الاستثناء:
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\app.py", line 917, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\app.py", line 891, in dispatch_request
    self.raise_routing_exception(req)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\app.py", line 500, in raise_routing_exception
    raise request.routing_exception  # type: ignore[misc]
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\ctx.py", line 362, in match_request
    result = self.url_adapter.match(return_rule=True)  # type: ignore
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\werkzeug\routing\map.py", line 629, in match
    raise NotFound() from None
werkzeug.exceptions.NotFound: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.

2025-05-19 01:11:36,505 - werkzeug - INFO - ************ - - [19/May/2025 01:11:36] "[35m[1mGET /favicon.ico HTTP/1.1[0m" 500 -
2025-05-19 01:11:38,876 - werkzeug - INFO - ************ - - [19/May/2025 01:11:38] "[32mGET /products/import-export HTTP/1.1[0m" 302 -
2025-05-19 01:11:38,916 - werkzeug - INFO - ************ - - [19/May/2025 01:11:38] "GET /products HTTP/1.1" 200 -
2025-05-19 01:11:39,022 - werkzeug - INFO - ************ - - [19/May/2025 01:11:39] "[36mGET /static/img/nobara-logo.svg HTTP/1.1[0m" 304 -
2025-05-19 01:11:39,546 - werkzeug - INFO - ************ - - [19/May/2025 01:11:39] "GET /api/notifications/count HTTP/1.1" 200 -
2025-05-19 01:11:39,556 - nobara - ERROR - استثناء: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.
معلومات الطلب: {"url": "http://************:5000/favicon.ico", "method": "GET", "headers": {"Host": "************:5000", "Connection": "keep-alive", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Accept": "image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8", "Referer": "http://************:5000/products", "Accept-Encoding": "gzip, deflate", "Accept-Language": "en-US,en;q=0.9", "Cookie": "NEXT_LOCALE=en; session=.eJztksFqwzAMhl_F-JJLKLGd2FYeo-y2laJYMklIk-E4p9J3n5s9xC4D8f0SQvxC6CnvccF95F32n08pchHJKW1J1vLraKymk_HkIE7hN407c9OLap7WGfUlHWueHnz54Mf3gpmvHDnxGlhsw8whV2LEXaybwJzTNByZRZWnvHAlb6_63_wvzG91eYDE-yj7nA4u1USyl5rQO6AWY2RnwSkfIAAOLhKB886E1pdoje7e3ai1ITs0QSkA1JZ9jGUsqg5ADdA5MpYVoNehaXywCtGpQKQ6YwPEYBE86eAsEZNFNuUK92Pn9LuNkq8frwfXjQ.aCpbGg.JT0AyNc7wFmzIJ2Viu-VRYeE1oQ"}, "args": {}, "form": {}, "cookies": {"NEXT_LOCALE": "en", "session": ".eJztksFqwzAMhl_F-JJLKLGd2FYeo-y2laJYMklIk-E4p9J3n5s9xC4D8f0SQvxC6CnvccF95F32n08pchHJKW1J1vLraKymk_HkIE7hN407c9OLap7WGfUlHWueHnz54Mf3gpmvHDnxGlhsw8whV2LEXaybwJzTNByZRZWnvHAlb6_63_wvzG91eYDE-yj7nA4u1USyl5rQO6AWY2RnwSkfIAAOLhKB886E1pdoje7e3ai1ITs0QSkA1JZ9jGUsqg5ADdA5MpYVoNehaXywCtGpQKQ6YwPEYBE86eAsEZNFNuUK92Pn9LuNkq8frwfXjQ.aCpbGg.JT0AyNc7wFmzIJ2Viu-VRYeE1oQ"}, "remote_addr": "************", "user_agent": null}
تتبع الاستثناء:
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\app.py", line 917, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\app.py", line 891, in dispatch_request
    self.raise_routing_exception(req)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\app.py", line 500, in raise_routing_exception
    raise request.routing_exception  # type: ignore[misc]
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\ctx.py", line 362, in match_request
    result = self.url_adapter.match(return_rule=True)  # type: ignore
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\werkzeug\routing\map.py", line 629, in match
    raise NotFound() from None
werkzeug.exceptions.NotFound: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.

2025-05-19 01:11:39,573 - werkzeug - INFO - ************ - - [19/May/2025 01:11:39] "[35m[1mGET /favicon.ico HTTP/1.1[0m" 500 -
2025-05-19 01:11:43,003 - werkzeug - INFO - ************ - - [19/May/2025 01:11:43] "[32mGET /products/import-export HTTP/1.1[0m" 302 -
2025-05-19 01:11:43,053 - werkzeug - INFO - ************ - - [19/May/2025 01:11:43] "GET /products HTTP/1.1" 200 -
2025-05-19 01:11:43,160 - werkzeug - INFO - ************ - - [19/May/2025 01:11:43] "[36mGET /static/img/nobara-logo.svg HTTP/1.1[0m" 304 -
2025-05-19 01:11:43,787 - werkzeug - INFO - ************ - - [19/May/2025 01:11:43] "GET /api/notifications/count HTTP/1.1" 200 -
2025-05-19 01:11:43,837 - nobara - ERROR - استثناء: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.
معلومات الطلب: {"url": "http://************:5000/favicon.ico", "method": "GET", "headers": {"Host": "************:5000", "Connection": "keep-alive", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Accept": "image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8", "Referer": "http://************:5000/products", "Accept-Encoding": "gzip, deflate", "Accept-Language": "en-US,en;q=0.9", "Cookie": "NEXT_LOCALE=en; session=.eJztksFqwzAMhl_F-JJLKLGd2FYeo-y2laJYMklIk-E4p9J3n5s9xgbi-yWE-IXQU97jgvvIu-w_n1LkIpJT2pKs5dfRWE0n48lBnMJvGnfmphfVPK0z6ks61jw9-PLBj-8FM185cuI1sNiGmUOuxIi7WDeBOadpODKLKk954UreXvW_-Z8zv9Xl-xLvo-xzOrhUE8leakLvgFqMkZ0Fp3yAADi4SATOOxNaX6I1unt3o9aG7NAEpQBQW_YxlrGoOgA1QOfIWFaAXoem8cEqRKcCkeqMDRCDRfCkg7NETBbZlCvcj53T7zZKvn4AFHUAfw.aCpbHw.4GZUqElJVuQrNozJL7R0ATCNtRQ"}, "args": {}, "form": {}, "cookies": {"NEXT_LOCALE": "en", "session": ".eJztksFqwzAMhl_F-JJLKLGd2FYeo-y2laJYMklIk-E4p9J3n5s9xgbi-yWE-IXQU97jgvvIu-w_n1LkIpJT2pKs5dfRWE0n48lBnMJvGnfmphfVPK0z6ks61jw9-PLBj-8FM185cuI1sNiGmUOuxIi7WDeBOadpODKLKk954UreXvW_-Z8zv9Xl-xLvo-xzOrhUE8leakLvgFqMkZ0Fp3yAADi4SATOOxNaX6I1unt3o9aG7NAEpQBQW_YxlrGoOgA1QOfIWFaAXoem8cEqRKcCkeqMDRCDRfCkg7NETBbZlCvcj53T7zZKvn4AFHUAfw.aCpbHw.4GZUqElJVuQrNozJL7R0ATCNtRQ"}, "remote_addr": "************", "user_agent": null}
تتبع الاستثناء:
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\app.py", line 917, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\app.py", line 891, in dispatch_request
    self.raise_routing_exception(req)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\app.py", line 500, in raise_routing_exception
    raise request.routing_exception  # type: ignore[misc]
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\ctx.py", line 362, in match_request
    result = self.url_adapter.match(return_rule=True)  # type: ignore
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\werkzeug\routing\map.py", line 629, in match
    raise NotFound() from None
werkzeug.exceptions.NotFound: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.

2025-05-19 01:11:43,883 - werkzeug - INFO - ************ - - [19/May/2025 01:11:43] "[35m[1mGET /favicon.ico HTTP/1.1[0m" 500 -
2025-05-19 01:12:20,756 - werkzeug - INFO - ************ - - [19/May/2025 01:12:20] "GET /purchases HTTP/1.1" 200 -
2025-05-19 01:12:20,908 - werkzeug - INFO - ************ - - [19/May/2025 01:12:20] "[36mGET /static/img/nobara-logo.svg HTTP/1.1[0m" 304 -
2025-05-19 01:12:21,734 - werkzeug - INFO - ************ - - [19/May/2025 01:12:21] "GET /api/notifications/count HTTP/1.1" 200 -
2025-05-19 01:12:21,742 - nobara - ERROR - استثناء: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.
معلومات الطلب: {"url": "http://************:5000/favicon.ico", "method": "GET", "headers": {"Host": "************:5000", "Connection": "keep-alive", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Accept": "image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8", "Referer": "http://************:5000/purchases", "Accept-Encoding": "gzip, deflate", "Accept-Language": "en-US,en;q=0.9", "Cookie": "NEXT_LOCALE=en; session=.eJztksFqwzAMhl_F-JJLKLGd2FYeo-y2laJYMklIk-E4p9J3n5s9xgbi-yWE-IXQU97jgvvIu-w_n1LkIpJT2pKs5dfRWE0n48lBnMJvGnfmphfVPK0z6ks61jw9-PLBj-8FM185cuI1sNiGmUOuxIi7WDeBOadpODKLKk954UreXvW_-Z8zv9Xl-xLvo-xzOrhUE8leakLvgFqMkZ0Fp3yAADi4SATOOxNaX6I1unt3o9aG7NAEpQBQW_YxlrGoOgA1QOfIWFaAXoem8cEqRKcCkeqMDRCDRfCkg7NETBbZlCvcj53T7zZKvn4AFHUAfw.aCpbHw.4GZUqElJVuQrNozJL7R0ATCNtRQ"}, "args": {}, "form": {}, "cookies": {"NEXT_LOCALE": "en", "session": ".eJztksFqwzAMhl_F-JJLKLGd2FYeo-y2laJYMklIk-E4p9J3n5s9xgbi-yWE-IXQU97jgvvIu-w_n1LkIpJT2pKs5dfRWE0n48lBnMJvGnfmphfVPK0z6ks61jw9-PLBj-8FM185cuI1sNiGmUOuxIi7WDeBOadpODKLKk954UreXvW_-Z8zv9Xl-xLvo-xzOrhUE8leakLvgFqMkZ0Fp3yAADi4SATOOxNaX6I1unt3o9aG7NAEpQBQW_YxlrGoOgA1QOfIWFaAXoem8cEqRKcCkeqMDRCDRfCkg7NETBbZlCvcj53T7zZKvn4AFHUAfw.aCpbHw.4GZUqElJVuQrNozJL7R0ATCNtRQ"}, "remote_addr": "************", "user_agent": null}
تتبع الاستثناء:
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\app.py", line 917, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\app.py", line 891, in dispatch_request
    self.raise_routing_exception(req)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\app.py", line 500, in raise_routing_exception
    raise request.routing_exception  # type: ignore[misc]
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\ctx.py", line 362, in match_request
    result = self.url_adapter.match(return_rule=True)  # type: ignore
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\werkzeug\routing\map.py", line 629, in match
    raise NotFound() from None
werkzeug.exceptions.NotFound: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.

2025-05-19 01:12:21,828 - werkzeug - INFO - ************ - - [19/May/2025 01:12:21] "[35m[1mGET /favicon.ico HTTP/1.1[0m" 500 -
2025-05-19 01:12:25,613 - werkzeug - INFO - ************ - - [19/May/2025 01:12:25] "GET /home HTTP/1.1" 200 -
2025-05-19 01:12:25,725 - werkzeug - INFO - ************ - - [19/May/2025 01:12:25] "[36mGET /static/css/nobara-pro.css?v=1.0.0 HTTP/1.1[0m" 304 -
2025-05-19 01:12:25,814 - werkzeug - INFO - ************ - - [19/May/2025 01:12:25] "[36mGET /static/css/dashboard-pro.css?v=1.0.1 HTTP/1.1[0m" 304 -
2025-05-19 01:12:25,819 - werkzeug - INFO - ************ - - [19/May/2025 01:12:25] "[36mGET /static/js/notifications-system.js?v=1.0.0 HTTP/1.1[0m" 304 -
2025-05-19 01:12:25,823 - werkzeug - INFO - ************ - - [19/May/2025 01:12:25] "[36mGET /static/js/system-manager.js?v=1.0.0 HTTP/1.1[0m" 304 -
2025-05-19 01:12:25,824 - werkzeug - INFO - ************ - - [19/May/2025 01:12:25] "[36mGET /static/uploads/logo_20250518120852.jpeg HTTP/1.1[0m" 304 -
2025-05-19 01:12:25,824 - werkzeug - INFO - ************ - - [19/May/2025 01:12:25] "[36mGET /static/js/dashboard-manager.js?v=1.0.0 HTTP/1.1[0m" 304 -
2025-05-19 01:12:26,210 - werkzeug - INFO - ************ - - [19/May/2025 01:12:26] "[36mGET /static/js/dashboard-pro.js?v=1.0.0 HTTP/1.1[0m" 304 -
2025-05-19 01:12:27,011 - werkzeug - INFO - ************ - - [19/May/2025 01:12:27] "GET /api/settings HTTP/1.1" 200 -
2025-05-19 01:12:27,030 - werkzeug - INFO - ************ - - [19/May/2025 01:12:27] "GET /api/notifications/count HTTP/1.1" 200 -
2025-05-19 01:12:27,157 - werkzeug - INFO - ************ - - [19/May/2025 01:12:27] "GET /api/category-distribution HTTP/1.1" 200 -
2025-05-19 01:12:27,165 - werkzeug - INFO - ************ - - [19/May/2025 01:12:27] "GET /api/sales-chart-data?days=30 HTTP/1.1" 200 -
2025-05-19 01:12:27,174 - werkzeug - INFO - ************ - - [19/May/2025 01:12:27] "GET /api/settings HTTP/1.1" 200 -
2025-05-19 01:12:27,265 - werkzeug - INFO - ************ - - [19/May/2025 01:12:27] "GET /api/dashboard HTTP/1.1" 200 -
2025-05-19 01:12:27,415 - werkzeug - INFO - ************ - - [19/May/2025 01:12:27] "GET /api/dashboard HTTP/1.1" 200 -
2025-05-19 01:12:27,795 - werkzeug - INFO - ************ - - [19/May/2025 01:12:27] "GET /api/notifications/count HTTP/1.1" 200 -
2025-05-19 01:12:27,875 - werkzeug - INFO - ************ - - [19/May/2025 01:12:27] "GET /api/notifications/recent HTTP/1.1" 200 -
2025-05-19 01:12:27,877 - nobara - ERROR - استثناء: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.
معلومات الطلب: {"url": "http://************:5000/favicon.ico", "method": "GET", "headers": {"Host": "************:5000", "Connection": "keep-alive", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Accept": "image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8", "Referer": "http://************:5000/home", "Accept-Encoding": "gzip, deflate", "Accept-Language": "en-US,en;q=0.9", "Cookie": "NEXT_LOCALE=en; session=.eJztksFqwzAMhl_F-JJLKLGd2FYeo-y2laJYMklIk-E4p9J3n5s9xgbi-yWE-IXQU97jgvvIu-w_n1LkIpJT2pKs5dfRWE0n48lBnMJvGnfmphfVPK0z6ks61jw9-PLBj-8FM185cuI1sNiGmUOuxIi7WDeBOadpODKLKk954UreXvW_-Z8zv9Xl-xLvo-xzOrhUE8leakLvgFqMkZ0Fp3yAADi4SATOOxNaX6I1unt3o9aG7NAEpQBQW_YxlrGoOgA1QOfIWFaAXoem8cEqRKcCkeqMDRCDRfCkg7NETBbZlCvcj53T7zZKvn4AFHUAfw.aCpbHw.4GZUqElJVuQrNozJL7R0ATCNtRQ"}, "args": {}, "form": {}, "cookies": {"NEXT_LOCALE": "en", "session": ".eJztksFqwzAMhl_F-JJLKLGd2FYeo-y2laJYMklIk-E4p9J3n5s9xgbi-yWE-IXQU97jgvvIu-w_n1LkIpJT2pKs5dfRWE0n48lBnMJvGnfmphfVPK0z6ks61jw9-PLBj-8FM185cuI1sNiGmUOuxIi7WDeBOadpODKLKk954UreXvW_-Z8zv9Xl-xLvo-xzOrhUE8leakLvgFqMkZ0Fp3yAADi4SATOOxNaX6I1unt3o9aG7NAEpQBQW_YxlrGoOgA1QOfIWFaAXoem8cEqRKcCkeqMDRCDRfCkg7NETBbZlCvcj53T7zZKvn4AFHUAfw.aCpbHw.4GZUqElJVuQrNozJL7R0ATCNtRQ"}, "remote_addr": "************", "user_agent": null}
تتبع الاستثناء:
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\app.py", line 917, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\app.py", line 891, in dispatch_request
    self.raise_routing_exception(req)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\app.py", line 500, in raise_routing_exception
    raise request.routing_exception  # type: ignore[misc]
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\ctx.py", line 362, in match_request
    result = self.url_adapter.match(return_rule=True)  # type: ignore
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\werkzeug\routing\map.py", line 629, in match
    raise NotFound() from None
werkzeug.exceptions.NotFound: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.

2025-05-19 01:12:27,898 - werkzeug - INFO - ************ - - [19/May/2025 01:12:27] "GET /api/notifications/count HTTP/1.1" 200 -
2025-05-19 01:12:27,977 - werkzeug - INFO - ************ - - [19/May/2025 01:12:27] "[35m[1mGET /favicon.ico HTTP/1.1[0m" 500 -
2025-05-19 01:12:27,991 - werkzeug - INFO - ************ - - [19/May/2025 01:12:27] "GET /api/notifications/recent HTTP/1.1" 200 -
2025-05-19 01:12:28,423 - werkzeug - INFO - ************ - - [19/May/2025 01:12:28] "GET /pos HTTP/1.1" 200 -
2025-05-19 01:12:28,594 - werkzeug - INFO - ************ - - [19/May/2025 01:12:28] "[36mGET /static/css/nobara-pro.css?v=1.0.0 HTTP/1.1[0m" 304 -
2025-05-19 01:12:28,619 - werkzeug - INFO - ************ - - [19/May/2025 01:12:28] "[36mGET /static/css/dashboard-pro.css?v=1.0.1 HTTP/1.1[0m" 304 -
2025-05-19 01:12:28,630 - werkzeug - INFO - ************ - - [19/May/2025 01:12:28] "[36mGET /static/js/notifications-system.js?v=1.0.0 HTTP/1.1[0m" 304 -
2025-05-19 01:12:28,637 - werkzeug - INFO - ************ - - [19/May/2025 01:12:28] "[36mGET /static/js/system-manager.js?v=1.0.0 HTTP/1.1[0m" 304 -
2025-05-19 01:12:28,638 - werkzeug - INFO - ************ - - [19/May/2025 01:12:28] "[36mGET /static/img/nobara-logo.svg HTTP/1.1[0m" 304 -
2025-05-19 01:12:28,751 - werkzeug - INFO - ************ - - [19/May/2025 01:12:28] "GET /static/js/pos.js HTTP/1.1" 200 -
2025-05-19 01:12:28,831 - werkzeug - INFO - ************ - - [19/May/2025 01:12:28] "GET /static/css/pos-theme.css?v=1.0.0 HTTP/1.1" 200 -
2025-05-19 01:12:28,846 - werkzeug - INFO - ************ - - [19/May/2025 01:12:28] "GET /static/js/pos_part2.js HTTP/1.1" 200 -
2025-05-19 01:12:28,929 - werkzeug - INFO - ************ - - [19/May/2025 01:12:28] "GET /static/js/pos_part4.js HTTP/1.1" 200 -
2025-05-19 01:12:28,990 - werkzeug - INFO - ************ - - [19/May/2025 01:12:28] "GET /static/js/pos_credit.js HTTP/1.1" 200 -
2025-05-19 01:12:29,146 - werkzeug - INFO - ************ - - [19/May/2025 01:12:29] "GET /static/js/payment_methods.js HTTP/1.1" 200 -
2025-05-19 01:12:29,624 - werkzeug - INFO - ************ - - [19/May/2025 01:12:29] "GET /static/js/pos_returns.js HTTP/1.1" 200 -
2025-05-19 01:12:29,625 - werkzeug - INFO - ************ - - [19/May/2025 01:12:29] "GET /static/js/pos_part3.js HTTP/1.1" 200 -
2025-05-19 01:12:29,625 - werkzeug - INFO - ************ - - [19/May/2025 01:12:29] "GET /static/js/customer_search.js HTTP/1.1" 200 -
2025-05-19 01:12:29,641 - werkzeug - INFO - ************ - - [19/May/2025 01:12:29] "GET /static/js/invoice-search.js HTTP/1.1" 200 -
2025-05-19 01:12:30,052 - nobara - ERROR - استثناء: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.
معلومات الطلب: {"url": "http://************:5000/api/log-error", "method": "POST", "headers": {"Host": "************:5000", "Connection": "keep-alive", "Content-Length": "397", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Content-Type": "application/json", "Accept": "*/*", "Origin": "http://************:5000", "Referer": "http://************:5000/pos", "Accept-Encoding": "gzip, deflate", "Accept-Language": "en-US,en;q=0.9", "Cookie": "NEXT_LOCALE=en; session=.eJztksFqwzAMhl_F-JJLKLGd2FYeo-y2laJYMklIk-E4p9J3n5s9xgbi-yWE-IXQU97jgvvIu-w_n1LkIpJT2pKs5dfRWE0n48lBnMJvGnfmphfVPK0z6ks61jw9-PLBj-8FM185cuI1sNiGmUOuxIi7WDeBOadpODKLKk954UreXvW_-Z8zv9Xl-xLvo-xzOrhUE8leakLvgFqMkZ0Fp3yAADi4SATOOxNaX6I1unt3o9aG7NAEpQBQW_YxlrGoOgA1QOfIWFaAXoem8cEqRKcCkeqMDRCDRfCkg7NETBbZlCvcj53T7zZKvn4AFHUAfw.aCpbHw.4GZUqElJVuQrNozJL7R0ATCNtRQ"}, "args": {}, "form": {}, "cookies": {"NEXT_LOCALE": "en", "session": ".eJztksFqwzAMhl_F-JJLKLGd2FYeo-y2laJYMklIk-E4p9J3n5s9xgbi-yWE-IXQU97jgvvIu-w_n1LkIpJT2pKs5dfRWE0n48lBnMJvGnfmphfVPK0z6ks61jw9-PLBj-8FM185cuI1sNiGmUOuxIi7WDeBOadpODKLKk954UreXvW_-Z8zv9Xl-xLvo-xzOrhUE8leakLvgFqMkZ0Fp3yAADi4SATOOxNaX6I1unt3o9aG7NAEpQBQW_YxlrGoOgA1QOfIWFaAXoem8cEqRKcCkeqMDRCDRfCkg7NETBbZlCvcj53T7zZKvn4AFHUAfw.aCpbHw.4GZUqElJVuQrNozJL7R0ATCNtRQ"}, "remote_addr": "************", "user_agent": null}
تتبع الاستثناء:
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\app.py", line 917, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\app.py", line 891, in dispatch_request
    self.raise_routing_exception(req)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\app.py", line 500, in raise_routing_exception
    raise request.routing_exception  # type: ignore[misc]
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\ctx.py", line 362, in match_request
    result = self.url_adapter.match(return_rule=True)  # type: ignore
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\werkzeug\routing\map.py", line 629, in match
    raise NotFound() from None
werkzeug.exceptions.NotFound: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.

2025-05-19 01:12:30,201 - werkzeug - INFO - ************ - - [19/May/2025 01:12:30] "[35m[1mPOST /api/log-error HTTP/1.1[0m" 500 -
2025-05-19 01:12:30,243 - werkzeug - INFO - ************ - - [19/May/2025 01:12:30] "GET /api/notifications/count HTTP/1.1" 200 -
2025-05-19 01:12:30,333 - werkzeug - INFO - ************ - - [19/May/2025 01:12:30] "GET /api/settings HTTP/1.1" 200 -
2025-05-19 01:12:30,367 - werkzeug - INFO - ************ - - [19/May/2025 01:12:30] "GET /api/notifications/recent HTTP/1.1" 200 -
2025-05-19 01:12:30,492 - nobara - ERROR - استثناء: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.
معلومات الطلب: {"url": "http://************:5000/favicon.ico", "method": "GET", "headers": {"Host": "************:5000", "Connection": "keep-alive", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Accept": "image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8", "Referer": "http://************:5000/pos", "Accept-Encoding": "gzip, deflate", "Accept-Language": "en-US,en;q=0.9", "Cookie": "NEXT_LOCALE=en; session=.eJztksFqwzAMhl_F-JJLKLGd2FYeo-y2laJYMklIk-E4p9J3n5s9xgbi-yWE-IXQU97jgvvIu-w_n1LkIpJT2pKs5dfRWE0n48lBnMJvGnfmphfVPK0z6ks61jw9-PLBj-8FM185cuI1sNiGmUOuxIi7WDeBOadpODKLKk954UreXvW_-Z8zv9Xl-xLvo-xzOrhUE8leakLvgFqMkZ0Fp3yAADi4SATOOxNaX6I1unt3o9aG7NAEpQBQW_YxlrGoOgA1QOfIWFaAXoem8cEqRKcCkeqMDRCDRfCkg7NETBbZlCvcj53T7zZKvn4AFHUAfw.aCpbHw.4GZUqElJVuQrNozJL7R0ATCNtRQ"}, "args": {}, "form": {}, "cookies": {"NEXT_LOCALE": "en", "session": ".eJztksFqwzAMhl_F-JJLKLGd2FYeo-y2laJYMklIk-E4p9J3n5s9xgbi-yWE-IXQU97jgvvIu-w_n1LkIpJT2pKs5dfRWE0n48lBnMJvGnfmphfVPK0z6ks61jw9-PLBj-8FM185cuI1sNiGmUOuxIi7WDeBOadpODKLKk954UreXvW_-Z8zv9Xl-xLvo-xzOrhUE8leakLvgFqMkZ0Fp3yAADi4SATOOxNaX6I1unt3o9aG7NAEpQBQW_YxlrGoOgA1QOfIWFaAXoem8cEqRKcCkeqMDRCDRfCkg7NETBbZlCvcj53T7zZKvn4AFHUAfw.aCpbHw.4GZUqElJVuQrNozJL7R0ATCNtRQ"}, "remote_addr": "************", "user_agent": null}
تتبع الاستثناء:
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\app.py", line 917, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\app.py", line 891, in dispatch_request
    self.raise_routing_exception(req)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\app.py", line 500, in raise_routing_exception
    raise request.routing_exception  # type: ignore[misc]
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\ctx.py", line 362, in match_request
    result = self.url_adapter.match(return_rule=True)  # type: ignore
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\werkzeug\routing\map.py", line 629, in match
    raise NotFound() from None
werkzeug.exceptions.NotFound: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.

2025-05-19 01:12:30,509 - werkzeug - INFO - ************ - - [19/May/2025 01:12:30] "[35m[1mGET /favicon.ico HTTP/1.1[0m" 500 -
2025-05-19 01:12:30,511 - werkzeug - INFO - ************ - - [19/May/2025 01:12:30] "GET /api/notifications/count HTTP/1.1" 200 -
2025-05-19 01:12:30,799 - werkzeug - INFO - ************ - - [19/May/2025 01:12:30] "GET /api/pos/products?warehouse_id=1 HTTP/1.1" 200 -
2025-05-19 01:12:33,718 - nobara - ERROR - استثناء: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.
معلومات الطلب: {"url": "http://************:5000/api/log-error", "method": "POST", "headers": {"Host": "************:5000", "Connection": "keep-alive", "Content-Length": "275", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Content-Type": "application/json", "Accept": "*/*", "Origin": "http://************:5000", "Referer": "http://************:5000/pos", "Accept-Encoding": "gzip, deflate", "Accept-Language": "en-US,en;q=0.9", "Cookie": "NEXT_LOCALE=en; session=.eJztksFqwzAMhl_F-JJLKLGd2FYeo-y2laJYMklIk-E4p9J3n5s9xgbi-yWE-IXQU97jgvvIu-w_n1LkIpJT2pKs5dfRWE0n48lBnMJvGnfmphfVPK0z6ks61jw9-PLBj-8FM185cuI1sNiGmUOuxIi7WDeBOadpODKLKk954UreXvW_-Z8zv9Xl-xLvo-xzOrhUE8leakLvgFqMkZ0Fp3yAADi4SATOOxNaX6I1unt3o9aG7NAEpQBQW_YxlrGoOgA1QOfIWFaAXoem8cEqRKcCkeqMDRCDRfCkg7NETBbZlCvcj53T7zZKvn4AFHUAfw.aCpbHw.4GZUqElJVuQrNozJL7R0ATCNtRQ"}, "args": {}, "form": {}, "cookies": {"NEXT_LOCALE": "en", "session": ".eJztksFqwzAMhl_F-JJLKLGd2FYeo-y2laJYMklIk-E4p9J3n5s9xgbi-yWE-IXQU97jgvvIu-w_n1LkIpJT2pKs5dfRWE0n48lBnMJvGnfmphfVPK0z6ks61jw9-PLBj-8FM185cuI1sNiGmUOuxIi7WDeBOadpODKLKk954UreXvW_-Z8zv9Xl-xLvo-xzOrhUE8leakLvgFqMkZ0Fp3yAADi4SATOOxNaX6I1unt3o9aG7NAEpQBQW_YxlrGoOgA1QOfIWFaAXoem8cEqRKcCkeqMDRCDRfCkg7NETBbZlCvcj53T7zZKvn4AFHUAfw.aCpbHw.4GZUqElJVuQrNozJL7R0ATCNtRQ"}, "remote_addr": "************", "user_agent": null}
تتبع الاستثناء:
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\app.py", line 917, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\app.py", line 891, in dispatch_request
    self.raise_routing_exception(req)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\app.py", line 500, in raise_routing_exception
    raise request.routing_exception  # type: ignore[misc]
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\ctx.py", line 362, in match_request
    result = self.url_adapter.match(return_rule=True)  # type: ignore
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\werkzeug\routing\map.py", line 629, in match
    raise NotFound() from None
werkzeug.exceptions.NotFound: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.

2025-05-19 01:12:33,765 - werkzeug - INFO - ************ - - [19/May/2025 01:12:33] "[35m[1mPOST /api/log-error HTTP/1.1[0m" 500 -
2025-05-19 01:13:00,050 - werkzeug - INFO - ************ - - [19/May/2025 01:13:00] "GET /api/notifications/recent HTTP/1.1" 200 -
2025-05-19 01:13:07,431 - werkzeug - INFO - ************ - - [19/May/2025 01:13:07] "[32mGET /settings HTTP/1.1[0m" 302 -
2025-05-19 01:13:07,541 - werkzeug - INFO - ************ - - [19/May/2025 01:13:07] "GET /home HTTP/1.1" 200 -
2025-05-19 01:13:07,637 - werkzeug - INFO - ************ - - [19/May/2025 01:13:07] "[36mGET /static/css/nobara-pro.css?v=1.0.0 HTTP/1.1[0m" 304 -
2025-05-19 01:13:07,640 - werkzeug - INFO - ************ - - [19/May/2025 01:13:07] "[36mGET /static/css/dashboard-pro.css?v=1.0.1 HTTP/1.1[0m" 304 -
2025-05-19 01:13:07,646 - werkzeug - INFO - ************ - - [19/May/2025 01:13:07] "[36mGET /static/js/notifications-system.js?v=1.0.0 HTTP/1.1[0m" 304 -
2025-05-19 01:13:07,667 - werkzeug - INFO - ************ - - [19/May/2025 01:13:07] "[36mGET /static/js/system-manager.js?v=1.0.0 HTTP/1.1[0m" 304 -
2025-05-19 01:13:07,668 - werkzeug - INFO - ************ - - [19/May/2025 01:13:07] "[36mGET /static/uploads/logo_20250518120852.jpeg HTTP/1.1[0m" 304 -
2025-05-19 01:13:07,670 - werkzeug - INFO - ************ - - [19/May/2025 01:13:07] "[36mGET /static/js/dashboard-manager.js?v=1.0.0 HTTP/1.1[0m" 304 -
2025-05-19 01:13:07,755 - werkzeug - INFO - ************ - - [19/May/2025 01:13:07] "[36mGET /static/js/dashboard-pro.js?v=1.0.0 HTTP/1.1[0m" 304 -
2025-05-19 01:13:08,379 - werkzeug - INFO - ************ - - [19/May/2025 01:13:08] "GET /api/settings HTTP/1.1" 200 -
2025-05-19 01:13:08,456 - werkzeug - INFO - ************ - - [19/May/2025 01:13:08] "GET /api/notifications/count HTTP/1.1" 200 -
2025-05-19 01:13:08,496 - werkzeug - INFO - ************ - - [19/May/2025 01:13:08] "GET /api/sales-chart-data?days=30 HTTP/1.1" 200 -
2025-05-19 01:13:08,506 - werkzeug - INFO - ************ - - [19/May/2025 01:13:08] "GET /api/category-distribution HTTP/1.1" 200 -
2025-05-19 01:13:08,507 - werkzeug - INFO - ************ - - [19/May/2025 01:13:08] "GET /api/settings HTTP/1.1" 200 -
2025-05-19 01:13:08,722 - werkzeug - INFO - ************ - - [19/May/2025 01:13:08] "GET /api/dashboard HTTP/1.1" 200 -
2025-05-19 01:13:08,776 - werkzeug - INFO - ************ - - [19/May/2025 01:13:08] "GET /api/notifications/count HTTP/1.1" 200 -
2025-05-19 01:13:08,839 - werkzeug - INFO - ************ - - [19/May/2025 01:13:08] "GET /api/notifications/recent HTTP/1.1" 200 -
2025-05-19 01:13:08,848 - nobara - ERROR - استثناء: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.
معلومات الطلب: {"url": "http://************:5000/favicon.ico", "method": "GET", "headers": {"Host": "************:5000", "Connection": "keep-alive", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Accept": "image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8", "Referer": "http://************:5000/home", "Accept-Encoding": "gzip, deflate", "Accept-Language": "en-US,en;q=0.9", "Cookie": "NEXT_LOCALE=en; session=.eJztksFqwzAMhl_F-JJLKLGd2FYeo-y2laJYMklIk-E4p9J3n5s9xgbi-yWE-IXQU97jgvvIu-w_n1LkIpJT2pKs5dfRWE0n48lBnMJvGnfmphfVPK0z6ks61jw9-PLBj-8FM185cuI1sNiGmUOuxIi7WDeBOadpODKLKk954UreXvW_-Z8zv9Xl-xLvo-xzOrhUE8leakLvgFqMkZ0Fp3yAADi4SATOOxNaX6I1unt3o9aG7NAEpQBQW_YxlrGoOgA1QOfIWFaAXoem8cEqRKcCkeqMDRCDRfCkg7NETBbZlCvcj53T7zZKvn4AFHUAfw.aCpbHw.4GZUqElJVuQrNozJL7R0ATCNtRQ"}, "args": {}, "form": {}, "cookies": {"NEXT_LOCALE": "en", "session": ".eJztksFqwzAMhl_F-JJLKLGd2FYeo-y2laJYMklIk-E4p9J3n5s9xgbi-yWE-IXQU97jgvvIu-w_n1LkIpJT2pKs5dfRWE0n48lBnMJvGnfmphfVPK0z6ks61jw9-PLBj-8FM185cuI1sNiGmUOuxIi7WDeBOadpODKLKk954UreXvW_-Z8zv9Xl-xLvo-xzOrhUE8leakLvgFqMkZ0Fp3yAADi4SATOOxNaX6I1unt3o9aG7NAEpQBQW_YxlrGoOgA1QOfIWFaAXoem8cEqRKcCkeqMDRCDRfCkg7NETBbZlCvcj53T7zZKvn4AFHUAfw.aCpbHw.4GZUqElJVuQrNozJL7R0ATCNtRQ"}, "remote_addr": "************", "user_agent": null}
تتبع الاستثناء:
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\app.py", line 917, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\app.py", line 891, in dispatch_request
    self.raise_routing_exception(req)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\app.py", line 500, in raise_routing_exception
    raise request.routing_exception  # type: ignore[misc]
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\ctx.py", line 362, in match_request
    result = self.url_adapter.match(return_rule=True)  # type: ignore
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\werkzeug\routing\map.py", line 629, in match
    raise NotFound() from None
werkzeug.exceptions.NotFound: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.

2025-05-19 01:13:08,892 - werkzeug - INFO - ************ - - [19/May/2025 01:13:08] "[35m[1mGET /favicon.ico HTTP/1.1[0m" 500 -
2025-05-19 01:13:08,957 - werkzeug - INFO - ************ - - [19/May/2025 01:13:08] "GET /api/notifications/count HTTP/1.1" 200 -
2025-05-19 01:13:08,994 - werkzeug - INFO - ************ - - [19/May/2025 01:13:08] "GET /api/dashboard HTTP/1.1" 200 -
2025-05-19 01:13:09,002 - werkzeug - INFO - ************ - - [19/May/2025 01:13:09] "GET /api/notifications/recent HTTP/1.1" 200 -
2025-05-19 01:13:14,392 - werkzeug - INFO - ************ - - [19/May/2025 01:13:14] "GET /purchases HTTP/1.1" 200 -
2025-05-19 01:13:14,510 - werkzeug - INFO - ************ - - [19/May/2025 01:13:14] "[36mGET /static/img/nobara-logo.svg HTTP/1.1[0m" 304 -
2025-05-19 01:13:14,950 - werkzeug - INFO - ************ - - [19/May/2025 01:13:14] "GET /api/notifications/count HTTP/1.1" 200 -
2025-05-19 01:13:14,984 - nobara - ERROR - استثناء: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.
معلومات الطلب: {"url": "http://************:5000/favicon.ico", "method": "GET", "headers": {"Host": "************:5000", "Connection": "keep-alive", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Accept": "image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8", "Referer": "http://************:5000/purchases", "Accept-Encoding": "gzip, deflate", "Accept-Language": "en-US,en;q=0.9", "Cookie": "NEXT_LOCALE=en; session=.eJztksFqwzAMhl_F-JJLKLGd2FYeo-y2laJYMklIk-E4p9J3n5s9xgbi-yWE-IXQU97jgvvIu-w_n1LkIpJT2pKs5dfRWE0n48lBnMJvGnfmphfVPK0z6ks61jw9-PLBj-8FM185cuI1sNiGmUOuxIi7WDeBOadpODKLKk954UreXvW_-Z8zv9Xl-xLvo-xzOrhUE8leakLvgFqMkZ0Fp3yAADi4SATOOxNaX6I1unt3o9aG7NAEpQBQW_YxlrGoOgA1QOfIWFaAXoem8cEqRKcCkeqMDRCDRfCkg7NETBbZlCvcj53T7zZKvn4AFHUAfw.aCpbHw.4GZUqElJVuQrNozJL7R0ATCNtRQ"}, "args": {}, "form": {}, "cookies": {"NEXT_LOCALE": "en", "session": ".eJztksFqwzAMhl_F-JJLKLGd2FYeo-y2laJYMklIk-E4p9J3n5s9xgbi-yWE-IXQU97jgvvIu-w_n1LkIpJT2pKs5dfRWE0n48lBnMJvGnfmphfVPK0z6ks61jw9-PLBj-8FM185cuI1sNiGmUOuxIi7WDeBOadpODKLKk954UreXvW_-Z8zv9Xl-xLvo-xzOrhUE8leakLvgFqMkZ0Fp3yAADi4SATOOxNaX6I1unt3o9aG7NAEpQBQW_YxlrGoOgA1QOfIWFaAXoem8cEqRKcCkeqMDRCDRfCkg7NETBbZlCvcj53T7zZKvn4AFHUAfw.aCpbHw.4GZUqElJVuQrNozJL7R0ATCNtRQ"}, "remote_addr": "************", "user_agent": null}
تتبع الاستثناء:
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\app.py", line 917, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\app.py", line 891, in dispatch_request
    self.raise_routing_exception(req)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\app.py", line 500, in raise_routing_exception
    raise request.routing_exception  # type: ignore[misc]
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\ctx.py", line 362, in match_request
    result = self.url_adapter.match(return_rule=True)  # type: ignore
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\werkzeug\routing\map.py", line 629, in match
    raise NotFound() from None
werkzeug.exceptions.NotFound: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.

2025-05-19 01:13:14,995 - werkzeug - INFO - ************ - - [19/May/2025 01:13:14] "[35m[1mGET /favicon.ico HTTP/1.1[0m" 500 -
2025-05-19 01:13:16,800 - werkzeug - INFO - ************ - - [19/May/2025 01:13:16] "GET /customers HTTP/1.1" 200 -
2025-05-19 01:13:16,916 - werkzeug - INFO - ************ - - [19/May/2025 01:13:16] "[36mGET /static/img/nobara-logo.svg HTTP/1.1[0m" 304 -
2025-05-19 01:13:17,297 - werkzeug - INFO - ************ - - [19/May/2025 01:13:17] "GET /api/notifications/count HTTP/1.1" 200 -
2025-05-19 01:13:17,299 - nobara - ERROR - استثناء: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.
معلومات الطلب: {"url": "http://************:5000/favicon.ico", "method": "GET", "headers": {"Host": "************:5000", "Connection": "keep-alive", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Accept": "image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8", "Referer": "http://************:5000/customers", "Accept-Encoding": "gzip, deflate", "Accept-Language": "en-US,en;q=0.9", "Cookie": "NEXT_LOCALE=en; session=.eJztksFqwzAMhl_F-JJLKLGd2FYeo-y2laJYMklIk-E4p9J3n5s9xgbi-yWE-IXQU97jgvvIu-w_n1LkIpJT2pKs5dfRWE0n48lBnMJvGnfmphfVPK0z6ks61jw9-PLBj-8FM185cuI1sNiGmUOuxIi7WDeBOadpODKLKk954UreXvW_-Z8zv9Xl-xLvo-xzOrhUE8leakLvgFqMkZ0Fp3yAADi4SATOOxNaX6I1unt3o9aG7NAEpQBQW_YxlrGoOgA1QOfIWFaAXoem8cEqRKcCkeqMDRCDRfCkg7NETBbZlCvcj53T7zZKvn4AFHUAfw.aCpbHw.4GZUqElJVuQrNozJL7R0ATCNtRQ"}, "args": {}, "form": {}, "cookies": {"NEXT_LOCALE": "en", "session": ".eJztksFqwzAMhl_F-JJLKLGd2FYeo-y2laJYMklIk-E4p9J3n5s9xgbi-yWE-IXQU97jgvvIu-w_n1LkIpJT2pKs5dfRWE0n48lBnMJvGnfmphfVPK0z6ks61jw9-PLBj-8FM185cuI1sNiGmUOuxIi7WDeBOadpODKLKk954UreXvW_-Z8zv9Xl-xLvo-xzOrhUE8leakLvgFqMkZ0Fp3yAADi4SATOOxNaX6I1unt3o9aG7NAEpQBQW_YxlrGoOgA1QOfIWFaAXoem8cEqRKcCkeqMDRCDRfCkg7NETBbZlCvcj53T7zZKvn4AFHUAfw.aCpbHw.4GZUqElJVuQrNozJL7R0ATCNtRQ"}, "remote_addr": "************", "user_agent": null}
تتبع الاستثناء:
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\app.py", line 917, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\app.py", line 891, in dispatch_request
    self.raise_routing_exception(req)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\app.py", line 500, in raise_routing_exception
    raise request.routing_exception  # type: ignore[misc]
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\ctx.py", line 362, in match_request
    result = self.url_adapter.match(return_rule=True)  # type: ignore
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\werkzeug\routing\map.py", line 629, in match
    raise NotFound() from None
werkzeug.exceptions.NotFound: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.

2025-05-19 01:13:17,317 - werkzeug - INFO - ************ - - [19/May/2025 01:13:17] "[35m[1mGET /favicon.ico HTTP/1.1[0m" 500 -
2025-05-19 01:13:18,180 - werkzeug - INFO - ************ - - [19/May/2025 01:13:18] "GET /suppliers HTTP/1.1" 200 -
2025-05-19 01:13:18,259 - werkzeug - INFO - ************ - - [19/May/2025 01:13:18] "[36mGET /static/img/nobara-logo.svg HTTP/1.1[0m" 304 -
2025-05-19 01:13:18,765 - werkzeug - INFO - ************ - - [19/May/2025 01:13:18] "GET /api/notifications/count HTTP/1.1" 200 -
2025-05-19 01:13:18,796 - nobara - ERROR - استثناء: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.
معلومات الطلب: {"url": "http://************:5000/favicon.ico", "method": "GET", "headers": {"Host": "************:5000", "Connection": "keep-alive", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Accept": "image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8", "Referer": "http://************:5000/suppliers", "Accept-Encoding": "gzip, deflate", "Accept-Language": "en-US,en;q=0.9", "Cookie": "NEXT_LOCALE=en; session=.eJztksFqwzAMhl_F-JJLKLGd2FYeo-y2laJYMklIk-E4p9J3n5s9xgbi-yWE-IXQU97jgvvIu-w_n1LkIpJT2pKs5dfRWE0n48lBnMJvGnfmphfVPK0z6ks61jw9-PLBj-8FM185cuI1sNiGmUOuxIi7WDeBOadpODKLKk954UreXvW_-Z8zv9Xl-xLvo-xzOrhUE8leakLvgFqMkZ0Fp3yAADi4SATOOxNaX6I1unt3o9aG7NAEpQBQW_YxlrGoOgA1QOfIWFaAXoem8cEqRKcCkeqMDRCDRfCkg7NETBbZlCvcj53T7zZKvn4AFHUAfw.aCpbHw.4GZUqElJVuQrNozJL7R0ATCNtRQ"}, "args": {}, "form": {}, "cookies": {"NEXT_LOCALE": "en", "session": ".eJztksFqwzAMhl_F-JJLKLGd2FYeo-y2laJYMklIk-E4p9J3n5s9xgbi-yWE-IXQU97jgvvIu-w_n1LkIpJT2pKs5dfRWE0n48lBnMJvGnfmphfVPK0z6ks61jw9-PLBj-8FM185cuI1sNiGmUOuxIi7WDeBOadpODKLKk954UreXvW_-Z8zv9Xl-xLvo-xzOrhUE8leakLvgFqMkZ0Fp3yAADi4SATOOxNaX6I1unt3o9aG7NAEpQBQW_YxlrGoOgA1QOfIWFaAXoem8cEqRKcCkeqMDRCDRfCkg7NETBbZlCvcj53T7zZKvn4AFHUAfw.aCpbHw.4GZUqElJVuQrNozJL7R0ATCNtRQ"}, "remote_addr": "************", "user_agent": null}
تتبع الاستثناء:
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\app.py", line 917, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\app.py", line 891, in dispatch_request
    self.raise_routing_exception(req)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\app.py", line 500, in raise_routing_exception
    raise request.routing_exception  # type: ignore[misc]
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\ctx.py", line 362, in match_request
    result = self.url_adapter.match(return_rule=True)  # type: ignore
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\werkzeug\routing\map.py", line 629, in match
    raise NotFound() from None
werkzeug.exceptions.NotFound: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.

2025-05-19 01:13:18,802 - werkzeug - INFO - ************ - - [19/May/2025 01:13:18] "[35m[1mGET /favicon.ico HTTP/1.1[0m" 500 -
2025-05-19 01:13:19,934 - werkzeug - INFO - ************ - - [19/May/2025 01:13:19] "GET /reports HTTP/1.1" 200 -
2025-05-19 01:13:20,048 - werkzeug - INFO - ************ - - [19/May/2025 01:13:20] "[36mGET /static/uploads/logo_20250518120852.jpeg HTTP/1.1[0m" 304 -
2025-05-19 01:13:20,446 - werkzeug - INFO - ************ - - [19/May/2025 01:13:20] "GET /api/notifications/count HTTP/1.1" 200 -
2025-05-19 01:13:20,483 - nobara - ERROR - استثناء: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.
معلومات الطلب: {"url": "http://************:5000/favicon.ico", "method": "GET", "headers": {"Host": "************:5000", "Connection": "keep-alive", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Accept": "image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8", "Referer": "http://************:5000/reports", "Accept-Encoding": "gzip, deflate", "Accept-Language": "en-US,en;q=0.9", "Cookie": "NEXT_LOCALE=en; session=.eJztksFqwzAMhl_F-JJLKLGd2FYeo-y2laJYMklIk-E4p9J3n5s9xgbi-yWE-IXQU97jgvvIu-w_n1LkIpJT2pKs5dfRWE0n48lBnMJvGnfmphfVPK0z6ks61jw9-PLBj-8FM185cuI1sNiGmUOuxIi7WDeBOadpODKLKk954UreXvW_-Z8zv9Xl-xLvo-xzOrhUE8leakLvgFqMkZ0Fp3yAADi4SATOOxNaX6I1unt3o9aG7NAEpQBQW_YxlrGoOgA1QOfIWFaAXoem8cEqRKcCkeqMDRCDRfCkg7NETBbZlCvcj53T7zZKvn4AFHUAfw.aCpbHw.4GZUqElJVuQrNozJL7R0ATCNtRQ"}, "args": {}, "form": {}, "cookies": {"NEXT_LOCALE": "en", "session": ".eJztksFqwzAMhl_F-JJLKLGd2FYeo-y2laJYMklIk-E4p9J3n5s9xgbi-yWE-IXQU97jgvvIu-w_n1LkIpJT2pKs5dfRWE0n48lBnMJvGnfmphfVPK0z6ks61jw9-PLBj-8FM185cuI1sNiGmUOuxIi7WDeBOadpODKLKk954UreXvW_-Z8zv9Xl-xLvo-xzOrhUE8leakLvgFqMkZ0Fp3yAADi4SATOOxNaX6I1unt3o9aG7NAEpQBQW_YxlrGoOgA1QOfIWFaAXoem8cEqRKcCkeqMDRCDRfCkg7NETBbZlCvcj53T7zZKvn4AFHUAfw.aCpbHw.4GZUqElJVuQrNozJL7R0ATCNtRQ"}, "remote_addr": "************", "user_agent": null}
تتبع الاستثناء:
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\app.py", line 917, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\app.py", line 891, in dispatch_request
    self.raise_routing_exception(req)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\app.py", line 500, in raise_routing_exception
    raise request.routing_exception  # type: ignore[misc]
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\ctx.py", line 362, in match_request
    result = self.url_adapter.match(return_rule=True)  # type: ignore
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\werkzeug\routing\map.py", line 629, in match
    raise NotFound() from None
werkzeug.exceptions.NotFound: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.

2025-05-19 01:13:20,489 - werkzeug - INFO - ************ - - [19/May/2025 01:13:20] "[35m[1mGET /favicon.ico HTTP/1.1[0m" 500 -
2025-05-19 01:13:21,842 - nobara - ERROR - استثناء: warehouses/warehouses.html
معلومات الطلب: {"url": "http://************:5000/warehouses", "method": "GET", "headers": {"Host": "************:5000", "Connection": "keep-alive", "Upgrade-Insecure-Requests": "1", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7", "Referer": "http://************:5000/reports", "Accept-Encoding": "gzip, deflate", "Accept-Language": "en-US,en;q=0.9", "Cookie": "NEXT_LOCALE=en; session=.eJztksFqwzAMhl_F-JJLKLGd2FYeo-y2laJYMklIk-E4p9J3n5s9xgbi-yWE-IXQU97jgvvIu-w_n1LkIpJT2pKs5dfRWE0n48lBnMJvGnfmphfVPK0z6ks61jw9-PLBj-8FM185cuI1sNiGmUOuxIi7WDeBOadpODKLKk954UreXvW_-Z8zv9Xl-xLvo-xzOrhUE8leakLvgFqMkZ0Fp3yAADi4SATOOxNaX6I1unt3o9aG7NAEpQBQW_YxlrGoOgA1QOfIWFaAXoem8cEqRKcCkeqMDRCDRfCkg7NETBbZlCvcj53T7zZKvn4AFHUAfw.aCpbHw.4GZUqElJVuQrNozJL7R0ATCNtRQ"}, "args": {}, "form": {}, "cookies": {"NEXT_LOCALE": "en", "session": ".eJztksFqwzAMhl_F-JJLKLGd2FYeo-y2laJYMklIk-E4p9J3n5s9xgbi-yWE-IXQU97jgvvIu-w_n1LkIpJT2pKs5dfRWE0n48lBnMJvGnfmphfVPK0z6ks61jw9-PLBj-8FM185cuI1sNiGmUOuxIi7WDeBOadpODKLKk954UreXvW_-Z8zv9Xl-xLvo-xzOrhUE8leakLvgFqMkZ0Fp3yAADi4SATOOxNaX6I1unt3o9aG7NAEpQBQW_YxlrGoOgA1QOfIWFaAXoem8cEqRKcCkeqMDRCDRfCkg7NETBbZlCvcj53T7zZKvn4AFHUAfw.aCpbHw.4GZUqElJVuQrNozJL7R0ATCNtRQ"}, "remote_addr": "************", "user_agent": null}
تتبع الاستثناء:
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\app.py", line 917, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\app.py", line 902, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)  # type: ignore[no-any-return]
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask_login\utils.py", line 290, in decorated_view
    return current_app.ensure_sync(func)(*args, **kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\routes\warehouses.py", line 45, in index
    return render_template('warehouses/warehouses.html',
                          warehouses=warehouses,
    ...<2 lines>...
                          status=status,
                          stats=stats)
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\templating.py", line 149, in render_template
    template = app.jinja_env.get_or_select_template(template_name_or_list)
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\jinja2\environment.py", line 1087, in get_or_select_template
    return self.get_template(template_name_or_list, parent, globals)
           ~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\jinja2\environment.py", line 1016, in get_template
    return self._load_template(name, globals)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\jinja2\environment.py", line 975, in _load_template
    template = self.loader.load(self, name, self.make_globals(globals))
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\jinja2\loaders.py", line 126, in load
    source, filename, uptodate = self.get_source(environment, name)
                                 ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\templating.py", line 65, in get_source
    return self._get_source_fast(environment, template)
           ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\templating.py", line 99, in _get_source_fast
    raise TemplateNotFound(template)
jinja2.exceptions.TemplateNotFound: warehouses/warehouses.html

2025-05-19 01:13:21,850 - werkzeug - INFO - ************ - - [19/May/2025 01:13:21] "[35m[1mGET /warehouses HTTP/1.1[0m" 500 -
2025-05-19 01:13:21,948 - nobara - ERROR - استثناء: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.
معلومات الطلب: {"url": "http://************:5000/favicon.ico", "method": "GET", "headers": {"Host": "************:5000", "Connection": "keep-alive", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Accept": "image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8", "Referer": "http://************:5000/warehouses", "Accept-Encoding": "gzip, deflate", "Accept-Language": "en-US,en;q=0.9", "Cookie": "NEXT_LOCALE=en; session=.eJztksFqwzAMhl_F-JJLKLGd2FYeo-y2laJYMklIk-E4p9J3n5s9xgbi-yWE-IXQU97jgvvIu-w_n1LkIpJT2pKs5dfRWE0n48lBnMJvGnfmphfVPK0z6ks61jw9-PLBj-8FM185cuI1sNiGmUOuxIi7WDeBOadpODKLKk954UreXvW_-Z8zv9Xl-xLvo-xzOrhUE8leakLvgFqMkZ0Fp3yAADi4SATOOxNaX6I1unt3o9aG7NAEpQBQW_YxlrGoOgA1QOfIWFaAXoem8cEqRKcCkeqMDRCDRfCkg7NETBbZlCvcj53T7zZKvn4AFHUAfw.aCpbHw.4GZUqElJVuQrNozJL7R0ATCNtRQ"}, "args": {}, "form": {}, "cookies": {"NEXT_LOCALE": "en", "session": ".eJztksFqwzAMhl_F-JJLKLGd2FYeo-y2laJYMklIk-E4p9J3n5s9xgbi-yWE-IXQU97jgvvIu-w_n1LkIpJT2pKs5dfRWE0n48lBnMJvGnfmphfVPK0z6ks61jw9-PLBj-8FM185cuI1sNiGmUOuxIi7WDeBOadpODKLKk954UreXvW_-Z8zv9Xl-xLvo-xzOrhUE8leakLvgFqMkZ0Fp3yAADi4SATOOxNaX6I1unt3o9aG7NAEpQBQW_YxlrGoOgA1QOfIWFaAXoem8cEqRKcCkeqMDRCDRfCkg7NETBbZlCvcj53T7zZKvn4AFHUAfw.aCpbHw.4GZUqElJVuQrNozJL7R0ATCNtRQ"}, "remote_addr": "************", "user_agent": null}
تتبع الاستثناء:
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\app.py", line 917, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\app.py", line 891, in dispatch_request
    self.raise_routing_exception(req)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\app.py", line 500, in raise_routing_exception
    raise request.routing_exception  # type: ignore[misc]
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\ctx.py", line 362, in match_request
    result = self.url_adapter.match(return_rule=True)  # type: ignore
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\werkzeug\routing\map.py", line 629, in match
    raise NotFound() from None
werkzeug.exceptions.NotFound: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.

2025-05-19 01:13:21,953 - werkzeug - INFO - ************ - - [19/May/2025 01:13:21] "[35m[1mGET /favicon.ico HTTP/1.1[0m" 500 -
2025-05-19 01:13:24,990 - nobara - ERROR - استثناء: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.
معلومات الطلب: {"url": "http://************:5000/favicon.ico", "method": "GET", "headers": {"Host": "************:5000", "Connection": "keep-alive", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Accept": "image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8", "Referer": "http://************:5000/reports", "Accept-Encoding": "gzip, deflate", "Accept-Language": "en-US,en;q=0.9", "Cookie": "NEXT_LOCALE=en; session=.eJztksFqwzAMhl_F-JJLKLGd2FYeo-y2laJYMklIk-E4p9J3n5s9xgbi-yWE-IXQU97jgvvIu-w_n1LkIpJT2pKs5dfRWE0n48lBnMJvGnfmphfVPK0z6ks61jw9-PLBj-8FM185cuI1sNiGmUOuxIi7WDeBOadpODKLKk954UreXvW_-Z8zv9Xl-xLvo-xzOrhUE8leakLvgFqMkZ0Fp3yAADi4SATOOxNaX6I1unt3o9aG7NAEpQBQW_YxlrGoOgA1QOfIWFaAXoem8cEqRKcCkeqMDRCDRfCkg7NETBbZlCvcj53T7zZKvn4AFHUAfw.aCpbHw.4GZUqElJVuQrNozJL7R0ATCNtRQ"}, "args": {}, "form": {}, "cookies": {"NEXT_LOCALE": "en", "session": ".eJztksFqwzAMhl_F-JJLKLGd2FYeo-y2laJYMklIk-E4p9J3n5s9xgbi-yWE-IXQU97jgvvIu-w_n1LkIpJT2pKs5dfRWE0n48lBnMJvGnfmphfVPK0z6ks61jw9-PLBj-8FM185cuI1sNiGmUOuxIi7WDeBOadpODKLKk954UreXvW_-Z8zv9Xl-xLvo-xzOrhUE8leakLvgFqMkZ0Fp3yAADi4SATOOxNaX6I1unt3o9aG7NAEpQBQW_YxlrGoOgA1QOfIWFaAXoem8cEqRKcCkeqMDRCDRfCkg7NETBbZlCvcj53T7zZKvn4AFHUAfw.aCpbHw.4GZUqElJVuQrNozJL7R0ATCNtRQ"}, "remote_addr": "************", "user_agent": null}
تتبع الاستثناء:
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\app.py", line 917, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\app.py", line 891, in dispatch_request
    self.raise_routing_exception(req)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\app.py", line 500, in raise_routing_exception
    raise request.routing_exception  # type: ignore[misc]
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\ctx.py", line 362, in match_request
    result = self.url_adapter.match(return_rule=True)  # type: ignore
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\werkzeug\routing\map.py", line 629, in match
    raise NotFound() from None
werkzeug.exceptions.NotFound: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.

2025-05-19 01:13:25,000 - werkzeug - INFO - ************ - - [19/May/2025 01:13:25] "[35m[1mGET /favicon.ico HTTP/1.1[0m" 500 -
2025-05-19 01:13:25,976 - nobara - ERROR - استثناء: warehouses/warehouses.html
معلومات الطلب: {"url": "http://************:5000/warehouses", "method": "GET", "headers": {"Host": "************:5000", "Connection": "keep-alive", "Upgrade-Insecure-Requests": "1", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7", "Referer": "http://************:5000/reports", "Accept-Encoding": "gzip, deflate", "Accept-Language": "en-US,en;q=0.9", "Cookie": "NEXT_LOCALE=en; session=.eJztksFqwzAMhl_F-JJLKLGd2FYeo-y2laJYMklIk-E4p9J3n5s9xgbi-yWE-IXQU97jgvvIu-w_n1LkIpJT2pKs5dfRWE0n48lBnMJvGnfmphfVPK0z6ks61jw9-PLBj-8FM185cuI1sNiGmUOuxIi7WDeBOadpODKLKk954UreXvW_-Z8zv9Xl-xLvo-xzOrhUE8leakLvgFqMkZ0Fp3yAADi4SATOOxNaX6I1unt3o9aG7NAEpQBQW_YxlrGoOgA1QOfIWFaAXoem8cEqRKcCkeqMDRCDRfCkg7NETBbZlCvcj53T7zZKvn4AFHUAfw.aCpbHw.4GZUqElJVuQrNozJL7R0ATCNtRQ"}, "args": {}, "form": {}, "cookies": {"NEXT_LOCALE": "en", "session": ".eJztksFqwzAMhl_F-JJLKLGd2FYeo-y2laJYMklIk-E4p9J3n5s9xgbi-yWE-IXQU97jgvvIu-w_n1LkIpJT2pKs5dfRWE0n48lBnMJvGnfmphfVPK0z6ks61jw9-PLBj-8FM185cuI1sNiGmUOuxIi7WDeBOadpODKLKk954UreXvW_-Z8zv9Xl-xLvo-xzOrhUE8leakLvgFqMkZ0Fp3yAADi4SATOOxNaX6I1unt3o9aG7NAEpQBQW_YxlrGoOgA1QOfIWFaAXoem8cEqRKcCkeqMDRCDRfCkg7NETBbZlCvcj53T7zZKvn4AFHUAfw.aCpbHw.4GZUqElJVuQrNozJL7R0ATCNtRQ"}, "remote_addr": "************", "user_agent": null}
تتبع الاستثناء:
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\app.py", line 917, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\app.py", line 902, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)  # type: ignore[no-any-return]
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask_login\utils.py", line 290, in decorated_view
    return current_app.ensure_sync(func)(*args, **kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\routes\warehouses.py", line 45, in index
    return render_template('warehouses/warehouses.html',
                          warehouses=warehouses,
    ...<2 lines>...
                          status=status,
                          stats=stats)
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\templating.py", line 149, in render_template
    template = app.jinja_env.get_or_select_template(template_name_or_list)
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\jinja2\environment.py", line 1087, in get_or_select_template
    return self.get_template(template_name_or_list, parent, globals)
           ~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\jinja2\environment.py", line 1016, in get_template
    return self._load_template(name, globals)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\jinja2\environment.py", line 975, in _load_template
    template = self.loader.load(self, name, self.make_globals(globals))
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\jinja2\loaders.py", line 126, in load
    source, filename, uptodate = self.get_source(environment, name)
                                 ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\templating.py", line 65, in get_source
    return self._get_source_fast(environment, template)
           ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\templating.py", line 99, in _get_source_fast
    raise TemplateNotFound(template)
jinja2.exceptions.TemplateNotFound: warehouses/warehouses.html

2025-05-19 01:13:25,985 - werkzeug - INFO - ************ - - [19/May/2025 01:13:25] "[35m[1mGET /warehouses HTTP/1.1[0m" 500 -
2025-05-19 01:13:26,125 - nobara - ERROR - استثناء: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.
معلومات الطلب: {"url": "http://************:5000/favicon.ico", "method": "GET", "headers": {"Host": "************:5000", "Connection": "keep-alive", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Accept": "image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8", "Referer": "http://************:5000/warehouses", "Accept-Encoding": "gzip, deflate", "Accept-Language": "en-US,en;q=0.9", "Cookie": "NEXT_LOCALE=en; session=.eJztksFqwzAMhl_F-JJLKLGd2FYeo-y2laJYMklIk-E4p9J3n5s9xgbi-yWE-IXQU97jgvvIu-w_n1LkIpJT2pKs5dfRWE0n48lBnMJvGnfmphfVPK0z6ks61jw9-PLBj-8FM185cuI1sNiGmUOuxIi7WDeBOadpODKLKk954UreXvW_-Z8zv9Xl-xLvo-xzOrhUE8leakLvgFqMkZ0Fp3yAADi4SATOOxNaX6I1unt3o9aG7NAEpQBQW_YxlrGoOgA1QOfIWFaAXoem8cEqRKcCkeqMDRCDRfCkg7NETBbZlCvcj53T7zZKvn4AFHUAfw.aCpbHw.4GZUqElJVuQrNozJL7R0ATCNtRQ"}, "args": {}, "form": {}, "cookies": {"NEXT_LOCALE": "en", "session": ".eJztksFqwzAMhl_F-JJLKLGd2FYeo-y2laJYMklIk-E4p9J3n5s9xgbi-yWE-IXQU97jgvvIu-w_n1LkIpJT2pKs5dfRWE0n48lBnMJvGnfmphfVPK0z6ks61jw9-PLBj-8FM185cuI1sNiGmUOuxIi7WDeBOadpODKLKk954UreXvW_-Z8zv9Xl-xLvo-xzOrhUE8leakLvgFqMkZ0Fp3yAADi4SATOOxNaX6I1unt3o9aG7NAEpQBQW_YxlrGoOgA1QOfIWFaAXoem8cEqRKcCkeqMDRCDRfCkg7NETBbZlCvcj53T7zZKvn4AFHUAfw.aCpbHw.4GZUqElJVuQrNozJL7R0ATCNtRQ"}, "remote_addr": "************", "user_agent": null}
تتبع الاستثناء:
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\app.py", line 917, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\app.py", line 891, in dispatch_request
    self.raise_routing_exception(req)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\app.py", line 500, in raise_routing_exception
    raise request.routing_exception  # type: ignore[misc]
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\ctx.py", line 362, in match_request
    result = self.url_adapter.match(return_rule=True)  # type: ignore
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\werkzeug\routing\map.py", line 629, in match
    raise NotFound() from None
werkzeug.exceptions.NotFound: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.

2025-05-19 01:13:26,133 - werkzeug - INFO - ************ - - [19/May/2025 01:13:26] "[35m[1mGET /favicon.ico HTTP/1.1[0m" 500 -
2025-05-19 01:13:27,552 - nobara - ERROR - استثناء: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.
معلومات الطلب: {"url": "http://************:5000/favicon.ico", "method": "GET", "headers": {"Host": "************:5000", "Connection": "keep-alive", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Accept": "image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8", "Referer": "http://************:5000/reports", "Accept-Encoding": "gzip, deflate", "Accept-Language": "en-US,en;q=0.9", "Cookie": "NEXT_LOCALE=en; session=.eJztksFqwzAMhl_F-JJLKLGd2FYeo-y2laJYMklIk-E4p9J3n5s9xgbi-yWE-IXQU97jgvvIu-w_n1LkIpJT2pKs5dfRWE0n48lBnMJvGnfmphfVPK0z6ks61jw9-PLBj-8FM185cuI1sNiGmUOuxIi7WDeBOadpODKLKk954UreXvW_-Z8zv9Xl-xLvo-xzOrhUE8leakLvgFqMkZ0Fp3yAADi4SATOOxNaX6I1unt3o9aG7NAEpQBQW_YxlrGoOgA1QOfIWFaAXoem8cEqRKcCkeqMDRCDRfCkg7NETBbZlCvcj53T7zZKvn4AFHUAfw.aCpbHw.4GZUqElJVuQrNozJL7R0ATCNtRQ"}, "args": {}, "form": {}, "cookies": {"NEXT_LOCALE": "en", "session": ".eJztksFqwzAMhl_F-JJLKLGd2FYeo-y2laJYMklIk-E4p9J3n5s9xgbi-yWE-IXQU97jgvvIu-w_n1LkIpJT2pKs5dfRWE0n48lBnMJvGnfmphfVPK0z6ks61jw9-PLBj-8FM185cuI1sNiGmUOuxIi7WDeBOadpODKLKk954UreXvW_-Z8zv9Xl-xLvo-xzOrhUE8leakLvgFqMkZ0Fp3yAADi4SATOOxNaX6I1unt3o9aG7NAEpQBQW_YxlrGoOgA1QOfIWFaAXoem8cEqRKcCkeqMDRCDRfCkg7NETBbZlCvcj53T7zZKvn4AFHUAfw.aCpbHw.4GZUqElJVuQrNozJL7R0ATCNtRQ"}, "remote_addr": "************", "user_agent": null}
تتبع الاستثناء:
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\app.py", line 917, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\app.py", line 891, in dispatch_request
    self.raise_routing_exception(req)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\app.py", line 500, in raise_routing_exception
    raise request.routing_exception  # type: ignore[misc]
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\ctx.py", line 362, in match_request
    result = self.url_adapter.match(return_rule=True)  # type: ignore
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\werkzeug\routing\map.py", line 629, in match
    raise NotFound() from None
werkzeug.exceptions.NotFound: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.

2025-05-19 01:13:27,577 - werkzeug - INFO - ************ - - [19/May/2025 01:13:27] "[35m[1mGET /favicon.ico HTTP/1.1[0m" 500 -
2025-05-19 01:13:29,964 - werkzeug - INFO - ************ - - [19/May/2025 01:13:29] "GET /home HTTP/1.1" 200 -
2025-05-19 01:13:30,124 - werkzeug - INFO - ************ - - [19/May/2025 01:13:30] "[36mGET /static/css/nobara-pro.css?v=1.0.0 HTTP/1.1[0m" 304 -
2025-05-19 01:13:30,133 - werkzeug - INFO - ************ - - [19/May/2025 01:13:30] "[36mGET /static/css/dashboard-pro.css?v=1.0.1 HTTP/1.1[0m" 304 -
2025-05-19 01:13:30,155 - werkzeug - INFO - ************ - - [19/May/2025 01:13:30] "[36mGET /static/js/notifications-system.js?v=1.0.0 HTTP/1.1[0m" 304 -
2025-05-19 01:13:30,160 - werkzeug - INFO - ************ - - [19/May/2025 01:13:30] "[36mGET /static/js/system-manager.js?v=1.0.0 HTTP/1.1[0m" 304 -
2025-05-19 01:13:30,167 - werkzeug - INFO - ************ - - [19/May/2025 01:13:30] "[36mGET /static/js/dashboard-manager.js?v=1.0.0 HTTP/1.1[0m" 304 -
2025-05-19 01:13:30,178 - werkzeug - INFO - ************ - - [19/May/2025 01:13:30] "[36mGET /static/uploads/logo_20250518120852.jpeg HTTP/1.1[0m" 304 -
2025-05-19 01:13:30,216 - werkzeug - INFO - ************ - - [19/May/2025 01:13:30] "[36mGET /static/js/dashboard-pro.js?v=1.0.0 HTTP/1.1[0m" 304 -
2025-05-19 01:13:30,787 - werkzeug - INFO - ************ - - [19/May/2025 01:13:30] "GET /api/settings HTTP/1.1" 200 -
2025-05-19 01:13:30,852 - werkzeug - INFO - ************ - - [19/May/2025 01:13:30] "GET /api/category-distribution HTTP/1.1" 200 -
2025-05-19 01:13:30,958 - werkzeug - INFO - ************ - - [19/May/2025 01:13:30] "GET /api/sales-chart-data?days=30 HTTP/1.1" 200 -
2025-05-19 01:13:30,960 - werkzeug - INFO - ************ - - [19/May/2025 01:13:30] "GET /api/notifications/count HTTP/1.1" 200 -
2025-05-19 01:13:31,014 - werkzeug - INFO - ************ - - [19/May/2025 01:13:31] "GET /api/dashboard HTTP/1.1" 200 -
2025-05-19 01:13:31,194 - werkzeug - INFO - ************ - - [19/May/2025 01:13:31] "GET /api/settings HTTP/1.1" 200 -
2025-05-19 01:13:31,535 - werkzeug - INFO - ************ - - [19/May/2025 01:13:31] "GET /api/dashboard HTTP/1.1" 200 -
2025-05-19 01:13:31,821 - werkzeug - INFO - ************ - - [19/May/2025 01:13:31] "GET /api/notifications/count HTTP/1.1" 200 -
2025-05-19 01:13:31,943 - werkzeug - INFO - ************ - - [19/May/2025 01:13:31] "GET /api/notifications/recent HTTP/1.1" 200 -
2025-05-19 01:13:32,120 - werkzeug - INFO - ************ - - [19/May/2025 01:13:32] "GET /api/notifications/count HTTP/1.1" 200 -
2025-05-19 01:13:32,127 - werkzeug - INFO - ************ - - [19/May/2025 01:13:32] "GET /api/notifications/recent HTTP/1.1" 200 -
2025-05-19 01:13:32,374 - nobara - ERROR - استثناء: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.
معلومات الطلب: {"url": "http://************:5000/favicon.ico", "method": "GET", "headers": {"Host": "************:5000", "Connection": "keep-alive", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Accept": "image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8", "Referer": "http://************:5000/home", "Accept-Encoding": "gzip, deflate", "Accept-Language": "en-US,en;q=0.9", "Cookie": "NEXT_LOCALE=en; session=.eJztksFqwzAMhl_F-JJLKLGd2FYeo-y2laJYMklIk-E4p9J3n5s9xgbi-yWE-IXQU97jgvvIu-w_n1LkIpJT2pKs5dfRWE0n48lBnMJvGnfmphfVPK0z6ks61jw9-PLBj-8FM185cuI1sNiGmUOuxIi7WDeBOadpODKLKk954UreXvW_-Z8zv9Xl-xLvo-xzOrhUE8leakLvgFqMkZ0Fp3yAADi4SATOOxNaX6I1unt3o9aG7NAEpQBQW_YxlrGoOgA1QOfIWFaAXoem8cEqRKcCkeqMDRCDRfCkg7NETBbZlCvcj53T7zZKvn4AFHUAfw.aCpbHw.4GZUqElJVuQrNozJL7R0ATCNtRQ"}, "args": {}, "form": {}, "cookies": {"NEXT_LOCALE": "en", "session": ".eJztksFqwzAMhl_F-JJLKLGd2FYeo-y2laJYMklIk-E4p9J3n5s9xgbi-yWE-IXQU97jgvvIu-w_n1LkIpJT2pKs5dfRWE0n48lBnMJvGnfmphfVPK0z6ks61jw9-PLBj-8FM185cuI1sNiGmUOuxIi7WDeBOadpODKLKk954UreXvW_-Z8zv9Xl-xLvo-xzOrhUE8leakLvgFqMkZ0Fp3yAADi4SATOOxNaX6I1unt3o9aG7NAEpQBQW_YxlrGoOgA1QOfIWFaAXoem8cEqRKcCkeqMDRCDRfCkg7NETBbZlCvcj53T7zZKvn4AFHUAfw.aCpbHw.4GZUqElJVuQrNozJL7R0ATCNtRQ"}, "remote_addr": "************", "user_agent": null}
تتبع الاستثناء:
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\app.py", line 917, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\app.py", line 891, in dispatch_request
    self.raise_routing_exception(req)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\app.py", line 500, in raise_routing_exception
    raise request.routing_exception  # type: ignore[misc]
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\ctx.py", line 362, in match_request
    result = self.url_adapter.match(return_rule=True)  # type: ignore
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\werkzeug\routing\map.py", line 629, in match
    raise NotFound() from None
werkzeug.exceptions.NotFound: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.

2025-05-19 01:13:32,390 - werkzeug - INFO - ************ - - [19/May/2025 01:13:32] "[35m[1mGET /favicon.ico HTTP/1.1[0m" 500 -
2025-05-19 01:14:01,149 - werkzeug - INFO - ************ - - [19/May/2025 01:14:01] "GET /api/notifications/recent HTTP/1.1" 200 -
2025-05-19 01:14:01,169 - werkzeug - INFO - ************ - - [19/May/2025 01:14:01] "GET /api/notifications/recent HTTP/1.1" 200 -
2025-05-19 01:14:28,205 - werkzeug - INFO -  * Detected change in 'c:\\Users\\<USER>\\Desktop\\ملف البرنامج\\PythonCashierSystem.v2 - Copy - Copy\\PythonCashierSystem\\routes\\products.py', reloading
2025-05-19 01:14:29,671 - werkzeug - INFO -  * Restarting with stat
2025-05-19 01:14:38,812 - app - INFO - استخدام قاعدة البيانات المحلية: sqlite:///c:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\instance\fouad_pos.db
2025-05-19 01:14:39,123 - nobara - INFO - تم بدء تشغيل التطبيق
2025-05-19 01:14:39,604 - app - WARNING - مكتبة schedule غير متوفرة. لن يتم تفعيل النسخ الاحتياطي التلقائي.
2025-05-19 01:14:39,623 - app - WARNING - تم تخطي تهيئة النسخ الاحتياطي التلقائي لأن مكتبة schedule غير متوفرة
2025-05-19 01:14:39,628 - app - INFO - عمود warehouse_id موجود بالفعل في جدول order
2025-05-19 01:14:40,991 - security - WARNING - No JWT secret provided. Generated a new one. Please set JWT_SECRET in your environment.
2025-05-19 01:14:41,283 - __main__ - INFO - جاري بدء التطبيق على العنوان: http://************:5000
2025-05-19 01:14:42,346 - werkzeug - WARNING -  * Debugger is active!
2025-05-19 01:14:42,399 - werkzeug - INFO -  * Debugger PIN: 692-333-133
2025-05-19 01:14:43,744 - werkzeug - INFO - ************ - - [19/May/2025 01:14:43] "[32mGET / HTTP/1.1[0m" 302 -
2025-05-19 01:14:43,748 - werkzeug - INFO - ************ - - [19/May/2025 01:14:43] "GET /api/notifications/count HTTP/1.1" 200 -
2025-05-19 01:14:43,759 - werkzeug - INFO - ************ - - [19/May/2025 01:14:43] "GET /api/notifications/recent HTTP/1.1" 200 -
2025-05-19 01:14:43,828 - werkzeug - INFO - ************ - - [19/May/2025 01:14:43] "GET /api/notifications/count HTTP/1.1" 200 -
2025-05-19 01:14:43,831 - werkzeug - INFO - ************ - - [19/May/2025 01:14:43] "GET /api/notifications/recent HTTP/1.1" 200 -
2025-05-19 01:14:44,748 - werkzeug - INFO - ************ - - [19/May/2025 01:14:44] "GET /home HTTP/1.1" 200 -
2025-05-19 01:14:45,446 - werkzeug - INFO - ************ - - [19/May/2025 01:14:45] "[36mGET /static/css/nobara-pro.css?v=1.0.0 HTTP/1.1[0m" 304 -
2025-05-19 01:14:45,465 - werkzeug - INFO - ************ - - [19/May/2025 01:14:45] "[36mGET /static/uploads/logo_20250518120852.jpeg HTTP/1.1[0m" 304 -
2025-05-19 01:14:45,518 - werkzeug - INFO - ************ - - [19/May/2025 01:14:45] "[36mGET /static/js/dashboard-manager.js?v=1.0.0 HTTP/1.1[0m" 304 -
2025-05-19 01:14:45,536 - werkzeug - INFO - ************ - - [19/May/2025 01:14:45] "[36mGET /static/js/notifications-system.js?v=1.0.0 HTTP/1.1[0m" 304 -
2025-05-19 01:14:45,605 - werkzeug - INFO - ************ - - [19/May/2025 01:14:45] "[36mGET /static/js/system-manager.js?v=1.0.0 HTTP/1.1[0m" 304 -
2025-05-19 01:14:45,611 - werkzeug - INFO - ************ - - [19/May/2025 01:14:45] "[36mGET /static/css/dashboard-pro.css?v=1.0.1 HTTP/1.1[0m" 304 -
2025-05-19 01:14:45,615 - werkzeug - INFO - ************ - - [19/May/2025 01:14:45] "[36mGET /static/js/dashboard-pro.js?v=1.0.0 HTTP/1.1[0m" 304 -
2025-05-19 01:14:47,170 - werkzeug - INFO - ************ - - [19/May/2025 01:14:47] "GET /api/settings HTTP/1.1" 200 -
2025-05-19 01:14:47,189 - werkzeug - INFO - ************ - - [19/May/2025 01:14:47] "GET /api/sales-chart-data?days=30 HTTP/1.1" 200 -
2025-05-19 01:14:47,380 - werkzeug - INFO - ************ - - [19/May/2025 01:14:47] "GET /api/notifications/count HTTP/1.1" 200 -
2025-05-19 01:14:47,397 - werkzeug - INFO - ************ - - [19/May/2025 01:14:47] "GET /api/settings HTTP/1.1" 200 -
2025-05-19 01:14:47,404 - werkzeug - INFO - ************ - - [19/May/2025 01:14:47] "GET /api/category-distribution HTTP/1.1" 200 -
2025-05-19 01:14:47,405 - werkzeug - INFO - ************ - - [19/May/2025 01:14:47] "GET /api/dashboard HTTP/1.1" 200 -
2025-05-19 01:14:47,528 - werkzeug - INFO - ************ - - [19/May/2025 01:14:47] "GET /api/dashboard HTTP/1.1" 200 -
2025-05-19 01:14:48,196 - werkzeug - INFO - ************ - - [19/May/2025 01:14:48] "GET /api/notifications/count HTTP/1.1" 200 -
2025-05-19 01:14:48,303 - werkzeug - INFO - ************ - - [19/May/2025 01:14:48] "GET /api/notifications/recent HTTP/1.1" 200 -
2025-05-19 01:14:48,435 - werkzeug - INFO - ************ - - [19/May/2025 01:14:48] "GET /api/notifications/count HTTP/1.1" 200 -
2025-05-19 01:14:48,462 - nobara - ERROR - استثناء: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.
معلومات الطلب: {"url": "http://************:5000/favicon.ico", "method": "GET", "headers": {"Host": "************:5000", "Connection": "keep-alive", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Accept": "image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8", "Referer": "http://************:5000/home", "Accept-Encoding": "gzip, deflate", "Accept-Language": "en-US,en;q=0.9", "Cookie": "NEXT_LOCALE=en; session=.eJztksFqwzAMhl_F-JJLKLGd2FYeo-y2laJYMklIk-E4p9J3n5s9xgbi-yWE-IXQU97jgvvIu-w_n1LkIpJT2pKs5dfRWE0n48lBnMJvGnfmphfVPK0z6ks61jw9-PLBj-8FM185cuI1sNiGmUOuxIi7WDeBOadpODKLKk954UreXvW_-Z8zv9Xl-xLvo-xzOrhUE8leakLvgFqMkZ0Fp3yAADi4SATOOxNaX6I1unt3o9aG7NAEpQBQW_YxlrGoOgA1QOfIWFaAXoem8cEqRKcCkeqMDRCDRfCkg7NETBbZlCvcj53T7zZKvn4AFHUAfw.aCpbHw.4GZUqElJVuQrNozJL7R0ATCNtRQ"}, "args": {}, "form": {}, "cookies": {"NEXT_LOCALE": "en", "session": ".eJztksFqwzAMhl_F-JJLKLGd2FYeo-y2laJYMklIk-E4p9J3n5s9xgbi-yWE-IXQU97jgvvIu-w_n1LkIpJT2pKs5dfRWE0n48lBnMJvGnfmphfVPK0z6ks61jw9-PLBj-8FM185cuI1sNiGmUOuxIi7WDeBOadpODKLKk954UreXvW_-Z8zv9Xl-xLvo-xzOrhUE8leakLvgFqMkZ0Fp3yAADi4SATOOxNaX6I1unt3o9aG7NAEpQBQW_YxlrGoOgA1QOfIWFaAXoem8cEqRKcCkeqMDRCDRfCkg7NETBbZlCvcj53T7zZKvn4AFHUAfw.aCpbHw.4GZUqElJVuQrNozJL7R0ATCNtRQ"}, "remote_addr": "************", "user_agent": null}
تتبع الاستثناء:
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\app.py", line 917, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\app.py", line 891, in dispatch_request
    self.raise_routing_exception(req)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\app.py", line 500, in raise_routing_exception
    raise request.routing_exception  # type: ignore[misc]
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\ctx.py", line 362, in match_request
    result = self.url_adapter.match(return_rule=True)  # type: ignore
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\werkzeug\routing\map.py", line 629, in match
    raise NotFound() from None
werkzeug.exceptions.NotFound: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.

2025-05-19 01:14:48,496 - werkzeug - INFO - ************ - - [19/May/2025 01:14:48] "GET /api/notifications/recent HTTP/1.1" 200 -
2025-05-19 01:14:48,521 - werkzeug - INFO - ************ - - [19/May/2025 01:14:48] "[35m[1mGET /favicon.ico HTTP/1.1[0m" 500 -
2025-05-19 01:14:58,207 - werkzeug - INFO - ************ - - [19/May/2025 01:14:58] "GET /products HTTP/1.1" 200 -
2025-05-19 01:14:58,315 - werkzeug - INFO - ************ - - [19/May/2025 01:14:58] "[36mGET /static/img/nobara-logo.svg HTTP/1.1[0m" 304 -
2025-05-19 01:14:59,371 - werkzeug - INFO - ************ - - [19/May/2025 01:14:59] "GET /api/notifications/count HTTP/1.1" 200 -
2025-05-19 01:14:59,630 - nobara - ERROR - استثناء: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.
معلومات الطلب: {"url": "http://************:5000/favicon.ico", "method": "GET", "headers": {"Host": "************:5000", "Connection": "keep-alive", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Accept": "image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8", "Referer": "http://************:5000/products", "Accept-Encoding": "gzip, deflate", "Accept-Language": "en-US,en;q=0.9", "Cookie": "NEXT_LOCALE=en; session=.eJztksFqwzAMhl_F-JJLKLGd2FYeo-y2laJYMklIk-E4p9J3n5s9xgbi-yWE-IXQU97jgvvIu-w_n1LkIpJT2pKs5dfRWE0n48lBnMJvGnfmphfVPK0z6ks61jw9-PLBj-8FM185cuI1sNiGmUOuxIi7WDeBOadpODKLKk954UreXvW_-Z8zv9Xl-xLvo-xzOrhUE8leakLvgFqMkZ0Fp3yAADi4SATOOxNaX6I1unt3o9aG7NAEpQBQW_YxlrGoOgA1QOfIWFaAXoem8cEqRKcCkeqMDRCDRfCkg7NETBbZlCvcj53T7zZKvn4AFHUAfw.aCpbHw.4GZUqElJVuQrNozJL7R0ATCNtRQ"}, "args": {}, "form": {}, "cookies": {"NEXT_LOCALE": "en", "session": ".eJztksFqwzAMhl_F-JJLKLGd2FYeo-y2laJYMklIk-E4p9J3n5s9xgbi-yWE-IXQU97jgvvIu-w_n1LkIpJT2pKs5dfRWE0n48lBnMJvGnfmphfVPK0z6ks61jw9-PLBj-8FM185cuI1sNiGmUOuxIi7WDeBOadpODKLKk954UreXvW_-Z8zv9Xl-xLvo-xzOrhUE8leakLvgFqMkZ0Fp3yAADi4SATOOxNaX6I1unt3o9aG7NAEpQBQW_YxlrGoOgA1QOfIWFaAXoem8cEqRKcCkeqMDRCDRfCkg7NETBbZlCvcj53T7zZKvn4AFHUAfw.aCpbHw.4GZUqElJVuQrNozJL7R0ATCNtRQ"}, "remote_addr": "************", "user_agent": null}
تتبع الاستثناء:
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\app.py", line 917, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\app.py", line 891, in dispatch_request
    self.raise_routing_exception(req)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\app.py", line 500, in raise_routing_exception
    raise request.routing_exception  # type: ignore[misc]
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\ctx.py", line 362, in match_request
    result = self.url_adapter.match(return_rule=True)  # type: ignore
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\werkzeug\routing\map.py", line 629, in match
    raise NotFound() from None
werkzeug.exceptions.NotFound: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.

2025-05-19 01:14:59,696 - werkzeug - INFO - ************ - - [19/May/2025 01:14:59] "[35m[1mGET /favicon.ico HTTP/1.1[0m" 500 -
2025-05-19 01:15:01,299 - werkzeug - INFO - ************ - - [19/May/2025 01:15:01] "GET /api/notifications/recent HTTP/1.1" 200 -
2025-05-19 01:15:02,133 - werkzeug - INFO - ************ - - [19/May/2025 01:15:02] "GET /api/notifications/recent HTTP/1.1" 200 -
2025-05-19 01:15:03,013 - werkzeug - INFO - ************ - - [19/May/2025 01:15:03] "[32mGET /products/import-export HTTP/1.1[0m" 302 -
2025-05-19 01:15:03,082 - werkzeug - INFO - ************ - - [19/May/2025 01:15:03] "GET /products HTTP/1.1" 200 -
2025-05-19 01:15:03,673 - werkzeug - INFO - ************ - - [19/May/2025 01:15:03] "[36mGET /static/img/nobara-logo.svg HTTP/1.1[0m" 304 -
2025-05-19 01:15:04,181 - werkzeug - INFO - ************ - - [19/May/2025 01:15:04] "GET /api/notifications/count HTTP/1.1" 200 -
2025-05-19 01:15:04,200 - nobara - ERROR - استثناء: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.
معلومات الطلب: {"url": "http://************:5000/favicon.ico", "method": "GET", "headers": {"Host": "************:5000", "Connection": "keep-alive", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Accept": "image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8", "Referer": "http://************:5000/products", "Accept-Encoding": "gzip, deflate", "Accept-Language": "en-US,en;q=0.9", "Cookie": "NEXT_LOCALE=en; session=.eJztksFqwzAMhl_F-JJLKLGd2FYeY-y2laJYMklIk-E4p9J3n5u9w04F8f8SQnxC6CFvccF95F32Xw8pcjHJKW1J1vL7aKymU-OpgziNX2rcmZteVPO0zqgv6VjzdOfLJ99_Fsz8wZETr4HFNswcciVG3MW6Ccw5TcORWVR5ygtX8vqs3_A3_D_h17q8fuJ9lH1OB5dqItlLTegdUIsxsrPglA8QAAcXicB5Z0LrS7RGd69u1NqQHZqgFABqyz7GMhZVB6AG6BwZywrQ69A0PliF6FQgUp2xAWKwCJ50cJaIySKbcoXbsXP620bJ5y9x1yli.aCpb5w.1Jv5pyD8PazDruwjt3qLzvBL5O8"}, "args": {}, "form": {}, "cookies": {"NEXT_LOCALE": "en", "session": ".eJztksFqwzAMhl_F-JJLKLGd2FYeY-y2laJYMklIk-E4p9J3n5u9w04F8f8SQnxC6CFvccF95F32Xw8pcjHJKW1J1vL7aKymU-OpgziNX2rcmZteVPO0zqgv6VjzdOfLJ99_Fsz8wZETr4HFNswcciVG3MW6Ccw5TcORWVR5ygtX8vqs3_A3_D_h17q8fuJ9lH1OB5dqItlLTegdUIsxsrPglA8QAAcXicB5Z0LrS7RGd69u1NqQHZqgFABqyz7GMhZVB6AG6BwZywrQ69A0PliF6FQgUp2xAWKwCJ50cJaIySKbcoXbsXP620bJ5y9x1yli.aCpb5w.1Jv5pyD8PazDruwjt3qLzvBL5O8"}, "remote_addr": "************", "user_agent": null}
تتبع الاستثناء:
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\app.py", line 917, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\app.py", line 891, in dispatch_request
    self.raise_routing_exception(req)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\app.py", line 500, in raise_routing_exception
    raise request.routing_exception  # type: ignore[misc]
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\ctx.py", line 362, in match_request
    result = self.url_adapter.match(return_rule=True)  # type: ignore
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\werkzeug\routing\map.py", line 629, in match
    raise NotFound() from None
werkzeug.exceptions.NotFound: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.

2025-05-19 01:15:04,233 - werkzeug - INFO - ************ - - [19/May/2025 01:15:04] "[35m[1mGET /favicon.ico HTTP/1.1[0m" 500 -
2025-05-19 01:15:17,679 - werkzeug - INFO -  * Detected change in 'c:\\Users\\<USER>\\Desktop\\ملف البرنامج\\PythonCashierSystem.v2 - Copy - Copy\\PythonCashierSystem\\routes\\products.py', reloading
2025-05-19 01:15:21,127 - werkzeug - INFO -  * Restarting with stat
2025-05-19 01:15:45,790 - app - INFO - استخدام قاعدة البيانات المحلية: sqlite:///c:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\instance\fouad_pos.db
2025-05-19 01:15:46,107 - nobara - INFO - تم بدء تشغيل التطبيق
2025-05-19 01:15:47,157 - app - WARNING - مكتبة schedule غير متوفرة. لن يتم تفعيل النسخ الاحتياطي التلقائي.
2025-05-19 01:15:47,270 - app - WARNING - تم تخطي تهيئة النسخ الاحتياطي التلقائي لأن مكتبة schedule غير متوفرة
2025-05-19 01:15:47,288 - app - INFO - عمود warehouse_id موجود بالفعل في جدول order
2025-05-19 01:16:03,210 - security - WARNING - No JWT secret provided. Generated a new one. Please set JWT_SECRET in your environment.
2025-05-19 01:16:04,610 - __main__ - INFO - جاري بدء التطبيق على العنوان: http://************:5000
2025-05-19 01:16:35,852 - werkzeug - WARNING -  * Debugger is active!
2025-05-19 01:16:35,862 - werkzeug - INFO -  * Debugger PIN: 692-333-133
2025-05-19 01:16:38,610 - werkzeug - INFO - ************ - - [19/May/2025 01:16:38] "[32mGET / HTTP/1.1[0m" 302 -
2025-05-19 01:16:38,830 - werkzeug - INFO - ************ - - [19/May/2025 01:16:38] "[32mGET / HTTP/1.1[0m" 302 -
2025-05-19 01:16:39,757 - werkzeug - INFO - ************ - - [19/May/2025 01:16:39] "GET /products HTTP/1.1" 200 -
2025-05-19 01:16:41,701 - werkzeug - INFO - ************ - - [19/May/2025 01:16:41] "GET /home HTTP/1.1" 200 -
2025-05-19 01:16:41,772 - werkzeug - INFO - ************ - - [19/May/2025 01:16:41] "GET /home HTTP/1.1" 200 -
2025-05-19 01:16:42,439 - werkzeug - INFO - ************ - - [19/May/2025 01:16:42] "[36mGET /static/uploads/logo_20250518120852.jpeg HTTP/1.1[0m" 304 -
2025-05-19 01:16:42,469 - werkzeug - INFO - ************ - - [19/May/2025 01:16:42] "[36mGET /static/css/nobara-pro.css?v=1.0.0 HTTP/1.1[0m" 304 -
2025-05-19 01:16:42,590 - werkzeug - INFO - ************ - - [19/May/2025 01:16:42] "[36mGET /static/css/nobara-pro.css?v=1.0.0 HTTP/1.1[0m" 304 -
2025-05-19 01:16:42,649 - werkzeug - INFO - ************ - - [19/May/2025 01:16:42] "[36mGET /static/uploads/logo_20250518120852.jpeg HTTP/1.1[0m" 304 -
2025-05-19 01:16:42,739 - werkzeug - INFO - ************ - - [19/May/2025 01:16:42] "[36mGET /static/js/system-manager.js?v=1.0.0 HTTP/1.1[0m" 304 -
2025-05-19 01:16:42,740 - werkzeug - INFO - ************ - - [19/May/2025 01:16:42] "[36mGET /static/js/dashboard-manager.js?v=1.0.0 HTTP/1.1[0m" 304 -
2025-05-19 01:16:42,741 - werkzeug - INFO - ************ - - [19/May/2025 01:16:42] "[36mGET /static/js/notifications-system.js?v=1.0.0 HTTP/1.1[0m" 304 -
2025-05-19 01:16:42,771 - werkzeug - INFO - ************ - - [19/May/2025 01:16:42] "[36mGET /static/js/dashboard-pro.js?v=1.0.0 HTTP/1.1[0m" 304 -
2025-05-19 01:16:42,775 - werkzeug - INFO - ************ - - [19/May/2025 01:16:42] "[36mGET /static/js/system-manager.js?v=1.0.0 HTTP/1.1[0m" 304 -
2025-05-19 01:16:42,786 - werkzeug - INFO - ************ - - [19/May/2025 01:16:42] "[36mGET /static/js/dashboard-manager.js?v=1.0.0 HTTP/1.1[0m" 304 -
2025-05-19 01:16:42,802 - werkzeug - INFO - ************ - - [19/May/2025 01:16:42] "[36mGET /static/js/notifications-system.js?v=1.0.0 HTTP/1.1[0m" 304 -
2025-05-19 01:16:42,883 - werkzeug - INFO - ************ - - [19/May/2025 01:16:42] "[36mGET /static/js/dashboard-pro.js?v=1.0.0 HTTP/1.1[0m" 304 -
2025-05-19 01:16:42,922 - werkzeug - INFO - ************ - - [19/May/2025 01:16:42] "[36mGET /static/css/dashboard-pro.css?v=1.0.1 HTTP/1.1[0m" 304 -
2025-05-19 01:16:42,958 - werkzeug - INFO - ************ - - [19/May/2025 01:16:42] "[36mGET /static/css/dashboard-pro.css?v=1.0.1 HTTP/1.1[0m" 304 -
2025-05-19 01:16:44,341 - werkzeug - INFO - ************ - - [19/May/2025 01:16:44] "GET /api/settings HTTP/1.1" 200 -
2025-05-19 01:16:44,419 - werkzeug - INFO - ************ - - [19/May/2025 01:16:44] "GET /api/notifications/count HTTP/1.1" 200 -
2025-05-19 01:16:44,438 - werkzeug - INFO - ************ - - [19/May/2025 01:16:44] "GET /api/category-distribution HTTP/1.1" 200 -
2025-05-19 01:16:44,485 - werkzeug - INFO - ************ - - [19/May/2025 01:16:44] "GET /api/sales-chart-data?days=30 HTTP/1.1" 200 -
2025-05-19 01:16:44,488 - werkzeug - INFO - ************ - - [19/May/2025 01:16:44] "GET /api/settings HTTP/1.1" 200 -
2025-05-19 01:16:44,525 - werkzeug - INFO - ************ - - [19/May/2025 01:16:44] "GET /api/dashboard HTTP/1.1" 200 -
2025-05-19 01:16:44,755 - werkzeug - INFO - ************ - - [19/May/2025 01:16:44] "GET /api/dashboard HTTP/1.1" 200 -
2025-05-19 01:16:44,908 - werkzeug - INFO - ************ - - [19/May/2025 01:16:44] "GET /api/settings HTTP/1.1" 200 -
2025-05-19 01:16:44,955 - werkzeug - INFO - ************ - - [19/May/2025 01:16:44] "GET /api/notifications/count HTTP/1.1" 200 -
2025-05-19 01:16:45,007 - werkzeug - INFO - ************ - - [19/May/2025 01:16:45] "GET /api/sales-chart-data?days=30 HTTP/1.1" 200 -
2025-05-19 01:16:45,014 - werkzeug - INFO - ************ - - [19/May/2025 01:16:45] "GET /api/category-distribution HTTP/1.1" 200 -
2025-05-19 01:16:45,071 - werkzeug - INFO - ************ - - [19/May/2025 01:16:45] "GET /api/dashboard HTTP/1.1" 200 -
2025-05-19 01:16:45,157 - werkzeug - INFO - ************ - - [19/May/2025 01:16:45] "GET /api/settings HTTP/1.1" 200 -
2025-05-19 01:16:45,268 - werkzeug - INFO - ************ - - [19/May/2025 01:16:45] "GET /api/dashboard HTTP/1.1" 200 -
2025-05-19 01:16:45,723 - werkzeug - INFO - ************ - - [19/May/2025 01:16:45] "GET /api/notifications/count HTTP/1.1" 200 -
2025-05-19 01:16:45,741 - werkzeug - INFO - ************ - - [19/May/2025 01:16:45] "GET /api/notifications/recent HTTP/1.1" 200 -
2025-05-19 01:16:45,751 - werkzeug - INFO - ************ - - [19/May/2025 01:16:45] "GET /api/notifications/count HTTP/1.1" 200 -
2025-05-19 01:16:45,842 - werkzeug - INFO - ************ - - [19/May/2025 01:16:45] "GET /api/notifications/recent HTTP/1.1" 200 -
2025-05-19 01:16:46,144 - nobara - ERROR - استثناء: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.
معلومات الطلب: {"url": "http://************:5000/favicon.ico", "method": "GET", "headers": {"Host": "************:5000", "Connection": "keep-alive", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Accept": "image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8", "Referer": "http://************:5000/home", "Accept-Encoding": "gzip, deflate", "Accept-Language": "en-US,en;q=0.9", "Cookie": "NEXT_LOCALE=en; session=.eJztksFqwzAMhl_F-JJLKLGd2FYeY-y2laJYMklIk-E4p9J3n5u9w04F8f8SQnxC6CFvccF95F32Xw8pcjHJKW1J1vL7aKymU-OpgziNX2rcmZteVPO0zqgv6VjzdOfLJ99_Fsz8wZETr4HFNswcciVG3MW6Ccw5TcORWVR5ygtX8vqs3_A3_D_h17q8fuJ9lH1OB5dqItlLTegdUIsxsrPglA8QAAcXicB5Z0LrS7RGd69u1NqQHZqgFABqyz7GMhZVB6AG6BwZywrQ69A0PliF6FQgUp2xAWKwCJ50cJaIySKbcoXbsXP620bJ5y9x1yli.aCpb5w.1Jv5pyD8PazDruwjt3qLzvBL5O8"}, "args": {}, "form": {}, "cookies": {"NEXT_LOCALE": "en", "session": ".eJztksFqwzAMhl_F-JJLKLGd2FYeY-y2laJYMklIk-E4p9J3n5u9w04F8f8SQnxC6CFvccF95F32Xw8pcjHJKW1J1vL7aKymU-OpgziNX2rcmZteVPO0zqgv6VjzdOfLJ99_Fsz8wZETr4HFNswcciVG3MW6Ccw5TcORWVR5ygtX8vqs3_A3_D_h17q8fuJ9lH1OB5dqItlLTegdUIsxsrPglA8QAAcXicB5Z0LrS7RGd69u1NqQHZqgFABqyz7GMhZVB6AG6BwZywrQ69A0PliF6FQgUp2xAWKwCJ50cJaIySKbcoXbsXP620bJ5y9x1yli.aCpb5w.1Jv5pyD8PazDruwjt3qLzvBL5O8"}, "remote_addr": "************", "user_agent": null}
تتبع الاستثناء:
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\app.py", line 917, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\app.py", line 891, in dispatch_request
    self.raise_routing_exception(req)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\app.py", line 500, in raise_routing_exception
    raise request.routing_exception  # type: ignore[misc]
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\ctx.py", line 362, in match_request
    result = self.url_adapter.match(return_rule=True)  # type: ignore
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\werkzeug\routing\map.py", line 629, in match
    raise NotFound() from None
werkzeug.exceptions.NotFound: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.

2025-05-19 01:16:46,189 - werkzeug - INFO - ************ - - [19/May/2025 01:16:46] "[35m[1mGET /favicon.ico HTTP/1.1[0m" 500 -
2025-05-19 01:16:46,842 - werkzeug - INFO - ************ - - [19/May/2025 01:16:46] "GET /api/notifications/count HTTP/1.1" 200 -
2025-05-19 01:16:46,918 - werkzeug - INFO - ************ - - [19/May/2025 01:16:46] "GET /api/notifications/recent HTTP/1.1" 200 -
2025-05-19 01:16:47,192 - nobara - ERROR - استثناء: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.
معلومات الطلب: {"url": "http://************:5000/favicon.ico", "method": "GET", "headers": {"Host": "************:5000", "Connection": "keep-alive", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Accept": "image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8", "Referer": "http://************:5000/home", "Accept-Encoding": "gzip, deflate", "Accept-Language": "en-US,en;q=0.9", "Cookie": "NEXT_LOCALE=en; session=.eJztksFqwzAMhl_F-JJLKLGd2FYeY-y2laJYMklIk-E4p9J3n5u9w04F8f8SQnxC6CFvccF95F32Xw8pcjHJKW1J1vL7aKymU-OpgziNX2rcmZteVPO0zqgv6VjzdOfLJ99_Fsz8wZETr4HFNswcciVG3MW6Ccw5TcORWVR5ygtX8vqs3_A3_D_h17q8fuJ9lH1OB5dqItlLTegdUIsxsrPglA8QAAcXicB5Z0LrS7RGd69u1NqQHZqgFABqyz7GMhZVB6AG6BwZywrQ69A0PliF6FQgUp2xAWKwCJ50cJaIySKbcoXbsXP620bJ5y9x1yli.aCpb5w.1Jv5pyD8PazDruwjt3qLzvBL5O8"}, "args": {}, "form": {}, "cookies": {"NEXT_LOCALE": "en", "session": ".eJztksFqwzAMhl_F-JJLKLGd2FYeY-y2laJYMklIk-E4p9J3n5u9w04F8f8SQnxC6CFvccF95F32Xw8pcjHJKW1J1vL7aKymU-OpgziNX2rcmZteVPO0zqgv6VjzdOfLJ99_Fsz8wZETr4HFNswcciVG3MW6Ccw5TcORWVR5ygtX8vqs3_A3_D_h17q8fuJ9lH1OB5dqItlLTegdUIsxsrPglA8QAAcXicB5Z0LrS7RGd69u1NqQHZqgFABqyz7GMhZVB6AG6BwZywrQ69A0PliF6FQgUp2xAWKwCJ50cJaIySKbcoXbsXP620bJ5y9x1yli.aCpb5w.1Jv5pyD8PazDruwjt3qLzvBL5O8"}, "remote_addr": "************", "user_agent": null}
تتبع الاستثناء:
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\app.py", line 917, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\app.py", line 891, in dispatch_request
    self.raise_routing_exception(req)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\app.py", line 500, in raise_routing_exception
    raise request.routing_exception  # type: ignore[misc]
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\ctx.py", line 362, in match_request
    result = self.url_adapter.match(return_rule=True)  # type: ignore
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\werkzeug\routing\map.py", line 629, in match
    raise NotFound() from None
werkzeug.exceptions.NotFound: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.

2025-05-19 01:16:47,326 - werkzeug - INFO - ************ - - [19/May/2025 01:16:47] "[35m[1mGET /favicon.ico HTTP/1.1[0m" 500 -
2025-05-19 01:16:47,435 - werkzeug - INFO - ************ - - [19/May/2025 01:16:47] "GET /api/notifications/count HTTP/1.1" 200 -
2025-05-19 01:16:47,530 - werkzeug - INFO - ************ - - [19/May/2025 01:16:47] "GET /api/notifications/recent HTTP/1.1" 200 -
2025-05-19 01:17:14,257 - werkzeug - INFO - ************ - - [19/May/2025 01:17:14] "GET /api/notifications/recent HTTP/1.1" 200 -
2025-05-19 01:17:14,326 - werkzeug - INFO - ************ - - [19/May/2025 01:17:14] "GET /api/notifications/recent HTTP/1.1" 200 -
2025-05-19 01:17:15,150 - werkzeug - INFO - ************ - - [19/May/2025 01:17:15] "GET /api/notifications/recent HTTP/1.1" 200 -
2025-05-19 01:17:16,159 - werkzeug - INFO - ************ - - [19/May/2025 01:17:16] "GET /api/notifications/recent HTTP/1.1" 200 -
2025-05-19 01:17:44,252 - werkzeug - INFO - ************ - - [19/May/2025 01:17:44] "GET /api/notifications/recent HTTP/1.1" 200 -
2025-05-19 01:17:44,321 - werkzeug - INFO - ************ - - [19/May/2025 01:17:44] "GET /api/notifications/recent HTTP/1.1" 200 -
2025-05-19 01:17:45,159 - werkzeug - INFO - ************ - - [19/May/2025 01:17:45] "GET /api/notifications/recent HTTP/1.1" 200 -
2025-05-19 01:17:46,175 - werkzeug - INFO - ************ - - [19/May/2025 01:17:46] "GET /api/notifications/count HTTP/1.1" 200 -
2025-05-19 01:17:46,176 - werkzeug - INFO - ************ - - [19/May/2025 01:17:46] "GET /api/notifications/recent HTTP/1.1" 200 -
2025-05-19 01:17:46,213 - werkzeug - INFO - ************ - - [19/May/2025 01:17:46] "GET /api/notifications/count HTTP/1.1" 200 -
2025-05-19 01:17:46,350 - werkzeug - INFO - ************ - - [19/May/2025 01:17:46] "GET /api/notifications/count HTTP/1.1" 200 -
2025-05-19 01:17:46,368 - werkzeug - INFO - ************ - - [19/May/2025 01:17:46] "GET /api/notifications/count HTTP/1.1" 200 -
2025-05-19 01:18:12,908 - werkzeug - INFO - ************ - - [19/May/2025 01:18:12] "GET /home HTTP/1.1" 200 -
2025-05-19 01:18:13,078 - werkzeug - INFO - ************ - - [19/May/2025 01:18:13] "[36mGET /static/css/nobara-pro.css?v=1.0.0 HTTP/1.1[0m" 304 -
2025-05-19 01:18:13,108 - werkzeug - INFO - ************ - - [19/May/2025 01:18:13] "[36mGET /static/css/dashboard-pro.css?v=1.0.1 HTTP/1.1[0m" 304 -
2025-05-19 01:18:13,124 - werkzeug - INFO - ************ - - [19/May/2025 01:18:13] "[36mGET /static/js/notifications-system.js?v=1.0.0 HTTP/1.1[0m" 304 -
2025-05-19 01:18:13,201 - werkzeug - INFO - ************ - - [19/May/2025 01:18:13] "[36mGET /static/js/system-manager.js?v=1.0.0 HTTP/1.1[0m" 304 -
2025-05-19 01:18:13,208 - werkzeug - INFO - ************ - - [19/May/2025 01:18:13] "[36mGET /static/uploads/logo_20250518120852.jpeg HTTP/1.1[0m" 304 -
2025-05-19 01:18:13,595 - werkzeug - INFO - ************ - - [19/May/2025 01:18:13] "[36mGET /static/js/dashboard-manager.js?v=1.0.0 HTTP/1.1[0m" 304 -
2025-05-19 01:18:14,126 - werkzeug - INFO - ************ - - [19/May/2025 01:18:14] "[36mGET /static/js/dashboard-pro.js?v=1.0.0 HTTP/1.1[0m" 304 -
2025-05-19 01:18:14,423 - werkzeug - INFO - ************ - - [19/May/2025 01:18:14] "GET /api/settings HTTP/1.1" 200 -
2025-05-19 01:18:14,426 - werkzeug - INFO - ************ - - [19/May/2025 01:18:14] "GET /api/notifications/count HTTP/1.1" 200 -
2025-05-19 01:18:14,566 - werkzeug - INFO - ************ - - [19/May/2025 01:18:14] "GET /api/sales-chart-data?days=30 HTTP/1.1" 200 -
2025-05-19 01:18:14,719 - werkzeug - INFO - ************ - - [19/May/2025 01:18:14] "GET /api/dashboard HTTP/1.1" 200 -
2025-05-19 01:18:14,871 - werkzeug - INFO - ************ - - [19/May/2025 01:18:14] "GET /api/category-distribution HTTP/1.1" 200 -
2025-05-19 01:18:15,272 - werkzeug - INFO - ************ - - [19/May/2025 01:18:15] "GET /api/settings HTTP/1.1" 200 -
2025-05-19 01:18:15,320 - werkzeug - INFO - ************ - - [19/May/2025 01:18:15] "GET /api/notifications/count HTTP/1.1" 200 -
2025-05-19 01:18:15,323 - werkzeug - INFO - ************ - - [19/May/2025 01:18:15] "GET /api/notifications/recent HTTP/1.1" 200 -
2025-05-19 01:18:15,377 - nobara - ERROR - استثناء: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.
معلومات الطلب: {"url": "http://************:5000/favicon.ico", "method": "GET", "headers": {"Host": "************:5000", "Connection": "keep-alive", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Accept": "image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8", "Referer": "http://************:5000/home", "Accept-Encoding": "gzip, deflate", "Accept-Language": "en-US,en;q=0.9", "Cookie": "NEXT_LOCALE=en; session=.eJztksFqwzAMhl_F-JJLKLGd2FYeY-y2laJYMklIk-E4p9J3n5u9w04F8f8SQnxC6CFvccF95F32Xw8pcjHJKW1J1vL7aKymU-OpgziNX2rcmZteVPO0zqgv6VjzdOfLJ99_Fsz8wZETr4HFNswcciVG3MW6Ccw5TcORWVR5ygtX8vqs3_A3_D_h17q8fuJ9lH1OB5dqItlLTegdUIsxsrPglA8QAAcXicB5Z0LrS7RGd69u1NqQHZqgFABqyz7GMhZVB6AG6BwZywrQ69A0PliF6FQgUp2xAWKwCJ50cJaIySKbcoXbsXP620bJ5y9x1yli.aCpb5w.1Jv5pyD8PazDruwjt3qLzvBL5O8"}, "args": {}, "form": {}, "cookies": {"NEXT_LOCALE": "en", "session": ".eJztksFqwzAMhl_F-JJLKLGd2FYeY-y2laJYMklIk-E4p9J3n5u9w04F8f8SQnxC6CFvccF95F32Xw8pcjHJKW1J1vL7aKymU-OpgziNX2rcmZteVPO0zqgv6VjzdOfLJ99_Fsz8wZETr4HFNswcciVG3MW6Ccw5TcORWVR5ygtX8vqs3_A3_D_h17q8fuJ9lH1OB5dqItlLTegdUIsxsrPglA8QAAcXicB5Z0LrS7RGd69u1NqQHZqgFABqyz7GMhZVB6AG6BwZywrQ69A0PliF6FQgUp2xAWKwCJ50cJaIySKbcoXbsXP620bJ5y9x1yli.aCpb5w.1Jv5pyD8PazDruwjt3qLzvBL5O8"}, "remote_addr": "************", "user_agent": null}
تتبع الاستثناء:
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\app.py", line 917, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\app.py", line 891, in dispatch_request
    self.raise_routing_exception(req)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\app.py", line 500, in raise_routing_exception
    raise request.routing_exception  # type: ignore[misc]
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\ctx.py", line 362, in match_request
    result = self.url_adapter.match(return_rule=True)  # type: ignore
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\werkzeug\routing\map.py", line 629, in match
    raise NotFound() from None
werkzeug.exceptions.NotFound: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.

2025-05-19 01:18:15,764 - werkzeug - INFO - ************ - - [19/May/2025 01:18:15] "[35m[1mGET /favicon.ico HTTP/1.1[0m" 500 -
2025-05-19 01:18:15,778 - werkzeug - INFO - ************ - - [19/May/2025 01:18:15] "GET /api/notifications/count HTTP/1.1" 200 -
2025-05-19 01:18:15,868 - werkzeug - INFO - ************ - - [19/May/2025 01:18:15] "GET /api/dashboard HTTP/1.1" 200 -
2025-05-19 01:18:16,155 - werkzeug - INFO - ************ - - [19/May/2025 01:18:16] "GET /api/notifications/recent HTTP/1.1" 200 -
2025-05-19 01:18:16,777 - werkzeug - INFO - ************ - - [19/May/2025 01:18:16] "GET /pos HTTP/1.1" 200 -
2025-05-19 01:18:17,175 - werkzeug - INFO - ************ - - [19/May/2025 01:18:17] "[36mGET /static/css/nobara-pro.css?v=1.0.0 HTTP/1.1[0m" 304 -
2025-05-19 01:18:17,180 - werkzeug - INFO - ************ - - [19/May/2025 01:18:17] "[36mGET /static/css/dashboard-pro.css?v=1.0.1 HTTP/1.1[0m" 304 -
2025-05-19 01:18:17,207 - werkzeug - INFO - ************ - - [19/May/2025 01:18:17] "[36mGET /static/js/notifications-system.js?v=1.0.0 HTTP/1.1[0m" 304 -
2025-05-19 01:18:17,231 - werkzeug - INFO - ************ - - [19/May/2025 01:18:17] "[36mGET /static/js/system-manager.js?v=1.0.0 HTTP/1.1[0m" 304 -
2025-05-19 01:18:17,513 - werkzeug - INFO - ************ - - [19/May/2025 01:18:17] "[36mGET /static/css/pos-theme.css?v=1.0.0 HTTP/1.1[0m" 304 -
2025-05-19 01:18:17,515 - werkzeug - INFO - ************ - - [19/May/2025 01:18:17] "[36mGET /static/img/nobara-logo.svg HTTP/1.1[0m" 304 -
2025-05-19 01:18:17,659 - werkzeug - INFO - ************ - - [19/May/2025 01:18:17] "GET /static/js/pos.js HTTP/1.1" 200 -
2025-05-19 01:18:17,955 - werkzeug - INFO - ************ - - [19/May/2025 01:18:17] "[36mGET /static/js/pos_part2.js HTTP/1.1[0m" 304 -
2025-05-19 01:18:18,362 - werkzeug - INFO - ************ - - [19/May/2025 01:18:18] "[36mGET /static/js/pos_part3.js HTTP/1.1[0m" 304 -
2025-05-19 01:18:18,459 - werkzeug - INFO - ************ - - [19/May/2025 01:18:18] "[36mGET /static/js/pos_credit.js HTTP/1.1[0m" 304 -
2025-05-19 01:18:18,612 - werkzeug - INFO - ************ - - [19/May/2025 01:18:18] "[36mGET /static/js/customer_search.js HTTP/1.1[0m" 304 -
2025-05-19 01:18:18,650 - werkzeug - INFO - ************ - - [19/May/2025 01:18:18] "[36mGET /static/js/invoice-search.js HTTP/1.1[0m" 304 -
2025-05-19 01:18:18,662 - werkzeug - INFO - ************ - - [19/May/2025 01:18:18] "[36mGET /static/js/pos_part4.js HTTP/1.1[0m" 304 -
2025-05-19 01:18:18,666 - werkzeug - INFO - ************ - - [19/May/2025 01:18:18] "[36mGET /static/js/pos_returns.js HTTP/1.1[0m" 304 -
2025-05-19 01:18:19,229 - nobara - ERROR - استثناء: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.
معلومات الطلب: {"url": "http://************:5000/api/log-error", "method": "POST", "headers": {"Host": "************:5000", "Connection": "keep-alive", "Content-Length": "397", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Content-Type": "application/json", "Accept": "*/*", "Origin": "http://************:5000", "Referer": "http://************:5000/pos", "Accept-Encoding": "gzip, deflate", "Accept-Language": "en-US,en;q=0.9", "Cookie": "NEXT_LOCALE=en; session=.eJztksFqwzAMhl_F-JJLKLGd2FYeY-y2laJYMklIk-E4p9J3n5u9w04F8f8SQnxC6CFvccF95F32Xw8pcjHJKW1J1vL7aKymU-OpgziNX2rcmZteVPO0zqgv6VjzdOfLJ99_Fsz8wZETr4HFNswcciVG3MW6Ccw5TcORWVR5ygtX8vqs3_A3_D_h17q8fuJ9lH1OB5dqItlLTegdUIsxsrPglA8QAAcXicB5Z0LrS7RGd69u1NqQHZqgFABqyz7GMhZVB6AG6BwZywrQ69A0PliF6FQgUp2xAWKwCJ50cJaIySKbcoXbsXP620bJ5y9x1yli.aCpb5w.1Jv5pyD8PazDruwjt3qLzvBL5O8"}, "args": {}, "form": {}, "cookies": {"NEXT_LOCALE": "en", "session": ".eJztksFqwzAMhl_F-JJLKLGd2FYeY-y2laJYMklIk-E4p9J3n5u9w04F8f8SQnxC6CFvccF95F32Xw8pcjHJKW1J1vL7aKymU-OpgziNX2rcmZteVPO0zqgv6VjzdOfLJ99_Fsz8wZETr4HFNswcciVG3MW6Ccw5TcORWVR5ygtX8vqs3_A3_D_h17q8fuJ9lH1OB5dqItlLTegdUIsxsrPglA8QAAcXicB5Z0LrS7RGd69u1NqQHZqgFABqyz7GMhZVB6AG6BwZywrQ69A0PliF6FQgUp2xAWKwCJ50cJaIySKbcoXbsXP620bJ5y9x1yli.aCpb5w.1Jv5pyD8PazDruwjt3qLzvBL5O8"}, "remote_addr": "************", "user_agent": null}
تتبع الاستثناء:
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\app.py", line 917, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\app.py", line 891, in dispatch_request
    self.raise_routing_exception(req)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\app.py", line 500, in raise_routing_exception
    raise request.routing_exception  # type: ignore[misc]
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\ctx.py", line 362, in match_request
    result = self.url_adapter.match(return_rule=True)  # type: ignore
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\werkzeug\routing\map.py", line 629, in match
    raise NotFound() from None
werkzeug.exceptions.NotFound: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.

2025-05-19 01:18:19,278 - werkzeug - INFO - ************ - - [19/May/2025 01:18:19] "[35m[1mPOST /api/log-error HTTP/1.1[0m" 500 -
2025-05-19 01:18:19,292 - werkzeug - INFO - ************ - - [19/May/2025 01:18:19] "GET /api/notifications/count HTTP/1.1" 200 -
2025-05-19 01:18:19,342 - werkzeug - INFO - ************ - - [19/May/2025 01:18:19] "GET /api/settings HTTP/1.1" 200 -
2025-05-19 01:18:19,379 - werkzeug - INFO - ************ - - [19/May/2025 01:18:19] "GET /api/notifications/recent HTTP/1.1" 200 -
2025-05-19 01:18:19,415 - nobara - ERROR - استثناء: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.
معلومات الطلب: {"url": "http://************:5000/favicon.ico", "method": "GET", "headers": {"Host": "************:5000", "Connection": "keep-alive", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Accept": "image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8", "Referer": "http://************:5000/pos", "Accept-Encoding": "gzip, deflate", "Accept-Language": "en-US,en;q=0.9", "Cookie": "NEXT_LOCALE=en; session=.eJztksFqwzAMhl_F-JJLKLGd2FYeY-y2laJYMklIk-E4p9J3n5u9w04F8f8SQnxC6CFvccF95F32Xw8pcjHJKW1J1vL7aKymU-OpgziNX2rcmZteVPO0zqgv6VjzdOfLJ99_Fsz8wZETr4HFNswcciVG3MW6Ccw5TcORWVR5ygtX8vqs3_A3_D_h17q8fuJ9lH1OB5dqItlLTegdUIsxsrPglA8QAAcXicB5Z0LrS7RGd69u1NqQHZqgFABqyz7GMhZVB6AG6BwZywrQ69A0PliF6FQgUp2xAWKwCJ50cJaIySKbcoXbsXP620bJ5y9x1yli.aCpb5w.1Jv5pyD8PazDruwjt3qLzvBL5O8"}, "args": {}, "form": {}, "cookies": {"NEXT_LOCALE": "en", "session": ".eJztksFqwzAMhl_F-JJLKLGd2FYeY-y2laJYMklIk-E4p9J3n5u9w04F8f8SQnxC6CFvccF95F32Xw8pcjHJKW1J1vL7aKymU-OpgziNX2rcmZteVPO0zqgv6VjzdOfLJ99_Fsz8wZETr4HFNswcciVG3MW6Ccw5TcORWVR5ygtX8vqs3_A3_D_h17q8fuJ9lH1OB5dqItlLTegdUIsxsrPglA8QAAcXicB5Z0LrS7RGd69u1NqQHZqgFABqyz7GMhZVB6AG6BwZywrQ69A0PliF6FQgUp2xAWKwCJ50cJaIySKbcoXbsXP620bJ5y9x1yli.aCpb5w.1Jv5pyD8PazDruwjt3qLzvBL5O8"}, "remote_addr": "************", "user_agent": null}
تتبع الاستثناء:
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\app.py", line 917, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\app.py", line 891, in dispatch_request
    self.raise_routing_exception(req)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\app.py", line 500, in raise_routing_exception
    raise request.routing_exception  # type: ignore[misc]
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\ctx.py", line 362, in match_request
    result = self.url_adapter.match(return_rule=True)  # type: ignore
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\werkzeug\routing\map.py", line 629, in match
    raise NotFound() from None
werkzeug.exceptions.NotFound: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.

2025-05-19 01:18:19,478 - werkzeug - INFO - ************ - - [19/May/2025 01:18:19] "GET /api/pos/products?warehouse_id=1 HTTP/1.1" 200 -
2025-05-19 01:18:19,733 - werkzeug - INFO - ************ - - [19/May/2025 01:18:19] "[35m[1mGET /favicon.ico HTTP/1.1[0m" 500 -
2025-05-19 01:18:19,760 - werkzeug - INFO - ************ - - [19/May/2025 01:18:19] "GET /api/notifications/count HTTP/1.1" 200 -
2025-05-19 01:18:22,161 - nobara - ERROR - استثناء: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.
معلومات الطلب: {"url": "http://************:5000/api/log-error", "method": "POST", "headers": {"Host": "************:5000", "Connection": "keep-alive", "Content-Length": "275", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Content-Type": "application/json", "Accept": "*/*", "Origin": "http://************:5000", "Referer": "http://************:5000/pos", "Accept-Encoding": "gzip, deflate", "Accept-Language": "en-US,en;q=0.9", "Cookie": "NEXT_LOCALE=en; session=.eJztksFqwzAMhl_F-JJLKLGd2FYeY-y2laJYMklIk-E4p9J3n5u9w04F8f8SQnxC6CFvccF95F32Xw8pcjHJKW1J1vL7aKymU-OpgziNX2rcmZteVPO0zqgv6VjzdOfLJ99_Fsz8wZETr4HFNswcciVG3MW6Ccw5TcORWVR5ygtX8vqs3_A3_D_h17q8fuJ9lH1OB5dqItlLTegdUIsxsrPglA8QAAcXicB5Z0LrS7RGd69u1NqQHZqgFABqyz7GMhZVB6AG6BwZywrQ69A0PliF6FQgUp2xAWKwCJ50cJaIySKbcoXbsXP620bJ5y9x1yli.aCpb5w.1Jv5pyD8PazDruwjt3qLzvBL5O8"}, "args": {}, "form": {}, "cookies": {"NEXT_LOCALE": "en", "session": ".eJztksFqwzAMhl_F-JJLKLGd2FYeY-y2laJYMklIk-E4p9J3n5u9w04F8f8SQnxC6CFvccF95F32Xw8pcjHJKW1J1vL7aKymU-OpgziNX2rcmZteVPO0zqgv6VjzdOfLJ99_Fsz8wZETr4HFNswcciVG3MW6Ccw5TcORWVR5ygtX8vqs3_A3_D_h17q8fuJ9lH1OB5dqItlLTegdUIsxsrPglA8QAAcXicB5Z0LrS7RGd69u1NqQHZqgFABqyz7GMhZVB6AG6BwZywrQ69A0PliF6FQgUp2xAWKwCJ50cJaIySKbcoXbsXP620bJ5y9x1yli.aCpb5w.1Jv5pyD8PazDruwjt3qLzvBL5O8"}, "remote_addr": "************", "user_agent": null}
تتبع الاستثناء:
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\app.py", line 917, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\app.py", line 891, in dispatch_request
    self.raise_routing_exception(req)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\app.py", line 500, in raise_routing_exception
    raise request.routing_exception  # type: ignore[misc]
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\ctx.py", line 362, in match_request
    result = self.url_adapter.match(return_rule=True)  # type: ignore
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\werkzeug\routing\map.py", line 629, in match
    raise NotFound() from None
werkzeug.exceptions.NotFound: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.

2025-05-19 01:18:22,167 - werkzeug - INFO - ************ - - [19/May/2025 01:18:22] "[35m[1mPOST /api/log-error HTTP/1.1[0m" 500 -
2025-05-19 01:18:26,879 - werkzeug - INFO - ************ - - [19/May/2025 01:18:26] "GET /products HTTP/1.1" 200 -
2025-05-19 01:18:26,984 - werkzeug - INFO - ************ - - [19/May/2025 01:18:26] "[36mGET /static/img/nobara-logo.svg HTTP/1.1[0m" 304 -
2025-05-19 01:18:27,829 - werkzeug - INFO - ************ - - [19/May/2025 01:18:27] "GET /api/notifications/count HTTP/1.1" 200 -
2025-05-19 01:18:27,969 - nobara - ERROR - استثناء: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.
معلومات الطلب: {"url": "http://************:5000/favicon.ico", "method": "GET", "headers": {"Host": "************:5000", "Connection": "keep-alive", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Accept": "image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8", "Referer": "http://************:5000/products", "Accept-Encoding": "gzip, deflate", "Accept-Language": "en-US,en;q=0.9", "Cookie": "NEXT_LOCALE=en; session=.eJztksFqwzAMhl_F-JJLKLGd2FYeY-y2laJYMklIk-E4p9J3n5u9w04F8f8SQnxC6CFvccF95F32Xw8pcjHJKW1J1vL7aKymU-OpgziNX2rcmZteVPO0zqgv6VjzdOfLJ99_Fsz8wZETr4HFNswcciVG3MW6Ccw5TcORWVR5ygtX8vqs3_A3_D_h17q8fuJ9lH1OB5dqItlLTegdUIsxsrPglA8QAAcXicB5Z0LrS7RGd69u1NqQHZqgFABqyz7GMhZVB6AG6BwZywrQ69A0PliF6FQgUp2xAWKwCJ50cJaIySKbcoXbsXP620bJ5y9x1yli.aCpb5w.1Jv5pyD8PazDruwjt3qLzvBL5O8"}, "args": {}, "form": {}, "cookies": {"NEXT_LOCALE": "en", "session": ".eJztksFqwzAMhl_F-JJLKLGd2FYeY-y2laJYMklIk-E4p9J3n5u9w04F8f8SQnxC6CFvccF95F32Xw8pcjHJKW1J1vL7aKymU-OpgziNX2rcmZteVPO0zqgv6VjzdOfLJ99_Fsz8wZETr4HFNswcciVG3MW6Ccw5TcORWVR5ygtX8vqs3_A3_D_h17q8fuJ9lH1OB5dqItlLTegdUIsxsrPglA8QAAcXicB5Z0LrS7RGd69u1NqQHZqgFABqyz7GMhZVB6AG6BwZywrQ69A0PliF6FQgUp2xAWKwCJ50cJaIySKbcoXbsXP620bJ5y9x1yli.aCpb5w.1Jv5pyD8PazDruwjt3qLzvBL5O8"}, "remote_addr": "************", "user_agent": null}
تتبع الاستثناء:
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\app.py", line 917, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\app.py", line 891, in dispatch_request
    self.raise_routing_exception(req)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\app.py", line 500, in raise_routing_exception
    raise request.routing_exception  # type: ignore[misc]
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\ctx.py", line 362, in match_request
    result = self.url_adapter.match(return_rule=True)  # type: ignore
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\werkzeug\routing\map.py", line 629, in match
    raise NotFound() from None
werkzeug.exceptions.NotFound: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.

2025-05-19 01:18:28,205 - werkzeug - INFO - ************ - - [19/May/2025 01:18:28] "[35m[1mGET /favicon.ico HTTP/1.1[0m" 500 -
2025-05-19 01:18:32,673 - werkzeug - INFO - ************ - - [19/May/2025 01:18:32] "[32mGET /settings HTTP/1.1[0m" 302 -
2025-05-19 01:18:32,761 - werkzeug - INFO - ************ - - [19/May/2025 01:18:32] "GET /home HTTP/1.1" 200 -
2025-05-19 01:18:32,891 - werkzeug - INFO - ************ - - [19/May/2025 01:18:32] "[36mGET /static/css/nobara-pro.css?v=1.0.0 HTTP/1.1[0m" 304 -
2025-05-19 01:18:32,894 - werkzeug - INFO - ************ - - [19/May/2025 01:18:32] "[36mGET /static/css/dashboard-pro.css?v=1.0.1 HTTP/1.1[0m" 304 -
2025-05-19 01:18:32,898 - werkzeug - INFO - ************ - - [19/May/2025 01:18:32] "[36mGET /static/js/notifications-system.js?v=1.0.0 HTTP/1.1[0m" 304 -
2025-05-19 01:18:32,905 - werkzeug - INFO - ************ - - [19/May/2025 01:18:32] "[36mGET /static/js/system-manager.js?v=1.0.0 HTTP/1.1[0m" 304 -
2025-05-19 01:18:32,974 - werkzeug - INFO - ************ - - [19/May/2025 01:18:32] "[36mGET /static/js/dashboard-manager.js?v=1.0.0 HTTP/1.1[0m" 304 -
2025-05-19 01:18:32,984 - werkzeug - INFO - ************ - - [19/May/2025 01:18:32] "[36mGET /static/uploads/logo_20250518120852.jpeg HTTP/1.1[0m" 304 -
2025-05-19 01:18:33,054 - werkzeug - INFO - ************ - - [19/May/2025 01:18:33] "[36mGET /static/js/dashboard-pro.js?v=1.0.0 HTTP/1.1[0m" 304 -
2025-05-19 01:18:33,527 - werkzeug - INFO - ************ - - [19/May/2025 01:18:33] "GET /api/settings HTTP/1.1" 200 -
2025-05-19 01:18:33,556 - werkzeug - INFO - ************ - - [19/May/2025 01:18:33] "GET /api/notifications/count HTTP/1.1" 200 -
2025-05-19 01:18:33,592 - werkzeug - INFO - ************ - - [19/May/2025 01:18:33] "GET /api/sales-chart-data?days=30 HTTP/1.1" 200 -
2025-05-19 01:18:33,605 - werkzeug - INFO - ************ - - [19/May/2025 01:18:33] "GET /api/category-distribution HTTP/1.1" 200 -
2025-05-19 01:18:33,609 - werkzeug - INFO - ************ - - [19/May/2025 01:18:33] "GET /api/settings HTTP/1.1" 200 -
2025-05-19 01:18:33,725 - werkzeug - INFO - ************ - - [19/May/2025 01:18:33] "GET /api/dashboard HTTP/1.1" 200 -
2025-05-19 01:18:33,954 - werkzeug - INFO - ************ - - [19/May/2025 01:18:33] "GET /api/notifications/count HTTP/1.1" 200 -
2025-05-19 01:18:34,025 - werkzeug - INFO - ************ - - [19/May/2025 01:18:34] "GET /api/notifications/recent HTTP/1.1" 200 -
2025-05-19 01:18:34,051 - nobara - ERROR - استثناء: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.
معلومات الطلب: {"url": "http://************:5000/favicon.ico", "method": "GET", "headers": {"Host": "************:5000", "Connection": "keep-alive", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Accept": "image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8", "Referer": "http://************:5000/home", "Accept-Encoding": "gzip, deflate", "Accept-Language": "en-US,en;q=0.9", "Cookie": "NEXT_LOCALE=en; session=.eJztksFqwzAMhl_F-JJLKLGd2FYeY-y2laJYMklIk-E4p9J3n5u9w04F8f8SQnxC6CFvccF95F32Xw8pcjHJKW1J1vL7aKymU-OpgziNX2rcmZteVPO0zqgv6VjzdOfLJ99_Fsz8wZETr4HFNswcciVG3MW6Ccw5TcORWVR5ygtX8vqs3_A3_D_h17q8fuJ9lH1OB5dqItlLTegdUIsxsrPglA8QAAcXicB5Z0LrS7RGd69u1NqQHZqgFABqyz7GMhZVB6AG6BwZywrQ69A0PliF6FQgUp2xAWKwCJ50cJaIySKbcoXbsXP620bJ5y9x1yli.aCpb5w.1Jv5pyD8PazDruwjt3qLzvBL5O8"}, "args": {}, "form": {}, "cookies": {"NEXT_LOCALE": "en", "session": ".eJztksFqwzAMhl_F-JJLKLGd2FYeY-y2laJYMklIk-E4p9J3n5u9w04F8f8SQnxC6CFvccF95F32Xw8pcjHJKW1J1vL7aKymU-OpgziNX2rcmZteVPO0zqgv6VjzdOfLJ99_Fsz8wZETr4HFNswcciVG3MW6Ccw5TcORWVR5ygtX8vqs3_A3_D_h17q8fuJ9lH1OB5dqItlLTegdUIsxsrPglA8QAAcXicB5Z0LrS7RGd69u1NqQHZqgFABqyz7GMhZVB6AG6BwZywrQ69A0PliF6FQgUp2xAWKwCJ50cJaIySKbcoXbsXP620bJ5y9x1yli.aCpb5w.1Jv5pyD8PazDruwjt3qLzvBL5O8"}, "remote_addr": "************", "user_agent": null}
تتبع الاستثناء:
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\app.py", line 917, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\app.py", line 891, in dispatch_request
    self.raise_routing_exception(req)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\app.py", line 500, in raise_routing_exception
    raise request.routing_exception  # type: ignore[misc]
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\ctx.py", line 362, in match_request
    result = self.url_adapter.match(return_rule=True)  # type: ignore
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\werkzeug\routing\map.py", line 629, in match
    raise NotFound() from None
werkzeug.exceptions.NotFound: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.

2025-05-19 01:18:34,059 - werkzeug - INFO - ************ - - [19/May/2025 01:18:34] "GET /api/dashboard HTTP/1.1" 200 -
2025-05-19 01:18:34,127 - werkzeug - INFO - ************ - - [19/May/2025 01:18:34] "[35m[1mGET /favicon.ico HTTP/1.1[0m" 500 -
2025-05-19 01:18:34,152 - werkzeug - INFO - ************ - - [19/May/2025 01:18:34] "GET /api/notifications/count HTTP/1.1" 200 -
2025-05-19 01:18:34,206 - werkzeug - INFO - ************ - - [19/May/2025 01:18:34] "GET /api/notifications/recent HTTP/1.1" 200 -
2025-05-19 01:18:34,990 - werkzeug - INFO - ************ - - [19/May/2025 01:18:34] "[32mGET /settings HTTP/1.1[0m" 302 -
2025-05-19 01:18:35,059 - werkzeug - INFO - ************ - - [19/May/2025 01:18:35] "GET /home HTTP/1.1" 200 -
2025-05-19 01:18:35,150 - werkzeug - INFO - ************ - - [19/May/2025 01:18:35] "[36mGET /static/css/nobara-pro.css?v=1.0.0 HTTP/1.1[0m" 304 -
2025-05-19 01:18:35,175 - werkzeug - INFO - ************ - - [19/May/2025 01:18:35] "[36mGET /static/css/dashboard-pro.css?v=1.0.1 HTTP/1.1[0m" 304 -
2025-05-19 01:18:35,177 - werkzeug - INFO - ************ - - [19/May/2025 01:18:35] "[36mGET /static/js/system-manager.js?v=1.0.0 HTTP/1.1[0m" 304 -
2025-05-19 01:18:35,188 - werkzeug - INFO - ************ - - [19/May/2025 01:18:35] "[36mGET /static/js/dashboard-manager.js?v=1.0.0 HTTP/1.1[0m" 304 -
2025-05-19 01:18:35,188 - werkzeug - INFO - ************ - - [19/May/2025 01:18:35] "[36mGET /static/js/notifications-system.js?v=1.0.0 HTTP/1.1[0m" 304 -
2025-05-19 01:18:35,192 - werkzeug - INFO - ************ - - [19/May/2025 01:18:35] "[36mGET /static/uploads/logo_20250518120852.jpeg HTTP/1.1[0m" 304 -
2025-05-19 01:18:35,220 - werkzeug - INFO - ************ - - [19/May/2025 01:18:35] "[36mGET /static/js/dashboard-pro.js?v=1.0.0 HTTP/1.1[0m" 304 -
2025-05-19 01:18:35,675 - werkzeug - INFO - ************ - - [19/May/2025 01:18:35] "GET /api/settings HTTP/1.1" 200 -
2025-05-19 01:18:35,687 - werkzeug - INFO - ************ - - [19/May/2025 01:18:35] "GET /api/notifications/count HTTP/1.1" 200 -
2025-05-19 01:18:35,709 - werkzeug - INFO - ************ - - [19/May/2025 01:18:35] "GET /api/category-distribution HTTP/1.1" 200 -
2025-05-19 01:18:35,743 - werkzeug - INFO - ************ - - [19/May/2025 01:18:35] "GET /api/sales-chart-data?days=30 HTTP/1.1" 200 -
2025-05-19 01:18:35,783 - werkzeug - INFO - ************ - - [19/May/2025 01:18:35] "GET /api/settings HTTP/1.1" 200 -
2025-05-19 01:18:35,879 - werkzeug - INFO - ************ - - [19/May/2025 01:18:35] "GET /api/dashboard HTTP/1.1" 200 -
2025-05-19 01:18:36,245 - werkzeug - INFO - ************ - - [19/May/2025 01:18:36] "GET /api/dashboard HTTP/1.1" 200 -
2025-05-19 01:18:36,246 - werkzeug - INFO - ************ - - [19/May/2025 01:18:36] "GET /api/notifications/recent HTTP/1.1" 200 -
2025-05-19 01:18:36,292 - werkzeug - INFO - ************ - - [19/May/2025 01:18:36] "GET /api/notifications/count HTTP/1.1" 200 -
2025-05-19 01:18:36,308 - nobara - ERROR - استثناء: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.
معلومات الطلب: {"url": "http://************:5000/favicon.ico", "method": "GET", "headers": {"Host": "************:5000", "Connection": "keep-alive", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Accept": "image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8", "Referer": "http://************:5000/home", "Accept-Encoding": "gzip, deflate", "Accept-Language": "en-US,en;q=0.9", "Cookie": "NEXT_LOCALE=en; session=.eJztksFqwzAMhl_F-JJLKLGd2FYeY-y2laJYMklIk-E4p9J3n5u9w04F8f8SQnxC6CFvccF95F32Xw8pcjHJKW1J1vL7aKymU-OpgziNX2rcmZteVPO0zqgv6VjzdOfLJ99_Fsz8wZETr4HFNswcciVG3MW6Ccw5TcORWVR5ygtX8vqs3_A3_D_h17q8fuJ9lH1OB5dqItlLTegdUIsxsrPglA8QAAcXicB5Z0LrS7RGd69u1NqQHZqgFABqyz7GMhZVB6AG6BwZywrQ69A0PliF6FQgUp2xAWKwCJ50cJaIySKbcoXbsXP620bJ5y9x1yli.aCpb5w.1Jv5pyD8PazDruwjt3qLzvBL5O8"}, "args": {}, "form": {}, "cookies": {"NEXT_LOCALE": "en", "session": ".eJztksFqwzAMhl_F-JJLKLGd2FYeY-y2laJYMklIk-E4p9J3n5u9w04F8f8SQnxC6CFvccF95F32Xw8pcjHJKW1J1vL7aKymU-OpgziNX2rcmZteVPO0zqgv6VjzdOfLJ99_Fsz8wZETr4HFNswcciVG3MW6Ccw5TcORWVR5ygtX8vqs3_A3_D_h17q8fuJ9lH1OB5dqItlLTegdUIsxsrPglA8QAAcXicB5Z0LrS7RGd69u1NqQHZqgFABqyz7GMhZVB6AG6BwZywrQ69A0PliF6FQgUp2xAWKwCJ50cJaIySKbcoXbsXP620bJ5y9x1yli.aCpb5w.1Jv5pyD8PazDruwjt3qLzvBL5O8"}, "remote_addr": "************", "user_agent": null}
تتبع الاستثناء:
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\app.py", line 917, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\app.py", line 891, in dispatch_request
    self.raise_routing_exception(req)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\app.py", line 500, in raise_routing_exception
    raise request.routing_exception  # type: ignore[misc]
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\ctx.py", line 362, in match_request
    result = self.url_adapter.match(return_rule=True)  # type: ignore
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\werkzeug\routing\map.py", line 629, in match
    raise NotFound() from None
werkzeug.exceptions.NotFound: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.

2025-05-19 01:18:36,342 - werkzeug - INFO - ************ - - [19/May/2025 01:18:36] "[35m[1mGET /favicon.ico HTTP/1.1[0m" 500 -
2025-05-19 01:18:36,361 - werkzeug - INFO - ************ - - [19/May/2025 01:18:36] "GET /api/notifications/recent HTTP/1.1" 200 -
2025-05-19 01:18:36,372 - werkzeug - INFO - ************ - - [19/May/2025 01:18:36] "GET /api/notifications/count HTTP/1.1" 200 -
2025-05-19 01:18:36,810 - werkzeug - INFO - ************ - - [19/May/2025 01:18:36] "[32mGET /settings HTTP/1.1[0m" 302 -
2025-05-19 01:18:36,871 - werkzeug - INFO - ************ - - [19/May/2025 01:18:36] "GET /home HTTP/1.1" 200 -
2025-05-19 01:18:37,041 - werkzeug - INFO - ************ - - [19/May/2025 01:18:37] "[36mGET /static/css/nobara-pro.css?v=1.0.0 HTTP/1.1[0m" 304 -
2025-05-19 01:18:37,050 - werkzeug - INFO - ************ - - [19/May/2025 01:18:37] "[36mGET /static/css/dashboard-pro.css?v=1.0.1 HTTP/1.1[0m" 304 -
2025-05-19 01:18:37,052 - werkzeug - INFO - ************ - - [19/May/2025 01:18:37] "[36mGET /static/js/notifications-system.js?v=1.0.0 HTTP/1.1[0m" 304 -
2025-05-19 01:18:37,052 - werkzeug - INFO - ************ - - [19/May/2025 01:18:37] "[36mGET /static/js/system-manager.js?v=1.0.0 HTTP/1.1[0m" 304 -
2025-05-19 01:18:37,058 - werkzeug - INFO - ************ - - [19/May/2025 01:18:37] "[36mGET /static/js/dashboard-manager.js?v=1.0.0 HTTP/1.1[0m" 304 -
2025-05-19 01:18:37,059 - werkzeug - INFO - ************ - - [19/May/2025 01:18:37] "[36mGET /static/uploads/logo_20250518120852.jpeg HTTP/1.1[0m" 304 -
2025-05-19 01:18:37,157 - werkzeug - INFO - ************ - - [19/May/2025 01:18:37] "[36mGET /static/js/dashboard-pro.js?v=1.0.0 HTTP/1.1[0m" 304 -
2025-05-19 01:18:37,572 - werkzeug - INFO - ************ - - [19/May/2025 01:18:37] "GET /api/sales-chart-data?days=30 HTTP/1.1" 200 -
2025-05-19 01:18:37,573 - werkzeug - INFO - ************ - - [19/May/2025 01:18:37] "GET /api/settings HTTP/1.1" 200 -
2025-05-19 01:18:37,589 - werkzeug - INFO - ************ - - [19/May/2025 01:18:37] "GET /api/notifications/count HTTP/1.1" 200 -
2025-05-19 01:18:37,624 - werkzeug - INFO - ************ - - [19/May/2025 01:18:37] "GET /api/category-distribution HTTP/1.1" 200 -
2025-05-19 01:18:37,631 - werkzeug - INFO - ************ - - [19/May/2025 01:18:37] "GET /api/settings HTTP/1.1" 200 -
2025-05-19 01:18:37,726 - werkzeug - INFO - ************ - - [19/May/2025 01:18:37] "GET /api/dashboard HTTP/1.1" 200 -
2025-05-19 01:18:37,892 - werkzeug - INFO - ************ - - [19/May/2025 01:18:37] "GET /api/notifications/recent HTTP/1.1" 200 -
2025-05-19 01:18:37,893 - werkzeug - INFO - ************ - - [19/May/2025 01:18:37] "GET /api/notifications/count HTTP/1.1" 200 -
2025-05-19 01:18:37,906 - nobara - ERROR - استثناء: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.
معلومات الطلب: {"url": "http://************:5000/favicon.ico", "method": "GET", "headers": {"Host": "************:5000", "Connection": "keep-alive", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Accept": "image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8", "Referer": "http://************:5000/home", "Accept-Encoding": "gzip, deflate", "Accept-Language": "en-US,en;q=0.9", "Cookie": "NEXT_LOCALE=en; session=.eJztksFqwzAMhl_F-JJLKLGd2FYeY-y2laJYMklIk-E4p9J3n5u9w04F8f8SQnxC6CFvccF95F32Xw8pcjHJKW1J1vL7aKymU-OpgziNX2rcmZteVPO0zqgv6VjzdOfLJ99_Fsz8wZETr4HFNswcciVG3MW6Ccw5TcORWVR5ygtX8vqs3_A3_D_h17q8fuJ9lH1OB5dqItlLTegdUIsxsrPglA8QAAcXicB5Z0LrS7RGd69u1NqQHZqgFABqyz7GMhZVB6AG6BwZywrQ69A0PliF6FQgUp2xAWKwCJ50cJaIySKbcoXbsXP620bJ5y9x1yli.aCpb5w.1Jv5pyD8PazDruwjt3qLzvBL5O8"}, "args": {}, "form": {}, "cookies": {"NEXT_LOCALE": "en", "session": ".eJztksFqwzAMhl_F-JJLKLGd2FYeY-y2laJYMklIk-E4p9J3n5u9w04F8f8SQnxC6CFvccF95F32Xw8pcjHJKW1J1vL7aKymU-OpgziNX2rcmZteVPO0zqgv6VjzdOfLJ99_Fsz8wZETr4HFNswcciVG3MW6Ccw5TcORWVR5ygtX8vqs3_A3_D_h17q8fuJ9lH1OB5dqItlLTegdUIsxsrPglA8QAAcXicB5Z0LrS7RGd69u1NqQHZqgFABqyz7GMhZVB6AG6BwZywrQ69A0PliF6FQgUp2xAWKwCJ50cJaIySKbcoXbsXP620bJ5y9x1yli.aCpb5w.1Jv5pyD8PazDruwjt3qLzvBL5O8"}, "remote_addr": "************", "user_agent": null}
تتبع الاستثناء:
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\app.py", line 917, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\app.py", line 891, in dispatch_request
    self.raise_routing_exception(req)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\app.py", line 500, in raise_routing_exception
    raise request.routing_exception  # type: ignore[misc]
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\ctx.py", line 362, in match_request
    result = self.url_adapter.match(return_rule=True)  # type: ignore
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\werkzeug\routing\map.py", line 629, in match
    raise NotFound() from None
werkzeug.exceptions.NotFound: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.

2025-05-19 01:18:37,944 - werkzeug - INFO - ************ - - [19/May/2025 01:18:37] "GET /api/dashboard HTTP/1.1" 200 -
2025-05-19 01:18:38,103 - werkzeug - INFO - ************ - - [19/May/2025 01:18:38] "[35m[1mGET /favicon.ico HTTP/1.1[0m" 500 -
2025-05-19 01:18:38,126 - werkzeug - INFO - ************ - - [19/May/2025 01:18:38] "GET /api/notifications/recent HTTP/1.1" 200 -
2025-05-19 01:18:38,194 - werkzeug - INFO - ************ - - [19/May/2025 01:18:38] "GET /api/notifications/count HTTP/1.1" 200 -
2025-05-19 01:18:39,420 - nobara - ERROR - استثناء: warehouses/warehouses.html
معلومات الطلب: {"url": "http://************:5000/warehouses", "method": "GET", "headers": {"Host": "************:5000", "Connection": "keep-alive", "Upgrade-Insecure-Requests": "1", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7", "Referer": "http://************:5000/home", "Accept-Encoding": "gzip, deflate", "Accept-Language": "en-US,en;q=0.9", "Cookie": "NEXT_LOCALE=en; session=.eJztksFqwzAMhl_F-JJLKLGd2FYeY-y2laJYMklIk-E4p9J3n5u9w04F8f8SQnxC6CFvccF95F32Xw8pcjHJKW1J1vL7aKymU-OpgziNX2rcmZteVPO0zqgv6VjzdOfLJ99_Fsz8wZETr4HFNswcciVG3MW6Ccw5TcORWVR5ygtX8vqs3_A3_D_h17q8fuJ9lH1OB5dqItlLTegdUIsxsrPglA8QAAcXicB5Z0LrS7RGd69u1NqQHZqgFABqyz7GMhZVB6AG6BwZywrQ69A0PliF6FQgUp2xAWKwCJ50cJaIySKbcoXbsXP620bJ5y9x1yli.aCpb5w.1Jv5pyD8PazDruwjt3qLzvBL5O8"}, "args": {}, "form": {}, "cookies": {"NEXT_LOCALE": "en", "session": ".eJztksFqwzAMhl_F-JJLKLGd2FYeY-y2laJYMklIk-E4p9J3n5u9w04F8f8SQnxC6CFvccF95F32Xw8pcjHJKW1J1vL7aKymU-OpgziNX2rcmZteVPO0zqgv6VjzdOfLJ99_Fsz8wZETr4HFNswcciVG3MW6Ccw5TcORWVR5ygtX8vqs3_A3_D_h17q8fuJ9lH1OB5dqItlLTegdUIsxsrPglA8QAAcXicB5Z0LrS7RGd69u1NqQHZqgFABqyz7GMhZVB6AG6BwZywrQ69A0PliF6FQgUp2xAWKwCJ50cJaIySKbcoXbsXP620bJ5y9x1yli.aCpb5w.1Jv5pyD8PazDruwjt3qLzvBL5O8"}, "remote_addr": "************", "user_agent": null}
تتبع الاستثناء:
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\app.py", line 917, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\app.py", line 902, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)  # type: ignore[no-any-return]
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask_login\utils.py", line 290, in decorated_view
    return current_app.ensure_sync(func)(*args, **kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\routes\warehouses.py", line 45, in index
    return render_template('warehouses/warehouses.html',
                          warehouses=warehouses,
    ...<2 lines>...
                          status=status,
                          stats=stats)
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\templating.py", line 149, in render_template
    template = app.jinja_env.get_or_select_template(template_name_or_list)
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\jinja2\environment.py", line 1087, in get_or_select_template
    return self.get_template(template_name_or_list, parent, globals)
           ~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\jinja2\environment.py", line 1016, in get_template
    return self._load_template(name, globals)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\jinja2\environment.py", line 975, in _load_template
    template = self.loader.load(self, name, self.make_globals(globals))
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\jinja2\loaders.py", line 126, in load
    source, filename, uptodate = self.get_source(environment, name)
                                 ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\templating.py", line 65, in get_source
    return self._get_source_fast(environment, template)
           ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\templating.py", line 99, in _get_source_fast
    raise TemplateNotFound(template)
jinja2.exceptions.TemplateNotFound: warehouses/warehouses.html

2025-05-19 01:18:39,581 - werkzeug - INFO - ************ - - [19/May/2025 01:18:39] "[35m[1mGET /warehouses HTTP/1.1[0m" 500 -
2025-05-19 01:18:39,740 - nobara - ERROR - استثناء: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.
معلومات الطلب: {"url": "http://************:5000/favicon.ico", "method": "GET", "headers": {"Host": "************:5000", "Connection": "keep-alive", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Accept": "image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8", "Referer": "http://************:5000/warehouses", "Accept-Encoding": "gzip, deflate", "Accept-Language": "en-US,en;q=0.9", "Cookie": "NEXT_LOCALE=en; session=.eJztksFqwzAMhl_F-JJLKLGd2FYeY-y2laJYMklIk-E4p9J3n5u9w04F8f8SQnxC6CFvccF95F32Xw8pcjHJKW1J1vL7aKymU-OpgziNX2rcmZteVPO0zqgv6VjzdOfLJ99_Fsz8wZETr4HFNswcciVG3MW6Ccw5TcORWVR5ygtX8vqs3_A3_D_h17q8fuJ9lH1OB5dqItlLTegdUIsxsrPglA8QAAcXicB5Z0LrS7RGd69u1NqQHZqgFABqyz7GMhZVB6AG6BwZywrQ69A0PliF6FQgUp2xAWKwCJ50cJaIySKbcoXbsXP620bJ5y9x1yli.aCpb5w.1Jv5pyD8PazDruwjt3qLzvBL5O8"}, "args": {}, "form": {}, "cookies": {"NEXT_LOCALE": "en", "session": ".eJztksFqwzAMhl_F-JJLKLGd2FYeY-y2laJYMklIk-E4p9J3n5u9w04F8f8SQnxC6CFvccF95F32Xw8pcjHJKW1J1vL7aKymU-OpgziNX2rcmZteVPO0zqgv6VjzdOfLJ99_Fsz8wZETr4HFNswcciVG3MW6Ccw5TcORWVR5ygtX8vqs3_A3_D_h17q8fuJ9lH1OB5dqItlLTegdUIsxsrPglA8QAAcXicB5Z0LrS7RGd69u1NqQHZqgFABqyz7GMhZVB6AG6BwZywrQ69A0PliF6FQgUp2xAWKwCJ50cJaIySKbcoXbsXP620bJ5y9x1yli.aCpb5w.1Jv5pyD8PazDruwjt3qLzvBL5O8"}, "remote_addr": "************", "user_agent": null}
تتبع الاستثناء:
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\app.py", line 917, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\app.py", line 891, in dispatch_request
    self.raise_routing_exception(req)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\app.py", line 500, in raise_routing_exception
    raise request.routing_exception  # type: ignore[misc]
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\ctx.py", line 362, in match_request
    result = self.url_adapter.match(return_rule=True)  # type: ignore
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\werkzeug\routing\map.py", line 629, in match
    raise NotFound() from None
werkzeug.exceptions.NotFound: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.

2025-05-19 01:18:39,744 - werkzeug - INFO - ************ - - [19/May/2025 01:18:39] "[35m[1mGET /favicon.ico HTTP/1.1[0m" 500 -
2025-05-19 01:18:41,898 - nobara - ERROR - استثناء: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.
معلومات الطلب: {"url": "http://************:5000/favicon.ico", "method": "GET", "headers": {"Host": "************:5000", "Connection": "keep-alive", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Accept": "image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8", "Referer": "http://************:5000/home", "Accept-Encoding": "gzip, deflate", "Accept-Language": "en-US,en;q=0.9", "Cookie": "NEXT_LOCALE=en; session=.eJztksFqwzAMhl_F-JJLKLGd2FYeY-y2laJYMklIk-E4p9J3n5u9w04F8f8SQnxC6CFvccF95F32Xw8pcjHJKW1J1vL7aKymU-OpgziNX2rcmZteVPO0zqgv6VjzdOfLJ99_Fsz8wZETr4HFNswcciVG3MW6Ccw5TcORWVR5ygtX8vqs3_A3_D_h17q8fuJ9lH1OB5dqItlLTegdUIsxsrPglA8QAAcXicB5Z0LrS7RGd69u1NqQHZqgFABqyz7GMhZVB6AG6BwZywrQ69A0PliF6FQgUp2xAWKwCJ50cJaIySKbcoXbsXP620bJ5y9x1yli.aCpb5w.1Jv5pyD8PazDruwjt3qLzvBL5O8"}, "args": {}, "form": {}, "cookies": {"NEXT_LOCALE": "en", "session": ".eJztksFqwzAMhl_F-JJLKLGd2FYeY-y2laJYMklIk-E4p9J3n5u9w04F8f8SQnxC6CFvccF95F32Xw8pcjHJKW1J1vL7aKymU-OpgziNX2rcmZteVPO0zqgv6VjzdOfLJ99_Fsz8wZETr4HFNswcciVG3MW6Ccw5TcORWVR5ygtX8vqs3_A3_D_h17q8fuJ9lH1OB5dqItlLTegdUIsxsrPglA8QAAcXicB5Z0LrS7RGd69u1NqQHZqgFABqyz7GMhZVB6AG6BwZywrQ69A0PliF6FQgUp2xAWKwCJ50cJaIySKbcoXbsXP620bJ5y9x1yli.aCpb5w.1Jv5pyD8PazDruwjt3qLzvBL5O8"}, "remote_addr": "************", "user_agent": null}
تتبع الاستثناء:
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\app.py", line 917, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\app.py", line 891, in dispatch_request
    self.raise_routing_exception(req)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\app.py", line 500, in raise_routing_exception
    raise request.routing_exception  # type: ignore[misc]
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\ctx.py", line 362, in match_request
    result = self.url_adapter.match(return_rule=True)  # type: ignore
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\werkzeug\routing\map.py", line 629, in match
    raise NotFound() from None
werkzeug.exceptions.NotFound: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.

2025-05-19 01:18:42,100 - werkzeug - INFO - ************ - - [19/May/2025 01:18:42] "[35m[1mGET /favicon.ico HTTP/1.1[0m" 500 -
2025-05-19 01:18:43,152 - werkzeug - INFO - ************ - - [19/May/2025 01:18:43] "GET /reports HTTP/1.1" 200 -
2025-05-19 01:18:43,240 - werkzeug - INFO - ************ - - [19/May/2025 01:18:43] "[36mGET /static/uploads/logo_20250518120852.jpeg HTTP/1.1[0m" 304 -
2025-05-19 01:18:43,715 - werkzeug - INFO - ************ - - [19/May/2025 01:18:43] "GET /api/notifications/count HTTP/1.1" 200 -
2025-05-19 01:18:43,744 - nobara - ERROR - استثناء: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.
معلومات الطلب: {"url": "http://************:5000/favicon.ico", "method": "GET", "headers": {"Host": "************:5000", "Connection": "keep-alive", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Accept": "image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8", "Referer": "http://************:5000/reports", "Accept-Encoding": "gzip, deflate", "Accept-Language": "en-US,en;q=0.9", "Cookie": "NEXT_LOCALE=en; session=.eJztksFqwzAMhl_F-JJLKLGd2FYeY-y2laJYMklIk-E4p9J3n5u9w04F8f8SQnxC6CFvccF95F32Xw8pcjHJKW1J1vL7aKymU-OpgziNX2rcmZteVPO0zqgv6VjzdOfLJ99_Fsz8wZETr4HFNswcciVG3MW6Ccw5TcORWVR5ygtX8vqs3_A3_D_h17q8fuJ9lH1OB5dqItlLTegdUIsxsrPglA8QAAcXicB5Z0LrS7RGd69u1NqQHZqgFABqyz7GMhZVB6AG6BwZywrQ69A0PliF6FQgUp2xAWKwCJ50cJaIySKbcoXbsXP620bJ5y9x1yli.aCpb5w.1Jv5pyD8PazDruwjt3qLzvBL5O8"}, "args": {}, "form": {}, "cookies": {"NEXT_LOCALE": "en", "session": ".eJztksFqwzAMhl_F-JJLKLGd2FYeY-y2laJYMklIk-E4p9J3n5u9w04F8f8SQnxC6CFvccF95F32Xw8pcjHJKW1J1vL7aKymU-OpgziNX2rcmZteVPO0zqgv6VjzdOfLJ99_Fsz8wZETr4HFNswcciVG3MW6Ccw5TcORWVR5ygtX8vqs3_A3_D_h17q8fuJ9lH1OB5dqItlLTegdUIsxsrPglA8QAAcXicB5Z0LrS7RGd69u1NqQHZqgFABqyz7GMhZVB6AG6BwZywrQ69A0PliF6FQgUp2xAWKwCJ50cJaIySKbcoXbsXP620bJ5y9x1yli.aCpb5w.1Jv5pyD8PazDruwjt3qLzvBL5O8"}, "remote_addr": "************", "user_agent": null}
تتبع الاستثناء:
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\app.py", line 917, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\app.py", line 891, in dispatch_request
    self.raise_routing_exception(req)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\app.py", line 500, in raise_routing_exception
    raise request.routing_exception  # type: ignore[misc]
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\ctx.py", line 362, in match_request
    result = self.url_adapter.match(return_rule=True)  # type: ignore
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\werkzeug\routing\map.py", line 629, in match
    raise NotFound() from None
werkzeug.exceptions.NotFound: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.

2025-05-19 01:18:43,792 - werkzeug - INFO - ************ - - [19/May/2025 01:18:43] "[35m[1mGET /favicon.ico HTTP/1.1[0m" 500 -
2025-05-19 01:18:45,584 - werkzeug - INFO - ************ - - [19/May/2025 01:18:45] "GET /reports/sales HTTP/1.1" 200 -
2025-05-19 01:18:45,709 - werkzeug - INFO - ************ - - [19/May/2025 01:18:45] "[36mGET /static/css/nobara-pro.css?v=1.0.0 HTTP/1.1[0m" 304 -
2025-05-19 01:18:45,716 - werkzeug - INFO - ************ - - [19/May/2025 01:18:45] "[36mGET /static/css/dashboard-pro.css?v=1.0.1 HTTP/1.1[0m" 304 -
2025-05-19 01:18:45,720 - werkzeug - INFO - ************ - - [19/May/2025 01:18:45] "[36mGET /static/js/notifications-system.js?v=1.0.0 HTTP/1.1[0m" 304 -
2025-05-19 01:18:45,729 - werkzeug - INFO - ************ - - [19/May/2025 01:18:45] "[36mGET /static/js/system-manager.js?v=1.0.0 HTTP/1.1[0m" 304 -
2025-05-19 01:18:45,737 - werkzeug - INFO - ************ - - [19/May/2025 01:18:45] "[36mGET /static/js/dashboard-manager.js?v=1.0.0 HTTP/1.1[0m" 304 -
2025-05-19 01:18:45,762 - werkzeug - INFO - ************ - - [19/May/2025 01:18:45] "[36mGET /static/uploads/logo_20250518120852.jpeg HTTP/1.1[0m" 304 -
2025-05-19 01:18:45,795 - werkzeug - INFO - ************ - - [19/May/2025 01:18:45] "[36mGET /static/js/dashboard-pro.js?v=1.0.0 HTTP/1.1[0m" 304 -
2025-05-19 01:18:46,094 - werkzeug - INFO - ************ - - [19/May/2025 01:18:46] "GET /api/settings HTTP/1.1" 200 -
2025-05-19 01:18:46,097 - werkzeug - INFO - ************ - - [19/May/2025 01:18:46] "GET /api/notifications/count HTTP/1.1" 200 -
2025-05-19 01:18:46,191 - werkzeug - INFO - ************ - - [19/May/2025 01:18:46] "GET /api/settings HTTP/1.1" 200 -
2025-05-19 01:18:46,213 - werkzeug - INFO - ************ - - [19/May/2025 01:18:46] "GET /api/notifications/recent HTTP/1.1" 200 -
2025-05-19 01:18:46,314 - werkzeug - INFO - ************ - - [19/May/2025 01:18:46] "GET /api/notifications/count HTTP/1.1" 200 -
2025-05-19 01:18:46,367 - nobara - ERROR - استثناء: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.
معلومات الطلب: {"url": "http://************:5000/favicon.ico", "method": "GET", "headers": {"Host": "************:5000", "Connection": "keep-alive", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Accept": "image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8", "Referer": "http://************:5000/reports/sales", "Accept-Encoding": "gzip, deflate", "Accept-Language": "en-US,en;q=0.9", "Cookie": "NEXT_LOCALE=en; session=.eJztksFqwzAMhl_F-JJLKLGd2FYeY-y2laJYMklIk-E4p9J3n5u9w04F8f8SQnxC6CFvccF95F32Xw8pcjHJKW1J1vL7aKymU-OpgziNX2rcmZteVPO0zqgv6VjzdOfLJ99_Fsz8wZETr4HFNswcciVG3MW6Ccw5TcORWVR5ygtX8vqs3_A3_D_h17q8fuJ9lH1OB5dqItlLTegdUIsxsrPglA8QAAcXicB5Z0LrS7RGd69u1NqQHZqgFABqyz7GMhZVB6AG6BwZywrQ69A0PliF6FQgUp2xAWKwCJ50cJaIySKbcoXbsXP620bJ5y9x1yli.aCpb5w.1Jv5pyD8PazDruwjt3qLzvBL5O8"}, "args": {}, "form": {}, "cookies": {"NEXT_LOCALE": "en", "session": ".eJztksFqwzAMhl_F-JJLKLGd2FYeY-y2laJYMklIk-E4p9J3n5u9w04F8f8SQnxC6CFvccF95F32Xw8pcjHJKW1J1vL7aKymU-OpgziNX2rcmZteVPO0zqgv6VjzdOfLJ99_Fsz8wZETr4HFNswcciVG3MW6Ccw5TcORWVR5ygtX8vqs3_A3_D_h17q8fuJ9lH1OB5dqItlLTegdUIsxsrPglA8QAAcXicB5Z0LrS7RGd69u1NqQHZqgFABqyz7GMhZVB6AG6BwZywrQ69A0PliF6FQgUp2xAWKwCJ50cJaIySKbcoXbsXP620bJ5y9x1yli.aCpb5w.1Jv5pyD8PazDruwjt3qLzvBL5O8"}, "remote_addr": "************", "user_agent": null}
تتبع الاستثناء:
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\app.py", line 917, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\app.py", line 891, in dispatch_request
    self.raise_routing_exception(req)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\app.py", line 500, in raise_routing_exception
    raise request.routing_exception  # type: ignore[misc]
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\ctx.py", line 362, in match_request
    result = self.url_adapter.match(return_rule=True)  # type: ignore
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\werkzeug\routing\map.py", line 629, in match
    raise NotFound() from None
werkzeug.exceptions.NotFound: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.

2025-05-19 01:18:46,393 - werkzeug - INFO - ************ - - [19/May/2025 01:18:46] "GET /api/notifications/recent HTTP/1.1" 200 -
2025-05-19 01:18:46,394 - werkzeug - INFO - ************ - - [19/May/2025 01:18:46] "GET /api/notifications/count HTTP/1.1" 200 -
2025-05-19 01:18:46,415 - werkzeug - INFO - ************ - - [19/May/2025 01:18:46] "[35m[1mGET /favicon.ico HTTP/1.1[0m" 500 -
2025-05-19 01:18:46,517 - werkzeug - INFO - ************ - - [19/May/2025 01:18:46] "GET /reports/sales HTTP/1.1" 200 -
2025-05-19 01:18:46,763 - werkzeug - INFO - ************ - - [19/May/2025 01:18:46] "[36mGET /static/css/nobara-pro.css?v=1.0.0 HTTP/1.1[0m" 304 -
2025-05-19 01:18:46,865 - werkzeug - INFO - ************ - - [19/May/2025 01:18:46] "[36mGET /static/uploads/logo_20250518120852.jpeg HTTP/1.1[0m" 304 -
2025-05-19 01:18:46,935 - werkzeug - INFO - ************ - - [19/May/2025 01:18:46] "[36mGET /static/js/dashboard-manager.js?v=1.0.0 HTTP/1.1[0m" 304 -
2025-05-19 01:18:47,049 - werkzeug - INFO - ************ - - [19/May/2025 01:18:47] "[36mGET /static/js/system-manager.js?v=1.0.0 HTTP/1.1[0m" 304 -
2025-05-19 01:18:47,217 - werkzeug - INFO - ************ - - [19/May/2025 01:18:47] "[36mGET /static/js/notifications-system.js?v=1.0.0 HTTP/1.1[0m" 304 -
2025-05-19 01:18:47,268 - werkzeug - INFO - ************ - - [19/May/2025 01:18:47] "[36mGET /static/css/dashboard-pro.css?v=1.0.1 HTTP/1.1[0m" 304 -
2025-05-19 01:18:47,274 - nobara - ERROR - استثناء: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.
معلومات الطلب: {"url": "http://************:5000/favicon.ico", "method": "GET", "headers": {"Host": "************:5000", "Connection": "keep-alive", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Accept": "image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8", "Referer": "http://************:5000/reports", "Accept-Encoding": "gzip, deflate", "Accept-Language": "en-US,en;q=0.9", "Cookie": "NEXT_LOCALE=en; session=.eJztksFqwzAMhl_F-JJLKLGd2FYeY-y2laJYMklIk-E4p9J3n5u9w04F8f8SQnxC6CFvccF95F32Xw8pcjHJKW1J1vL7aKymU-OpgziNX2rcmZteVPO0zqgv6VjzdOfLJ99_Fsz8wZETr4HFNswcciVG3MW6Ccw5TcORWVR5ygtX8vqs3_A3_D_h17q8fuJ9lH1OB5dqItlLTegdUIsxsrPglA8QAAcXicB5Z0LrS7RGd69u1NqQHZqgFABqyz7GMhZVB6AG6BwZywrQ69A0PliF6FQgUp2xAWKwCJ50cJaIySKbcoXbsXP620bJ5y9x1yli.aCpb5w.1Jv5pyD8PazDruwjt3qLzvBL5O8"}, "args": {}, "form": {}, "cookies": {"NEXT_LOCALE": "en", "session": ".eJztksFqwzAMhl_F-JJLKLGd2FYeY-y2laJYMklIk-E4p9J3n5u9w04F8f8SQnxC6CFvccF95F32Xw8pcjHJKW1J1vL7aKymU-OpgziNX2rcmZteVPO0zqgv6VjzdOfLJ99_Fsz8wZETr4HFNswcciVG3MW6Ccw5TcORWVR5ygtX8vqs3_A3_D_h17q8fuJ9lH1OB5dqItlLTegdUIsxsrPglA8QAAcXicB5Z0LrS7RGd69u1NqQHZqgFABqyz7GMhZVB6AG6BwZywrQ69A0PliF6FQgUp2xAWKwCJ50cJaIySKbcoXbsXP620bJ5y9x1yli.aCpb5w.1Jv5pyD8PazDruwjt3qLzvBL5O8"}, "remote_addr": "************", "user_agent": null}
تتبع الاستثناء:
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\app.py", line 917, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\app.py", line 891, in dispatch_request
    self.raise_routing_exception(req)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\app.py", line 500, in raise_routing_exception
    raise request.routing_exception  # type: ignore[misc]
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\ctx.py", line 362, in match_request
    result = self.url_adapter.match(return_rule=True)  # type: ignore
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\werkzeug\routing\map.py", line 629, in match
    raise NotFound() from None
werkzeug.exceptions.NotFound: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.

2025-05-19 01:18:47,288 - werkzeug - INFO - ************ - - [19/May/2025 01:18:47] "[35m[1mGET /favicon.ico HTTP/1.1[0m" 500 -
2025-05-19 01:18:48,087 - werkzeug - INFO - ************ - - [19/May/2025 01:18:48] "GET /reports/shift-closure HTTP/1.1" 200 -
2025-05-19 01:18:48,195 - werkzeug - INFO - ************ - - [19/May/2025 01:18:48] "[36mGET /static/uploads/logo_20250518120852.jpeg HTTP/1.1[0m" 304 -
2025-05-19 01:18:48,555 - werkzeug - INFO - ************ - - [19/May/2025 01:18:48] "GET /api/notifications/count HTTP/1.1" 200 -
2025-05-19 01:18:48,680 - nobara - ERROR - استثناء: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.
معلومات الطلب: {"url": "http://************:5000/favicon.ico", "method": "GET", "headers": {"Host": "************:5000", "Connection": "keep-alive", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Accept": "image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8", "Referer": "http://************:5000/reports/shift-closure", "Accept-Encoding": "gzip, deflate", "Accept-Language": "en-US,en;q=0.9", "Cookie": "NEXT_LOCALE=en; session=.eJztksFqwzAMhl_F-JJLKLGd2FYeY-y2laJYMklIk-E4p9J3n5u9w04F8f8SQnxC6CFvccF95F32Xw8pcjHJKW1J1vL7aKymU-OpgziNX2rcmZteVPO0zqgv6VjzdOfLJ99_Fsz8wZETr4HFNswcciVG3MW6Ccw5TcORWVR5ygtX8vqs3_A3_D_h17q8fuJ9lH1OB5dqItlLTegdUIsxsrPglA8QAAcXicB5Z0LrS7RGd69u1NqQHZqgFABqyz7GMhZVB6AG6BwZywrQ69A0PliF6FQgUp2xAWKwCJ50cJaIySKbcoXbsXP620bJ5y9x1yli.aCpb5w.1Jv5pyD8PazDruwjt3qLzvBL5O8"}, "args": {}, "form": {}, "cookies": {"NEXT_LOCALE": "en", "session": ".eJztksFqwzAMhl_F-JJLKLGd2FYeY-y2laJYMklIk-E4p9J3n5u9w04F8f8SQnxC6CFvccF95F32Xw8pcjHJKW1J1vL7aKymU-OpgziNX2rcmZteVPO0zqgv6VjzdOfLJ99_Fsz8wZETr4HFNswcciVG3MW6Ccw5TcORWVR5ygtX8vqs3_A3_D_h17q8fuJ9lH1OB5dqItlLTegdUIsxsrPglA8QAAcXicB5Z0LrS7RGd69u1NqQHZqgFABqyz7GMhZVB6AG6BwZywrQ69A0PliF6FQgUp2xAWKwCJ50cJaIySKbcoXbsXP620bJ5y9x1yli.aCpb5w.1Jv5pyD8PazDruwjt3qLzvBL5O8"}, "remote_addr": "************", "user_agent": null}
تتبع الاستثناء:
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\app.py", line 917, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\app.py", line 891, in dispatch_request
    self.raise_routing_exception(req)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\app.py", line 500, in raise_routing_exception
    raise request.routing_exception  # type: ignore[misc]
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\ctx.py", line 362, in match_request
    result = self.url_adapter.match(return_rule=True)  # type: ignore
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\werkzeug\routing\map.py", line 629, in match
    raise NotFound() from None
werkzeug.exceptions.NotFound: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.

2025-05-19 01:18:48,725 - werkzeug - INFO - ************ - - [19/May/2025 01:18:48] "[35m[1mGET /favicon.ico HTTP/1.1[0m" 500 -
2025-05-19 01:18:55,294 - nobara - ERROR - استثناء: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.
معلومات الطلب: {"url": "http://************:5000/favicon.ico", "method": "GET", "headers": {"Host": "************:5000", "Connection": "keep-alive", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Accept": "image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8", "Referer": "http://************:5000/reports", "Accept-Encoding": "gzip, deflate", "Accept-Language": "en-US,en;q=0.9", "Cookie": "NEXT_LOCALE=en; session=.eJztksFqwzAMhl_F-JJLKLGd2FYeY-y2laJYMklIk-E4p9J3n5u9w04F8f8SQnxC6CFvccF95F32Xw8pcjHJKW1J1vL7aKymU-OpgziNX2rcmZteVPO0zqgv6VjzdOfLJ99_Fsz8wZETr4HFNswcciVG3MW6Ccw5TcORWVR5ygtX8vqs3_A3_D_h17q8fuJ9lH1OB5dqItlLTegdUIsxsrPglA8QAAcXicB5Z0LrS7RGd69u1NqQHZqgFABqyz7GMhZVB6AG6BwZywrQ69A0PliF6FQgUp2xAWKwCJ50cJaIySKbcoXbsXP620bJ5y9x1yli.aCpb5w.1Jv5pyD8PazDruwjt3qLzvBL5O8"}, "args": {}, "form": {}, "cookies": {"NEXT_LOCALE": "en", "session": ".eJztksFqwzAMhl_F-JJLKLGd2FYeY-y2laJYMklIk-E4p9J3n5u9w04F8f8SQnxC6CFvccF95F32Xw8pcjHJKW1J1vL7aKymU-OpgziNX2rcmZteVPO0zqgv6VjzdOfLJ99_Fsz8wZETr4HFNswcciVG3MW6Ccw5TcORWVR5ygtX8vqs3_A3_D_h17q8fuJ9lH1OB5dqItlLTegdUIsxsrPglA8QAAcXicB5Z0LrS7RGd69u1NqQHZqgFABqyz7GMhZVB6AG6BwZywrQ69A0PliF6FQgUp2xAWKwCJ50cJaIySKbcoXbsXP620bJ5y9x1yli.aCpb5w.1Jv5pyD8PazDruwjt3qLzvBL5O8"}, "remote_addr": "************", "user_agent": null}
تتبع الاستثناء:
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\app.py", line 917, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\app.py", line 891, in dispatch_request
    self.raise_routing_exception(req)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\app.py", line 500, in raise_routing_exception
    raise request.routing_exception  # type: ignore[misc]
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\ctx.py", line 362, in match_request
    result = self.url_adapter.match(return_rule=True)  # type: ignore
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\werkzeug\routing\map.py", line 629, in match
    raise NotFound() from None
werkzeug.exceptions.NotFound: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.

2025-05-19 01:18:55,308 - werkzeug - INFO - ************ - - [19/May/2025 01:18:55] "[35m[1mGET /favicon.ico HTTP/1.1[0m" 500 -
2025-05-19 01:18:56,501 - nobara - ERROR - استثناء: (sqlite3.OperationalError) no such column: cash_transaction.daily_closure_id
[SQL: SELECT cash_transaction.id AS cash_transaction_id, cash_transaction.cash_register_id AS cash_transaction_cash_register_id, cash_transaction.transaction_type AS cash_transaction_transaction_type, cash_transaction.amount AS cash_transaction_amount, cash_transaction.previous_balance AS cash_transaction_previous_balance, cash_transaction.new_balance AS cash_transaction_new_balance, cash_transaction.reference AS cash_transaction_reference, cash_transaction.notes AS cash_transaction_notes, cash_transaction.created_by AS cash_transaction_created_by, cash_transaction.created_at AS cash_transaction_created_at, cash_transaction.daily_closure_id AS cash_transaction_daily_closure_id, cash_transaction.order_id AS cash_transaction_order_id, cash_transaction.purchase_id AS cash_transaction_purchase_id, cash_transaction.shift_id AS cash_transaction_shift_id 
FROM cash_transaction 
WHERE cash_transaction.created_at BETWEEN ? AND ? ORDER BY cash_transaction.created_at DESC]
[parameters: ('2025-04-19 00:00:00.000000', '2025-05-19 23:59:59.000000')]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
معلومات الطلب: {"url": "http://************:5000/reports/cash-transactions", "method": "GET", "headers": {"Host": "************:5000", "Connection": "keep-alive", "Upgrade-Insecure-Requests": "1", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7", "Referer": "http://************:5000/reports", "Accept-Encoding": "gzip, deflate", "Accept-Language": "en-US,en;q=0.9", "Cookie": "NEXT_LOCALE=en; session=.eJztksFqwzAMhl_F-JJLKLGd2FYeY-y2laJYMklIk-E4p9J3n5u9w04F8f8SQnxC6CFvccF95F32Xw8pcjHJKW1J1vL7aKymU-OpgziNX2rcmZteVPO0zqgv6VjzdOfLJ99_Fsz8wZETr4HFNswcciVG3MW6Ccw5TcORWVR5ygtX8vqs3_A3_D_h17q8fuJ9lH1OB5dqItlLTegdUIsxsrPglA8QAAcXicB5Z0LrS7RGd69u1NqQHZqgFABqyz7GMhZVB6AG6BwZywrQ69A0PliF6FQgUp2xAWKwCJ50cJaIySKbcoXbsXP620bJ5y9x1yli.aCpb5w.1Jv5pyD8PazDruwjt3qLzvBL5O8"}, "args": {}, "form": {}, "cookies": {"NEXT_LOCALE": "en", "session": ".eJztksFqwzAMhl_F-JJLKLGd2FYeY-y2laJYMklIk-E4p9J3n5u9w04F8f8SQnxC6CFvccF95F32Xw8pcjHJKW1J1vL7aKymU-OpgziNX2rcmZteVPO0zqgv6VjzdOfLJ99_Fsz8wZETr4HFNswcciVG3MW6Ccw5TcORWVR5ygtX8vqs3_A3_D_h17q8fuJ9lH1OB5dqItlLTegdUIsxsrPglA8QAAcXicB5Z0LrS7RGd69u1NqQHZqgFABqyz7GMhZVB6AG6BwZywrQ69A0PliF6FQgUp2xAWKwCJ50cJaIySKbcoXbsXP620bJ5y9x1yli.aCpb5w.1Jv5pyD8PazDruwjt3qLzvBL5O8"}, "remote_addr": "************", "user_agent": null}
تتبع الاستثناء:
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1964, in _exec_single_context
    self.dialect.do_execute(
    ~~~~~~~~~~~~~~~~~~~~~~~^
        cursor, str_statement, effective_parameters, context
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\sqlalchemy\engine\default.py", line 945, in do_execute
    cursor.execute(statement, parameters)
    ~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
sqlite3.OperationalError: no such column: cash_transaction.daily_closure_id

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\app.py", line 917, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\app.py", line 902, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)  # type: ignore[no-any-return]
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask_login\utils.py", line 290, in decorated_view
    return current_app.ensure_sync(func)(*args, **kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\routes\reports.py", line 1561, in cash_transactions_report
    transactions = transactions_query.order_by(desc(CashTransaction.created_at)).all()
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\sqlalchemy\orm\query.py", line 2704, in all
    return self._iter().all()  # type: ignore
           ~~~~~~~~~~^^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\sqlalchemy\orm\query.py", line 2858, in _iter
    result: Union[ScalarResult[_T], Result[_T]] = self.session.execute(
                                                  ~~~~~~~~~~~~~~~~~~~~^
        statement,
        ^^^^^^^^^^
        params,
        ^^^^^^^
        execution_options={"_sa_orm_load_options": self.load_options},
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\sqlalchemy\orm\session.py", line 2365, in execute
    return self._execute_internal(
           ~~~~~~~~~~~~~~~~~~~~~~^
        statement,
        ^^^^^^^^^^
    ...<4 lines>...
        _add_event=_add_event,
        ^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\sqlalchemy\orm\session.py", line 2251, in _execute_internal
    result: Result[Any] = compile_state_cls.orm_execute_statement(
                          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        self,
        ^^^^^
    ...<4 lines>...
        conn,
        ^^^^^
    )
    ^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\sqlalchemy\orm\context.py", line 306, in orm_execute_statement
    result = conn.execute(
        statement, params or {}, execution_options=execution_options
    )
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1416, in execute
    return meth(
        self,
        distilled_parameters,
        execution_options or NO_OPTIONS,
    )
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\sqlalchemy\sql\elements.py", line 523, in _execute_on_connection
    return connection._execute_clauseelement(
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        self, distilled_params, execution_options
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1638, in _execute_clauseelement
    ret = self._execute_context(
        dialect,
    ...<8 lines>...
        cache_hit=cache_hit,
    )
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1843, in _execute_context
    return self._exec_single_context(
           ~~~~~~~~~~~~~~~~~~~~~~~~~^
        dialect, context, statement, parameters
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1983, in _exec_single_context
    self._handle_dbapi_exception(
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        e, str_statement, effective_parameters, cursor, context
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 2352, in _handle_dbapi_exception
    raise sqlalchemy_exception.with_traceback(exc_info[2]) from e
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1964, in _exec_single_context
    self.dialect.do_execute(
    ~~~~~~~~~~~~~~~~~~~~~~~^
        cursor, str_statement, effective_parameters, context
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\sqlalchemy\engine\default.py", line 945, in do_execute
    cursor.execute(statement, parameters)
    ~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
sqlalchemy.exc.OperationalError: (sqlite3.OperationalError) no such column: cash_transaction.daily_closure_id
[SQL: SELECT cash_transaction.id AS cash_transaction_id, cash_transaction.cash_register_id AS cash_transaction_cash_register_id, cash_transaction.transaction_type AS cash_transaction_transaction_type, cash_transaction.amount AS cash_transaction_amount, cash_transaction.previous_balance AS cash_transaction_previous_balance, cash_transaction.new_balance AS cash_transaction_new_balance, cash_transaction.reference AS cash_transaction_reference, cash_transaction.notes AS cash_transaction_notes, cash_transaction.created_by AS cash_transaction_created_by, cash_transaction.created_at AS cash_transaction_created_at, cash_transaction.daily_closure_id AS cash_transaction_daily_closure_id, cash_transaction.order_id AS cash_transaction_order_id, cash_transaction.purchase_id AS cash_transaction_purchase_id, cash_transaction.shift_id AS cash_transaction_shift_id 
FROM cash_transaction 
WHERE cash_transaction.created_at BETWEEN ? AND ? ORDER BY cash_transaction.created_at DESC]
[parameters: ('2025-04-19 00:00:00.000000', '2025-05-19 23:59:59.000000')]
(Background on this error at: https://sqlalche.me/e/20/e3q8)

2025-05-19 01:18:56,603 - werkzeug - INFO - ************ - - [19/May/2025 01:18:56] "[35m[1mGET /reports/cash-transactions HTTP/1.1[0m" 500 -
2025-05-19 01:18:56,709 - nobara - ERROR - استثناء: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.
معلومات الطلب: {"url": "http://************:5000/favicon.ico", "method": "GET", "headers": {"Host": "************:5000", "Connection": "keep-alive", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Accept": "image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8", "Referer": "http://************:5000/reports/cash-transactions", "Accept-Encoding": "gzip, deflate", "Accept-Language": "en-US,en;q=0.9", "Cookie": "NEXT_LOCALE=en; session=.eJztksFqwzAMhl_F-JJLKLGd2FYeY-y2laJYMklIk-E4p9J3n5u9w04F8f8SQnxC6CFvccF95F32Xw8pcjHJKW1J1vL7aKymU-OpgziNX2rcmZteVPO0zqgv6VjzdOfLJ99_Fsz8wZETr4HFNswcciVG3MW6Ccw5TcORWVR5ygtX8vqs3_A3_D_h17q8fuJ9lH1OB5dqItlLTegdUIsxsrPglA8QAAcXicB5Z0LrS7RGd69u1NqQHZqgFABqyz7GMhZVB6AG6BwZywrQ69A0PliF6FQgUp2xAWKwCJ50cJaIySKbcoXbsXP620bJ5y9x1yli.aCpb5w.1Jv5pyD8PazDruwjt3qLzvBL5O8"}, "args": {}, "form": {}, "cookies": {"NEXT_LOCALE": "en", "session": ".eJztksFqwzAMhl_F-JJLKLGd2FYeY-y2laJYMklIk-E4p9J3n5u9w04F8f8SQnxC6CFvccF95F32Xw8pcjHJKW1J1vL7aKymU-OpgziNX2rcmZteVPO0zqgv6VjzdOfLJ99_Fsz8wZETr4HFNswcciVG3MW6Ccw5TcORWVR5ygtX8vqs3_A3_D_h17q8fuJ9lH1OB5dqItlLTegdUIsxsrPglA8QAAcXicB5Z0LrS7RGd69u1NqQHZqgFABqyz7GMhZVB6AG6BwZywrQ69A0PliF6FQgUp2xAWKwCJ50cJaIySKbcoXbsXP620bJ5y9x1yli.aCpb5w.1Jv5pyD8PazDruwjt3qLzvBL5O8"}, "remote_addr": "************", "user_agent": null}
تتبع الاستثناء:
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\app.py", line 917, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\app.py", line 891, in dispatch_request
    self.raise_routing_exception(req)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\app.py", line 500, in raise_routing_exception
    raise request.routing_exception  # type: ignore[misc]
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\ctx.py", line 362, in match_request
    result = self.url_adapter.match(return_rule=True)  # type: ignore
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\werkzeug\routing\map.py", line 629, in match
    raise NotFound() from None
werkzeug.exceptions.NotFound: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.

2025-05-19 01:18:56,716 - werkzeug - INFO - ************ - - [19/May/2025 01:18:56] "[35m[1mGET /favicon.ico HTTP/1.1[0m" 500 -
2025-05-19 01:18:59,681 - nobara - ERROR - استثناء: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.
معلومات الطلب: {"url": "http://************:5000/favicon.ico", "method": "GET", "headers": {"Host": "************:5000", "Connection": "keep-alive", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Accept": "image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8", "Referer": "http://************:5000/reports", "Accept-Encoding": "gzip, deflate", "Accept-Language": "en-US,en;q=0.9", "Cookie": "NEXT_LOCALE=en; session=.eJztksFqwzAMhl_F-JJLKLGd2FYeY-y2laJYMklIk-E4p9J3n5u9w04F8f8SQnxC6CFvccF95F32Xw8pcjHJKW1J1vL7aKymU-OpgziNX2rcmZteVPO0zqgv6VjzdOfLJ99_Fsz8wZETr4HFNswcciVG3MW6Ccw5TcORWVR5ygtX8vqs3_A3_D_h17q8fuJ9lH1OB5dqItlLTegdUIsxsrPglA8QAAcXicB5Z0LrS7RGd69u1NqQHZqgFABqyz7GMhZVB6AG6BwZywrQ69A0PliF6FQgUp2xAWKwCJ50cJaIySKbcoXbsXP620bJ5y9x1yli.aCpb5w.1Jv5pyD8PazDruwjt3qLzvBL5O8"}, "args": {}, "form": {}, "cookies": {"NEXT_LOCALE": "en", "session": ".eJztksFqwzAMhl_F-JJLKLGd2FYeY-y2laJYMklIk-E4p9J3n5u9w04F8f8SQnxC6CFvccF95F32Xw8pcjHJKW1J1vL7aKymU-OpgziNX2rcmZteVPO0zqgv6VjzdOfLJ99_Fsz8wZETr4HFNswcciVG3MW6Ccw5TcORWVR5ygtX8vqs3_A3_D_h17q8fuJ9lH1OB5dqItlLTegdUIsxsrPglA8QAAcXicB5Z0LrS7RGd69u1NqQHZqgFABqyz7GMhZVB6AG6BwZywrQ69A0PliF6FQgUp2xAWKwCJ50cJaIySKbcoXbsXP620bJ5y9x1yli.aCpb5w.1Jv5pyD8PazDruwjt3qLzvBL5O8"}, "remote_addr": "************", "user_agent": null}
تتبع الاستثناء:
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\app.py", line 917, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\app.py", line 891, in dispatch_request
    self.raise_routing_exception(req)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\app.py", line 500, in raise_routing_exception
    raise request.routing_exception  # type: ignore[misc]
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\ctx.py", line 362, in match_request
    result = self.url_adapter.match(return_rule=True)  # type: ignore
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\werkzeug\routing\map.py", line 629, in match
    raise NotFound() from None
werkzeug.exceptions.NotFound: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.

2025-05-19 01:18:59,795 - werkzeug - INFO - ************ - - [19/May/2025 01:18:59] "[35m[1mGET /favicon.ico HTTP/1.1[0m" 500 -
2025-05-19 01:19:01,074 - werkzeug - INFO - ************ - - [19/May/2025 01:19:01] "GET /reports/daily-closure HTTP/1.1" 200 -
2025-05-19 01:19:01,163 - werkzeug - INFO - ************ - - [19/May/2025 01:19:01] "[36mGET /static/img/nobara-logo.svg HTTP/1.1[0m" 304 -
2025-05-19 01:19:01,490 - werkzeug - INFO - ************ - - [19/May/2025 01:19:01] "GET /api/notifications/count HTTP/1.1" 200 -
2025-05-19 01:19:01,622 - nobara - ERROR - استثناء: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.
معلومات الطلب: {"url": "http://************:5000/favicon.ico", "method": "GET", "headers": {"Host": "************:5000", "Connection": "keep-alive", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Accept": "image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8", "Referer": "http://************:5000/reports/daily-closure", "Accept-Encoding": "gzip, deflate", "Accept-Language": "en-US,en;q=0.9", "Cookie": "NEXT_LOCALE=en; session=.eJztksFqwzAMhl_F-JJLKLGd2FYeY-y2laJYMklIk-E4p9J3n5u9w04F8f8SQnxC6CFvccF95F32Xw8pcjHJKW1J1vL7aKymU-OpgziNX2rcmZteVPO0zqgv6VjzdOfLJ99_Fsz8wZETr4HFNswcciVG3MW6Ccw5TcORWVR5ygtX8vqs3_A3_D_h17q8fuJ9lH1OB5dqItlLTegdUIsxsrPglA8QAAcXicB5Z0LrS7RGd69u1NqQHZqgFABqyz7GMhZVB6AG6BwZywrQ69A0PliF6FQgUp2xAWKwCJ50cJaIySKbcoXbsXP620bJ5y9x1yli.aCpb5w.1Jv5pyD8PazDruwjt3qLzvBL5O8"}, "args": {}, "form": {}, "cookies": {"NEXT_LOCALE": "en", "session": ".eJztksFqwzAMhl_F-JJLKLGd2FYeY-y2laJYMklIk-E4p9J3n5u9w04F8f8SQnxC6CFvccF95F32Xw8pcjHJKW1J1vL7aKymU-OpgziNX2rcmZteVPO0zqgv6VjzdOfLJ99_Fsz8wZETr4HFNswcciVG3MW6Ccw5TcORWVR5ygtX8vqs3_A3_D_h17q8fuJ9lH1OB5dqItlLTegdUIsxsrPglA8QAAcXicB5Z0LrS7RGd69u1NqQHZqgFABqyz7GMhZVB6AG6BwZywrQ69A0PliF6FQgUp2xAWKwCJ50cJaIySKbcoXbsXP620bJ5y9x1yli.aCpb5w.1Jv5pyD8PazDruwjt3qLzvBL5O8"}, "remote_addr": "************", "user_agent": null}
تتبع الاستثناء:
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\app.py", line 917, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\app.py", line 891, in dispatch_request
    self.raise_routing_exception(req)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\app.py", line 500, in raise_routing_exception
    raise request.routing_exception  # type: ignore[misc]
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\ctx.py", line 362, in match_request
    result = self.url_adapter.match(return_rule=True)  # type: ignore
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\werkzeug\routing\map.py", line 629, in match
    raise NotFound() from None
werkzeug.exceptions.NotFound: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.

2025-05-19 01:19:01,663 - werkzeug - INFO - ************ - - [19/May/2025 01:19:01] "[35m[1mGET /favicon.ico HTTP/1.1[0m" 500 -
2025-05-19 01:19:09,580 - nobara - ERROR - استثناء: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.
معلومات الطلب: {"url": "http://************:5000/favicon.ico", "method": "GET", "headers": {"Host": "************:5000", "Connection": "keep-alive", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Accept": "image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8", "Referer": "http://************:5000/reports", "Accept-Encoding": "gzip, deflate", "Accept-Language": "en-US,en;q=0.9", "Cookie": "NEXT_LOCALE=en; session=.eJztksFqwzAMhl_F-JJLKLGd2FYeY-y2laJYMklIk-E4p9J3n5u9w04F8f8SQnxC6CFvccF95F32Xw8pcjHJKW1J1vL7aKymU-OpgziNX2rcmZteVPO0zqgv6VjzdOfLJ99_Fsz8wZETr4HFNswcciVG3MW6Ccw5TcORWVR5ygtX8vqs3_A3_D_h17q8fuJ9lH1OB5dqItlLTegdUIsxsrPglA8QAAcXicB5Z0LrS7RGd69u1NqQHZqgFABqyz7GMhZVB6AG6BwZywrQ69A0PliF6FQgUp2xAWKwCJ50cJaIySKbcoXbsXP620bJ5y9x1yli.aCpb5w.1Jv5pyD8PazDruwjt3qLzvBL5O8"}, "args": {}, "form": {}, "cookies": {"NEXT_LOCALE": "en", "session": ".eJztksFqwzAMhl_F-JJLKLGd2FYeY-y2laJYMklIk-E4p9J3n5u9w04F8f8SQnxC6CFvccF95F32Xw8pcjHJKW1J1vL7aKymU-OpgziNX2rcmZteVPO0zqgv6VjzdOfLJ99_Fsz8wZETr4HFNswcciVG3MW6Ccw5TcORWVR5ygtX8vqs3_A3_D_h17q8fuJ9lH1OB5dqItlLTegdUIsxsrPglA8QAAcXicB5Z0LrS7RGd69u1NqQHZqgFABqyz7GMhZVB6AG6BwZywrQ69A0PliF6FQgUp2xAWKwCJ50cJaIySKbcoXbsXP620bJ5y9x1yli.aCpb5w.1Jv5pyD8PazDruwjt3qLzvBL5O8"}, "remote_addr": "************", "user_agent": null}
تتبع الاستثناء:
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\app.py", line 917, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\app.py", line 891, in dispatch_request
    self.raise_routing_exception(req)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\app.py", line 500, in raise_routing_exception
    raise request.routing_exception  # type: ignore[misc]
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\ctx.py", line 362, in match_request
    result = self.url_adapter.match(return_rule=True)  # type: ignore
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\werkzeug\routing\map.py", line 629, in match
    raise NotFound() from None
werkzeug.exceptions.NotFound: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.

2025-05-19 01:19:09,584 - werkzeug - INFO - ************ - - [19/May/2025 01:19:09] "[35m[1mGET /favicon.ico HTTP/1.1[0m" 500 -
2025-05-19 01:19:13,196 - nobara - ERROR - استثناء: warehouses/warehouses.html
معلومات الطلب: {"url": "http://************:5000/warehouses", "method": "GET", "headers": {"Host": "************:5000", "Connection": "keep-alive", "Upgrade-Insecure-Requests": "1", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7", "Referer": "http://************:5000/reports", "Accept-Encoding": "gzip, deflate", "Accept-Language": "en-US,en;q=0.9", "Cookie": "NEXT_LOCALE=en; session=.eJztksFqwzAMhl_F-JJLKLGd2FYeY-y2laJYMklIk-E4p9J3n5u9w04F8f8SQnxC6CFvccF95F32Xw8pcjHJKW1J1vL7aKymU-OpgziNX2rcmZteVPO0zqgv6VjzdOfLJ99_Fsz8wZETr4HFNswcciVG3MW6Ccw5TcORWVR5ygtX8vqs3_A3_D_h17q8fuJ9lH1OB5dqItlLTegdUIsxsrPglA8QAAcXicB5Z0LrS7RGd69u1NqQHZqgFABqyz7GMhZVB6AG6BwZywrQ69A0PliF6FQgUp2xAWKwCJ50cJaIySKbcoXbsXP620bJ5y9x1yli.aCpb5w.1Jv5pyD8PazDruwjt3qLzvBL5O8"}, "args": {}, "form": {}, "cookies": {"NEXT_LOCALE": "en", "session": ".eJztksFqwzAMhl_F-JJLKLGd2FYeY-y2laJYMklIk-E4p9J3n5u9w04F8f8SQnxC6CFvccF95F32Xw8pcjHJKW1J1vL7aKymU-OpgziNX2rcmZteVPO0zqgv6VjzdOfLJ99_Fsz8wZETr4HFNswcciVG3MW6Ccw5TcORWVR5ygtX8vqs3_A3_D_h17q8fuJ9lH1OB5dqItlLTegdUIsxsrPglA8QAAcXicB5Z0LrS7RGd69u1NqQHZqgFABqyz7GMhZVB6AG6BwZywrQ69A0PliF6FQgUp2xAWKwCJ50cJaIySKbcoXbsXP620bJ5y9x1yli.aCpb5w.1Jv5pyD8PazDruwjt3qLzvBL5O8"}, "remote_addr": "************", "user_agent": null}
تتبع الاستثناء:
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\app.py", line 917, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\app.py", line 902, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)  # type: ignore[no-any-return]
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask_login\utils.py", line 290, in decorated_view
    return current_app.ensure_sync(func)(*args, **kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\routes\warehouses.py", line 45, in index
    return render_template('warehouses/warehouses.html',
                          warehouses=warehouses,
    ...<2 lines>...
                          status=status,
                          stats=stats)
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\templating.py", line 149, in render_template
    template = app.jinja_env.get_or_select_template(template_name_or_list)
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\jinja2\environment.py", line 1087, in get_or_select_template
    return self.get_template(template_name_or_list, parent, globals)
           ~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\jinja2\environment.py", line 1016, in get_template
    return self._load_template(name, globals)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\jinja2\environment.py", line 975, in _load_template
    template = self.loader.load(self, name, self.make_globals(globals))
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\jinja2\loaders.py", line 126, in load
    source, filename, uptodate = self.get_source(environment, name)
                                 ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\templating.py", line 65, in get_source
    return self._get_source_fast(environment, template)
           ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\templating.py", line 99, in _get_source_fast
    raise TemplateNotFound(template)
jinja2.exceptions.TemplateNotFound: warehouses/warehouses.html

2025-05-19 01:19:13,203 - werkzeug - INFO - ************ - - [19/May/2025 01:19:13] "[35m[1mGET /warehouses HTTP/1.1[0m" 500 -
2025-05-19 01:19:13,348 - nobara - ERROR - استثناء: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.
معلومات الطلب: {"url": "http://************:5000/favicon.ico", "method": "GET", "headers": {"Host": "************:5000", "Connection": "keep-alive", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Accept": "image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8", "Referer": "http://************:5000/warehouses", "Accept-Encoding": "gzip, deflate", "Accept-Language": "en-US,en;q=0.9", "Cookie": "NEXT_LOCALE=en; session=.eJztksFqwzAMhl_F-JJLKLGd2FYeY-y2laJYMklIk-E4p9J3n5u9w04F8f8SQnxC6CFvccF95F32Xw8pcjHJKW1J1vL7aKymU-OpgziNX2rcmZteVPO0zqgv6VjzdOfLJ99_Fsz8wZETr4HFNswcciVG3MW6Ccw5TcORWVR5ygtX8vqs3_A3_D_h17q8fuJ9lH1OB5dqItlLTegdUIsxsrPglA8QAAcXicB5Z0LrS7RGd69u1NqQHZqgFABqyz7GMhZVB6AG6BwZywrQ69A0PliF6FQgUp2xAWKwCJ50cJaIySKbcoXbsXP620bJ5y9x1yli.aCpb5w.1Jv5pyD8PazDruwjt3qLzvBL5O8"}, "args": {}, "form": {}, "cookies": {"NEXT_LOCALE": "en", "session": ".eJztksFqwzAMhl_F-JJLKLGd2FYeY-y2laJYMklIk-E4p9J3n5u9w04F8f8SQnxC6CFvccF95F32Xw8pcjHJKW1J1vL7aKymU-OpgziNX2rcmZteVPO0zqgv6VjzdOfLJ99_Fsz8wZETr4HFNswcciVG3MW6Ccw5TcORWVR5ygtX8vqs3_A3_D_h17q8fuJ9lH1OB5dqItlLTegdUIsxsrPglA8QAAcXicB5Z0LrS7RGd69u1NqQHZqgFABqyz7GMhZVB6AG6BwZywrQ69A0PliF6FQgUp2xAWKwCJ50cJaIySKbcoXbsXP620bJ5y9x1yli.aCpb5w.1Jv5pyD8PazDruwjt3qLzvBL5O8"}, "remote_addr": "************", "user_agent": null}
تتبع الاستثناء:
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\app.py", line 917, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\app.py", line 891, in dispatch_request
    self.raise_routing_exception(req)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\app.py", line 500, in raise_routing_exception
    raise request.routing_exception  # type: ignore[misc]
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\ctx.py", line 362, in match_request
    result = self.url_adapter.match(return_rule=True)  # type: ignore
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\werkzeug\routing\map.py", line 629, in match
    raise NotFound() from None
werkzeug.exceptions.NotFound: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.

2025-05-19 01:19:13,353 - werkzeug - INFO - ************ - - [19/May/2025 01:19:13] "[35m[1mGET /favicon.ico HTTP/1.1[0m" 500 -
2025-05-19 01:19:15,740 - nobara - ERROR - استثناء: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.
معلومات الطلب: {"url": "http://************:5000/favicon.ico", "method": "GET", "headers": {"Host": "************:5000", "Connection": "keep-alive", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Accept": "image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8", "Referer": "http://************:5000/reports", "Accept-Encoding": "gzip, deflate", "Accept-Language": "en-US,en;q=0.9", "Cookie": "NEXT_LOCALE=en; session=.eJztksFqwzAMhl_F-JJLKLGd2FYeY-y2laJYMklIk-E4p9J3n5u9w04F8f8SQnxC6CFvccF95F32Xw8pcjHJKW1J1vL7aKymU-OpgziNX2rcmZteVPO0zqgv6VjzdOfLJ99_Fsz8wZETr4HFNswcciVG3MW6Ccw5TcORWVR5ygtX8vqs3_A3_D_h17q8fuJ9lH1OB5dqItlLTegdUIsxsrPglA8QAAcXicB5Z0LrS7RGd69u1NqQHZqgFABqyz7GMhZVB6AG6BwZywrQ69A0PliF6FQgUp2xAWKwCJ50cJaIySKbcoXbsXP620bJ5y9x1yli.aCpb5w.1Jv5pyD8PazDruwjt3qLzvBL5O8"}, "args": {}, "form": {}, "cookies": {"NEXT_LOCALE": "en", "session": ".eJztksFqwzAMhl_F-JJLKLGd2FYeY-y2laJYMklIk-E4p9J3n5u9w04F8f8SQnxC6CFvccF95F32Xw8pcjHJKW1J1vL7aKymU-OpgziNX2rcmZteVPO0zqgv6VjzdOfLJ99_Fsz8wZETr4HFNswcciVG3MW6Ccw5TcORWVR5ygtX8vqs3_A3_D_h17q8fuJ9lH1OB5dqItlLTegdUIsxsrPglA8QAAcXicB5Z0LrS7RGd69u1NqQHZqgFABqyz7GMhZVB6AG6BwZywrQ69A0PliF6FQgUp2xAWKwCJ50cJaIySKbcoXbsXP620bJ5y9x1yli.aCpb5w.1Jv5pyD8PazDruwjt3qLzvBL5O8"}, "remote_addr": "************", "user_agent": null}
تتبع الاستثناء:
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\app.py", line 917, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\app.py", line 891, in dispatch_request
    self.raise_routing_exception(req)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\app.py", line 500, in raise_routing_exception
    raise request.routing_exception  # type: ignore[misc]
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\ctx.py", line 362, in match_request
    result = self.url_adapter.match(return_rule=True)  # type: ignore
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\werkzeug\routing\map.py", line 629, in match
    raise NotFound() from None
werkzeug.exceptions.NotFound: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.

2025-05-19 01:19:15,893 - werkzeug - INFO - ************ - - [19/May/2025 01:19:15] "[35m[1mGET /favicon.ico HTTP/1.1[0m" 500 -
2025-05-19 01:19:17,081 - werkzeug - INFO - ************ - - [19/May/2025 01:19:17] "[32mGET /settings HTTP/1.1[0m" 302 -
2025-05-19 01:19:17,121 - werkzeug - INFO - ************ - - [19/May/2025 01:19:17] "GET /home HTTP/1.1" 200 -
2025-05-19 01:19:17,189 - werkzeug - INFO - ************ - - [19/May/2025 01:19:17] "[36mGET /static/css/nobara-pro.css?v=1.0.0 HTTP/1.1[0m" 304 -
2025-05-19 01:19:17,225 - werkzeug - INFO - ************ - - [19/May/2025 01:19:17] "GET /static/css/dashboard-pro.css?v=1.0.1 HTTP/1.1" 200 -
2025-05-19 01:19:17,229 - werkzeug - INFO - ************ - - [19/May/2025 01:19:17] "GET /static/js/notifications-system.js?v=1.0.0 HTTP/1.1" 200 -
2025-05-19 01:19:17,238 - werkzeug - INFO - ************ - - [19/May/2025 01:19:17] "GET /static/js/system-manager.js?v=1.0.0 HTTP/1.1" 200 -
2025-05-19 01:19:17,242 - werkzeug - INFO - ************ - - [19/May/2025 01:19:17] "[36mGET /static/js/dashboard-manager.js?v=1.0.0 HTTP/1.1[0m" 304 -
2025-05-19 01:19:17,244 - werkzeug - INFO - ************ - - [19/May/2025 01:19:17] "[36mGET /static/uploads/logo_20250518120852.jpeg HTTP/1.1[0m" 304 -
2025-05-19 01:19:17,438 - werkzeug - INFO - ************ - - [19/May/2025 01:19:17] "GET /static/js/dashboard-pro.js?v=1.0.0 HTTP/1.1" 200 -
2025-05-19 01:19:18,543 - werkzeug - INFO - ************ - - [19/May/2025 01:19:18] "GET /api/settings HTTP/1.1" 200 -
2025-05-19 01:19:18,677 - werkzeug - INFO - ************ - - [19/May/2025 01:19:18] "GET /api/notifications/count HTTP/1.1" 200 -
2025-05-19 01:19:18,693 - werkzeug - INFO - ************ - - [19/May/2025 01:19:18] "GET /api/category-distribution HTTP/1.1" 200 -
2025-05-19 01:19:18,711 - werkzeug - INFO - ************ - - [19/May/2025 01:19:18] "GET /api/sales-chart-data?days=30 HTTP/1.1" 200 -
2025-05-19 01:19:18,835 - werkzeug - INFO - ************ - - [19/May/2025 01:19:18] "GET /api/notifications/recent HTTP/1.1" 200 -
2025-05-19 01:19:18,839 - nobara - ERROR - استثناء: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.
معلومات الطلب: {"url": "http://************:5000/favicon.ico", "method": "GET", "headers": {"Host": "************:5000", "Connection": "keep-alive", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Accept": "image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8", "Referer": "http://************:5000/home", "Accept-Encoding": "gzip, deflate", "Accept-Language": "en-US,en;q=0.9", "Cookie": "NEXT_LOCALE=en; session=.eJztksFqwzAMhl_F-JJLKLGd2FYeY-y2laJYMklIk-E4p9J3n5u9w04F8f8SQnxC6CFvccF95F32Xw8pcjHJKW1J1vL7aKymU-OpgziNX2rcmZteVPO0zqgv6VjzdOfLJ99_Fsz8wZETr4HFNswcciVG3MW6Ccw5TcORWVR5ygtX8vqs3_A3_D_h17q8fuJ9lH1OB5dqItlLTegdUIsxsrPglA8QAAcXicB5Z0LrS7RGd69u1NqQHZqgFABqyz7GMhZVB6AG6BwZywrQ69A0PliF6FQgUp2xAWKwCJ50cJaIySKbcoXbsXP620bJ5y9x1yli.aCpb5w.1Jv5pyD8PazDruwjt3qLzvBL5O8"}, "args": {}, "form": {}, "cookies": {"NEXT_LOCALE": "en", "session": ".eJztksFqwzAMhl_F-JJLKLGd2FYeY-y2laJYMklIk-E4p9J3n5u9w04F8f8SQnxC6CFvccF95F32Xw8pcjHJKW1J1vL7aKymU-OpgziNX2rcmZteVPO0zqgv6VjzdOfLJ99_Fsz8wZETr4HFNswcciVG3MW6Ccw5TcORWVR5ygtX8vqs3_A3_D_h17q8fuJ9lH1OB5dqItlLTegdUIsxsrPglA8QAAcXicB5Z0LrS7RGd69u1NqQHZqgFABqyz7GMhZVB6AG6BwZywrQ69A0PliF6FQgUp2xAWKwCJ50cJaIySKbcoXbsXP620bJ5y9x1yli.aCpb5w.1Jv5pyD8PazDruwjt3qLzvBL5O8"}, "remote_addr": "************", "user_agent": null}
تتبع الاستثناء:
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\app.py", line 917, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\app.py", line 891, in dispatch_request
    self.raise_routing_exception(req)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\app.py", line 500, in raise_routing_exception
    raise request.routing_exception  # type: ignore[misc]
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\ctx.py", line 362, in match_request
    result = self.url_adapter.match(return_rule=True)  # type: ignore
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\werkzeug\routing\map.py", line 629, in match
    raise NotFound() from None
werkzeug.exceptions.NotFound: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.

2025-05-19 01:19:18,849 - werkzeug - INFO - ************ - - [19/May/2025 01:19:18] "GET /api/settings HTTP/1.1" 200 -
2025-05-19 01:19:18,893 - werkzeug - INFO - ************ - - [19/May/2025 01:19:18] "[35m[1mGET /favicon.ico HTTP/1.1[0m" 500 -
2025-05-19 01:19:18,926 - werkzeug - INFO - ************ - - [19/May/2025 01:19:18] "GET /api/notifications/recent HTTP/1.1" 200 -
2025-05-19 01:19:18,956 - werkzeug - INFO - ************ - - [19/May/2025 01:19:18] "GET /api/notifications/count HTTP/1.1" 200 -
2025-05-19 01:19:19,011 - werkzeug - INFO - ************ - - [19/May/2025 01:19:19] "GET /api/dashboard HTTP/1.1" 200 -
2025-05-19 01:19:19,019 - werkzeug - INFO - ************ - - [19/May/2025 01:19:19] "GET /api/notifications/count HTTP/1.1" 200 -
2025-05-19 01:19:19,195 - werkzeug - INFO - ************ - - [19/May/2025 01:19:19] "GET /api/dashboard HTTP/1.1" 200 -
2025-05-19 01:19:48,149 - werkzeug - INFO - ************ - - [19/May/2025 01:19:48] "GET /api/notifications/recent HTTP/1.1" 200 -
2025-05-19 01:19:48,162 - werkzeug - INFO - ************ - - [19/May/2025 01:19:48] "GET /api/notifications/recent HTTP/1.1" 200 -
2025-05-19 01:20:18,165 - werkzeug - INFO - ************ - - [19/May/2025 01:20:18] "GET /api/notifications/recent HTTP/1.1" 200 -
2025-05-19 01:20:18,184 - werkzeug - INFO - ************ - - [19/May/2025 01:20:18] "GET /api/notifications/recent HTTP/1.1" 200 -
2025-05-19 01:20:19,157 - werkzeug - INFO - ************ - - [19/May/2025 01:20:19] "GET /api/notifications/count HTTP/1.1" 200 -
2025-05-19 01:20:19,208 - werkzeug - INFO - ************ - - [19/May/2025 01:20:19] "GET /api/notifications/count HTTP/1.1" 200 -
2025-05-19 01:20:48,150 - werkzeug - INFO - ************ - - [19/May/2025 01:20:48] "GET /api/notifications/recent HTTP/1.1" 200 -
2025-05-19 01:20:48,167 - werkzeug - INFO - ************ - - [19/May/2025 01:20:48] "GET /api/notifications/recent HTTP/1.1" 200 -
2025-05-19 01:21:18,158 - werkzeug - INFO - ************ - - [19/May/2025 01:21:18] "GET /api/notifications/recent HTTP/1.1" 200 -
2025-05-19 01:21:18,180 - werkzeug - INFO - ************ - - [19/May/2025 01:21:18] "GET /api/notifications/recent HTTP/1.1" 200 -
2025-05-19 01:21:19,149 - werkzeug - INFO - ************ - - [19/May/2025 01:21:19] "GET /api/notifications/count HTTP/1.1" 200 -
2025-05-19 01:21:19,176 - werkzeug - INFO - ************ - - [19/May/2025 01:21:19] "GET /api/notifications/count HTTP/1.1" 200 -
2025-05-19 01:21:48,157 - werkzeug - INFO - ************ - - [19/May/2025 01:21:48] "GET /api/notifications/recent HTTP/1.1" 200 -
2025-05-19 01:21:48,171 - werkzeug - INFO - ************ - - [19/May/2025 01:21:48] "GET /api/notifications/recent HTTP/1.1" 200 -
2025-05-19 01:22:18,157 - werkzeug - INFO - ************ - - [19/May/2025 01:22:18] "GET /api/notifications/recent HTTP/1.1" 200 -
2025-05-19 01:22:18,176 - werkzeug - INFO - ************ - - [19/May/2025 01:22:18] "GET /api/notifications/recent HTTP/1.1" 200 -
2025-05-19 01:22:19,264 - werkzeug - INFO - ************ - - [19/May/2025 01:22:19] "GET /api/notifications/count HTTP/1.1" 200 -
2025-05-19 01:22:19,303 - werkzeug - INFO - ************ - - [19/May/2025 01:22:19] "GET /api/notifications/count HTTP/1.1" 200 -
2025-05-19 01:22:48,158 - werkzeug - INFO - ************ - - [19/May/2025 01:22:48] "GET /api/notifications/recent HTTP/1.1" 200 -
2025-05-19 01:22:48,172 - werkzeug - INFO - ************ - - [19/May/2025 01:22:48] "GET /api/notifications/recent HTTP/1.1" 200 -
2025-05-19 01:23:08,561 - werkzeug - INFO -  * Detected change in 'c:\\Users\\<USER>\\Desktop\\ملف البرنامج\\PythonCashierSystem.v2 - Copy - Copy\\PythonCashierSystem\\models\\accounting.py', reloading
2025-05-19 01:23:09,485 - werkzeug - INFO -  * Restarting with stat
2025-05-19 01:23:14,549 - app - INFO - استخدام قاعدة البيانات المحلية: sqlite:///c:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\instance\fouad_pos.db
2025-05-19 01:23:14,984 - nobara - INFO - تم بدء تشغيل التطبيق
2025-05-19 01:23:15,236 - app - WARNING - مكتبة schedule غير متوفرة. لن يتم تفعيل النسخ الاحتياطي التلقائي.
2025-05-19 01:23:15,911 - app - WARNING - تم تخطي تهيئة النسخ الاحتياطي التلقائي لأن مكتبة schedule غير متوفرة
2025-05-19 01:23:15,919 - app - INFO - عمود warehouse_id موجود بالفعل في جدول order
2025-05-19 01:23:16,770 - security - WARNING - No JWT secret provided. Generated a new one. Please set JWT_SECRET in your environment.
2025-05-19 01:23:17,434 - __main__ - INFO - جاري بدء التطبيق على العنوان: http://************:5000
2025-05-19 01:23:18,193 - werkzeug - WARNING -  * Debugger is active!
2025-05-19 01:23:18,205 - werkzeug - INFO -  * Debugger PIN: 692-333-133
2025-05-19 01:23:18,775 - werkzeug - INFO - ************ - - [19/May/2025 01:23:18] "[32mGET / HTTP/1.1[0m" 302 -
2025-05-19 01:23:19,026 - werkzeug - INFO - ************ - - [19/May/2025 01:23:19] "GET /home HTTP/1.1" 200 -
2025-05-19 01:23:19,382 - werkzeug - INFO - ************ - - [19/May/2025 01:23:19] "[36mGET /static/css/dashboard-pro.css?v=1.0.1 HTTP/1.1[0m" 304 -
2025-05-19 01:23:19,471 - werkzeug - INFO - ************ - - [19/May/2025 01:23:19] "GET /api/notifications/count HTTP/1.1" 200 -
2025-05-19 01:23:19,493 - werkzeug - INFO - ************ - - [19/May/2025 01:23:19] "[36mGET /static/css/nobara-pro.css?v=1.0.0 HTTP/1.1[0m" 304 -
2025-05-19 01:23:19,494 - werkzeug - INFO - ************ - - [19/May/2025 01:23:19] "GET /api/notifications/count HTTP/1.1" 200 -
2025-05-19 01:23:19,496 - werkzeug - INFO - ************ - - [19/May/2025 01:23:19] "[36mGET /static/js/notifications-system.js?v=1.0.0 HTTP/1.1[0m" 304 -
2025-05-19 01:23:19,542 - werkzeug - INFO - ************ - - [19/May/2025 01:23:19] "[36mGET /static/uploads/logo_20250518120852.jpeg HTTP/1.1[0m" 304 -
2025-05-19 01:23:19,560 - werkzeug - INFO - ************ - - [19/May/2025 01:23:19] "[36mGET /static/js/dashboard-pro.js?v=1.0.0 HTTP/1.1[0m" 304 -
2025-05-19 01:23:19,567 - werkzeug - INFO - ************ - - [19/May/2025 01:23:19] "[36mGET /static/js/system-manager.js?v=1.0.0 HTTP/1.1[0m" 304 -
2025-05-19 01:23:19,583 - werkzeug - INFO - ************ - - [19/May/2025 01:23:19] "[36mGET /static/js/dashboard-manager.js?v=1.0.0 HTTP/1.1[0m" 304 -
2025-05-19 01:23:20,050 - werkzeug - INFO - ************ - - [19/May/2025 01:23:20] "GET /api/notifications/recent HTTP/1.1" 200 -
2025-05-19 01:23:20,081 - werkzeug - INFO - ************ - - [19/May/2025 01:23:20] "GET /api/notifications/recent HTTP/1.1" 200 -
2025-05-19 01:23:45,426 - werkzeug - INFO -  * Detected change in 'c:\\Users\\<USER>\\Desktop\\ملف البرنامج\\PythonCashierSystem.v2 - Copy - Copy\\PythonCashierSystem\\models\\accounting.py', reloading
2025-05-19 01:23:46,027 - werkzeug - INFO -  * Restarting with stat
2025-05-19 01:23:47,421 - app - INFO - استخدام قاعدة البيانات المحلية: sqlite:///c:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\instance\fouad_pos.db
2025-05-19 01:23:47,466 - nobara - INFO - تم بدء تشغيل التطبيق
2025-05-19 01:23:47,793 - app - WARNING - مكتبة schedule غير متوفرة. لن يتم تفعيل النسخ الاحتياطي التلقائي.
2025-05-19 01:23:47,823 - app - WARNING - تم تخطي تهيئة النسخ الاحتياطي التلقائي لأن مكتبة schedule غير متوفرة
2025-05-19 01:23:47,829 - app - INFO - عمود warehouse_id موجود بالفعل في جدول order
2025-05-19 01:23:48,000 - security - WARNING - No JWT secret provided. Generated a new one. Please set JWT_SECRET in your environment.
2025-05-19 01:23:48,183 - __main__ - INFO - جاري بدء التطبيق على العنوان: http://************:5000
2025-05-19 01:23:48,922 - werkzeug - WARNING -  * Debugger is active!
2025-05-19 01:23:48,934 - werkzeug - INFO -  * Debugger PIN: 692-333-133
2025-05-19 01:23:49,656 - werkzeug - INFO - ************ - - [19/May/2025 01:23:49] "[32mGET / HTTP/1.1[0m" 302 -
2025-05-19 01:23:49,695 - werkzeug - INFO - ************ - - [19/May/2025 01:23:49] "GET /api/notifications/recent HTTP/1.1" 200 -
2025-05-19 01:23:49,734 - werkzeug - INFO - ************ - - [19/May/2025 01:23:49] "GET /api/notifications/recent HTTP/1.1" 200 -
2025-05-19 01:23:49,985 - werkzeug - INFO - ************ - - [19/May/2025 01:23:49] "GET /home HTTP/1.1" 200 -
2025-05-19 01:23:50,309 - werkzeug - INFO - ************ - - [19/May/2025 01:23:50] "[36mGET /static/css/nobara-pro.css?v=1.0.0 HTTP/1.1[0m" 304 -
2025-05-19 01:23:50,312 - werkzeug - INFO - ************ - - [19/May/2025 01:23:50] "[36mGET /static/css/dashboard-pro.css?v=1.0.1 HTTP/1.1[0m" 304 -
2025-05-19 01:23:50,388 - werkzeug - INFO - ************ - - [19/May/2025 01:23:50] "[36mGET /static/js/notifications-system.js?v=1.0.0 HTTP/1.1[0m" 304 -
2025-05-19 01:23:50,390 - werkzeug - INFO - ************ - - [19/May/2025 01:23:50] "[36mGET /static/js/system-manager.js?v=1.0.0 HTTP/1.1[0m" 304 -
2025-05-19 01:23:50,497 - werkzeug - INFO - ************ - - [19/May/2025 01:23:50] "[36mGET /static/uploads/logo_20250518120852.jpeg HTTP/1.1[0m" 304 -
2025-05-19 01:23:50,519 - werkzeug - INFO - ************ - - [19/May/2025 01:23:50] "[36mGET /static/js/dashboard-manager.js?v=1.0.0 HTTP/1.1[0m" 304 -
2025-05-19 01:23:50,520 - werkzeug - INFO - ************ - - [19/May/2025 01:23:50] "[36mGET /static/js/dashboard-pro.js?v=1.0.0 HTTP/1.1[0m" 304 -
2025-05-19 01:24:15,826 - werkzeug - INFO -  * Detected change in 'c:\\Users\\<USER>\\Desktop\\ملف البرنامج\\PythonCashierSystem.v2 - Copy - Copy\\PythonCashierSystem\\models\\accounting.py', reloading
2025-05-19 01:24:16,585 - werkzeug - INFO -  * Restarting with stat
2025-05-19 01:24:18,757 - app - INFO - استخدام قاعدة البيانات المحلية: sqlite:///c:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\instance\fouad_pos.db
2025-05-19 01:24:18,822 - nobara - INFO - تم بدء تشغيل التطبيق
2025-05-19 01:24:19,122 - app - WARNING - مكتبة schedule غير متوفرة. لن يتم تفعيل النسخ الاحتياطي التلقائي.
2025-05-19 01:24:19,153 - app - WARNING - تم تخطي تهيئة النسخ الاحتياطي التلقائي لأن مكتبة schedule غير متوفرة
2025-05-19 01:24:19,157 - app - INFO - عمود warehouse_id موجود بالفعل في جدول order
2025-05-19 01:24:19,352 - security - WARNING - No JWT secret provided. Generated a new one. Please set JWT_SECRET in your environment.
2025-05-19 01:24:19,551 - __main__ - INFO - جاري بدء التطبيق على العنوان: http://************:5000
2025-05-19 01:24:20,184 - werkzeug - WARNING -  * Debugger is active!
2025-05-19 01:24:20,196 - werkzeug - INFO -  * Debugger PIN: 692-333-133
2025-05-19 01:24:20,904 - werkzeug - INFO - ************ - - [19/May/2025 01:24:20] "GET /api/notifications/count HTTP/1.1" 200 -
2025-05-19 01:24:20,906 - werkzeug - INFO - ************ - - [19/May/2025 01:24:20] "[32mGET / HTTP/1.1[0m" 302 -
2025-05-19 01:24:20,938 - werkzeug - INFO - ************ - - [19/May/2025 01:24:20] "GET /api/notifications/recent HTTP/1.1" 200 -
2025-05-19 01:24:20,962 - werkzeug - INFO - ************ - - [19/May/2025 01:24:20] "GET /api/notifications/count HTTP/1.1" 200 -
2025-05-19 01:24:20,985 - werkzeug - INFO - ************ - - [19/May/2025 01:24:20] "GET /api/notifications/recent HTTP/1.1" 200 -
2025-05-19 01:24:21,174 - werkzeug - INFO - ************ - - [19/May/2025 01:24:21] "GET /api/dashboard HTTP/1.1" 200 -
2025-05-19 01:24:21,256 - werkzeug - INFO - ************ - - [19/May/2025 01:24:21] "GET /home HTTP/1.1" 200 -
2025-05-19 01:24:21,328 - werkzeug - INFO - ************ - - [19/May/2025 01:24:21] "GET /api/dashboard HTTP/1.1" 200 -
2025-05-19 01:24:21,480 - werkzeug - INFO - ************ - - [19/May/2025 01:24:21] "[36mGET /static/css/dashboard-pro.css?v=1.0.1 HTTP/1.1[0m" 304 -
2025-05-19 01:24:21,485 - werkzeug - INFO - ************ - - [19/May/2025 01:24:21] "[36mGET /static/js/notifications-system.js?v=1.0.0 HTTP/1.1[0m" 304 -
2025-05-19 01:24:21,513 - werkzeug - INFO - ************ - - [19/May/2025 01:24:21] "[36mGET /static/js/system-manager.js?v=1.0.0 HTTP/1.1[0m" 304 -
2025-05-19 01:24:21,530 - werkzeug - INFO - ************ - - [19/May/2025 01:24:21] "[36mGET /static/uploads/logo_20250518120852.jpeg HTTP/1.1[0m" 304 -
2025-05-19 01:24:21,537 - werkzeug - INFO - ************ - - [19/May/2025 01:24:21] "[36mGET /static/js/dashboard-manager.js?v=1.0.0 HTTP/1.1[0m" 304 -
2025-05-19 01:24:21,548 - werkzeug - INFO - ************ - - [19/May/2025 01:24:21] "[36mGET /static/js/dashboard-pro.js?v=1.0.0 HTTP/1.1[0m" 304 -
2025-05-19 01:24:21,569 - werkzeug - INFO - ************ - - [19/May/2025 01:24:21] "[36mGET /static/css/nobara-pro.css?v=1.0.0 HTTP/1.1[0m" 304 -
2025-05-19 01:24:42,754 - werkzeug - INFO -  * Detected change in 'c:\\Users\\<USER>\\Desktop\\ملف البرنامج\\PythonCashierSystem.v2 - Copy - Copy\\PythonCashierSystem\\models\\accounting.py', reloading
2025-05-19 01:24:43,628 - werkzeug - INFO -  * Restarting with stat
2025-05-19 01:24:45,718 - app - INFO - استخدام قاعدة البيانات المحلية: sqlite:///c:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\instance\fouad_pos.db
2025-05-19 01:24:45,767 - nobara - INFO - تم بدء تشغيل التطبيق
2025-05-19 01:24:46,051 - app - WARNING - مكتبة schedule غير متوفرة. لن يتم تفعيل النسخ الاحتياطي التلقائي.
2025-05-19 01:24:46,072 - app - WARNING - تم تخطي تهيئة النسخ الاحتياطي التلقائي لأن مكتبة schedule غير متوفرة
2025-05-19 01:24:46,076 - app - INFO - عمود warehouse_id موجود بالفعل في جدول order
2025-05-19 01:24:46,257 - security - WARNING - No JWT secret provided. Generated a new one. Please set JWT_SECRET in your environment.
2025-05-19 01:24:46,447 - __main__ - INFO - جاري بدء التطبيق على العنوان: http://************:5000
2025-05-19 01:24:47,139 - werkzeug - WARNING -  * Debugger is active!
2025-05-19 01:24:47,149 - werkzeug - INFO -  * Debugger PIN: 692-333-133
2025-05-19 01:24:47,801 - nobara - ERROR - استثناء: When initializing mapper Mapper[FiscalPeriod(fiscal_period)], expression 'FiscalYear' failed to locate a name ('FiscalYear'). If this is a class name, consider adding this relationship() to the <class 'models.accounting.FiscalPeriod'> class after both dependent classes have been defined.
معلومات الطلب: {"url": "http://************:5000/", "method": "GET", "headers": {"Host": "************:5000", "Connection": "keep-alive", "Upgrade-Insecure-Requests": "1", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7", "Accept-Encoding": "gzip, deflate", "Accept-Language": "en-US,en;q=0.9", "Cookie": "NEXT_LOCALE=en; session=.eJztksFqwzAMhl_F-JJLKLGd2FYeY-y2laJYMklIk-E4p9J3n5u9w04F8f8SQnxC6CFvccF95F32Xw8pcjHJKW1J1vL7aKymU-OpgziNX2rcmZteVPO0zqgv6VjzdOfLJ99_Fsz8wZETr4HFNswcciVG3MW6Ccw5TcORWVR5ygtX8vqs3_A3_D_h17q8fuJ9lH1OB5dqItlLTegdUIsxsrPglA8QAAcXicB5Z0LrS7RGd69u1NqQHZqgFABqyz7GMhZVB6AG6BwZywrQ69A0PliF6FQgUp2xAWKwCJ50cJaIySKbcoXbsXP620bJ5y9x1yli.aCpb5w.1Jv5pyD8PazDruwjt3qLzvBL5O8"}, "args": {}, "form": {}, "cookies": {"NEXT_LOCALE": "en", "session": ".eJztksFqwzAMhl_F-JJLKLGd2FYeY-y2laJYMklIk-E4p9J3n5u9w04F8f8SQnxC6CFvccF95F32Xw8pcjHJKW1J1vL7aKymU-OpgziNX2rcmZteVPO0zqgv6VjzdOfLJ99_Fsz8wZETr4HFNswcciVG3MW6Ccw5TcORWVR5ygtX8vqs3_A3_D_h17q8fuJ9lH1OB5dqItlLTegdUIsxsrPglA8QAAcXicB5Z0LrS7RGd69u1NqQHZqgFABqyz7GMhZVB6AG6BwZywrQ69A0PliF6FQgUp2xAWKwCJ50cJaIySKbcoXbsXP620bJ5y9x1yli.aCpb5w.1Jv5pyD8PazDruwjt3qLzvBL5O8"}, "remote_addr": "************", "user_agent": null}
تتبع الاستثناء:
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\sqlalchemy\orm\clsregistry.py", line 516, in _resolve_name
    rval = d[token]
           ~^^^^^^^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\sqlalchemy\util\_collections.py", line 345, in __missing__
    self[key] = val = self.creator(key)
                      ~~~~~~~~~~~~^^^^^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\sqlalchemy\orm\clsregistry.py", line 484, in _access_cls
    return self.fallback[key]
           ~~~~~~~~~~~~~^^^^^
KeyError: 'FiscalYear'

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\app.py", line 917, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\app.py", line 902, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)  # type: ignore[no-any-return]
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\routes\auth.py", line 13, in index
    if current_user.is_authenticated:
       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\werkzeug\local.py", line 318, in __get__
    obj = instance._get_current_object()
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\werkzeug\local.py", line 526, in _get_current_object
    return get_name(local())
                    ~~~~~^^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask_login\utils.py", line 25, in <lambda>
    current_user = LocalProxy(lambda: _get_user())
                                      ~~~~~~~~~^^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask_login\utils.py", line 370, in _get_user
    current_app.login_manager._load_user()
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask_login\login_manager.py", line 364, in _load_user
    user = self._user_callback(user_id)
  File "c:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\main.py", line 31, in load_user
    return User.query.get(int(user_id))
           ^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask_sqlalchemy\model.py", line 22, in __get__
    return cls.query_class(
           ~~~~~~~~~~~~~~~^
        cls, session=cls.__fsa__.session()  # type: ignore[arg-type]
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\sqlalchemy\orm\query.py", line 276, in __init__
    self._set_entities(entities)
    ~~~~~~~~~~~~~~~~~~^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\sqlalchemy\orm\query.py", line 289, in _set_entities
    coercions.expect(
    ~~~~~~~~~~~~~~~~^
        roles.ColumnsClauseRole,
        ^^^^^^^^^^^^^^^^^^^^^^^^
    ...<2 lines>...
        post_inspect=True,
        ^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\sqlalchemy\sql\coercions.py", line 388, in expect
    insp._post_inspect
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\sqlalchemy\util\langhelpers.py", line 1257, in __get__
    obj.__dict__[self.__name__] = result = self.fget(obj)
                                           ~~~~~~~~~^^^^^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\sqlalchemy\orm\mapper.py", line 2724, in _post_inspect
    self._check_configure()
    ~~~~~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\sqlalchemy\orm\mapper.py", line 2401, in _check_configure
    _configure_registries({self.registry}, cascade=True)
    ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\sqlalchemy\orm\mapper.py", line 4214, in _configure_registries
    _do_configure_registries(registries, cascade)
    ~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\sqlalchemy\orm\mapper.py", line 4255, in _do_configure_registries
    mapper._post_configure_properties()
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\sqlalchemy\orm\mapper.py", line 2418, in _post_configure_properties
    prop.init()
    ~~~~~~~~~^^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\sqlalchemy\orm\interfaces.py", line 589, in init
    self.do_init()
    ~~~~~~~~~~~~^^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\sqlalchemy\orm\relationships.py", line 1656, in do_init
    self._setup_entity()
    ~~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\sqlalchemy\orm\relationships.py", line 1868, in _setup_entity
    self._clsregistry_resolve_name(argument)(),
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\sqlalchemy\orm\clsregistry.py", line 520, in _resolve_name
    self._raise_for_name(name, err)
    ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\sqlalchemy\orm\clsregistry.py", line 501, in _raise_for_name
    raise exc.InvalidRequestError(
    ...<5 lines>...
    ) from err
sqlalchemy.exc.InvalidRequestError: When initializing mapper Mapper[FiscalPeriod(fiscal_period)], expression 'FiscalYear' failed to locate a name ('FiscalYear'). If this is a class name, consider adding this relationship() to the <class 'models.accounting.FiscalPeriod'> class after both dependent classes have been defined.

2025-05-19 01:24:47,828 - werkzeug - INFO - ************ - - [19/May/2025 01:24:47] "[35m[1mGET / HTTP/1.1[0m" 500 -
2025-05-19 01:24:47,932 - nobara - ERROR - استثناء: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.
معلومات الطلب: {"url": "http://************:5000/favicon.ico", "method": "GET", "headers": {"Host": "************:5000", "Connection": "keep-alive", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Accept": "image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8", "Referer": "http://************:5000/", "Accept-Encoding": "gzip, deflate", "Accept-Language": "en-US,en;q=0.9", "Cookie": "NEXT_LOCALE=en; session=.eJztksFqwzAMhl_F-JJLKLGd2FYeY-y2laJYMklIk-E4p9J3n5u9w04F8f8SQnxC6CFvccF95F32Xw8pcjHJKW1J1vL7aKymU-OpgziNX2rcmZteVPO0zqgv6VjzdOfLJ99_Fsz8wZETr4HFNswcciVG3MW6Ccw5TcORWVR5ygtX8vqs3_A3_D_h17q8fuJ9lH1OB5dqItlLTegdUIsxsrPglA8QAAcXicB5Z0LrS7RGd69u1NqQHZqgFABqyz7GMhZVB6AG6BwZywrQ69A0PliF6FQgUp2xAWKwCJ50cJaIySKbcoXbsXP620bJ5y9x1yli.aCpb5w.1Jv5pyD8PazDruwjt3qLzvBL5O8"}, "args": {}, "form": {}, "cookies": {"NEXT_LOCALE": "en", "session": ".eJztksFqwzAMhl_F-JJLKLGd2FYeY-y2laJYMklIk-E4p9J3n5u9w04F8f8SQnxC6CFvccF95F32Xw8pcjHJKW1J1vL7aKymU-OpgziNX2rcmZteVPO0zqgv6VjzdOfLJ99_Fsz8wZETr4HFNswcciVG3MW6Ccw5TcORWVR5ygtX8vqs3_A3_D_h17q8fuJ9lH1OB5dqItlLTegdUIsxsrPglA8QAAcXicB5Z0LrS7RGd69u1NqQHZqgFABqyz7GMhZVB6AG6BwZywrQ69A0PliF6FQgUp2xAWKwCJ50cJaIySKbcoXbsXP620bJ5y9x1yli.aCpb5w.1Jv5pyD8PazDruwjt3qLzvBL5O8"}, "remote_addr": "************", "user_agent": null}
تتبع الاستثناء:
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\app.py", line 917, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\app.py", line 891, in dispatch_request
    self.raise_routing_exception(req)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\app.py", line 500, in raise_routing_exception
    raise request.routing_exception  # type: ignore[misc]
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\ctx.py", line 362, in match_request
    result = self.url_adapter.match(return_rule=True)  # type: ignore
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\werkzeug\routing\map.py", line 629, in match
    raise NotFound() from None
werkzeug.exceptions.NotFound: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.

2025-05-19 01:24:47,944 - werkzeug - INFO - ************ - - [19/May/2025 01:24:47] "[35m[1mGET /favicon.ico HTTP/1.1[0m" 500 -
2025-05-19 01:24:48,158 - nobara - ERROR - استثناء: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[FiscalPeriod(fiscal_period)]'. Original exception was: When initializing mapper Mapper[FiscalPeriod(fiscal_period)], expression 'FiscalYear' failed to locate a name ('FiscalYear'). If this is a class name, consider adding this relationship() to the <class 'models.accounting.FiscalPeriod'> class after both dependent classes have been defined.
معلومات الطلب: {"url": "http://************:5000/api/notifications/recent", "method": "GET", "headers": {"Host": "************:5000", "Connection": "keep-alive", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Accept": "*/*", "Referer": "http://************:5000/home", "Accept-Encoding": "gzip, deflate", "Accept-Language": "en-US,en;q=0.9", "Cookie": "NEXT_LOCALE=en; session=.eJztksFqwzAMhl_F-JJLKLGd2FYeY-y2laJYMklIk-E4p9J3n5u9w04F8f8SQnxC6CFvccF95F32Xw8pcjHJKW1J1vL7aKymU-OpgziNX2rcmZteVPO0zqgv6VjzdOfLJ99_Fsz8wZETr4HFNswcciVG3MW6Ccw5TcORWVR5ygtX8vqs3_A3_D_h17q8fuJ9lH1OB5dqItlLTegdUIsxsrPglA8QAAcXicB5Z0LrS7RGd69u1NqQHZqgFABqyz7GMhZVB6AG6BwZywrQ69A0PliF6FQgUp2xAWKwCJ50cJaIySKbcoXbsXP620bJ5y9x1yli.aCpb5w.1Jv5pyD8PazDruwjt3qLzvBL5O8"}, "args": {}, "form": {}, "cookies": {"NEXT_LOCALE": "en", "session": ".eJztksFqwzAMhl_F-JJLKLGd2FYeY-y2laJYMklIk-E4p9J3n5u9w04F8f8SQnxC6CFvccF95F32Xw8pcjHJKW1J1vL7aKymU-OpgziNX2rcmZteVPO0zqgv6VjzdOfLJ99_Fsz8wZETr4HFNswcciVG3MW6Ccw5TcORWVR5ygtX8vqs3_A3_D_h17q8fuJ9lH1OB5dqItlLTegdUIsxsrPglA8QAAcXicB5Z0LrS7RGd69u1NqQHZqgFABqyz7GMhZVB6AG6BwZywrQ69A0PliF6FQgUp2xAWKwCJ50cJaIySKbcoXbsXP620bJ5y9x1yli.aCpb5w.1Jv5pyD8PazDruwjt3qLzvBL5O8"}, "remote_addr": "************", "user_agent": null}
تتبع الاستثناء:
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\app.py", line 917, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\app.py", line 902, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)  # type: ignore[no-any-return]
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask_login\utils.py", line 284, in decorated_view
    elif not current_user.is_authenticated:
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\werkzeug\local.py", line 318, in __get__
    obj = instance._get_current_object()
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\werkzeug\local.py", line 526, in _get_current_object
    return get_name(local())
                    ~~~~~^^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask_login\utils.py", line 25, in <lambda>
    current_user = LocalProxy(lambda: _get_user())
                                      ~~~~~~~~~^^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask_login\utils.py", line 370, in _get_user
    current_app.login_manager._load_user()
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask_login\login_manager.py", line 364, in _load_user
    user = self._user_callback(user_id)
  File "c:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\main.py", line 31, in load_user
    return User.query.get(int(user_id))
           ^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask_sqlalchemy\model.py", line 22, in __get__
    return cls.query_class(
           ~~~~~~~~~~~~~~~^
        cls, session=cls.__fsa__.session()  # type: ignore[arg-type]
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\sqlalchemy\orm\query.py", line 276, in __init__
    self._set_entities(entities)
    ~~~~~~~~~~~~~~~~~~^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\sqlalchemy\orm\query.py", line 289, in _set_entities
    coercions.expect(
    ~~~~~~~~~~~~~~~~^
        roles.ColumnsClauseRole,
        ^^^^^^^^^^^^^^^^^^^^^^^^
    ...<2 lines>...
        post_inspect=True,
        ^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\sqlalchemy\sql\coercions.py", line 388, in expect
    insp._post_inspect
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\sqlalchemy\util\langhelpers.py", line 1257, in __get__
    obj.__dict__[self.__name__] = result = self.fget(obj)
                                           ~~~~~~~~~^^^^^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\sqlalchemy\orm\mapper.py", line 2724, in _post_inspect
    self._check_configure()
    ~~~~~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\sqlalchemy\orm\mapper.py", line 2401, in _check_configure
    _configure_registries({self.registry}, cascade=True)
    ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\sqlalchemy\orm\mapper.py", line 4214, in _configure_registries
    _do_configure_registries(registries, cascade)
    ~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\sqlalchemy\orm\mapper.py", line 4251, in _do_configure_registries
    raise e
sqlalchemy.exc.InvalidRequestError: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[FiscalPeriod(fiscal_period)]'. Original exception was: When initializing mapper Mapper[FiscalPeriod(fiscal_period)], expression 'FiscalYear' failed to locate a name ('FiscalYear'). If this is a class name, consider adding this relationship() to the <class 'models.accounting.FiscalPeriod'> class after both dependent classes have been defined.

2025-05-19 01:24:48,174 - werkzeug - INFO - ************ - - [19/May/2025 01:24:48] "[35m[1mGET /api/notifications/recent HTTP/1.1[0m" 500 -
2025-05-19 01:24:48,191 - nobara - ERROR - استثناء: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[FiscalPeriod(fiscal_period)]'. Original exception was: When initializing mapper Mapper[FiscalPeriod(fiscal_period)], expression 'FiscalYear' failed to locate a name ('FiscalYear'). If this is a class name, consider adding this relationship() to the <class 'models.accounting.FiscalPeriod'> class after both dependent classes have been defined.
معلومات الطلب: {"url": "http://************:5000/api/notifications/recent", "method": "GET", "headers": {"Host": "************:5000", "Connection": "keep-alive", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Accept": "*/*", "Referer": "http://************:5000/home", "Accept-Encoding": "gzip, deflate", "Accept-Language": "en-US,en;q=0.9", "Cookie": "NEXT_LOCALE=en; session=.eJztksFqwzAMhl_F-JJLKLGd2FYeY-y2laJYMklIk-E4p9J3n5u9w04F8f8SQnxC6CFvccF95F32Xw8pcjHJKW1J1vL7aKymU-OpgziNX2rcmZteVPO0zqgv6VjzdOfLJ99_Fsz8wZETr4HFNswcciVG3MW6Ccw5TcORWVR5ygtX8vqs3_A3_D_h17q8fuJ9lH1OB5dqItlLTegdUIsxsrPglA8QAAcXicB5Z0LrS7RGd69u1NqQHZqgFABqyz7GMhZVB6AG6BwZywrQ69A0PliF6FQgUp2xAWKwCJ50cJaIySKbcoXbsXP620bJ5y9x1yli.aCpb5w.1Jv5pyD8PazDruwjt3qLzvBL5O8"}, "args": {}, "form": {}, "cookies": {"NEXT_LOCALE": "en", "session": ".eJztksFqwzAMhl_F-JJLKLGd2FYeY-y2laJYMklIk-E4p9J3n5u9w04F8f8SQnxC6CFvccF95F32Xw8pcjHJKW1J1vL7aKymU-OpgziNX2rcmZteVPO0zqgv6VjzdOfLJ99_Fsz8wZETr4HFNswcciVG3MW6Ccw5TcORWVR5ygtX8vqs3_A3_D_h17q8fuJ9lH1OB5dqItlLTegdUIsxsrPglA8QAAcXicB5Z0LrS7RGd69u1NqQHZqgFABqyz7GMhZVB6AG6BwZywrQ69A0PliF6FQgUp2xAWKwCJ50cJaIySKbcoXbsXP620bJ5y9x1yli.aCpb5w.1Jv5pyD8PazDruwjt3qLzvBL5O8"}, "remote_addr": "************", "user_agent": null}
تتبع الاستثناء:
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\app.py", line 917, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\app.py", line 902, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)  # type: ignore[no-any-return]
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask_login\utils.py", line 284, in decorated_view
    elif not current_user.is_authenticated:
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\werkzeug\local.py", line 318, in __get__
    obj = instance._get_current_object()
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\werkzeug\local.py", line 526, in _get_current_object
    return get_name(local())
                    ~~~~~^^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask_login\utils.py", line 25, in <lambda>
    current_user = LocalProxy(lambda: _get_user())
                                      ~~~~~~~~~^^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask_login\utils.py", line 370, in _get_user
    current_app.login_manager._load_user()
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask_login\login_manager.py", line 364, in _load_user
    user = self._user_callback(user_id)
  File "c:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\main.py", line 31, in load_user
    return User.query.get(int(user_id))
           ^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask_sqlalchemy\model.py", line 22, in __get__
    return cls.query_class(
           ~~~~~~~~~~~~~~~^
        cls, session=cls.__fsa__.session()  # type: ignore[arg-type]
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\sqlalchemy\orm\query.py", line 276, in __init__
    self._set_entities(entities)
    ~~~~~~~~~~~~~~~~~~^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\sqlalchemy\orm\query.py", line 289, in _set_entities
    coercions.expect(
    ~~~~~~~~~~~~~~~~^
        roles.ColumnsClauseRole,
        ^^^^^^^^^^^^^^^^^^^^^^^^
    ...<2 lines>...
        post_inspect=True,
        ^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\sqlalchemy\sql\coercions.py", line 388, in expect
    insp._post_inspect
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\sqlalchemy\util\langhelpers.py", line 1257, in __get__
    obj.__dict__[self.__name__] = result = self.fget(obj)
                                           ~~~~~~~~~^^^^^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\sqlalchemy\orm\mapper.py", line 2724, in _post_inspect
    self._check_configure()
    ~~~~~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\sqlalchemy\orm\mapper.py", line 2401, in _check_configure
    _configure_registries({self.registry}, cascade=True)
    ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\sqlalchemy\orm\mapper.py", line 4214, in _configure_registries
    _do_configure_registries(registries, cascade)
    ~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\sqlalchemy\orm\mapper.py", line 4251, in _do_configure_registries
    raise e
sqlalchemy.exc.InvalidRequestError: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[FiscalPeriod(fiscal_period)]'. Original exception was: When initializing mapper Mapper[FiscalPeriod(fiscal_period)], expression 'FiscalYear' failed to locate a name ('FiscalYear'). If this is a class name, consider adding this relationship() to the <class 'models.accounting.FiscalPeriod'> class after both dependent classes have been defined.

2025-05-19 01:24:48,203 - werkzeug - INFO - ************ - - [19/May/2025 01:24:48] "[35m[1mGET /api/notifications/recent HTTP/1.1[0m" 500 -
2025-05-19 01:25:14,407 - werkzeug - INFO -  * Detected change in 'c:\\Users\\<USER>\\Desktop\\ملف البرنامج\\PythonCashierSystem.v2 - Copy - Copy\\PythonCashierSystem\\models\\accounting.py', reloading
2025-05-19 01:25:14,842 - werkzeug - INFO -  * Restarting with stat
2025-05-19 01:25:16,462 - app - INFO - استخدام قاعدة البيانات المحلية: sqlite:///c:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\instance\fouad_pos.db
2025-05-19 01:25:16,514 - nobara - INFO - تم بدء تشغيل التطبيق
2025-05-19 01:25:16,817 - app - WARNING - مكتبة schedule غير متوفرة. لن يتم تفعيل النسخ الاحتياطي التلقائي.
2025-05-19 01:25:17,040 - app - WARNING - تم تخطي تهيئة النسخ الاحتياطي التلقائي لأن مكتبة schedule غير متوفرة
2025-05-19 01:25:17,049 - app - INFO - عمود warehouse_id موجود بالفعل في جدول order
2025-05-19 01:25:17,256 - security - WARNING - No JWT secret provided. Generated a new one. Please set JWT_SECRET in your environment.
2025-05-19 01:25:17,431 - __main__ - INFO - جاري بدء التطبيق على العنوان: http://************:5000
2025-05-19 01:25:18,307 - werkzeug - WARNING -  * Debugger is active!
2025-05-19 01:25:18,319 - werkzeug - INFO -  * Debugger PIN: 692-333-133
2025-05-19 01:25:18,938 - werkzeug - INFO - ************ - - [19/May/2025 01:25:18] "[32mGET / HTTP/1.1[0m" 302 -
2025-05-19 01:25:18,992 - werkzeug - INFO - ************ - - [19/May/2025 01:25:18] "GET /api/notifications/recent HTTP/1.1" 200 -
2025-05-19 01:25:19,019 - werkzeug - INFO - ************ - - [19/May/2025 01:25:19] "GET /api/notifications/recent HTTP/1.1" 200 -
2025-05-19 01:25:19,193 - werkzeug - INFO - ************ - - [19/May/2025 01:25:19] "GET /api/notifications/count HTTP/1.1" 200 -
2025-05-19 01:25:19,200 - werkzeug - INFO - ************ - - [19/May/2025 01:25:19] "GET /home HTTP/1.1" 200 -
2025-05-19 01:25:19,219 - werkzeug - INFO - ************ - - [19/May/2025 01:25:19] "GET /api/notifications/count HTTP/1.1" 200 -
2025-05-19 01:25:19,718 - werkzeug - INFO - ************ - - [19/May/2025 01:25:19] "[36mGET /static/js/system-manager.js?v=1.0.0 HTTP/1.1[0m" 304 -
2025-05-19 01:25:19,728 - werkzeug - INFO - ************ - - [19/May/2025 01:25:19] "[36mGET /static/js/notifications-system.js?v=1.0.0 HTTP/1.1[0m" 304 -
2025-05-19 01:25:19,732 - werkzeug - INFO - ************ - - [19/May/2025 01:25:19] "[36mGET /static/css/dashboard-pro.css?v=1.0.1 HTTP/1.1[0m" 304 -
2025-05-19 01:25:19,756 - werkzeug - INFO - ************ - - [19/May/2025 01:25:19] "[36mGET /static/css/nobara-pro.css?v=1.0.0 HTTP/1.1[0m" 304 -
2025-05-19 01:25:19,764 - werkzeug - INFO - ************ - - [19/May/2025 01:25:19] "[36mGET /static/uploads/logo_20250518120852.jpeg HTTP/1.1[0m" 304 -
2025-05-19 01:25:19,776 - werkzeug - INFO - ************ - - [19/May/2025 01:25:19] "[36mGET /static/js/dashboard-manager.js?v=1.0.0 HTTP/1.1[0m" 304 -
2025-05-19 01:25:19,811 - werkzeug - INFO - ************ - - [19/May/2025 01:25:19] "[36mGET /static/js/dashboard-pro.js?v=1.0.0 HTTP/1.1[0m" 304 -
2025-05-19 01:25:20,985 - werkzeug - INFO - ************ - - [19/May/2025 01:25:20] "GET /api/settings HTTP/1.1" 200 -
2025-05-19 01:25:21,004 - werkzeug - INFO - ************ - - [19/May/2025 01:25:21] "GET /api/notifications/count HTTP/1.1" 200 -
2025-05-19 01:25:21,026 - werkzeug - INFO - ************ - - [19/May/2025 01:25:21] "GET /api/sales-chart-data?days=30 HTTP/1.1" 200 -
2025-05-19 01:25:21,037 - werkzeug - INFO - ************ - - [19/May/2025 01:25:21] "GET /api/category-distribution HTTP/1.1" 200 -
2025-05-19 01:25:21,090 - werkzeug - INFO - ************ - - [19/May/2025 01:25:21] "GET /api/settings HTTP/1.1" 200 -
2025-05-19 01:25:21,134 - werkzeug - INFO - ************ - - [19/May/2025 01:25:21] "GET /api/dashboard HTTP/1.1" 200 -
2025-05-19 01:25:21,230 - werkzeug - INFO - ************ - - [19/May/2025 01:25:21] "GET /api/dashboard HTTP/1.1" 200 -
2025-05-19 01:25:22,087 - werkzeug - INFO - ************ - - [19/May/2025 01:25:22] "GET /api/notifications/count HTTP/1.1" 200 -
2025-05-19 01:25:22,096 - werkzeug - INFO - ************ - - [19/May/2025 01:25:22] "GET /api/notifications/recent HTTP/1.1" 200 -
2025-05-19 01:25:22,121 - werkzeug - INFO - ************ - - [19/May/2025 01:25:22] "GET /api/notifications/count HTTP/1.1" 200 -
2025-05-19 01:25:22,124 - werkzeug - INFO - ************ - - [19/May/2025 01:25:22] "GET /api/notifications/recent HTTP/1.1" 200 -
2025-05-19 01:25:22,185 - nobara - ERROR - استثناء: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.
معلومات الطلب: {"url": "http://************:5000/favicon.ico", "method": "GET", "headers": {"Host": "************:5000", "Connection": "keep-alive", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Accept": "image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8", "Referer": "http://************:5000/home", "Accept-Encoding": "gzip, deflate", "Accept-Language": "en-US,en;q=0.9", "Cookie": "NEXT_LOCALE=en; session=.eJztksFqwzAMhl_F-JJLKLGd2FYeY-y2laJYMklIk-E4p9J3n5u9w04F8f8SQnxC6CFvccF95F32Xw8pcjHJKW1J1vL7aKymU-OpgziNX2rcmZteVPO0zqgv6VjzdOfLJ99_Fsz8wZETr4HFNswcciVG3MW6Ccw5TcORWVR5ygtX8vqs3_A3_D_h17q8fuJ9lH1OB5dqItlLTegdUIsxsrPglA8QAAcXicB5Z0LrS7RGd69u1NqQHZqgFABqyz7GMhZVB6AG6BwZywrQ69A0PliF6FQgUp2xAWKwCJ50cJaIySKbcoXbsXP620bJ5y9x1yli.aCpb5w.1Jv5pyD8PazDruwjt3qLzvBL5O8"}, "args": {}, "form": {}, "cookies": {"NEXT_LOCALE": "en", "session": ".eJztksFqwzAMhl_F-JJLKLGd2FYeY-y2laJYMklIk-E4p9J3n5u9w04F8f8SQnxC6CFvccF95F32Xw8pcjHJKW1J1vL7aKymU-OpgziNX2rcmZteVPO0zqgv6VjzdOfLJ99_Fsz8wZETr4HFNswcciVG3MW6Ccw5TcORWVR5ygtX8vqs3_A3_D_h17q8fuJ9lH1OB5dqItlLTegdUIsxsrPglA8QAAcXicB5Z0LrS7RGd69u1NqQHZqgFABqyz7GMhZVB6AG6BwZywrQ69A0PliF6FQgUp2xAWKwCJ50cJaIySKbcoXbsXP620bJ5y9x1yli.aCpb5w.1Jv5pyD8PazDruwjt3qLzvBL5O8"}, "remote_addr": "************", "user_agent": null}
تتبع الاستثناء:
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\app.py", line 917, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\app.py", line 891, in dispatch_request
    self.raise_routing_exception(req)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\app.py", line 500, in raise_routing_exception
    raise request.routing_exception  # type: ignore[misc]
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\ctx.py", line 362, in match_request
    result = self.url_adapter.match(return_rule=True)  # type: ignore
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\werkzeug\routing\map.py", line 629, in match
    raise NotFound() from None
werkzeug.exceptions.NotFound: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.

2025-05-19 01:25:22,192 - werkzeug - INFO - ************ - - [19/May/2025 01:25:22] "[35m[1mGET /favicon.ico HTTP/1.1[0m" 500 -
2025-05-19 01:25:42,215 - werkzeug - INFO -  * Detected change in 'c:\\Users\\<USER>\\Desktop\\ملف البرنامج\\PythonCashierSystem.v2 - Copy - Copy\\PythonCashierSystem\\models\\accounting.py', reloading
2025-05-19 01:25:43,749 - werkzeug - INFO -  * Restarting with stat
2025-05-19 01:25:46,678 - app - INFO - استخدام قاعدة البيانات المحلية: sqlite:///c:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\instance\fouad_pos.db
2025-05-19 01:25:46,749 - nobara - INFO - تم بدء تشغيل التطبيق
2025-05-19 01:25:47,297 - app - WARNING - مكتبة schedule غير متوفرة. لن يتم تفعيل النسخ الاحتياطي التلقائي.
2025-05-19 01:25:47,348 - app - WARNING - تم تخطي تهيئة النسخ الاحتياطي التلقائي لأن مكتبة schedule غير متوفرة
2025-05-19 01:25:47,352 - app - INFO - عمود warehouse_id موجود بالفعل في جدول order
2025-05-19 01:25:47,688 - security - WARNING - No JWT secret provided. Generated a new one. Please set JWT_SECRET in your environment.
2025-05-19 01:25:48,022 - __main__ - INFO - جاري بدء التطبيق على العنوان: http://************:5000
2025-05-19 01:25:48,706 - werkzeug - WARNING -  * Debugger is active!
2025-05-19 01:25:48,750 - werkzeug - INFO -  * Debugger PIN: 692-333-133
2025-05-19 01:25:49,739 - werkzeug - INFO - ************ - - [19/May/2025 01:25:49] "[32mGET / HTTP/1.1[0m" 302 -
2025-05-19 01:25:49,794 - werkzeug - INFO - ************ - - [19/May/2025 01:25:49] "GET /api/notifications/recent HTTP/1.1" 200 -
2025-05-19 01:25:49,821 - werkzeug - INFO - ************ - - [19/May/2025 01:25:49] "GET /api/notifications/recent HTTP/1.1" 200 -
2025-05-19 01:25:50,044 - werkzeug - INFO - ************ - - [19/May/2025 01:25:50] "GET /home HTTP/1.1" 200 -
2025-05-19 01:25:50,408 - werkzeug - INFO - ************ - - [19/May/2025 01:25:50] "[36mGET /static/js/notifications-system.js?v=1.0.0 HTTP/1.1[0m" 304 -
2025-05-19 01:25:50,540 - werkzeug - INFO - ************ - - [19/May/2025 01:25:50] "[36mGET /static/css/dashboard-pro.css?v=1.0.1 HTTP/1.1[0m" 304 -
2025-05-19 01:25:50,556 - werkzeug - INFO - ************ - - [19/May/2025 01:25:50] "[36mGET /static/js/system-manager.js?v=1.0.0 HTTP/1.1[0m" 304 -
2025-05-19 01:25:50,569 - werkzeug - INFO - ************ - - [19/May/2025 01:25:50] "[36mGET /static/css/nobara-pro.css?v=1.0.0 HTTP/1.1[0m" 304 -
2025-05-19 01:25:50,592 - werkzeug - INFO - ************ - - [19/May/2025 01:25:50] "[36mGET /static/uploads/logo_20250518120852.jpeg HTTP/1.1[0m" 304 -
2025-05-19 01:25:50,600 - werkzeug - INFO - ************ - - [19/May/2025 01:25:50] "[36mGET /static/js/dashboard-manager.js?v=1.0.0 HTTP/1.1[0m" 304 -
2025-05-19 01:25:50,640 - werkzeug - INFO - ************ - - [19/May/2025 01:25:50] "[36mGET /static/js/dashboard-pro.js?v=1.0.0 HTTP/1.1[0m" 304 -
2025-05-19 01:25:51,266 - werkzeug - INFO - ************ - - [19/May/2025 01:25:51] "GET /api/notifications/recent HTTP/1.1" 200 -
2025-05-19 01:25:51,283 - werkzeug - INFO - ************ - - [19/May/2025 01:25:51] "GET /api/notifications/recent HTTP/1.1" 200 -
2025-05-19 01:26:12,033 - werkzeug - INFO -  * Detected change in 'c:\\Users\\<USER>\\Desktop\\ملف البرنامج\\PythonCashierSystem.v2 - Copy - Copy\\PythonCashierSystem\\models\\accounting.py', reloading
2025-05-19 01:26:12,636 - werkzeug - INFO -  * Restarting with stat
2025-05-19 01:26:14,443 - app - INFO - استخدام قاعدة البيانات المحلية: sqlite:///c:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\instance\fouad_pos.db
2025-05-19 01:26:14,503 - nobara - INFO - تم بدء تشغيل التطبيق
2025-05-19 01:26:14,824 - app - WARNING - مكتبة schedule غير متوفرة. لن يتم تفعيل النسخ الاحتياطي التلقائي.
2025-05-19 01:26:14,853 - app - WARNING - تم تخطي تهيئة النسخ الاحتياطي التلقائي لأن مكتبة schedule غير متوفرة
2025-05-19 01:26:14,857 - app - INFO - عمود warehouse_id موجود بالفعل في جدول order
2025-05-19 01:26:15,106 - security - WARNING - No JWT secret provided. Generated a new one. Please set JWT_SECRET in your environment.
2025-05-19 01:26:15,295 - __main__ - INFO - جاري بدء التطبيق على العنوان: http://************:5000
2025-05-19 01:26:15,971 - werkzeug - WARNING -  * Debugger is active!
2025-05-19 01:26:15,992 - werkzeug - INFO -  * Debugger PIN: 692-333-133
2025-05-19 01:26:16,657 - werkzeug - INFO - ************ - - [19/May/2025 01:26:16] "[32mGET / HTTP/1.1[0m" 302 -
2025-05-19 01:26:16,905 - werkzeug - INFO - ************ - - [19/May/2025 01:26:16] "GET /home HTTP/1.1" 200 -
2025-05-19 01:26:17,142 - werkzeug - INFO - ************ - - [19/May/2025 01:26:17] "[36mGET /static/css/nobara-pro.css?v=1.0.0 HTTP/1.1[0m" 304 -
2025-05-19 01:26:17,162 - werkzeug - INFO - ************ - - [19/May/2025 01:26:17] "[36mGET /static/uploads/logo_20250518120852.jpeg HTTP/1.1[0m" 304 -
2025-05-19 01:26:17,196 - werkzeug - INFO - ************ - - [19/May/2025 01:26:17] "[36mGET /static/js/dashboard-manager.js?v=1.0.0 HTTP/1.1[0m" 304 -
2025-05-19 01:26:17,222 - werkzeug - INFO - ************ - - [19/May/2025 01:26:17] "[36mGET /static/js/dashboard-pro.js?v=1.0.0 HTTP/1.1[0m" 304 -
2025-05-19 01:26:17,229 - werkzeug - INFO - ************ - - [19/May/2025 01:26:17] "[36mGET /static/css/dashboard-pro.css?v=1.0.1 HTTP/1.1[0m" 304 -
2025-05-19 01:26:17,274 - werkzeug - INFO - ************ - - [19/May/2025 01:26:17] "[36mGET /static/js/notifications-system.js?v=1.0.0 HTTP/1.1[0m" 304 -
2025-05-19 01:26:17,295 - werkzeug - INFO - ************ - - [19/May/2025 01:26:17] "[36mGET /static/js/system-manager.js?v=1.0.0 HTTP/1.1[0m" 304 -
2025-05-19 01:26:18,094 - werkzeug - INFO - ************ - - [19/May/2025 01:26:18] "GET /api/settings HTTP/1.1" 200 -
2025-05-19 01:26:18,144 - werkzeug - INFO - ************ - - [19/May/2025 01:26:18] "GET /api/category-distribution HTTP/1.1" 200 -
2025-05-19 01:26:18,174 - werkzeug - INFO - ************ - - [19/May/2025 01:26:18] "GET /api/sales-chart-data?days=30 HTTP/1.1" 200 -
2025-05-19 01:26:18,228 - werkzeug - INFO - ************ - - [19/May/2025 01:26:18] "GET /api/settings HTTP/1.1" 200 -
2025-05-19 01:26:18,277 - werkzeug - INFO - ************ - - [19/May/2025 01:26:18] "GET /api/notifications/count HTTP/1.1" 200 -
2025-05-19 01:26:18,348 - werkzeug - INFO - ************ - - [19/May/2025 01:26:18] "GET /api/dashboard HTTP/1.1" 200 -
2025-05-19 01:26:18,557 - werkzeug - INFO - ************ - - [19/May/2025 01:26:18] "GET /api/dashboard HTTP/1.1" 200 -
2025-05-19 01:26:18,685 - werkzeug - INFO - ************ - - [19/May/2025 01:26:18] "GET /api/notifications/count HTTP/1.1" 200 -
2025-05-19 01:26:18,704 - werkzeug - INFO - ************ - - [19/May/2025 01:26:18] "GET /api/notifications/recent HTTP/1.1" 200 -
2025-05-19 01:26:18,830 - werkzeug - INFO - ************ - - [19/May/2025 01:26:18] "GET /api/notifications/count HTTP/1.1" 200 -
2025-05-19 01:26:18,839 - werkzeug - INFO - ************ - - [19/May/2025 01:26:18] "GET /api/notifications/recent HTTP/1.1" 200 -
2025-05-19 01:26:18,859 - nobara - ERROR - استثناء: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.
معلومات الطلب: {"url": "http://************:5000/favicon.ico", "method": "GET", "headers": {"Host": "************:5000", "Connection": "keep-alive", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Accept": "image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8", "Referer": "http://************:5000/home", "Accept-Encoding": "gzip, deflate", "Accept-Language": "en-US,en;q=0.9", "Cookie": "NEXT_LOCALE=en; session=.eJztksFqwzAMhl_F-JJLKLGd2FYeY-y2laJYMklIk-E4p9J3n5u9w04F8f8SQnxC6CFvccF95F32Xw8pcjHJKW1J1vL7aKymU-OpgziNX2rcmZteVPO0zqgv6VjzdOfLJ99_Fsz8wZETr4HFNswcciVG3MW6Ccw5TcORWVR5ygtX8vqs3_A3_D_h17q8fuJ9lH1OB5dqItlLTegdUIsxsrPglA8QAAcXicB5Z0LrS7RGd69u1NqQHZqgFABqyz7GMhZVB6AG6BwZywrQ69A0PliF6FQgUp2xAWKwCJ50cJaIySKbcoXbsXP620bJ5y9x1yli.aCpb5w.1Jv5pyD8PazDruwjt3qLzvBL5O8"}, "args": {}, "form": {}, "cookies": {"NEXT_LOCALE": "en", "session": ".eJztksFqwzAMhl_F-JJLKLGd2FYeY-y2laJYMklIk-E4p9J3n5u9w04F8f8SQnxC6CFvccF95F32Xw8pcjHJKW1J1vL7aKymU-OpgziNX2rcmZteVPO0zqgv6VjzdOfLJ99_Fsz8wZETr4HFNswcciVG3MW6Ccw5TcORWVR5ygtX8vqs3_A3_D_h17q8fuJ9lH1OB5dqItlLTegdUIsxsrPglA8QAAcXicB5Z0LrS7RGd69u1NqQHZqgFABqyz7GMhZVB6AG6BwZywrQ69A0PliF6FQgUp2xAWKwCJ50cJaIySKbcoXbsXP620bJ5y9x1yli.aCpb5w.1Jv5pyD8PazDruwjt3qLzvBL5O8"}, "remote_addr": "************", "user_agent": null}
تتبع الاستثناء:
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\app.py", line 917, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\app.py", line 891, in dispatch_request
    self.raise_routing_exception(req)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\app.py", line 500, in raise_routing_exception
    raise request.routing_exception  # type: ignore[misc]
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\ctx.py", line 362, in match_request
    result = self.url_adapter.match(return_rule=True)  # type: ignore
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\werkzeug\routing\map.py", line 629, in match
    raise NotFound() from None
werkzeug.exceptions.NotFound: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.

2025-05-19 01:26:18,874 - werkzeug - INFO - ************ - - [19/May/2025 01:26:18] "[35m[1mGET /favicon.ico HTTP/1.1[0m" 500 -
2025-05-19 01:26:35,058 - werkzeug - INFO -  * Detected change in 'c:\\Users\\<USER>\\Desktop\\ملف البرنامج\\PythonCashierSystem.v2 - Copy - Copy\\PythonCashierSystem\\models\\accounting.py', reloading
2025-05-19 01:26:37,025 - werkzeug - INFO -  * Restarting with stat
2025-05-19 01:26:40,118 - app - INFO - استخدام قاعدة البيانات المحلية: sqlite:///c:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\instance\fouad_pos.db
2025-05-19 01:26:40,253 - nobara - INFO - تم بدء تشغيل التطبيق
2025-05-19 01:26:40,905 - app - WARNING - مكتبة schedule غير متوفرة. لن يتم تفعيل النسخ الاحتياطي التلقائي.
2025-05-19 01:26:40,946 - app - WARNING - تم تخطي تهيئة النسخ الاحتياطي التلقائي لأن مكتبة schedule غير متوفرة
2025-05-19 01:26:40,956 - app - INFO - عمود warehouse_id موجود بالفعل في جدول order
2025-05-19 01:26:41,242 - security - WARNING - No JWT secret provided. Generated a new one. Please set JWT_SECRET in your environment.
2025-05-19 01:26:41,496 - __main__ - INFO - جاري بدء التطبيق على العنوان: http://************:5000
2025-05-19 01:26:42,108 - werkzeug - WARNING -  * Debugger is active!
2025-05-19 01:26:42,128 - werkzeug - INFO -  * Debugger PIN: 692-333-133
2025-05-19 01:26:43,915 - werkzeug - INFO - ************ - - [19/May/2025 01:26:43] "[32mGET / HTTP/1.1[0m" 302 -
2025-05-19 01:26:44,243 - werkzeug - INFO - ************ - - [19/May/2025 01:26:44] "GET /home HTTP/1.1" 200 -
2025-05-19 01:26:44,807 - werkzeug - INFO - ************ - - [19/May/2025 01:26:44] "[36mGET /static/css/dashboard-pro.css?v=1.0.1 HTTP/1.1[0m" 304 -
2025-05-19 01:26:44,978 - werkzeug - INFO - ************ - - [19/May/2025 01:26:44] "[36mGET /static/css/nobara-pro.css?v=1.0.0 HTTP/1.1[0m" 304 -
2025-05-19 01:26:45,064 - werkzeug - INFO - ************ - - [19/May/2025 01:26:45] "[36mGET /static/js/notifications-system.js?v=1.0.0 HTTP/1.1[0m" 304 -
2025-05-19 01:26:45,176 - werkzeug - INFO - ************ - - [19/May/2025 01:26:45] "[36mGET /static/js/system-manager.js?v=1.0.0 HTTP/1.1[0m" 304 -
2025-05-19 01:26:45,196 - werkzeug - INFO - ************ - - [19/May/2025 01:26:45] "[36mGET /static/uploads/logo_20250518120852.jpeg HTTP/1.1[0m" 304 -
2025-05-19 01:26:45,206 - werkzeug - INFO - ************ - - [19/May/2025 01:26:45] "[36mGET /static/js/dashboard-manager.js?v=1.0.0 HTTP/1.1[0m" 304 -
2025-05-19 01:27:10,810 - werkzeug - INFO -  * Detected change in 'c:\\Users\\<USER>\\Desktop\\ملف البرنامج\\PythonCashierSystem.v2 - Copy - Copy\\PythonCashierSystem\\models\\accounting.py', reloading
2025-05-19 01:27:11,423 - werkzeug - INFO -  * Restarting with stat
2025-05-19 01:27:13,110 - app - INFO - استخدام قاعدة البيانات المحلية: sqlite:///c:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\instance\fouad_pos.db
2025-05-19 01:27:13,171 - nobara - INFO - تم بدء تشغيل التطبيق
2025-05-19 01:27:13,493 - app - WARNING - مكتبة schedule غير متوفرة. لن يتم تفعيل النسخ الاحتياطي التلقائي.
2025-05-19 01:27:13,520 - app - WARNING - تم تخطي تهيئة النسخ الاحتياطي التلقائي لأن مكتبة schedule غير متوفرة
2025-05-19 01:27:13,528 - app - INFO - عمود warehouse_id موجود بالفعل في جدول order
2025-05-19 01:27:13,756 - security - WARNING - No JWT secret provided. Generated a new one. Please set JWT_SECRET in your environment.
2025-05-19 01:27:13,975 - __main__ - INFO - جاري بدء التطبيق على العنوان: http://************:5000
2025-05-19 01:27:14,647 - werkzeug - WARNING -  * Debugger is active!
2025-05-19 01:27:14,668 - werkzeug - INFO -  * Debugger PIN: 692-333-133
2025-05-19 01:27:15,300 - werkzeug - INFO - ************ - - [19/May/2025 01:27:15] "[32mGET / HTTP/1.1[0m" 302 -
2025-05-19 01:27:15,537 - werkzeug - INFO - ************ - - [19/May/2025 01:27:15] "GET /home HTTP/1.1" 200 -
2025-05-19 01:27:15,952 - werkzeug - INFO - ************ - - [19/May/2025 01:27:15] "[36mGET /static/js/notifications-system.js?v=1.0.0 HTTP/1.1[0m" 304 -
2025-05-19 01:27:16,030 - werkzeug - INFO - ************ - - [19/May/2025 01:27:16] "[36mGET /static/uploads/logo_20250518120852.jpeg HTTP/1.1[0m" 304 -
2025-05-19 01:27:16,072 - werkzeug - INFO - ************ - - [19/May/2025 01:27:16] "[36mGET /static/css/dashboard-pro.css?v=1.0.1 HTTP/1.1[0m" 304 -
2025-05-19 01:27:16,107 - werkzeug - INFO - ************ - - [19/May/2025 01:27:16] "[36mGET /static/js/dashboard-pro.js?v=1.0.0 HTTP/1.1[0m" 304 -
2025-05-19 01:27:16,115 - werkzeug - INFO - ************ - - [19/May/2025 01:27:16] "[36mGET /static/js/system-manager.js?v=1.0.0 HTTP/1.1[0m" 304 -
2025-05-19 01:27:16,121 - werkzeug - INFO - ************ - - [19/May/2025 01:27:16] "[36mGET /static/css/nobara-pro.css?v=1.0.0 HTTP/1.1[0m" 304 -
2025-05-19 01:27:16,173 - werkzeug - INFO - ************ - - [19/May/2025 01:27:16] "[36mGET /static/js/dashboard-manager.js?v=1.0.0 HTTP/1.1[0m" 304 -
2025-05-19 01:27:17,279 - werkzeug - INFO - ************ - - [19/May/2025 01:27:17] "GET /api/settings HTTP/1.1" 200 -
2025-05-19 01:27:17,436 - werkzeug - INFO - ************ - - [19/May/2025 01:27:17] "GET /api/notifications/count HTTP/1.1" 200 -
2025-05-19 01:27:17,540 - werkzeug - INFO - ************ - - [19/May/2025 01:27:17] "GET /api/dashboard HTTP/1.1" 200 -
2025-05-19 01:27:17,541 - werkzeug - INFO - ************ - - [19/May/2025 01:27:17] "GET /api/sales-chart-data?days=30 HTTP/1.1" 200 -
2025-05-19 01:27:17,579 - werkzeug - INFO - ************ - - [19/May/2025 01:27:17] "GET /api/category-distribution HTTP/1.1" 200 -
2025-05-19 01:27:17,638 - werkzeug - INFO - ************ - - [19/May/2025 01:27:17] "GET /api/settings HTTP/1.1" 200 -
2025-05-19 01:27:17,750 - werkzeug - INFO - ************ - - [19/May/2025 01:27:17] "GET /api/dashboard HTTP/1.1" 200 -
2025-05-19 01:27:17,875 - werkzeug - INFO - ************ - - [19/May/2025 01:27:17] "GET /api/notifications/recent HTTP/1.1" 200 -
2025-05-19 01:27:17,879 - werkzeug - INFO - ************ - - [19/May/2025 01:27:17] "GET /api/notifications/count HTTP/1.1" 200 -
2025-05-19 01:27:17,942 - nobara - ERROR - استثناء: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.
معلومات الطلب: {"url": "http://************:5000/favicon.ico", "method": "GET", "headers": {"Host": "************:5000", "Connection": "keep-alive", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Accept": "image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8", "Referer": "http://************:5000/home", "Accept-Encoding": "gzip, deflate", "Accept-Language": "en-US,en;q=0.9", "Cookie": "NEXT_LOCALE=en; session=.eJztksFqwzAMhl_F-JJLKLGd2FYeY-y2laJYMklIk-E4p9J3n5u9w04F8f8SQnxC6CFvccF95F32Xw8pcjHJKW1J1vL7aKymU-OpgziNX2rcmZteVPO0zqgv6VjzdOfLJ99_Fsz8wZETr4HFNswcciVG3MW6Ccw5TcORWVR5ygtX8vqs3_A3_D_h17q8fuJ9lH1OB5dqItlLTegdUIsxsrPglA8QAAcXicB5Z0LrS7RGd69u1NqQHZqgFABqyz7GMhZVB6AG6BwZywrQ69A0PliF6FQgUp2xAWKwCJ50cJaIySKbcoXbsXP620bJ5y9x1yli.aCpb5w.1Jv5pyD8PazDruwjt3qLzvBL5O8"}, "args": {}, "form": {}, "cookies": {"NEXT_LOCALE": "en", "session": ".eJztksFqwzAMhl_F-JJLKLGd2FYeY-y2laJYMklIk-E4p9J3n5u9w04F8f8SQnxC6CFvccF95F32Xw8pcjHJKW1J1vL7aKymU-OpgziNX2rcmZteVPO0zqgv6VjzdOfLJ99_Fsz8wZETr4HFNswcciVG3MW6Ccw5TcORWVR5ygtX8vqs3_A3_D_h17q8fuJ9lH1OB5dqItlLTegdUIsxsrPglA8QAAcXicB5Z0LrS7RGd69u1NqQHZqgFABqyz7GMhZVB6AG6BwZywrQ69A0PliF6FQgUp2xAWKwCJ50cJaIySKbcoXbsXP620bJ5y9x1yli.aCpb5w.1Jv5pyD8PazDruwjt3qLzvBL5O8"}, "remote_addr": "************", "user_agent": null}
تتبع الاستثناء:
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\app.py", line 917, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\app.py", line 891, in dispatch_request
    self.raise_routing_exception(req)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\app.py", line 500, in raise_routing_exception
    raise request.routing_exception  # type: ignore[misc]
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\ctx.py", line 362, in match_request
    result = self.url_adapter.match(return_rule=True)  # type: ignore
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\werkzeug\routing\map.py", line 629, in match
    raise NotFound() from None
werkzeug.exceptions.NotFound: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.

2025-05-19 01:27:17,991 - werkzeug - INFO - ************ - - [19/May/2025 01:27:17] "[35m[1mGET /favicon.ico HTTP/1.1[0m" 500 -
2025-05-19 01:27:18,002 - werkzeug - INFO - ************ - - [19/May/2025 01:27:18] "GET /api/notifications/recent HTTP/1.1" 200 -
2025-05-19 01:27:18,175 - werkzeug - INFO - ************ - - [19/May/2025 01:27:18] "GET /api/notifications/count HTTP/1.1" 200 -
2025-05-19 01:27:39,501 - werkzeug - INFO -  * Detected change in 'c:\\Users\\<USER>\\Desktop\\ملف البرنامج\\PythonCashierSystem.v2 - Copy - Copy\\PythonCashierSystem\\models\\accounting.py', reloading
2025-05-19 01:27:40,356 - werkzeug - INFO -  * Restarting with stat
2025-05-19 01:27:43,345 - app - INFO - استخدام قاعدة البيانات المحلية: sqlite:///c:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\instance\fouad_pos.db
2025-05-19 01:27:43,470 - nobara - INFO - تم بدء تشغيل التطبيق
2025-05-19 01:27:46,011 - app - WARNING - مكتبة schedule غير متوفرة. لن يتم تفعيل النسخ الاحتياطي التلقائي.
2025-05-19 01:27:54,758 - app - WARNING - تم تخطي تهيئة النسخ الاحتياطي التلقائي لأن مكتبة schedule غير متوفرة
2025-05-19 01:27:54,785 - app - INFO - عمود warehouse_id موجود بالفعل في جدول order
2025-05-19 01:27:55,135 - security - WARNING - No JWT secret provided. Generated a new one. Please set JWT_SECRET in your environment.
2025-05-19 01:27:55,538 - __main__ - INFO - جاري بدء التطبيق على العنوان: http://************:5000
2025-05-19 01:27:56,597 - werkzeug - WARNING -  * Debugger is active!
2025-05-19 01:27:56,614 - werkzeug - INFO -  * Debugger PIN: 692-333-133
2025-05-19 01:27:57,872 - werkzeug - INFO - ************ - - [19/May/2025 01:27:57] "[32mGET / HTTP/1.1[0m" 302 -
2025-05-19 01:27:58,265 - werkzeug - INFO - ************ - - [19/May/2025 01:27:58] "GET /home HTTP/1.1" 200 -
2025-05-19 01:27:58,956 - werkzeug - INFO - ************ - - [19/May/2025 01:27:58] "[36mGET /static/css/nobara-pro.css?v=1.0.0 HTTP/1.1[0m" 304 -
2025-05-19 01:27:58,979 - werkzeug - INFO - ************ - - [19/May/2025 01:27:58] "[36mGET /static/css/dashboard-pro.css?v=1.0.1 HTTP/1.1[0m" 304 -
2025-05-19 01:27:59,024 - werkzeug - INFO - ************ - - [19/May/2025 01:27:59] "[36mGET /static/js/notifications-system.js?v=1.0.0 HTTP/1.1[0m" 304 -
2025-05-19 01:27:59,045 - werkzeug - INFO - ************ - - [19/May/2025 01:27:59] "[36mGET /static/js/system-manager.js?v=1.0.0 HTTP/1.1[0m" 304 -
2025-05-19 01:27:59,317 - werkzeug - INFO - ************ - - [19/May/2025 01:27:59] "[36mGET /static/uploads/logo_20250518120852.jpeg HTTP/1.1[0m" 304 -
2025-05-19 01:27:59,367 - werkzeug - INFO - ************ - - [19/May/2025 01:27:59] "[36mGET /static/js/dashboard-manager.js?v=1.0.0 HTTP/1.1[0m" 304 -
2025-05-19 01:28:06,144 - werkzeug - INFO -  * Detected change in 'c:\\Users\\<USER>\\Desktop\\ملف البرنامج\\PythonCashierSystem.v2 - Copy - Copy\\PythonCashierSystem\\models\\accounting.py', reloading
2025-05-19 01:28:07,877 - werkzeug - INFO -  * Restarting with stat
2025-05-19 01:28:10,512 - app - INFO - استخدام قاعدة البيانات المحلية: sqlite:///c:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\instance\fouad_pos.db
2025-05-19 01:28:10,641 - nobara - INFO - تم بدء تشغيل التطبيق
2025-05-19 01:51:42,868 - app - INFO - استخدام قاعدة البيانات المحلية: sqlite:///instance/fouad_pos.db
2025-05-19 01:51:43,493 - nobara - INFO - تم بدء تشغيل التطبيق
2025-05-19 01:51:44,420 - app - ERROR - خطأ أثناء تهيئة النظام المحاسبي: Table 'budget' is already defined for this MetaData instance.  Specify 'extend_existing=True' to redefine options and columns on an existing Table object.
2025-05-19 01:57:00,241 - app - INFO - استخدام قاعدة البيانات المحلية: sqlite:///instance/fouad_pos.db
2025-05-19 01:57:00,308 - nobara - INFO - تم بدء تشغيل التطبيق
2025-05-19 01:57:00,723 - app - ERROR - خطأ أثناء تهيئة النظام المحاسبي: Table 'accounting_payment_method' is already defined for this MetaData instance.  Specify 'extend_existing=True' to redefine options and columns on an existing Table object.
2025-05-19 02:00:48,517 - app - INFO - استخدام قاعدة البيانات المحلية: sqlite:///instance/fouad_pos.db
2025-05-19 02:00:48,575 - nobara - INFO - تم بدء تشغيل التطبيق
2025-05-19 02:00:48,852 - app - ERROR - خطأ أثناء تهيئة النظام المحاسبي: Table 'accounting_payment_method' is already defined for this MetaData instance.  Specify 'extend_existing=True' to redefine options and columns on an existing Table object.
2025-05-19 02:16:36,864 - app - INFO - استخدام قاعدة البيانات المحلية: sqlite:///instance/fouad_pos.db
2025-05-19 02:16:36,989 - nobara - INFO - تم بدء تشغيل التطبيق
2025-05-19 02:16:37,630 - app - ERROR - خطأ أثناء تهيئة النظام المحاسبي: Table 'cost_center' is already defined for this MetaData instance.  Specify 'extend_existing=True' to redefine options and columns on an existing Table object.
2025-05-19 02:20:12,026 - app - INFO - استخدام قاعدة البيانات المحلية: sqlite:///instance/fouad_pos.db
2025-05-19 02:20:12,092 - nobara - INFO - تم بدء تشغيل التطبيق
2025-05-19 02:20:12,434 - app - ERROR - خطأ أثناء تهيئة النظام المحاسبي: Table 'cost_center' is already defined for this MetaData instance.  Specify 'extend_existing=True' to redefine options and columns on an existing Table object.
2025-05-19 02:23:12,496 - app - INFO - استخدام قاعدة البيانات المحلية: sqlite:///instance/fouad_pos.db
2025-05-19 02:23:16,438 - nobara - INFO - تم بدء تشغيل التطبيق
2025-05-19 02:23:18,848 - app - ERROR - خطأ أثناء تهيئة النظام المحاسبي: Table 'cost_center' is already defined for this MetaData instance.  Specify 'extend_existing=True' to redefine options and columns on an existing Table object.
2025-05-19 02:27:07,698 - app - INFO - استخدام قاعدة البيانات المحلية: sqlite:///instance/fouad_pos.db
2025-05-19 02:27:07,755 - nobara - INFO - تم بدء تشغيل التطبيق
2025-05-19 02:27:08,113 - app - ERROR - خطأ أثناء تهيئة النظام المحاسبي: Table 'cost_center' is already defined for this MetaData instance.  Specify 'extend_existing=True' to redefine options and columns on an existing Table object.
2025-05-19 02:28:08,960 - app - INFO - استخدام قاعدة البيانات المحلية: sqlite:///instance/fouad_pos.db
2025-05-19 02:28:09,069 - nobara - INFO - تم بدء تشغيل التطبيق
2025-05-19 02:28:09,464 - app - ERROR - خطأ أثناء تهيئة النظام المحاسبي: Table 'cost_center' is already defined for this MetaData instance.  Specify 'extend_existing=True' to redefine options and columns on an existing Table object.
2025-05-19 02:28:14,439 - app - INFO - استخدام قاعدة البيانات المحلية: sqlite:///instance/fouad_pos.db
2025-05-19 02:28:14,493 - nobara - INFO - تم بدء تشغيل التطبيق
2025-05-19 02:28:14,772 - app - ERROR - خطأ أثناء تهيئة النظام المحاسبي: Table 'cost_center' is already defined for this MetaData instance.  Specify 'extend_existing=True' to redefine options and columns on an existing Table object.
2025-05-19 02:29:02,593 - app - INFO - استخدام قاعدة البيانات المحلية: sqlite:///instance/fouad_pos.db
2025-05-19 02:29:02,647 - nobara - INFO - تم بدء تشغيل التطبيق
2025-05-19 02:29:03,016 - app - ERROR - خطأ أثناء تهيئة النظام المحاسبي: Table 'cost_center' is already defined for this MetaData instance.  Specify 'extend_existing=True' to redefine options and columns on an existing Table object.
2025-05-19 23:38:04,056 - app - INFO - استخدام قاعدة البيانات المحلية: sqlite:///instance/fouad_pos.db
2025-05-19 23:38:05,091 - nobara - INFO - تم بدء تشغيل التطبيق
2025-05-19 23:38:05,833 - app - ERROR - خطأ أثناء تهيئة النظام المحاسبي: Table 'cost_center' is already defined for this MetaData instance.  Specify 'extend_existing=True' to redefine options and columns on an existing Table object.
2025-05-19 23:39:56,271 - app - INFO - استخدام قاعدة البيانات المحلية: sqlite:///instance/fouad_pos.db
2025-05-19 23:39:56,332 - nobara - INFO - تم بدء تشغيل التطبيق
2025-05-19 23:39:57,387 - app - ERROR - خطأ أثناء تهيئة النظام المحاسبي: Table 'cost_center' is already defined for this MetaData instance.  Specify 'extend_existing=True' to redefine options and columns on an existing Table object.
2025-05-19 23:40:24,041 - app - INFO - استخدام قاعدة البيانات المحلية: sqlite:///instance/fouad_pos.db
2025-05-19 23:40:24,113 - nobara - INFO - تم بدء تشغيل التطبيق
2025-05-19 23:40:24,737 - app - ERROR - خطأ أثناء تهيئة النظام المحاسبي: Table 'cost_center' is already defined for this MetaData instance.  Specify 'extend_existing=True' to redefine options and columns on an existing Table object.
2025-05-19 23:42:05,889 - app - INFO - استخدام قاعدة البيانات المحلية: sqlite:///instance/fouad_pos.db
2025-05-19 23:42:05,963 - nobara - INFO - تم بدء تشغيل التطبيق
2025-05-19 23:42:06,389 - app - ERROR - خطأ أثناء تهيئة النظام المحاسبي: Table 'cost_center' is already defined for this MetaData instance.  Specify 'extend_existing=True' to redefine options and columns on an existing Table object.
2025-05-19 23:42:52,451 - app - INFO - استخدام قاعدة البيانات المحلية: sqlite:///instance/fouad_pos.db
2025-05-19 23:42:52,984 - nobara - INFO - تم بدء تشغيل التطبيق
2025-05-19 23:42:53,369 - app - ERROR - خطأ أثناء تهيئة النظام المحاسبي: Table 'cost_center' is already defined for this MetaData instance.  Specify 'extend_existing=True' to redefine options and columns on an existing Table object.
2025-05-19 23:43:32,667 - app - INFO - استخدام قاعدة البيانات المحلية: sqlite:///instance/fouad_pos.db
2025-05-19 23:43:32,742 - nobara - INFO - تم بدء تشغيل التطبيق
2025-05-19 23:43:33,238 - app - ERROR - خطأ أثناء تهيئة النظام المحاسبي: Table 'cost_center' is already defined for this MetaData instance.  Specify 'extend_existing=True' to redefine options and columns on an existing Table object.
2025-05-19 23:44:05,918 - app - INFO - استخدام قاعدة البيانات المحلية: sqlite:///instance/fouad_pos.db
2025-05-19 23:44:05,993 - nobara - INFO - تم بدء تشغيل التطبيق
2025-05-19 23:44:06,345 - app - ERROR - خطأ أثناء تهيئة النظام المحاسبي: Table 'cost_center' is already defined for this MetaData instance.  Specify 'extend_existing=True' to redefine options and columns on an existing Table object.
2025-05-19 23:44:41,173 - app - INFO - استخدام قاعدة البيانات المحلية: sqlite:///instance/fouad_pos.db
2025-05-19 23:44:41,290 - nobara - INFO - تم بدء تشغيل التطبيق
2025-05-19 23:44:41,699 - app - ERROR - خطأ أثناء تهيئة النظام المحاسبي: Table 'cost_center' is already defined for this MetaData instance.  Specify 'extend_existing=True' to redefine options and columns on an existing Table object.
2025-05-19 23:44:47,188 - app - INFO - استخدام قاعدة البيانات المحلية: sqlite:///instance/fouad_pos.db
2025-05-19 23:44:47,268 - nobara - INFO - تم بدء تشغيل التطبيق
2025-05-19 23:44:47,605 - app - ERROR - خطأ أثناء تهيئة النظام المحاسبي: Table 'cost_center' is already defined for this MetaData instance.  Specify 'extend_existing=True' to redefine options and columns on an existing Table object.
2025-05-19 23:45:17,028 - app - INFO - استخدام قاعدة البيانات المحلية: sqlite:///instance/fouad_pos.db
2025-05-19 23:45:17,096 - nobara - INFO - تم بدء تشغيل التطبيق
2025-05-19 23:45:17,476 - app - ERROR - خطأ أثناء تهيئة النظام المحاسبي: Table 'cost_center' is already defined for this MetaData instance.  Specify 'extend_existing=True' to redefine options and columns on an existing Table object.
2025-05-19 23:45:51,242 - app - INFO - استخدام قاعدة البيانات المحلية: sqlite:///instance/fouad_pos.db
2025-05-19 23:45:53,989 - nobara - INFO - تم بدء تشغيل التطبيق
2025-05-19 23:45:54,321 - app - ERROR - خطأ أثناء تهيئة النظام المحاسبي: Table 'cost_center' is already defined for this MetaData instance.  Specify 'extend_existing=True' to redefine options and columns on an existing Table object.
2025-05-19 23:46:40,689 - app - INFO - استخدام قاعدة البيانات المحلية: sqlite:///instance/fouad_pos.db
2025-05-19 23:46:40,778 - nobara - INFO - تم بدء تشغيل التطبيق
2025-05-19 23:46:41,191 - app - ERROR - خطأ أثناء تهيئة النظام المحاسبي: Table 'cost_center' is already defined for this MetaData instance.  Specify 'extend_existing=True' to redefine options and columns on an existing Table object.
2025-05-19 23:48:59,296 - app - INFO - استخدام قاعدة البيانات المحلية: sqlite:///instance/fouad_pos.db
2025-05-19 23:48:59,392 - nobara - INFO - تم بدء تشغيل التطبيق
2025-05-19 23:48:59,928 - app - INFO - تم تهيئة النظام المحاسبي بنجاح
2025-05-19 23:49:37,124 - app - INFO - استخدام قاعدة البيانات المحلية: sqlite:///instance/fouad_pos.db
2025-05-19 23:49:37,183 - nobara - INFO - تم بدء تشغيل التطبيق
2025-05-19 23:49:37,607 - app - INFO - تم تهيئة النظام المحاسبي بنجاح
2025-05-19 23:50:05,268 - app - INFO - استخدام قاعدة البيانات المحلية: sqlite:///instance/fouad_pos.db
2025-05-19 23:50:05,335 - nobara - INFO - تم بدء تشغيل التطبيق
2025-05-19 23:50:05,735 - app - INFO - تم تهيئة النظام المحاسبي بنجاح
2025-05-19 23:50:48,691 - app - INFO - استخدام قاعدة البيانات المحلية: sqlite:///instance/fouad_pos.db
2025-05-19 23:50:48,761 - nobara - INFO - تم بدء تشغيل التطبيق
2025-05-19 23:50:49,173 - app - INFO - تم تهيئة النظام المحاسبي بنجاح
2025-05-19 23:52:30,382 - app - INFO - استخدام قاعدة البيانات المحلية: sqlite:///instance/fouad_pos.db
2025-05-19 23:52:30,447 - nobara - INFO - تم بدء تشغيل التطبيق
2025-05-19 23:52:30,809 - app - INFO - تم تهيئة النظام المحاسبي بنجاح
2025-05-19 23:54:13,799 - app - INFO - استخدام قاعدة البيانات المحلية: sqlite:///instance/fouad_pos.db
2025-05-19 23:54:13,855 - nobara - INFO - تم بدء تشغيل التطبيق
2025-05-19 23:54:14,191 - app - INFO - تم تهيئة النظام المحاسبي بنجاح
2025-05-19 23:54:16,116 - app - INFO - تم تهيئة جدولة النسخ الاحتياطي التلقائي
2025-05-19 23:54:16,150 - app - INFO - إضافة عمود warehouse_id إلى جدول order...
2025-05-19 23:54:16,154 - app - ERROR - خطأ أثناء التحقق من عمود warehouse_id: no such table: order
2025-05-19 23:54:16,188 - security - WARNING - No JWT secret provided. Generated a new one. Please set JWT_SECRET in your environment.
2025-05-19 23:54:16,374 - __main__ - INFO - جاري بدء التطبيق على العنوان: http://************:5000
2025-05-19 23:54:20,060 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://************:5000
2025-05-19 23:54:20,061 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-19 23:54:20,125 - werkzeug - INFO -  * Restarting with stat
2025-05-19 23:54:22,964 - app - INFO - استخدام قاعدة البيانات المحلية: sqlite:///instance/fouad_pos.db
2025-05-19 23:54:23,049 - nobara - INFO - تم بدء تشغيل التطبيق
2025-05-19 23:54:23,446 - app - INFO - تم تهيئة النظام المحاسبي بنجاح
2025-05-19 23:54:23,698 - app - INFO - تم تهيئة جدولة النسخ الاحتياطي التلقائي
2025-05-19 23:54:23,702 - app - INFO - إضافة عمود warehouse_id إلى جدول order...
2025-05-19 23:54:23,708 - app - ERROR - خطأ أثناء التحقق من عمود warehouse_id: no such table: order
2025-05-19 23:54:23,724 - security - WARNING - No JWT secret provided. Generated a new one. Please set JWT_SECRET in your environment.
2025-05-19 23:54:23,945 - __main__ - INFO - جاري بدء التطبيق على العنوان: http://************:5000
2025-05-19 23:54:24,782 - werkzeug - WARNING -  * Debugger is active!
2025-05-19 23:54:25,092 - werkzeug - INFO -  * Debugger PIN: 692-333-133
2025-05-19 23:54:25,726 - werkzeug - INFO - ************ - - [19/May/2025 23:54:25] "[32mGET / HTTP/1.1[0m" 302 -
2025-05-19 23:54:25,770 - werkzeug - INFO - ************ - - [19/May/2025 23:54:25] "[32mGET / HTTP/1.1[0m" 302 -
2025-05-19 23:54:29,445 - werkzeug - INFO - ************ - - [19/May/2025 23:54:29] "GET /login HTTP/1.1" 200 -
2025-05-19 23:54:29,468 - werkzeug - INFO - ************ - - [19/May/2025 23:54:29] "GET /login HTTP/1.1" 200 -
2025-05-19 23:54:30,234 - werkzeug - INFO - ************ - - [19/May/2025 23:54:30] "GET /static/css/nobara-pro.css?v=1.0.0 HTTP/1.1" 200 -
2025-05-19 23:54:30,235 - werkzeug - INFO - ************ - - [19/May/2025 23:54:30] "GET /static/css/dashboard-pro.css?v=1.0.1 HTTP/1.1" 200 -
2025-05-19 23:54:30,249 - werkzeug - INFO - ************ - - [19/May/2025 23:54:30] "[36mGET /static/css/nobara-pro.css?v=1.0.0 HTTP/1.1[0m" 304 -
2025-05-19 23:54:30,251 - werkzeug - INFO - ************ - - [19/May/2025 23:54:30] "[36mGET /static/css/dashboard-pro.css?v=1.0.1 HTTP/1.1[0m" 304 -
2025-05-19 23:54:30,546 - werkzeug - INFO - ************ - - [19/May/2025 23:54:30] "GET /static/js/notifications-system.js?v=1.0.0 HTTP/1.1" 200 -
2025-05-19 23:54:30,650 - werkzeug - INFO - ************ - - [19/May/2025 23:54:30] "[36mGET /static/js/notifications-system.js?v=1.0.0 HTTP/1.1[0m" 304 -
2025-05-19 23:54:30,664 - werkzeug - INFO - ************ - - [19/May/2025 23:54:30] "GET /static/uploads/logo_20250518120852.jpeg HTTP/1.1" 200 -
2025-05-19 23:54:30,690 - werkzeug - INFO - ************ - - [19/May/2025 23:54:30] "[36mGET /static/uploads/logo_20250518120852.jpeg HTTP/1.1[0m" 304 -
2025-05-19 23:54:30,730 - werkzeug - INFO - ************ - - [19/May/2025 23:54:30] "GET /static/js/system-manager.js?v=1.0.0 HTTP/1.1" 200 -
2025-05-19 23:54:30,738 - werkzeug - INFO - ************ - - [19/May/2025 23:54:30] "[36mGET /static/js/system-manager.js?v=1.0.0 HTTP/1.1[0m" 304 -
2025-05-19 23:54:31,221 - werkzeug - INFO - ************ - - [19/May/2025 23:54:31] "GET /api/settings HTTP/1.1" 200 -
2025-05-19 23:54:31,500 - werkzeug - INFO - ************ - - [19/May/2025 23:54:31] "GET /api/settings HTTP/1.1" 200 -
2025-05-19 23:54:31,718 - werkzeug - INFO - ************ - - [19/May/2025 23:54:31] "[32mGET /api/notifications/count HTTP/1.1[0m" 302 -
2025-05-19 23:54:31,773 - werkzeug - INFO - ************ - - [19/May/2025 23:54:31] "[32mGET /api/notifications/recent HTTP/1.1[0m" 302 -
2025-05-19 23:54:31,804 - werkzeug - INFO - ************ - - [19/May/2025 23:54:31] "GET /login?next=/api/notifications/count HTTP/1.1" 200 -
2025-05-19 23:54:31,807 - werkzeug - INFO - ************ - - [19/May/2025 23:54:31] "GET /login?next=/api/notifications/recent HTTP/1.1" 200 -
2025-05-19 23:54:32,001 - nobara - ERROR - استثناء: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.
معلومات الطلب: {"url": "http://************:5000/favicon.ico", "method": "GET", "headers": {"Host": "************:5000", "Connection": "keep-alive", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Accept": "image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8", "Referer": "http://************:5000/login", "Accept-Encoding": "gzip, deflate", "Accept-Language": "en-US,en;q=0.9", "Cookie": "NEXT_LOCALE=en; session=.eJztksFqwzAMhl_F-JJLKLGd2FYeY-y2laJYMklIk-E4p9J3n5u9w04F8f8SQnxC6CFvccF95F32Xw8pcjHJKW1J1vL7aKymU-OpgziNX2rcmZteVPO0zqgv6VjzdOfLJ99_Fsz8wZETr4HFNswcciVG3MW6Ccw5TcORWVR5ygtX8vqs3_A3_D_h17q8fuJ9lH1OB5dqItlLTegdUIsxsrPglA8QAAcXicB5Z0LrS7RGd69u1NqQHZqgFABqyz7GMhZVB6AG6BwZywrQ69A0PliF6FQgUp2xAWKwCJ50cJaIySKbcoXbsXP620bJ5y9x1yli.aCpb5w.1Jv5pyD8PazDruwjt3qLzvBL5O8"}, "args": {}, "form": {}, "cookies": {"NEXT_LOCALE": "en", "session": ".eJztksFqwzAMhl_F-JJLKLGd2FYeY-y2laJYMklIk-E4p9J3n5u9w04F8f8SQnxC6CFvccF95F32Xw8pcjHJKW1J1vL7aKymU-OpgziNX2rcmZteVPO0zqgv6VjzdOfLJ99_Fsz8wZETr4HFNswcciVG3MW6Ccw5TcORWVR5ygtX8vqs3_A3_D_h17q8fuJ9lH1OB5dqItlLTegdUIsxsrPglA8QAAcXicB5Z0LrS7RGd69u1NqQHZqgFABqyz7GMhZVB6AG6BwZywrQ69A0PliF6FQgUp2xAWKwCJ50cJaIySKbcoXbsXP620bJ5y9x1yli.aCpb5w.1Jv5pyD8PazDruwjt3qLzvBL5O8"}, "remote_addr": "************", "user_agent": null}
تتبع الاستثناء:
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\app.py", line 917, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\app.py", line 891, in dispatch_request
    self.raise_routing_exception(req)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\app.py", line 500, in raise_routing_exception
    raise request.routing_exception  # type: ignore[misc]
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\ctx.py", line 362, in match_request
    result = self.url_adapter.match(return_rule=True)  # type: ignore
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\werkzeug\routing\map.py", line 629, in match
    raise NotFound() from None
werkzeug.exceptions.NotFound: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.

2025-05-19 23:54:32,051 - werkzeug - INFO - ************ - - [19/May/2025 23:54:32] "[35m[1mGET /favicon.ico HTTP/1.1[0m" 500 -
2025-05-19 23:54:32,287 - werkzeug - INFO - ************ - - [19/May/2025 23:54:32] "[32mGET /api/notifications/count HTTP/1.1[0m" 302 -
2025-05-19 23:54:32,312 - werkzeug - INFO - ************ - - [19/May/2025 23:54:32] "[32mGET /api/notifications/recent HTTP/1.1[0m" 302 -
2025-05-19 23:54:32,387 - werkzeug - INFO - ************ - - [19/May/2025 23:54:32] "GET /login?next=/api/notifications/recent HTTP/1.1" 200 -
2025-05-19 23:54:32,387 - werkzeug - INFO - ************ - - [19/May/2025 23:54:32] "GET /login?next=/api/notifications/count HTTP/1.1" 200 -
2025-05-19 23:54:32,389 - nobara - ERROR - استثناء: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.
معلومات الطلب: {"url": "http://************:5000/favicon.ico", "method": "GET", "headers": {"Host": "************:5000", "Connection": "keep-alive", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Accept": "image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8", "Referer": "http://************:5000/login", "Accept-Encoding": "gzip, deflate", "Accept-Language": "en-US,en;q=0.9", "Cookie": "NEXT_LOCALE=en; session=.eJydjbsJwDAMBVcRrzYZwFOkDyYII39AiUPkznj3eIZUr7g73sCZlK2IwR8D1NfgEjPOAoddhU1IW6Z6U2_EMS5IvVSjZzkbwnQ_u-DW-StW4BOryfwADbYvmA.aCuaiA.0cG9DOnDuDGbcxGno9kvEFNkR9E"}, "args": {}, "form": {}, "cookies": {"NEXT_LOCALE": "en", "session": ".eJydjbsJwDAMBVcRrzYZwFOkDyYII39AiUPkznj3eIZUr7g73sCZlK2IwR8D1NfgEjPOAoddhU1IW6Z6U2_EMS5IvVSjZzkbwnQ_u-DW-StW4BOryfwADbYvmA.aCuaiA.0cG9DOnDuDGbcxGno9kvEFNkR9E"}, "remote_addr": "************", "user_agent": null}
تتبع الاستثناء:
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\app.py", line 917, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\app.py", line 891, in dispatch_request
    self.raise_routing_exception(req)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\app.py", line 500, in raise_routing_exception
    raise request.routing_exception  # type: ignore[misc]
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\ctx.py", line 362, in match_request
    result = self.url_adapter.match(return_rule=True)  # type: ignore
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\werkzeug\routing\map.py", line 629, in match
    raise NotFound() from None
werkzeug.exceptions.NotFound: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.

2025-05-19 23:54:32,405 - werkzeug - INFO - ************ - - [19/May/2025 23:54:32] "[35m[1mGET /favicon.ico HTTP/1.1[0m" 500 -
2025-05-19 23:54:37,743 - nobara - ERROR - استثناء: (sqlite3.OperationalError) unable to open database file
(Background on this error at: https://sqlalche.me/e/20/e3q8)
معلومات الطلب: {"url": "http://************:5000/login", "method": "POST", "headers": {"Host": "************:5000", "Connection": "keep-alive", "Content-Length": "29", "Cache-Control": "max-age=0", "Origin": "http://************:5000", "Content-Type": "application/x-www-form-urlencoded", "Upgrade-Insecure-Requests": "1", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7", "Referer": "http://************:5000/login", "Accept-Encoding": "gzip, deflate", "Accept-Language": "en-US,en;q=0.9", "Cookie": "NEXT_LOCALE=en; session=eyJfZnJlc2giOmZhbHNlfQ.aCuaiA.agL3BDgsq9tWX6Vnf9mVediOAHY"}, "args": {}, "form": {"username": "admin", "password": "admin"}, "cookies": {"NEXT_LOCALE": "en", "session": "eyJfZnJlc2giOmZhbHNlfQ.aCuaiA.agL3BDgsq9tWX6Vnf9mVediOAHY"}, "remote_addr": "************", "user_agent": null}
تتبع الاستثناء:
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 146, in __init__
    self._dbapi_connection = engine.raw_connection()
                             ~~~~~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 3298, in raw_connection
    return self.pool.connect()
           ~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\sqlalchemy\pool\base.py", line 449, in connect
    return _ConnectionFairy._checkout(self)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\sqlalchemy\pool\base.py", line 1264, in _checkout
    fairy = _ConnectionRecord.checkout(pool)
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\sqlalchemy\pool\base.py", line 713, in checkout
    rec = pool._do_get()
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\sqlalchemy\pool\impl.py", line 179, in _do_get
    with util.safe_reraise():
         ~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\sqlalchemy\util\langhelpers.py", line 146, in __exit__
    raise exc_value.with_traceback(exc_tb)
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\sqlalchemy\pool\impl.py", line 177, in _do_get
    return self._create_connection()
           ~~~~~~~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\sqlalchemy\pool\base.py", line 390, in _create_connection
    return _ConnectionRecord(self)
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\sqlalchemy\pool\base.py", line 675, in __init__
    self.__connect()
    ~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\sqlalchemy\pool\base.py", line 901, in __connect
    with util.safe_reraise():
         ~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\sqlalchemy\util\langhelpers.py", line 146, in __exit__
    raise exc_value.with_traceback(exc_tb)
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\sqlalchemy\pool\base.py", line 897, in __connect
    self.dbapi_connection = connection = pool._invoke_creator(self)
                                         ~~~~~~~~~~~~~~~~~~~~^^^^^^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\sqlalchemy\engine\create.py", line 646, in connect
    return dialect.connect(*cargs, **cparams)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\sqlalchemy\engine\default.py", line 625, in connect
    return self.loaded_dbapi.connect(*cargs, **cparams)  # type: ignore[no-any-return]  # NOQA: E501
           ~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^
sqlite3.OperationalError: unable to open database file

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\app.py", line 917, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\app.py", line 902, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)  # type: ignore[no-any-return]
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\routes\auth.py", line 27, in login
    user = User.query.filter_by(username=username).first()
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\sqlalchemy\orm\query.py", line 2759, in first
    return self.limit(1)._iter().first()  # type: ignore
           ~~~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\sqlalchemy\orm\query.py", line 2858, in _iter
    result: Union[ScalarResult[_T], Result[_T]] = self.session.execute(
                                                  ~~~~~~~~~~~~~~~~~~~~^
        statement,
        ^^^^^^^^^^
        params,
        ^^^^^^^
        execution_options={"_sa_orm_load_options": self.load_options},
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\sqlalchemy\orm\session.py", line 2365, in execute
    return self._execute_internal(
           ~~~~~~~~~~~~~~~~~~~~~~^
        statement,
        ^^^^^^^^^^
    ...<4 lines>...
        _add_event=_add_event,
        ^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\sqlalchemy\orm\session.py", line 2241, in _execute_internal
    conn = self._connection_for_bind(bind)
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\sqlalchemy\orm\session.py", line 2110, in _connection_for_bind
    return trans._connection_for_bind(engine, execution_options)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<string>", line 2, in _connection_for_bind
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\sqlalchemy\orm\state_changes.py", line 139, in _go
    ret_value = fn(self, *arg, **kw)
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\sqlalchemy\orm\session.py", line 1189, in _connection_for_bind
    conn = bind.connect()
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 3274, in connect
    return self._connection_cls(self)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 148, in __init__
    Connection._handle_dbapi_exception_noconnection(
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        err, dialect, engine
        ^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 2439, in _handle_dbapi_exception_noconnection
    raise sqlalchemy_exception.with_traceback(exc_info[2]) from e
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 146, in __init__
    self._dbapi_connection = engine.raw_connection()
                             ~~~~~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 3298, in raw_connection
    return self.pool.connect()
           ~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\sqlalchemy\pool\base.py", line 449, in connect
    return _ConnectionFairy._checkout(self)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\sqlalchemy\pool\base.py", line 1264, in _checkout
    fairy = _ConnectionRecord.checkout(pool)
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\sqlalchemy\pool\base.py", line 713, in checkout
    rec = pool._do_get()
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\sqlalchemy\pool\impl.py", line 179, in _do_get
    with util.safe_reraise():
         ~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\sqlalchemy\util\langhelpers.py", line 146, in __exit__
    raise exc_value.with_traceback(exc_tb)
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\sqlalchemy\pool\impl.py", line 177, in _do_get
    return self._create_connection()
           ~~~~~~~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\sqlalchemy\pool\base.py", line 390, in _create_connection
    return _ConnectionRecord(self)
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\sqlalchemy\pool\base.py", line 675, in __init__
    self.__connect()
    ~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\sqlalchemy\pool\base.py", line 901, in __connect
    with util.safe_reraise():
         ~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\sqlalchemy\util\langhelpers.py", line 146, in __exit__
    raise exc_value.with_traceback(exc_tb)
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\sqlalchemy\pool\base.py", line 897, in __connect
    self.dbapi_connection = connection = pool._invoke_creator(self)
                                         ~~~~~~~~~~~~~~~~~~~~^^^^^^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\sqlalchemy\engine\create.py", line 646, in connect
    return dialect.connect(*cargs, **cparams)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\sqlalchemy\engine\default.py", line 625, in connect
    return self.loaded_dbapi.connect(*cargs, **cparams)  # type: ignore[no-any-return]  # NOQA: E501
           ~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^
sqlalchemy.exc.OperationalError: (sqlite3.OperationalError) unable to open database file
(Background on this error at: https://sqlalche.me/e/20/e3q8)

2025-05-19 23:54:37,778 - werkzeug - INFO - ************ - - [19/May/2025 23:54:37] "[35m[1mPOST /login HTTP/1.1[0m" 500 -
2025-05-19 23:54:37,869 - nobara - ERROR - استثناء: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.
معلومات الطلب: {"url": "http://************:5000/favicon.ico", "method": "GET", "headers": {"Host": "************:5000", "Connection": "keep-alive", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Accept": "image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8", "Referer": "http://************:5000/login", "Accept-Encoding": "gzip, deflate", "Accept-Language": "en-US,en;q=0.9", "Cookie": "NEXT_LOCALE=en; session=eyJfZnJlc2giOmZhbHNlfQ.aCuaiA.agL3BDgsq9tWX6Vnf9mVediOAHY"}, "args": {}, "form": {}, "cookies": {"NEXT_LOCALE": "en", "session": "eyJfZnJlc2giOmZhbHNlfQ.aCuaiA.agL3BDgsq9tWX6Vnf9mVediOAHY"}, "remote_addr": "************", "user_agent": null}
تتبع الاستثناء:
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\app.py", line 917, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\app.py", line 891, in dispatch_request
    self.raise_routing_exception(req)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\app.py", line 500, in raise_routing_exception
    raise request.routing_exception  # type: ignore[misc]
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\ctx.py", line 362, in match_request
    result = self.url_adapter.match(return_rule=True)  # type: ignore
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\werkzeug\routing\map.py", line 629, in match
    raise NotFound() from None
werkzeug.exceptions.NotFound: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.

2025-05-19 23:54:37,877 - werkzeug - INFO - ************ - - [19/May/2025 23:54:37] "[35m[1mGET /favicon.ico HTTP/1.1[0m" 500 -
2025-05-19 23:54:57,384 - werkzeug - INFO - ************ - - [19/May/2025 23:54:57] "GET /login HTTP/1.1" 200 -
2025-05-19 23:54:57,673 - werkzeug - INFO - ************ - - [19/May/2025 23:54:57] "[32mGET /api/notifications/count HTTP/1.1[0m" 302 -
2025-05-19 23:54:57,690 - werkzeug - INFO - ************ - - [19/May/2025 23:54:57] "[32mGET /api/notifications/recent HTTP/1.1[0m" 302 -
2025-05-19 23:54:57,697 - nobara - ERROR - استثناء: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.
معلومات الطلب: {"url": "http://************:5000/favicon.ico", "method": "GET", "headers": {"Host": "************:5000", "Connection": "keep-alive", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Accept": "image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8", "Referer": "http://************:5000/login", "Accept-Encoding": "gzip, deflate", "Accept-Language": "en-US,en;q=0.9", "Cookie": "NEXT_LOCALE=en; session=eyJfZnJlc2giOmZhbHNlfQ.aCuaiA.agL3BDgsq9tWX6Vnf9mVediOAHY"}, "args": {}, "form": {}, "cookies": {"NEXT_LOCALE": "en", "session": "eyJfZnJlc2giOmZhbHNlfQ.aCuaiA.agL3BDgsq9tWX6Vnf9mVediOAHY"}, "remote_addr": "************", "user_agent": null}
تتبع الاستثناء:
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\app.py", line 917, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\app.py", line 891, in dispatch_request
    self.raise_routing_exception(req)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\app.py", line 500, in raise_routing_exception
    raise request.routing_exception  # type: ignore[misc]
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\ctx.py", line 362, in match_request
    result = self.url_adapter.match(return_rule=True)  # type: ignore
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\werkzeug\routing\map.py", line 629, in match
    raise NotFound() from None
werkzeug.exceptions.NotFound: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.

2025-05-19 23:54:57,707 - werkzeug - INFO - ************ - - [19/May/2025 23:54:57] "GET /login?next=/api/notifications/count HTTP/1.1" 200 -
2025-05-19 23:54:57,709 - werkzeug - INFO - ************ - - [19/May/2025 23:54:57] "[35m[1mGET /favicon.ico HTTP/1.1[0m" 500 -
2025-05-19 23:54:57,719 - werkzeug - INFO - ************ - - [19/May/2025 23:54:57] "GET /login?next=/api/notifications/recent HTTP/1.1" 200 -
2025-05-19 23:56:21,114 - app - INFO - استخدام قاعدة البيانات المحلية: sqlite:///instance/fouad_pos.db
2025-05-19 23:56:21,186 - nobara - INFO - تم بدء تشغيل التطبيق
2025-05-19 23:56:21,591 - app - INFO - تم تهيئة النظام المحاسبي بنجاح
2025-05-19 23:56:21,843 - app - INFO - تم تهيئة جدولة النسخ الاحتياطي التلقائي
2025-05-19 23:56:21,849 - app - INFO - إضافة عمود warehouse_id إلى جدول order...
2025-05-19 23:56:21,850 - app - ERROR - خطأ أثناء التحقق من عمود warehouse_id: no such table: order
2025-05-19 23:56:21,854 - security - WARNING - No JWT secret provided. Generated a new one. Please set JWT_SECRET in your environment.
2025-05-19 23:56:22,019 - __main__ - INFO - جاري بدء التطبيق على العنوان: http://************:5000
2025-05-19 23:56:22,838 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://************:5000
2025-05-19 23:56:22,843 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-19 23:56:22,853 - werkzeug - INFO -  * Restarting with stat
2025-05-19 23:56:25,193 - app - INFO - استخدام قاعدة البيانات المحلية: sqlite:///instance/fouad_pos.db
2025-05-19 23:56:25,284 - nobara - INFO - تم بدء تشغيل التطبيق
2025-05-19 23:56:25,855 - app - INFO - تم تهيئة النظام المحاسبي بنجاح
2025-05-19 23:56:26,156 - app - INFO - تم تهيئة جدولة النسخ الاحتياطي التلقائي
2025-05-19 23:56:26,158 - app - INFO - إضافة عمود warehouse_id إلى جدول order...
2025-05-19 23:56:26,159 - app - ERROR - خطأ أثناء التحقق من عمود warehouse_id: no such table: order
2025-05-19 23:56:26,168 - security - WARNING - No JWT secret provided. Generated a new one. Please set JWT_SECRET in your environment.
2025-05-19 23:56:26,428 - __main__ - INFO - جاري بدء التطبيق على العنوان: http://************:5000
2025-05-19 23:56:27,126 - werkzeug - WARNING -  * Debugger is active!
2025-05-19 23:56:27,169 - werkzeug - INFO -  * Debugger PIN: 692-333-133
2025-05-19 23:56:27,591 - werkzeug - INFO - ************ - - [19/May/2025 23:56:27] "[32mGET / HTTP/1.1[0m" 302 -
2025-05-19 23:56:27,597 - werkzeug - INFO - ************ - - [19/May/2025 23:56:27] "[32mGET / HTTP/1.1[0m" 302 -
2025-05-19 23:56:27,644 - werkzeug - INFO - ************ - - [19/May/2025 23:56:27] "[32mGET /api/notifications/recent HTTP/1.1[0m" 302 -
2025-05-19 23:56:27,693 - werkzeug - INFO - ************ - - [19/May/2025 23:56:27] "GET /login HTTP/1.1" 200 -
2025-05-19 23:56:27,706 - werkzeug - INFO - ************ - - [19/May/2025 23:56:27] "GET /login?next=/api/notifications/recent HTTP/1.1" 200 -
2025-05-19 23:56:27,723 - werkzeug - INFO - ************ - - [19/May/2025 23:56:27] "GET /login HTTP/1.1" 200 -
2025-05-19 23:56:27,978 - werkzeug - INFO - ************ - - [19/May/2025 23:56:27] "[36mGET /static/css/nobara-pro.css?v=1.0.0 HTTP/1.1[0m" 304 -
2025-05-19 23:56:27,991 - werkzeug - INFO - ************ - - [19/May/2025 23:56:27] "[36mGET /static/css/nobara-pro.css?v=1.0.0 HTTP/1.1[0m" 304 -
2025-05-19 23:56:28,012 - werkzeug - INFO - ************ - - [19/May/2025 23:56:28] "[36mGET /static/css/dashboard-pro.css?v=1.0.1 HTTP/1.1[0m" 304 -
2025-05-19 23:56:28,027 - werkzeug - INFO - ************ - - [19/May/2025 23:56:28] "[36mGET /static/css/dashboard-pro.css?v=1.0.1 HTTP/1.1[0m" 304 -
2025-05-19 23:56:28,069 - werkzeug - INFO - ************ - - [19/May/2025 23:56:28] "[36mGET /static/js/notifications-system.js?v=1.0.0 HTTP/1.1[0m" 304 -
2025-05-19 23:56:28,072 - werkzeug - INFO - ************ - - [19/May/2025 23:56:28] "[36mGET /static/js/system-manager.js?v=1.0.0 HTTP/1.1[0m" 304 -
2025-05-19 23:56:28,074 - werkzeug - INFO - ************ - - [19/May/2025 23:56:28] "[36mGET /static/uploads/logo_20250518120852.jpeg HTTP/1.1[0m" 304 -
2025-05-19 23:56:28,145 - werkzeug - INFO - ************ - - [19/May/2025 23:56:28] "[36mGET /static/js/notifications-system.js?v=1.0.0 HTTP/1.1[0m" 304 -
2025-05-19 23:56:28,166 - werkzeug - INFO - ************ - - [19/May/2025 23:56:28] "[36mGET /static/js/system-manager.js?v=1.0.0 HTTP/1.1[0m" 304 -
2025-05-19 23:56:28,169 - werkzeug - INFO - ************ - - [19/May/2025 23:56:28] "[36mGET /static/uploads/logo_20250518120852.jpeg HTTP/1.1[0m" 304 -
2025-05-19 23:56:28,628 - werkzeug - INFO - ************ - - [19/May/2025 23:56:28] "GET /api/settings HTTP/1.1" 200 -
2025-05-19 23:56:28,630 - werkzeug - INFO - ************ - - [19/May/2025 23:56:28] "GET /login HTTP/1.1" 200 -
2025-05-19 23:56:28,717 - werkzeug - INFO - ************ - - [19/May/2025 23:56:28] "[36mGET /static/css/nobara-pro.css?v=1.0.0 HTTP/1.1[0m" 304 -
2025-05-19 23:56:28,719 - werkzeug - INFO - ************ - - [19/May/2025 23:56:28] "[36mGET /static/css/dashboard-pro.css?v=1.0.1 HTTP/1.1[0m" 304 -
2025-05-19 23:56:28,727 - werkzeug - INFO - ************ - - [19/May/2025 23:56:28] "[36mGET /static/js/notifications-system.js?v=1.0.0 HTTP/1.1[0m" 304 -
2025-05-19 23:56:28,733 - werkzeug - INFO - ************ - - [19/May/2025 23:56:28] "[36mGET /static/js/system-manager.js?v=1.0.0 HTTP/1.1[0m" 304 -
2025-05-19 23:56:28,755 - werkzeug - INFO - ************ - - [19/May/2025 23:56:28] "[36mGET /static/uploads/logo_20250518120852.jpeg HTTP/1.1[0m" 304 -
2025-05-19 23:56:28,909 - werkzeug - INFO - ************ - - [19/May/2025 23:56:28] "GET /api/settings HTTP/1.1" 200 -
2025-05-19 23:56:29,110 - werkzeug - INFO - ************ - - [19/May/2025 23:56:29] "[32mGET /api/notifications/count HTTP/1.1[0m" 302 -
2025-05-19 23:56:29,111 - werkzeug - INFO - ************ - - [19/May/2025 23:56:29] "GET /api/settings HTTP/1.1" 200 -
2025-05-19 23:56:29,126 - werkzeug - INFO - ************ - - [19/May/2025 23:56:29] "[32mGET /api/notifications/recent HTTP/1.1[0m" 302 -
2025-05-19 23:56:29,212 - nobara - ERROR - استثناء: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.
معلومات الطلب: {"url": "http://************:5000/favicon.ico", "method": "GET", "headers": {"Host": "************:5000", "Connection": "keep-alive", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Accept": "image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8", "Referer": "http://************:5000/login", "Accept-Encoding": "gzip, deflate", "Accept-Language": "en-US,en;q=0.9", "Cookie": "NEXT_LOCALE=en; session=eyJfZnJlc2giOmZhbHNlfQ.aCua-w.ohDWIIkm9hFNLX_9OqhR5a9n0zU"}, "args": {}, "form": {}, "cookies": {"NEXT_LOCALE": "en", "session": "eyJfZnJlc2giOmZhbHNlfQ.aCua-w.ohDWIIkm9hFNLX_9OqhR5a9n0zU"}, "remote_addr": "************", "user_agent": null}
تتبع الاستثناء:
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\app.py", line 917, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\app.py", line 891, in dispatch_request
    self.raise_routing_exception(req)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\app.py", line 500, in raise_routing_exception
    raise request.routing_exception  # type: ignore[misc]
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\ctx.py", line 362, in match_request
    result = self.url_adapter.match(return_rule=True)  # type: ignore
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\werkzeug\routing\map.py", line 629, in match
    raise NotFound() from None
werkzeug.exceptions.NotFound: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.

2025-05-19 23:56:29,314 - werkzeug - INFO - ************ - - [19/May/2025 23:56:29] "[35m[1mGET /favicon.ico HTTP/1.1[0m" 500 -
2025-05-19 23:56:29,359 - werkzeug - INFO - ************ - - [19/May/2025 23:56:29] "GET /login?next=/api/notifications/count HTTP/1.1" 200 -
2025-05-19 23:56:29,377 - werkzeug - INFO - ************ - - [19/May/2025 23:56:29] "GET /login?next=/api/notifications/recent HTTP/1.1" 200 -
2025-05-19 23:56:29,570 - werkzeug - INFO - ************ - - [19/May/2025 23:56:29] "[32mGET /api/notifications/count HTTP/1.1[0m" 302 -
2025-05-19 23:56:29,574 - werkzeug - INFO - ************ - - [19/May/2025 23:56:29] "[32mGET /api/notifications/recent HTTP/1.1[0m" 302 -
2025-05-19 23:56:29,623 - werkzeug - INFO - ************ - - [19/May/2025 23:56:29] "[32mGET /api/notifications/count HTTP/1.1[0m" 302 -
2025-05-19 23:56:29,626 - werkzeug - INFO - ************ - - [19/May/2025 23:56:29] "[32mGET /api/notifications/recent HTTP/1.1[0m" 302 -
2025-05-19 23:56:29,689 - werkzeug - INFO - ************ - - [19/May/2025 23:56:29] "GET /login?next=/api/notifications/count HTTP/1.1" 200 -
2025-05-19 23:56:29,698 - werkzeug - INFO - ************ - - [19/May/2025 23:56:29] "GET /login?next=/api/notifications/recent HTTP/1.1" 200 -
2025-05-19 23:56:29,696 - nobara - ERROR - استثناء: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.
معلومات الطلب: {"url": "http://************:5000/favicon.ico", "method": "GET", "headers": {"Host": "************:5000", "Connection": "keep-alive", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Accept": "image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8", "Referer": "http://************:5000/login", "Accept-Encoding": "gzip, deflate", "Accept-Language": "en-US,en;q=0.9", "Cookie": "NEXT_LOCALE=en; session=.eJydjbsJwDAMBVcRrzYZwFOkDyYII39AiUPkznj3eIZUr7g73sCZlK2IwR8D1NfgEjPOAoddhU1IW6Z6U2_EMS5IvVSjZzkbwnQ_u-DW-StW4BOryfwADbYvmA.aCua_Q.59bv2DTbKSrkC4T2iceSsa4KuXY"}, "args": {}, "form": {}, "cookies": {"NEXT_LOCALE": "en", "session": ".eJydjbsJwDAMBVcRrzYZwFOkDyYII39AiUPkznj3eIZUr7g73sCZlK2IwR8D1NfgEjPOAoddhU1IW6Z6U2_EMS5IvVSjZzkbwnQ_u-DW-StW4BOryfwADbYvmA.aCua_Q.59bv2DTbKSrkC4T2iceSsa4KuXY"}, "remote_addr": "************", "user_agent": null}
تتبع الاستثناء:
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\app.py", line 917, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\app.py", line 891, in dispatch_request
    self.raise_routing_exception(req)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\app.py", line 500, in raise_routing_exception
    raise request.routing_exception  # type: ignore[misc]
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\ctx.py", line 362, in match_request
    result = self.url_adapter.match(return_rule=True)  # type: ignore
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\werkzeug\routing\map.py", line 629, in match
    raise NotFound() from None
werkzeug.exceptions.NotFound: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.

2025-05-19 23:56:29,747 - werkzeug - INFO - ************ - - [19/May/2025 23:56:29] "[35m[1mGET /favicon.ico HTTP/1.1[0m" 500 -
2025-05-19 23:56:29,762 - werkzeug - INFO - ************ - - [19/May/2025 23:56:29] "GET /login?next=/api/notifications/count HTTP/1.1" 200 -
2025-05-19 23:56:29,848 - werkzeug - INFO - ************ - - [19/May/2025 23:56:29] "GET /login?next=/api/notifications/recent HTTP/1.1" 200 -
2025-05-19 23:56:29,860 - nobara - ERROR - استثناء: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.
معلومات الطلب: {"url": "http://************:5000/favicon.ico", "method": "GET", "headers": {"Host": "************:5000", "Connection": "keep-alive", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Accept": "image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8", "Referer": "http://************:5000/login", "Accept-Encoding": "gzip, deflate", "Accept-Language": "en-US,en;q=0.9", "Cookie": "NEXT_LOCALE=en; session=eyJfZnJlc2giOmZhbHNlfQ.aCua_Q.8QqNEGJtzmMl-OVduea8iB8GW2s"}, "args": {}, "form": {}, "cookies": {"NEXT_LOCALE": "en", "session": "eyJfZnJlc2giOmZhbHNlfQ.aCua_Q.8QqNEGJtzmMl-OVduea8iB8GW2s"}, "remote_addr": "************", "user_agent": null}
تتبع الاستثناء:
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\app.py", line 917, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\app.py", line 891, in dispatch_request
    self.raise_routing_exception(req)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\app.py", line 500, in raise_routing_exception
    raise request.routing_exception  # type: ignore[misc]
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\ctx.py", line 362, in match_request
    result = self.url_adapter.match(return_rule=True)  # type: ignore
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\werkzeug\routing\map.py", line 629, in match
    raise NotFound() from None
werkzeug.exceptions.NotFound: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.

2025-05-19 23:56:29,880 - werkzeug - INFO - ************ - - [19/May/2025 23:56:29] "[35m[1mGET /favicon.ico HTTP/1.1[0m" 500 -
2025-05-19 23:56:32,546 - nobara - ERROR - استثناء: (sqlite3.OperationalError) unable to open database file
(Background on this error at: https://sqlalche.me/e/20/e3q8)
معلومات الطلب: {"url": "http://************:5000/login", "method": "POST", "headers": {"Host": "************:5000", "Connection": "keep-alive", "Content-Length": "29", "Cache-Control": "max-age=0", "Origin": "http://************:5000", "Content-Type": "application/x-www-form-urlencoded", "Upgrade-Insecure-Requests": "1", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7", "Referer": "http://************:5000/login", "Accept-Encoding": "gzip, deflate", "Accept-Language": "en-US,en;q=0.9", "Cookie": "NEXT_LOCALE=en; session=eyJfZnJlc2giOmZhbHNlfQ.aCua_Q.8QqNEGJtzmMl-OVduea8iB8GW2s"}, "args": {}, "form": {"username": "admin", "password": "admin"}, "cookies": {"NEXT_LOCALE": "en", "session": "eyJfZnJlc2giOmZhbHNlfQ.aCua_Q.8QqNEGJtzmMl-OVduea8iB8GW2s"}, "remote_addr": "************", "user_agent": null}
تتبع الاستثناء:
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 146, in __init__
    self._dbapi_connection = engine.raw_connection()
                             ~~~~~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 3298, in raw_connection
    return self.pool.connect()
           ~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\sqlalchemy\pool\base.py", line 449, in connect
    return _ConnectionFairy._checkout(self)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\sqlalchemy\pool\base.py", line 1264, in _checkout
    fairy = _ConnectionRecord.checkout(pool)
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\sqlalchemy\pool\base.py", line 713, in checkout
    rec = pool._do_get()
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\sqlalchemy\pool\impl.py", line 179, in _do_get
    with util.safe_reraise():
         ~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\sqlalchemy\util\langhelpers.py", line 146, in __exit__
    raise exc_value.with_traceback(exc_tb)
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\sqlalchemy\pool\impl.py", line 177, in _do_get
    return self._create_connection()
           ~~~~~~~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\sqlalchemy\pool\base.py", line 390, in _create_connection
    return _ConnectionRecord(self)
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\sqlalchemy\pool\base.py", line 675, in __init__
    self.__connect()
    ~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\sqlalchemy\pool\base.py", line 901, in __connect
    with util.safe_reraise():
         ~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\sqlalchemy\util\langhelpers.py", line 146, in __exit__
    raise exc_value.with_traceback(exc_tb)
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\sqlalchemy\pool\base.py", line 897, in __connect
    self.dbapi_connection = connection = pool._invoke_creator(self)
                                         ~~~~~~~~~~~~~~~~~~~~^^^^^^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\sqlalchemy\engine\create.py", line 646, in connect
    return dialect.connect(*cargs, **cparams)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\sqlalchemy\engine\default.py", line 625, in connect
    return self.loaded_dbapi.connect(*cargs, **cparams)  # type: ignore[no-any-return]  # NOQA: E501
           ~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^
sqlite3.OperationalError: unable to open database file

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\app.py", line 917, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\app.py", line 902, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)  # type: ignore[no-any-return]
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\routes\auth.py", line 27, in login
    user = User.query.filter_by(username=username).first()
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\sqlalchemy\orm\query.py", line 2759, in first
    return self.limit(1)._iter().first()  # type: ignore
           ~~~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\sqlalchemy\orm\query.py", line 2858, in _iter
    result: Union[ScalarResult[_T], Result[_T]] = self.session.execute(
                                                  ~~~~~~~~~~~~~~~~~~~~^
        statement,
        ^^^^^^^^^^
        params,
        ^^^^^^^
        execution_options={"_sa_orm_load_options": self.load_options},
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\sqlalchemy\orm\session.py", line 2365, in execute
    return self._execute_internal(
           ~~~~~~~~~~~~~~~~~~~~~~^
        statement,
        ^^^^^^^^^^
    ...<4 lines>...
        _add_event=_add_event,
        ^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\sqlalchemy\orm\session.py", line 2241, in _execute_internal
    conn = self._connection_for_bind(bind)
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\sqlalchemy\orm\session.py", line 2110, in _connection_for_bind
    return trans._connection_for_bind(engine, execution_options)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<string>", line 2, in _connection_for_bind
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\sqlalchemy\orm\state_changes.py", line 139, in _go
    ret_value = fn(self, *arg, **kw)
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\sqlalchemy\orm\session.py", line 1189, in _connection_for_bind
    conn = bind.connect()
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 3274, in connect
    return self._connection_cls(self)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 148, in __init__
    Connection._handle_dbapi_exception_noconnection(
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        err, dialect, engine
        ^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 2439, in _handle_dbapi_exception_noconnection
    raise sqlalchemy_exception.with_traceback(exc_info[2]) from e
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 146, in __init__
    self._dbapi_connection = engine.raw_connection()
                             ~~~~~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 3298, in raw_connection
    return self.pool.connect()
           ~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\sqlalchemy\pool\base.py", line 449, in connect
    return _ConnectionFairy._checkout(self)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\sqlalchemy\pool\base.py", line 1264, in _checkout
    fairy = _ConnectionRecord.checkout(pool)
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\sqlalchemy\pool\base.py", line 713, in checkout
    rec = pool._do_get()
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\sqlalchemy\pool\impl.py", line 179, in _do_get
    with util.safe_reraise():
         ~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\sqlalchemy\util\langhelpers.py", line 146, in __exit__
    raise exc_value.with_traceback(exc_tb)
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\sqlalchemy\pool\impl.py", line 177, in _do_get
    return self._create_connection()
           ~~~~~~~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\sqlalchemy\pool\base.py", line 390, in _create_connection
    return _ConnectionRecord(self)
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\sqlalchemy\pool\base.py", line 675, in __init__
    self.__connect()
    ~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\sqlalchemy\pool\base.py", line 901, in __connect
    with util.safe_reraise():
         ~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\sqlalchemy\util\langhelpers.py", line 146, in __exit__
    raise exc_value.with_traceback(exc_tb)
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\sqlalchemy\pool\base.py", line 897, in __connect
    self.dbapi_connection = connection = pool._invoke_creator(self)
                                         ~~~~~~~~~~~~~~~~~~~~^^^^^^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\sqlalchemy\engine\create.py", line 646, in connect
    return dialect.connect(*cargs, **cparams)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\sqlalchemy\engine\default.py", line 625, in connect
    return self.loaded_dbapi.connect(*cargs, **cparams)  # type: ignore[no-any-return]  # NOQA: E501
           ~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^
sqlalchemy.exc.OperationalError: (sqlite3.OperationalError) unable to open database file
(Background on this error at: https://sqlalche.me/e/20/e3q8)

2025-05-19 23:56:32,575 - werkzeug - INFO - ************ - - [19/May/2025 23:56:32] "[35m[1mPOST /login HTTP/1.1[0m" 500 -
2025-05-19 23:56:32,663 - nobara - ERROR - استثناء: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.
معلومات الطلب: {"url": "http://************:5000/favicon.ico", "method": "GET", "headers": {"Host": "************:5000", "Connection": "keep-alive", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Accept": "image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8", "Referer": "http://************:5000/login", "Accept-Encoding": "gzip, deflate", "Accept-Language": "en-US,en;q=0.9", "Cookie": "NEXT_LOCALE=en; session=eyJfZnJlc2giOmZhbHNlfQ.aCua_Q.8QqNEGJtzmMl-OVduea8iB8GW2s"}, "args": {}, "form": {}, "cookies": {"NEXT_LOCALE": "en", "session": "eyJfZnJlc2giOmZhbHNlfQ.aCua_Q.8QqNEGJtzmMl-OVduea8iB8GW2s"}, "remote_addr": "************", "user_agent": null}
تتبع الاستثناء:
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\app.py", line 917, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\app.py", line 891, in dispatch_request
    self.raise_routing_exception(req)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\app.py", line 500, in raise_routing_exception
    raise request.routing_exception  # type: ignore[misc]
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\ctx.py", line 362, in match_request
    result = self.url_adapter.match(return_rule=True)  # type: ignore
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\werkzeug\routing\map.py", line 629, in match
    raise NotFound() from None
werkzeug.exceptions.NotFound: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.

2025-05-19 23:56:32,666 - werkzeug - INFO - ************ - - [19/May/2025 23:56:32] "[35m[1mGET /favicon.ico HTTP/1.1[0m" 500 -
2025-05-19 23:59:05,748 - app - INFO - استخدام قاعدة البيانات المحلية: sqlite:///instance/fouad_pos.db
2025-05-19 23:59:05,823 - nobara - INFO - تم بدء تشغيل التطبيق
2025-05-19 23:59:06,169 - app - INFO - تم تهيئة النظام المحاسبي بنجاح
2025-05-19 23:59:06,400 - app - INFO - تم تهيئة جدولة النسخ الاحتياطي التلقائي
2025-05-19 23:59:06,406 - app - INFO - إضافة عمود warehouse_id إلى جدول order...
2025-05-19 23:59:06,407 - app - ERROR - خطأ أثناء التحقق من عمود warehouse_id: no such table: order
2025-05-19 23:59:06,410 - security - WARNING - No JWT secret provided. Generated a new one. Please set JWT_SECRET in your environment.
2025-05-19 23:59:06,609 - __main__ - INFO - جاري بدء التطبيق على العنوان: http://************:5000
2025-05-19 23:59:07,442 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://************:5000
2025-05-19 23:59:07,444 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-19 23:59:07,451 - werkzeug - INFO -  * Restarting with stat
2025-05-19 23:59:09,749 - app - INFO - استخدام قاعدة البيانات المحلية: sqlite:///C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\fouad_pos.db
2025-05-19 23:59:09,844 - nobara - INFO - تم بدء تشغيل التطبيق
2025-05-19 23:59:10,496 - app - INFO - تم تهيئة النظام المحاسبي بنجاح
2025-05-19 23:59:30,846 - app - INFO - تم تهيئة جدولة النسخ الاحتياطي التلقائي
2025-05-19 23:59:30,852 - app - INFO - عمود warehouse_id موجود بالفعل في جدول order
2025-05-19 23:59:30,861 - security - WARNING - No JWT secret provided. Generated a new one. Please set JWT_SECRET in your environment.
2025-05-19 23:59:31,168 - __main__ - INFO - جاري بدء التطبيق على العنوان: http://************:5000
2025-05-19 23:59:31,814 - werkzeug - WARNING -  * Debugger is active!
2025-05-19 23:59:31,870 - werkzeug - INFO -  * Debugger PIN: 692-333-133
2025-05-19 23:59:32,109 - werkzeug - INFO - ************ - - [19/May/2025 23:59:32] "[32mGET / HTTP/1.1[0m" 302 -
2025-05-19 23:59:32,114 - werkzeug - INFO - ************ - - [19/May/2025 23:59:32] "[32mGET / HTTP/1.1[0m" 302 -
2025-05-19 23:59:32,167 - werkzeug - INFO - ************ - - [19/May/2025 23:59:32] "GET /login HTTP/1.1" 200 -
2025-05-19 23:59:32,183 - werkzeug - INFO - ************ - - [19/May/2025 23:59:32] "GET /login HTTP/1.1" 200 -
2025-05-19 23:59:32,592 - werkzeug - INFO - ************ - - [19/May/2025 23:59:32] "[36mGET /static/js/system-manager.js?v=1.0.0 HTTP/1.1[0m" 304 -
2025-05-19 23:59:32,648 - werkzeug - INFO - ************ - - [19/May/2025 23:59:32] "[36mGET /static/js/notifications-system.js?v=1.0.0 HTTP/1.1[0m" 304 -
2025-05-19 23:59:32,685 - werkzeug - INFO - ************ - - [19/May/2025 23:59:32] "[36mGET /static/css/dashboard-pro.css?v=1.0.1 HTTP/1.1[0m" 304 -
2025-05-19 23:59:32,689 - werkzeug - INFO - ************ - - [19/May/2025 23:59:32] "[36mGET /static/uploads/logo_20250518120852.jpeg HTTP/1.1[0m" 304 -
2025-05-19 23:59:32,712 - werkzeug - INFO - ************ - - [19/May/2025 23:59:32] "[36mGET /static/css/nobara-pro.css?v=1.0.0 HTTP/1.1[0m" 304 -
2025-05-19 23:59:32,719 - werkzeug - INFO - ************ - - [19/May/2025 23:59:32] "[36mGET /static/css/dashboard-pro.css?v=1.0.1 HTTP/1.1[0m" 304 -
2025-05-19 23:59:32,739 - werkzeug - INFO - ************ - - [19/May/2025 23:59:32] "[36mGET /static/css/nobara-pro.css?v=1.0.0 HTTP/1.1[0m" 304 -
2025-05-19 23:59:32,817 - werkzeug - INFO - ************ - - [19/May/2025 23:59:32] "[36mGET /static/js/notifications-system.js?v=1.0.0 HTTP/1.1[0m" 304 -
2025-05-19 23:59:32,864 - werkzeug - INFO - ************ - - [19/May/2025 23:59:32] "[36mGET /static/js/system-manager.js?v=1.0.0 HTTP/1.1[0m" 304 -
2025-05-19 23:59:32,866 - werkzeug - INFO - ************ - - [19/May/2025 23:59:32] "[36mGET /static/uploads/logo_20250518120852.jpeg HTTP/1.1[0m" 304 -
2025-05-19 23:59:33,167 - werkzeug - INFO - ************ - - [19/May/2025 23:59:33] "GET /api/settings HTTP/1.1" 200 -
2025-05-19 23:59:33,204 - werkzeug - INFO - ************ - - [19/May/2025 23:59:33] "[32mGET /api/notifications/count HTTP/1.1[0m" 302 -
2025-05-19 23:59:33,212 - werkzeug - INFO - ************ - - [19/May/2025 23:59:33] "[32mGET /api/notifications/recent HTTP/1.1[0m" 302 -
2025-05-19 23:59:33,252 - werkzeug - INFO - ************ - - [19/May/2025 23:59:33] "GET /login?next=/api/notifications/count HTTP/1.1" 200 -
2025-05-19 23:59:33,287 - werkzeug - INFO - ************ - - [19/May/2025 23:59:33] "GET /login?next=/api/notifications/recent HTTP/1.1" 200 -
2025-05-19 23:59:33,301 - nobara - ERROR - استثناء: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.
معلومات الطلب: {"url": "http://************:5000/favicon.ico", "method": "GET", "headers": {"Host": "************:5000", "Connection": "keep-alive", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Accept": "image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8", "Referer": "http://************:5000/login", "Accept-Encoding": "gzip, deflate", "Accept-Language": "en-US,en;q=0.9", "Cookie": "NEXT_LOCALE=en; session=eyJfZnJlc2giOmZhbHNlfQ.aCua_Q.8QqNEGJtzmMl-OVduea8iB8GW2s"}, "args": {}, "form": {}, "cookies": {"NEXT_LOCALE": "en", "session": "eyJfZnJlc2giOmZhbHNlfQ.aCua_Q.8QqNEGJtzmMl-OVduea8iB8GW2s"}, "remote_addr": "************", "user_agent": null}
تتبع الاستثناء:
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\app.py", line 917, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\app.py", line 891, in dispatch_request
    self.raise_routing_exception(req)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\app.py", line 500, in raise_routing_exception
    raise request.routing_exception  # type: ignore[misc]
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\ctx.py", line 362, in match_request
    result = self.url_adapter.match(return_rule=True)  # type: ignore
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\werkzeug\routing\map.py", line 629, in match
    raise NotFound() from None
werkzeug.exceptions.NotFound: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.

2025-05-19 23:59:33,319 - werkzeug - INFO - ************ - - [19/May/2025 23:59:33] "[35m[1mGET /favicon.ico HTTP/1.1[0m" 500 -
2025-05-19 23:59:33,494 - werkzeug - INFO - ************ - - [19/May/2025 23:59:33] "GET /api/settings HTTP/1.1" 200 -
2025-05-19 23:59:33,717 - werkzeug - INFO - ************ - - [19/May/2025 23:59:33] "[32mGET /api/notifications/count HTTP/1.1[0m" 302 -
2025-05-19 23:59:33,719 - werkzeug - INFO - ************ - - [19/May/2025 23:59:33] "[32mGET /api/notifications/recent HTTP/1.1[0m" 302 -
2025-05-19 23:59:33,787 - werkzeug - INFO - ************ - - [19/May/2025 23:59:33] "GET /login?next=/api/notifications/recent HTTP/1.1" 200 -
2025-05-19 23:59:33,802 - werkzeug - INFO - ************ - - [19/May/2025 23:59:33] "GET /login?next=/api/notifications/count HTTP/1.1" 200 -
2025-05-19 23:59:33,801 - nobara - ERROR - استثناء: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.
معلومات الطلب: {"url": "http://************:5000/favicon.ico", "method": "GET", "headers": {"Host": "************:5000", "Connection": "keep-alive", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Accept": "image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8", "Referer": "http://************:5000/login", "Accept-Encoding": "gzip, deflate", "Accept-Language": "en-US,en;q=0.9", "Cookie": "NEXT_LOCALE=en; session=eyJfZmxhc2hlcyI6W3siIHQiOlsibWVzc2FnZSIsIlBsZWFzZSBsb2cgaW4gdG8gYWNjZXNzIHRoaXMgcGFnZS4iXX1dLCJfZnJlc2giOmZhbHNlfQ.aCubtQ.S1kz39m3zZba7tDrsCupgCBX74E"}, "args": {}, "form": {}, "cookies": {"NEXT_LOCALE": "en", "session": "eyJfZmxhc2hlcyI6W3siIHQiOlsibWVzc2FnZSIsIlBsZWFzZSBsb2cgaW4gdG8gYWNjZXNzIHRoaXMgcGFnZS4iXX1dLCJfZnJlc2giOmZhbHNlfQ.aCubtQ.S1kz39m3zZba7tDrsCupgCBX74E"}, "remote_addr": "************", "user_agent": null}
تتبع الاستثناء:
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\app.py", line 917, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\app.py", line 891, in dispatch_request
    self.raise_routing_exception(req)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\app.py", line 500, in raise_routing_exception
    raise request.routing_exception  # type: ignore[misc]
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\ctx.py", line 362, in match_request
    result = self.url_adapter.match(return_rule=True)  # type: ignore
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\werkzeug\routing\map.py", line 629, in match
    raise NotFound() from None
werkzeug.exceptions.NotFound: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.

2025-05-19 23:59:33,868 - werkzeug - INFO - ************ - - [19/May/2025 23:59:33] "[35m[1mGET /favicon.ico HTTP/1.1[0m" 500 -
2025-05-20 00:00:03,170 - werkzeug - INFO - ************ - - [20/May/2025 00:00:03] "[32mGET /api/notifications/recent HTTP/1.1[0m" 302 -
2025-05-20 00:00:03,186 - werkzeug - INFO - ************ - - [20/May/2025 00:00:03] "GET /login?next=/api/notifications/recent HTTP/1.1" 200 -
2025-05-20 00:00:04,142 - werkzeug - INFO - ************ - - [20/May/2025 00:00:04] "[32mGET /api/notifications/recent HTTP/1.1[0m" 302 -
2025-05-20 00:00:04,159 - werkzeug - INFO - ************ - - [20/May/2025 00:00:04] "GET /login?next=/api/notifications/recent HTTP/1.1" 200 -
