/**
 * POS Returns System
 * Handles product returns and refunds in the POS system
 *
 * Features:
 * - Return products from an existing invoice
 * - Return products from current cart
 * - Support for different payment methods
 * - Automatic inventory update
 */

// Global variables for returns
let selectedInvoice = null;
let returnItems = [];
let selectedReturnPaymentMethod = 'cash';
let returnReasons = [
    { id: 'defective', name: 'منتج معيب' },
    { id: 'wrong_item', name: 'منتج خاطئ' },
    { id: 'customer_dissatisfaction', name: 'عدم رضا العميل' },
    { id: 'size_color_issue', name: 'مشكلة في المقاس أو اللون' },
    { id: 'expired', name: 'منتج منتهي الصلاحية' },
    { id: 'damaged', name: 'منتج تالف' },
    { id: 'other', name: 'سبب آخر' }
];

// Initialize returns functionality
document.addEventListener('DOMContentLoaded', function() {
    // Setup return invoice button
    const returnInvoiceBtn = document.getElementById('return-invoice-btn');
    if (returnInvoiceBtn) {
        returnInvoiceBtn.addEventListener('click', function() {
            showModal('returnInvoiceModal', 'return-modal-content');
        });
    }

    // Setup return products modal events
    setupReturnProductsModal();

    // Setup return payment method selection
    setupReturnPaymentMethods();

    // Tab switching
    const invoiceTab = document.getElementById('return-tab-invoice');
    const cartTab = document.getElementById('return-tab-cart');

    if (invoiceTab && cartTab) {
        invoiceTab.addEventListener('click', function() {
            invoiceTab.classList.add('text-primary', 'dark:text-blue-400', 'border-b-2', 'border-primary', 'dark:border-blue-400', 'return-tab-active');
            cartTab.classList.remove('text-primary', 'dark:text-blue-400', 'border-b-2', 'border-primary', 'dark:border-blue-400', 'return-tab-active');
            cartTab.classList.add('text-gray-500', 'dark:text-gray-400');

            document.getElementById('return-invoice-content').classList.remove('hidden');
            document.getElementById('return-cart-content').classList.add('hidden');
        });

        cartTab.addEventListener('click', function() {
            cartTab.classList.add('text-primary', 'dark:text-blue-400', 'border-b-2', 'border-primary', 'dark:border-blue-400', 'return-tab-active');
            invoiceTab.classList.remove('text-primary', 'dark:text-blue-400', 'border-b-2', 'border-primary', 'dark:border-blue-400', 'return-tab-active');
            invoiceTab.classList.add('text-gray-500', 'dark:text-gray-400');

            document.getElementById('return-cart-content').classList.remove('hidden');
            document.getElementById('return-invoice-content').classList.add('hidden');

            // Load cart items for return
            loadCartItemsForReturn();
        });
    }

    // Search invoice
    const searchBtn = document.getElementById('invoice-search-btn');
    if (searchBtn) {
        searchBtn.addEventListener('click', searchInvoice);
    }

    const searchInput = document.getElementById('invoice-search-input');
    if (searchInput) {
        searchInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                searchInvoice();
            }
        });
    }
});

/**
 * Setup return products modal events
 */
function setupReturnProductsModal() {
    // Close modal button
    const closeReturnProductsModal = document.getElementById('close-return-products-modal');
    if (closeReturnProductsModal) {
        closeReturnProductsModal.addEventListener('click', function() {
            hideModal('returnProductsModal', 'return-products-modal-content');
        });
    }

    // Cancel button
    const cancelReturnProducts = document.getElementById('cancel-return-products');
    if (cancelReturnProducts) {
        cancelReturnProducts.addEventListener('click', function() {
            hideModal('returnProductsModal', 'return-products-modal-content');
        });
    }

    // Confirm return button
    const confirmReturnProducts = document.getElementById('confirm-return-products');
    if (confirmReturnProducts) {
        confirmReturnProducts.addEventListener('click', processReturnProducts);
    }
}

/**
 * Setup return payment method selection
 */
function setupReturnPaymentMethods() {
    const paymentMethods = document.querySelectorAll('.return-payment-method');

    paymentMethods.forEach(method => {
        method.addEventListener('click', function() {
            // Remove active class from all methods
            paymentMethods.forEach(m => m.classList.remove('payment-method-active'));

            // Add active class to selected method
            this.classList.add('payment-method-active');

            // Update selected payment method
            selectedReturnPaymentMethod = this.getAttribute('data-method');
        });
    });
}

/**
 * Open return products modal for a specific invoice
 * @param {Object} invoice - The invoice object
 * @param {Array} items - The invoice items
 */
function openReturnProductsModal(invoice, items) {
    // Store selected invoice
    selectedInvoice = invoice;

    // Reset return items
    returnItems = [];

    // Set invoice details
    document.getElementById('return-invoice-number').textContent = invoice.invoice_number;
    document.getElementById('return-customer-name').textContent = invoice.customer_name || 'عميل نقدي';
    document.getElementById('return-invoice-date').textContent = new Date(invoice.created_at).toLocaleString('ar-EG');

    // Clear products list
    const productsList = document.getElementById('return-products-list');
    productsList.innerHTML = '';

    // Check if there are items to return
    if (!items || items.length === 0) {
        productsList.innerHTML = `
            <div class="text-center p-4 text-gray-500 dark:text-gray-400">
                <i class="ri-error-warning-line text-3xl mb-2"></i>
                <p>لا توجد منتجات متاحة للإرجاع</p>
            </div>
        `;

        // Disable submit button
        const submitButton = document.getElementById('submit-return');
        if (submitButton) submitButton.disabled = true;

        // Show modal
        showModal('returnProductsModal', 'return-products-modal-content');
        return;
    }

    // Enable submit button
    const submitButton = document.getElementById('submit-return');
    if (submitButton) submitButton.disabled = false;

    // Add products to list
    items.forEach(item => {
        // Skip items that have been fully returned
        if (item.returned_quantity && item.returned_quantity >= item.quantity) {
            return;
        }

        // Calculate available quantity for return
        const availableQuantity = item.returned_quantity ?
            item.quantity - item.returned_quantity :
            item.quantity;

        if (availableQuantity <= 0) {
            return;
        }

        // Create return item
        const returnItem = {
            id: item.id,
            product_id: item.product_id,
            name: item.product_name || item.name,
            price: item.price,
            max_quantity: availableQuantity,
            quantity: 1,
            total: item.price
        };

        // Add to return items
        returnItems.push(returnItem);

        // Add to UI
        addReturnProductToUI(returnItem, returnItems.length - 1);
    });

    // If no items were added (all were skipped)
    if (returnItems.length === 0) {
        productsList.innerHTML = `
            <div class="text-center p-4 text-gray-500 dark:text-gray-400">
                <i class="ri-error-warning-line text-3xl mb-2"></i>
                <p>لا توجد منتجات متاحة للإرجاع</p>
            </div>
        `;

        // Disable submit button
        if (submitButton) submitButton.disabled = true;
    } else {
        // Update total
        updateReturnTotal();
    }

    // Show modal
    showModal('returnProductsModal', 'return-products-modal-content');
}

/**
 * Add return product to UI
 * @param {Object} item - The return item
 * @param {number} index - The item index in returnItems array
 */
function addReturnProductToUI(item, index) {
    const productsList = document.getElementById('return-products-list');
    const template = document.getElementById('return-product-template');

    // Clone template
    const clone = template.content.cloneNode(true);

    // Set product data
    clone.querySelector('.return-product-name').textContent = item.name;
    clone.querySelector('.return-product-price').textContent = `${item.price.toFixed(2)} ج.م`;

    const quantityInput = clone.querySelector('.return-quantity');
    quantityInput.value = item.quantity;
    quantityInput.max = item.max_quantity;
    quantityInput.setAttribute('data-index', index);

    clone.querySelector('.return-product-total').textContent = `${item.total.toFixed(2)} ج.م`;

    // Add reasons dropdown if it exists
    const reasonSelect = clone.querySelector('.return-reason-select');
    if (reasonSelect) {
        // Clear existing options
        reasonSelect.innerHTML = '';

        // Add default option
        const defaultOption = document.createElement('option');
        defaultOption.value = '';
        defaultOption.textContent = 'اختر سبب الإرجاع';
        reasonSelect.appendChild(defaultOption);

        // Add reason options
        returnReasons.forEach(reason => {
            const option = document.createElement('option');
            option.value = reason.id;
            option.textContent = reason.name;
            reasonSelect.appendChild(option);
        });

        // Set selected reason if exists
        if (item.reason) {
            reasonSelect.value = item.reason;
        }

        // Add event listener
        reasonSelect.addEventListener('change', function() {
            returnItems[index].reason = this.value;
        });
    }

    // Add event listeners
    const decreaseBtn = clone.querySelector('.return-quantity-decrease');
    decreaseBtn.addEventListener('click', function() {
        updateReturnItemQuantity(index, Math.max(1, item.quantity - 1));
    });

    const increaseBtn = clone.querySelector('.return-quantity-increase');
    increaseBtn.addEventListener('click', function() {
        updateReturnItemQuantity(index, Math.min(item.max_quantity, item.quantity + 1));
    });

    quantityInput.addEventListener('change', function() {
        const newQuantity = parseInt(this.value) || 1;
        updateReturnItemQuantity(index, Math.min(Math.max(1, newQuantity), item.max_quantity));
    });

    // Add remove button event listener if it exists
    const removeBtn = clone.querySelector('.return-product-remove');
    if (removeBtn) {
        removeBtn.addEventListener('click', function() {
            removeReturnItem(index);
        });
    }

    // Add to list
    productsList.appendChild(clone);
}

/**
 * Update return item quantity
 * @param {number} index - The item index in returnItems array
 * @param {number} newQuantity - The new quantity
 */
function updateReturnItemQuantity(index, newQuantity) {
    // Check if item exists
    if (!returnItems[index]) return;

    // Update item
    returnItems[index].quantity = newQuantity;
    returnItems[index].total = returnItems[index].price * newQuantity;

    // Find the quantity input and total element
    const container = document.getElementById('invoice-items-container');
    if (!container) {
        container = document.getElementById('cart-items-for-return');
    }

    // Find all return items
    const items = container.querySelectorAll('.return-item');

    // Find the specific item
    const itemElements = Array.from(container.querySelectorAll('.return-qty')).filter(
        el => parseInt(el.getAttribute('data-index')) === index
    );

    if (itemElements.length > 0) {
        // Update quantity input
        itemElements[0].value = newQuantity;

        // Update total
        const itemElement = itemElements[0].closest('.return-item');
        const totalElement = itemElement.querySelector('.bg-red-100');
        if (totalElement) {
            totalElement.textContent = `${returnItems[index].total.toFixed(2)} ج.م`;
        }
    }

    // Update total
    updateReturnTotal();
}

/**
 * Update return total amount
 */
function updateReturnTotal() {
    const total = returnItems.reduce((sum, item) => sum + item.total, 0);
    const totalElement = document.getElementById('return-total');
    if (totalElement) {
        totalElement.textContent = `${total.toFixed(2)} ج.م`;
    }
}

/**
 * Search for invoice by number
 */
function searchInvoice() {
    const invoiceNumber = document.getElementById('invoice-search-input').value.trim();

    if (!invoiceNumber) {
        showNotification('يرجى إدخال رقم الفاتورة', 'warning');
        return;
    }

    // Show loading
    showLoading('جاري البحث عن الفاتورة...');

    // Fetch invoice details
    fetch(`/api/sales/invoice_search?invoice_number=${invoiceNumber}`)
        .then(response => response.json())
        .then(data => {
            // Hide loading
            hideLoading();

            if (data.success) {
                if (data.multiple_results) {
                    // عرض قائمة بالفواتير المطابقة
                    displayMultipleInvoices(data.invoices);
                } else if (data.invoice) {
                    // Store invoice
                    selectedInvoice = data.invoice;

                    // Display invoice details
                    displayInvoiceDetails(data.invoice, data.items);

                    // Show return info section
                    document.getElementById('return-info-section').classList.remove('hidden');

                    // Show return button
                    document.getElementById('return-invoice-btn').classList.remove('hidden');
                }
            } else {
                showNotification(data.message || 'لم يتم العثور على الفاتورة', 'error');
            }
        })
        .catch(error => {
            // Hide loading
            hideLoading();

            console.error('Error searching invoice:', error);
            showNotification('حدث خطأ أثناء البحث عن الفاتورة', 'error');
        });
}

/**
 * Display multiple invoices matching the search
 * @param {Array} invoices - List of invoices
 */
function displayMultipleInvoices(invoices) {
    const detailsContainer = document.getElementById('invoice-details-container');
    const itemsContainer = document.getElementById('invoice-items-container');

    // Clear containers
    itemsContainer.innerHTML = '';

    // Hide return info section
    document.getElementById('return-info-section').classList.add('hidden');

    // Hide return button
    document.getElementById('return-invoice-btn').classList.add('hidden');

    // Display invoices list
    detailsContainer.innerHTML = `
        <div class="bg-blue-50 dark:bg-blue-900/30 p-4 rounded-lg mb-3">
            <h3 class="text-sm font-bold text-gray-700 dark:text-gray-300 mb-2">تم العثور على ${invoices.length} فاتورة مطابقة</h3>
            <p class="text-xs text-gray-600 dark:text-gray-400 mb-3">يرجى اختيار الفاتورة المطلوبة من القائمة أدناه</p>
        </div>
    `;

    // Create invoices list
    const invoicesList = document.createElement('div');
    invoicesList.className = 'space-y-2';

    invoices.forEach(invoice => {
        const invoiceItem = document.createElement('div');
        invoiceItem.className = 'bg-white dark:bg-gray-700 rounded-lg shadow-sm border border-gray-200 dark:border-gray-600 p-3 cursor-pointer hover:border-blue-500 dark:hover:border-blue-400 transition-all duration-300';
        invoiceItem.innerHTML = `
            <div class="flex justify-between items-center">
                <div>
                    <div class="font-medium text-gray-800 dark:text-white text-sm">${invoice.invoice_number}</div>
                    <div class="text-xs text-gray-500 dark:text-gray-400">${new Date(invoice.created_at).toLocaleString('ar-EG')}</div>
                </div>
                <div class="flex items-center gap-2">
                    <div class="text-xs bg-gray-100 dark:bg-gray-600 text-gray-700 dark:text-gray-300 px-2 py-1 rounded-full">${invoice.customer_name || 'عميل نقدي'}</div>
                    <div class="text-sm font-bold text-primary dark:text-blue-400">${invoice.total.toFixed(2)} ج.م</div>
                </div>
            </div>
        `;

        // Add click event to select invoice
        invoiceItem.addEventListener('click', () => {
            // Show loading
            showLoading('جاري تحميل تفاصيل الفاتورة...');

            // Fetch invoice details
            fetch(`/api/sales/invoice_search?invoice_number=${invoice.invoice_number}`)
                .then(response => response.json())
                .then(data => {
                    // Hide loading
                    hideLoading();

                    if (data.success && data.invoice) {
                        // Store invoice
                        selectedInvoice = data.invoice;

                        // Display invoice details
                        displayInvoiceDetails(data.invoice, data.items);

                        // Show return info section
                        document.getElementById('return-info-section').classList.remove('hidden');

                        // Show return button
                        document.getElementById('return-invoice-btn').classList.remove('hidden');
                    } else {
                        showNotification(data.message || 'لم يتم العثور على الفاتورة', 'error');
                    }
                })
                .catch(error => {
                    // Hide loading
                    hideLoading();

                    console.error('Error fetching invoice details:', error);
                    showNotification('حدث خطأ أثناء تحميل تفاصيل الفاتورة', 'error');
                });
        });

        invoicesList.appendChild(invoiceItem);
    });

    detailsContainer.appendChild(invoicesList);
}

/**
 * Display invoice details
 * @param {Object} invoice - The invoice object
 * @param {Array} items - The invoice items
 */
function displayInvoiceDetails(invoice, items) {
    const detailsContainer = document.getElementById('invoice-details-container');
    const itemsContainer = document.getElementById('invoice-items-container');

    // Clear containers
    returnItems = [];

    // Display invoice details
    detailsContainer.innerHTML = `
        <div class="bg-blue-50 dark:bg-blue-900/30 p-4 rounded-lg">
            <div class="flex justify-between items-center mb-2">
                <span class="text-gray-700 dark:text-gray-300 font-medium">رقم الفاتورة:</span>
                <span class="text-primary dark:text-blue-400 font-bold">${invoice.invoice_number}</span>
            </div>
            <div class="flex justify-between items-center mb-2">
                <span class="text-gray-700 dark:text-gray-300 font-medium">تاريخ الفاتورة:</span>
                <span class="text-gray-800 dark:text-gray-200">${new Date(invoice.created_at).toLocaleString('ar-EG')}</span>
            </div>
            <div class="flex justify-between items-center mb-2">
                <span class="text-gray-700 dark:text-gray-300 font-medium">العميل:</span>
                <span class="text-gray-800 dark:text-gray-200">${invoice.customer_name || 'عميل نقدي'}</span>
            </div>
            <div class="flex justify-between items-center">
                <span class="text-gray-700 dark:text-gray-300 font-medium">إجمالي الفاتورة:</span>
                <span class="text-primary dark:text-blue-400 font-bold">${invoice.total.toFixed(2)} ج.م</span>
            </div>
        </div>
    `;

    // Display invoice items
    if (items.length === 0) {
        itemsContainer.innerHTML = `
            <div class="flex flex-col items-center justify-center py-8 text-gray-400 dark:text-gray-500">
                <div class="w-16 h-16 rounded-full bg-gray-100 dark:bg-gray-700 flex items-center justify-center mb-2">
                    <i class="ri-shopping-bag-line ri-2x"></i>
                </div>
                <p class="text-sm">لا توجد منتجات في هذه الفاتورة</p>
            </div>
        `;
        return;
    }

    // Clear items container
    itemsContainer.innerHTML = '';

    // Add items to UI
    items.forEach((item, index) => {
        // Check if item can be returned
        const availableQuantity = item.quantity - (item.returned_quantity || 0);

        if (availableQuantity <= 0) {
            return; // Skip items that can't be returned
        }

        // Add item to return items array
        returnItems.push({
            id: item.id,
            order_item_id: item.id,
            product_id: item.product_id,
            name: item.product_name,
            price: item.price,
            quantity: 1,
            max_quantity: availableQuantity,
            total: item.price,
            reason: ''
        });

        // Create return item element
        const itemElement = document.createElement('div');
        itemElement.className = 'bg-white dark:bg-gray-700 rounded-lg shadow-sm border border-gray-200 dark:border-gray-600 p-4 mb-3 return-item';
        itemElement.innerHTML = `
            <div class="flex justify-between items-start">
                <div class="flex-1">
                    <h4 class="font-medium text-gray-800 dark:text-white text-sm mb-2">${item.product_name}</h4>
                    <div class="flex items-center justify-between">
                        <div class="flex items-center gap-2 bg-gray-50 dark:bg-gray-800 p-1 rounded-lg border border-gray-100 dark:border-gray-600">
                            <button class="return-qty-decrease w-7 h-7 rounded-full bg-white dark:bg-gray-700 text-gray-600 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-600 flex items-center justify-center shadow-sm transition-all duration-300">
                                <i class="ri-subtract-line"></i>
                            </button>
                            <input type="number" class="return-qty w-12 text-center bg-transparent border-none focus:ring-0 text-gray-800 dark:text-white font-medium" min="1" max="${availableQuantity}" value="1" data-index="${returnItems.length - 1}">
                            <button class="return-qty-increase w-7 h-7 rounded-full bg-white dark:bg-gray-700 text-gray-600 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-600 flex items-center justify-center shadow-sm transition-all duration-300">
                                <i class="ri-add-line"></i>
                            </button>
                        </div>
                        <div class="text-sm text-gray-500 dark:text-gray-400 bg-blue-50 dark:bg-blue-900/30 px-2 py-1 rounded-full text-blue-700 dark:text-blue-400 font-medium">${item.price.toFixed(2)} ج.م</div>
                    </div>
                </div>
                <div class="flex flex-col items-end">
                    <button class="return-item-remove p-1.5 text-gray-400 hover:text-red-500 transition-all duration-300 hover:bg-red-50 dark:hover:bg-red-900/30 rounded-full">
                        <i class="ri-close-line"></i>
                    </button>
                </div>
            </div>
            <div class="mt-3">
                <select class="return-reason w-full text-sm border border-gray-300 dark:border-gray-600 rounded-md p-2 bg-white dark:bg-gray-800 text-gray-800 dark:text-gray-200" data-index="${returnItems.length - 1}">
                    <option value="">اختر سبب الإرجاع</option>
                    ${returnReasons.map(reason => `<option value="${reason.id}">${reason.name}</option>`).join('')}
                </select>
            </div>
            <div class="flex justify-between items-center mt-3 pt-2 border-t border-gray-100 dark:border-gray-600">
                <div class="text-xs text-gray-600 dark:text-gray-400">
                    <div>السعر: <span class="font-medium">${item.price.toFixed(2)} ج.م</span></div>
                </div>
                <div class="font-bold text-sm bg-red-100 dark:bg-red-900/30 px-3 py-1 rounded-full text-red-600 dark:text-red-400">${item.price.toFixed(2)} ج.م</div>
            </div>
        `;

        // Add to container
        itemsContainer.appendChild(itemElement);

        // Add event listeners
        const currentIndex = returnItems.length - 1;

        // Quantity decrease button
        itemElement.querySelector('.return-qty-decrease').addEventListener('click', function() {
            updateReturnItemQuantity(currentIndex, Math.max(1, returnItems[currentIndex].quantity - 1));
        });

        // Quantity increase button
        itemElement.querySelector('.return-qty-increase').addEventListener('click', function() {
            updateReturnItemQuantity(currentIndex, Math.min(returnItems[currentIndex].max_quantity, returnItems[currentIndex].quantity + 1));
        });

        // Quantity input
        itemElement.querySelector('.return-qty').addEventListener('change', function() {
            const newQuantity = parseInt(this.value) || 1;
            updateReturnItemQuantity(currentIndex, Math.min(Math.max(1, newQuantity), returnItems[currentIndex].max_quantity));
        });

        // Reason select
        itemElement.querySelector('.return-reason').addEventListener('change', function() {
            returnItems[currentIndex].reason = this.value;
        });

        // Remove button
        itemElement.querySelector('.return-item-remove').addEventListener('click', function() {
            removeReturnItem(currentIndex);
        });
    });

    // Update total
    updateReturnTotal();
}

/**
 * Load cart items for return
 */
function loadCartItemsForReturn() {
    const container = document.getElementById('cart-items-for-return');

    // Clear container
    container.innerHTML = '';

    // Check if cart is empty
    if (!cartItems || cartItems.length === 0) {
        container.innerHTML = `
            <div class="flex flex-col items-center justify-center py-8 text-gray-400 dark:text-gray-500">
                <div class="w-16 h-16 rounded-full bg-gray-100 dark:bg-gray-700 flex items-center justify-center mb-2">
                    <i class="ri-shopping-cart-line ri-2x"></i>
                </div>
                <p class="text-sm">لا توجد منتجات في السلة</p>
            </div>
        `;
        return;
    }

    // Clear return items
    returnItems = [];

    // Add cart items to return items
    cartItems.forEach((item, index) => {
        returnItems.push({
            id: item.id,
            product_id: item.id,
            name: item.name,
            price: item.price,
            quantity: 1,
            max_quantity: item.quantity,
            total: item.price,
            reason: ''
        });

        // Create return item element
        const itemElement = document.createElement('div');
        itemElement.className = 'bg-white dark:bg-gray-700 rounded-lg shadow-sm border border-gray-200 dark:border-gray-600 p-4 mb-3 return-item';
        itemElement.innerHTML = `
            <div class="flex justify-between items-start">
                <div class="flex-1">
                    <h4 class="font-medium text-gray-800 dark:text-white text-sm mb-2">${item.name}</h4>
                    <div class="flex items-center justify-between">
                        <div class="flex items-center gap-2 bg-gray-50 dark:bg-gray-800 p-1 rounded-lg border border-gray-100 dark:border-gray-600">
                            <button class="return-qty-decrease w-7 h-7 rounded-full bg-white dark:bg-gray-700 text-gray-600 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-600 flex items-center justify-center shadow-sm transition-all duration-300">
                                <i class="ri-subtract-line"></i>
                            </button>
                            <input type="number" class="return-qty w-12 text-center bg-transparent border-none focus:ring-0 text-gray-800 dark:text-white font-medium" min="1" max="${item.quantity}" value="1" data-index="${returnItems.length - 1}">
                            <button class="return-qty-increase w-7 h-7 rounded-full bg-white dark:bg-gray-700 text-gray-600 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-600 flex items-center justify-center shadow-sm transition-all duration-300">
                                <i class="ri-add-line"></i>
                            </button>
                        </div>
                        <div class="text-sm text-gray-500 dark:text-gray-400 bg-blue-50 dark:bg-blue-900/30 px-2 py-1 rounded-full text-blue-700 dark:text-blue-400 font-medium">${item.price.toFixed(2)} ج.م</div>
                    </div>
                </div>
                <div class="flex flex-col items-end">
                    <button class="return-item-remove p-1.5 text-gray-400 hover:text-red-500 transition-all duration-300 hover:bg-red-50 dark:hover:bg-red-900/30 rounded-full">
                        <i class="ri-close-line"></i>
                    </button>
                </div>
            </div>
            <div class="mt-3">
                <select class="return-reason w-full text-sm border border-gray-300 dark:border-gray-600 rounded-md p-2 bg-white dark:bg-gray-800 text-gray-800 dark:text-gray-200" data-index="${returnItems.length - 1}">
                    <option value="">اختر سبب الإرجاع</option>
                    ${returnReasons.map(reason => `<option value="${reason.id}">${reason.name}</option>`).join('')}
                </select>
            </div>
            <div class="flex justify-between items-center mt-3 pt-2 border-t border-gray-100 dark:border-gray-600">
                <div class="text-xs text-gray-600 dark:text-gray-400">
                    <div>السعر: <span class="font-medium">${item.price.toFixed(2)} ج.م</span></div>
                </div>
                <div class="font-bold text-sm bg-red-100 dark:bg-red-900/30 px-3 py-1 rounded-full text-red-600 dark:text-red-400">${item.price.toFixed(2)} ج.م</div>
            </div>
        `;

        // Add to container
        container.appendChild(itemElement);

        // Add event listeners
        const currentIndex = returnItems.length - 1;

        // Quantity decrease button
        itemElement.querySelector('.return-qty-decrease').addEventListener('click', function() {
            updateReturnItemQuantity(currentIndex, Math.max(1, returnItems[currentIndex].quantity - 1));
        });

        // Quantity increase button
        itemElement.querySelector('.return-qty-increase').addEventListener('click', function() {
            updateReturnItemQuantity(currentIndex, Math.min(returnItems[currentIndex].max_quantity, returnItems[currentIndex].quantity + 1));
        });

        // Quantity input
        itemElement.querySelector('.return-qty').addEventListener('change', function() {
            const newQuantity = parseInt(this.value) || 1;
            updateReturnItemQuantity(currentIndex, Math.min(Math.max(1, newQuantity), returnItems[currentIndex].max_quantity));
        });

        // Reason select
        itemElement.querySelector('.return-reason').addEventListener('change', function() {
            returnItems[currentIndex].reason = this.value;
        });

        // Remove button
        itemElement.querySelector('.return-item-remove').addEventListener('click', function() {
            removeReturnItem(currentIndex);
        });
    });

    // Update total
    updateReturnTotal();

    // Show return info section
    document.getElementById('return-info-section').classList.remove('hidden');

    // Show return button
    document.getElementById('return-invoice-btn').classList.remove('hidden');
}

/**
 * Remove return item
 * @param {number} index - The item index in returnItems array
 */
function removeReturnItem(index) {
    // Find the container
    let container = document.getElementById('invoice-items-container');
    if (!container) {
        container = document.getElementById('cart-items-for-return');
    }

    // Find the item element
    const itemElements = Array.from(container.querySelectorAll('.return-qty')).filter(
        el => parseInt(el.getAttribute('data-index')) === index
    );

    if (itemElements.length > 0) {
        const itemElement = itemElements[0].closest('.return-item');

        // Remove item from DOM
        if (itemElement) {
            itemElement.remove();
        }
    }

    // Remove item from array
    returnItems.splice(index, 1);

    // Update data-index attributes for remaining items
    const allQtyInputs = container.querySelectorAll('.return-qty');
    const allReasonSelects = container.querySelectorAll('.return-reason');

    allQtyInputs.forEach((input, idx) => {
        const currentIndex = parseInt(input.getAttribute('data-index'));
        if (currentIndex > index) {
            input.setAttribute('data-index', currentIndex - 1);
        }
    });

    allReasonSelects.forEach((select, idx) => {
        const currentIndex = parseInt(select.getAttribute('data-index'));
        if (currentIndex > index) {
            select.setAttribute('data-index', currentIndex - 1);
        }
    });

    // Update total
    updateReturnTotal();

    // Show notification
    showNotification('تم إزالة المنتج من قائمة المرتجعات', 'info');

    // If no items left, hide return button and info section
    if (returnItems.length === 0) {
        document.getElementById('return-invoice-btn').classList.add('hidden');
        document.getElementById('return-info-section').classList.add('hidden');
    }
}

/**
 * Process return products
 */
function processReturnProducts() {
    // Validate return items
    if (returnItems.length === 0) {
        Swal.fire({
            title: 'تنبيه',
            text: 'لا توجد منتجات للإرجاع',
            icon: 'warning',
            confirmButtonText: 'حسناً'
        });
        return;
    }

    // Check if all items have reasons
    const itemsWithoutReasons = returnItems.filter(item => !item.reason);
    if (itemsWithoutReasons.length > 0) {
        // Highlight items without reasons
        itemsWithoutReasons.forEach(item => {
            const index = returnItems.indexOf(item);
            const reasonSelect = document.querySelector(`.return-reason[data-index="${index}"]`);
            if (reasonSelect) {
                reasonSelect.classList.add('border-red-500', 'dark:border-red-500', 'ring-2', 'ring-red-500', 'dark:ring-red-500');

                // Add animation
                reasonSelect.classList.add('animate-pulse');

                // Remove animation after 2 seconds
                setTimeout(() => {
                    reasonSelect.classList.remove('animate-pulse');
                }, 2000);
            }
        });

        Swal.fire({
            title: 'تنبيه',
            text: 'يرجى تحديد سبب الإرجاع لجميع المنتجات المحددة باللون الأحمر',
            icon: 'warning',
            confirmButtonText: 'حسناً'
        });
        return;
    }

    // Get payment method
    const paymentMethod = document.querySelector('input[name="return_payment_method"]:checked');
    if (paymentMethod) {
        selectedReturnPaymentMethod = paymentMethod.value;
    }

    // Get notes
    const notes = document.getElementById('return-notes') ?
                 document.getElementById('return-notes').value : '';

    // Get print options
    const printReceipt = document.getElementById('print-return-receipt') ?
                        document.getElementById('print-return-receipt').checked : true;
    const printInvoice = document.getElementById('print-return-invoice') ?
                        document.getElementById('print-return-invoice').checked : false;

    // Prepare data
    let returnData = {};

    // Check if we're returning from an existing invoice or from cart
    if (selectedInvoice && selectedInvoice.id) {
        // Return from existing invoice
        returnData = {
            invoice_id: selectedInvoice.id,
            invoice_number: selectedInvoice.invoice_number,
            items: returnItems.map(item => ({
                order_item_id: item.order_item_id || item.id,
                product_id: item.product_id,
                quantity: item.quantity,
                reason: item.reason || ''
            })),
            payment_method: selectedReturnPaymentMethod,
            notes: notes
        };
    } else {
        // Return from cart
        // Get customer
        const customerSelect = document.getElementById('return-customer-select');
        const customerId = customerSelect ? customerSelect.value : '';

        returnData = {
            from_cart: true,
            customer_id: customerId,
            items: returnItems.map(item => ({
                product_id: item.product_id,
                quantity: item.quantity,
                price: item.price,
                reason: item.reason || ''
            })),
            payment_method: selectedReturnPaymentMethod,
            notes: notes
        };
    }

    // Confirm return
    Swal.fire({
        title: 'تأكيد الإرجاع',
        html: `
            <p>هل أنت متأكد من إرجاع ${returnItems.length} منتج؟</p>
            <p class="mt-2">طريقة الاسترداد:
                <span class="font-bold">
                    ${selectedReturnPaymentMethod === 'cash' ? 'نقدي' :
                      selectedReturnPaymentMethod === 'card' ? 'بطاقة' : 'رصيد متجر'}
                </span>
            </p>
            <p class="mt-2 text-sm text-gray-500">سيتم إرجاع المنتجات للمخزون وتسجيل عملية الاسترجاع في المبيعات والتقارير الحسابية.</p>
        `,
        icon: 'question',
        showCancelButton: true,
        confirmButtonText: 'نعم، إرجاع',
        cancelButtonText: 'إلغاء',
        confirmButtonColor: '#ef4444'
    }).then((result) => {
        if (result.isConfirmed) {
            // Show loading
            Swal.fire({
                title: 'جاري معالجة الإرجاع',
                text: 'يرجى الانتظار...',
                allowOutsideClick: false,
                didOpen: () => {
                    Swal.showLoading();
                }
            });

            // Send request to API
            fetch('/api/sales/return', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(returnData)
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Show success message
                    Swal.fire({
                        title: 'تم استرجاع المنتجات بنجاح',
                        html: `
                            <p>تم استرجاع المنتجات وإعادتها للمخزون.</p>
                            <p class="mt-2 text-lg font-bold text-red-500">المبلغ المسترد: ${data.returned_amount.toFixed(2)} ج.م</p>
                            <p class="mt-2">رقم المرتجع: ${data.return_reference || ''}</p>
                        `,
                        icon: 'success',
                        confirmButtonText: 'تم',
                        showDenyButton: printReceipt && data.return_id ? true : false,
                        showCancelButton: printInvoice && data.return_id ? true : false,
                        denyButtonText: 'طباعة إيصال المرتجع',
                        cancelButtonText: 'طباعة فاتورة المرتجع'
                    }).then((result) => {
                        // Close modal
                        hideModal('returnInvoiceModal', 'return-modal-content');

                        if (result.isDenied && data.return_id) {
                            // Print receipt
                            window.open(`/returns/print/${data.return_id}`, '_blank');
                        }

                        if (result.dismiss === Swal.DismissReason.cancel && data.return_id) {
                            // Print invoice
                            window.open(`/returns/print_invoice/${data.return_id}`, '_blank');
                        }

                        // If returning from cart, ask if user wants to clear cart
                        if (!selectedInvoice || !selectedInvoice.id) {
                            Swal.fire({
                                title: 'هل تريد إفراغ السلة؟',
                                text: 'هل تريد إفراغ السلة وبدء عملية بيع جديدة؟',
                                icon: 'question',
                                showCancelButton: true,
                                confirmButtonText: 'نعم',
                                cancelButtonText: 'لا'
                            }).then((result) => {
                                if (result.isConfirmed) {
                                    clearCart();
                                }
                                // Refresh page
                                window.location.reload();
                            });
                        } else {
                            // Refresh page
                            window.location.reload();
                        }
                    });
                } else {
                    Swal.fire({
                        title: 'خطأ',
                        text: data.message || 'حدث خطأ أثناء استرجاع المنتجات',
                        icon: 'error',
                        confirmButtonText: 'حسناً'
                    });
                }
            })
            .catch(error => {
                console.error('Error processing return:', error);
                Swal.fire({
                    title: 'خطأ',
                    text: 'حدث خطأ أثناء الاتصال بالخادم',
                    icon: 'error',
                    confirmButtonText: 'حسناً'
                });
            });
        }
    });
}

/**
 * Show loading indicator
 * @param {string} message - Loading message
 */
function showLoading(message = 'جاري التحميل...') {
    // Check if loading container exists
    let loadingContainer = document.getElementById('loading-container');

    // Create loading container if it doesn't exist
    if (!loadingContainer) {
        loadingContainer = document.createElement('div');
        loadingContainer.id = 'loading-container';
        loadingContainer.className = 'fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center';
        loadingContainer.innerHTML = `
            <div class="bg-white dark:bg-gray-800 rounded-lg p-4 shadow-xl flex items-center space-x-4 space-x-reverse">
                <div class="w-10 h-10 rounded-full border-4 border-t-primary border-r-primary border-b-gray-200 border-l-gray-200 animate-spin"></div>
                <p id="loading-message" class="text-gray-700 dark:text-gray-300 font-medium"></p>
            </div>
        `;
        document.body.appendChild(loadingContainer);
    }

    // Update loading message
    document.getElementById('loading-message').textContent = message;

    // Show loading container
    loadingContainer.classList.remove('hidden');
}

/**
 * Hide loading indicator
 */
function hideLoading() {
    const loadingContainer = document.getElementById('loading-container');
    if (loadingContainer) {
        loadingContainer.classList.add('hidden');
    }
}

/**
 * Return a single product from cart
 * @param {number} index - The product index in cart
 */
function returnSingleProduct(index) {
    const item = cartItems[index];

    // Skip if not a valid item
    if (!item) {
        showNotification('لا يمكن استرجاع هذا المنتج', 'error');
        return;
    }

    // Create a fake invoice for the return
    const invoice = {
        id: 0, // Will be ignored for single product return
        invoice_number: document.getElementById('cart-invoice')?.textContent || `CART-${new Date().getTime()}`,
        created_at: new Date().toISOString(),
        customer_name: document.getElementById('customer-search')?.value || 'عميل نقدي'
    };

    // Create return items array with just this product
    const returnItem = {
        id: item.id,
        product_id: item.id,
        product_name: item.name,
        price: item.price,
        quantity: item.quantity,
        max_quantity: item.quantity,
        total: item.total
    };

    // Reset return items and add this item
    returnItems = [returnItem];
    selectedInvoice = invoice;

    // Open return products modal
    openReturnProductsModal(invoice, [
        {
            id: item.id,
            product_id: item.id,
            product_name: item.name,
            price: item.price,
            quantity: item.quantity,
            returned_quantity: 0,
            total: item.total
        }
    ]);
}
