<!-- Return Invoice Modal -->
<div id="returnInvoiceModal" class="fixed inset-0 bg-black bg-opacity-60 flex items-center justify-center z-50 hidden backdrop-blur-sm">
    <div class="bg-white dark:bg-gray-800 rounded-xl w-full max-w-4xl mx-4 overflow-hidden shadow-2xl transform transition-all duration-300 scale-95 opacity-0" id="return-modal-content">
        <div class="p-6">
            <div class="flex justify-between items-center mb-5">
                <h3 class="text-lg font-bold text-gray-900 dark:text-gray-100 flex items-center gap-2">
                    <div class="w-8 h-8 rounded-full bg-red-100 dark:bg-red-900 flex items-center justify-center text-red-600 dark:text-red-400">
                        <i class="ri-arrow-go-back-line"></i>
                    </div>
                    <span>نظام مرتجع المبيعات</span>
                </h3>
                <button id="close-return-modal" class="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors duration-300">
                    <i class="ri-close-line text-xl"></i>
                </button>
            </div>

            <div class="mb-5">
                <!-- Tabs -->
                <div class="flex border-b border-gray-200 dark:border-gray-700 mb-4">
                    <button id="return-tab-invoice" class="px-4 py-2 text-sm font-medium text-primary dark:text-blue-400 border-b-2 border-primary dark:border-blue-400 return-tab-active">
                        استرجاع فاتورة سابقة
                    </button>
                    <button id="return-tab-cart" class="px-4 py-2 text-sm font-medium text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300">
                        استرجاع من السلة الحالية
                    </button>
                </div>

                <!-- استرجاع فاتورة سابقة -->
                <div id="return-invoice-content" class="return-tab-content">
                    <div class="bg-blue-50 dark:bg-blue-900/30 p-4 rounded-lg mb-4">
                        <p class="text-sm text-blue-800 dark:text-blue-300">
                            يمكنك استرجاع فاتورة عن طريق إدخال رقم الفاتورة أو مسح الباركود. سيتم إرجاع المنتجات للمخزون وتسجيل عملية الاسترجاع في المبيعات والتقارير الحسابية.
                        </p>
                    </div>

                    <div class="flex gap-2 mb-4">
                        <div class="relative flex-1">
                            <input type="text" id="invoice-search-input" placeholder="أدخل رقم الفاتورة..." class="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent text-gray-800 dark:text-gray-200 dark:bg-gray-700 transition-all duration-300">
                        </div>
                        <button id="invoice-search-btn" class="px-4 py-3 bg-primary text-white rounded-lg hover:bg-blue-600 transition-all duration-300 flex items-center gap-2 shadow-md">
                            <i class="ri-search-line"></i>
                            <span>بحث</span>
                        </button>
                    </div>

                    <div id="invoice-details-container" class="mb-4">
                        <!-- Invoice details will be loaded here -->
                    </div>

                    <div class="max-h-96 overflow-y-auto pr-1 mb-4">
                        <h4 class="font-medium text-gray-800 dark:text-gray-200 mb-3">المنتجات</h4>
                        <div id="invoice-items-container">
                            <!-- Invoice items will be loaded here -->
                            <div class="flex flex-col items-center justify-center py-8 text-gray-400 dark:text-gray-500">
                                <div class="w-16 h-16 rounded-full bg-gray-100 dark:bg-gray-700 flex items-center justify-center mb-2">
                                    <i class="ri-shopping-bag-line ri-2x"></i>
                                </div>
                                <p class="text-sm">قم بالبحث عن فاتورة لعرض المنتجات</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- استرجاع من السلة الحالية -->
                <div id="return-cart-content" class="return-tab-content hidden">
                    <div class="bg-yellow-50 dark:bg-yellow-900/30 p-4 rounded-lg mb-4">
                        <p class="text-sm text-yellow-800 dark:text-yellow-300">
                            يمكنك استرجاع منتجات من السلة الحالية. حدد المنتجات التي تريد استرجاعها وأدخل الكمية المراد استرجاعها.
                        </p>
                    </div>

                    <div class="mb-4">
                        <label for="return-customer-select" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">اختر العميل</label>
                        <select id="return-customer-select" class="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent text-gray-800 dark:text-gray-200 dark:bg-gray-700 transition-all duration-300">
                            <option value="">عميل نقدي</option>
                            <!-- سيتم تحميل العملاء هنا -->
                        </select>
                    </div>

                    <div class="max-h-96 overflow-y-auto pr-1 mb-4">
                        <h4 class="font-medium text-gray-800 dark:text-gray-200 mb-3">منتجات السلة</h4>
                        <div id="cart-items-for-return">
                            <!-- Cart items will be loaded here -->
                            <div class="flex flex-col items-center justify-center py-8 text-gray-400 dark:text-gray-500">
                                <div class="w-16 h-16 rounded-full bg-gray-100 dark:bg-gray-700 flex items-center justify-center mb-2">
                                    <i class="ri-shopping-cart-line ri-2x"></i>
                                </div>
                                <p class="text-sm">لا توجد منتجات في السلة</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- معلومات المرتجع -->
                <div id="return-info-section" class="hidden">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                        <div>
                            <h4 class="font-medium text-gray-800 dark:text-gray-200 mb-3">طريقة استرداد المبلغ</h4>
                            <div class="flex flex-wrap gap-3">
                                <label class="flex items-center bg-white dark:bg-gray-700 p-3 rounded-lg border border-gray-200 dark:border-gray-600 cursor-pointer hover:border-primary dark:hover:border-blue-500 transition-all duration-300">
                                    <input type="radio" name="return_payment_method" value="cash" class="w-4 h-4 text-primary" checked>
                                    <span class="mr-2 text-sm text-gray-700 dark:text-gray-300">نقدي</span>
                                </label>
                                <label class="flex items-center bg-white dark:bg-gray-700 p-3 rounded-lg border border-gray-200 dark:border-gray-600 cursor-pointer hover:border-primary dark:hover:border-blue-500 transition-all duration-300">
                                    <input type="radio" name="return_payment_method" value="card" class="w-4 h-4 text-primary">
                                    <span class="mr-2 text-sm text-gray-700 dark:text-gray-300">بطاقة</span>
                                </label>
                                <label class="flex items-center bg-white dark:bg-gray-700 p-3 rounded-lg border border-gray-200 dark:border-gray-600 cursor-pointer hover:border-primary dark:hover:border-blue-500 transition-all duration-300">
                                    <input type="radio" name="return_payment_method" value="store_credit" class="w-4 h-4 text-primary">
                                    <span class="mr-2 text-sm text-gray-700 dark:text-gray-300">رصيد متجر</span>
                                    <i class="ri-information-line text-gray-400 cursor-help ml-1" title="رصيد المتجر هو رصيد يتم إضافته لحساب العميل ويمكن استخدامه في عمليات الشراء المستقبلية"></i>
                                </label>
                            </div>
                        </div>
                        <div>
                            <h4 class="font-medium text-gray-800 dark:text-gray-200 mb-3">ملاحظات المرتجع</h4>
                            <textarea id="return-notes" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent text-gray-800 dark:text-gray-200 dark:bg-gray-700 transition-all duration-300" rows="2" placeholder="أضف ملاحظات حول سبب الإرجاع (اختياري)"></textarea>
                        </div>
                    </div>

                    <div class="flex justify-between items-center p-3 bg-red-50 dark:bg-red-900/30 rounded-lg shadow-sm border border-red-100 dark:border-red-800 mb-4">
                        <span class="text-red-800 dark:text-red-300 font-bold">إجمالي المبلغ المسترجع:</span>
                        <span id="return-total" class="text-red-600 dark:text-red-400 text-xl font-bold">0.00 ج.م</span>
                    </div>

                    <div class="bg-gray-50 dark:bg-gray-700 p-3 rounded-lg mb-4">
                        <h4 class="font-medium text-gray-800 dark:text-gray-200 mb-3">خيارات الطباعة</h4>
                        <div class="flex flex-wrap gap-3">
                            <label class="flex items-center">
                                <input type="checkbox" id="print-return-receipt" class="w-4 h-4 text-primary" checked>
                                <span class="mr-2 text-sm text-gray-700 dark:text-gray-300">طباعة إيصال المرتجع</span>
                            </label>
                            <label class="flex items-center">
                                <input type="checkbox" id="print-return-invoice" class="w-4 h-4 text-primary">
                                <span class="mr-2 text-sm text-gray-700 dark:text-gray-300">طباعة فاتورة المرتجع</span>
                            </label>
                        </div>
                    </div>
                </div>
            </div>

            <div class="flex justify-between gap-3">
                <button id="cancel-return" class="px-4 py-2.5 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-600 transition-all duration-300 whitespace-nowrap text-sm shadow-sm">
                    إلغاء
                </button>
                <button id="return-invoice-btn" class="hidden flex-1 px-4 py-2.5 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-all duration-300 whitespace-nowrap text-sm shadow-md flex items-center justify-center gap-2">
                    <i class="ri-arrow-go-back-line"></i>
                    <span>تأكيد الاسترجاع</span>
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Return Item Template -->
<template id="return-item-template">
    <div class="bg-white dark:bg-gray-700 rounded-lg shadow-sm border border-gray-200 dark:border-gray-600 p-4 mb-3 return-item">
        <div class="flex justify-between items-start">
            <div class="flex-1">
                <h4 class="return-item-name font-medium text-gray-800 dark:text-white text-sm mb-2"></h4>
                <div class="flex items-center justify-between">
                    <div class="flex items-center gap-2 bg-gray-50 dark:bg-gray-800 p-1 rounded-lg border border-gray-100 dark:border-gray-600">
                        <button class="return-qty-decrease w-7 h-7 rounded-full bg-white dark:bg-gray-700 text-gray-600 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-600 flex items-center justify-center shadow-sm transition-all duration-300">
                            <i class="ri-subtract-line"></i>
                        </button>
                        <input type="number" class="return-qty w-12 text-center bg-transparent border-none focus:ring-0 text-gray-800 dark:text-white font-medium" min="1" value="1">
                        <button class="return-qty-increase w-7 h-7 rounded-full bg-white dark:bg-gray-700 text-gray-600 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-600 flex items-center justify-center shadow-sm transition-all duration-300">
                            <i class="ri-add-line"></i>
                        </button>
                    </div>
                    <div class="text-sm text-gray-500 dark:text-gray-400 return-item-price bg-blue-50 dark:bg-blue-900/30 px-2 py-1 rounded-full text-blue-700 dark:text-blue-400 font-medium"></div>
                </div>
            </div>
            <div class="flex flex-col items-end">
                <button class="return-item-remove p-1.5 text-gray-400 hover:text-red-500 transition-all duration-300 hover:bg-red-50 dark:hover:bg-red-900/30 rounded-full">
                    <i class="ri-close-line"></i>
                </button>
            </div>
        </div>
        <div class="mt-3">
            <select class="return-reason w-full text-sm border border-gray-300 dark:border-gray-600 rounded-md p-2 bg-white dark:bg-gray-800 text-gray-800 dark:text-gray-200">
                <option value="">اختر سبب الإرجاع</option>
                <option value="defective">منتج معيب</option>
                <option value="wrong_item">منتج خاطئ</option>
                <option value="customer_dissatisfaction">عدم رضا العميل</option>
                <option value="size_color_issue">مشكلة في المقاس أو اللون</option>
                <option value="expired">منتج منتهي الصلاحية</option>
                <option value="damaged">منتج تالف</option>
                <option value="other">سبب آخر</option>
            </select>
        </div>
        <div class="flex justify-between items-center mt-3 pt-2 border-t border-gray-100 dark:border-gray-600">
            <div class="text-xs text-gray-600 dark:text-gray-400">
                <div>السعر: <span class="return-item-unit-price font-medium"></span></div>
            </div>
            <div class="font-bold text-sm return-item-total bg-red-100 dark:bg-red-900/30 px-3 py-1 rounded-full text-red-600 dark:text-red-400"></div>
        </div>
    </div>
</template>
