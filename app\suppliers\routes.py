"""
Nobara POS System - Suppliers Routes
نظام نوبارا لنقاط البيع - مسارات الموردين
"""

from flask import render_template, request, jsonify, redirect, url_for, flash
from flask_login import login_required, current_user
from app.suppliers import bp
import logging

logger = logging.getLogger(__name__)

@bp.route('/')
@login_required
def index():
    """قائمة الموردين"""
    return render_template('suppliers/index.html')

@bp.route('/create')
@login_required
def create():
    """إنشاء مورد جديد"""
    return render_template('suppliers/form.html')

@bp.route('/<int:id>')
@login_required
def view(id):
    """عرض تفاصيل المورد"""
    return render_template('suppliers/view.html')

@bp.route('/<int:id>/edit')
@login_required
def edit(id):
    """تعديل المورد"""
    return render_template('suppliers/form.html')
