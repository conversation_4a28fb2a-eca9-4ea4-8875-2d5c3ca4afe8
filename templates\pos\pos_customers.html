<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>FoUaD - قائمة عملاء نقطة البيع</title>
    <script src="https://cdn.tailwindcss.com/3.4.16"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#6366F1',
                        secondary: '#10b981',
                        success: '#22c55e',
                        warning: '#f59e0b',
                        danger: '#ef4444',
                        info: '#3b82f6'
                    },
                    container: {
                        center: true,
                        padding: {
                            DEFAULT: '1rem',
                            sm: '2rem',
                            lg: '4rem',
                            xl: '5rem',
                            '2xl': '6rem',
                        },
                    },
                    borderRadius: {
                        'none': '0px',
                        'sm': '4px',
                        DEFAULT: '8px',
                        'md': '12px',
                        'lg': '16px',
                        'xl': '20px',
                        '2xl': '24px',
                        '3xl': '32px',
                        'full': '9999px'
                    }
                }
            }
        }
    </script>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@400;500;700&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Pacifico&display=swap" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/remixicon@3.5.0/fonts/remixicon.css" rel="stylesheet">
    <style>
        :where([class^="ri-"])::before { content: "\f3c2"; }
        body {
            font-family: 'Tajawal', sans-serif;
            background-color: #f9fafb;
        }
        .scrollbar-hide::-webkit-scrollbar {
            display: none;
        }
        .scrollbar-hide {
            -ms-overflow-style: none;
            scrollbar-width: none;
        }
        .bg-pattern {
            background-image: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%236366F1' fill-opacity='0.05'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
        }
        .glass-effect {
            backdrop-filter: blur(8px);
            background: rgba(255, 255, 255, 0.8);
            border: 1px solid rgba(255, 255, 255, 0.3);
            box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.07);
        }
        .pulse-animation {
            animation: pulse 2s infinite;
        }
        @keyframes pulse {
            0% {
                box-shadow: 0 0 0 0 rgba(99, 102, 241, 0.4);
            }
            70% {
                box-shadow: 0 0 0 10px rgba(99, 102, 241, 0);
            }
            100% {
                box-shadow: 0 0 0 0 rgba(99, 102, 241, 0);
            }
        }
        .card-hover {
            transition: all 0.3s ease;
        }
        .card-hover:hover {
            transform: translateY(-5px);
        }
    </style>
</head>
<body class="bg-pattern">
    <div class="flex h-screen overflow-hidden">
        <!-- Sidebar -->
        {% include 'partials/sidebar.html' %}
        
        <!-- Main Content -->
        <div class="flex-1 flex flex-col overflow-hidden">
            <!-- Top Navigation -->
            {% include 'partials/topnav.html' %}
            
            <!-- Content Area -->
            <main class="flex-1 overflow-y-auto p-6">
                <!-- Page Header -->
                <div class="mb-6 flex flex-wrap items-center justify-between">
                    <div>
                        <h1 class="text-2xl font-bold text-gray-800">قائمة عملاء نقطة البيع</h1>
                        <p class="text-gray-600">اختر عميل أو قم بإضافة عميل جديد</p>
                    </div>
                    <div class="flex gap-3">
                        <a href="{{ url_for('pos.create_customer', from_pos='true') }}" class="bg-gradient-to-r from-primary to-indigo-600 text-white px-4 py-2.5 rounded-lg shadow-md hover:shadow-lg transition-all duration-300 flex items-center gap-2 pulse-animation whitespace-nowrap">
                            <div class="w-5 h-5 flex items-center justify-center">
                                <i class="ri-user-add-line"></i>
                            </div>
                            <span>إضافة عميل جديد</span>
                        </a>
                        <a href="{{ url_for('pos.index') }}" class="bg-gray-100 text-gray-700 px-4 py-2.5 rounded-lg hover:bg-gray-200 transition-all duration-300 flex items-center gap-2 whitespace-nowrap">
                            <div class="w-5 h-5 flex items-center justify-center">
                                <i class="ri-arrow-right-line"></i>
                            </div>
                            <span>العودة لنقطة البيع</span>
                        </a>
                    </div>
                </div>
                
                <!-- Customers Grid -->
                <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
                    {% for customer in customers %}
                    <div class="bg-white rounded-xl shadow-sm p-5 border border-gray-100 card-hover cursor-pointer" onclick="selectCustomer({{ customer.id }}, '{{ customer.name }}')">
                        <div class="flex items-center mb-3">
                            <div class="w-12 h-12 rounded-full bg-primary bg-opacity-10 text-primary flex items-center justify-center">
                                <span class="text-lg font-medium">{{ customer.name[0]|upper }}</span>
                            </div>
                            <div class="mr-3">
                                <h3 class="font-medium text-gray-900">{{ customer.name }}</h3>
                                <p class="text-sm text-gray-500">{{ customer.phone or 'لا يوجد رقم هاتف' }}</p>
                            </div>
                        </div>
                        <div class="text-xs text-gray-500">
                            {% if customer.address %}
                            <p class="truncate mb-1">{{ customer.address }}</p>
                            {% endif %}
                            {% if customer.email %}
                            <p>{{ customer.email }}</p>
                            {% endif %}
                            <div class="mt-2 flex justify-between text-xs">
                                <span>عميل منذ</span>
                                <span>{{ customer.created_at.strftime('%Y-%m-%d') }}</span>
                            </div>
                        </div>
                    </div>
                    {% else %}
                    <div class="col-span-full">
                        <div class="bg-white rounded-xl shadow-sm p-8 text-center">
                            <div class="flex flex-col items-center">
                                <div class="w-16 h-16 rounded-full bg-gray-100 flex items-center justify-center text-gray-400 mb-4">
                                    <i class="ri-user-line text-3xl"></i>
                                </div>
                                <h3 class="text-lg font-medium text-gray-800 mb-2">لا يوجد عملاء</h3>
                                <p class="text-gray-500 max-w-md mb-6">لم يتم إضافة عملاء بعد، يمكنك إضافة عملاء جدد من خلال زر "إضافة عميل جديد".</p>
                                <a href="{{ url_for('pos.create_customer', from_pos='true') }}" class="px-4 py-2 bg-primary text-white rounded-md hover:bg-indigo-700 transition-all">
                                    إضافة عميل جديد
                                </a>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
            </main>
        </div>
    </div>
    
    <script>
        function selectCustomer(id, name) {
            // Send the selected customer back to POS page
            window.opener ? window.opener.setCustomer(id, name) : null;
            window.location.href = "{{ url_for('pos.index') }}?customer_id=" + id;
        }
    </script>
</body>
</html>