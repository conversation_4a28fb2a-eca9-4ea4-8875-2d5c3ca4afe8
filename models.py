from datetime import datetime, <PERSON><PERSON><PERSON>
from app import db
from flask_login import UserMixin
from werkzeug.security import generate_password_hash, check_password_hash
from sqlalchemy.sql import func


class PaymentMethod(db.Model):
    """نموذج طرق الدفع"""
    __table_args__ = {'extend_existing': True}
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(64), nullable=False)
    code = db.Column(db.String(20), unique=True)
    description = db.Column(db.String(256))
    icon = db.Column(db.String(50), default='ri-money-dollar-circle-line')
    color = db.Column(db.String(20), default='blue')
    is_active = db.Column(db.<PERSON>, default=True)
    is_default = db.Column(db.Boolean, default=False)
    requires_reference = db.Column(db.Bo<PERSON>, default=False)

    # خصائص المعاملات المالية
    affects_cash_register = db.Column(db.Bo<PERSON>, default=True)  # هل يؤثر على الخزينة
    is_credit = db.Column(db.Boolean, default=False)  # هل هو دفع آجل
    allow_partial_payment = db.Column(db.Boolean, default=True)  # هل يسمح بالدفع الجزئي
    requires_approval = db.Column(db.Boolean, default=False)  # هل يتطلب موافقة
    payment_category = db.Column(db.String(20), default='cash')  # تصنيف الدفع: cash, electronic, credit, other

    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    def to_dict(self):
        return {
            'id': self.id,
            'name': self.name,
            'code': self.code,
            'description': self.description,
            'icon': self.icon,
            'color': self.color,
            'is_active': self.is_active,
            'is_default': self.is_default,
            'requires_reference': self.requires_reference,
            'affects_cash_register': self.affects_cash_register,
            'is_credit': self.is_credit,
            'allow_partial_payment': self.allow_partial_payment,
            'requires_approval': self.requires_approval,
            'payment_category': self.payment_category,
            'created_at': self.created_at.strftime('%Y-%m-%d %H:%M:%S') if self.created_at else None,
            'updated_at': self.updated_at.strftime('%Y-%m-%d %H:%M:%S') if self.updated_at else None
        }




class Permission(db.Model):
    """نموذج للصلاحيات في النظام"""
    __table_args__ = {'extend_existing': True}
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(64), unique=True, nullable=False)
    description = db.Column(db.String(256))
    module = db.Column(db.String(64), nullable=False)  # اسم الوحدة (المبيعات، المشتريات، المخزون، إلخ)
    action = db.Column(db.String(64), nullable=False)  # نوع الإجراء (إضافة، تعديل، حذف، عرض)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    __table_args__ = {'extend_existing': True}

    def __repr__(self):
        return f'<Permission {self.name}>'


class UserPermission(db.Model):
    """نموذج لربط المستخدمين بالصلاحيات"""
    __table_args__ = {'extend_existing': True}
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    permission_id = db.Column(db.Integer, db.ForeignKey('permission.id'), nullable=False)
    granted = db.Column(db.Boolean, default=True)  # هل الصلاحية ممنوحة أم محظورة
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # العلاقات
    permission = db.relationship('Permission', backref='user_permissions')

    # إضافة فهرس مركب للبحث السريع
    __table_args__ = (
        db.UniqueConstraint('user_id', 'permission_id', name='uix_user_permission'),
        {'extend_existing': True}
    )

    def to_dict(self):
        """تحويل بيانات الصلاحية إلى قاموس"""
        return {
            'id': self.id,
            'user_id': self.user_id,
            'permission_id': self.permission_id,
            'permission_name': self.permission.name if self.permission else None,
            'permission_description': self.permission.description if self.permission else None,
            'granted': self.granted,
            'created_at': self.created_at.strftime('%Y-%m-%d %H:%M:%S') if self.created_at else None,
            'updated_at': self.updated_at.strftime('%Y-%m-%d %H:%M:%S') if self.updated_at else None
        }


# تم إزالة نموذج UserActivity لأنه غير مستخدم حاليًا


# تم إزالة نموذج UserSession لأنه غير مستخدم حاليًا
# تم نقل نموذج Role إلى ملف models/security.py


class User(UserMixin, db.Model):
    __table_args__ = {'extend_existing': True}

    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(64), unique=True, nullable=False)
    email = db.Column(db.String(120), unique=True, nullable=False)
    password_hash = db.Column(db.String(256), nullable=False)
    full_name = db.Column(db.String(128))
    role = db.Column(db.String(20), default='staff')  # admin, staff
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    # العلاقات
    permissions = db.relationship('UserPermission', backref='user', cascade='all, delete-orphan')

    # خصائص للأعمدة الجديدة (لا تؤثر على قاعدة البيانات)
    @property
    def phone(self):
        return None

    @property
    def position(self):
        return None

    @property
    def profile_image(self):
        return None

    @property
    def is_active(self):
        return True

    @property
    def last_login(self):
        return None

    @property
    def reset_token(self):
        return None

    @property
    def reset_token_expiry(self):
        return None

    @property
    def updated_at(self):
        return self.created_at

    def set_password(self, password):
        """تعيين كلمة المرور المشفرة"""
        self.password_hash = generate_password_hash(password)

    def check_password(self, password):
        """التحقق من صحة كلمة المرور"""
        return check_password_hash(self.password_hash, password)

    def has_permission(self, module, action):
        """التحقق مما إذا كان المستخدم لديه صلاحية محددة"""
        # المدير لديه جميع الصلاحيات
        if self.role == 'admin':
            return True

        # البحث عن الصلاحية المحددة
        permission = Permission.query.filter_by(module=module, action=action).first()
        if not permission:
            return False

        # التحقق من وجود الصلاحية للمستخدم
        user_permission = UserPermission.query.filter_by(
            user_id=self.id,
            permission_id=permission.id,
            granted=True
        ).first()

        return user_permission is not None

    def generate_reset_token(self, expiry_hours=24):
        """إنشاء رمز إعادة تعيين كلمة المرور"""
        import secrets
        import string

        # إنشاء رمز عشوائي
        token_chars = string.ascii_letters + string.digits
        token = ''.join(secrets.choice(token_chars) for _ in range(32))

        # تخزين الرمز في قاعدة بيانات مؤقتة أو ملف
        # يمكن استخدام Redis أو ملف JSON لتخزين الرموز مؤقتًا
        # في هذه الحالة، سنعيد الرمز فقط
        return token

    def verify_reset_token(self, token):
        """التحقق من صحة رمز إعادة تعيين كلمة المرور"""
        # في الإصدار الحالي، لا يمكن التحقق من الرمز
        # لأننا لا نخزنه في قاعدة البيانات
        # يمكن استخدام Redis أو ملف JSON للتحقق من الرمز
        return False

    def clear_reset_token(self):
        """مسح رمز إعادة تعيين كلمة المرور"""
        # لا شيء للقيام به في الإصدار الحالي
        pass

    def update_last_login(self):
        """تحديث تاريخ آخر تسجيل دخول"""
        # لا شيء للقيام به في الإصدار الحالي
        pass

    def log_activity(self, activity_type, description, ip_address=None):
        """تسجيل نشاط المستخدم"""
        # لا شيء للقيام به في الإصدار الحالي
        pass

    def create_session(self, token, ip_address=None, user_agent=None):
        """إنشاء جلسة جديدة للمستخدم"""
        # لا شيء للقيام به في الإصدار الحالي
        return None

    def get_active_sessions(self):
        """الحصول على جلسات المستخدم النشطة"""
        # لا شيء للقيام به في الإصدار الحالي
        return []

    def invalidate_all_sessions(self):
        """إبطال جميع جلسات المستخدم"""
        # لا شيء للقيام به في الإصدار الحالي
        pass

    def to_dict(self):
        """تحويل بيانات المستخدم إلى قاموس"""
        return {
            'id': self.id,
            'username': self.username,
            'email': self.email,
            'full_name': self.full_name,
            'role': self.role,
            'created_at': self.created_at.strftime('%Y-%m-%d %H:%M:%S') if self.created_at else None
        }


class Category(db.Model):
    __table_args__ = {'extend_existing': True}
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(64), nullable=False)
    description = db.Column(db.String(256))
    color = db.Column(db.String(20), default='blue')  # CSS color class
    products = db.relationship('Product', backref='category', lazy=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    def to_dict(self):
        return {
            'id': self.id,
            'name': self.name,
            'description': self.description,
            'color': self.color,
            'product_count': len(self.products)
        }


class Warehouse(db.Model):
    __table_args__ = {'extend_existing': True}
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(128), nullable=False)
    location = db.Column(db.String(256))
    description = db.Column(db.Text)
    is_active = db.Column(db.Boolean, default=True)
    is_default = db.Column(db.Boolean, default=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    # جعل حقل updated_at اختياريًا لتجنب مشاكل قاعدة البيانات
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=True)
    inventories = db.relationship('Inventory', backref='warehouse', lazy=True, cascade="all, delete-orphan")

    def get_product_count(self):
        """الحصول على عدد المنتجات في المخزن"""
        return len(self.inventories)

    def get_low_stock_count(self):
        """الحصول على عدد المنتجات ذات المخزون المنخفض"""
        return sum(1 for inv in self.inventories if inv.quantity > 0 and inv.quantity <= inv.minimum_stock)

    def get_out_of_stock_count(self):
        """الحصول على عدد المنتجات التي نفذت من المخزون"""
        return sum(1 for inv in self.inventories if inv.quantity <= 0)

    def get_inventory_value(self):
        """حساب قيمة المخزون في المستودع"""
        total_value = 0
        for inventory in self.inventories:
            if inventory.product and inventory.product.cost_price:
                total_value += inventory.quantity * inventory.product.cost_price
        return total_value

    def get_low_stock_products(self):
        """الحصول على المنتجات ذات المخزون المنخفض"""
        return [inv for inv in self.inventories if inv.quantity > 0 and inv.quantity <= inv.minimum_stock]

    def get_out_of_stock_products(self):
        """الحصول على المنتجات التي نفذت من المخزون"""
        return [inv for inv in self.inventories if inv.quantity <= 0]

    def get_inventory_by_product(self, product_id):
        """الحصول على مخزون منتج محدد في المستودع"""
        inventory = Inventory.query.filter_by(warehouse_id=self.id, product_id=product_id).first()
        return inventory if inventory else None

    def add_product(self, product_id, quantity=0, minimum_stock=5, user_id=None):
        """إضافة منتج جديد إلى المستودع"""
        # التحقق من وجود المنتج في المستودع
        inventory = self.get_inventory_by_product(product_id)

        if inventory:
            # تحديث المخزون الحالي
            inventory.update_quantity(
                new_quantity=quantity,
                movement_type='add',
                user_id=user_id,
                reference='manual_update',
                notes='تحديث يدوي للمخزون'
            )
            inventory.minimum_stock = minimum_stock
        else:
            # إنشاء مخزون جديد
            inventory = Inventory(
                warehouse_id=self.id,
                product_id=product_id,
                quantity=0,
                minimum_stock=minimum_stock
            )
            db.session.add(inventory)
            db.session.flush()  # للحصول على معرف المخزون

            # تحديث الكمية وتسجيل الحركة
            inventory.update_quantity(
                new_quantity=quantity,
                movement_type='add',
                user_id=user_id,
                reference='initial_stock',
                notes='إضافة أولية للمخزون'
            )

        return inventory

    def transfer_product(self, product_id, target_warehouse_id, quantity, user_id=None, notes=None):
        """نقل منتج من المستودع الحالي إلى مستودع آخر"""
        # التحقق من وجود المنتج في المستودع الحالي
        source_inventory = self.get_inventory_by_product(product_id)

        if not source_inventory:
            raise ValueError('المنتج غير موجود في المستودع المصدر')

        # التحقق من توفر الكمية المطلوبة
        if source_inventory.quantity < quantity:
            raise ValueError(f'الكمية المتوفرة في المستودع المصدر ({source_inventory.quantity}) أقل من الكمية المطلوبة ({quantity})')

        # الحصول على المستودع الهدف
        target_warehouse = Warehouse.query.get(target_warehouse_id)
        if not target_warehouse:
            raise ValueError('المستودع الهدف غير موجود')

        # إنشاء مرجع للنقل
        transfer_reference = f"TRANSFER-{datetime.now().strftime('%Y%m%d%H%M%S')}"

        # خصم الكمية من المستودع المصدر
        new_source_quantity = source_inventory.quantity - quantity
        source_inventory.update_quantity(
            new_quantity=new_source_quantity,
            movement_type='transfer_out',
            user_id=user_id,
            reference=transfer_reference,
            notes=f'نقل إلى {target_warehouse.name}: {notes or ""}'
        )

        # إضافة الكمية إلى المستودع الهدف
        target_inventory = target_warehouse.get_inventory_by_product(product_id)

        if target_inventory:
            # تحديث المخزون الحالي وتسجيل الحركة
            new_target_quantity = target_inventory.quantity + quantity
            target_inventory.update_quantity(
                new_quantity=new_target_quantity,
                movement_type='transfer_in',
                user_id=user_id,
                reference=transfer_reference,
                notes=f'نقل من {self.name}: {notes or ""}'
            )
        else:
            # إنشاء مخزون جديد في المستودع الهدف
            target_inventory = Inventory(
                warehouse_id=target_warehouse_id,
                product_id=product_id,
                quantity=0,
                minimum_stock=source_inventory.minimum_stock
            )
            db.session.add(target_inventory)
            db.session.flush()  # للحصول على معرف المخزون

            # تحديث الكمية وتسجيل الحركة
            target_inventory.update_quantity(
                new_quantity=quantity,
                movement_type='transfer_in',
                user_id=user_id,
                reference=transfer_reference,
                notes=f'نقل من {self.name}: {notes or ""}'
            )

        return {
            'source_inventory': source_inventory,
            'target_inventory': target_inventory,
            'transfer_reference': transfer_reference
        }

    @classmethod
    def search_by_name_or_location(cls, search_term):
        """البحث عن المخازن بالاسم أو الموقع"""
        return cls.query.filter(
            (cls.name.ilike(f'%{search_term}%') |
             cls.location.ilike(f'%{search_term}%')) &
            (cls.is_active == True)
        ).all()

    @classmethod
    def get_default_warehouse(cls):
        """الحصول على المستودع الافتراضي"""
        return cls.query.filter_by(is_default=True).first()

    @classmethod
    def set_default_warehouse(cls, warehouse_id):
        """تعيين المستودع الافتراضي"""
        # إلغاء تعيين المستودع الافتراضي الحالي
        current_default = cls.get_default_warehouse()
        if current_default:
            current_default.is_default = False

        # تعيين المستودع الجديد كافتراضي
        new_default = cls.query.get(warehouse_id)
        if new_default:
            new_default.is_default = True
            return True
        return False

    def to_dict(self):
        return {
            'id': self.id,
            'name': self.name,
            'location': self.location,
            'description': self.description,
            'is_active': self.is_active,
            'is_default': self.is_default,
            'product_count': self.get_product_count(),
            'low_stock_count': self.get_low_stock_count(),
            'out_of_stock_count': self.get_out_of_stock_count(),
            'inventory_value': self.get_inventory_value(),
            'created_at': self.created_at.strftime('%Y-%m-%d %H:%M:%S') if self.created_at else None,
            # تجنب الخطأ في حالة عدم وجود حقل updated_at
            'updated_at': self.updated_at.strftime('%Y-%m-%d %H:%M:%S') if hasattr(self, 'updated_at') and self.updated_at else None
        }

class InventoryCount(db.Model):
    """نموذج للجرد الدوري"""
    __table_args__ = {'extend_existing': True}
    id = db.Column(db.Integer, primary_key=True)
    warehouse_id = db.Column(db.Integer, db.ForeignKey('warehouse.id'), nullable=False)
    name = db.Column(db.String(128), nullable=False)
    status = db.Column(db.String(20), default='draft')  # draft, in_progress, completed, cancelled
    notes = db.Column(db.Text)
    created_by = db.Column(db.Integer, db.ForeignKey('user.id'))
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    completed_at = db.Column(db.DateTime)

    # العلاقات
    warehouse = db.relationship('Warehouse', backref='inventory_counts')
    user = db.relationship('User', backref='inventory_counts')
    items = db.relationship('InventoryCountItem', backref='count', lazy=True, cascade="all, delete-orphan")

    def to_dict(self):
        return {
            'id': self.id,
            'warehouse_id': self.warehouse_id,
            'warehouse_name': self.warehouse.name if self.warehouse else None,
            'name': self.name,
            'status': self.status,
            'notes': self.notes,
            'created_by': self.user.username if self.user else None,
            'created_at': self.created_at.strftime('%Y-%m-%d %H:%M:%S') if self.created_at else None,
            'completed_at': self.completed_at.strftime('%Y-%m-%d %H:%M:%S') if self.completed_at else None,
            'items_count': len(self.items)
        }

class InventoryCountItem(db.Model):
    """نموذج لعناصر الجرد الدوري"""
    __table_args__ = {'extend_existing': True}
    id = db.Column(db.Integer, primary_key=True)
    count_id = db.Column(db.Integer, db.ForeignKey('inventory_count.id'), nullable=False)
    product_id = db.Column(db.Integer, db.ForeignKey('product.id'), nullable=False)
    expected_quantity = db.Column(db.Integer, default=0)
    actual_quantity = db.Column(db.Integer)
    difference = db.Column(db.Integer)
    notes = db.Column(db.Text)

    # العلاقات
    product = db.relationship('Product')

    def to_dict(self):
        return {
            'id': self.id,
            'count_id': self.count_id,
            'product_id': self.product_id,
            'product_name': self.product.name if self.product else None,
            'expected_quantity': self.expected_quantity,
            'actual_quantity': self.actual_quantity,
            'difference': self.difference,
            'notes': self.notes
        }

class Notification(db.Model):
    """نموذج للإشعارات"""
    __table_args__ = {'extend_existing': True}
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'))
    title = db.Column(db.String(128), nullable=False)
    message = db.Column(db.Text, nullable=False)
    type = db.Column(db.String(20), default='info')  # info, warning, danger, success
    is_read = db.Column(db.Boolean, default=False)
    link = db.Column(db.String(256))
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    # العلاقات
    user = db.relationship('User', backref='notifications')

    def to_dict(self):
        return {
            'id': self.id,
            'user_id': self.user_id,
            'title': self.title,
            'message': self.message,
            'type': self.type,
            'is_read': self.is_read,
            'link': self.link,
            'created_at': self.created_at.strftime('%Y-%m-%d %H:%M:%S') if self.created_at else None
        }

    @classmethod
    def create_inventory_notification(cls, inventory, notification_type, user_id=None):
        """إنشاء إشعار للمخزون"""
        if notification_type == 'low_stock':
            title = 'تنبيه: مخزون منخفض'
            message = f'المنتج {inventory.product.name} في المخزن {inventory.warehouse.name} وصل للحد الأدنى. الكمية الحالية: {inventory.quantity}, الحد الأدنى: {inventory.minimum_stock}'
            type = 'warning'
        elif notification_type == 'out_of_stock':
            title = 'تنبيه: نفاذ المخزون'
            message = f'المنتج {inventory.product.name} في المخزن {inventory.warehouse.name} نفذ من المخزون. يرجى إعادة تعبئة المخزون.'
            type = 'danger'
        else:
            return None

        # إنشاء رابط للمخزون
        link = f'/warehouses/{inventory.warehouse_id}/inventory'

        # إنشاء إشعار لكل مستخدم إداري
        if user_id:
            # إنشاء إشعار لمستخدم محدد
            notification = cls(
                user_id=user_id,
                title=title,
                message=message,
                type=type,
                link=link
            )
            db.session.add(notification)
        else:
            # إنشاء إشعار لجميع المستخدمين الإداريين
            admin_users = User.query.filter_by(role='admin').all()
            for user in admin_users:
                notification = cls(
                    user_id=user.id,
                    title=title,
                    message=message,
                    type=type,
                    link=link
                )
                db.session.add(notification)

        return True

class InventoryAlert(db.Model):
    """نموذج لتنبيهات المخزون"""
    __table_args__ = {'extend_existing': True}
    id = db.Column(db.Integer, primary_key=True)
    inventory_id = db.Column(db.Integer, db.ForeignKey('inventory.id'), nullable=False)
    alert_type = db.Column(db.String(20), nullable=False)  # low_stock, out_of_stock
    message = db.Column(db.Text, nullable=False)
    is_read = db.Column(db.Boolean, default=False)
    is_resolved = db.Column(db.Boolean, default=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    resolved_at = db.Column(db.DateTime)

    # العلاقات
    inventory = db.relationship('Inventory', backref='alerts')

    def to_dict(self):
        return {
            'id': self.id,
            'inventory_id': self.inventory_id,
            'product_name': self.inventory.product.name if self.inventory and self.inventory.product else None,
            'warehouse_name': self.inventory.warehouse.name if self.inventory and self.inventory.warehouse else None,
            'alert_type': self.alert_type,
            'message': self.message,
            'is_read': self.is_read,
            'is_resolved': self.is_resolved,
            'created_at': self.created_at.strftime('%Y-%m-%d %H:%M:%S') if self.created_at else None,
            'resolved_at': self.resolved_at.strftime('%Y-%m-%d %H:%M:%S') if self.resolved_at else None
        }

class InventoryMovement(db.Model):
    """نموذج لتتبع حركة المخزون"""
    __table_args__ = {'extend_existing': True}
    id = db.Column(db.Integer, primary_key=True)
    product_id = db.Column(db.Integer, db.ForeignKey('product.id'), nullable=False)
    warehouse_id = db.Column(db.Integer, db.ForeignKey('warehouse.id'), nullable=False)
    movement_type = db.Column(db.String(20), nullable=False)  # add, remove, transfer_in, transfer_out
    quantity = db.Column(db.Integer, nullable=False)
    previous_quantity = db.Column(db.Integer, nullable=False)
    new_quantity = db.Column(db.Integer, nullable=False)
    reference = db.Column(db.String(128))  # مرجع للحركة (مثل رقم الطلب أو رقم الفاتورة)
    notes = db.Column(db.Text)
    created_by = db.Column(db.Integer, db.ForeignKey('user.id'))
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    # العلاقات
    product = db.relationship('Product', backref='movements')
    warehouse = db.relationship('Warehouse', backref='movements')
    user = db.relationship('User', backref='inventory_movements')

    def to_dict(self):
        return {
            'id': self.id,
            'product_id': self.product_id,
            'product_name': self.product.name if self.product else None,
            'warehouse_id': self.warehouse_id,
            'warehouse_name': self.warehouse.name if self.warehouse else None,
            'movement_type': self.movement_type,
            'quantity': self.quantity,
            'previous_quantity': self.previous_quantity,
            'new_quantity': self.new_quantity,
            'reference': self.reference,
            'notes': self.notes,
            'created_by': self.user.username if self.user else None,
            'created_at': self.created_at.strftime('%Y-%m-%d %H:%M:%S') if self.created_at else None
        }

class Inventory(db.Model):
    __table_args__ = {'extend_existing': True}
    id = db.Column(db.Integer, primary_key=True)
    product_id = db.Column(db.Integer, db.ForeignKey('product.id'), nullable=False)
    warehouse_id = db.Column(db.Integer, db.ForeignKey('warehouse.id'), nullable=False)
    quantity = db.Column(db.Integer, default=0)
    minimum_stock = db.Column(db.Integer, default=5)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # إضافة فهرس مركب للبحث السريع
    __table_args__ = (db.UniqueConstraint('product_id', 'warehouse_id', name='uix_product_warehouse'),)

    def get_status(self):
        """الحصول على حالة المخزون"""
        if self.quantity <= 0:
            return 'out_of_stock'
        elif self.quantity <= self.minimum_stock:
            return 'low_stock'
        else:
            return 'in_stock'

    def update_quantity(self, new_quantity, movement_type, user_id=None, reference=None, notes=None):
        """تحديث كمية المخزون وتسجيل الحركة"""
        previous_quantity = self.quantity
        quantity_change = new_quantity - previous_quantity

        # تحديث الكمية
        self.quantity = new_quantity

        # تسجيل حركة المخزون
        movement = InventoryMovement(
            product_id=self.product_id,
            warehouse_id=self.warehouse_id,
            movement_type=movement_type,
            quantity=abs(quantity_change),
            previous_quantity=previous_quantity,
            new_quantity=new_quantity,
            reference=reference,
            notes=notes,
            created_by=user_id
        )

        db.session.add(movement)

        # التحقق من حالة المخزون وإنشاء تنبيهات إذا لزم الأمر
        self.check_and_create_alerts()

        return movement

    def check_and_create_alerts(self):
        """التحقق من حالة المخزون وإنشاء تنبيهات إذا لزم الأمر"""
        # التحقق من وجود تنبيهات غير محلولة
        existing_alerts = InventoryAlert.query.filter_by(
            inventory_id=self.id,
            is_resolved=False
        ).all()

        # حالة المخزون المنخفض
        if 0 < self.quantity <= self.minimum_stock:
            # التحقق من وجود تنبيه مخزون منخفض غير محلول
            low_stock_alert_exists = any(alert.alert_type == 'low_stock' for alert in existing_alerts)

            if not low_stock_alert_exists:
                # إنشاء تنبيه مخزون منخفض
                alert = InventoryAlert(
                    inventory_id=self.id,
                    alert_type='low_stock',
                    message=f'المنتج {self.product.name} في المخزن {self.warehouse.name} وصل للحد الأدنى. الكمية الحالية: {self.quantity}, الحد الأدنى: {self.minimum_stock}'
                )
                db.session.add(alert)

                # إنشاء إشعار للمستخدمين
                Notification.create_inventory_notification(self, 'low_stock')

        # حالة نفاذ المخزون
        elif self.quantity <= 0:
            # التحقق من وجود تنبيه نفاذ المخزون غير محلول
            out_of_stock_alert_exists = any(alert.alert_type == 'out_of_stock' for alert in existing_alerts)

            if not out_of_stock_alert_exists:
                # إنشاء تنبيه نفاذ المخزون
                alert = InventoryAlert(
                    inventory_id=self.id,
                    alert_type='out_of_stock',
                    message=f'المنتج {self.product.name} في المخزن {self.warehouse.name} نفذ من المخزون. يرجى إعادة تعبئة المخزون.'
                )
                db.session.add(alert)

                # إنشاء إشعار للمستخدمين
                Notification.create_inventory_notification(self, 'out_of_stock')

        # حالة المخزون الطبيعي (تحديث التنبيهات الموجودة)
        else:
            # تحديث التنبيهات الموجودة كمحلولة
            for alert in existing_alerts:
                alert.is_resolved = True
                alert.resolved_at = datetime.utcnow()

    @classmethod
    def get_low_stock(cls, warehouse_id=None):
        """الحصول على المنتجات ذات المخزون المنخفض"""
        query = cls.query.filter(cls.quantity > 0, cls.quantity <= cls.minimum_stock)
        if warehouse_id:
            query = query.filter_by(warehouse_id=warehouse_id)
        return query.all()

    @classmethod
    def get_out_of_stock(cls, warehouse_id=None):
        """الحصول على المنتجات التي نفذت من المخزون"""
        query = cls.query.filter(cls.quantity <= 0)
        if warehouse_id:
            query = query.filter_by(warehouse_id=warehouse_id)
        return query.all()

    def to_dict(self):
        return {
            'id': self.id,
            'product_id': self.product_id,
            'product_name': self.product.name if self.product else None,
            'warehouse_id': self.warehouse_id,
            'warehouse_name': self.warehouse.name if self.warehouse else None,
            'quantity': self.quantity,
            'minimum_stock': self.minimum_stock,
            'status': self.get_status(),
            'updated_at': self.updated_at.strftime('%Y-%m-%d %H:%M:%S') if self.updated_at else None
        }

class Product(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(128), nullable=False)
    code = db.Column(db.String(64), unique=True)
    brand = db.Column(db.String(64))
    description = db.Column(db.Text)
    price = db.Column(db.Float, nullable=False)
    cost_price = db.Column(db.Float)
    stock_quantity = db.Column(db.Integer, default=0)
    minimum_stock = db.Column(db.Integer, default=5)
    category_id = db.Column(db.Integer, db.ForeignKey('category.id'))
    supplier_id = db.Column(db.Integer, db.ForeignKey('supplier.id'))
    image_path = db.Column(db.String(256))
    is_active = db.Column(db.Boolean, default=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    inventories = db.relationship('Inventory', backref='product', lazy=True, cascade="all, delete-orphan")

    def get_total_inventory(self):
        """حساب إجمالي المخزون من جميع المستودعات"""
        return sum(inv.quantity for inv in self.inventories)

    def get_inventory_by_warehouse(self, warehouse_id):
        """الحصول على مخزون المنتج في مخزن محدد"""
        inventory = Inventory.query.filter_by(product_id=self.id, warehouse_id=warehouse_id).first()
        return inventory.quantity if inventory else 0

    @classmethod
    def search_by_name_or_code(cls, search_term):
        """البحث عن المنتجات بالاسم أو الباركود"""
        return cls.query.filter(
            (cls.name.ilike(f'%{search_term}%') |
             cls.code.ilike(f'%{search_term}%')) &
            (cls.is_active == True)
        ).all()

    def to_dict(self):
        return {
            'id': self.id,
            'name': self.name,
            'code': self.code,
            'brand': self.brand,
            'description': self.description,
            'price': self.price,
            'cost_price': self.cost_price,
            'stock_quantity': self.stock_quantity,
            'minimum_stock': self.minimum_stock,
            'total_inventory': self.get_total_inventory(),
            'category_id': self.category_id,
            'category_name': self.category.name if self.category else None,
            'supplier_id': self.supplier_id,
            'supplier_name': self.supplier.name if self.supplier else None,
            'is_active': self.is_active,
            'stock_status': self.get_stock_status(),
            'inventories': [inv.to_dict() for inv in self.inventories]
        }

    def get_stock_status(self):
        total_inventory = self.get_total_inventory()
        if total_inventory <= 0:
            return 'out_of_stock'
        elif total_inventory <= self.minimum_stock:
            return 'low_stock'
        else:
            return 'in_stock'


class Customer(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(128), nullable=False)
    phone = db.Column(db.String(20))
    email = db.Column(db.String(120))
    address = db.Column(db.String(256))
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    orders = db.relationship('Order', backref='customer', lazy=True)

    def to_dict(self):
        return {
            'id': self.id,
            'name': self.name,
            'phone': self.phone,
            'email': self.email,
            'address': self.address
        }


class Order(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    invoice_number = db.Column(db.String(20), unique=True)
    # نحتاج لإضافة barcode عندما تكون قاعدة البيانات جاهزة
    # barcode = db.Column(db.String(50), unique=True)
    customer_id = db.Column(db.Integer, db.ForeignKey('customer.id'))
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'))
    warehouse_id = db.Column(db.Integer, db.ForeignKey('warehouse.id'), nullable=True)  # المخزن المرتبط بالطلب
    cash_register_id = db.Column(db.Integer, db.ForeignKey('cash_register.id'))  # الخزينة المرتبطة بالطلب
    subtotal = db.Column(db.Float, nullable=False)
    discount = db.Column(db.Float, default=0)
    discount_type = db.Column(db.String(10), default='fixed')  # fixed or percentage
    tax = db.Column(db.Float, default=0)
    tax_percentage = db.Column(db.Float, default=0)
    total = db.Column(db.Float, nullable=False)
    payment_method = db.Column(db.String(20), default='cash')  # cash, card
    payment_status = db.Column(db.String(20), default='unpaid')  # unpaid, partial, paid
    status = db.Column(db.String(20), default='completed')  # completed, pending, cancelled, suspended, deferred
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    items = db.relationship('OrderItem', backref='order', lazy=True, cascade="all, delete-orphan")
    # العلاقات
    warehouse = db.relationship('Warehouse', backref='orders')
    cash_register = db.relationship('CashRegister', backref='orders')
    # حذف الأعمدة غير الموجودة في قاعدة البيانات الحالية
    # is_returned = db.Column(db.Boolean, default=False)
    # return_date = db.Column(db.DateTime)
    # cash_drawer_opened = db.Column(db.Boolean, default=False)

    # وظائف الربط المحاسبي
    after_save_hooks = []  # قائمة بوظائف ما بعد الحفظ

    def save(self):
        """حفظ الطلب في قاعدة البيانات وتنفيذ وظائف ما بعد الحفظ"""
        is_new = self.id is None
        db.session.add(self)
        db.session.commit()

        # تنفيذ وظائف ما بعد الحفظ
        for hook in self.after_save_hooks:
            hook(self, created=is_new)

        return self

    def update_payment_status(self):
        """تحديث حالة الدفع للطلب"""
        # حساب إجمالي المدفوعات
        total_payments = db.session.query(func.sum(Payment.amount)).filter(
            Payment.order_id == self.id
        ).scalar() or 0

        # تحديث حالة الطلب بناءً على المدفوعات
        if total_payments >= self.total:
            self.payment_status = 'paid'
        elif total_payments > 0:
            self.payment_status = 'partial'
        else:
            self.payment_status = 'unpaid'

        return self.payment_status

    def get_user(self):
        """الحصول على معلومات المستخدم الذي أنشأ الطلب"""
        if hasattr(self, 'user_id') and self.user_id:
            from models import User
            return User.query.get(self.user_id)
        return None

    def to_dict(self):
        # حساب المبلغ المدفوع والمتبقي
        paid_amount = sum(payment.amount for payment in self.payments) if hasattr(self, 'payments') else 0
        remaining_amount = self.total - paid_amount

        # التحقق من كون الطلب مؤجل
        is_deferred = self.payment_method == 'deferred' or self.status == 'deferred'

        # الحصول على معلومات المستخدم
        user = self.get_user()

        return {
            'id': self.id,
            'invoice_number': self.invoice_number,
            # 'barcode': self.barcode,  # معطل مؤقتاً
            'customer_id': self.customer_id,
            'customer_name': self.customer.name if self.customer else None,
            'user_id': self.user_id if hasattr(self, 'user_id') else None,
            'user_name': user.username if user else None,
            'warehouse_id': self.warehouse_id,
            'warehouse_name': self.warehouse.name if self.warehouse else None,
            'subtotal': self.subtotal,
            'discount': self.discount,
            'discount_type': self.discount_type,
            'tax': self.tax,
            'tax_percentage': self.tax_percentage,
            'total': self.total,
            'payment_method': self.payment_method,
            'status': self.status,
            'is_deferred': is_deferred,
            'paid_amount': paid_amount,
            'remaining_amount': remaining_amount,
            'created_at': self.created_at.strftime('%Y-%m-%d %H:%M:%S'),
            'items': [item.to_dict() for item in self.items]
        }

    def update_payment_status(self):
        """تحديث حالة الدفع بناءً على المدفوعات"""
        # حساب إجمالي المدفوعات
        total_payments = sum(payment.amount for payment in self.payments) if hasattr(self, 'payments') else 0

        # تحديث الحالة
        is_deferred = self.payment_method == 'deferred' or self.status == 'deferred'

        # إذا كان الطلب آجل
        if is_deferred:
            # إذا تم دفع كامل المبلغ
            if total_payments >= self.total:
                self.status = 'completed'
            else:
                self.status = 'deferred'
        # إذا كان الطلب غير آجل ولكن المبلغ المدفوع أقل من الإجمالي
        elif total_payments < self.total and self.payment_method != 'cash' and self.payment_method != 'card':
            self.status = 'deferred'
            self.payment_method = 'deferred'

        return self.status


class OrderItem(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    order_id = db.Column(db.Integer, db.ForeignKey('order.id'), nullable=False)
    product_id = db.Column(db.Integer, db.ForeignKey('product.id'), nullable=False)
    product = db.relationship('Product')
    quantity = db.Column(db.Integer, nullable=False)
    price = db.Column(db.Float, nullable=False)
    total = db.Column(db.Float, nullable=False)
    returned_quantity = db.Column(db.Integer, default=0)

    # العلاقة مع المرتجعات
    returns = db.relationship('ReturnItem', backref='order_item', lazy=True)

    def to_dict(self):
        return {
            'id': self.id,
            'product_id': self.product_id,
            'product_name': self.product.name,
            'quantity': self.quantity,
            'price': self.price,
            'total': self.total,
            'returned_quantity': self.returned_quantity,
            'is_returned': self.returned_quantity >= self.quantity,
            'available_for_return': self.quantity - self.returned_quantity
        }

    def can_return(self, quantity):
        """التحقق من إمكانية إرجاع كمية محددة من المنتج"""
        return quantity > 0 and quantity <= (self.quantity - self.returned_quantity)

    def return_items(self, quantity, user_id=None):
        """إرجاع كمية محددة من المنتج"""
        if not self.can_return(quantity):
            return False, "الكمية المطلوبة غير متاحة للإرجاع"

        # تحديث الكمية المرتجعة
        self.returned_quantity += quantity

        # إعادة المنتج إلى المخزن المحدد
        if self.order.warehouse_id:
            # البحث عن المخزون في المخزن المحدد
            inventory = Inventory.query.filter_by(
                product_id=self.product_id,
                warehouse_id=self.order.warehouse_id
            ).first()

            if inventory:
                # تحديث المخزون في المخزن المحدد
                inventory.update_quantity(
                    new_quantity=inventory.quantity + quantity,
                    movement_type='return',
                    user_id=user_id,
                    reference=f'RETURN-{self.order.invoice_number}',
                    notes=f'إرجاع منتج من الفاتورة {self.order.invoice_number}'
                )
            else:
                # إذا لم يكن المنتج موجودًا في المخزن، إنشاء مخزون جديد
                inventory = Inventory(
                    product_id=self.product_id,
                    warehouse_id=self.order.warehouse_id,
                    quantity=quantity
                )
                db.session.add(inventory)
        else:
            # إذا لم يكن هناك مخزن محدد، تحديث الكمية العامة للمنتج
            product = Product.query.get(self.product_id)
            if product:
                product.stock_quantity += quantity

        return True, "تم إرجاع المنتج بنجاح"


class Supplier(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(128), nullable=False)
    contact_person = db.Column(db.String(128))
    phone = db.Column(db.String(20))
    email = db.Column(db.String(120))
    address = db.Column(db.String(256))
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    purchases = db.relationship('Purchase', backref='supplier', lazy=True)
    products = db.relationship('Product', backref='supplier', lazy=True)

    def to_dict(self):
        return {
            'id': self.id,
            'name': self.name,
            'contact_person': self.contact_person,
            'phone': self.phone,
            'email': self.email,
            'address': self.address
        }


class Purchase(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    reference_number = db.Column(db.String(20), unique=True, nullable=False)
    supplier_id = db.Column(db.Integer, db.ForeignKey('supplier.id'))
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'))
    cash_register_id = db.Column(db.Integer, db.ForeignKey('cash_register.id'))
    total = db.Column(db.Float, nullable=False)
    payment_status = db.Column(db.String(20), default='unpaid')  # unpaid, partial, paid
    status = db.Column(db.String(20), default='received')  # ordered, received, pending, deferred
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    expected_delivery = db.Column(db.DateTime)
    items = db.relationship('PurchaseItem', backref='purchase', lazy=True, cascade="all, delete-orphan")

    # العلاقات
    cash_register = db.relationship('CashRegister', backref='purchases')

    # وظائف الربط المحاسبي
    after_save_hooks = []  # قائمة بوظائف ما بعد الحفظ

    def save(self):
        """حفظ المشتريات في قاعدة البيانات وتنفيذ وظائف ما بعد الحفظ"""
        is_new = self.id is None
        db.session.add(self)
        db.session.commit()

        # تنفيذ وظائف ما بعد الحفظ
        for hook in self.after_save_hooks:
            hook(self, created=is_new)

        return self

    def update_payment_status(self):
        """تحديث حالة الدفع للمشتريات"""
        # حساب إجمالي المدفوعات
        total_payments = db.session.query(func.sum(Payment.amount)).filter(
            Payment.purchase_id == self.id
        ).scalar() or 0

        # تحديث حالة المشتريات بناءً على المدفوعات
        if total_payments >= self.total:
            self.payment_status = 'paid'
        elif total_payments > 0:
            self.payment_status = 'partial'
        else:
            self.payment_status = 'unpaid'

        return self.payment_status

    def to_dict(self):
        # حساب المبلغ المدفوع والمتبقي
        paid_amount = sum(payment.amount for payment in self.payments) if hasattr(self, 'payments') else 0
        remaining_amount = self.total - paid_amount

        # التحقق من كون الشراء مؤجل
        is_deferred = self.status == 'deferred'

        return {
            'id': self.id,
            'reference_number': self.reference_number,
            'supplier_id': self.supplier_id,
            'supplier_name': self.supplier.name if self.supplier else None,
            'total': self.total,
            'status': self.status,
            'is_deferred': is_deferred,
            'paid_amount': paid_amount,
            'remaining_amount': remaining_amount,
            'created_at': self.created_at.strftime('%Y-%m-%d %H:%M:%S'),
            'expected_delivery': self.expected_delivery.strftime('%Y-%m-%d') if self.expected_delivery else None,
            'items': [item.to_dict() for item in self.items]
        }

    def update_payment_status(self):
        """تحديث حالة الدفع بناءً على المدفوعات"""
        # حساب إجمالي المدفوعات
        total_payments = sum(payment.amount for payment in self.payments) if hasattr(self, 'payments') else 0

        # تحديث الحالة
        is_deferred = self.status == 'deferred'
        if is_deferred:
            if total_payments >= self.total:
                self.status = 'received'
            else:
                self.status = 'deferred'

        return self.status


class Settings(db.Model):
    """نموذج إعدادات المتجر"""
    id = db.Column(db.Integer, primary_key=True)
    store_name = db.Column(db.String(128), nullable=False, default="متجري")
    address = db.Column(db.String(256))
    phone = db.Column(db.String(20))
    email = db.Column(db.String(120))
    tax_percentage = db.Column(db.Float, default=0)
    currency = db.Column(db.String(10), default="ج.م")
    logo = db.Column(db.String(256))
    receipt_footer = db.Column(db.Text)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    def to_dict(self):
        return {
            'id': self.id,
            'store_name': self.store_name,
            'address': self.address,
            'phone': self.phone,
            'email': self.email,
            'tax_percentage': self.tax_percentage,
            'currency': self.currency,
            'logo': self.logo,
            'receipt_footer': self.receipt_footer,
            'updated_at': self.updated_at.strftime('%Y-%m-%d %H:%M:%S') if self.updated_at else None
        }


class PurchaseItem(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    purchase_id = db.Column(db.Integer, db.ForeignKey('purchase.id'), nullable=False)
    product_id = db.Column(db.Integer, db.ForeignKey('product.id'), nullable=False)
    product = db.relationship('Product')
    quantity = db.Column(db.Integer, nullable=False)
    cost_price = db.Column(db.Float, nullable=False)
    total = db.Column(db.Float, nullable=False)

    def to_dict(self):
        return {
            'id': self.id,
            'product_id': self.product_id,
            'product_name': self.product.name,
            'quantity': self.quantity,
            'cost_price': self.cost_price,
            'total': self.total
        }


class Payment(db.Model):
    """نموذج لتسجيل المدفوعات"""
    id = db.Column(db.Integer, primary_key=True)
    reference_number = db.Column(db.String(20), unique=True, nullable=False)
    amount = db.Column(db.Float, nullable=False)
    payment_method = db.Column(db.String(20), default='cash')  # cash, card, bank_transfer, check
    payment_date = db.Column(db.DateTime, default=datetime.utcnow)
    notes = db.Column(db.Text)
    created_by = db.Column(db.Integer, db.ForeignKey('user.id'))
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    # حقول للربط مع الطلبات أو المشتريات
    order_id = db.Column(db.Integer, db.ForeignKey('order.id'))
    purchase_id = db.Column(db.Integer, db.ForeignKey('purchase.id'))

    # العلاقات
    user = db.relationship('User', backref='payments')
    order = db.relationship('Order', backref='payments')
    purchase = db.relationship('Purchase', backref='payments')

    # وظائف الربط المحاسبي
    after_save_hooks = []  # قائمة بوظائف ما بعد الحفظ

    def save(self):
        """حفظ الدفعة في قاعدة البيانات وتنفيذ وظائف ما بعد الحفظ"""
        is_new = self.id is None
        db.session.add(self)
        db.session.commit()

        # تنفيذ وظائف ما بعد الحفظ
        for hook in self.after_save_hooks:
            hook(self, created=is_new)

        # تنفيذ الإجراءات بعد الإضافة
        if is_new:
            self.after_add()

        return self

    def after_add(self):
        """تنفيذ الإجراءات بعد إضافة الدفعة"""
        # تحديث حالة الطلب إذا كان مرتبطًا بطلب
        if self.order_id:
            self.order.update_payment_status()
            db.session.add(self.order)

        # تحديث حالة المشتريات إذا كانت مرتبطة بمشتريات
        if self.purchase_id:
            self.purchase.update_payment_status()
            db.session.add(self.purchase)

    def to_dict(self):
        return {
            'id': self.id,
            'reference_number': self.reference_number,
            'amount': self.amount,
            'payment_method': self.payment_method,
            'payment_date': self.payment_date.strftime('%Y-%m-%d %H:%M:%S') if self.payment_date else None,
            'notes': self.notes,
            'created_by': self.user.username if self.user else None,
            'created_at': self.created_at.strftime('%Y-%m-%d %H:%M:%S'),
            'order_id': self.order_id,
            'purchase_id': self.purchase_id,
            'related_to': 'order' if self.order_id else 'purchase' if self.purchase_id else None
        }

    @classmethod
    def generate_reference_number(cls):
        """توليد رقم مرجعي فريد للدفعة"""
        prefix = "PAY"
        date_part = datetime.utcnow().strftime('%Y%m%d')

        # البحث عن آخر رقم مرجعي لهذا اليوم
        last_payment = cls.query.filter(
            cls.reference_number.like(f"{prefix}-{date_part}-%")
        ).order_by(cls.id.desc()).first()

        if last_payment:
            # استخراج الرقم التسلسلي من آخر رقم مرجعي
            try:
                seq_num = int(last_payment.reference_number.split('-')[-1]) + 1
            except:
                seq_num = 1
        else:
            seq_num = 1

        return f"{prefix}-{date_part}-{seq_num:04d}"


class DeferredPaymentPlan(db.Model):
    """نموذج لخطط الدفع المؤجل"""
    id = db.Column(db.Integer, primary_key=True)
    total_amount = db.Column(db.Float, nullable=False)
    paid_amount = db.Column(db.Float, default=0)
    remaining_amount = db.Column(db.Float, nullable=False)
    installments_count = db.Column(db.Integer, default=1)
    start_date = db.Column(db.DateTime, default=datetime.utcnow)
    end_date = db.Column(db.DateTime)
    status = db.Column(db.String(20), default='active')  # active, completed, cancelled
    notes = db.Column(db.Text)
    created_by = db.Column(db.Integer, db.ForeignKey('user.id'))
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    # حقول للربط مع الطلبات أو المشتريات
    order_id = db.Column(db.Integer, db.ForeignKey('order.id'))
    purchase_id = db.Column(db.Integer, db.ForeignKey('purchase.id'))

    # العلاقات
    user = db.relationship('User', backref='deferred_payment_plans')
    order = db.relationship('Order', backref='deferred_payment_plan')
    purchase = db.relationship('Purchase', backref='deferred_payment_plan')
    installments = db.relationship('DeferredPaymentInstallment', backref='plan', lazy=True, cascade="all, delete-orphan")

    def to_dict(self):
        return {
            'id': self.id,
            'total_amount': self.total_amount,
            'paid_amount': self.paid_amount,
            'remaining_amount': self.remaining_amount,
            'installments_count': self.installments_count,
            'start_date': self.start_date.strftime('%Y-%m-%d') if self.start_date else None,
            'end_date': self.end_date.strftime('%Y-%m-%d') if self.end_date else None,
            'status': self.status,
            'notes': self.notes,
            'created_by': self.user.username if self.user else None,
            'created_at': self.created_at.strftime('%Y-%m-%d %H:%M:%S'),
            'order_id': self.order_id,
            'purchase_id': self.purchase_id,
            'related_to': 'order' if self.order_id else 'purchase' if self.purchase_id else None,
            'installments': [installment.to_dict() for installment in self.installments]
        }

    def update_status(self):
        """تحديث حالة خطة الدفع بناءً على المدفوعات"""
        if self.paid_amount >= self.total_amount:
            self.status = 'completed'
            self.remaining_amount = 0
        else:
            self.remaining_amount = self.total_amount - self.paid_amount

        return self.status


class DeferredPaymentInstallment(db.Model):
    """نموذج لأقساط الدفع المؤجل"""
    id = db.Column(db.Integer, primary_key=True)
    plan_id = db.Column(db.Integer, db.ForeignKey('deferred_payment_plan.id'), nullable=False)
    amount = db.Column(db.Float, nullable=False)
    due_date = db.Column(db.DateTime, nullable=False)
    payment_date = db.Column(db.DateTime)
    status = db.Column(db.String(20), default='pending')  # pending, paid, overdue
    payment_id = db.Column(db.Integer, db.ForeignKey('payment.id'))
    notes = db.Column(db.Text)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    # العلاقات
    payment = db.relationship('Payment', backref='installment')

    def to_dict(self):
        return {
            'id': self.id,
            'plan_id': self.plan_id,
            'amount': self.amount,
            'due_date': self.due_date.strftime('%Y-%m-%d') if self.due_date else None,
            'payment_date': self.payment_date.strftime('%Y-%m-%d') if self.payment_date else None,
            'status': self.status,
            'payment_id': self.payment_id,
            'notes': self.notes,
            'created_at': self.created_at.strftime('%Y-%m-%d %H:%M:%S')
        }

    def mark_as_paid(self, payment_id, payment_date=None):
        """تحديث حالة القسط كمدفوع"""
        self.status = 'paid'
        self.payment_id = payment_id
        self.payment_date = payment_date or datetime.utcnow()

        # تحديث المبلغ المدفوع في خطة الدفع
        self.plan.paid_amount += self.amount
        self.plan.update_status()

        return True


class CashRegister(db.Model):
    """نموذج الخزينة"""
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(128), nullable=False)
    description = db.Column(db.Text)
    is_active = db.Column(db.Boolean, default=True)
    is_default = db.Column(db.Boolean, default=False)
    current_balance = db.Column(db.Float, default=0)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # العلاقات
    transactions = db.relationship('CashTransaction', backref='cash_register', lazy=True, cascade="all, delete-orphan")
    shifts = db.relationship('Shift', backref='cash_register', lazy=True)

    def to_dict(self):
        return {
            'id': self.id,
            'name': self.name,
            'description': self.description,
            'is_active': self.is_active,
            'is_default': self.is_default,
            'current_balance': self.current_balance,
            'created_at': self.created_at.strftime('%Y-%m-%d %H:%M:%S'),
            'updated_at': self.updated_at.strftime('%Y-%m-%d %H:%M:%S')
        }


class CashTransaction(db.Model):
    """نموذج معاملات الخزينة"""
    id = db.Column(db.Integer, primary_key=True)
    cash_register_id = db.Column(db.Integer, db.ForeignKey('cash_register.id'), nullable=False)
    transaction_type = db.Column(db.String(20), nullable=False)  # deposit, withdraw, transfer
    amount = db.Column(db.Float, nullable=False)
    previous_balance = db.Column(db.Float, nullable=False)
    new_balance = db.Column(db.Float, nullable=False)
    reference = db.Column(db.String(128))  # مرجع للمعاملة (مثل رقم الطلب أو رقم الفاتورة)
    notes = db.Column(db.Text)
    created_by = db.Column(db.Integer, db.ForeignKey('user.id'))
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    daily_closure_id = db.Column(db.Integer, db.ForeignKey('daily_cash_closure.id'))

    # العلاقات
    user = db.relationship('User', backref='cash_transactions')
    daily_closure = db.relationship('DailyCashClosure', back_populates='cash_transactions')

    # حقول للربط مع الطلبات أو المشتريات أو الشيفت
    order_id = db.Column(db.Integer, db.ForeignKey('order.id'))
    purchase_id = db.Column(db.Integer, db.ForeignKey('purchase.id'))
    shift_id = db.Column(db.Integer, db.ForeignKey('shift.id'))

    def to_dict(self):
        return {
            'id': self.id,
            'cash_register_id': self.cash_register_id,
            'cash_register_name': self.cash_register.name if self.cash_register else None,
            'transaction_type': self.transaction_type,
            'amount': self.amount,
            'previous_balance': self.previous_balance,
            'new_balance': self.new_balance,
            'reference': self.reference,
            'notes': self.notes,
            'created_by': self.user.username if self.user else None,
            'created_at': self.created_at.strftime('%Y-%m-%d %H:%M:%S'),
            'order_id': self.order_id,
            'purchase_id': self.purchase_id,
            'shift_id': self.shift_id
        }


class ReturnOrder(db.Model):
    """نموذج لتتبع عمليات إرجاع المبيعات"""
    id = db.Column(db.Integer, primary_key=True)
    reference_number = db.Column(db.String(20), unique=True, nullable=False)
    order_id = db.Column(db.Integer, db.ForeignKey('order.id'), nullable=False)
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    total_amount = db.Column(db.Float, default=0)
    payment_method = db.Column(db.String(20), default='cash')  # cash, card, store_credit
    refund_method = db.Column(db.String(20), default='cash')  # cash, card, store_credit
    status = db.Column(db.String(20), default='completed')  # completed, pending, cancelled
    notes = db.Column(db.Text)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    # العلاقات
    order = db.relationship('Order', backref='returns')
    user = db.relationship('User', backref='returns')
    items = db.relationship('ReturnItem', backref='return_order', lazy=True, cascade="all, delete-orphan")

    # وظائف الربط المحاسبي
    after_save_hooks = []  # قائمة بوظائف ما بعد الحفظ

    def save(self):
        """حفظ المرتجع في قاعدة البيانات وتنفيذ وظائف ما بعد الحفظ"""
        is_new = self.id is None
        db.session.add(self)
        db.session.commit()

        # تنفيذ وظائف ما بعد الحفظ
        for hook in self.after_save_hooks:
            hook(self, created=is_new)

        return self

    def to_dict(self):
        return {
            'id': self.id,
            'reference_number': self.reference_number,
            'order_id': self.order_id,
            'invoice_number': self.order.invoice_number if self.order else None,
            'user_id': self.user_id,
            'user_name': self.user.username if self.user else None,
            'total_amount': self.total_amount,
            'payment_method': self.payment_method,
            'status': self.status,
            'notes': self.notes,
            'created_at': self.created_at.strftime('%Y-%m-%d %H:%M:%S'),
            'items': [item.to_dict() for item in self.items]
        }

    @classmethod
    def generate_reference_number(cls):
        """توليد رقم مرجعي فريد للإرجاع"""
        prefix = "RET"
        date_part = datetime.utcnow().strftime('%Y%m%d')

        # البحث عن آخر رقم مرجعي لهذا اليوم
        last_return = cls.query.filter(
            cls.reference_number.like(f"{prefix}-{date_part}-%")
        ).order_by(cls.id.desc()).first()

        if last_return:
            # استخراج الرقم التسلسلي من آخر رقم مرجعي
            try:
                seq_num = int(last_return.reference_number.split('-')[-1]) + 1
            except:
                seq_num = 1
        else:
            seq_num = 1

        return f"{prefix}-{date_part}-{seq_num:04d}"


class ReturnItem(db.Model):
    """نموذج لعناصر الإرجاع"""
    id = db.Column(db.Integer, primary_key=True)
    return_order_id = db.Column(db.Integer, db.ForeignKey('return_order.id'), nullable=False)
    order_item_id = db.Column(db.Integer, db.ForeignKey('order_item.id'), nullable=False)
    product_id = db.Column(db.Integer, db.ForeignKey('product.id'), nullable=False)
    quantity = db.Column(db.Integer, nullable=False)
    price = db.Column(db.Float, nullable=False)
    total = db.Column(db.Float, nullable=False)
    reason = db.Column(db.String(128))
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    # العلاقات
    product = db.relationship('Product', backref='return_items')

    def to_dict(self):
        return {
            'id': self.id,
            'return_order_id': self.return_order_id,
            'order_item_id': self.order_item_id,
            'product_id': self.product_id,
            'product_name': self.product.name if self.product else None,
            'quantity': self.quantity,
            'price': self.price,
            'total': self.total,
            'reason': self.reason,
            'created_at': self.created_at.strftime('%Y-%m-%d %H:%M:%S')
        }


class Shift(db.Model):
    """نموذج الشيفت"""
    id = db.Column(db.Integer, primary_key=True)
    cash_register_id = db.Column(db.Integer, db.ForeignKey('cash_register.id'), nullable=False)
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    start_time = db.Column(db.DateTime, default=datetime.utcnow)
    end_time = db.Column(db.DateTime)
    start_balance = db.Column(db.Float, default=0)
    end_balance = db.Column(db.Float)
    expected_balance = db.Column(db.Float)
    difference = db.Column(db.Float)
    status = db.Column(db.String(20), default='open')  # open, closed
    notes = db.Column(db.Text)

    # العلاقات
    user = db.relationship('User', backref='shifts')
    transactions = db.relationship('CashTransaction', backref='shift', lazy=True)

    def to_dict(self):
        return {
            'id': self.id,
            'cash_register_id': self.cash_register_id,
            'cash_register_name': self.cash_register.name if self.cash_register else None,
            'user_id': self.user_id,
            'user_name': self.user.username if self.user else None,
            'start_time': self.start_time.strftime('%Y-%m-%d %H:%M:%S'),
            'end_time': self.end_time.strftime('%Y-%m-%d %H:%M:%S') if self.end_time else None,
            'start_balance': self.start_balance,
            'end_balance': self.end_balance,
            'expected_balance': self.expected_balance,
            'difference': self.difference,
            'status': self.status,
            'notes': self.notes
        }

    def close_shift(self, end_balance, notes=None):
        """إغلاق الشيفت"""
        if self.status == 'closed':
            return False, 'الشيفت مغلق بالفعل'

        self.end_time = datetime.utcnow()
        self.end_balance = end_balance

        # حساب الرصيد المتوقع
        sales_total = db.session.query(func.sum(Order.total)).filter(
            Order.created_at.between(self.start_time, self.end_time),
            Order.user_id == self.user_id,
            Order.payment_method == 'cash',
            Order.status == 'completed'
        ).scalar() or 0

        purchases_total = db.session.query(func.sum(Purchase.total)).filter(
            Purchase.created_at.between(self.start_time, self.end_time),
            Purchase.cash_register_id == self.cash_register_id,
            Purchase.status == 'received'
        ).scalar() or 0

        # حساب الإيداعات والسحوبات
        deposits = db.session.query(func.sum(CashTransaction.amount)).filter(
            CashTransaction.created_at.between(self.start_time, self.end_time),
            CashTransaction.cash_register_id == self.cash_register_id,
            CashTransaction.transaction_type == 'deposit',
            CashTransaction.shift_id == self.id
        ).scalar() or 0

        withdrawals = db.session.query(func.sum(CashTransaction.amount)).filter(
            CashTransaction.created_at.between(self.start_time, self.end_time),
            CashTransaction.cash_register_id == self.cash_register_id,
            CashTransaction.transaction_type == 'withdraw',
            CashTransaction.shift_id == self.id
        ).scalar() or 0

        # الرصيد المتوقع = الرصيد الافتتاحي + المبيعات - المشتريات + الإيداعات - السحوبات
        self.expected_balance = self.start_balance + sales_total - purchases_total + deposits - withdrawals

        # حساب الفرق
        self.difference = self.end_balance - self.expected_balance

        # تحديث الحالة والملاحظات
        self.status = 'closed'
        if notes:
            self.notes = notes

        return True, 'تم إغلاق الشيفت بنجاح'

class Expense(db.Model):
    """نموذج المصروفات"""
    id = db.Column(db.Integer, primary_key=True)
    reference_number = db.Column(db.String(20), unique=True, nullable=False)
    description = db.Column(db.String(256), nullable=False)
    amount = db.Column(db.Float, nullable=False)
    date = db.Column(db.Date, nullable=False)
    payment_method = db.Column(db.String(20), default='cash')  # cash, card, bank_transfer, check
    account_id = db.Column(db.Integer)  # حساب المصروفات
    category = db.Column(db.String(64))  # تصنيف المصروفات
    notes = db.Column(db.Text)
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'))
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    # العلاقات
    user = db.relationship('User', backref='expenses')

    # وظائف الربط المحاسبي
    after_save_hooks = []  # قائمة بوظائف ما بعد الحفظ

    def save(self):
        """حفظ المصروفات في قاعدة البيانات وتنفيذ وظائف ما بعد الحفظ"""
        is_new = self.id is None
        db.session.add(self)
        db.session.commit()

        # تنفيذ وظائف ما بعد الحفظ
        for hook in self.after_save_hooks:
            hook(self, created=is_new)

        return self

    def to_dict(self):
        return {
            'id': self.id,
            'reference_number': self.reference_number,
            'description': self.description,
            'amount': self.amount,
            'date': self.date.strftime('%Y-%m-%d') if self.date else None,
            'payment_method': self.payment_method,
            'account_id': self.account_id,
            'category': self.category,
            'notes': self.notes,
            'user_id': self.user_id,
            'user_name': self.user.username if self.user else None,
            'created_at': self.created_at.strftime('%Y-%m-%d %H:%M:%S')
        }

    @classmethod
    def generate_reference_number(cls):
        """توليد رقم مرجعي فريد للمصروفات"""
        prefix = "EXP"
        date_part = datetime.utcnow().strftime('%Y%m%d')

        # البحث عن آخر رقم مرجعي لهذا اليوم
        last_expense = cls.query.filter(
            cls.reference_number.like(f"{prefix}-{date_part}-%")
        ).order_by(cls.id.desc()).first()

        if last_expense:
            # استخراج الرقم التسلسلي من آخر رقم مرجعي
            try:
                seq_num = int(last_expense.reference_number.split('-')[-1]) + 1
            except:
                seq_num = 1
        else:
            seq_num = 1

        return f"{prefix}-{date_part}-{seq_num:04d}"


class DailyCashClosure(db.Model):
    """نموذج الإغلاق اليومي للخزينة"""
    id = db.Column(db.Integer, primary_key=True)
    cash_register_id = db.Column(db.Integer, db.ForeignKey('cash_register.id'), nullable=False)
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    closure_date = db.Column(db.Date, nullable=False)
    opening_balance = db.Column(db.Float, nullable=False)
    closing_balance = db.Column(db.Float, nullable=False)
    expected_balance = db.Column(db.Float, nullable=False)
    difference = db.Column(db.Float)
    total_sales = db.Column(db.Float, default=0)
    total_purchases = db.Column(db.Float, default=0)
    total_deposits = db.Column(db.Float, default=0)
    total_withdrawals = db.Column(db.Float, default=0)
    notes = db.Column(db.Text)
    status = db.Column(db.String(20), default='pending')  # pending, completed, cancelled
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    completed_at = db.Column(db.DateTime)

    # العلاقات
    cash_register = db.relationship('CashRegister', backref='daily_closures')
    user = db.relationship('User', backref='daily_closures')
    cash_transactions = db.relationship('CashTransaction', back_populates='daily_closure')

    def to_dict(self):
        return {
            'id': self.id,
            'cash_register_id': self.cash_register_id,
            'cash_register_name': self.cash_register.name if self.cash_register else None,
            'user_id': self.user_id,
            'user_name': self.user.username if self.user else None,
            'closure_date': self.closure_date.strftime('%Y-%m-%d'),
            'opening_balance': self.opening_balance,
            'closing_balance': self.closing_balance,
            'expected_balance': self.expected_balance,
            'difference': self.difference,
            'total_sales': self.total_sales,
            'total_purchases': self.total_purchases,
            'total_deposits': self.total_deposits,
            'total_withdrawals': self.total_withdrawals,
            'notes': self.notes,
            'status': self.status,
            'created_at': self.created_at.strftime('%Y-%m-%d %H:%M:%S'),
            'completed_at': self.completed_at.strftime('%Y-%m-%d %H:%M:%S') if self.completed_at else None
        }

    def calculate_daily_totals(self):
        """حساب إجماليات اليوم"""
        from datetime import datetime, timedelta

        # تحديد بداية ونهاية اليوم
        start_of_day = datetime.combine(self.closure_date, datetime.min.time())
        end_of_day = datetime.combine(self.closure_date, datetime.max.time())

        # حساب المبيعات
        self.total_sales = db.session.query(func.sum(Order.total)).filter(
            Order.created_at.between(start_of_day, end_of_day),
            Order.cash_register_id == self.cash_register_id,
            Order.status == 'completed'
        ).scalar() or 0

        # حساب المشتريات
        self.total_purchases = db.session.query(func.sum(Purchase.total)).filter(
            Purchase.created_at.between(start_of_day, end_of_day),
            Purchase.cash_register_id == self.cash_register_id,
            Purchase.status == 'received'
        ).scalar() or 0

        # حساب الإيداعات
        self.total_deposits = db.session.query(func.sum(CashTransaction.amount)).filter(
            CashTransaction.created_at.between(start_of_day, end_of_day),
            CashTransaction.cash_register_id == self.cash_register_id,
            CashTransaction.transaction_type == 'deposit'
        ).scalar() or 0

        # حساب السحوبات
        self.total_withdrawals = db.session.query(func.sum(CashTransaction.amount)).filter(
            CashTransaction.created_at.between(start_of_day, end_of_day),
            CashTransaction.cash_register_id == self.cash_register_id,
            CashTransaction.transaction_type == 'withdraw'
        ).scalar() or 0

        # حساب الرصيد المتوقع
        self.expected_balance = (
            self.opening_balance +
            self.total_sales -
            self.total_purchases +
            self.total_deposits -
            self.total_withdrawals
        )

        # حساب الفرق
        self.difference = self.closing_balance - self.expected_balance

    def complete_closure(self, closing_balance, notes=None):
        """إكمال عملية الإغلاق اليومي"""
        if self.status == 'completed':
            return False, 'تم إغلاق الخزينة لهذا اليوم بالفعل'

        self.closing_balance = closing_balance
        self.calculate_daily_totals()

        # تحديث الحالة والملاحظات
        self.status = 'completed'
        self.completed_at = datetime.utcnow()
        if notes:
            self.notes = notes

        # تحديث رصيد الخزينة
        self.cash_register.current_balance = closing_balance

        return True, 'تم إغلاق الخزينة بنجاح'