"""
واجهة برمجة التطبيقات لتسجيل وإدارة الأخطاء
"""

from flask import Blueprint, request, jsonify, current_app, Response
from flask_login import current_user, login_required
from utils.error_logger import error_logger
import logging
import json
import csv
from io import StringIO
from datetime import datetime

# إعداد التسجيل
logger = logging.getLogger(__name__)

errors_api = Blueprint('errors_api_blueprint', __name__)


@errors_api.route('/api/log-error', methods=['POST'])
def log_client_error():
    """تسجيل خطأ من جانب العميل"""
    try:
        # الحصول على بيانات الخطأ
        data = request.json

        if not data:
            return jsonify({
                'success': False,
                'message': 'لم يتم توفير بيانات الخطأ'
            }), 400

        # استخراج معلومات الخطأ
        message = data.get('message', 'خطأ غير معروف')
        stack = data.get('stack', '')
        url = data.get('url', request.referrer or '')
        timestamp = data.get('timestamp', '')
        browser = data.get('browser', '')
        os = data.get('os', '')
        component = data.get('component', '')
        severity = data.get('severity', 'error')

        # إنشاء رسالة الخطأ
        error_message = f"خطأ في جانب العميل: {message}"

        # إضافة معلومات إضافية
        extra = {
            'source': 'client',
            'client_stack': stack,
            'url': url,
            'timestamp': timestamp,
            'browser': browser,
            'os': os,
            'component': component,
            'user_id': current_user.id if not current_user.is_anonymous else None,
            'username': current_user.username if not current_user.is_anonymous else None,
            'client_ip': request.remote_addr
        }

        # تسجيل الخطأ بالمستوى المناسب
        if severity == 'warning':
            error_logger.log_warning(error_message, extra)
        elif severity == 'info':
            error_logger.log_info(error_message, extra)
        else:
            error_logger.log_error(error_message, extra)

        return jsonify({
            'success': True,
            'message': 'تم تسجيل الخطأ بنجاح',
            'error_id': timestamp  # يمكن استخدام هذا المعرف للرجوع إلى الخطأ لاحقًا
        })
    except Exception as e:
        logger.error(f"خطأ أثناء تسجيل خطأ العميل: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'حدث خطأ أثناء تسجيل الخطأ: {str(e)}'
        }), 500


@errors_api.route('/api/errors', methods=['GET'])
@login_required
def get_errors():
    """الحصول على قائمة الأخطاء"""
    # التحقق من صلاحيات المستخدم
    if current_user.role != 'admin':
        return jsonify({
            'success': False,
            'message': 'ليس لديك صلاحية الوصول إلى سجلات الأخطاء'
        }), 403

    try:
        # الحصول على معلمات التصفية
        limit = int(request.args.get('limit', 100))
        offset = int(request.args.get('offset', 0))
        search = request.args.get('search', '')
        start_date = request.args.get('start_date', '')
        end_date = request.args.get('end_date', '')
        log_type = request.args.get('log_type', '')
        source = request.args.get('source', '')
        user_id = request.args.get('user_id', '')
        ip_address = request.args.get('ip_address', '')
        module = request.args.get('module', '')
        sort_by = request.args.get('sort_by', 'timestamp')
        sort_order = request.args.get('sort_order', 'desc')

        # الحصول على سجلات الأخطاء
        logs = error_logger.get_error_logs(
            limit=limit,
            offset=offset,
            search=search,
            start_date=start_date,
            end_date=end_date,
            log_type=log_type,
            source=source,
            user_id=user_id,
            ip_address=ip_address,
            module=module,
            sort_by=sort_by,
            sort_order=sort_order
        )

        # الحصول على إجمالي عدد السجلات
        total_logs = len(error_logger.get_error_logs(
            search=search,
            start_date=start_date,
            end_date=end_date,
            log_type=log_type,
            source=source,
            user_id=user_id,
            ip_address=ip_address,
            module=module
        ))

        return jsonify({
            'success': True,
            'logs': logs,
            'total': total_logs,
            'limit': limit,
            'offset': offset
        })
    except Exception as e:
        logger.error(f"خطأ أثناء الحصول على سجلات الأخطاء: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'حدث خطأ أثناء الحصول على سجلات الأخطاء: {str(e)}'
        }), 500


@errors_api.route('/api/errors/clear', methods=['POST'])
@login_required
def clear_errors():
    """مسح سجلات الأخطاء"""
    # التحقق من صلاحيات المستخدم
    if current_user.role != 'admin':
        return jsonify({
            'success': False,
            'message': 'ليس لديك صلاحية مسح سجلات الأخطاء'
        }), 403

    try:
        # الحصول على نوع السجل المراد مسحه
        log_type = request.json.get('log_type', None)

        # مسح سجلات الأخطاء
        success = error_logger.clear_error_logs(log_type=log_type)

        if success:
            # تسجيل النشاط
            if hasattr(current_user, 'log_activity'):
                current_user.log_activity(
                    action='clear_error_logs',
                    module='settings',
                    description=f'تم مسح سجلات الأخطاء. النوع: {log_type or "الكل"}',
                    ip_address=request.remote_addr
                )

            return jsonify({
                'success': True,
                'message': f'تم مسح سجلات الأخطاء بنجاح. النوع: {log_type or "الكل"}'
            })
        else:
            return jsonify({
                'success': False,
                'message': 'فشل مسح سجلات الأخطاء'
            }), 500
    except Exception as e:
        logger.error(f"خطأ أثناء مسح سجلات الأخطاء: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'حدث خطأ أثناء مسح سجلات الأخطاء: {str(e)}'
        }), 500


@errors_api.route('/api/errors/export', methods=['GET'])
@login_required
def export_errors():
    """تصدير سجلات الأخطاء"""
    # التحقق من صلاحيات المستخدم
    if current_user.role != 'admin':
        return jsonify({
            'success': False,
            'message': 'ليس لديك صلاحية تصدير سجلات الأخطاء'
        }), 403

    try:
        # الحصول على معلمات التصفية
        search = request.args.get('search', '')
        start_date = request.args.get('start_date', '')
        end_date = request.args.get('end_date', '')
        log_type = request.args.get('log_type', '')
        source = request.args.get('source', '')
        user_id = request.args.get('user_id', '')
        ip_address = request.args.get('ip_address', '')
        module = request.args.get('module', '')
        format = request.args.get('format', 'csv')  # csv أو json

        # الحصول على سجلات الأخطاء (بدون حد)
        logs = error_logger.get_error_logs(
            limit=10000,  # حد كبير لتضمين معظم السجلات
            offset=0,
            search=search,
            start_date=start_date,
            end_date=end_date,
            log_type=log_type,
            source=source,
            user_id=user_id,
            ip_address=ip_address,
            module=module
        )

        # تنسيق التاريخ للاستخدام في اسم الملف
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')

        if format == 'json':
            # تصدير بتنسيق JSON
            output = json.dumps(logs, ensure_ascii=False, indent=2)
            mimetype = 'application/json'
            filename = f'error_logs_{timestamp}.json'
        else:
            # تصدير بتنسيق CSV
            output = StringIO()
            writer = csv.writer(output)

            # كتابة الترويسة
            writer.writerow(['التاريخ والوقت', 'النوع', 'المصدر', 'الرسالة', 'المستخدم', 'عنوان IP', 'الملف', 'السطر', 'الدالة', 'التفاصيل'])

            # كتابة البيانات
            for log in logs:
                writer.writerow([
                    log.get('timestamp', ''),
                    log.get('level', ''),
                    log.get('source', ''),
                    log.get('message', ''),
                    log.get('username', ''),
                    log.get('ip_address', ''),
                    log.get('file', ''),
                    log.get('line', ''),
                    log.get('function', ''),
                    '\n'.join(log.get('details', []))
                ])

            output = output.getvalue()
            mimetype = 'text/csv'
            filename = f'error_logs_{timestamp}.csv'

        # تسجيل النشاط
        if hasattr(current_user, 'log_activity'):
            current_user.log_activity(
                action='export_error_logs',
                module='settings',
                description=f'تم تصدير سجلات الأخطاء بتنسيق {format}',
                ip_address=request.remote_addr
            )

        # إرجاع الملف للتنزيل
        return Response(
            output,
            mimetype=mimetype,
            headers={
                'Content-Disposition': f'attachment; filename={filename}',
                'Content-Type': f'{mimetype}; charset=utf-8'
            }
        )
    except Exception as e:
        logger.error(f"خطأ أثناء تصدير سجلات الأخطاء: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'حدث خطأ أثناء تصدير سجلات الأخطاء: {str(e)}'
        }), 500


def register_errors_api(app):
    """تسجيل واجهة برمجة التطبيقات للأخطاء"""
    app.register_blueprint(errors_api)
