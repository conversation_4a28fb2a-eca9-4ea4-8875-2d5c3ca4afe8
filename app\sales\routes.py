"""
Nobara POS System - Sales Routes
نظام نوبارا لنقاط البيع - مسارات المبيعات
"""

from flask import render_template, request, jsonify, redirect, url_for, flash
from flask_login import login_required, current_user
from app.sales import bp
from app.models import Sale, SaleItem, Customer, Product, db
from datetime import datetime, timedelta
import logging

logger = logging.getLogger(__name__)

@bp.route('/')
@login_required
def index():
    """قائمة المبيعات"""
    try:
        page = request.args.get('page', 1, type=int)
        per_page = 20
        
        # فلاتر البحث
        search = request.args.get('search', '').strip()
        status = request.args.get('status', 'all')
        date_from = request.args.get('date_from')
        date_to = request.args.get('date_to')
        
        # بناء الاستعلام
        query = Sale.query
        
        if search:
            query = query.filter(
                db.or_(
                    Sale.sale_number.contains(search),
                    Sale.customer.has(Customer.name.contains(search))
                )
            )
        
        if status != 'all':
            query = query.filter_by(status=status)
        
        if date_from:
            date_from = datetime.strptime(date_from, '%Y-%m-%d')
            query = query.filter(Sale.sale_date >= date_from)
        
        if date_to:
            date_to = datetime.strptime(date_to, '%Y-%m-%d')
            query = query.filter(Sale.sale_date <= date_to)
        
        # ترتيب النتائج
        query = query.order_by(Sale.created_at.desc())
        
        # تطبيق التصفح
        sales = query.paginate(page=page, per_page=per_page, error_out=False)
        
        # إحصائيات
        stats = {
            'total_sales': Sale.query.filter_by(status='completed').count(),
            'today_sales': Sale.query.filter(
                Sale.sale_date >= datetime.utcnow().date(),
                Sale.status == 'completed'
            ).count(),
            'pending_sales': Sale.query.filter_by(status='pending').count()
        }
        
        return render_template('sales/index.html',
                             sales=sales,
                             stats=stats,
                             search=search,
                             status=status,
                             date_from=date_from,
                             date_to=date_to)
        
    except Exception as e:
        logger.error(f'Error loading sales: {e}')
        flash('حدث خطأ أثناء تحميل المبيعات', 'error')
        return redirect(url_for('main.dashboard'))

@bp.route('/<int:id>')
@login_required
def view(id):
    """عرض تفاصيل المبيعة"""
    try:
        sale = Sale.query.get_or_404(id)
        return render_template('sales/view.html', sale=sale)
        
    except Exception as e:
        logger.error(f'Error loading sale: {e}')
        flash('حدث خطأ أثناء تحميل المبيعة', 'error')
        return redirect(url_for('sales.index'))

@bp.route('/returns')
@login_required
def returns():
    """مرتجع المبيعات"""
    return render_template('sales/returns.html')

@bp.route('/deferred')
@login_required
def deferred():
    """المبيعات الآجلة"""
    return render_template('sales/deferred.html')
