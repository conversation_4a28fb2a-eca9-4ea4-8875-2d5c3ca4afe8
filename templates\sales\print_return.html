<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إيصال مرتجع - {{ return_order.reference_number }}</title>
    <style>
        @page {
            size: 80mm 297mm;
            margin: 0;
        }
        body {
            font-family: 'Arial', sans-serif;
            margin: 0;
            padding: 0;
            background-color: white;
            font-size: 12px;
            width: 80mm;
            margin: 0 auto;
        }
        .receipt {
            padding: 10px;
            box-sizing: border-box;
        }
        .header {
            text-align: center;
            margin-bottom: 10px;
            border-bottom: 1px dashed #000;
            padding-bottom: 10px;
        }
        .store-name {
            font-size: 16px;
            font-weight: bold;
            margin: 5px 0;
        }
        .store-info {
            font-size: 10px;
            margin: 2px 0;
        }
        .title {
            font-size: 14px;
            font-weight: bold;
            text-align: center;
            margin: 10px 0;
            border: 1px solid #000;
            padding: 5px;
            background-color: #f0f0f0;
        }
        .info-row {
            display: flex;
            justify-content: space-between;
            margin: 5px 0;
            font-size: 11px;
        }
        .info-label {
            font-weight: bold;
        }
        .items {
            margin: 10px 0;
            border-top: 1px dashed #000;
            border-bottom: 1px dashed #000;
            padding: 10px 0;
        }
        .item {
            margin-bottom: 8px;
        }
        .item-header {
            display: flex;
            justify-content: space-between;
            font-weight: bold;
            font-size: 11px;
            border-bottom: 1px solid #ccc;
            padding-bottom: 3px;
            margin-bottom: 5px;
        }
        .item-row {
            display: flex;
            justify-content: space-between;
            font-size: 10px;
            margin: 3px 0;
        }
        .item-name {
            flex: 2;
        }
        .item-price, .item-qty, .item-total {
            flex: 1;
            text-align: left;
        }
        .totals {
            margin: 10px 0;
        }
        .total-row {
            display: flex;
            justify-content: space-between;
            margin: 5px 0;
            font-size: 11px;
        }
        .grand-total {
            font-weight: bold;
            font-size: 14px;
            border-top: 1px solid #000;
            padding-top: 5px;
            margin-top: 5px;
        }
        .footer {
            text-align: center;
            margin-top: 20px;
            font-size: 10px;
            border-top: 1px dashed #000;
            padding-top: 10px;
        }
        .barcode {
            text-align: center;
            margin: 15px 0;
        }
        .barcode img {
            max-width: 100%;
            height: auto;
        }
        .powered-by {
            text-align: center;
            font-size: 10px;
            margin-top: 10px;
            font-style: italic;
        }
        @media print {
            body {
                width: 100%;
                margin: 0;
                padding: 0;
            }
            .no-print {
                display: none;
            }
        }
    </style>
</head>
<body>
    <div class="receipt">
        <div class="header">
            <div class="store-name">{{ store_info.name }}</div>
            <div class="store-info">{{ store_info.address }}</div>
            <div class="store-info">هاتف: {{ store_info.phone }}</div>
            {% if store_info.tax_number %}
            <div class="store-info">الرقم الضريبي: {{ store_info.tax_number }}</div>
            {% endif %}
        </div>

        <div class="title">إيصال مرتجع مبيعات</div>

        <div class="info-section">
            <div class="info-row">
                <span class="info-label">رقم المرتجع:</span>
                <span>{{ return_order.reference_number }}</span>
            </div>
            <div class="info-row">
                <span class="info-label">رقم الفاتورة الأصلية:</span>
                <span>{{ order.invoice_number }}</span>
            </div>
            <div class="info-row">
                <span class="info-label">التاريخ:</span>
                <span>{{ return_order.created_at.strftime('%Y-%m-%d %H:%M') }}</span>
            </div>
            <div class="info-row">
                <span class="info-label">العميل:</span>
                <span>{{ order.customer.name if order.customer else 'عميل نقدي' }}</span>
            </div>
            <div class="info-row">
                <span class="info-label">طريقة الاسترداد:</span>
                <span>
                    {% if return_order.payment_method == 'cash' %}
                        نقدي
                    {% elif return_order.payment_method == 'card' %}
                        بطاقة
                    {% elif return_order.payment_method == 'store_credit' %}
                        رصيد متجر
                    {% else %}
                        {{ return_order.payment_method }}
                    {% endif %}
                </span>
            </div>
            <div class="info-row">
                <span class="info-label">الموظف:</span>
                <span>{{ return_order.user.username }}</span>
            </div>
        </div>

        <div class="items">
            <div class="item-header">
                <div class="item-name">المنتج</div>
                <div class="item-price">السعر</div>
                <div class="item-qty">الكمية</div>
                <div class="item-total">الإجمالي</div>
            </div>
            
            {% for item in return_order.items %}
            <div class="item">
                <div class="item-row">
                    <div class="item-name">{{ item.product.name }}</div>
                    <div class="item-price">{{ "%.2f"|format(item.price) }}</div>
                    <div class="item-qty">{{ item.quantity }}</div>
                    <div class="item-total">{{ "%.2f"|format(item.total) }}</div>
                </div>
                {% if item.reason %}
                <div style="font-size: 9px; color: #666; margin-right: 5px;">السبب: {{ item.reason }}</div>
                {% endif %}
            </div>
            {% endfor %}
        </div>

        <div class="totals">
            <div class="total-row grand-total">
                <span>إجمالي المبلغ المسترد:</span>
                <span>{{ "%.2f"|format(return_order.total_amount) }} ج.م</span>
            </div>
        </div>

        {% if return_order.notes %}
        <div style="margin: 10px 0; font-size: 10px; border-top: 1px dashed #ccc; padding-top: 5px;">
            <div style="font-weight: bold;">ملاحظات:</div>
            <div>{{ return_order.notes }}</div>
        </div>
        {% endif %}

        <div class="barcode">
            <img src="data:image/png;base64,{{ barcode_data }}" alt="Barcode">
            <div style="font-size: 10px; margin-top: 5px;">{{ return_order.reference_number }}</div>
        </div>

        <div class="footer">
            <div>شكراً لتعاملكم معنا</div>
            <div style="margin-top: 5px;">هذا الإيصال دليل على استرداد المبلغ</div>
        </div>

        <div class="powered-by">
            Powered By ENG/ Fouad Saber Tel: 01020073527
        </div>
    </div>

    <div class="no-print" style="text-align: center; margin: 20px 0;">
        <button onclick="window.print()" style="padding: 10px 20px; background-color: #3B82F6; color: white; border: none; border-radius: 5px; cursor: pointer;">
            طباعة الإيصال
        </button>
    </div>

    <script>
        // طباعة تلقائية عند تحميل الصفحة
        window.onload = function() {
            setTimeout(function() {
                window.print();
            }, 1000);
        };
    </script>
</body>
</html>
