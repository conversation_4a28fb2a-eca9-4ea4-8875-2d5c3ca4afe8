<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Nobara - إعدادات الضرائب</title>
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <!-- Remixicon -->
    <link href="https://cdn.jsdelivr.net/npm/remixicon@2.5.0/fonts/remixicon.css" rel="stylesheet">
    <!-- Alpine.js -->
    <script defer src="https://cdn.jsdelivr.net/npm/alpinejs@3.x.x/dist/cdn.min.js"></script>
    <script>
        tailwind.config = {
            darkMode: 'class',
            theme: {
                extend: {
                    colors: {
                        primary: {
                            50: '#eff6ff',
                            100: '#dbeafe',
                            200: '#bfdbfe',
                            300: '#93c5fd',
                            400: '#60a5fa',
                            500: '#3b82f6',
                            600: '#2563eb',
                            700: '#1d4ed8',
                            800: '#1e40af',
                            900: '#1e3a8a',
                            950: '#172554',
                        },
                        dark: {
                            100: '#1E293B',
                            200: '#0F172A',
                            300: '#0B1120',
                            400: '#060A15'
                        }
                    }
                }
            }
        }
    </script>
    <style>
        body {
            font-family: 'Cairo', 'Tajawal', sans-serif;
            transition: background-color 0.3s ease, color 0.3s ease;
        }

        .bg-pattern {
            background-color: #f9fafb;
            background-image: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%233B82F6' fill-opacity='0.05'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
        }

        .dark .bg-pattern {
            background-color: #0F172A;
            background-image: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%233B82F6' fill-opacity='0.1'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
        }

        .powered-by {
            position: fixed;
            bottom: 1rem;
            left: 1rem;
            background-color: #ffffff;
            border-radius: 1rem;
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
            padding: 0.75rem 1rem;
            display: flex;
            align-items: center;
            z-index: 40;
            transition: all 0.3s ease;
            border: 1px solid #e5e7eb;
        }

        .dark .powered-by {
            background-color: #1E293B;
            border-color: #2D3748;
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.3), 0 4px 6px -2px rgba(0, 0, 0, 0.2);
        }

        .powered-by:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
        }

        .dark .powered-by:hover {
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.3), 0 10px 10px -5px rgba(0, 0, 0, 0.2);
        }
    </style>
</head>
<body class="bg-pattern dark:bg-dark-200 dark:text-gray-100" x-data="{ darkMode: localStorage.getItem('darkMode') === 'enabled' }">
    <div class="flex h-screen overflow-hidden">
        <!-- Sidebar -->
        {% include 'partials/sidebar.html' %}

        <!-- Main Content -->
        <div class="flex-1 flex flex-col overflow-hidden">
            <!-- Top Navigation -->
            {% include 'partials/topnav.html' %}

            <!-- Content Area -->
            <main class="flex-1 overflow-y-auto p-6">
                <div class="mb-6 flex justify-between items-center">
                    <div>
                        <h1 class="text-2xl font-bold text-gray-800 dark:text-white">إعدادات الضرائب</h1>
                        <p class="text-gray-600 dark:text-gray-400">تكوين نسب الضرائب وإعدادات الضريبة على القيمة المضافة</p>
                    </div>
                    <div class="flex items-center gap-2">
                        <a href="{{ url_for('settings.index') }}" class="flex items-center gap-1 bg-gray-100 dark:bg-dark-100 hover:bg-gray-200 dark:hover:bg-dark-300 text-gray-700 dark:text-gray-300 py-2 px-4 rounded-lg transition-colors">
                            <i class="ri-arrow-right-line"></i>
                            <span>العودة للإعدادات</span>
                        </a>
                        <button @click="darkMode = !darkMode; localStorage.setItem('darkMode', darkMode ? 'enabled' : 'disabled'); document.documentElement.classList.toggle('dark')" class="p-2 rounded-full bg-gray-100 dark:bg-dark-100 hover:bg-gray-200 dark:hover:bg-dark-300 transition-colors">
                            <i class="ri-moon-line dark:hidden"></i>
                            <i class="ri-sun-line hidden dark:block"></i>
                        </button>
                    </div>
                </div>

                <div class="bg-white dark:bg-dark-100 rounded-xl shadow-sm overflow-hidden mb-6">
                    <div class="p-6">
                        <form id="taxSettingsForm" action="{{ url_for('settings.update_tax_settings') }}" method="POST">
                            <div class="mt-2">
                                <h3 class="text-lg font-medium text-gray-800 dark:text-white mb-4 flex items-center">
                                    <i class="ri-list-settings-line ml-2 text-primary-500"></i>
                                    أنواع الضرائب
                                </h3>

                                <div class="mb-4">
                                    <p class="text-sm text-gray-600 dark:text-gray-400 mb-2">يمكنك إضافة أنواع متعددة من الضرائب وتطبيقها على المنتجات المختلفة</p>

                                    <div class="overflow-x-auto">
                                        <table class="min-w-full bg-white dark:bg-dark-200 rounded-lg overflow-hidden border border-gray-200 dark:border-gray-700 mb-4">
                                            <thead class="bg-gray-50 dark:bg-dark-300">
                                                <tr>
                                                    <th class="py-3 px-4 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">الاسم</th>
                                                    <th class="py-3 px-4 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">النسبة</th>
                                                    <th class="py-3 px-4 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">الوصف</th>
                                                    <th class="py-3 px-4 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">الحالة</th>
                                                    <th class="py-3 px-4 text-center text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">الإجراءات</th>
                                                </tr>
                                            </thead>
                                            <tbody id="tax-types-table" class="divide-y divide-gray-200 dark:divide-gray-700">
                                                {% if settings.tax.types %}
                                                    {% for tax in settings.tax.types %}
                                                    <tr class="hover:bg-gray-50 dark:hover:bg-dark-300 transition-colors">
                                                        <td class="py-3 px-4 text-sm text-gray-800 dark:text-gray-200">{{ tax.name }}</td>
                                                        <td class="py-3 px-4 text-sm text-gray-800 dark:text-gray-200">{{ tax.rate }}%</td>
                                                        <td class="py-3 px-4 text-sm text-gray-600 dark:text-gray-400">{{ tax.description }}</td>
                                                        <td class="py-3 px-4">
                                                            {% if tax.is_active %}
                                                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400">
                                                                <span class="w-1.5 h-1.5 ml-1 rounded-full bg-green-500"></span>
                                                                مفعل
                                                            </span>
                                                            {% else %}
                                                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300">
                                                                <span class="w-1.5 h-1.5 ml-1 rounded-full bg-gray-500"></span>
                                                                غير مفعل
                                                            </span>
                                                            {% endif %}
                                                        </td>
                                                        <td class="py-3 px-4 text-center">
                                                            <button type="button" class="edit-tax-btn text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 mx-1" data-id="{{ loop.index0 }}">
                                                                <i class="ri-edit-line"></i>
                                                            </button>
                                                            <button type="button" class="delete-tax-btn text-red-600 hover:text-red-800 dark:text-red-400 dark:hover:text-red-300 mx-1" data-id="{{ loop.index0 }}">
                                                                <i class="ri-delete-bin-line"></i>
                                                            </button>
                                                        </td>
                                                    </tr>
                                                    {% endfor %}
                                                {% else %}
                                                    <tr>
                                                        <td colspan="5" class="py-4 px-4 text-center text-gray-500 dark:text-gray-400">لا توجد أنواع ضرائب مضافة</td>
                                                    </tr>
                                                {% endif %}
                                            </tbody>
                                        </table>
                                    </div>

                                    <button type="button" id="add-tax-btn" class="bg-green-500 hover:bg-green-600 text-white py-2 px-4 rounded-lg inline-flex items-center transition-colors text-sm">
                                        <i class="ri-add-line ml-1"></i>
                                        <span>إضافة نوع ضريبة جديد</span>
                                    </button>
                                </div>
                            </div>

                            <div class="mt-6 flex justify-end">
                                <button type="submit" class="bg-primary-500 hover:bg-primary-600 text-white py-2 px-6 rounded-lg inline-flex items-center transition-colors">
                                    <i class="ri-save-line ml-1"></i>
                                    <span>حفظ الإعدادات</span>
                                </button>
                            </div>

                            <input type="hidden" name="section" value="tax">
                            <input type="hidden" id="tax_types_json" name="tax_types_json" value="{{ settings.tax.types|tojson if settings.tax.types else '[]' }}">
                        </form>
                    </div>
                </div>


            </main>
        </div>
    </div>

    <!-- Powered By -->
    <div class="powered-by">
        <div class="w-8 h-8 rounded-full bg-primary-50 dark:bg-primary-900/50 flex items-center justify-center ml-2 text-primary-600 dark:text-primary-400">
            <i class="ri-code-s-slash-line"></i>
        </div>
        <div>
            <div class="text-xs text-gray-600 dark:text-gray-400">
                Powered By <span class="font-bold text-primary-600 dark:text-primary-400">ENG/ Fouad Saber</span>
            </div>
            <div class="text-xs text-gray-500 dark:text-gray-500">Tel: 01020073527</div>
        </div>
    </div>

    <!-- نافذة إضافة/تعديل نوع ضريبة -->
    <div id="tax-modal" class="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center hidden">
        <div class="bg-white dark:bg-dark-100 rounded-xl shadow-xl w-full max-w-md transform scale-95 opacity-0 transition-all duration-300 p-6">
            <div class="flex justify-between items-center mb-4">
                <h3 id="tax-modal-title" class="text-lg font-bold text-gray-800 dark:text-white">إضافة نوع ضريبة جديد</h3>
                <button id="close-tax-modal" class="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200">
                    <i class="ri-close-line text-xl"></i>
                </button>
            </div>

            <form id="tax-form">
                <div class="mb-4">
                    <label for="tax-name" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">اسم الضريبة</label>
                    <input type="text" id="tax-name" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 dark:bg-dark-200 dark:text-gray-100" required>
                </div>

                <div class="mb-4">
                    <label for="tax-rate" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">نسبة الضريبة (%)</label>
                    <div class="relative">
                        <input type="number" id="tax-rate" min="0" max="100" step="0.01" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 dark:bg-dark-200 dark:text-gray-100" required>
                        <div class="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                            <span class="text-gray-500 dark:text-gray-400">%</span>
                        </div>
                    </div>
                </div>

                <div class="mb-4">
                    <label for="tax-description" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">الوصف</label>
                    <textarea id="tax-description" rows="2" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 dark:bg-dark-200 dark:text-gray-100"></textarea>
                </div>

                <div class="mb-4">
                    <label class="flex items-center">
                        <input type="checkbox" id="tax-is-active" class="w-4 h-4 text-primary-600 border-gray-300 rounded focus:ring-primary-500 dark:bg-dark-200 dark:border-gray-600" checked>
                        <span class="mr-2 text-sm text-gray-700 dark:text-gray-300">مفعل</span>
                    </label>
                </div>

                <div class="flex justify-end mt-6">
                    <button type="button" id="cancel-tax-btn" class="px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-dark-300 transition-colors mr-2">إلغاء</button>
                    <button type="submit" id="save-tax-btn" class="px-4 py-2 bg-primary-500 text-white rounded-lg hover:bg-primary-600 transition-colors">حفظ</button>
                </div>

                <input type="hidden" id="tax-edit-index" value="-1">
            </form>
        </div>
    </div>

    <script>
        // تفعيل وضع الظلام إذا كان مخزنًا
        if (localStorage.getItem('darkMode') === 'enabled') {
            document.documentElement.classList.add('dark');
        }

        document.addEventListener('DOMContentLoaded', function() {
            // متغيرات عامة
            let taxTypes = [];
            try {
                taxTypes = JSON.parse(document.getElementById('tax_types_json').value || '[]');
            } catch (e) {
                taxTypes = [];
                console.error('Error parsing tax types:', e);
            }

            // إظهار/إخفاء نافذة الضريبة
            function showTaxModal() {
                const modal = document.getElementById('tax-modal');
                modal.classList.remove('hidden');
                setTimeout(() => {
                    modal.querySelector('div').classList.remove('scale-95', 'opacity-0');
                    modal.querySelector('div').classList.add('scale-100', 'opacity-100');
                }, 10);
            }

            function hideTaxModal() {
                const modal = document.getElementById('tax-modal');
                modal.querySelector('div').classList.remove('scale-100', 'opacity-100');
                modal.querySelector('div').classList.add('scale-95', 'opacity-0');
                setTimeout(() => {
                    modal.classList.add('hidden');
                }, 300);
            }

            // إضافة نوع ضريبة جديد
            document.getElementById('add-tax-btn').addEventListener('click', function() {
                document.getElementById('tax-modal-title').textContent = 'إضافة نوع ضريبة جديد';
                document.getElementById('tax-form').reset();
                document.getElementById('tax-edit-index').value = '-1';
                document.getElementById('tax-is-active').checked = true;
                showTaxModal();
            });

            // إغلاق نافذة الضريبة
            document.getElementById('close-tax-modal').addEventListener('click', hideTaxModal);
            document.getElementById('cancel-tax-btn').addEventListener('click', hideTaxModal);

            // حفظ نوع الضريبة
            document.getElementById('tax-form').addEventListener('submit', function(e) {
                e.preventDefault();

                const name = document.getElementById('tax-name').value;
                const rate = parseFloat(document.getElementById('tax-rate').value);
                const description = document.getElementById('tax-description').value;
                const isActive = document.getElementById('tax-is-active').checked;
                const editIndex = parseInt(document.getElementById('tax-edit-index').value);

                const taxType = {
                    name: name,
                    rate: rate,
                    description: description,
                    is_active: isActive
                };

                if (editIndex >= 0) {
                    // تعديل نوع ضريبة موجود
                    taxTypes[editIndex] = taxType;
                } else {
                    // إضافة نوع ضريبة جديد
                    taxTypes.push(taxType);
                }

                // تحديث قيمة الحقل المخفي
                document.getElementById('tax_types_json').value = JSON.stringify(taxTypes);

                // تحديث الجدول
                updateTaxTypesTable();

                // إغلاق النافذة
                hideTaxModal();
            });

            // تحديث جدول أنواع الضرائب
            function updateTaxTypesTable() {
                const tableBody = document.getElementById('tax-types-table');

                if (taxTypes.length === 0) {
                    tableBody.innerHTML = `
                        <tr>
                            <td colspan="5" class="py-4 px-4 text-center text-gray-500 dark:text-gray-400">لا توجد أنواع ضرائب مضافة</td>
                        </tr>
                    `;
                    return;
                }

                let html = '';
                taxTypes.forEach((tax, index) => {
                    html += `
                        <tr class="hover:bg-gray-50 dark:hover:bg-dark-300 transition-colors">
                            <td class="py-3 px-4 text-sm text-gray-800 dark:text-gray-200">${tax.name}</td>
                            <td class="py-3 px-4 text-sm text-gray-800 dark:text-gray-200">${tax.rate}%</td>
                            <td class="py-3 px-4 text-sm text-gray-600 dark:text-gray-400">${tax.description}</td>
                            <td class="py-3 px-4">
                                ${tax.is_active ?
                                    `<span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400">
                                        <span class="w-1.5 h-1.5 ml-1 rounded-full bg-green-500"></span>
                                        مفعل
                                    </span>` :
                                    `<span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300">
                                        <span class="w-1.5 h-1.5 ml-1 rounded-full bg-gray-500"></span>
                                        غير مفعل
                                    </span>`
                                }
                            </td>
                            <td class="py-3 px-4 text-center">
                                <button type="button" class="edit-tax-btn text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 mx-1" data-id="${index}">
                                    <i class="ri-edit-line"></i>
                                </button>
                                <button type="button" class="delete-tax-btn text-red-600 hover:text-red-800 dark:text-red-400 dark:hover:text-red-300 mx-1" data-id="${index}">
                                    <i class="ri-delete-bin-line"></i>
                                </button>
                            </td>
                        </tr>
                    `;
                });

                tableBody.innerHTML = html;

                // إضافة مستمعي الأحداث للأزرار
                document.querySelectorAll('.edit-tax-btn').forEach(btn => {
                    btn.addEventListener('click', function() {
                        const index = parseInt(this.dataset.id);
                        editTaxType(index);
                    });
                });

                document.querySelectorAll('.delete-tax-btn').forEach(btn => {
                    btn.addEventListener('click', function() {
                        const index = parseInt(this.dataset.id);
                        deleteTaxType(index);
                    });
                });
            }

            // تعديل نوع ضريبة
            function editTaxType(index) {
                const tax = taxTypes[index];

                document.getElementById('tax-modal-title').textContent = 'تعديل نوع الضريبة';
                document.getElementById('tax-name').value = tax.name;
                document.getElementById('tax-rate').value = tax.rate;
                document.getElementById('tax-description').value = tax.description;
                document.getElementById('tax-is-active').checked = tax.is_active;
                document.getElementById('tax-edit-index').value = index;

                showTaxModal();
            }

            // حذف نوع ضريبة
            function deleteTaxType(index) {
                if (confirm('هل أنت متأكد من حذف نوع الضريبة هذا؟')) {
                    taxTypes.splice(index, 1);
                    document.getElementById('tax_types_json').value = JSON.stringify(taxTypes);
                    updateTaxTypesTable();
                }
            }

            // تحديث الجدول عند تحميل الصفحة
            updateTaxTypesTable();

            // إرسال النموذج
            document.getElementById('taxSettingsForm').addEventListener('submit', function(e) {
                e.preventDefault();

                fetch(this.action, {
                    method: 'POST',
                    body: new FormData(this),
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        alert("تم حفظ إعدادات الضريبة بنجاح");
                    } else {
                        alert("حدث خطأ أثناء حفظ الإعدادات: " + data.error);
                    }
                })
                .catch(error => {
                    alert("حدث خطأ أثناء الاتصال بالخادم");
                    console.error(error);
                });
            });
        });
    </script>
</body>
</html>
