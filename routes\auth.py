from flask import Blueprint, render_template, redirect, url_for, request, flash, session
from flask_login import login_user, logout_user, login_required, current_user
from werkzeug.security import generate_password_hash
from app import db
from models import User
from datetime import datetime
from routes.settings import load_settings

auth_blueprint = Blueprint('auth', __name__)

@auth_blueprint.route('/')
def index():
    if current_user.is_authenticated:
        return redirect(url_for('dashboard.home'))
    return redirect(url_for('auth.login'))

@auth_blueprint.route('/login', methods=['GET', 'POST'])
def login():
    if current_user.is_authenticated:
        return redirect(url_for('dashboard.home'))

    if request.method == 'POST':
        username = request.form.get('username')
        password = request.form.get('password')
        remember = True if request.form.get('remember') else False

        user = User.query.filter_by(username=username).first()

        if not user or not user.check_password(password):
            flash('خطأ في اسم المستخدم أو كلمة المرور', 'danger')
            return redirect(url_for('auth.login'))

        login_user(user, remember=remember)
        next_page = request.args.get('next')
        if next_page:
            return redirect(next_page)
        return redirect(url_for('dashboard.home'))

    # تحميل إعدادات المتجر
    settings = load_settings()

    # Get current year for the footer
    current_year = datetime.now().year
    return render_template('core/login.html', current_year=current_year, settings=settings)

@auth_blueprint.route('/logout')
@login_required
def logout():
    logout_user()
    return redirect(url_for('auth.login'))

@auth_blueprint.route('/initialize-admin', methods=['GET'])
def initialize_admin():
    """Create initial admin user if no users exist"""
    if User.query.count() == 0:
        admin = User(
            username='admin',
            email='<EMAIL>',
            full_name='مدير النظام',
            role='admin'
        )
        admin.set_password('admin')
        db.session.add(admin)
        db.session.commit()
        flash('تم إنشاء حساب المدير بنجاح. اسم المستخدم: admin، كلمة المرور: admin', 'success')
    else:
        flash('يوجد مستخدمين بالفعل في النظام', 'info')

    return redirect(url_for('auth.login'))
