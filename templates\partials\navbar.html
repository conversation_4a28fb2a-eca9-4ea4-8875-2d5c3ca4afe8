<nav class="bg-white dark:bg-gray-800 shadow-md">
    <div class="container mx-auto px-4">
        <div class="flex justify-between items-center h-16">
            <div class="flex items-center">
                <a href="{{ url_for('dashboard.home') }}" class="flex items-center">
                    <img src="{{ url_for('static', filename='img/nobara-logo.svg') }}" alt="Nobara" class="h-8 w-auto mr-2">
                    <span class="text-xl font-bold text-primary dark:text-blue-400">Nobara</span>
                </a>
                <div class="hidden md:block ml-10">
                    <div class="flex items-center space-x-4 space-x-reverse">
                        <a href="{{ url_for('dashboard.home') }}" class="px-3 py-2 rounded-md text-sm font-medium text-gray-700 dark:text-gray-300 hover:text-primary dark:hover:text-blue-400 hover:bg-gray-100 dark:hover:bg-gray-700">
                            <i class="ri-dashboard-line ml-1"></i>
                            لوحة التحكم
                        </a>
                        <a href="{{ url_for('pos.index') }}" class="px-3 py-2 rounded-md text-sm font-medium text-gray-700 dark:text-gray-300 hover:text-primary dark:hover:text-blue-400 hover:bg-gray-100 dark:hover:bg-gray-700">
                            <i class="ri-shopping-cart-line ml-1"></i>
                            نقطة البيع
                        </a>
                        <a href="{{ url_for('products.index') }}" class="px-3 py-2 rounded-md text-sm font-medium text-gray-700 dark:text-gray-300 hover:text-primary dark:hover:text-blue-400 hover:bg-gray-100 dark:hover:bg-gray-700">
                            <i class="ri-store-2-line ml-1"></i>
                            المنتجات
                        </a>
                        <a href="{{ url_for('sales.index') }}" class="px-3 py-2 rounded-md text-sm font-medium text-gray-700 dark:text-gray-300 hover:text-primary dark:hover:text-blue-400 hover:bg-gray-100 dark:hover:bg-gray-700">
                            <i class="ri-exchange-dollar-line ml-1"></i>
                            المبيعات
                        </a>
                        <a href="{{ url_for('customers.index') }}" class="px-3 py-2 rounded-md text-sm font-medium text-gray-700 dark:text-gray-300 hover:text-primary dark:hover:text-blue-400 hover:bg-gray-100 dark:hover:bg-gray-700">
                            <i class="ri-user-line ml-1"></i>
                            العملاء
                        </a>
                    </div>
                </div>
            </div>
            <div class="flex items-center">
                <button id="darkModeToggle" class="p-2 rounded-full text-gray-600 dark:text-gray-300 hover:text-gray-800 dark:hover:text-white focus:outline-none">
                    <i class="ri-moon-line dark:hidden"></i>
                    <i class="ri-sun-line hidden dark:block"></i>
                </button>
                <div class="relative ml-3">
                    <div>
                        <button type="button" class="flex items-center max-w-xs rounded-full bg-white dark:bg-gray-800 text-sm focus:outline-none" id="user-menu-button" aria-expanded="false" aria-haspopup="true">
                            <span class="sr-only">Open user menu</span>
                            <div class="w-8 h-8 rounded-full bg-primary text-white flex items-center justify-center">
                                {{ current_user.username[0].upper() }}
                            </div>
                            <span class="mr-2 text-gray-700 dark:text-gray-300">{{ current_user.username }}</span>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</nav>
