<!DOCTYPE html>
<html lang="{{ g.language or 'ar' }}" dir="{{ 'rtl' if (g.language or 'ar') == 'ar' else 'ltr' }}" data-theme="{{ g.theme or 'light' }}">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    
    <!-- Title -->
    <title>{% block title %}نوبارا - نظام نقاط البيع الاحترافي{% endblock %}</title>
    
    <!-- Meta Tags -->
    <meta name="description" content="{% block description %}نظام نوبارا لنقاط البيع - حل شامل ومتطور لإدارة المبيعات والمخزون{% endblock %}">
    <meta name="keywords" content="نوبارا, نقطة بيع, مبيعات, مخزون, محاسبة, POS">
    <meta name="author" content="ENG/ Fouad Saber">
    <meta name="robots" content="noindex, nofollow">
    <meta name="theme-color" content="#2563eb">
    
    <!-- Favicon -->
    <link rel="icon" type="image/svg+xml" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>🏪</text></svg>">
    
    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@200;300;400;500;600;700;800;900&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@100;200;300;400;500;600;700;800;900&display=swap" rel="stylesheet">
    
    <!-- Icons -->
    <link href="https://cdn.jsdelivr.net/npm/remixicon@3.5.0/fonts/remixicon.css" rel="stylesheet">
    
    <!-- Core CSS -->
    <link href="{{ url_for('static', filename='css/nobara-design-system.css') }}?v=2.0.0" rel="stylesheet">
    
    <!-- Additional CSS -->
    {% block extra_css %}{% endblock %}
</head>

<body class="{{ 'rtl' if (g.language or 'ar') == 'ar' else 'ltr' }} {{ 'dark-mode' if (g.theme or 'light') == 'dark' else 'light-mode' }}">
    <!-- Loading Screen -->
    <div id="loading-screen" class="fixed inset-0 bg-gradient-primary z-50 flex items-center justify-center">
        <div class="text-center text-white">
            <div class="mb-4">
                <div class="animate-spin rounded-full h-16 w-16 border-4 border-white border-t-transparent mx-auto"></div>
            </div>
            <h2 class="text-2xl font-bold mb-2">نوبارا</h2>
            <p class="text-sm opacity-90">جاري تحميل النظام...</p>
        </div>
    </div>

    <!-- App Container -->
    <div id="app" class="min-h-screen bg-primary transition-all duration-300">
        <!-- Header -->
        <header class="header bg-card shadow-md border-b border-primary sticky top-0 z-40">
            <div class="container-fluid">
                <div class="flex items-center justify-between h-16">
                    <!-- Logo & Menu Toggle -->
                    <div class="flex items-center gap-4">
                        <button id="sidebar-toggle" class="btn btn-icon btn-secondary lg:hidden">
                            <i class="ri-menu-line"></i>
                        </button>
                        
                        <div class="flex items-center gap-3">
                            <div class="w-10 h-10 bg-gradient-primary rounded-lg flex items-center justify-center text-white font-bold text-lg">
                                ن
                            </div>
                            <div class="hidden sm:block">
                                <h1 class="text-lg font-bold text-primary">نوبارا</h1>
                                <p class="text-xs text-secondary">نظام نقاط البيع</p>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Header Actions -->
                    <div class="flex items-center gap-2 header-actions">
                        <!-- Search -->
                        <div class="hidden md:block relative">
                            <input type="text" placeholder="البحث السريع..." 
                                   class="form-control w-64 pl-10 pr-4 py-2 text-sm">
                            <i class="ri-search-line absolute left-3 top-1/2 transform -translate-y-1/2 text-muted"></i>
                        </div>
                        
                        <!-- Notifications -->
                        <button id="notifications-toggle" class="btn btn-icon btn-secondary relative">
                            <i class="ri-notification-line"></i>
                            <span class="absolute -top-1 -right-1 w-5 h-5 bg-red text-white text-xs rounded-full flex items-center justify-center">3</span>
                        </button>
                        
                        <!-- User Menu -->
                        <div class="relative">
                            <button id="user-menu-toggle" class="flex items-center gap-2 p-2 rounded-lg hover:bg-hover transition-colors">
                                <div class="w-8 h-8 bg-gradient-secondary rounded-full flex items-center justify-center text-white text-sm font-medium">
                                    {% if current_user.is_authenticated %}
                                        {{ current_user.first_name[0] }}{{ current_user.last_name[0] }}
                                    {% else %}
                                        ؟
                                    {% endif %}
                                </div>
                                <div class="hidden sm:block text-right">
                                    <p class="text-sm font-medium text-primary">
                                        {% if current_user.is_authenticated %}
                                            {{ current_user.full_name }}
                                        {% else %}
                                            ضيف
                                        {% endif %}
                                    </p>
                                    <p class="text-xs text-secondary">
                                        {% if current_user.is_authenticated and current_user.role %}
                                            {{ current_user.role.name_ar }}
                                        {% else %}
                                            غير مسجل
                                        {% endif %}
                                    </p>
                                </div>
                                <i class="ri-arrow-down-s-line text-muted"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </header>

        <!-- Sidebar -->
        <aside id="sidebar" class="sidebar fixed right-0 top-16 h-full w-64 bg-card border-l border-primary transform translate-x-full lg:translate-x-0 transition-transform duration-300 z-30">
            <div class="p-4">
                <!-- Navigation Menu -->
                <nav class="space-y-2">
                    <!-- Dashboard -->
                    <a href="/" class="nav-item flex items-center gap-3 p-3 rounded-lg hover:bg-hover transition-colors">
                        <i class="ri-dashboard-line text-blue"></i>
                        <span class="font-medium">لوحة التحكم</span>
                    </a>
                    
                    <!-- POS -->
                    <a href="/pos" class="nav-item flex items-center gap-3 p-3 rounded-lg hover:bg-hover transition-colors">
                        <i class="ri-shopping-cart-line text-red"></i>
                        <span class="font-medium">نقطة البيع</span>
                    </a>
                    
                    <!-- Sales -->
                    <div class="nav-group">
                        <button class="nav-group-toggle w-full flex items-center justify-between p-3 rounded-lg hover:bg-hover transition-colors">
                            <div class="flex items-center gap-3">
                                <i class="ri-line-chart-line text-blue"></i>
                                <span class="font-medium">المبيعات</span>
                            </div>
                            <i class="ri-arrow-down-s-line transform transition-transform"></i>
                        </button>
                        <div class="nav-group-content hidden mt-2 mr-6 space-y-1">
                            <a href="/sales" class="block p-2 rounded hover:bg-hover transition-colors text-sm">قائمة المبيعات</a>
                            <a href="/sales/returns" class="block p-2 rounded hover:bg-hover transition-colors text-sm">مرتجع المبيعات</a>
                            <a href="/sales/deferred" class="block p-2 rounded hover:bg-hover transition-colors text-sm">المبيعات الآجلة</a>
                        </div>
                    </div>
                    
                    <!-- Products -->
                    <a href="/products" class="nav-item flex items-center gap-3 p-3 rounded-lg hover:bg-hover transition-colors">
                        <i class="ri-product-hunt-line text-red"></i>
                        <span class="font-medium">المنتجات</span>
                    </a>
                    
                    <!-- Inventory -->
                    <a href="/inventory" class="nav-item flex items-center gap-3 p-3 rounded-lg hover:bg-hover transition-colors">
                        <i class="ri-building-line text-blue"></i>
                        <span class="font-medium">المخازن</span>
                    </a>
                    
                    <!-- Customers -->
                    <a href="/customers" class="nav-item flex items-center gap-3 p-3 rounded-lg hover:bg-hover transition-colors">
                        <i class="ri-user-line text-red"></i>
                        <span class="font-medium">العملاء</span>
                    </a>
                    
                    <!-- Reports -->
                    <a href="/reports" class="nav-item flex items-center gap-3 p-3 rounded-lg hover:bg-hover transition-colors">
                        <i class="ri-file-chart-line text-blue"></i>
                        <span class="font-medium">التقارير</span>
                    </a>
                    
                    <!-- Settings -->
                    <a href="/settings" class="nav-item flex items-center gap-3 p-3 rounded-lg hover:bg-hover transition-colors">
                        <i class="ri-settings-line text-red"></i>
                        <span class="font-medium">الإعدادات</span>
                    </a>
                </nav>
            </div>
        </aside>

        <!-- Main Content -->
        <main class="main-content lg:mr-64 min-h-screen">
            <!-- Page Header -->
            {% block page_header %}
            <div class="page-header bg-card border-b border-primary p-6">
                <div class="flex items-center justify-between">
                    <div>
                        <h1 class="text-2xl font-bold text-primary mb-1">
                            {% block page_title %}لوحة التحكم{% endblock %}
                        </h1>
                        <p class="text-secondary">
                            {% block page_subtitle %}مرحباً بك في نظام نوبارا لنقاط البيع{% endblock %}
                        </p>
                    </div>
                    <div class="flex items-center gap-3">
                        {% block page_actions %}{% endblock %}
                    </div>
                </div>
            </div>
            {% endblock %}
            
            <!-- Page Content -->
            <div class="page-content p-6">
                {% block content %}
                <div class="text-center py-20">
                    <div class="text-6xl mb-4">🏪</div>
                    <h2 class="text-2xl font-bold text-primary mb-2">مرحباً بك في نوبارا</h2>
                    <p class="text-secondary">نظام نقاط البيع الاحترافي</p>
                </div>
                {% endblock %}
            </div>
        </main>

        <!-- Footer -->
        <footer class="footer bg-card border-t border-primary p-4 lg:mr-64">
            <div class="flex items-center justify-between text-sm text-secondary">
                <div>
                    <p>&copy; 2024 نوبارا - جميع الحقوق محفوظة</p>
                </div>
                <div>
                    <p>Powered By ENG/ Fouad Saber Tel: 01020073527</p>
                </div>
            </div>
        </footer>
    </div>

    <!-- Overlay for mobile sidebar -->
    <div id="sidebar-overlay" class="fixed inset-0 bg-black bg-opacity-50 z-20 hidden lg:hidden"></div>

    <!-- Core JavaScript -->
    <script>
        // Global configuration
        window.APP_CONFIG = {
            name: "Nobara POS",
            nameAr: "نوبارا",
            version: "2.0.0",
            language: "{{ g.language or 'ar' }}",
            theme: "{{ g.theme or 'light' }}",
            isRTL: {{ 'true' if (g.language or 'ar') == 'ar' else 'false' }}
        };
        
        // Current user
        {% if current_user.is_authenticated %}
        window.currentUser = {
            id: {{ current_user.id }},
            username: "{{ current_user.username }}",
            fullName: "{{ current_user.full_name }}",
            role: "{{ current_user.role.name if current_user.role else 'user' }}"
        };
        {% else %}
        window.currentUser = null;
        {% endif %}
    </script>
    
    <!-- Theme Manager -->
    <script src="{{ url_for('static', filename='js/nobara-theme-manager.js') }}?v=2.0.0"></script>
    
    <!-- App JavaScript -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Hide loading screen
            setTimeout(() => {
                document.getElementById('loading-screen').style.display = 'none';
            }, 1000);
            
            // Sidebar toggle
            const sidebarToggle = document.getElementById('sidebar-toggle');
            const sidebar = document.getElementById('sidebar');
            const overlay = document.getElementById('sidebar-overlay');
            
            sidebarToggle?.addEventListener('click', () => {
                sidebar.classList.toggle('translate-x-full');
                overlay.classList.toggle('hidden');
            });
            
            overlay?.addEventListener('click', () => {
                sidebar.classList.add('translate-x-full');
                overlay.classList.add('hidden');
            });
            
            // Navigation groups
            document.querySelectorAll('.nav-group-toggle').forEach(toggle => {
                toggle.addEventListener('click', () => {
                    const content = toggle.nextElementSibling;
                    const arrow = toggle.querySelector('.ri-arrow-down-s-line');
                    
                    content.classList.toggle('hidden');
                    arrow.classList.toggle('rotate-180');
                });
            });
            
            console.log('🚀 Nobara POS System loaded successfully');
        });
    </script>
    
    <!-- Page Scripts -->
    {% block scripts %}{% endblock %}
</body>
</html>
