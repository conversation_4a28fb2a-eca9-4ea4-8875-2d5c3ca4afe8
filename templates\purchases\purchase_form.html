<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ 'إضافة مشتريات جديدة' if action == 'create' else 'تعديل المشتريات' }} - نظام إدارة المبيعات</title>
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <!-- Remixicon -->
    <link href="https://cdn.jsdelivr.net/npm/remixicon@2.5.0/fonts/remixicon.css" rel="stylesheet">
    <style>
        .bg-pattern {
            background-color: #f9fafb;
            background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='40' height='40' viewBox='0 0 40 40'%3E%3Cg fill-rule='evenodd'%3E%3Cg fill='%23e5e7eb' fill-opacity='0.4'%3E%3Cpath d='M0 38.59l2.83-2.83 1.41 1.41L1.41 40H0v-1.41zM0 1.4l2.83 2.83 1.41-1.41L1.41 0H0v1.41zM38.59 40l-2.83-2.83 1.41-1.41L40 38.59V40h-1.41zM40 1.41l-2.83 2.83-1.41-1.41L38.59 0H40v1.41zM20 18.6l2.83-2.83 1.41 1.41L21.41 20l2.83 2.83-1.41 1.41L20 21.41l-2.83 2.83-1.41-1.41L18.59 20l-2.83-2.83 1.41-1.41L20 18.59z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
        }
        .rtl {
            direction: rtl;
        }
    </style>
</head>
<body class="bg-pattern">
    <div class="flex h-screen overflow-hidden">
        <!-- Sidebar -->
        {% include 'partials/sidebar.html' %}

        <!-- Main Content -->
        <div class="flex-1 flex flex-col overflow-hidden">
            <!-- Top Navigation -->
            {% include 'partials/topnav.html' %}

            <!-- Content Area -->
            <main class="flex-1 overflow-y-auto p-6">
                <div class="mb-6 flex items-center justify-between">
                    <div>
                        <h1 class="text-2xl font-bold text-gray-800">{{ 'إضافة مشتريات جديدة' if action == 'create' else 'تعديل المشتريات' }}</h1>
                        <p class="text-gray-600">{{ 'إضافة عملية شراء جديدة من المورد' if action == 'create' else 'تعديل بيانات عملية الشراء' }}</p>
                    </div>
                    <a href="{{ url_for('purchases.index') }}" class="px-6 py-3 bg-blue-600 text-white rounded-lg shadow-md hover:bg-blue-700 hover:shadow-lg transition-all duration-300 text-lg font-bold flex items-center gap-2">
                        <i class="ri-arrow-right-line text-xl"></i>
                        العودة للمشتريات
                    </a>
                </div>

                <div class="glass-effect rounded-lg overflow-hidden">
                    <form id="purchaseForm" method="POST" action="{{ url_for('purchases.edit', id=purchase.id) if action == 'edit' else url_for('purchases.create') }}" class="p-6 space-y-6">
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <label for="reference_number" class="block text-sm font-medium text-gray-700 mb-1">رقم المرجع</label>
                                <input type="text" id="reference_number" name="reference_number" value="{{ purchase.reference_number if purchase else '' }}" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent" required>
                            </div>

                            <div>
                                <label for="supplier_id" class="block text-sm font-medium text-gray-700 mb-1">المورد</label>
                                <select id="supplier_id" name="supplier_id" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent" required>
                                    <option value="">اختر المورد</option>
                                    {% for supplier in suppliers %}
                                    <option value="{{ supplier.id }}" {% if purchase and purchase.supplier_id == supplier.id %}selected{% endif %}>{{ supplier.name }}</option>
                                    {% endfor %}
                                </select>
                            </div>

                            <div>
                                <label for="status" class="block text-sm font-medium text-gray-700 mb-1">الحالة</label>
                                <select id="status" name="status" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent" required>
                                    <option value="ordered" {% if purchase and purchase.status == 'ordered' %}selected{% endif %}>تم الطلب</option>
                                    <option value="received" {% if purchase and purchase.status == 'received' %}selected{% endif %}>تم الاستلام</option>
                                    <option value="pending" {% if purchase and purchase.status == 'pending' %}selected{% endif %}>قيد الانتظار</option>
                                </select>
                            </div>

                            <div>
                                <label for="expected_delivery" class="block text-sm font-medium text-gray-700 mb-1">تاريخ التسليم المتوقع</label>
                                <input type="date" id="expected_delivery" name="expected_delivery" value="{{ purchase.expected_delivery.strftime('%Y-%m-%d') if purchase and purchase.expected_delivery else '' }}" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent">
                            </div>
                        </div>

                        <div class="border-t border-gray-200 pt-6">
                            <h2 class="text-lg font-medium text-gray-800 mb-4">المنتجات</h2>

                            <!-- البحث بالباركود -->
                            <div class="mb-6 p-5 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg border border-blue-200 shadow-sm">
                                <div class="flex items-center gap-3 mb-3">
                                    <div class="w-10 h-10 rounded-full bg-blue-100 flex items-center justify-center text-blue-600">
                                        <i class="ri-barcode-line text-xl"></i>
                                    </div>
                                    <h3 class="text-lg font-medium text-blue-800">البحث بالباركود</h3>
                                </div>

                                <div class="flex items-center gap-4">
                                    <div class="flex-1 relative">
                                        <div class="absolute left-3 top-3 text-gray-400">
                                            <i class="ri-search-line"></i>
                                        </div>
                                        <input type="text" id="barcode-input" placeholder="أدخل الباركود أو اسم المنتج..."
                                            class="w-full pl-10 px-4 py-3 border border-blue-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white shadow-sm">
                                    </div>
                                    <button type="button" id="search-barcode"
                                        class="px-5 py-3 bg-gradient-to-r from-blue-600 to-indigo-600 text-white rounded-lg hover:from-blue-700 hover:to-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-all shadow-md flex items-center gap-2">
                                        <i class="ri-barcode-line"></i>
                                        <span>بحث</span>
                                    </button>
                                </div>

                                <div class="flex items-center gap-2 mt-3 text-sm text-blue-700">
                                    <i class="ri-information-line text-blue-500"></i>
                                    <p>يمكنك البحث بالباركود أو بجزء من اسم المنتج، وسيتم إضافة المنتج تلقائيًا إلى القائمة</p>
                                </div>

                                <!-- نتائج البحث (مخفية افتراضيًا) -->
                                <div id="search-results" class="mt-3 bg-white rounded-lg border border-blue-200 p-2 max-h-60 overflow-y-auto hidden">
                                    <!-- سيتم ملء هذا القسم بنتائج البحث -->
                                </div>
                            </div>

                            <div id="products-container" class="space-y-4">
                                <!-- هنا سيتم إضافة صفوف المنتجات بواسطة JavaScript -->
                                {% if purchase and purchase.items %}
                                    {% for item in purchase.items %}
                                    <div class="product-row grid grid-cols-12 gap-4 items-center">
                                        <div class="col-span-5">
                                            <select name="product_id[]" class="product-select w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent" required>
                                                <option value="">اختر المنتج</option>
                                                <!-- سيتم ملء هذه القائمة بواسطة JavaScript -->
                                            </select>
                                        </div>
                                        <div class="col-span-2">
                                            <input type="number" name="quantity[]" value="{{ item.quantity }}" placeholder="الكمية" min="1" class="quantity-input w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent" required>
                                        </div>
                                        <div class="col-span-2">
                                            <input type="number" name="cost_price[]" value="{{ item.cost_price }}" placeholder="سعر التكلفة" step="0.01" min="0" class="cost-price-input w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent" required>
                                        </div>
                                        <div class="col-span-2">
                                            <input type="number" name="total[]" value="{{ item.total }}" placeholder="الإجمالي" step="0.01" min="0" class="total-input w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent bg-gray-50" readonly>
                                        </div>
                                        <div class="col-span-1 text-center">
                                            <button type="button" class="remove-product text-red-500 hover:text-red-700">
                                                <i class="ri-delete-bin-line"></i>
                                            </button>
                                        </div>
                                    </div>
                                    {% endfor %}
                                {% else %}
                                <div class="product-row grid grid-cols-12 gap-4 items-center">
                                    <div class="col-span-5">
                                        <select name="product_id[]" class="product-select w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent" required>
                                            <option value="">اختر المنتج</option>
                                            <!-- سيتم ملء هذه القائمة بواسطة JavaScript -->
                                        </select>
                                    </div>
                                    <div class="col-span-2">
                                        <input type="number" name="quantity[]" placeholder="الكمية" min="1" class="quantity-input w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent" required>
                                    </div>
                                    <div class="col-span-2">
                                        <input type="number" name="cost_price[]" placeholder="سعر التكلفة" step="0.01" min="0" class="cost-price-input w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent" required>
                                    </div>
                                    <div class="col-span-2">
                                        <input type="number" name="total[]" placeholder="الإجمالي" step="0.01" min="0" class="total-input w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent bg-gray-50" readonly>
                                    </div>
                                    <div class="col-span-1 text-center">
                                        <button type="button" class="remove-product text-red-500 hover:text-red-700">
                                            <i class="ri-delete-bin-line"></i>
                                        </button>
                                    </div>
                                </div>
                                {% endif %}
                            </div>

                            <div class="mt-4">
                                <button type="button" id="add-product" class="px-4 py-2 bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 transition-all !rounded-button whitespace-nowrap">
                                    <i class="ri-add-line"></i>
                                    إضافة منتج
                                </button>
                            </div>
                        </div>

                        <div class="border-t border-gray-200 pt-6">
                            <div class="flex justify-between items-center">
                                <div class="text-lg font-medium text-gray-800">الإجمالي</div>
                                <div class="text-xl font-bold" id="grand-total">0.00 ج.م</div>
                                <input type="hidden" name="total" id="total-input" value="{{ purchase.total if purchase else '0.00' }}">
                            </div>
                        </div>

                        <div class="flex justify-end space-x-4 space-x-reverse mt-6">
                            <button type="submit" class="px-6 py-3 bg-gradient-to-r from-green-500 to-green-700 text-white rounded-lg shadow-md hover:shadow-lg transition-all duration-300 text-lg font-bold flex items-center gap-2">
                                <i class="ri-save-line text-xl"></i>
                                {{ 'إضافة المشتريات' if action == 'create' else 'حفظ التغييرات' }}
                            </button>
                            <a href="{{ url_for('purchases.index') }}" class="px-6 py-3 bg-gray-200 text-gray-700 rounded-lg hover:bg-gray-300 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 transition-all text-lg font-medium flex items-center gap-2">
                                <i class="ri-close-line text-xl"></i>
                                إلغاء
                            </a>
                        </div>
                    </form>
                </div>
            </main>
        </div>
    </div>

    <script>
        // تخزين قائمة المنتجات
        let products = [];

        // الحصول على قائمة المنتجات من API
        fetch('/api/products/search?query=')
            .then(response => response.json())
            .then(data => {
                products = data;

                // ملء قوائم المنتجات
                const productSelects = document.querySelectorAll('.product-select');
                productSelects.forEach(select => {
                    fillProductOptions(select);
                });

                // تحديد المنتجات المحددة مسبقًا (في حالة التعديل)
                {% if purchase and purchase.items %}
                    const productRows = document.querySelectorAll('.product-row');
                    {% for item in purchase.items %}
                        if (productRows[{{ loop.index0 }}]) {
                            const select = productRows[{{ loop.index0 }}].querySelector('.product-select');
                            select.value = "{{ item.product_id }}";
                        }
                    {% endfor %}
                {% endif %}
            });

        // ملء خيارات المنتجات في قائمة منسدلة
        function fillProductOptions(select) {
            // الاحتفاظ بالخيار الافتراضي
            const defaultOption = select.querySelector('option');

            // إزالة جميع الخيارات الحالية
            select.innerHTML = '';

            // إعادة إضافة الخيار الافتراضي
            select.appendChild(defaultOption);

            // إضافة المنتجات
            products.forEach(product => {
                const option = document.createElement('option');
                option.value = product.id;
                option.textContent = `${product.name} (${product.barcode || 'بدون باركود'})`;
                option.dataset.barcode = product.barcode;
                option.dataset.costPrice = product.cost_price;
                select.appendChild(option);
            });
        }

        // إضافة صف منتج جديد
        document.getElementById('add-product').addEventListener('click', function() {
            addNewProductRow();
        });

        // دالة إضافة صف منتج جديد
        function addNewProductRow(productData = null) {
            const container = document.getElementById('products-container');

            const newRow = document.createElement('div');
            newRow.className = 'product-row grid grid-cols-12 gap-4 items-center';
            newRow.innerHTML = `
                <div class="col-span-5">
                    <select name="product_id[]" class="product-select w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent" required>
                        <option value="">اختر المنتج</option>
                    </select>
                </div>
                <div class="col-span-2">
                    <input type="number" name="quantity[]" placeholder="الكمية" min="1" value="1" class="quantity-input w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent" required>
                </div>
                <div class="col-span-2">
                    <input type="number" name="cost_price[]" placeholder="سعر التكلفة" step="0.01" min="0" class="cost-price-input w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent" required>
                </div>
                <div class="col-span-2">
                    <input type="number" name="total[]" placeholder="الإجمالي" step="0.01" min="0" class="total-input w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent bg-gray-50" readonly>
                </div>
                <div class="col-span-1 text-center">
                    <button type="button" class="remove-product text-red-500 hover:text-red-700">
                        <i class="ri-delete-bin-line"></i>
                    </button>
                </div>
            `;

            container.appendChild(newRow);

            // ملء قائمة المنتجات
            const select = newRow.querySelector('.product-select');
            fillProductOptions(select);

            // إذا تم تمرير بيانات منتج، قم بتعيينها
            if (productData) {
                select.value = productData.id;

                const costPriceInput = newRow.querySelector('.cost-price-input');
                costPriceInput.value = productData.cost_price;

                // حساب الإجمالي
                calculateRowTotal(newRow);
            }

            // إضافة مستمعي الأحداث
            addEventListeners(newRow);

            // إضافة مستمع حدث لتغيير المنتج
            select.addEventListener('change', function() {
                const selectedOption = this.options[this.selectedIndex];
                if (selectedOption.dataset.costPrice) {
                    const costPriceInput = newRow.querySelector('.cost-price-input');
                    costPriceInput.value = selectedOption.dataset.costPrice;
                    calculateRowTotal(newRow);
                }
            });

            return newRow;
        }

        // إزالة صف منتج
        document.addEventListener('click', function(e) {
            if (e.target.classList.contains('remove-product') || e.target.closest('.remove-product')) {
                const button = e.target.classList.contains('remove-product') ? e.target : e.target.closest('.remove-product');
                const row = button.closest('.product-row');

                // التأكد من وجود صف واحد على الأقل
                const rows = document.querySelectorAll('.product-row');
                if (rows.length > 1) {
                    row.remove();
                    calculateTotals();
                }
            }
        });

        // إضافة مستمعي الأحداث لصف منتج
        function addEventListeners(row) {
            const quantityInput = row.querySelector('.quantity-input');
            const costPriceInput = row.querySelector('.cost-price-input');

            quantityInput.addEventListener('input', function() {
                calculateRowTotal(row);
            });

            costPriceInput.addEventListener('input', function() {
                calculateRowTotal(row);
            });
        }

        // حساب إجمالي الصف
        function calculateRowTotal(row) {
            const quantity = parseFloat(row.querySelector('.quantity-input').value) || 0;
            const costPrice = parseFloat(row.querySelector('.cost-price-input').value) || 0;
            const total = quantity * costPrice;

            row.querySelector('.total-input').value = total.toFixed(2);

            calculateTotals();
        }

        // حساب الإجمالي الكلي
        function calculateTotals() {
            const totalInputs = document.querySelectorAll('.total-input');
            let grandTotal = 0;

            totalInputs.forEach(input => {
                grandTotal += parseFloat(input.value) || 0;
            });

            document.getElementById('grand-total').textContent = grandTotal.toFixed(2) + ' ج.م';
            document.getElementById('total-input').value = grandTotal.toFixed(2);
        }

        // البحث بالباركود
        document.getElementById('search-barcode').addEventListener('click', function() {
            searchByBarcode();
        });

        // البحث بالباركود عند الضغط على Enter
        document.getElementById('barcode-input').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                e.preventDefault();
                searchByBarcode();
            }
        });

        // البحث أثناء الكتابة (مع تأخير)
        document.getElementById('barcode-input').addEventListener('input', debounce(function() {
            const searchTerm = this.value.trim();
            if (searchTerm.length >= 2) {
                searchByBarcode();
            } else {
                document.getElementById('search-results').classList.add('hidden');
            }
        }, 500));

        // إخفاء نتائج البحث عند النقر خارجها
        document.addEventListener('click', function(e) {
            const searchResults = document.getElementById('search-results');
            const barcodeInput = document.getElementById('barcode-input');
            const searchButton = document.getElementById('search-barcode');

            if (!searchResults.contains(e.target) && e.target !== barcodeInput && e.target !== searchButton) {
                searchResults.classList.add('hidden');
            }
        });

        // دالة البحث بالباركود أو الاسم
        function searchByBarcode() {
            const barcodeInput = document.getElementById('barcode-input');
            const searchTerm = barcodeInput.value.trim();
            const searchResults = document.getElementById('search-results');

            if (!searchTerm) {
                alert('الرجاء إدخال الباركود أو اسم المنتج');
                return;
            }

            // التحقق إذا كان الإدخال باركود (أرقام فقط)
            const isBarcode = /^\d+$/.test(searchTerm);

            if (isBarcode) {
                // البحث عن المنتج بالباركود
                fetch(`/api/products/barcode/${searchTerm}`)
                    .then(response => {
                        if (!response.ok) {
                            // إذا لم يتم العثور على المنتج بالباركود، نبحث بالاسم
                            return fetch(`/api/products/search?query=${searchTerm}`);
                        }
                        return response.json().then(product => ({ singleProduct: true, product }));
                    })
                    .then(data => {
                        if (data.singleProduct) {
                            // تم العثور على منتج واحد بالباركود
                            addProductAndClearSearch(data.product);
                        } else {
                            // عرض نتائج البحث
                            displaySearchResults(data);
                        }
                    })
                    .catch(error => {
                        console.error('خطأ في البحث:', error);
                        searchResults.innerHTML = `<div class="p-3 text-red-600 text-center">حدث خطأ أثناء البحث</div>`;
                        searchResults.classList.remove('hidden');
                    });
            } else {
                // البحث عن المنتج بالاسم
                fetch(`/api/products/search?query=${searchTerm}`)
                    .then(response => response.json())
                    .then(data => {
                        displaySearchResults(data);
                    })
                    .catch(error => {
                        console.error('خطأ في البحث:', error);
                        searchResults.innerHTML = `<div class="p-3 text-red-600 text-center">حدث خطأ أثناء البحث</div>`;
                        searchResults.classList.remove('hidden');
                    });
            }
        }

        // عرض نتائج البحث
        function displaySearchResults(products) {
            const searchResults = document.getElementById('search-results');

            // إذا لم تكن هناك نتائج
            if (!products || products.length === 0) {
                searchResults.innerHTML = `<div class="p-3 text-gray-500 text-center">لم يتم العثور على منتجات</div>`;
                searchResults.classList.remove('hidden');
                return;
            }

            // إنشاء قائمة النتائج
            let resultsHTML = '';
            products.forEach(product => {
                resultsHTML += `
                    <div class="product-result p-2 hover:bg-blue-50 rounded cursor-pointer flex items-center justify-between" data-product-id="${product.id}">
                        <div>
                            <div class="font-medium text-gray-800">${product.name}</div>
                            <div class="text-xs text-gray-500">
                                <span class="ml-2">الباركود: ${product.barcode || 'غير متوفر'}</span>
                                <span>السعر: ${product.cost_price} ج.م</span>
                            </div>
                        </div>
                        <button class="add-product-btn px-2 py-1 bg-green-100 text-green-700 rounded hover:bg-green-200 text-xs">
                            <i class="ri-add-line"></i>
                            إضافة
                        </button>
                    </div>
                `;
            });

            searchResults.innerHTML = resultsHTML;
            searchResults.classList.remove('hidden');

            // إضافة مستمعي الأحداث لنتائج البحث
            document.querySelectorAll('.product-result').forEach(result => {
                result.addEventListener('click', function() {
                    const productId = this.dataset.productId;
                    const product = products.find(p => p.id == productId);
                    if (product) {
                        addProductAndClearSearch(product);
                    }
                });
            });
        }

        // إضافة المنتج وتنظيف البحث
        function addProductAndClearSearch(product) {
            // إضافة المنتج إلى الجدول
            const newRow = addNewProductRow(product);

            // مسح حقل البحث وإخفاء النتائج
            const barcodeInput = document.getElementById('barcode-input');
            barcodeInput.value = '';
            barcodeInput.focus();
            document.getElementById('search-results').classList.add('hidden');

            // تمييز الصف الجديد
            newRow.classList.add('bg-green-50');
            setTimeout(() => {
                newRow.classList.remove('bg-green-50');
            }, 2000);

            // التمرير إلى الصف الجديد
            newRow.scrollIntoView({ behavior: 'smooth', block: 'center' });
        }

        // إضافة مستمعي الأحداث للصفوف الموجودة
        document.querySelectorAll('.product-row').forEach(row => {
            addEventListeners(row);
        });

        // حساب الإجمالي الأولي
        calculateTotals();

        // دالة debounce لتأخير تنفيذ الدوال
        function debounce(func, delay) {
            let timeout;
            return function() {
                const context = this;
                const args = arguments;
                clearTimeout(timeout);
                timeout = setTimeout(() => func.apply(context, args), delay);
            };
        }
    </script>
</body>
</html>
