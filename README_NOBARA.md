# 🏪 نوبارا - نظام نقاط البيع الاحترافي
# Nobara Professional POS System

## 📋 معلومات النظام / System Information

**النسخة / Version:** 2.0.0  
**المطور / Developer:** ENG/ Fouad <PERSON>  
**الهاتف / Phone:** 01020073527  
**البريد الإلكتروني / Email:** <EMAIL>  

---

## 🚀 كيفية تشغيل النظام / How to Run

### الطريقة الأولى / Method 1 (Recommended):
1. انقر نقراً مزدوجاً على ملف `start_nobara.bat`
2. Double-click on `start_nobara.bat` file

### الطريقة الثانية / Method 2:
1. افتح موجه الأوامر / Open Command Prompt
2. اكتب الأمر التالي / Type the following command:
```bash
python run_nobara.py
```

---

## 🌐 الوصول للنظام / System Access

بعد تشغيل النظام، يمكنك الوصول إليه عبر:
After running the system, you can access it via:

- **المحلي / Local:** http://localhost:5000
- **الشبكة المحلية / Network:** http://[YOUR-IP]:5000

---

## 🎯 بيانات الدخول الافتراضية / Default Login Credentials

- **اسم المستخدم / Username:** admin
- **كلمة المرور / Password:** admin

---

## ✨ الميزات المتاحة / Available Features

### ✅ متاح حالياً / Currently Available:
- تسجيل الدخول والخروج الآمن / Secure Login/Logout
- لوحة التحكم مع الإحصائيات / Dashboard with Statistics
- قاعدة بيانات محلية / Local Database
- واجهة مستخدم احترافية / Professional UI
- دعم اللغة العربية / Arabic Language Support

### 🔄 قيد التطوير / Under Development:
- نقطة البيع / Point of Sale
- إدارة المنتجات / Product Management
- إدارة العملاء / Customer Management
- التقارير / Reports
- إدارة المخزون / Inventory Management

---

## 🛠️ المتطلبات التقنية / Technical Requirements

- Python 3.7 أو أحدث / Python 3.7 or newer
- Flask
- Flask-SQLAlchemy
- Flask-Login
- Werkzeug

---

## 📁 هيكل الملفات / File Structure

```
📁 PythonCashierSystem/
├── 📄 run_nobara.py          # الملف الرئيسي للتشغيل / Main runner file
├── 📄 start_nobara.bat       # ملف التشغيل السريع / Quick start file
├── 📄 README_NOBARA.md       # هذا الملف / This file
├── 📄 nobara_pos.db          # قاعدة البيانات / Database file
└── 📁 instance/              # مجلد البيانات / Data folder
```

---

## 🔧 استكشاف الأخطاء / Troubleshooting

### مشكلة: لا يعمل النظام
**الحل:**
1. تأكد من تثبيت Python
2. تأكد من تثبيت المكتبات المطلوبة
3. شغل الأمر: `pip install flask flask-sqlalchemy flask-login`

### Problem: System doesn't work
**Solution:**
1. Make sure Python is installed
2. Make sure required libraries are installed
3. Run: `pip install flask flask-sqlalchemy flask-login`

---

## 📞 الدعم الفني / Technical Support

للحصول على الدعم الفني أو الاستفسارات:
For technical support or inquiries:

- **الهاتف / Phone:** 01020073527
- **البريد الإلكتروني / Email:** <EMAIL>

---

## 📄 الترخيص / License

هذا النظام مطور بواسطة المهندس فؤاد صابر
This system is developed by ENG/ Fouad Saber

**جميع الحقوق محفوظة © 2024**
**All Rights Reserved © 2024**

---

## 🏆 شكر خاص / Special Thanks

**Powered By ENG/ Fouad Saber - Tel: 01020073527**

نشكركم لاستخدام نظام نوبارا لنقاط البيع
Thank you for using Nobara POS System
