"""
نماذج نظام الأمان المتقدم
"""

from datetime import datetime
from app import db
from sqlalchemy.orm import relationship


class Role(db.Model):
    """نموذج الأدوار (Roles)"""
    __table_args__ = {'extend_existing': True}

    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(64), nullable=False, unique=True)
    description = db.Column(db.Text)
    is_system = db.Column(db.<PERSON><PERSON>, default=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # العلاقات
    # تم إزالة العلاقة مع المستخدمين لأن نموذج User يستخدم حقل role كنص وليس كمفتاح خارجي
    # تم تعطيل العلاقة مؤقتاً لحل مشكلة التشغيل
    # permissions = relationship('SecurityPermission', secondary='role_permission', backref='roles')

    def __repr__(self):
        return f"<Role {self.name}>"


class SecurityPermission(db.Model):
    """نموذج الصلاحيات الأمنية (Security Permissions)"""
    __tablename__ = 'security_permission'

    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(64), nullable=False, unique=True)
    code = db.Column(db.String(64), nullable=False, unique=True)
    description = db.Column(db.Text)
    module = db.Column(db.String(64), nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    def __repr__(self):
        return f"<SecurityPermission {self.code}>"


class RolePermission(db.Model):
    """نموذج صلاحيات الأدوار (Role Permissions)"""
    __tablename__ = 'role_permission'

    id = db.Column(db.Integer, primary_key=True)
    role_id = db.Column(db.Integer, db.ForeignKey('role.id', ondelete='CASCADE'), nullable=False, index=True)
    permission_id = db.Column(db.Integer, db.ForeignKey('security_permission.id', ondelete='CASCADE'), nullable=False, index=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    # العلاقات
    permission = db.relationship('SecurityPermission')

    def __repr__(self):
        return f"<RolePermission {self.id}>"


class ActivityLog(db.Model):
    """نموذج سجل الأنشطة (Activity Log)"""
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), index=True)
    action = db.Column(db.String(64), nullable=False, index=True)
    module = db.Column(db.String(64), nullable=False, index=True)
    description = db.Column(db.Text)
    ip_address = db.Column(db.String(45))
    user_agent = db.Column(db.String(256))
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    # العلاقات
    user = relationship('User', backref='activities')

    def __repr__(self):
        return f"<ActivityLog {self.id}: {self.action}>"


class UserSession(db.Model):
    """نموذج جلسات المستخدمين (User Sessions)"""
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('user.id', ondelete='CASCADE'), nullable=False, index=True)
    session_id = db.Column(db.String(128), nullable=False, unique=True)
    ip_address = db.Column(db.String(45))
    user_agent = db.Column(db.String(256))
    login_at = db.Column(db.DateTime, nullable=False, default=datetime.utcnow)
    logout_at = db.Column(db.DateTime)
    is_active = db.Column(db.Boolean, default=True)

    # العلاقات
    user = relationship('User', backref='sessions')

    def __repr__(self):
        return f"<UserSession {self.id}>"


class LoginAttempt(db.Model):
    """نموذج محاولات تسجيل الدخول (Login Attempts)"""
    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(64), nullable=False, index=True)
    ip_address = db.Column(db.String(45))
    user_agent = db.Column(db.String(256))
    status = db.Column(db.String(20), nullable=False, index=True)  # success, failed
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    def __repr__(self):
        return f"<LoginAttempt {self.id}: {self.status}>"


class PasswordResetToken(db.Model):
    """نموذج رموز إعادة تعيين كلمة المرور (Password Reset Tokens)"""
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('user.id', ondelete='CASCADE'), nullable=False, index=True)
    token = db.Column(db.String(128), nullable=False, unique=True)
    expires_at = db.Column(db.DateTime, nullable=False)
    is_used = db.Column(db.Boolean, default=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    # العلاقات
    user = relationship('User', backref='reset_tokens')

    def __repr__(self):
        return f"<PasswordResetToken {self.id}>"

    @property
    def is_expired(self):
        """التحقق من انتهاء صلاحية الرمز"""
        return datetime.utcnow() > self.expires_at

    @property
    def is_valid(self):
        """التحقق من صلاحية الرمز"""
        return not self.is_used and not self.is_expired
