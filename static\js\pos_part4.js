// Setup all event listeners
function setupEventListeners() {
    // Category buttons
    document.querySelectorAll('.category-btn').forEach(btn => {
        btn.addEventListener('click', function() {
            // Update selected category
            selectedCategory = this.dataset.category;

            // Update UI
            document.querySelectorAll('.category-btn').forEach(b => b.classList.remove('category-active'));
            this.classList.add('category-active');

            // Load products for this category
            loadProducts(document.getElementById('product-search').value);
        });
    });

    // Warehouse select
    const warehouseSelect = document.getElementById('warehouse-select');
    if (warehouseSelect) {
        warehouseSelect.addEventListener('change', function() {
            // Update selected warehouse
            selectedWarehouse = this.value;

            // Reload products
            loadProducts(document.getElementById('product-search').value);

            // Show notification
            showNotification(`تم تحديد المخزن: ${this.options[this.selectedIndex].text}`, 'info');
        });
    }

    // Search input
    const searchInput = document.getElementById('product-search');
    searchInput.addEventListener('input', debounce(function() {
        loadProducts(this.value);
    }, 300));

    // إضافة مستمع حدث للبحث عند الضغط على Enter
    searchInput.addEventListener('keydown', function(e) {
        if (e.key === 'Enter') {
            e.preventDefault();
            loadProducts(this.value);

            // إضافة المنتج الأول من نتائج البحث إلى السلة إذا كان متاحًا
            if (products.length === 1 && products[0].stock_status !== 'out_of_stock') {
                addToCart(products[0]);
                this.value = ''; // مسح حقل البحث
                loadProducts(); // إعادة تحميل جميع المنتجات
            }
        }
    });

    // Discount and tax inputs
    document.getElementById('discount-value').addEventListener('input', function() {
        const discount = parseFloat(this.value) || 0;
        const discountType = document.getElementById('discount-type').value;
        applyCartDiscount(discount, discountType);
    });

    document.getElementById('discount-type').addEventListener('change', function() {
        const discount = parseFloat(document.getElementById('discount-value').value) || 0;
        applyCartDiscount(discount, this.value);
    });

    document.getElementById('tax-percentage').addEventListener('input', function() {
        updateCartDisplay();
    });

    // Clear cart button
    document.getElementById('clear-cart-btn').addEventListener('click', function() {
        if (cartItems.length > 0 && confirm('هل أنت متأكد من حذف جميع المنتجات من السلة؟')) {
            cartItems = [];
            updateCartDisplay();
        }
    });

    // Suspend cart button
    document.getElementById('suspend-cart-btn').addEventListener('click', function() {
        suspendInvoice();
    });

    // Suspended invoices button
    document.getElementById('suspended-invoices-btn').addEventListener('click', function() {
        showSuspendedInvoices();
        showModal('suspendedInvoicesModal', 'suspended-modal-content');
    });

    // Close suspended invoices modal
    document.getElementById('close-suspended-modal').addEventListener('click', function() {
        hideModal('suspendedInvoicesModal', 'suspended-modal-content');
    });

    // Return invoice button
    document.getElementById('return-invoice-btn').addEventListener('click', function() {
        showModal('returnInvoiceModal', 'return-modal-content');
    });

    // Close return invoice modal
    document.getElementById('close-return-modal').addEventListener('click', function() {
        hideModal('returnInvoiceModal', 'return-modal-content');
    });

    // Checkout button
    document.getElementById('checkout-btn').addEventListener('click', function() {
        openCheckoutModal();
    });

    // Payment method cards
    document.querySelectorAll('.payment-method-card').forEach(card => {
        card.addEventListener('click', function() {
            // Update selected payment method
            selectedPaymentMethod = this.dataset.method;

            // Check if this is a credit payment method
            const isCredit = this.dataset.isCredit === 'true';
            const requiresReference = this.dataset.requiresReference === 'true';

            // Update UI
            document.querySelectorAll('.payment-method-card').forEach(c => c.classList.remove('payment-method-active'));
            this.classList.add('payment-method-active');

            // Show/hide reference details for methods that require it
            const visaDetails = document.getElementById('visa-payment-details');
            if (visaDetails) {
                if (requiresReference) {
                    visaDetails.classList.remove('hidden');
                    // Focus on card number field
                    setTimeout(() => {
                        document.getElementById('card-number').focus();
                    }, 100);
                } else {
                    visaDetails.classList.add('hidden');
                }
            }

            // Handle credit payment method
            if (isCredit) {
                const customerId = document.getElementById('customer-select').value;
                if (!customerId || customerId === 'null' || customerId === '') {
                    showNotification('يرجى اختيار عميل للبيع الآجل', 'warning');
                    document.getElementById('customer-select').focus();
                }
            }

            // Update received amount field based on payment method
            const receivedAmountField = document.getElementById('received-amount');
            const total = calculateTotal();

            if (selectedPaymentMethod === 'card' || selectedPaymentMethod === 'visa') {
                // For card payments, automatically set the exact amount
                receivedAmountField.value = total.toFixed(2);
                receivedAmountField.readOnly = true;
                document.getElementById('change-amount').textContent = '0.00 ج.م';
            } else {
                // For cash or credit, allow manual input
                receivedAmountField.readOnly = false;
                if (selectedPaymentMethod === 'cash' && !receivedAmountField.value) {
                    // Suggest the exact amount for cash payments
                    receivedAmountField.placeholder = total.toFixed(2) + ' ج.م';
                }
            }
        });
    });

    // Cancel checkout button
    document.getElementById('cancel-checkout').addEventListener('click', function() {
        hideModal('checkoutModal', 'checkout-modal-content');
    });

    // Confirm checkout button
    document.getElementById('confirm-checkout').addEventListener('click', function() {
        processSale();
    });

    // Close checkout modal button
    document.getElementById('close-checkout-modal').addEventListener('click', function() {
        hideModal('checkoutModal', 'checkout-modal-content');
    });

    // Add customer modal
    document.getElementById('close-add-customer-modal').addEventListener('click', function() {
        hideModal('addCustomerModal', 'add-customer-modal-content');
    });

    document.getElementById('cancel-add-customer').addEventListener('click', function() {
        hideModal('addCustomerModal', 'add-customer-modal-content');
    });

    document.getElementById('save-customer').addEventListener('click', function() {
        saveNewCustomer();
    });

    // Success modal - طباعة الإيصال
    const printReceiptBtn = document.getElementById('print-receipt');
    if (printReceiptBtn) {
        printReceiptBtn.addEventListener('click', function() {
            const orderId = this.dataset.orderId;
            if (!orderId) {
                showNotification('لم يتم العثور على معرف الطلب', 'error');
                return;
            }

            showNotification('جاري تحضير الإيصال للطباعة...', 'info');

            // Create a hidden iframe for printing
            const iframe = document.createElement('iframe');
            iframe.style.display = 'none';
            iframe.src = `/pos/receipt/${orderId}`;
            document.body.appendChild(iframe);

            // Wait for iframe to load then print
            iframe.onload = function() {
                try {
                    iframe.contentWindow.print();
                    showNotification('تم إرسال الإيصال للطباعة', 'success');

                    // Remove iframe after printing (with delay)
                    setTimeout(function() {
                        document.body.removeChild(iframe);
                    }, 1000);
                } catch (e) {
                    console.error('Error printing receipt:', e);
                    showNotification('حدث خطأ أثناء الطباعة', 'error');
                    document.body.removeChild(iframe);
                }
            };
        });
    }

    // طباعة الفاتورة الحرارية
    const printInvoiceBtn = document.getElementById('print-invoice');
    if (printInvoiceBtn) {
        printInvoiceBtn.addEventListener('click', function(event) {
            event.stopPropagation(); // منع انتشار الحدث

            const orderId = this.dataset.orderId;
            if (!orderId) {
                showNotification('لم يتم العثور على معرف الطلب', 'error');
                return;
            }

            // إخفاء القائمة المنسدلة
            const invoiceOptions = document.getElementById('invoice-options');
            if (invoiceOptions) {
                invoiceOptions.classList.add('opacity-0', 'scale-95');
                invoiceOptions.classList.remove('opacity-100', 'scale-100');
                setTimeout(() => {
                    invoiceOptions.classList.add('hidden');
                    invoiceOptions.style.display = 'none';
                }, 300);
            }

            showNotification('جاري تحضير الفاتورة للطباعة...', 'info');

            // Create a hidden iframe for printing
            const iframe = document.createElement('iframe');
            iframe.style.display = 'none';
            iframe.src = `/pos/invoice/${orderId}`;
            document.body.appendChild(iframe);

            // Wait for iframe to load then print
            iframe.onload = function() {
                try {
                    iframe.contentWindow.print();
                    showNotification('تم إرسال الفاتورة للطباعة', 'success');

                    // Remove iframe after printing (with delay)
                    setTimeout(function() {
                        document.body.removeChild(iframe);
                    }, 1000);
                } catch (e) {
                    console.error('Error printing invoice:', e);
                    showNotification('حدث خطأ أثناء الطباعة', 'error');
                    document.body.removeChild(iframe);
                }
            };
        });
    }

    // طباعة فاتورة A4
    const printInvoiceA4Btn = document.getElementById('print-invoice-a4');
    if (printInvoiceA4Btn) {
        printInvoiceA4Btn.addEventListener('click', function(event) {
            event.stopPropagation(); // منع انتشار الحدث

            const orderId = this.dataset.orderId;
            if (!orderId) {
                showNotification('لم يتم العثور على معرف الطلب', 'error');
                return;
            }

            // إخفاء القائمة المنسدلة
            const invoiceOptions = document.getElementById('invoice-options');
            if (invoiceOptions) {
                invoiceOptions.classList.add('opacity-0', 'scale-95');
                invoiceOptions.classList.remove('opacity-100', 'scale-100');
                setTimeout(() => {
                    invoiceOptions.classList.add('hidden');
                    invoiceOptions.style.display = 'none';
                }, 300);
            }

            showNotification('جاري تحضير فاتورة A4 للطباعة...', 'info');

            // Create a hidden iframe for printing
            const iframe = document.createElement('iframe');
            iframe.style.display = 'none';
            iframe.src = `/pos/invoice_a4/${orderId}`;
            document.body.appendChild(iframe);

            // Wait for iframe to load then print
            iframe.onload = function() {
                try {
                    iframe.contentWindow.print();
                    showNotification('تم إرسال فاتورة A4 للطباعة', 'success');

                    // Remove iframe after printing (with delay)
                    setTimeout(function() {
                        document.body.removeChild(iframe);
                    }, 1000);
                } catch (e) {
                    console.error('Error printing A4 invoice:', e);
                    showNotification('حدث خطأ أثناء الطباعة', 'error');
                    document.body.removeChild(iframe);
                }
            };
        });
    }

    // طباعة فاتورة A5
    const printInvoiceA5Btn = document.getElementById('print-invoice-a5');
    if (printInvoiceA5Btn) {
        printInvoiceA5Btn.addEventListener('click', function(event) {
            event.stopPropagation(); // منع انتشار الحدث

            const orderId = this.dataset.orderId;
            if (!orderId) {
                showNotification('لم يتم العثور على معرف الطلب', 'error');
                return;
            }

            // إخفاء القائمة المنسدلة
            const invoiceOptions = document.getElementById('invoice-options');
            if (invoiceOptions) {
                invoiceOptions.classList.add('opacity-0', 'scale-95');
                invoiceOptions.classList.remove('opacity-100', 'scale-100');
                setTimeout(() => {
                    invoiceOptions.classList.add('hidden');
                    invoiceOptions.style.display = 'none';
                }, 300);
            }

            showNotification('جاري تحضير فاتورة A5 للطباعة...', 'info');

            // Create a hidden iframe for printing
            const iframe = document.createElement('iframe');
            iframe.style.display = 'none';
            iframe.src = `/pos/invoice_a5/${orderId}`;
            document.body.appendChild(iframe);

            // Wait for iframe to load then print
            iframe.onload = function() {
                try {
                    iframe.contentWindow.print();
                    showNotification('تم إرسال فاتورة A5 للطباعة', 'success');

                    // Remove iframe after printing (with delay)
                    setTimeout(function() {
                        document.body.removeChild(iframe);
                    }, 1000);
                } catch (e) {
                    console.error('Error printing A5 invoice:', e);
                    showNotification('حدث خطأ أثناء الطباعة', 'error');
                    document.body.removeChild(iframe);
                }
            };
        });
    }

    const openDrawerBtn = document.getElementById('open-drawer');
    if (openDrawerBtn) {
        openDrawerBtn.addEventListener('click', function() {
            showNotification('جاري فتح الدرج...', 'info');

            fetch('/api/pos/open-drawer', { method: 'POST' })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        showNotification('تم فتح الدرج بنجاح', 'success');
                    } else {
                        showNotification(data.message || 'حدث خطأ أثناء فتح الدرج', 'error');
                    }
                })
                .catch(error => {
                    console.error('Error opening drawer:', error);
                    showNotification('حدث خطأ أثناء فتح الدرج', 'error');
                });
        });
    }

    const newSaleBtn = document.getElementById('new-sale');
    if (newSaleBtn) {
        newSaleBtn.addEventListener('click', function() {
            // إخفاء القائمة المنسدلة إذا كانت مفتوحة
            const invoiceOptions = document.getElementById('invoice-options');
            if (invoiceOptions && (invoiceOptions.style.display === 'block' || !invoiceOptions.classList.contains('hidden'))) {
                invoiceOptions.classList.add('opacity-0', 'scale-95');
                invoiceOptions.classList.remove('opacity-100', 'scale-100');
                invoiceOptions.classList.add('hidden');
                invoiceOptions.style.display = 'none';
            }

            // إخفاء نافذة النجاح
            hideModal('successModal', 'success-modal-content');

            // عرض إشعار
            showNotification('تم بدء عملية بيع جديدة', 'success');
        });
    }

    // إضافة مستمع حدث لزر القائمة المنسدلة لطباعة الفاتورة
    document.addEventListener('DOMContentLoaded', function() {
        const invoiceDropdown = document.getElementById('print-invoice-dropdown');
        if (invoiceDropdown) {
            invoiceDropdown.addEventListener('click', function(event) {
                event.stopPropagation(); // منع انتشار الحدث للعناصر الأخرى

                const invoiceOptions = document.getElementById('invoice-options');
                if (!invoiceOptions) return;

                // تبديل حالة العرض باستخدام خاصية style.display
                if (invoiceOptions.style.display === 'none' || invoiceOptions.classList.contains('hidden')) {
                    // إظهار القائمة
                    invoiceOptions.style.display = 'block';
                    invoiceOptions.classList.remove('hidden');
                    setTimeout(() => {
                        invoiceOptions.classList.remove('opacity-0', 'scale-95');
                        invoiceOptions.classList.add('opacity-100', 'scale-100');
                    }, 10);
                } else {
                    // إخفاء القائمة
                    invoiceOptions.classList.add('opacity-0', 'scale-95');
                    invoiceOptions.classList.remove('opacity-100', 'scale-100');
                    setTimeout(() => {
                        invoiceOptions.classList.add('hidden');
                        invoiceOptions.style.display = 'none';
                    }, 300);
                }
            });
        }
    });

    // إخفاء القائمة عند النقر خارجها
    document.addEventListener('DOMContentLoaded', function() {
        document.addEventListener('click', function(event) {
            const invoiceOptions = document.getElementById('invoice-options');
            const dropdown = document.getElementById('print-invoice-dropdown');

            // التحقق من وجود العناصر قبل استخدامها
            if (invoiceOptions && dropdown &&
                (invoiceOptions.style.display === 'block' || !invoiceOptions.classList.contains('hidden')) &&
                !dropdown.contains(event.target) &&
                !invoiceOptions.contains(event.target)) {
                invoiceOptions.classList.add('opacity-0', 'scale-95');
                invoiceOptions.classList.remove('opacity-100', 'scale-100');
                setTimeout(() => {
                    invoiceOptions.classList.add('hidden');
                    invoiceOptions.style.display = 'none';
                }, 300);
            }
        });
    });
}

// Open item discount modal
function openItemDiscountModal(index) {
    currentItemDiscountIndex = index;
    const item = cartItems[index];

    // Create modal if it doesn't exist
    if (!document.getElementById('itemDiscountModal')) {
        const modal = document.createElement('div');
        modal.id = 'itemDiscountModal';
        modal.className = 'fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center hidden';
        modal.innerHTML = `
            <div id="item-discount-modal-content" class="bg-white rounded-lg shadow-xl w-full max-w-md transform scale-95 opacity-0 transition-all duration-300">
                <div class="p-4 border-b border-gray-200 flex justify-between items-center">
                    <h3 class="text-lg font-bold text-gray-800">تطبيق خصم على المنتج</h3>
                    <button id="close-item-discount-modal" class="text-gray-500 hover:text-gray-700 transition-colors duration-300">
                        <i class="ri-close-line text-xl"></i>
                    </button>
                </div>
                <div class="p-6">
                    <div class="mb-4">
                        <p class="text-sm text-gray-600 mb-1">المنتج:</p>
                        <p id="discount-item-name" class="font-medium"></p>
                    </div>
                    <div class="mb-4">
                        <p class="text-sm text-gray-600 mb-1">السعر الأصلي:</p>
                        <p id="discount-item-price" class="font-medium"></p>
                    </div>
                    <div class="mb-4">
                        <label for="item-discount-value" class="block text-sm font-medium text-gray-700 mb-1">قيمة الخصم:</label>
                        <div class="flex">
                            <input type="number" id="item-discount-value" class="block w-full rounded-r-lg border-gray-300 focus:ring-primary focus:border-primary" min="0" step="0.01" value="0">
                            <select id="item-discount-type" class="rounded-l-lg border-gray-300 focus:ring-primary focus:border-primary">
                                <option value="fixed">ج.م</option>
                                <option value="percentage">%</option>
                            </select>
                        </div>
                    </div>
                    <div class="mb-4">
                        <p class="text-sm text-gray-600 mb-1">السعر بعد الخصم:</p>
                        <p id="discount-item-final-price" class="font-medium text-green-600"></p>
                    </div>
                </div>
                <div class="p-4 border-t border-gray-200 flex justify-between">
                    <button id="remove-item-discount" class="px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors duration-300">إزالة الخصم</button>
                    <button id="apply-item-discount" class="px-4 py-2 bg-primary text-white rounded-lg hover:bg-primary-dark transition-colors duration-300">تطبيق الخصم</button>
                </div>
            </div>
        `;
        document.body.appendChild(modal);

        // Add event listeners
        document.getElementById('close-item-discount-modal').addEventListener('click', function() {
            hideModal('itemDiscountModal', 'item-discount-modal-content');
        });

        document.getElementById('item-discount-value').addEventListener('input', updateItemDiscountPreview);
        document.getElementById('item-discount-type').addEventListener('change', updateItemDiscountPreview);

        document.getElementById('apply-item-discount').addEventListener('click', function() {
            const discount = parseFloat(document.getElementById('item-discount-value').value) || 0;
            const discountType = document.getElementById('item-discount-type').value;

            if (applyItemDiscount(currentItemDiscountIndex, discount, discountType)) {
                hideModal('itemDiscountModal', 'item-discount-modal-content');
            }
        });

        document.getElementById('remove-item-discount').addEventListener('click', function() {
            applyItemDiscount(currentItemDiscountIndex, 0, 'fixed');
            hideModal('itemDiscountModal', 'item-discount-modal-content');
        });
    }

    // Update modal content
    document.getElementById('discount-item-name').textContent = item.name;
    document.getElementById('discount-item-price').textContent = `${item.price.toFixed(2)} ج.م`;
    document.getElementById('item-discount-value').value = item.discount;
    document.getElementById('item-discount-type').value = item.discount_type;

    // Update preview
    updateItemDiscountPreview();

    // Show modal
    showModal('itemDiscountModal', 'item-discount-modal-content');
}

// Update item discount preview
function updateItemDiscountPreview() {
    const item = cartItems[currentItemDiscountIndex];
    const discount = parseFloat(document.getElementById('item-discount-value').value) || 0;
    const discountType = document.getElementById('item-discount-type').value;

    let finalPrice = item.price;
    if (discount > 0) {
        if (discountType === 'percentage') {
            finalPrice = item.price * (1 - (discount / 100));
        } else {
            finalPrice = item.price - discount;
        }
    }

    // Ensure price doesn't go below zero
    finalPrice = Math.max(0, finalPrice);

    document.getElementById('discount-item-final-price').textContent = `${finalPrice.toFixed(2)} ج.م`;
}
