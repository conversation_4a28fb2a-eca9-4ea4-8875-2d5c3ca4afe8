{% extends "base.html" %}

{% block title %}تفاصيل الإغلاق اليومي{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">تفاصيل الإغلاق اليومي</h3>
                    <div class="card-tools">
                        {% if closure.status == 'pending' %}
                            <a href="{{ url_for('cash.complete_closure', closure_id=closure.id) }}" class="btn btn-success">
                                <i class="fas fa-check"></i> إكمال الإغلاق
                            </a>
                        {% endif %}
                        <a href="{{ url_for('cash.daily_closure') }}" class="btn btn-secondary">
                            <i class="fas fa-arrow-right"></i> العودة
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <table class="table table-bordered">
                                <tr>
                                    <th>الخزينة</th>
                                    <td>{{ closure.cash_register_name }}</td>
                                </tr>
                                <tr>
                                    <th>المستخدم</th>
                                    <td>{{ closure.user_name }}</td>
                                </tr>
                                <tr>
                                    <th>تاريخ الإغلاق</th>
                                    <td>{{ closure.closure_date }}</td>
                                </tr>
                                <tr>
                                    <th>الحالة</th>
                                    <td>
                                        {% if closure.status == 'pending' %}
                                            <span class="badge badge-warning">مفتوح</span>
                                        {% elif closure.status == 'completed' %}
                                            <span class="badge badge-success">مكتمل</span>
                                        {% else %}
                                            <span class="badge badge-danger">ملغي</span>
                                        {% endif %}
                                    </td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-6">
                            <table class="table table-bordered">
                                <tr>
                                    <th>الرصيد الافتتاحي</th>
                                    <td>{{ closure.opening_balance }}</td>
                                </tr>
                                <tr>
                                    <th>إجمالي المبيعات</th>
                                    <td>{{ closure.total_sales }}</td>
                                </tr>
                                <tr>
                                    <th>إجمالي المشتريات</th>
                                    <td>{{ closure.total_purchases }}</td>
                                </tr>
                                <tr>
                                    <th>إجمالي الإيداعات</th>
                                    <td>{{ closure.total_deposits }}</td>
                                </tr>
                                <tr>
                                    <th>إجمالي السحوبات</th>
                                    <td>{{ closure.total_withdrawals }}</td>
                                </tr>
                                <tr>
                                    <th>الرصيد المتوقع</th>
                                    <td>{{ closure.expected_balance }}</td>
                                </tr>
                                {% if closure.status == 'completed' %}
                                    <tr>
                                        <th>الرصيد الختامي</th>
                                        <td>{{ closure.closing_balance }}</td>
                                    </tr>
                                    <tr>
                                        <th>الفرق</th>
                                        <td class="{{ 'text-danger' if closure.difference < 0 else 'text-success' }}">
                                            {{ closure.difference }}
                                        </td>
                                    </tr>
                                {% endif %}
                            </table>
                        </div>
                    </div>

                    {% if closure.notes %}
                        <div class="row mt-3">
                            <div class="col-12">
                                <div class="card">
                                    <div class="card-header">
                                        <h4 class="card-title">ملاحظات</h4>
                                    </div>
                                    <div class="card-body">
                                        {{ closure.notes }}
                                    </div>
                                </div>
                            </div>
                        </div>
                    {% endif %}

                    <div class="row mt-3">
                        <div class="col-12">
                            <div class="card">
                                <div class="card-header">
                                    <h4 class="card-title">المعاملات</h4>
                                </div>
                                <div class="card-body">
                                    <div class="table-responsive">
                                        <table class="table table-bordered table-striped">
                                            <thead>
                                                <tr>
                                                    <th>التاريخ</th>
                                                    <th>نوع العملية</th>
                                                    <th>المبلغ</th>
                                                    <th>الرصيد السابق</th>
                                                    <th>الرصيد الجديد</th>
                                                    <th>المرجع</th>
                                                    <th>ملاحظات</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                {% for transaction in transactions %}
                                                    <tr>
                                                        <td>{{ transaction.created_at }}</td>
                                                        <td>
                                                            {% if transaction.transaction_type == 'deposit' %}
                                                                <span class="badge badge-success">إيداع</span>
                                                            {% elif transaction.transaction_type == 'withdraw' %}
                                                                <span class="badge badge-danger">سحب</span>
                                                            {% else %}
                                                                <span class="badge badge-info">تحويل</span>
                                                            {% endif %}
                                                        </td>
                                                        <td>{{ transaction.amount }}</td>
                                                        <td>{{ transaction.previous_balance }}</td>
                                                        <td>{{ transaction.new_balance }}</td>
                                                        <td>{{ transaction.reference or '-' }}</td>
                                                        <td>{{ transaction.notes or '-' }}</td>
                                                    </tr>
                                                {% else %}
                                                    <tr>
                                                        <td colspan="7" class="text-center">لا توجد معاملات</td>
                                                    </tr>
                                                {% endfor %}
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %} 