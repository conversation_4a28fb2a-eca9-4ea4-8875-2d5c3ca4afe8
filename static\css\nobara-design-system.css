/**
 * Nobara Design System - نظام التصميم نوبارا
 * Professional POS System Design
 * Supports Arabic & English with Dark/Light modes
 * Color Scheme: Red & Blue Professional Palette
 * Version: 2.0.0
 * Developer: ENG/ Fouad Saber
 */

/* ===== FONT IMPORTS ===== */
@import url('https://fonts.googleapis.com/css2?family=Cairo:wght@200;300;400;500;600;700;800;900&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@100;200;300;400;500;600;700;800;900&display=swap');
@import url('https://cdn.jsdelivr.net/npm/remixicon@3.5.0/fonts/remixicon.css');

/* ===== ROOT VARIABLES ===== */
:root {
  /* Primary Colors - الألوان الأساسية */
  --primary-red: #dc2626;
  --primary-red-light: #ef4444;
  --primary-red-dark: #b91c1c;
  --primary-red-50: #fef2f2;
  --primary-red-100: #fee2e2;
  --primary-red-200: #fecaca;
  --primary-red-500: #ef4444;
  --primary-red-600: #dc2626;
  --primary-red-700: #b91c1c;
  --primary-red-800: #991b1b;
  --primary-red-900: #7f1d1d;

  --primary-blue: #2563eb;
  --primary-blue-light: #3b82f6;
  --primary-blue-dark: #1d4ed8;
  --primary-blue-50: #eff6ff;
  --primary-blue-100: #dbeafe;
  --primary-blue-200: #bfdbfe;
  --primary-blue-500: #3b82f6;
  --primary-blue-600: #2563eb;
  --primary-blue-700: #1d4ed8;
  --primary-blue-800: #1e40af;
  --primary-blue-900: #1e3a8a;

  /* Gradient Colors */
  --gradient-primary: linear-gradient(135deg, var(--primary-blue-600) 0%, var(--primary-red-600) 100%);
  --gradient-secondary: linear-gradient(135deg, var(--primary-blue-500) 0%, var(--primary-blue-700) 100%);
  --gradient-accent: linear-gradient(135deg, var(--primary-red-500) 0%, var(--primary-red-700) 100%);

  /* Light Mode Colors */
  --bg-primary: #ffffff;
  --bg-secondary: #f8fafc;
  --bg-tertiary: #f1f5f9;
  --bg-card: #ffffff;
  --bg-hover: #f8fafc;
  --bg-active: #e2e8f0;

  --text-primary: #0f172a;
  --text-secondary: #475569;
  --text-tertiary: #64748b;
  --text-muted: #94a3b8;
  --text-inverse: #ffffff;

  --border-primary: #e2e8f0;
  --border-secondary: #cbd5e1;
  --border-focus: var(--primary-blue-500);

  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);

  /* Typography */
  --font-family-primary: 'Cairo', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  --font-family-english: 'Inter', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  --font-family-mono: 'Fira Code', 'Consolas', 'Monaco', monospace;

  /* Spacing */
  --spacing-xs: 0.25rem;
  --spacing-sm: 0.5rem;
  --spacing-md: 1rem;
  --spacing-lg: 1.5rem;
  --spacing-xl: 2rem;
  --spacing-2xl: 3rem;

  /* Border Radius */
  --radius-sm: 0.375rem;
  --radius-md: 0.5rem;
  --radius-lg: 0.75rem;
  --radius-xl: 1rem;
  --radius-2xl: 1.5rem;

  /* Transitions */
  --transition-fast: 150ms ease-in-out;
  --transition-normal: 250ms ease-in-out;
  --transition-slow: 350ms ease-in-out;

  /* Z-Index Scale */
  --z-dropdown: 1000;
  --z-sticky: 1020;
  --z-fixed: 1030;
  --z-modal-backdrop: 1040;
  --z-modal: 1050;
  --z-popover: 1060;
  --z-tooltip: 1070;
  --z-toast: 1080;
}

/* ===== DARK MODE VARIABLES ===== */
[data-theme="dark"] {
  --bg-primary: #0f172a;
  --bg-secondary: #1e293b;
  --bg-tertiary: #334155;
  --bg-card: #1e293b;
  --bg-hover: #334155;
  --bg-active: #475569;

  --text-primary: #f8fafc;
  --text-secondary: #e2e8f0;
  --text-tertiary: #cbd5e1;
  --text-muted: #94a3b8;
  --text-inverse: #0f172a;

  --border-primary: #334155;
  --border-secondary: #475569;

  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.3);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.3), 0 2px 4px -1px rgba(0, 0, 0, 0.2);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.3), 0 4px 6px -2px rgba(0, 0, 0, 0.2);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.4), 0 10px 10px -5px rgba(0, 0, 0, 0.3);
}

/* ===== BASE STYLES ===== */
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

html {
  font-size: 16px;
  scroll-behavior: smooth;
}

body {
  font-family: var(--font-family-primary);
  background-color: var(--bg-primary);
  color: var(--text-primary);
  line-height: 1.6;
  transition: background-color var(--transition-normal), color var(--transition-normal);
  direction: rtl;
  text-align: right;
}

[lang="en"] {
  font-family: var(--font-family-english);
  direction: ltr;
  text-align: left;
}

/* ===== TYPOGRAPHY ===== */
h1, h2, h3, h4, h5, h6 {
  font-weight: 700;
  line-height: 1.2;
  margin-bottom: var(--spacing-md);
  color: var(--text-primary);
}

h1 { font-size: 2.5rem; }
h2 { font-size: 2rem; }
h3 { font-size: 1.75rem; }
h4 { font-size: 1.5rem; }
h5 { font-size: 1.25rem; }
h6 { font-size: 1.125rem; }

p {
  margin-bottom: var(--spacing-md);
  color: var(--text-secondary);
}

a {
  color: var(--primary-blue-600);
  text-decoration: none;
  transition: color var(--transition-fast);
}

a:hover {
  color: var(--primary-blue-700);
}

/* ===== UTILITY CLASSES ===== */
.text-primary { color: var(--text-primary) !important; }
.text-secondary { color: var(--text-secondary) !important; }
.text-tertiary { color: var(--text-tertiary) !important; }
.text-muted { color: var(--text-muted) !important; }
.text-inverse { color: var(--text-inverse) !important; }

.text-red { color: var(--primary-red-600) !important; }
.text-blue { color: var(--primary-blue-600) !important; }

.bg-primary { background-color: var(--bg-primary) !important; }
.bg-secondary { background-color: var(--bg-secondary) !important; }
.bg-tertiary { background-color: var(--bg-tertiary) !important; }
.bg-card { background-color: var(--bg-card) !important; }

.bg-red { background-color: var(--primary-red-600) !important; }
.bg-blue { background-color: var(--primary-blue-600) !important; }
.bg-gradient-primary { background: var(--gradient-primary) !important; }
.bg-gradient-secondary { background: var(--gradient-secondary) !important; }
.bg-gradient-accent { background: var(--gradient-accent) !important; }

.border-primary { border-color: var(--border-primary) !important; }
.border-secondary { border-color: var(--border-secondary) !important; }
.border-red { border-color: var(--primary-red-600) !important; }
.border-blue { border-color: var(--primary-blue-600) !important; }

.shadow-sm { box-shadow: var(--shadow-sm) !important; }
.shadow-md { box-shadow: var(--shadow-md) !important; }
.shadow-lg { box-shadow: var(--shadow-lg) !important; }
.shadow-xl { box-shadow: var(--shadow-xl) !important; }

.rounded-sm { border-radius: var(--radius-sm) !important; }
.rounded-md { border-radius: var(--radius-md) !important; }
.rounded-lg { border-radius: var(--radius-lg) !important; }
.rounded-xl { border-radius: var(--radius-xl) !important; }
.rounded-2xl { border-radius: var(--radius-2xl) !important; }

.transition-fast { transition: all var(--transition-fast) !important; }
.transition-normal { transition: all var(--transition-normal) !important; }
.transition-slow { transition: all var(--transition-slow) !important; }

/* ===== RESPONSIVE UTILITIES ===== */
.container {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--spacing-md);
}

.container-fluid {
  width: 100%;
  padding: 0 var(--spacing-md);
}

/* Grid System */
.grid {
  display: grid;
  gap: var(--spacing-md);
}

.grid-cols-1 { grid-template-columns: repeat(1, 1fr); }
.grid-cols-2 { grid-template-columns: repeat(2, 1fr); }
.grid-cols-3 { grid-template-columns: repeat(3, 1fr); }
.grid-cols-4 { grid-template-columns: repeat(4, 1fr); }
.grid-cols-6 { grid-template-columns: repeat(6, 1fr); }
.grid-cols-12 { grid-template-columns: repeat(12, 1fr); }

/* Flexbox Utilities */
.flex { display: flex; }
.flex-col { flex-direction: column; }
.flex-row { flex-direction: row; }
.flex-wrap { flex-wrap: wrap; }
.flex-nowrap { flex-wrap: nowrap; }

.justify-start { justify-content: flex-start; }
.justify-center { justify-content: center; }
.justify-end { justify-content: flex-end; }
.justify-between { justify-content: space-between; }
.justify-around { justify-content: space-around; }

.items-start { align-items: flex-start; }
.items-center { align-items: center; }
.items-end { align-items: flex-end; }
.items-stretch { align-items: stretch; }

.flex-1 { flex: 1; }
.flex-auto { flex: auto; }
.flex-none { flex: none; }

/* Spacing Utilities */
.m-0 { margin: 0 !important; }
.m-1 { margin: var(--spacing-xs) !important; }
.m-2 { margin: var(--spacing-sm) !important; }
.m-3 { margin: var(--spacing-md) !important; }
.m-4 { margin: var(--spacing-lg) !important; }
.m-5 { margin: var(--spacing-xl) !important; }
.m-6 { margin: var(--spacing-2xl) !important; }

.p-0 { padding: 0 !important; }
.p-1 { padding: var(--spacing-xs) !important; }
.p-2 { padding: var(--spacing-sm) !important; }
.p-3 { padding: var(--spacing-md) !important; }
.p-4 { padding: var(--spacing-lg) !important; }
.p-5 { padding: var(--spacing-xl) !important; }
.p-6 { padding: var(--spacing-2xl) !important; }

/* Display Utilities */
.hidden { display: none !important; }
.block { display: block !important; }
.inline { display: inline !important; }
.inline-block { display: inline-block !important; }

/* Position Utilities */
.relative { position: relative !important; }
.absolute { position: absolute !important; }
.fixed { position: fixed !important; }
.sticky { position: sticky !important; }

/* Width & Height Utilities */
.w-full { width: 100% !important; }
.w-auto { width: auto !important; }
.h-full { height: 100% !important; }
.h-auto { height: auto !important; }
.h-screen { height: 100vh !important; }

/* Text Alignment */
.text-left { text-align: left !important; }
.text-center { text-align: center !important; }
.text-right { text-align: right !important; }
.text-justify { text-align: justify !important; }

/* Font Weight */
.font-thin { font-weight: 100 !important; }
.font-light { font-weight: 300 !important; }
.font-normal { font-weight: 400 !important; }
.font-medium { font-weight: 500 !important; }
.font-semibold { font-weight: 600 !important; }
.font-bold { font-weight: 700 !important; }
.font-extrabold { font-weight: 800 !important; }
.font-black { font-weight: 900 !important; }

/* ===== RESPONSIVE BREAKPOINTS ===== */
@media (min-width: 640px) {
  .sm\:grid-cols-1 { grid-template-columns: repeat(1, 1fr); }
  .sm\:grid-cols-2 { grid-template-columns: repeat(2, 1fr); }
  .sm\:grid-cols-3 { grid-template-columns: repeat(3, 1fr); }
  .sm\:grid-cols-4 { grid-template-columns: repeat(4, 1fr); }
  .sm\:flex-row { flex-direction: row; }
  .sm\:block { display: block; }
  .sm\:hidden { display: none; }
}

@media (min-width: 768px) {
  .md\:grid-cols-1 { grid-template-columns: repeat(1, 1fr); }
  .md\:grid-cols-2 { grid-template-columns: repeat(2, 1fr); }
  .md\:grid-cols-3 { grid-template-columns: repeat(3, 1fr); }
  .md\:grid-cols-4 { grid-template-columns: repeat(4, 1fr); }
  .md\:grid-cols-6 { grid-template-columns: repeat(6, 1fr); }
  .md\:flex-row { flex-direction: row; }
  .md\:block { display: block; }
  .md\:hidden { display: none; }
}

@media (min-width: 1024px) {
  .lg\:grid-cols-1 { grid-template-columns: repeat(1, 1fr); }
  .lg\:grid-cols-2 { grid-template-columns: repeat(2, 1fr); }
  .lg\:grid-cols-3 { grid-template-columns: repeat(3, 1fr); }
  .lg\:grid-cols-4 { grid-template-columns: repeat(4, 1fr); }
  .lg\:grid-cols-6 { grid-template-columns: repeat(6, 1fr); }
  .lg\:flex-row { flex-direction: row; }
  .lg\:block { display: block; }
  .lg\:hidden { display: none; }
}

@media (min-width: 1280px) {
  .xl\:grid-cols-1 { grid-template-columns: repeat(1, 1fr); }
  .xl\:grid-cols-2 { grid-template-columns: repeat(2, 1fr); }
  .xl\:grid-cols-3 { grid-template-columns: repeat(3, 1fr); }
  .xl\:grid-cols-4 { grid-template-columns: repeat(4, 1fr); }
  .xl\:grid-cols-6 { grid-template-columns: repeat(6, 1fr); }
  .xl\:flex-row { flex-direction: row; }
  .xl\:block { display: block; }
  .xl\:hidden { display: none; }
}

/* ===== COMPONENT STYLES ===== */

/* Buttons */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0.75rem 1.5rem;
  font-size: 0.875rem;
  font-weight: 500;
  line-height: 1;
  border: 1px solid transparent;
  border-radius: var(--radius-md);
  text-decoration: none;
  cursor: pointer;
  transition: var(--transition-fast);
  user-select: none;
  white-space: nowrap;
}

.btn:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* Button Variants */
.btn-primary {
  background: var(--gradient-primary);
  color: white;
  border-color: var(--primary-blue-600);
}

.btn-primary:hover:not(:disabled) {
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.btn-secondary {
  background-color: var(--bg-secondary);
  color: var(--text-primary);
  border-color: var(--border-primary);
}

.btn-secondary:hover:not(:disabled) {
  background-color: var(--bg-hover);
  border-color: var(--border-secondary);
}

.btn-danger {
  background: var(--gradient-accent);
  color: white;
  border-color: var(--primary-red-600);
}

.btn-danger:hover:not(:disabled) {
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.btn-success {
  background-color: #10b981;
  color: white;
  border-color: #059669;
}

.btn-success:hover:not(:disabled) {
  background-color: #059669;
}

.btn-warning {
  background-color: #f59e0b;
  color: white;
  border-color: #d97706;
}

.btn-warning:hover:not(:disabled) {
  background-color: #d97706;
}

.btn-info {
  background-color: var(--primary-blue-500);
  color: white;
  border-color: var(--primary-blue-600);
}

.btn-info:hover:not(:disabled) {
  background-color: var(--primary-blue-600);
}

.btn-light {
  background-color: #f8fafc;
  color: #374151;
  border-color: #e5e7eb;
}

.btn-light:hover:not(:disabled) {
  background-color: #f1f5f9;
}

.btn-dark {
  background-color: #374151;
  color: white;
  border-color: #374151;
}

.btn-dark:hover:not(:disabled) {
  background-color: #1f2937;
}

/* Button Sizes */
.btn-xs {
  padding: 0.25rem 0.5rem;
  font-size: 0.75rem;
}

.btn-sm {
  padding: 0.5rem 1rem;
  font-size: 0.875rem;
}

.btn-lg {
  padding: 1rem 2rem;
  font-size: 1rem;
}

.btn-xl {
  padding: 1.25rem 2.5rem;
  font-size: 1.125rem;
}

/* Button Shapes */
.btn-rounded {
  border-radius: 9999px;
}

.btn-square {
  border-radius: 0;
}

/* Icon Buttons */
.btn-icon {
  width: 2.5rem;
  height: 2.5rem;
  padding: 0;
  border-radius: 50%;
}

.btn-icon-sm {
  width: 2rem;
  height: 2rem;
}

.btn-icon-lg {
  width: 3rem;
  height: 3rem;
}

/* Cards */
.card {
  background-color: var(--bg-card);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-sm);
  transition: var(--transition-normal);
  overflow: hidden;
}

.card:hover {
  box-shadow: var(--shadow-md);
  transform: translateY(-2px);
}

.card-header {
  padding: 1.5rem;
  border-bottom: 1px solid var(--border-primary);
  background-color: var(--bg-secondary);
}

.card-body {
  padding: 1.5rem;
}

.card-footer {
  padding: 1rem 1.5rem;
  border-top: 1px solid var(--border-primary);
  background-color: var(--bg-secondary);
}

.card-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 0.5rem;
}

.card-subtitle {
  font-size: 0.875rem;
  color: var(--text-secondary);
  margin-bottom: 1rem;
}

/* Forms */
.form-group {
  margin-bottom: 1.5rem;
}

.form-label {
  display: block;
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--text-primary);
  margin-bottom: 0.5rem;
}

.form-label.required::after {
  content: ' *';
  color: var(--primary-red-500);
}

.form-control {
  display: block;
  width: 100%;
  padding: 0.75rem;
  font-size: 0.875rem;
  line-height: 1.5;
  color: var(--text-primary);
  background-color: var(--bg-primary);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-md);
  transition: var(--transition-fast);
}

.form-control:focus {
  outline: none;
  border-color: var(--border-focus);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.form-control:disabled {
  background-color: var(--bg-tertiary);
  opacity: 0.6;
  cursor: not-allowed;
}

.form-control.is-invalid {
  border-color: var(--primary-red-500);
}

.form-control.is-valid {
  border-color: #10b981;
}

.form-select {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
  background-position: left 0.75rem center;
  background-repeat: no-repeat;
  background-size: 1rem;
  padding-left: 2.5rem;
}

[dir="ltr"] .form-select {
  background-position: right 0.75rem center;
  padding-left: 0.75rem;
  padding-right: 2.5rem;
}

.form-check {
  display: flex;
  align-items: center;
  margin-bottom: 0.5rem;
}

.form-check-input {
  width: 1rem;
  height: 1rem;
  margin-left: 0.5rem;
  accent-color: var(--primary-blue-500);
}

[dir="ltr"] .form-check-input {
  margin-left: 0;
  margin-right: 0.5rem;
}

.form-check-label {
  font-size: 0.875rem;
  color: var(--text-primary);
  cursor: pointer;
}

.form-text {
  font-size: 0.75rem;
  color: var(--text-muted);
  margin-top: 0.25rem;
}

.invalid-feedback {
  font-size: 0.75rem;
  color: var(--primary-red-500);
  margin-top: 0.25rem;
}

.valid-feedback {
  font-size: 0.75rem;
  color: #10b981;
  margin-top: 0.25rem;
}
