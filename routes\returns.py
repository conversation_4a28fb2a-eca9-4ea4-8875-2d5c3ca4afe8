from flask import Blueprint, render_template, request, jsonify, redirect, url_for, flash
from flask_login import login_required, current_user
from models import db, ReturnOrder, ReturnItem, Order, OrderItem, Product, Customer, User, Warehouse, Inventory
from datetime import datetime, timedelta
from sqlalchemy import func, desc
import json
import random

returns_blueprint = Blueprint('returns', __name__)

@returns_blueprint.route('/returns')
@login_required
def index():
    """عرض صفحة المرتجعات الرئيسية"""
    # الحصول على معايير البحث
    search = request.args.get('search', '')
    customer_id = request.args.get('customer_id', '')
    user_id = request.args.get('user_id', '')
    status = request.args.get('status', '')
    payment_method = request.args.get('payment_method', '')
    warehouse_id = request.args.get('warehouse_id', '')
    date_from = request.args.get('date_from', '')
    date_to = request.args.get('date_to', '')
    page = request.args.get('page', 1, type=int)

    # إنشاء استعلام قاعدة البيانات
    query = ReturnOrder.query

    # تطبيق معايير البحث
    if search:
        query = query.join(Order).filter(
            (ReturnOrder.reference_number.ilike(f'%{search}%')) |
            (Order.invoice_number.ilike(f'%{search}%'))
        )

    if customer_id:
        query = query.join(Order).filter(Order.customer_id == customer_id)

    if user_id:
        query = query.filter(ReturnOrder.user_id == user_id)

    if status:
        query = query.filter(ReturnOrder.status == status)

    if payment_method:
        query = query.filter(ReturnOrder.payment_method == payment_method)

    if warehouse_id and warehouse_id.isdigit():
        query = query.join(Order).filter(Order.warehouse_id == int(warehouse_id))

    # تطبيق فلتر التاريخ
    if date_from:
        try:
            date_from = datetime.strptime(date_from, '%Y-%m-%d')
            query = query.filter(ReturnOrder.created_at >= date_from)
        except:
            pass

    if date_to:
        try:
            date_to = datetime.strptime(date_to, '%Y-%m-%d')
            date_to = date_to + timedelta(days=1)  # لتضمين اليوم المحدد كاملاً
            query = query.filter(ReturnOrder.created_at < date_to)
        except:
            pass

    # ترتيب النتائج
    query = query.order_by(ReturnOrder.created_at.desc())

    # تقسيم الصفحات
    returns = query.paginate(page=page, per_page=20, error_out=False)

    # الحصول على قائمة العملاء والمستخدمين والمخازن للفلتر
    customers = Customer.query.order_by(Customer.name).all()
    users = User.query.order_by(User.username).all()
    warehouses = Warehouse.query.filter_by(is_active=True).order_by(Warehouse.name).all()

    # إحصائيات المرتجعات
    stats = get_returns_stats()

    return render_template(
        'sales/returns.html',
        returns=returns,
        customers=customers,
        users=users,
        warehouses=warehouses,
        stats=stats,
        search=search,
        customer_id=customer_id,
        user_id=user_id,
        status=status,
        payment_method=payment_method,
        warehouse_id=warehouse_id,
        date_from=date_from.strftime('%Y-%m-%d') if isinstance(date_from, datetime) else '',
        date_to=date_to.strftime('%Y-%m-%d') if isinstance(date_to, datetime) and date_to > timedelta(days=1) else ''
    )

@returns_blueprint.route('/returns/<int:id>/details')
@login_required
def details(id):
    """عرض تفاصيل المرتجع"""
    return_order = ReturnOrder.query.get_or_404(id)

    # الحصول على تفاصيل الفاتورة الأصلية
    order = Order.query.get(return_order.order_id)

    return render_template(
        'sales/return_details.html',
        return_order=return_order,
        order=order
    )

@returns_blueprint.route('/returns/print/<int:id>')
@login_required
def print_return(id):
    """طباعة إيصال المرتجع"""
    return_order = ReturnOrder.query.get_or_404(id)

    # الحصول على تفاصيل الفاتورة الأصلية
    order = Order.query.get(return_order.order_id)

    return render_template(
        'sales/print_return.html',
        return_order=return_order,
        order=order
    )

@returns_blueprint.route('/returns/print_invoice/<int:id>')
@login_required
def print_return_invoice(id):
    """طباعة فاتورة المرتجع"""
    return_order = ReturnOrder.query.get_or_404(id)

    # الحصول على تفاصيل الفاتورة الأصلية
    order = Order.query.get(return_order.order_id)

    return render_template(
        'sales/print_return_invoice.html',
        return_order=return_order,
        order=order
    )

@returns_blueprint.route('/api/returns/reasons')
@login_required
def api_return_reasons():
    """الحصول على قائمة أسباب الإرجاع المتاحة"""
    try:
        # قائمة أسباب الإرجاع المتاحة
        reasons = [
            {'id': 'defective', 'name': 'منتج معيب'},
            {'id': 'wrong_item', 'name': 'منتج خاطئ'},
            {'id': 'customer_dissatisfaction', 'name': 'عدم رضا العميل'},
            {'id': 'size_color_issue', 'name': 'مشكلة في المقاس أو اللون'},
            {'id': 'expired', 'name': 'منتج منتهي الصلاحية'},
            {'id': 'damaged', 'name': 'منتج تالف'},
            {'id': 'other', 'name': 'سبب آخر'}
        ]

        return jsonify({
            'success': True,
            'reasons': reasons
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'حدث خطأ أثناء جلب أسباب الإرجاع: {str(e)}'
        }), 500

@returns_blueprint.route('/api/returns/stats')
@login_required
def api_returns_stats():
    """الحصول على إحصائيات المرتجعات"""
    stats = get_returns_stats()

    return jsonify({
        'success': True,
        'stats': stats
    })

@returns_blueprint.route('/api/returns')
@login_required
def api_returns_list():
    """الحصول على قائمة المرتجعات"""
    try:
        # الحصول على معايير البحث
        search = request.args.get('search', '')
        customer_id = request.args.get('customer_id', '')
        user_id = request.args.get('user_id', '')
        status = request.args.get('status', '')
        payment_method = request.args.get('payment_method', '')
        warehouse_id = request.args.get('warehouse_id', '')
        date_from = request.args.get('date_from', '')
        date_to = request.args.get('date_to', '')
        limit = request.args.get('limit', 20, type=int)

        # إنشاء استعلام قاعدة البيانات
        query = ReturnOrder.query

        # تطبيق معايير البحث
        if search:
            query = query.join(Order).filter(
                (ReturnOrder.reference_number.ilike(f'%{search}%')) |
                (Order.invoice_number.ilike(f'%{search}%'))
            )

        if customer_id:
            query = query.join(Order).filter(Order.customer_id == customer_id)

        if user_id:
            query = query.filter(ReturnOrder.user_id == user_id)

        if status:
            query = query.filter(ReturnOrder.status == status)

        if payment_method:
            query = query.filter(ReturnOrder.payment_method == payment_method)

        if warehouse_id and warehouse_id.isdigit():
            query = query.join(Order).filter(Order.warehouse_id == int(warehouse_id))

        # تطبيق فلتر التاريخ
        if date_from:
            try:
                date_from = datetime.strptime(date_from, '%Y-%m-%d')
                query = query.filter(ReturnOrder.created_at >= date_from)
            except:
                pass

        if date_to:
            try:
                date_to = datetime.strptime(date_to, '%Y-%m-%d')
                date_to = date_to + timedelta(days=1)  # لتضمين اليوم المحدد كاملاً
                query = query.filter(ReturnOrder.created_at < date_to)
            except:
                pass

        # ترتيب النتائج
        query = query.order_by(ReturnOrder.created_at.desc())

        # تحديد عدد النتائج
        returns = query.limit(limit).all()

        # تحضير البيانات
        returns_data = []
        for return_order in returns:
            order = Order.query.get(return_order.order_id)
            customer_name = order.customer.name if order and order.customer else 'عميل نقدي'

            returns_data.append({
                'id': return_order.id,
                'reference_number': return_order.reference_number,
                'created_at': return_order.created_at.strftime('%Y-%m-%d %H:%M:%S'),
                'total_amount': return_order.total_amount,
                'payment_method': return_order.payment_method,
                'status': return_order.status,
                'order_id': return_order.order_id,
                'invoice_number': order.invoice_number if order else None,
                'customer_name': customer_name,
                'user': return_order.user.username if return_order.user else None,
                'items_count': len(return_order.items),
                'warehouse_id': order.warehouse_id if order else None,
                'warehouse_name': order.warehouse.name if order and order.warehouse else 'المخزن الافتراضي'
            })

        return jsonify({
            'success': True,
            'returns': returns_data
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'حدث خطأ أثناء جلب قائمة المرتجعات: {str(e)}'
        }), 500

@returns_blueprint.route('/api/returns/create', methods=['POST'])
@login_required
def api_create_return():
    """إنشاء مرتجع جديد"""
    try:
        data = request.json

        # التحقق من البيانات المطلوبة
        if not data:
            return jsonify({
                'success': False,
                'message': 'لم يتم توفير بيانات المرتجع'
            }), 400

        # الحصول على بيانات المرتجع
        order_id = data.get('order_id')
        payment_method = data.get('payment_method', 'cash')
        notes = data.get('notes', '')
        items = data.get('items', [])

        # التحقق من وجود الطلب
        order = Order.query.get(order_id)
        if not order:
            return jsonify({
                'success': False,
                'message': 'الطلب غير موجود'
            }), 404

        # التحقق من وجود عناصر للإرجاع
        if not items:
            return jsonify({
                'success': False,
                'message': 'لم يتم توفير عناصر للإرجاع'
            }), 400

        # إنشاء رقم مرجعي للمرتجع
        reference_number = f"RET-{datetime.now().strftime('%Y%m%d')}-{random.randint(1000, 9999)}"

        # إنشاء المرتجع
        return_order = ReturnOrder(
            reference_number=reference_number,
            order_id=order_id,
            user_id=current_user.id,
            payment_method=payment_method,
            notes=notes,
            status='completed'
        )

        db.session.add(return_order)
        db.session.flush()  # للحصول على معرف المرتجع

        # إضافة العناصر المرتجعة
        total_amount = 0
        for item_data in items:
            product_id = item_data.get('product_id')
            order_item_id = item_data.get('order_item_id')
            quantity = item_data.get('quantity', 1)
            price = item_data.get('price', 0)
            reason = item_data.get('reason', '')

            # التحقق من وجود المنتج
            product = Product.query.get(product_id)
            if not product:
                continue

            # التحقق من وجود عنصر الطلب
            order_item = OrderItem.query.get(order_item_id)
            if not order_item:
                continue

            # التحقق من الكمية المتاحة للإرجاع
            available_quantity = order_item.quantity - order_item.returned_quantity
            if quantity > available_quantity:
                quantity = available_quantity

            if quantity <= 0:
                continue

            # حساب المبلغ الإجمالي للعنصر
            item_total = price * quantity

            # إنشاء عنصر المرتجع
            return_item = ReturnItem(
                return_order_id=return_order.id,
                order_item_id=order_item_id,
                product_id=product_id,
                quantity=quantity,
                price=price,
                total=item_total,
                reason=reason
            )

            db.session.add(return_item)

            # تحديث الكمية المرتجعة في عنصر الطلب
            order_item.returned_quantity += quantity

            # تحديث المخزون في المخزن المناسب
            if order.warehouse_id:
                # إذا كانت الفاتورة مرتبطة بمخزن، أعد المنتج إلى نفس المخزن
                inventory = Inventory.query.filter_by(
                    product_id=product_id,
                    warehouse_id=order.warehouse_id
                ).first()

                if inventory:
                    # تحديث المخزون وتسجيل الحركة
                    inventory.update_quantity(
                        new_quantity=inventory.quantity + quantity,
                        movement_type='add',
                        user_id=current_user.id,
                        reference=return_order.reference_number,
                        notes=f'إرجاع من الفاتورة {order.invoice_number}'
                    )
                else:
                    # إنشاء مخزون جديد إذا لم يكن موجودًا
                    inventory = Inventory(
                        product_id=product_id,
                        warehouse_id=order.warehouse_id,
                        quantity=quantity,
                        minimum_stock=product.minimum_stock
                    )
                    db.session.add(inventory)
            else:
                # إذا لم تكن الفاتورة مرتبطة بمخزن، أضف إلى المخزون العام للمنتج
                product.stock_quantity += quantity

            # إضافة المبلغ الإجمالي
            total_amount += item_total

        # تحديث المبلغ الإجمالي للمرتجع
        return_order.total_amount = total_amount

        # حفظ التغييرات
        db.session.commit()

        return jsonify({
            'success': True,
            'message': 'تم إنشاء المرتجع بنجاح',
            'return_order': {
                'id': return_order.id,
                'reference_number': return_order.reference_number,
                'total_amount': return_order.total_amount
            }
        })
    except Exception as e:
        db.session.rollback()
        return jsonify({
            'success': False,
            'message': f'حدث خطأ أثناء إنشاء المرتجع: {str(e)}'
        }), 500

@returns_blueprint.route('/api/returns/<int:id>/update-status', methods=['POST'])
@login_required
def api_update_return_status(id):
    """تحديث حالة المرتجع"""
    try:
        return_order = ReturnOrder.query.get_or_404(id)

        data = request.json
        if not data:
            return jsonify({
                'success': False,
                'message': 'لم يتم توفير بيانات التحديث'
            }), 400

        status = data.get('status')
        notes = data.get('notes')

        if status:
            # التحقق من صحة الحالة
            valid_statuses = ['completed', 'pending', 'cancelled']
            if status not in valid_statuses:
                return jsonify({
                    'success': False,
                    'message': f'الحالة غير صالحة. الحالات الصالحة هي: {", ".join(valid_statuses)}'
                }), 400

            # تحديث الحالة
            return_order.status = status

        if notes:
            # تحديث الملاحظات
            return_order.notes = notes

        # حفظ التغييرات
        db.session.commit()

        return jsonify({
            'success': True,
            'message': 'تم تحديث حالة المرتجع بنجاح',
            'return_order': {
                'id': return_order.id,
                'reference_number': return_order.reference_number,
                'status': return_order.status,
                'notes': return_order.notes
            }
        })
    except Exception as e:
        db.session.rollback()
        return jsonify({
            'success': False,
            'message': f'حدث خطأ أثناء تحديث حالة المرتجع: {str(e)}'
        }), 500

@returns_blueprint.route('/api/returns/<int:id>')
@login_required
def api_return_details(id):
    """الحصول على تفاصيل المرتجع"""
    try:
        return_order = ReturnOrder.query.get_or_404(id)

        # الحصول على تفاصيل الفاتورة الأصلية
        order = Order.query.get(return_order.order_id)

        # تحضير بيانات المرتجع
        return_data = {
            'id': return_order.id,
            'reference_number': return_order.reference_number,
            'created_at': return_order.created_at.strftime('%Y-%m-%d %H:%M:%S'),
            'total_amount': return_order.total_amount,
            'payment_method': return_order.payment_method,
            'status': return_order.status,
            'notes': return_order.notes,
            'user': return_order.user.username if return_order.user else None,
            'order': {
                'id': order.id if order else None,
                'invoice_number': order.invoice_number if order else None,
                'customer_name': order.customer.name if order and order.customer else 'عميل نقدي',
                'created_at': order.created_at.strftime('%Y-%m-%d %H:%M:%S') if order else None,
                'warehouse_id': order.warehouse_id if order else None,
                'warehouse_name': order.warehouse.name if order and order.warehouse else 'المخزن الافتراضي'
            }
        }

        # تحضير بيانات المنتجات المرتجعة
        items_data = []
        for item in return_order.items:
            product = Product.query.get(item.product_id)
            items_data.append({
                'id': item.id,
                'product_id': item.product_id,
                'product_name': product.name if product else 'منتج غير معروف',
                'quantity': item.quantity,
                'price': item.price,
                'total': item.total,
                'reason': item.reason
            })

        return jsonify({
            'success': True,
            'return_order': return_data,
            'items': items_data
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'حدث خطأ أثناء جلب بيانات المرتجع: {str(e)}'
        }), 500

def get_returns_stats():
    """حساب إحصائيات المرتجعات"""
    # إجمالي المرتجعات
    total_returns = db.session.query(func.sum(ReturnOrder.total_amount)).scalar() or 0
    total_returns_count = ReturnOrder.query.count()

    # مرتجعات اليوم
    today = datetime.utcnow().date()
    today_start = datetime.combine(today, datetime.min.time())
    today_end = datetime.combine(today, datetime.max.time())

    today_returns = db.session.query(func.sum(ReturnOrder.total_amount)).filter(
        ReturnOrder.created_at.between(today_start, today_end)
    ).scalar() or 0

    today_returns_count = ReturnOrder.query.filter(
        ReturnOrder.created_at.between(today_start, today_end)
    ).count()

    # مرتجعات الشهر
    month_start = datetime(today.year, today.month, 1)
    month_end = (datetime(today.year, today.month + 1, 1) if today.month < 12
                else datetime(today.year + 1, 1, 1)) - timedelta(seconds=1)

    month_returns = db.session.query(func.sum(ReturnOrder.total_amount)).filter(
        ReturnOrder.created_at.between(month_start, month_end)
    ).scalar() or 0

    month_returns_count = ReturnOrder.query.filter(
        ReturnOrder.created_at.between(month_start, month_end)
    ).count()

    # المرتجعات حسب طريقة الدفع
    returns_by_payment = {}
    payment_methods = ['cash', 'card', 'store_credit']

    for method in payment_methods:
        amount = db.session.query(func.sum(ReturnOrder.total_amount)).filter(
            ReturnOrder.payment_method == method
        ).scalar() or 0

        returns_by_payment[method] = amount

    # المنتجات الأكثر إرجاعاً
    top_returned_products = db.session.query(
        Product.id,
        Product,
        func.sum(ReturnItem.quantity).label('quantity'),
        func.sum(ReturnItem.total).label('total')
    ).join(ReturnItem).group_by(Product.id).order_by(desc('quantity')).limit(5).all()

    return {
        'total_returns': total_returns,
        'total_returns_count': total_returns_count,
        'today_returns': today_returns,
        'today_returns_count': today_returns_count,
        'month_returns': month_returns,
        'month_returns_count': month_returns_count,
        'returns_by_payment': returns_by_payment,
        'top_returned_products': top_returned_products
    }
