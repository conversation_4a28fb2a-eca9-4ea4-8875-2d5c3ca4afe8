<!DOCTYPE html>
<html lang="ar" dir="rtl" data-theme="light">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>الصفحة غير موجودة - نوبارا</title>
    
    <!-- Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@200;300;400;500;600;700;800;900&display=swap" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/remixicon@3.5.0/fonts/remixicon.css" rel="stylesheet">
    
    <!-- CSS -->
    <link href="{{ url_for('static', filename='css/nobara-design-system.css') }}?v=2.0.0" rel="stylesheet">
    
    <style>
        .error-container {
            min-height: 100vh;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 2rem;
        }
        
        .error-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 2rem;
            padding: 3rem;
            text-align: center;
            max-width: 500px;
            width: 100%;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }
        
        .error-icon {
            font-size: 6rem;
            color: #dc2626;
            margin-bottom: 1rem;
        }
        
        .error-code {
            font-size: 4rem;
            font-weight: 900;
            color: #2563eb;
            margin-bottom: 1rem;
        }
        
        .error-title {
            font-size: 1.5rem;
            font-weight: 700;
            color: #1f2937;
            margin-bottom: 1rem;
        }
        
        .error-description {
            color: #6b7280;
            margin-bottom: 2rem;
            line-height: 1.6;
        }
        
        .btn-home {
            background: linear-gradient(135deg, #2563eb 0%, #dc2626 100%);
            color: white;
            padding: 1rem 2rem;
            border-radius: 1rem;
            text-decoration: none;
            font-weight: 600;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            transition: transform 0.3s ease;
        }
        
        .btn-home:hover {
            transform: translateY(-2px);
            color: white;
        }
    </style>
</head>

<body>
    <div class="error-container">
        <div class="error-card">
            <div class="error-icon">
                <i class="ri-error-warning-line"></i>
            </div>
            
            <div class="error-code">404</div>
            
            <h1 class="error-title">الصفحة غير موجودة</h1>
            
            <p class="error-description">
                عذراً، الصفحة التي تبحث عنها غير موجودة أو تم نقلها إلى مكان آخر.
                يرجى التحقق من الرابط أو العودة إلى الصفحة الرئيسية.
            </p>
            
            <a href="{{ url_for('main.dashboard') }}" class="btn-home">
                <i class="ri-home-line"></i>
                العودة للرئيسية
            </a>
        </div>
    </div>
    
    <!-- Theme Manager -->
    <script src="{{ url_for('static', filename='js/nobara-theme-manager.js') }}?v=2.0.0"></script>
</body>
</html>
