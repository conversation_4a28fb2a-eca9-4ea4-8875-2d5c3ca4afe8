// Setup numpad for received amount input
function setupNumpad() {
    const numpadButtons = document.querySelectorAll('.numpad-btn');
    const receivedInput = document.getElementById('received-amount');

    numpadButtons.forEach(button => {
        button.addEventListener('click', function() {
            const value = this.dataset.value;

            if (value === 'clear') {
                if (receivedInput.value.length > 0) {
                    // Delete last character
                    receivedInput.value = receivedInput.value.slice(0, -1);
                } else {
                    // If already empty, do nothing
                    return;
                }
            } else {
                // Add digit
                receivedInput.value += value;
            }

            // Trigger input event to update change amount
            receivedInput.dispatchEvent(new Event('input'));

            // Add animation effect to the button
            this.classList.add('scale-95', 'bg-gray-100');
            setTimeout(() => {
                this.classList.remove('scale-95', 'bg-gray-100');
            }, 150);
        });
    });

    // Update change amount when received amount changes
    receivedInput.addEventListener('input', function() {
        const total = calculateTotal();
        const received = parseFloat(this.value) || 0;
        const change = received - total;

        if (received === 0) {
            document.getElementById('change-amount').textContent = '0.00 ج.م';
            return;
        }

        if (change >= 0) {
            // If received amount is enough, show change amount in green
            document.getElementById('change-amount').textContent = `${change.toFixed(2)} ج.م`;
            document.getElementById('change-amount').classList.remove('text-red-500');
            document.getElementById('change-amount').classList.add('text-green-600');
        } else {
            // If received amount is not enough, show remaining amount in red
            document.getElementById('change-amount').textContent = `${Math.abs(change).toFixed(2)} ج.م`;
            document.getElementById('change-amount').classList.remove('text-green-600');
            document.getElementById('change-amount').classList.add('text-red-500');
        }
    });

    // Add keyboard support for numpad
    receivedInput.addEventListener('keydown', function(e) {
        // Allow only numbers, backspace, delete, tab, and arrow keys
        if (
            !/^\d$/.test(e.key) && // Not a digit
            e.key !== 'Backspace' &&
            e.key !== 'Delete' &&
            e.key !== 'Tab' &&
            e.key !== 'ArrowLeft' &&
            e.key !== 'ArrowRight' &&
            e.key !== '.' &&
            e.key !== ',' &&
            !e.ctrlKey // Allow Ctrl+C, Ctrl+V, etc.
        ) {
            e.preventDefault();
        }
    });
}

// This file has been updated to use the new customer search functionality
// The old setupCustomerSearch function has been moved to customer_search.js

// Save new customer
function saveNewCustomer() {
    const form = document.getElementById('add-customer-form');
    const formData = new FormData(form);

    // Validate form
    if (!formData.get('name')) {
        showNotification('يرجى إدخال اسم العميل', 'error');
        return;
    }

    // Add customer via API
    fetch('/api/customers/add', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Add customer to select
            const select = document.getElementById('customer-select');
            const option = document.createElement('option');
            option.value = data.customer.id;
            option.textContent = data.customer.name;
            select.appendChild(option);

            // Select the new customer
            selectCustomer(data.customer.id, data.customer.name);

            // Hide modal
            hideModal('addCustomerModal', 'add-customer-modal-content');

            // Reset form
            form.reset();

            // Show notification
            showNotification('تم إضافة العميل بنجاح', 'success');
        } else {
            showNotification(`فشل في إضافة العميل: ${data.message}`, 'error');
        }
    })
    .catch(error => {
        console.error('Error adding customer:', error);
        showNotification('حدث خطأ أثناء إضافة العميل. يرجى المحاولة مرة أخرى.', 'error');
    });
}

// Show modal with animation
function showModal(modalId, contentId) {
    const modal = document.getElementById(modalId);
    const content = document.getElementById(contentId);

    modal.classList.remove('hidden');

    // Trigger animation after a small delay to ensure the display change has taken effect
    setTimeout(() => {
        content.classList.add('scale-100', 'opacity-100');
        content.classList.remove('scale-95', 'opacity-0');
    }, 10);
}

// Hide modal with animation
function hideModal(modalId, contentId) {
    const modal = document.getElementById(modalId);
    const content = document.getElementById(contentId);

    content.classList.remove('scale-100', 'opacity-100');
    content.classList.add('scale-95', 'opacity-0');

    // Hide modal after animation completes
    setTimeout(() => {
        modal.classList.add('hidden');
    }, 300);
}

// Show notification
function showNotification(message, type = 'info') {
    const container = document.getElementById('notification-container');
    const id = 'notification-' + Date.now();

    const notification = document.createElement('div');
    notification.id = id;
    notification.className = `notification notification-${type}`;

    let icon;
    switch (type) {
        case 'success':
            icon = 'ri-check-line';
            break;
        case 'error':
            icon = 'ri-error-warning-line';
            break;
        case 'warning':
            icon = 'ri-alert-line';
            break;
        default:
            icon = 'ri-information-line';
    }

    notification.innerHTML = `
        <div class="mr-3 text-xl">
            <i class="${icon}"></i>
        </div>
        <div class="flex-1">
            <p class="text-sm">${message}</p>
        </div>
        <button class="ml-2 text-gray-500 hover:text-gray-700" onclick="document.getElementById('${id}').remove()">
            <i class="ri-close-line"></i>
        </button>
    `;

    container.appendChild(notification);

    // Remove notification after 5 seconds
    setTimeout(() => {
        if (document.getElementById(id)) {
            document.getElementById(id).remove();
        }
    }, 5000);
}

// Debounce function to limit how often a function can be called
function debounce(func, wait) {
    let timeout;
    return function() {
        const context = this;
        const args = arguments;
        clearTimeout(timeout);
        timeout = setTimeout(() => {
            func.apply(context, args);
        }, wait);
    };
}
