#!/usr/bin/env python3
"""
Nobara POS System - Simple Runner
نظام نوبارا لنقاط البيع - ملف التشغيل المبسط
"""

import os
from flask import Flask
from flask_sqlalchemy import SQLAlchemy
from flask_login import LoginManager

# Initialize extensions
db = SQLAlchemy()
login_manager = LoginManager()

def create_app():
    """Create Flask application"""
    app = Flask(__name__)
    
    # Basic configuration
    app.config['SECRET_KEY'] = 'nobara-pos-secret-key-2024'
    app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///instance/nobara_pos.db'
    app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False
    
    # Initialize extensions
    db.init_app(app)
    login_manager.init_app(app)
    
    # Configure login manager
    login_manager.login_view = 'auth.login'
    login_manager.login_message = 'يرجى تسجيل الدخول للوصول إلى هذه الصفحة'
    
    # Create instance directory
    if not os.path.exists('instance'):
        os.makedirs('instance')
    
    # Simple routes
    @app.route('/')
    def index():
        return '''
        <!DOCTYPE html>
        <html lang="ar" dir="rtl">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>نوبارا - نظام نقاط البيع</title>
            <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@400;600;700&display=swap" rel="stylesheet">
            <style>
                * { margin: 0; padding: 0; box-sizing: border-box; }
                body { 
                    font-family: 'Cairo', sans-serif; 
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    min-height: 100vh;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    color: white;
                }
                .container {
                    text-align: center;
                    background: rgba(255,255,255,0.1);
                    padding: 3rem;
                    border-radius: 2rem;
                    backdrop-filter: blur(10px);
                    box-shadow: 0 20px 40px rgba(0,0,0,0.1);
                }
                h1 { font-size: 3rem; margin-bottom: 1rem; }
                p { font-size: 1.2rem; margin-bottom: 2rem; opacity: 0.9; }
                .btn {
                    display: inline-block;
                    padding: 1rem 2rem;
                    background: linear-gradient(135deg, #2563eb 0%, #dc2626 100%);
                    color: white;
                    text-decoration: none;
                    border-radius: 1rem;
                    font-weight: 600;
                    transition: transform 0.3s ease;
                }
                .btn:hover { transform: translateY(-2px); }
                .version { margin-top: 2rem; font-size: 0.9rem; opacity: 0.7; }
            </style>
        </head>
        <body>
            <div class="container">
                <h1>🏪 نوبارا</h1>
                <p>نظام نقاط البيع الاحترافي</p>
                <a href="/auth/login" class="btn">تسجيل الدخول</a>
                <div class="version">
                    Version 2.0.0<br>
                    Powered By ENG/ Fouad Saber<br>
                    Tel: 01020073527
                </div>
            </div>
        </body>
        </html>
        '''
    
    @app.route('/auth/login')
    def login():
        return '''
        <!DOCTYPE html>
        <html lang="ar" dir="rtl">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>تسجيل الدخول - نوبارا</title>
            <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@400;600;700&display=swap" rel="stylesheet">
            <style>
                * { margin: 0; padding: 0; box-sizing: border-box; }
                body { 
                    font-family: 'Cairo', sans-serif; 
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    min-height: 100vh;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                }
                .login-card {
                    background: rgba(255,255,255,0.95);
                    padding: 3rem;
                    border-radius: 2rem;
                    box-shadow: 0 20px 40px rgba(0,0,0,0.1);
                    width: 100%;
                    max-width: 400px;
                    color: #333;
                }
                .logo { 
                    text-align: center; 
                    margin-bottom: 2rem;
                    font-size: 2rem;
                    color: #2563eb;
                }
                .form-group { margin-bottom: 1.5rem; }
                label { display: block; margin-bottom: 0.5rem; font-weight: 600; }
                input {
                    width: 100%;
                    padding: 1rem;
                    border: 2px solid #e5e7eb;
                    border-radius: 0.75rem;
                    font-size: 1rem;
                    transition: border-color 0.3s ease;
                }
                input:focus {
                    outline: none;
                    border-color: #2563eb;
                }
                .btn {
                    width: 100%;
                    padding: 1rem;
                    background: linear-gradient(135deg, #2563eb 0%, #dc2626 100%);
                    color: white;
                    border: none;
                    border-radius: 0.75rem;
                    font-size: 1rem;
                    font-weight: 600;
                    cursor: pointer;
                    transition: transform 0.3s ease;
                }
                .btn:hover { transform: translateY(-2px); }
                .default-creds {
                    margin-top: 2rem;
                    padding: 1rem;
                    background: #f3f4f6;
                    border-radius: 0.75rem;
                    font-size: 0.9rem;
                    text-align: center;
                }
            </style>
        </head>
        <body>
            <div class="login-card">
                <div class="logo">🏪 نوبارا</div>
                
                <form>
                    <div class="form-group">
                        <label>اسم المستخدم</label>
                        <input type="text" value="admin" readonly>
                    </div>
                    
                    <div class="form-group">
                        <label>كلمة المرور</label>
                        <input type="password" value="admin" readonly>
                    </div>
                    
                    <button type="button" class="btn" onclick="alert('النظام قيد التطوير - سيتم تفعيل تسجيل الدخول قريباً')">
                        تسجيل الدخول
                    </button>
                </form>
                
                <div class="default-creds">
                    <strong>بيانات الدخول الافتراضية:</strong><br>
                    اسم المستخدم: admin<br>
                    كلمة المرور: admin
                </div>
            </div>
        </body>
        </html>
        '''
    
    return app

if __name__ == '__main__':
    app = create_app()
    
    print("""
🏪 ═══════════════════════════════════════════════════════════════
   نوبارا - نظام نقاط البيع الاحترافي
   Nobara Professional POS System
   
   📱 Version: 2.0.0
   👨‍💻 Developer: ENG/ Fouad Saber
   📞 Phone: 01020073527
   📧 Email: <EMAIL>
   
   🌐 Access: http://localhost:5000
   
   ✅ النظام يعمل بنجاح!
═══════════════════════════════════════════════════════════════
    """)
    
    app.run(host='0.0.0.0', port=5000, debug=True)
