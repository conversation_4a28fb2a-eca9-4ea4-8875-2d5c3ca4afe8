<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Nobara - إعدادات طرق الدفع</title>
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <!-- Remixicon -->
    <link href="https://cdn.jsdelivr.net/npm/remixicon@2.5.0/fonts/remixicon.css" rel="stylesheet">
    <!-- Alpine.js -->
    <script defer src="https://cdn.jsdelivr.net/npm/alpinejs@3.x.x/dist/cdn.min.js"></script>
    <script>
        tailwind.config = {
            darkMode: 'class',
            theme: {
                extend: {
                    colors: {
                        primary: {
                            50: '#eff6ff',
                            100: '#dbeafe',
                            200: '#bfdbfe',
                            300: '#93c5fd',
                            400: '#60a5fa',
                            500: '#3b82f6',
                            600: '#2563eb',
                            700: '#1d4ed8',
                            800: '#1e40af',
                            900: '#1e3a8a',
                            950: '#172554',
                        },
                        dark: {
                            100: '#1E293B',
                            200: '#0F172A',
                            300: '#0B1120',
                            400: '#060A15'
                        }
                    }
                }
            }
        }
    </script>
    <style>
        body {
            font-family: 'Cairo', 'Tajawal', sans-serif;
            transition: background-color 0.3s ease, color 0.3s ease;
        }

        .bg-pattern {
            background-color: #f9fafb;
            background-image: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%233B82F6' fill-opacity='0.05'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
        }

        .dark .bg-pattern {
            background-color: #0F172A;
            background-image: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%233B82F6' fill-opacity='0.1'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
        }

        .powered-by {
            position: fixed;
            bottom: 1rem;
            left: 1rem;
            background-color: #ffffff;
            border-radius: 1rem;
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
            padding: 0.75rem 1rem;
            display: flex;
            align-items: center;
            z-index: 40;
            transition: all 0.3s ease;
            border: 1px solid #e5e7eb;
        }

        .dark .powered-by {
            background-color: #1E293B;
            border-color: #2D3748;
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.3), 0 4px 6px -2px rgba(0, 0, 0, 0.2);
        }

        .powered-by:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
        }

        .dark .powered-by:hover {
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.3), 0 10px 10px -5px rgba(0, 0, 0, 0.2);
        }
    </style>
</head>
<body class="bg-pattern dark:bg-dark-200 dark:text-gray-100" x-data="{ darkMode: localStorage.getItem('darkMode') === 'enabled', showModal: false, editMode: false, currentMethod: {} }">
    <div class="flex h-screen overflow-hidden">
        <!-- Sidebar -->
        {% include 'partials/sidebar.html' %}

        <!-- Main Content -->
        <div class="flex-1 flex flex-col overflow-hidden">
            <!-- Top Navigation -->
            {% include 'partials/topnav.html' %}

            <!-- Content Area -->
            <main class="flex-1 overflow-y-auto p-6">
                <div class="mb-6 flex justify-between items-center">
                    <div>
                        <h1 class="text-2xl font-bold text-gray-800 dark:text-white">إعدادات طرق الدفع</h1>
                        <p class="text-gray-600 dark:text-gray-400">إدارة طرق الدفع المتاحة في النظام</p>
                    </div>
                    <div class="flex items-center gap-2">
                        <a href="{{ url_for('settings.index') }}" class="flex items-center gap-1 bg-gray-100 dark:bg-dark-100 hover:bg-gray-200 dark:hover:bg-dark-300 text-gray-700 dark:text-gray-300 py-2 px-4 rounded-lg transition-colors">
                            <i class="ri-arrow-right-line"></i>
                            <span>العودة للإعدادات</span>
                        </a>
                        <button @click="darkMode = !darkMode; localStorage.setItem('darkMode', darkMode ? 'enabled' : 'disabled'); document.documentElement.classList.toggle('dark')" class="p-2 rounded-full bg-gray-100 dark:bg-dark-100 hover:bg-gray-200 dark:hover:bg-dark-300 transition-colors">
                            <i class="ri-moon-line dark:hidden"></i>
                            <i class="ri-sun-line hidden dark:block"></i>
                        </button>
                    </div>
                </div>

                <div class="bg-white dark:bg-dark-100 rounded-xl shadow-sm overflow-hidden mb-6">
                    <div class="p-6">
                        <div class="flex justify-between items-center mb-6">
                            <h3 class="text-lg font-medium text-gray-800 dark:text-white flex items-center">
                                <i class="ri-bank-card-line ml-2 text-primary-500"></i>
                                طرق الدفع المتاحة
                            </h3>
                            <button @click="showModal = true; editMode = false; currentMethod = {}" class="bg-primary-500 hover:bg-primary-600 text-white py-2 px-4 rounded-lg inline-flex items-center transition-colors">
                                <i class="ri-add-line ml-1"></i>
                                <span>إضافة طريقة دفع</span>
                            </button>
                        </div>

                        <div class="overflow-x-auto">
                            <table class="w-full">
                                <thead class="bg-gray-50 dark:bg-dark-300 text-gray-700 dark:text-gray-300">
                                    <tr>
                                        <th class="py-3 px-4 text-right">الاسم</th>
                                        <th class="py-3 px-4 text-right">الرمز</th>
                                        <th class="py-3 px-4 text-right">الوصف</th>
                                        <th class="py-3 px-4 text-right">الحالة</th>
                                        <th class="py-3 px-4 text-right">الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody class="divide-y divide-gray-200 dark:divide-gray-700">
                                    {% for method in payment_methods %}
                                    <tr class="hover:bg-gray-50 dark:hover:bg-dark-300/50">
                                        <td class="py-3 px-4">
                                            <div class="flex items-center">
                                                <div class="w-8 h-8 rounded-full bg-{{ method.color }}-100 dark:bg-{{ method.color }}-900/30 flex items-center justify-center text-{{ method.color }}-600 dark:text-{{ method.color }}-400 ml-2">
                                                    <i class="{{ method.icon }}"></i>
                                                </div>
                                                <span>{{ method.name }}</span>
                                            </div>
                                        </td>
                                        <td class="py-3 px-4">{{ method.code }}</td>
                                        <td class="py-3 px-4">{{ method.description }}</td>
                                        <td class="py-3 px-4">
                                            {% if method.is_active %}
                                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400">
                                                <i class="ri-checkbox-circle-line ml-1"></i>
                                                مفعل
                                            </span>
                                            {% else %}
                                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400">
                                                <i class="ri-close-circle-line ml-1"></i>
                                                معطل
                                            </span>
                                            {% endif %}
                                        </td>
                                        <td class="py-3 px-4">
                                            <div class="flex items-center space-x-2 space-x-reverse">
                                                <button @click="showModal = true; editMode = true; currentMethod = {{ method|tojson }}" class="text-primary-500 hover:text-primary-600">
                                                    <i class="ri-edit-line"></i>
                                                </button>
                                                {% if not method.is_default %}
                                                <button @click="deletePaymentMethod({{ method.id }})" class="text-red-500 hover:text-red-600">
                                                    <i class="ri-delete-bin-line"></i>
                                                </button>
                                                {% endif %}
                                                {% if not method.is_default %}
                                                <button @click="setDefaultPaymentMethod({{ method.id }})" class="text-yellow-500 hover:text-yellow-600" title="تعيين كافتراضي">
                                                    <i class="ri-star-line"></i>
                                                </button>
                                                {% else %}
                                                <span class="text-yellow-500">
                                                    <i class="ri-star-fill"></i>
                                                </span>
                                                {% endif %}
                                            </div>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- معلومات طرق الدفع -->
                <div class="bg-white dark:bg-dark-100 rounded-xl shadow-sm overflow-hidden">
                    <div class="p-6">
                        <h3 class="text-lg font-medium text-gray-800 dark:text-white mb-4 flex items-center">
                            <i class="ri-information-line ml-2 text-primary-500"></i>
                            معلومات طرق الدفع
                        </h3>

                        <div class="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4 border border-blue-100 dark:border-blue-900/30">
                            <p class="text-gray-700 dark:text-gray-300 mb-2">
                                <strong>طرق الدفع الافتراضية:</strong> النظام يوفر ثلاث طرق دفع افتراضية: نقدي، بطاقة ائتمان، وآجل (دين). لا يمكن حذف هذه الطرق ولكن يمكن تعديلها.
                            </p>
                            <p class="text-gray-700 dark:text-gray-300 mb-2">
                                <strong>طريقة الدفع الافتراضية:</strong> هي الطريقة التي يتم اختيارها تلقائيًا عند إنشاء فاتورة جديدة. يمكن تغيير الطريقة الافتراضية بالنقر على أيقونة النجمة.
                            </p>
                            <p class="text-gray-700 dark:text-gray-300">
                                <strong>رمز طريقة الدفع:</strong> هو رمز فريد يستخدم في التقارير والإحصائيات. يجب أن يكون الرمز فريدًا لكل طريقة دفع.
                            </p>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <!-- Modal إضافة/تعديل طريقة دفع -->
    <div x-show="showModal" class="fixed inset-0 z-50 overflow-y-auto" style="display: none;">
        <div class="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
            <div x-show="showModal" @click="showModal = false" x-transition:enter="ease-out duration-300" x-transition:enter-start="opacity-0" x-transition:enter-end="opacity-100" x-transition:leave="ease-in duration-200" x-transition:leave-start="opacity-100" x-transition:leave-end="opacity-0" class="fixed inset-0 transition-opacity">
                <div class="absolute inset-0 bg-gray-500 dark:bg-gray-900 opacity-75"></div>
            </div>

            <span class="hidden sm:inline-block sm:align-middle sm:h-screen">&#8203;</span>

            <div x-show="showModal" x-transition:enter="ease-out duration-300" x-transition:enter-start="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95" x-transition:enter-end="opacity-100 translate-y-0 sm:scale-100" x-transition:leave="ease-in duration-200" x-transition:leave-start="opacity-100 translate-y-0 sm:scale-100" x-transition:leave-end="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95" class="inline-block align-bottom bg-white dark:bg-dark-100 rounded-lg text-right overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
                <form id="paymentMethodForm" @submit.prevent="submitPaymentMethod">
                    <div class="bg-white dark:bg-dark-100 px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                        <div class="mb-4">
                            <h3 class="text-lg font-medium text-gray-900 dark:text-white" x-text="editMode ? 'تعديل طريقة دفع' : 'إضافة طريقة دفع جديدة'"></h3>
                        </div>

                        <div class="grid grid-cols-1 gap-4">
                            <!-- الاسم -->
                            <div>
                                <label for="name" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">الاسم <span class="text-red-500">*</span></label>
                                <input type="text" id="name" name="name" x-model="currentMethod.name" required class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 dark:bg-dark-200 dark:text-gray-100">
                            </div>

                            <!-- الرمز -->
                            <div>
                                <label for="code" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">الرمز <span class="text-red-500">*</span></label>
                                <input type="text" id="code" name="code" x-model="currentMethod.code" required class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 dark:bg-dark-200 dark:text-gray-100">
                                <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">رمز فريد لطريقة الدفع (مثال: CASH, CARD, CREDIT)</p>
                            </div>

                            <!-- الوصف -->
                            <div>
                                <label for="description" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">الوصف</label>
                                <textarea id="description" name="description" x-model="currentMethod.description" rows="2" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 dark:bg-dark-200 dark:text-gray-100"></textarea>
                            </div>

                            <!-- الأيقونة -->
                            <div>
                                <label for="icon" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">الأيقونة</label>
                                <select id="icon" name="icon" x-model="currentMethod.icon" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 dark:bg-dark-200 dark:text-gray-100">
                                    <option value="ri-money-dollar-circle-line">نقود</option>
                                    <option value="ri-bank-card-line">بطاقة ائتمان</option>
                                    <option value="ri-calendar-check-line">آجل</option>
                                    <option value="ri-bank-line">تحويل بنكي</option>
                                    <option value="ri-smartphone-line">محفظة إلكترونية</option>
                                    <option value="ri-qr-code-line">QR Code</option>
                                    <option value="ri-coupon-line">قسيمة</option>
                                </select>
                            </div>

                            <!-- اللون -->
                            <div>
                                <label for="color" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">اللون</label>
                                <select id="color" name="color" x-model="currentMethod.color" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 dark:bg-dark-200 dark:text-gray-100">
                                    <option value="blue">أزرق</option>
                                    <option value="green">أخضر</option>
                                    <option value="red">أحمر</option>
                                    <option value="yellow">أصفر</option>
                                    <option value="purple">بنفسجي</option>
                                    <option value="pink">وردي</option>
                                    <option value="indigo">نيلي</option>
                                    <option value="teal">أزرق مخضر</option>
                                </select>
                            </div>

                            <!-- خصائص المعاملات المالية -->
                            <div class="mt-4 border-t border-gray-200 dark:border-gray-700 pt-4">
                                <h4 class="text-base font-medium text-gray-800 dark:text-white mb-3">خصائص المعاملات المالية</h4>

                                <!-- تصنيف الدفع -->
                                <div class="mb-3">
                                    <label for="payment_category" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">تصنيف الدفع</label>
                                    <select id="payment_category" name="payment_category" x-model="currentMethod.payment_category" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 dark:bg-dark-200 dark:text-gray-100">
                                        <option value="cash">نقدي</option>
                                        <option value="electronic">إلكتروني</option>
                                        <option value="credit">آجل</option>
                                        <option value="other">أخرى</option>
                                    </select>
                                </div>

                                <!-- خصائص إضافية -->
                                <div class="space-y-2">
                                    <label class="flex items-center">
                                        <input type="checkbox" id="affects_cash_register" name="affects_cash_register" x-model="currentMethod.affects_cash_register" class="w-4 h-4 text-primary-600 border-gray-300 rounded focus:ring-primary-500 dark:bg-dark-200 dark:border-gray-600">
                                        <span class="mr-2 text-sm text-gray-700 dark:text-gray-300">يؤثر على الخزينة</span>
                                    </label>

                                    <label class="flex items-center">
                                        <input type="checkbox" id="is_credit" name="is_credit" x-model="currentMethod.is_credit" class="w-4 h-4 text-primary-600 border-gray-300 rounded focus:ring-primary-500 dark:bg-dark-200 dark:border-gray-600">
                                        <span class="mr-2 text-sm text-gray-700 dark:text-gray-300">دفع آجل</span>
                                    </label>

                                    <label class="flex items-center">
                                        <input type="checkbox" id="allow_partial_payment" name="allow_partial_payment" x-model="currentMethod.allow_partial_payment" class="w-4 h-4 text-primary-600 border-gray-300 rounded focus:ring-primary-500 dark:bg-dark-200 dark:border-gray-600">
                                        <span class="mr-2 text-sm text-gray-700 dark:text-gray-300">يسمح بالدفع الجزئي</span>
                                    </label>

                                    <label class="flex items-center">
                                        <input type="checkbox" id="requires_approval" name="requires_approval" x-model="currentMethod.requires_approval" class="w-4 h-4 text-primary-600 border-gray-300 rounded focus:ring-primary-500 dark:bg-dark-200 dark:border-gray-600">
                                        <span class="mr-2 text-sm text-gray-700 dark:text-gray-300">يتطلب موافقة</span>
                                    </label>

                                    <label class="flex items-center">
                                        <input type="checkbox" id="requires_reference" name="requires_reference" x-model="currentMethod.requires_reference" class="w-4 h-4 text-primary-600 border-gray-300 rounded focus:ring-primary-500 dark:bg-dark-200 dark:border-gray-600">
                                        <span class="mr-2 text-sm text-gray-700 dark:text-gray-300">يتطلب رقم مرجعي (مثل رقم الشيك أو رقم التحويل)</span>
                                    </label>
                                </div>
                            </div>

                            <!-- الحالة -->
                            <div class="mt-4">
                                <label class="flex items-center">
                                    <input type="checkbox" id="is_active" name="is_active" x-model="currentMethod.is_active" class="w-4 h-4 text-primary-600 border-gray-300 rounded focus:ring-primary-500 dark:bg-dark-200 dark:border-gray-600">
                                    <span class="mr-2 text-sm text-gray-700 dark:text-gray-300">تفعيل طريقة الدفع</span>
                                </label>
                            </div>
                        </div>
                    </div>

                    <div class="bg-gray-50 dark:bg-dark-200 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                        <button type="submit" class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-primary-600 text-base font-medium text-white hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 sm:ml-3 sm:w-auto sm:text-sm">
                            <span x-text="editMode ? 'حفظ التغييرات' : 'إضافة'"></span>
                        </button>
                        <button type="button" @click="showModal = false" class="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 dark:border-gray-600 shadow-sm px-4 py-2 bg-white dark:bg-dark-300 text-base font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-dark-400 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm">
                            إلغاء
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Powered By -->
    <div class="powered-by">
        <div class="w-8 h-8 rounded-full bg-primary-50 dark:bg-primary-900/50 flex items-center justify-center ml-2 text-primary-600 dark:text-primary-400">
            <i class="ri-code-s-slash-line"></i>
        </div>
        <div>
            <div class="text-xs text-gray-600 dark:text-gray-400">
                Powered By <span class="font-bold text-primary-600 dark:text-primary-400">ENG/ Fouad Saber</span>
            </div>
            <div class="text-xs text-gray-500 dark:text-gray-500">Tel: 01020073527</div>
        </div>
    </div>

    <script>
        // تفعيل وضع الظلام إذا كان مخزنًا
        if (localStorage.getItem('darkMode') === 'enabled') {
            document.documentElement.classList.add('dark');
        }

        document.addEventListener('DOMContentLoaded', function() {
            window.deletePaymentMethod = function(id) {
                if (confirm('هل أنت متأكد من حذف طريقة الدفع هذه؟')) {
                    fetch("{{ url_for('settings.delete_payment_method', payment_method_id=0) }}".replace('0', id), {
                        method: 'DELETE',
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            alert("تم حذف طريقة الدفع بنجاح");
                            location.reload();
                        } else {
                            alert("حدث خطأ أثناء حذف طريقة الدفع: " + data.error);
                        }
                    })
                    .catch(error => {
                        alert("حدث خطأ أثناء الاتصال بالخادم");
                        console.error(error);
                    });
                }
            };

            window.setDefaultPaymentMethod = function(id) {
                fetch("{{ url_for('settings.set_default_payment_method', payment_method_id=0) }}".replace('0', id), {
                    method: 'POST',
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        alert("تم تعيين طريقة الدفع كافتراضية بنجاح");
                        location.reload();
                    } else {
                        alert("حدث خطأ أثناء تعيين طريقة الدفع كافتراضية: " + data.error);
                    }
                })
                .catch(error => {
                    alert("حدث خطأ أثناء الاتصال بالخادم");
                    console.error(error);
                });
            };

            window.submitPaymentMethod = function() {
                const form = document.getElementById('paymentMethodForm');
                const formData = new FormData(form);

                // إضافة البيانات من Alpine.js
                for (const key in this.currentMethod) {
                    if (key === 'is_active') {
                        formData.set(key, this.currentMethod[key] ? '1' : '0');
                    } else {
                        formData.set(key, this.currentMethod[key] || '');
                    }
                }

                const url = this.editMode
                    ? "{{ url_for('settings.update_payment_method', payment_method_id=0) }}".replace('0', this.currentMethod.id)
                    : "{{ url_for('settings.add_payment_method') }}";

                fetch(url, {
                    method: this.editMode ? 'PUT' : 'POST',
                    body: formData,
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        alert(this.editMode ? "تم تحديث طريقة الدفع بنجاح" : "تم إضافة طريقة الدفع بنجاح");
                        location.reload();
                    } else {
                        alert("حدث خطأ: " + data.error);
                    }
                })
                .catch(error => {
                    alert("حدث خطأ أثناء الاتصال بالخادم");
                    console.error(error);
                });
            };
        });
    </script>
</body>
</html>
