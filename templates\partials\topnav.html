<header class="bg-white shadow-sm z-10">
    <div class="flex items-center justify-between px-6 py-3">
        <div class="flex items-center">
            <button id="sidebarToggle" class="p-1 rounded-full text-gray-700 hover:bg-gray-100 focus:outline-none mr-2">
                <div class="w-6 h-6 flex items-center justify-center">
                    <i class="ri-menu-line"></i>
                </div>
            </button>
        </div>

        <div class="flex items-center space-x-4 space-x-reverse">
            <!-- تم إزالة زر الوضع الداكن بناءً على طلب العميل -->

            <div class="relative">
                <button id="notificationBtn" class="p-1 rounded-full text-gray-700 hover:bg-gray-100 focus:outline-none">
                    <div class="w-6 h-6 flex items-center justify-center">
                        <i class="ri-notification-3-line"></i>
                    </div>
                </button>
                <span id="notificationUnreadBadge" class="absolute top-0 right-0 h-4 w-4 rounded-full bg-red-500 text-white text-xs flex items-center justify-center hidden"></span>

                <!-- Notifications Dropdown -->
                <div id="notificationsMenu" class="hidden absolute left-0 mt-2 w-80 rounded-md shadow-lg py-1 bg-white ring-1 ring-black ring-opacity-5 z-50" role="menu">
                    <div class="px-4 py-2 border-b border-gray-100 flex justify-between items-center">
                        <h3 class="text-sm font-medium text-gray-700">الإشعارات</h3>
                        <button id="markAllReadBtnDropdown" class="text-xs text-blue-500 hover:text-blue-700">تحديد الكل كمقروء</button>
                    </div>
                    <div id="notificationsContainer" class="max-h-60 overflow-y-auto">
                        <div class="p-4 text-center text-gray-500">
                            <div class="animate-spin w-6 h-6 border-2 border-gray-300 border-t-blue-500 rounded-full mx-auto mb-2"></div>
                            <p class="text-sm">جاري تحميل الإشعارات...</p>
                        </div>
                    </div>
                    <a href="{{ url_for('notifications.index') }}" class="block text-center text-sm text-primary font-medium py-2 hover:bg-gray-50">عرض جميع الإشعارات</a>
                </div>
            </div>

            <div class="flex items-center">
                <span class="text-sm font-medium ml-2">{{ current_user.full_name }}</span>
                <img class="h-8 w-8 rounded-full object-cover" src="https://ui-avatars.com/api/?name={{ current_user.full_name }}&background=3b82f6&color=fff" alt="صورة المستخدم">
            </div>

            <div class="relative">
                <button id="userMenuBtn" class="p-1 rounded-full text-gray-700 hover:bg-gray-100 focus:outline-none">
                    <div class="w-6 h-6 flex items-center justify-center">
                        <i class="ri-arrow-down-s-line"></i>
                    </div>
                </button>

                <div id="userMenu" class="hidden absolute left-0 mt-2 w-48 rounded-md shadow-lg py-1 bg-white ring-1 ring-black ring-opacity-5 z-50" role="menu">
                    <a href="{{ url_for('users.profile') }}" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100" role="menuitem">الملف الشخصي</a>
                    <a href="{{ url_for('auth.logout') }}" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100" role="menuitem">تسجيل الخروج</a>
                </div>
            </div>
        </div>
    </div>
</header>

<script>
    // User menu toggle
    const userMenuBtn = document.getElementById('userMenuBtn');
    const userMenu = document.getElementById('userMenu');

    userMenuBtn.addEventListener('click', function() {
        userMenu.classList.toggle('hidden');
        // Hide notifications menu when user menu is opened
        notificationsMenu.classList.add('hidden');
    });

    // Notifications menu toggle
    const notificationBtn = document.getElementById('notificationBtn');
    const notificationsMenu = document.getElementById('notificationsMenu');
    const notificationsContainer = document.getElementById('notificationsContainer');
    const notificationUnreadBadge = document.getElementById('notificationUnreadBadge');
    const markAllReadBtnDropdown = document.getElementById('markAllReadBtnDropdown');

    // تحميل الإشعارات
    function loadNotifications() {
        fetch('/api/notifications/recent')
            .then(response => response.json())
            .then(data => {
                // تحديث عدد الإشعارات غير المقروءة
                if (data.unread_count > 0) {
                    notificationUnreadBadge.textContent = data.unread_count;
                    notificationUnreadBadge.classList.remove('hidden');
                } else {
                    notificationUnreadBadge.classList.add('hidden');
                }

                // تحديث قائمة الإشعارات
                if (data.notifications.length > 0) {
                    let html = '';
                    data.notifications.forEach(notification => {
                        let iconClass = '';
                        let bgClass = '';

                        if (notification.type === 'info') {
                            iconClass = 'ri-information-line text-indigo-600';
                            bgClass = 'bg-indigo-100';
                        } else if (notification.type === 'warning') {
                            iconClass = 'ri-error-warning-line text-yellow-600';
                            bgClass = 'bg-yellow-100';
                        } else if (notification.type === 'danger') {
                            iconClass = 'ri-alarm-warning-line text-red-600';
                            bgClass = 'bg-red-100';
                        } else if (notification.type === 'success') {
                            iconClass = 'ri-checkbox-circle-line text-green-600';
                            bgClass = 'bg-green-100';
                        }

                        html += `
                            <a href="${notification.link || '#'}" class="block px-4 py-3 hover:bg-gray-50 border-b border-gray-100 ${!notification.is_read ? 'bg-blue-50' : ''}">
                                <div class="flex items-start">
                                    <div class="flex-shrink-0 ${bgClass} rounded-full p-1">
                                        <i class="${iconClass}"></i>
                                    </div>
                                    <div class="mr-3 w-full">
                                        <p class="text-sm font-medium text-gray-900">${notification.title}</p>
                                        <p class="text-xs text-gray-500 mt-1">${notification.message}</p>
                                        <p class="text-xs text-gray-400 mt-1">${notification.created_at}</p>
                                    </div>
                                </div>
                            </a>
                        `;
                    });

                    notificationsContainer.innerHTML = html;
                } else {
                    notificationsContainer.innerHTML = `
                        <div class="p-4 text-center text-gray-500">
                            <p class="text-sm">لا توجد إشعارات جديدة</p>
                        </div>
                    `;
                }
            })
            .catch(error => {
                console.error('Error loading notifications:', error);
                notificationsContainer.innerHTML = `
                    <div class="p-4 text-center text-gray-500">
                        <p class="text-sm">حدث خطأ أثناء تحميل الإشعارات</p>
                    </div>
                `;
            });
    }

    // تحديد جميع الإشعارات كمقروءة
    if (markAllReadBtnDropdown) {
        markAllReadBtnDropdown.addEventListener('click', function() {
            fetch('/notifications/mark-all-read', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // إعادة تحميل الإشعارات
                    loadNotifications();
                }
            });
        });
    }

    notificationBtn.addEventListener('click', function() {
        notificationsMenu.classList.toggle('hidden');
        // Hide user menu when notifications menu is opened
        userMenu.classList.add('hidden');

        // تحميل الإشعارات عند فتح القائمة
        if (!notificationsMenu.classList.contains('hidden')) {
            loadNotifications();
        }
    });

    // Close the menus when clicking outside
    document.addEventListener('click', function(event) {
        // Close user menu
        if (!userMenuBtn.contains(event.target) && !userMenu.contains(event.target)) {
            userMenu.classList.add('hidden');
        }

        // Close notifications menu
        if (!notificationBtn.contains(event.target) && !notificationsMenu.contains(event.target)) {
            notificationsMenu.classList.add('hidden');
        }
    });

    // Sidebar toggle functionality
    const sidebarToggle = document.getElementById('sidebarToggle');
    const sidebar = document.getElementById('sidebar');

    sidebarToggle.addEventListener('click', function() {
        sidebar.classList.toggle('w-64');
        sidebar.classList.toggle('w-20');

        // Toggle visibility of text in sidebar
        const sidebarTexts = document.querySelectorAll('#sidebar span');
        sidebarTexts.forEach(function(text) {
            text.classList.toggle('hidden');
        });
    });

    // Dark mode is now handled by dark-mode-manager.js and system-manager.js

    // تحميل عدد الإشعارات غير المقروءة عند تحميل الصفحة
    document.addEventListener('DOMContentLoaded', function() {
        // تحميل عدد الإشعارات غير المقروءة
        fetch('/api/notifications/count')
            .then(response => response.json())
            .then(data => {
                if (data.count > 0) {
                    notificationUnreadBadge.textContent = data.count;
                    notificationUnreadBadge.classList.remove('hidden');
                } else {
                    notificationUnreadBadge.classList.add('hidden');
                }
            });
    });
</script>