from models import Permission, UserPermission, User
from app import db
import logging

logger = logging.getLogger(__name__)

class PermissionsManager:
    """مدير الصلاحيات في النظام"""

    # تعريف وحدات النظام
    MODULES = {
        'products': 'المنتجات',
        'sales': 'المبيعات',
        'purchases': 'المشتريات',
        'customers': 'العملاء',
        'suppliers': 'الموردين',
        'inventory': 'المخزون',
        'reports': 'التقارير',
        'users': 'المستخدمين',
        'settings': 'الإعدادات',
        'pos': 'نقطة البيع',
        'warehouses': 'المستودعات',
        'dashboard': 'لوحة التحكم',
        'employees': 'الموظفين',
        'returns': 'المرتجعات',
        'deferred_sales': 'المبيعات الآجلة',
        'profile': 'الملف الشخصي',
        'sessions': 'الجلسات',
        'roles': 'الأدوار',
        'activities': 'الأنشطة'
    }

    # تعريف الإجراءات
    ACTIONS = {
        'view': 'عرض',
        'add': 'إضافة',
        'edit': 'تعديل',
        'delete': 'حذف',
        'print': 'طباعة',
        'export': 'تصدير',
        'import': 'استيراد',
        'approve': 'موافقة',
        'reject': 'رفض',
        'cancel': 'إلغاء',
        'return': 'إرجاع',
        'transfer': 'نقل',
        'count': 'جرد',
        'adjust': 'تعديل المخزون',
        'manage': 'إدارة',
        'suspend': 'تعليق',
        'resume': 'استئناف',
        'pay': 'دفع',
        'refund': 'استرداد',
        'discount': 'خصم'
    }

    @classmethod
    def initialize_permissions(cls):
        """تهيئة الصلاحيات الافتراضية في النظام"""
        try:
            # التحقق من وجود صلاحيات مسبقة
            if Permission.query.count() > 0:
                logger.info("الصلاحيات موجودة بالفعل في النظام")
                return True

            # إنشاء الصلاحيات لكل وحدة وإجراء
            for module_code, module_name in cls.MODULES.items():
                for action_code, action_name in cls.ACTIONS.items():
                    permission_name = f"{module_code}.{action_code}"
                    permission_description = f"{action_name} {module_name}"

                    # التحقق من وجود الصلاحية
                    existing_permission = Permission.query.filter_by(name=permission_name).first()
                    if not existing_permission:
                        permission = Permission(
                            name=permission_name,
                            description=permission_description,
                            module=module_code,
                            action=action_code
                        )
                        db.session.add(permission)

            # حفظ التغييرات
            db.session.commit()
            logger.info("تم إنشاء الصلاحيات بنجاح")

            # منح جميع الصلاحيات للمستخدمين الإداريين الحاليين
            cls.grant_all_permissions_to_admins()

            return True
        except Exception as e:
            db.session.rollback()
            logger.error(f"خطأ أثناء تهيئة الصلاحيات: {str(e)}")
            return False

    @classmethod
    def grant_all_permissions_to_admins(cls):
        """منح جميع الصلاحيات للمستخدمين الإداريين"""
        try:
            # الحصول على جميع المستخدمين الإداريين
            admin_users = User.query.filter_by(role='admin').all()

            # الحصول على جميع الصلاحيات
            all_permissions = Permission.query.all()

            # منح جميع الصلاحيات لكل مدير
            for user in admin_users:
                # حذف الصلاحيات الحالية للمستخدم
                UserPermission.query.filter_by(user_id=user.id).delete()

                # إضافة الصلاحيات الجديدة
                for permission in all_permissions:
                    # التحقق من وجود الصلاحية للمستخدم
                    existing = UserPermission.query.filter_by(
                        user_id=user.id,
                        permission_id=permission.id
                    ).first()

                    if not existing:
                        user_permission = UserPermission(
                            user_id=user.id,
                            permission_id=permission.id,
                            granted=True
                        )
                        db.session.add(user_permission)

            # حفظ التغييرات
            db.session.commit()

            logger.info(f"تم منح الصلاحيات لـ {len(admin_users)} مدير بنجاح")
            return True
        except Exception as e:
            db.session.rollback()
            logger.error(f"خطأ أثناء منح الصلاحيات للمديرين: {str(e)}")
            return False

    @classmethod
    def get_user_permissions(cls, user_id):
        """الحصول على صلاحيات مستخدم محدد"""
        try:
            # الحصول على المستخدم
            user = User.query.get(user_id)
            if not user:
                return {}

            # إذا كان المستخدم مدير، فلديه جميع الصلاحيات
            if user.role == 'admin':
                all_permissions = Permission.query.all()
                return {permission.name: True for permission in all_permissions}

            # الحصول على صلاحيات المستخدم
            user_permissions = UserPermission.query.filter_by(user_id=user_id).all()

            # تحويل الصلاحيات إلى قاموس
            permissions_dict = {}

            # الحصول على جميع الصلاحيات
            all_permissions = Permission.query.all()

            # تعيين جميع الصلاحيات إلى False افتراضيًا
            for permission in all_permissions:
                permissions_dict[permission.name] = False

            # تحديث الصلاحيات الممنوحة
            for user_permission in user_permissions:
                permission = Permission.query.get(user_permission.permission_id)
                if permission:
                    permissions_dict[permission.name] = user_permission.granted

            return permissions_dict
        except Exception as e:
            logger.error(f"خطأ أثناء الحصول على صلاحيات المستخدم: {str(e)}")
            return {}

    @classmethod
    def update_user_permissions(cls, user_id, permissions_data):
        """تحديث صلاحيات مستخدم محدد"""
        try:
            # حذف الصلاحيات الحالية للمستخدم
            UserPermission.query.filter_by(user_id=user_id).delete()

            # إضافة الصلاحيات الجديدة
            for permission_id, granted in permissions_data.items():
                if granted:
                    user_permission = UserPermission(
                        user_id=user_id,
                        permission_id=int(permission_id),
                        granted=True
                    )
                    db.session.add(user_permission)

            db.session.commit()
            logger.info(f"تم تحديث صلاحيات المستخدم {user_id} بنجاح")
            return True
        except Exception as e:
            db.session.rollback()
            logger.error(f"خطأ أثناء تحديث صلاحيات المستخدم: {str(e)}")
            return False

    @classmethod
    def get_permissions_by_module(cls):
        """الحصول على الصلاحيات مرتبة حسب الوحدة"""
        try:
            # الحصول على جميع الصلاحيات
            all_permissions = Permission.query.all()

            # تنظيم الصلاحيات حسب الوحدة
            permissions_by_module = {}

            for permission in all_permissions:
                if permission.module not in permissions_by_module:
                    permissions_by_module[permission.module] = {
                        'name': cls.MODULES.get(permission.module, permission.module),
                        'permissions': []
                    }

                permissions_by_module[permission.module]['permissions'].append({
                    'id': permission.id,
                    'name': permission.name,
                    'description': permission.description,
                    'action': permission.action,
                    'action_name': cls.ACTIONS.get(permission.action, permission.action)
                })

            return permissions_by_module
        except Exception as e:
            logger.error(f"خطأ أثناء الحصول على الصلاحيات حسب الوحدة: {str(e)}")
            return {}
