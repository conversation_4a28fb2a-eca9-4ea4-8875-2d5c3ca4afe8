from flask import Blueprint, render_template, request, jsonify, flash, redirect, url_for
from flask_login import login_required, current_user
from models import Purchase, PurchaseItem, Supplier, Product, Payment, DeferredPaymentPlan, DeferredPaymentInstallment
from app import db
from sqlalchemy import func, desc
from datetime import datetime, timedelta
import random
import string

deferred_purchases_blueprint = Blueprint('deferred_purchases', __name__)

@deferred_purchases_blueprint.route('/deferred-purchases')
@login_required
def index():
    """عرض قائمة المشتريات الآجلة"""
    # الحصول على معلمات الفلترة
    search = request.args.get('search', '')
    supplier_id = request.args.get('supplier', '')
    date_from = request.args.get('date_from', '')
    date_to = request.args.get('date_to', '')

    # إعداد الاستعلام
    query = Purchase.query.filter(Purchase.status == 'deferred')

    # تطبيق الفلاتر
    if search:
        query = query.filter(Purchase.reference_number.ilike(f'%{search}%'))

    if supplier_id and supplier_id.isdigit():
        query = query.filter(Purchase.supplier_id == int(supplier_id))

    if date_from:
        try:
            date_from_obj = datetime.strptime(date_from, '%Y-%m-%d')
            query = query.filter(Purchase.created_at >= date_from_obj)
        except:
            pass

    if date_to:
        try:
            date_to_obj = datetime.strptime(date_to, '%Y-%m-%d')
            date_to_obj = date_to_obj.replace(hour=23, minute=59, second=59)
            query = query.filter(Purchase.created_at <= date_to_obj)
        except:
            pass

    # تنفيذ الاستعلام مع التقسيم إلى صفحات
    page = request.args.get('page', 1, type=int)
    per_page = 10
    purchases_paginated = query.order_by(Purchase.created_at.desc()).paginate(page=page, per_page=per_page)

    # الحصول على الموردين لقائمة الفلترة
    suppliers = Supplier.query.order_by(Supplier.name).all()

    # إحصائيات المشتريات الآجلة
    try:
        total_deferred_purchases = query.count()
        total_deferred_amount = db.session.query(func.sum(Purchase.total)).filter(
            Purchase.status == 'deferred'
        ).scalar() or 0
        
        # حساب إجمالي المدفوعات
        total_paid = db.session.query(func.sum(Payment.amount)).join(
            Purchase, Payment.purchase_id == Purchase.id
        ).filter(
            Purchase.status == 'deferred'
        ).scalar() or 0
        
        # حساب المبلغ المتبقي
        total_remaining = total_deferred_amount - total_paid
        
    except Exception as e:
        print(f"خطأ في الحصول على إحصائيات المشتريات الآجلة: {str(e)}")
        total_deferred_purchases = 0
        total_deferred_amount = 0
        total_paid = 0
        total_remaining = 0

    # الإحصائيات
    stats = {
        'total_deferred_purchases': total_deferred_purchases,
        'total_deferred_amount': total_deferred_amount,
        'total_paid': total_paid,
        'total_remaining': total_remaining
    }

    return render_template(
        'deferred_purchases/index.html',
        purchases=purchases_paginated,
        suppliers=suppliers,
        stats=stats,
        search=search,
        supplier_id=supplier_id,
        date_from=date_from,
        date_to=date_to,
        current_user=current_user
    )

@deferred_purchases_blueprint.route('/deferred-purchases/<int:id>')
@login_required
def view(id):
    """عرض تفاصيل الشراء الآجل"""
    purchase = Purchase.query.get_or_404(id)
    
    # التحقق من أن الطلب آجل
    if purchase.status != 'deferred':
        flash('هذا الطلب ليس شراءً آجلاً', 'warning')
        return redirect(url_for('deferred_purchases.index'))
    
    # الحصول على المدفوعات
    payments = Payment.query.filter_by(purchase_id=id).order_by(Payment.payment_date).all()
    
    # حساب إجمالي المدفوعات
    total_paid = sum(payment.amount for payment in payments)
    remaining_amount = purchase.total - total_paid
    
    # الحصول على خطة الدفع إن وجدت
    payment_plan = DeferredPaymentPlan.query.filter_by(purchase_id=id).first()
    
    return render_template(
        'deferred_purchases/view.html',
        purchase=purchase,
        payments=payments,
        total_paid=total_paid,
        remaining_amount=remaining_amount,
        payment_plan=payment_plan,
        current_user=current_user
    )

@deferred_purchases_blueprint.route('/deferred-purchases/<int:id>/add-payment', methods=['POST'])
@login_required
def add_payment(id):
    """إضافة دفعة جديدة للشراء الآجل"""
    purchase = Purchase.query.get_or_404(id)
    
    # التحقق من أن الطلب آجل
    if purchase.status != 'deferred':
        flash('هذا الطلب ليس شراءً آجلاً', 'warning')
        return redirect(url_for('deferred_purchases.index'))
    
    try:
        # الحصول على بيانات الدفعة
        amount = float(request.form.get('amount', 0))
        payment_method = request.form.get('payment_method', 'cash')
        notes = request.form.get('notes', '')
        
        # التحقق من صحة المبلغ
        if amount <= 0:
            flash('يجب أن يكون المبلغ أكبر من صفر', 'danger')
            return redirect(url_for('deferred_purchases.view', id=id))
        
        # حساب المبلغ المتبقي
        payments = Payment.query.filter_by(purchase_id=id).all()
        total_paid = sum(payment.amount for payment in payments)
        remaining_amount = purchase.total - total_paid
        
        # التحقق من أن المبلغ لا يتجاوز المبلغ المتبقي
        if amount > remaining_amount:
            flash(f'المبلغ يتجاوز المبلغ المتبقي ({remaining_amount})', 'danger')
            return redirect(url_for('deferred_purchases.view', id=id))
        
        # إنشاء رقم مرجعي للدفعة
        reference_number = Payment.generate_reference_number()
        
        # إنشاء الدفعة
        payment = Payment(
            reference_number=reference_number,
            amount=amount,
            payment_method=payment_method,
            payment_date=datetime.now(),
            notes=notes,
            created_by=current_user.id,
            purchase_id=id
        )
        
        db.session.add(payment)
        
        # تحديث حالة الطلب إذا تم دفع كامل المبلغ
        new_total_paid = total_paid + amount
        if new_total_paid >= purchase.total:
            purchase.status = 'received'
            flash('تم دفع كامل المبلغ وتحديث حالة الطلب إلى مستلم', 'success')
        
        # تحديث خطة الدفع إن وجدت
        payment_plan = DeferredPaymentPlan.query.filter_by(purchase_id=id).first()
        if payment_plan:
            # البحث عن القسط المستحق
            installment = DeferredPaymentInstallment.query.filter_by(
                plan_id=payment_plan.id,
                status='pending'
            ).order_by(DeferredPaymentInstallment.due_date).first()
            
            if installment:
                installment.mark_as_paid(payment.id)
        
        db.session.commit()
        
        flash('تم إضافة الدفعة بنجاح', 'success')
        
    except Exception as e:
        db.session.rollback()
        flash(f'حدث خطأ أثناء إضافة الدفعة: {str(e)}', 'danger')
    
    return redirect(url_for('deferred_purchases.view', id=id))

@deferred_purchases_blueprint.route('/deferred-purchases/<int:id>/create-plan', methods=['POST'])
@login_required
def create_payment_plan(id):
    """إنشاء خطة دفع للشراء الآجل"""
    purchase = Purchase.query.get_or_404(id)
    
    # التحقق من أن الطلب آجل
    if purchase.status != 'deferred':
        flash('هذا الطلب ليس شراءً آجلاً', 'warning')
        return redirect(url_for('deferred_purchases.index'))
    
    # التحقق من عدم وجود خطة دفع سابقة
    existing_plan = DeferredPaymentPlan.query.filter_by(purchase_id=id).first()
    if existing_plan:
        flash('يوجد بالفعل خطة دفع لهذا الطلب', 'warning')
        return redirect(url_for('deferred_purchases.view', id=id))
    
    try:
        # الحصول على بيانات خطة الدفع
        installments_count = int(request.form.get('installments_count', 1))
        start_date_str = request.form.get('start_date')
        end_date_str = request.form.get('end_date')
        notes = request.form.get('notes', '')
        
        # التحقق من صحة عدد الأقساط
        if installments_count <= 0:
            flash('يجب أن يكون عدد الأقساط أكبر من صفر', 'danger')
            return redirect(url_for('deferred_purchases.view', id=id))
        
        # تحويل التواريخ
        start_date = datetime.strptime(start_date_str, '%Y-%m-%d') if start_date_str else datetime.now()
        end_date = datetime.strptime(end_date_str, '%Y-%m-%d') if end_date_str else None
        
        # حساب المبلغ المتبقي
        payments = Payment.query.filter_by(purchase_id=id).all()
        total_paid = sum(payment.amount for payment in payments)
        remaining_amount = purchase.total - total_paid
        
        # إنشاء خطة الدفع
        payment_plan = DeferredPaymentPlan(
            total_amount=remaining_amount,
            paid_amount=0,
            remaining_amount=remaining_amount,
            installments_count=installments_count,
            start_date=start_date,
            end_date=end_date,
            status='active',
            notes=notes,
            created_by=current_user.id,
            purchase_id=id
        )
        
        db.session.add(payment_plan)
        db.session.flush()  # للحصول على معرف خطة الدفع
        
        # إنشاء الأقساط
        installment_amount = remaining_amount / installments_count
        
        # حساب الفترة بين الأقساط
        if end_date:
            days_between = (end_date - start_date).days / installments_count
        else:
            days_between = 30  # شهر افتراضي
        
        for i in range(installments_count):
            due_date = start_date + timedelta(days=i * days_between)
            
            installment = DeferredPaymentInstallment(
                plan_id=payment_plan.id,
                amount=installment_amount,
                due_date=due_date,
                status='pending',
                notes=f'القسط رقم {i+1} من {installments_count}'
            )
            
            db.session.add(installment)
        
        db.session.commit()
        
        flash('تم إنشاء خطة الدفع بنجاح', 'success')
        
    except Exception as e:
        db.session.rollback()
        flash(f'حدث خطأ أثناء إنشاء خطة الدفع: {str(e)}', 'danger')
    
    return redirect(url_for('deferred_purchases.view', id=id))

@deferred_purchases_blueprint.route('/deferred-purchases/reports')
@login_required
def reports():
    """تقارير المشتريات الآجلة"""
    # إحصائيات المشتريات الآجلة
    try:
        # إجمالي المشتريات الآجلة
        total_deferred_amount = db.session.query(func.sum(Purchase.total)).filter(
            Purchase.status == 'deferred'
        ).scalar() or 0
        
        # عدد المشتريات الآجلة
        total_deferred_purchases = Purchase.query.filter(
            Purchase.status == 'deferred'
        ).count()
        
        # إجمالي المدفوعات
        total_paid = db.session.query(func.sum(Payment.amount)).join(
            Purchase, Payment.purchase_id == Purchase.id
        ).filter(
            Purchase.status == 'deferred'
        ).scalar() or 0
        
        # المبلغ المتبقي
        total_remaining = total_deferred_amount - total_paid
        
        # المشتريات الآجلة حسب المورد
        deferred_by_supplier = db.session.query(
            Supplier.name,
            func.sum(Purchase.total).label('total_amount'),
            func.count(Purchase.id).label('purchases_count')
        ).join(
            Purchase, Supplier.id == Purchase.supplier_id
        ).filter(
            Purchase.status == 'deferred'
        ).group_by(
            Supplier.name
        ).all()
        
        # المشتريات الآجلة حسب الشهر
        deferred_by_month = db.session.query(
            func.strftime('%Y-%m', Purchase.created_at).label('month'),
            func.sum(Purchase.total).label('total_amount'),
            func.count(Purchase.id).label('purchases_count')
        ).filter(
            Purchase.status == 'deferred'
        ).group_by(
            'month'
        ).order_by(
            'month'
        ).all()
        
    except Exception as e:
        print(f"خطأ في الحصول على إحصائيات المشتريات الآجلة: {str(e)}")
        total_deferred_purchases = 0
        total_deferred_amount = 0
        total_paid = 0
        total_remaining = 0
        deferred_by_supplier = []
        deferred_by_month = []
    
    # الإحصائيات
    stats = {
        'total_deferred_purchases': total_deferred_purchases,
        'total_deferred_amount': total_deferred_amount,
        'total_paid': total_paid,
        'total_remaining': total_remaining,
        'deferred_by_supplier': deferred_by_supplier,
        'deferred_by_month': deferred_by_month
    }
    
    return render_template(
        'deferred_purchases/reports.html',
        stats=stats,
        current_user=current_user
    )
