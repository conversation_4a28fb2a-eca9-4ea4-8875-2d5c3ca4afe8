"""
Nobara POS System - Database Models
نظام نوبارا لنقاط البيع - نماذج قاعدة البيانات
"""

from datetime import datetime
from flask_sqlalchemy import SQLAlchemy
from flask_login import UserMixin
from werkzeug.security import generate_password_hash, check_password_hash

db = SQLAlchemy()

# Association tables for many-to-many relationships
user_permissions = db.Table('user_permissions',
    db.Column('user_id', db.Integer, db.<PERSON>ey('users.id'), primary_key=True),
    db.Column('permission_id', db.Integer, db.<PERSON><PERSON>('permissions.id'), primary_key=True)
)

role_permissions = db.Table('role_permissions',
    db.Column('role_id', db.Integer, db.<PERSON>ey('roles.id'), primary_key=True),
    db.Column('permission_id', db.Integer, db.<PERSON>('permissions.id'), primary_key=True)
)

class User(UserMixin, db.Model):
    """User model - نموذج المستخدم"""
    __tablename__ = 'users'

    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(80), unique=True, nullable=False, index=True)
    email = db.Column(db.String(120), unique=True, nullable=True, index=True)
    password_hash = db.Column(db.String(255), nullable=False)

    # Personal Information
    first_name = db.Column(db.String(50), nullable=False)
    last_name = db.Column(db.String(50), nullable=False)
    phone = db.Column(db.String(20), nullable=True)

    # Account Settings
    is_active = db.Column(db.Boolean, default=True, nullable=False)
    can_login = db.Column(db.Boolean, default=True, nullable=False)
    language = db.Column(db.String(5), default='ar', nullable=False)
    theme = db.Column(db.String(10), default='light', nullable=False)

    # Relationships
    role_id = db.Column(db.Integer, db.ForeignKey('roles.id'), nullable=True)
    warehouse_id = db.Column(db.Integer, db.ForeignKey('warehouses.id'), nullable=True)

    # Timestamps
    created_at = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False)
    last_login = db.Column(db.DateTime, nullable=True)

    # Relationships
    role = db.relationship('Role', backref='users', lazy=True)
    warehouse = db.relationship('Warehouse', backref='users', lazy=True)
    permissions = db.relationship('Permission', secondary=user_permissions, backref='users', lazy='dynamic')
    sales = db.relationship('Sale', backref='user', lazy='dynamic')

    def set_password(self, password):
        """Set password hash"""
        self.password_hash = generate_password_hash(password)

    def check_password(self, password):
        """Check password"""
        return check_password_hash(self.password_hash, password)

    @property
    def full_name(self):
        """Get full name"""
        return f"{self.first_name} {self.last_name}"

    def has_permission(self, permission_name):
        """Check if user has specific permission"""
        # Check direct permissions
        if self.permissions.filter_by(name=permission_name).first():
            return True

        # Check role permissions
        if self.role:
            return self.role.has_permission(permission_name)

        return False

    def __repr__(self):
        return f'<User {self.username}>'

class Role(db.Model):
    """Role model - نموذج الدور"""
    __tablename__ = 'roles'

    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(50), unique=True, nullable=False)
    name_ar = db.Column(db.String(50), nullable=False)
    description = db.Column(db.Text, nullable=True)
    is_active = db.Column(db.Boolean, default=True, nullable=False)

    # Timestamps
    created_at = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False)

    # Relationships
    permissions = db.relationship('Permission', secondary=role_permissions, backref='roles', lazy='dynamic')

    def has_permission(self, permission_name):
        """Check if role has specific permission"""
        return self.permissions.filter_by(name=permission_name).first() is not None

    def __repr__(self):
        return f'<Role {self.name}>'

class Permission(db.Model):
    """Permission model - نموذج الصلاحية"""
    __tablename__ = 'permissions'

    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), unique=True, nullable=False)
    name_ar = db.Column(db.String(100), nullable=False)
    description = db.Column(db.Text, nullable=True)
    category = db.Column(db.String(50), nullable=False)

    # Timestamps
    created_at = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)

    def __repr__(self):
        return f'<Permission {self.name}>'

class Warehouse(db.Model):
    """Warehouse model - نموذج المخزن"""
    __tablename__ = 'warehouses'

    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    name_ar = db.Column(db.String(100), nullable=True)
    location = db.Column(db.String(200), nullable=True)
    description = db.Column(db.Text, nullable=True)
    is_active = db.Column(db.Boolean, default=True, nullable=False)
    is_main = db.Column(db.Boolean, default=False, nullable=False)

    # Contact Information
    phone = db.Column(db.String(20), nullable=True)
    email = db.Column(db.String(120), nullable=True)
    address = db.Column(db.Text, nullable=True)

    # Timestamps
    created_at = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False)

    # Relationships
    inventory_items = db.relationship('InventoryItem', backref='warehouse', lazy='dynamic')

    def __repr__(self):
        return f'<Warehouse {self.name}>'

class Category(db.Model):
    """Category model - نموذج الفئة"""
    __tablename__ = 'categories'

    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    name_ar = db.Column(db.String(100), nullable=True)
    description = db.Column(db.Text, nullable=True)
    is_active = db.Column(db.Boolean, default=True, nullable=False)

    # Hierarchy
    parent_id = db.Column(db.Integer, db.ForeignKey('categories.id'), nullable=True)

    # Timestamps
    created_at = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False)

    # Relationships
    parent = db.relationship('Category', remote_side=[id], backref='children')
    products = db.relationship('Product', backref='category', lazy='dynamic')

    def __repr__(self):
        return f'<Category {self.name}>'

class Product(db.Model):
    """Product model - نموذج المنتج"""
    __tablename__ = 'products'

    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(200), nullable=False, index=True)
    name_ar = db.Column(db.String(200), nullable=True)
    description = db.Column(db.Text, nullable=True)

    # Identification
    sku = db.Column(db.String(50), unique=True, nullable=True, index=True)
    barcode = db.Column(db.String(50), unique=True, nullable=True, index=True)

    # Pricing
    cost_price = db.Column(db.Numeric(10, 2), nullable=False, default=0)
    selling_price = db.Column(db.Numeric(10, 2), nullable=False, default=0)
    min_price = db.Column(db.Numeric(10, 2), nullable=True)

    # Inventory
    track_inventory = db.Column(db.Boolean, default=True, nullable=False)
    min_stock_level = db.Column(db.Integer, default=0, nullable=False)
    max_stock_level = db.Column(db.Integer, nullable=True)

    # Product Details
    unit = db.Column(db.String(20), default='piece', nullable=False)
    weight = db.Column(db.Numeric(8, 3), nullable=True)
    dimensions = db.Column(db.String(100), nullable=True)

    # Status
    is_active = db.Column(db.Boolean, default=True, nullable=False)
    is_featured = db.Column(db.Boolean, default=False, nullable=False)

    # Media
    image_url = db.Column(db.String(500), nullable=True)

    # Tax
    tax_rate = db.Column(db.Numeric(5, 4), default=0.14, nullable=False)

    # Relationships
    category_id = db.Column(db.Integer, db.ForeignKey('categories.id'), nullable=True)

    # Timestamps
    created_at = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False)

    # Relationships
    inventory_items = db.relationship('InventoryItem', backref='product', lazy='dynamic')
    sale_items = db.relationship('SaleItem', backref='product', lazy='dynamic')

    @property
    def total_stock(self):
        """Get total stock across all warehouses"""
        return sum(item.quantity for item in self.inventory_items)

    @property
    def profit_margin(self):
        """Calculate profit margin"""
        if self.cost_price and self.selling_price:
            return ((self.selling_price - self.cost_price) / self.selling_price) * 100
        return 0

    def get_stock_in_warehouse(self, warehouse_id):
        """Get stock quantity in specific warehouse"""
        item = self.inventory_items.filter_by(warehouse_id=warehouse_id).first()
        return item.quantity if item else 0

    def __repr__(self):
        return f'<Product {self.name}>'

class InventoryItem(db.Model):
    """Inventory item model - نموذج عنصر المخزون"""
    __tablename__ = 'inventory_items'

    id = db.Column(db.Integer, primary_key=True)
    quantity = db.Column(db.Integer, default=0, nullable=False)
    reserved_quantity = db.Column(db.Integer, default=0, nullable=False)

    # Relationships
    product_id = db.Column(db.Integer, db.ForeignKey('products.id'), nullable=False)
    warehouse_id = db.Column(db.Integer, db.ForeignKey('warehouses.id'), nullable=False)

    # Timestamps
    created_at = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False)

    # Unique constraint
    __table_args__ = (db.UniqueConstraint('product_id', 'warehouse_id', name='unique_product_warehouse'),)

    @property
    def available_quantity(self):
        """Get available quantity (total - reserved)"""
        return self.quantity - self.reserved_quantity

    def __repr__(self):
        return f'<InventoryItem Product:{self.product_id} Warehouse:{self.warehouse_id}>'

class Customer(db.Model):
    """Customer model - نموذج العميل"""
    __tablename__ = 'customers'

    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(200), nullable=False, index=True)
    email = db.Column(db.String(120), nullable=True, index=True)
    phone = db.Column(db.String(20), nullable=True, index=True)

    # Address
    address = db.Column(db.Text, nullable=True)
    city = db.Column(db.String(100), nullable=True)
    country = db.Column(db.String(100), nullable=True)

    # Business Information
    tax_number = db.Column(db.String(50), nullable=True)
    credit_limit = db.Column(db.Numeric(12, 2), default=0, nullable=False)

    # Status
    is_active = db.Column(db.Boolean, default=True, nullable=False)
    customer_type = db.Column(db.String(20), default='retail', nullable=False)  # retail, wholesale

    # Timestamps
    created_at = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False)

    # Relationships
    sales = db.relationship('Sale', backref='customer', lazy='dynamic')

    @property
    def total_purchases(self):
        """Get total purchase amount"""
        return sum(sale.total_amount for sale in self.sales if sale.status == 'completed')

    @property
    def outstanding_balance(self):
        """Get outstanding balance"""
        return sum(sale.total_amount - sale.paid_amount for sale in self.sales
                  if sale.status == 'completed' and sale.payment_status != 'paid')

    def __repr__(self):
        return f'<Customer {self.name}>'

class Supplier(db.Model):
    """Supplier model - نموذج المورد"""
    __tablename__ = 'suppliers'

    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(200), nullable=False, index=True)
    email = db.Column(db.String(120), nullable=True, index=True)
    phone = db.Column(db.String(20), nullable=True, index=True)

    # Address
    address = db.Column(db.Text, nullable=True)
    city = db.Column(db.String(100), nullable=True)
    country = db.Column(db.String(100), nullable=True)

    # Business Information
    tax_number = db.Column(db.String(50), nullable=True)
    credit_terms = db.Column(db.Integer, default=30, nullable=False)  # days

    # Status
    is_active = db.Column(db.Boolean, default=True, nullable=False)

    # Timestamps
    created_at = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False)

    # Relationships
    purchases = db.relationship('Purchase', backref='supplier', lazy='dynamic')

    def __repr__(self):
        return f'<Supplier {self.name}>'

class Sale(db.Model):
    """Sale model - نموذج المبيعة"""
    __tablename__ = 'sales'

    id = db.Column(db.Integer, primary_key=True)
    sale_number = db.Column(db.String(50), unique=True, nullable=False, index=True)

    # Amounts
    subtotal = db.Column(db.Numeric(12, 2), nullable=False, default=0)
    tax_amount = db.Column(db.Numeric(12, 2), nullable=False, default=0)
    discount_amount = db.Column(db.Numeric(12, 2), nullable=False, default=0)
    total_amount = db.Column(db.Numeric(12, 2), nullable=False, default=0)
    paid_amount = db.Column(db.Numeric(12, 2), nullable=False, default=0)

    # Status
    status = db.Column(db.String(20), default='pending', nullable=False)  # pending, completed, cancelled, returned
    payment_status = db.Column(db.String(20), default='unpaid', nullable=False)  # unpaid, partial, paid
    payment_method = db.Column(db.String(20), default='cash', nullable=False)  # cash, card, credit, installment

    # Sale Type
    sale_type = db.Column(db.String(20), default='regular', nullable=False)  # regular, deferred, installment

    # Notes
    notes = db.Column(db.Text, nullable=True)

    # Relationships
    customer_id = db.Column(db.Integer, db.ForeignKey('customers.id'), nullable=True)
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    warehouse_id = db.Column(db.Integer, db.ForeignKey('warehouses.id'), nullable=False)

    # Timestamps
    sale_date = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)
    due_date = db.Column(db.DateTime, nullable=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False)

    # Relationships
    warehouse = db.relationship('Warehouse', backref='sales')
    items = db.relationship('SaleItem', backref='sale', lazy='dynamic', cascade='all, delete-orphan')
    payments = db.relationship('SalePayment', backref='sale', lazy='dynamic', cascade='all, delete-orphan')

    @property
    def remaining_amount(self):
        """Get remaining amount to be paid"""
        return self.total_amount - self.paid_amount

    def update_payment_status(self):
        """Update payment status based on paid amount"""
        if self.paid_amount >= self.total_amount:
            self.payment_status = 'paid'
        elif self.paid_amount > 0:
            self.payment_status = 'partial'
        else:
            self.payment_status = 'unpaid'

    def __repr__(self):
        return f'<Sale {self.sale_number}>'

class SaleItem(db.Model):
    """Sale item model - نموذج عنصر المبيعة"""
    __tablename__ = 'sale_items'

    id = db.Column(db.Integer, primary_key=True)
    quantity = db.Column(db.Integer, nullable=False)
    unit_price = db.Column(db.Numeric(10, 2), nullable=False)
    discount_amount = db.Column(db.Numeric(10, 2), default=0, nullable=False)
    tax_rate = db.Column(db.Numeric(5, 4), default=0.14, nullable=False)
    total_amount = db.Column(db.Numeric(12, 2), nullable=False)

    # Relationships
    sale_id = db.Column(db.Integer, db.ForeignKey('sales.id'), nullable=False)
    product_id = db.Column(db.Integer, db.ForeignKey('products.id'), nullable=False)

    # Timestamps
    created_at = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)

    @property
    def subtotal(self):
        """Calculate subtotal (quantity * unit_price - discount)"""
        return (self.quantity * self.unit_price) - self.discount_amount

    @property
    def tax_amount(self):
        """Calculate tax amount"""
        return self.subtotal * self.tax_rate

    def __repr__(self):
        return f'<SaleItem Sale:{self.sale_id} Product:{self.product_id}>'

class SalePayment(db.Model):
    """Sale payment model - نموذج دفعة المبيعة"""
    __tablename__ = 'sale_payments'

    id = db.Column(db.Integer, primary_key=True)
    amount = db.Column(db.Numeric(12, 2), nullable=False)
    payment_method = db.Column(db.String(20), nullable=False)  # cash, card, bank_transfer
    reference_number = db.Column(db.String(100), nullable=True)
    notes = db.Column(db.Text, nullable=True)

    # Relationships
    sale_id = db.Column(db.Integer, db.ForeignKey('sales.id'), nullable=False)

    # Timestamps
    payment_date = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)

    def __repr__(self):
        return f'<SalePayment Sale:{self.sale_id} Amount:{self.amount}>'
