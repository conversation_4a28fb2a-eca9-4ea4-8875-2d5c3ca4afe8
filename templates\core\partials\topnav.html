<header class="bg-white shadow-sm z-10 border-b border-gray-100">
    <div class="flex items-center justify-between px-6 py-4">
        <div class="flex items-center">
            <button id="sidebarToggle" class="p-2 rounded-lg text-gray-600 hover:bg-gray-100 hover:text-primary-600 focus:outline-none mr-2 transition-all duration-300">
                <div class="w-6 h-6 flex items-center justify-center">
                    <i class="ri-menu-line text-lg"></i>
                </div>
            </button>

            <div class="ml-4 hidden md:block">
                <h1 class="text-lg font-semibold text-gray-800">{{ self.title() }}</h1>
            </div>
        </div>

        <div class="flex items-center space-x-5 space-x-reverse">
            <div class="relative">
                <button id="notificationBtn" class="flex items-center px-3 py-2 rounded-lg bg-gray-50 hover:bg-primary-50 text-gray-700 hover:text-primary-600 transition-all duration-300" title="الإشعارات" aria-label="الإشعارات">
                    <i class="ri-notification-3-line ml-2 text-lg"></i>
                    <span class="text-sm font-medium">الإشعارات</span>
                </button>
                <span id="notificationUnreadBadge" class="absolute top-0 right-0 h-5 w-5 rounded-full bg-red-500 text-white text-xs flex items-center justify-center shadow-md hidden pulse-animation"></span>

                <!-- Notifications Dropdown -->
                <div id="notificationsMenu" class="hidden absolute left-0 mt-2 w-96 rounded-lg shadow-xl py-1 bg-white ring-1 ring-black ring-opacity-5 z-50 transform transition-all duration-300" role="menu">
                    <div class="px-4 py-3 border-b border-gray-100 flex justify-between items-center bg-gradient-to-r from-primary-50 to-white">
                        <h3 class="text-sm font-bold text-gray-700 flex items-center">
                            <i class="ri-notification-3-line mr-2 text-primary-600"></i>
                            الإشعارات
                        </h3>
                        <button id="markAllReadBtnDropdown" class="text-xs bg-primary-600 hover:bg-primary-700 text-white px-2 py-1 rounded transition-colors duration-300 flex items-center">
                            <i class="ri-check-double-line mr-1"></i>
                            تحديد الكل كمقروء
                        </button>
                    </div>
                    <div id="notificationsContainer" class="max-h-80 overflow-y-auto">
                        <div class="p-4 text-center text-gray-500">
                            <div class="animate-spin w-8 h-8 border-3 border-gray-300 border-t-primary-500 rounded-full mx-auto mb-3"></div>
                            <p class="text-sm">جاري تحميل الإشعارات...</p>
                        </div>
                    </div>
                    <div class="border-t border-gray-100 p-2 bg-gradient-to-r from-gray-50 to-white">
                        <a href="{{ url_for('notifications.index') }}" class="block text-center text-sm text-primary-600 font-medium py-2 hover:bg-primary-50 rounded-lg transition-colors duration-300">
                            <i class="ri-arrow-left-line ml-1"></i>
                            عرض جميع الإشعارات
                        </a>
                    </div>
                </div>
            </div>

            <div class="flex items-center bg-gray-50 px-3 py-2 rounded-lg">
                <span class="text-sm font-medium ml-2 text-gray-700">{{ current_user.full_name }}</span>
                <img class="h-8 w-8 rounded-full object-cover border-2 border-white shadow-sm" src="https://ui-avatars.com/api/?name={{ current_user.full_name }}&background=3b82f6&color=fff" alt="صورة المستخدم">
            </div>

            <div class="relative">
                <button id="userMenuBtn" class="p-2 rounded-lg text-gray-600 hover:bg-gray-100 hover:text-primary-600 focus:outline-none transition-all duration-300">
                    <div class="w-6 h-6 flex items-center justify-center">
                        <i class="ri-arrow-down-s-line text-lg"></i>
                    </div>
                </button>

                <div id="userMenu" class="hidden absolute left-0 mt-2 w-56 rounded-lg shadow-xl py-1 bg-white ring-1 ring-black ring-opacity-5 z-50" role="menu">
                    <div class="px-4 py-3 border-b border-gray-100">
                        <p class="text-sm font-medium text-gray-700">{{ current_user.full_name }}</p>
                        <p class="text-xs text-gray-500 truncate">{{ current_user.email }}</p>
                    </div>
                    <a href="{{ url_for('users.profile') }}" class="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-primary-50 hover:text-primary-600" role="menuitem">
                        <i class="ri-user-line ml-2 text-gray-500"></i>
                        الملف الشخصي
                    </a>
                    <a href="{{ url_for('settings.index') }}" class="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-primary-50 hover:text-primary-600" role="menuitem">
                        <i class="ri-settings-line ml-2 text-gray-500"></i>
                        الإعدادات
                    </a>
                    <div class="border-t border-gray-100 my-1"></div>
                    <a href="{{ url_for('auth.logout') }}" class="flex items-center px-4 py-2 text-sm text-red-600 hover:bg-red-50" role="menuitem">
                        <i class="ri-logout-box-line ml-2 text-red-500"></i>
                        تسجيل الخروج
                    </a>
                </div>
            </div>
        </div>
    </div>
</header>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // تهيئة المتغيرات
        const userMenuBtn = document.getElementById('userMenuBtn');
        const userMenu = document.getElementById('userMenu');
        const notificationBtn = document.getElementById('notificationBtn');
        const notificationsMenu = document.getElementById('notificationsMenu');
        const notificationsContainer = document.getElementById('notificationsContainer');
        const notificationUnreadBadge = document.getElementById('notificationUnreadBadge');
        const markAllReadBtnDropdown = document.getElementById('markAllReadBtnDropdown');
        const sidebarToggle = document.getElementById('sidebarToggle');
        const sidebar = document.getElementById('sidebar');

        // إضافة تأثيرات الحركة
        const addAnimations = () => {
            userMenu.classList.add('transition-all', 'duration-300', 'transform');
            notificationsMenu.classList.add('transition-all', 'duration-300', 'transform');
        };

        // تبديل قائمة المستخدم
        const toggleUserMenu = (event) => {
            event.stopPropagation();

            if (userMenu.classList.contains('hidden')) {
                // إخفاء قائمة الإشعارات
                notificationsMenu.classList.add('hidden');

                // إظهار قائمة المستخدم مع تأثير حركي
                userMenu.classList.remove('hidden');
                userMenu.classList.add('animate-fadeIn');
            } else {
                // إخفاء قائمة المستخدم
                userMenu.classList.add('hidden');
                userMenu.classList.remove('animate-fadeIn');
            }
        };

        // تحميل الإشعارات
        const loadNotifications = () => {
            fetch('/api/notifications/recent')
                .then(response => response.json())
                .then(data => {
                    // تحديث عدد الإشعارات غير المقروءة
                    updateNotificationBadge(data.unread_count);

                    // تحديث قائمة الإشعارات
                    if (data.notifications && data.notifications.length > 0) {
                        renderNotifications(data.notifications);
                    } else {
                        showEmptyNotifications();
                    }
                })
                .catch(error => {
                    console.error('Error loading notifications:', error);
                    showNotificationError();
                });
        };

        // تحديث شارة الإشعارات
        const updateNotificationBadge = (count) => {
            if (count > 0) {
                notificationUnreadBadge.textContent = count;
                notificationUnreadBadge.classList.remove('hidden');
                notificationUnreadBadge.classList.add('animate-pulse');
            } else {
                notificationUnreadBadge.classList.add('hidden');
                notificationUnreadBadge.classList.remove('animate-pulse');
            }
        };

        // عرض الإشعارات
        const renderNotifications = (notifications) => {
            let html = '';

            notifications.forEach(notification => {
                // تحديد الأيقونة والخلفية حسب نوع الإشعار
                const { iconClass, bgClass } = getNotificationStyle(notification.type);

                // إنشاء عنصر الإشعار
                html += `
                    <a href="${notification.link || '#'}" class="block px-4 py-3 hover:bg-primary-50 border-b border-gray-100 ${!notification.is_read ? 'bg-primary-50' : ''} transition-colors duration-300">
                        <div class="flex items-start">
                            <div class="flex-shrink-0 ${bgClass} rounded-full p-2 flex items-center justify-center">
                                <i class="${iconClass}"></i>
                            </div>
                            <div class="mr-3 w-full">
                                <p class="text-sm font-medium text-gray-900">${notification.title}</p>
                                <p class="text-xs text-gray-600 mt-1">${notification.message}</p>
                                <p class="text-xs text-gray-400 mt-1">${notification.created_at}</p>
                            </div>
                        </div>
                    </a>
                `;
            });

            notificationsContainer.innerHTML = html;
        };

        // تحديد نمط الإشعار حسب النوع
        const getNotificationStyle = (type) => {
            switch (type) {
                case 'info':
                    return {
                        iconClass: 'ri-information-line text-primary-600',
                        bgClass: 'bg-primary-100'
                    };
                case 'warning':
                    return {
                        iconClass: 'ri-error-warning-line text-yellow-600',
                        bgClass: 'bg-yellow-100'
                    };
                case 'danger':
                    return {
                        iconClass: 'ri-alarm-warning-line text-red-600',
                        bgClass: 'bg-red-100'
                    };
                case 'success':
                    return {
                        iconClass: 'ri-checkbox-circle-line text-green-600',
                        bgClass: 'bg-green-100'
                    };
                default:
                    return {
                        iconClass: 'ri-notification-3-line text-primary-600',
                        bgClass: 'bg-primary-100'
                    };
            }
        };

        // عرض رسالة عند عدم وجود إشعارات
        const showEmptyNotifications = () => {
            notificationsContainer.innerHTML = `
                <div class="p-6 text-center text-gray-500">
                    <div class="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-3">
                        <i class="ri-notification-off-line text-2xl text-gray-400"></i>
                    </div>
                    <p class="text-sm font-medium">لا توجد إشعارات جديدة</p>
                    <p class="text-xs text-gray-400 mt-1">ستظهر الإشعارات الجديدة هنا</p>
                </div>
            `;
        };

        // عرض رسالة خطأ
        const showNotificationError = () => {
            notificationsContainer.innerHTML = `
                <div class="p-6 text-center text-gray-500">
                    <div class="w-16 h-16 bg-red-50 rounded-full flex items-center justify-center mx-auto mb-3">
                        <i class="ri-error-warning-line text-2xl text-red-400"></i>
                    </div>
                    <p class="text-sm font-medium">حدث خطأ أثناء تحميل الإشعارات</p>
                    <p class="text-xs text-gray-400 mt-1">يرجى المحاولة مرة أخرى لاحقًا</p>
                </div>
            `;
        };

        // تبديل قائمة الإشعارات
        const toggleNotificationsMenu = (event) => {
            event.stopPropagation();

            if (notificationsMenu.classList.contains('hidden')) {
                // إخفاء قائمة المستخدم
                userMenu.classList.add('hidden');

                // إظهار قائمة الإشعارات مع تأثير حركي
                notificationsMenu.classList.remove('hidden');
                notificationsMenu.classList.add('animate-fadeIn');

                // تحميل الإشعارات
                loadNotifications();
            } else {
                // إخفاء قائمة الإشعارات
                notificationsMenu.classList.add('hidden');
                notificationsMenu.classList.remove('animate-fadeIn');
            }
        };

        // تحديد جميع الإشعارات كمقروءة
        const markAllAsRead = (event) => {
            event.stopPropagation();

            fetch('/notifications/mark-all-read', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // إعادة تحميل الإشعارات
                    loadNotifications();
                }
            });
        };

        // تبديل الشريط الجانبي
        const toggleSidebar = () => {
            sidebar.classList.toggle('w-64');
            sidebar.classList.toggle('w-20');

            // تبديل ظهور النصوص في الشريط الجانبي
            const sidebarTexts = document.querySelectorAll('#sidebar span, #sidebar h2, .powered-by');
            sidebarTexts.forEach(text => {
                text.classList.toggle('hidden');
            });
        };

        // إغلاق القوائم عند النقر خارجها
        const closeMenusOnClickOutside = (event) => {
            // إغلاق قائمة المستخدم
            if (userMenuBtn && !userMenuBtn.contains(event.target) && userMenu && !userMenu.contains(event.target)) {
                userMenu.classList.add('hidden');
            }

            // إغلاق قائمة الإشعارات
            if (notificationBtn && !notificationBtn.contains(event.target) && notificationsMenu && !notificationsMenu.contains(event.target)) {
                notificationsMenu.classList.add('hidden');
            }
        };

        // تحميل عدد الإشعارات غير المقروءة
        const loadNotificationCount = () => {
            fetch('/api/notifications/count')
                .then(response => response.json())
                .then(data => {
                    updateNotificationBadge(data.count);
                });
        };

        // إضافة مستمعي الأحداث
        const addEventListeners = () => {
            if (userMenuBtn) userMenuBtn.addEventListener('click', toggleUserMenu);
            if (notificationBtn) notificationBtn.addEventListener('click', toggleNotificationsMenu);
            if (markAllReadBtnDropdown) markAllReadBtnDropdown.addEventListener('click', markAllAsRead);
            if (sidebarToggle) sidebarToggle.addEventListener('click', toggleSidebar);
            document.addEventListener('click', closeMenusOnClickOutside);
        };

        // تهيئة الصفحة
        const init = () => {
            addAnimations();
            addEventListeners();
            loadNotificationCount();
        };

        // تنفيذ التهيئة
        init();
    });
</script>