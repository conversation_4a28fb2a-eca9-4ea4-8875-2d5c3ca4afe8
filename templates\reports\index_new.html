<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Nobara - نظام التقارير</title>
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <!-- Remixicon -->
    <link href="https://cdn.jsdelivr.net/npm/remixicon@2.5.0/fonts/remixicon.css" rel="stylesheet">
    <!-- Alpine.js -->
    <script defer src="https://cdn.jsdelivr.net/npm/alpinejs@3.x.x/dist/cdn.min.js"></script>
    <script>
        tailwind.config = {
            darkMode: 'class',
            theme: {
                extend: {
                    colors: {
                        primary: {
                            50: '#eff6ff',
                            100: '#dbeafe',
                            200: '#bfdbfe',
                            300: '#93c5fd',
                            400: '#60a5fa',
                            500: '#3b82f6',
                            600: '#2563eb',
                            700: '#1d4ed8',
                            800: '#1e40af',
                            900: '#1e3a8a',
                            950: '#172554',
                        },
                        dark: {
                            100: '#1E293B',
                            200: '#0F172A',
                            300: '#0B1120',
                            400: '#060A15'
                        }
                    }
                }
            }
        }
    </script>
    <style>
        body {
            font-family: 'Cairo', 'Tajawal', sans-serif;
            transition: background-color 0.3s ease, color 0.3s ease;
        }
        
        .bg-pattern {
            background-color: #f9fafb;
            background-image: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%233B82F6' fill-opacity='0.05'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
        }
        
        .dark .bg-pattern {
            background-color: #0F172A;
            background-image: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%233B82F6' fill-opacity='0.1'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
        }
        
        .report-card {
            transition: all 0.3s ease;
            border: 1px solid #e5e7eb;
        }
        
        .report-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px -5px rgba(59, 130, 246, 0.1), 0 8px 10px -6px rgba(59, 130, 246, 0.1);
            border-color: #93c5fd;
        }
        
        .dark .report-card {
            border-color: #1E293B;
        }
        
        .dark .report-card:hover {
            box-shadow: 0 10px 25px -5px rgba(59, 130, 246, 0.2), 0 8px 10px -6px rgba(59, 130, 246, 0.2);
            border-color: #3b82f6;
        }
        
        .report-icon {
            transition: all 0.3s ease;
        }
        
        .report-card:hover .report-icon {
            transform: scale(1.1);
        }
        
        .powered-by {
            position: fixed;
            bottom: 1rem;
            left: 1rem;
            background-color: #ffffff;
            border-radius: 1rem;
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
            padding: 0.75rem 1rem;
            display: flex;
            align-items: center;
            z-index: 40;
            transition: all 0.3s ease;
            border: 1px solid #e5e7eb;
        }
        
        .dark .powered-by {
            background-color: #1E293B;
            border-color: #2D3748;
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.3), 0 4px 6px -2px rgba(0, 0, 0, 0.2);
        }
        
        .powered-by:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
        }
        
        .dark .powered-by:hover {
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.3), 0 10px 10px -5px rgba(0, 0, 0, 0.2);
        }
    </style>
</head>
<body class="bg-pattern dark:bg-dark-200 dark:text-gray-100" x-data="{ darkMode: localStorage.getItem('darkMode') === 'enabled' }">
    <div class="flex h-screen overflow-hidden">
        <!-- Sidebar -->
        {% include 'partials/sidebar.html' %}

        <!-- Main Content -->
        <div class="flex-1 flex flex-col overflow-hidden">
            <!-- Top Navigation -->
            {% include 'partials/topnav.html' %}

            <!-- Content Area -->
            <main class="flex-1 overflow-y-auto p-6">
                <div class="mb-6 flex justify-between items-center">
                    <div>
                        <h1 class="text-2xl font-bold text-gray-800 dark:text-white">نظام التقارير</h1>
                        <p class="text-gray-600 dark:text-gray-400">عرض وتحليل بيانات المبيعات والمخزون والعملاء والشيفتات</p>
                    </div>
                    <div>
                        <button @click="darkMode = !darkMode; localStorage.setItem('darkMode', darkMode ? 'enabled' : 'disabled'); document.documentElement.classList.toggle('dark')" class="p-2 rounded-full bg-gray-100 dark:bg-dark-100 hover:bg-gray-200 dark:hover:bg-dark-300 transition-colors">
                            <i class="ri-moon-line dark:hidden"></i>
                            <i class="ri-sun-line hidden dark:block"></i>
                        </button>
                    </div>
                </div>

                <!-- تقارير المبيعات -->
                <div class="mb-8">
                    <h2 class="text-xl font-bold text-gray-800 dark:text-white mb-4 flex items-center">
                        <i class="ri-shopping-cart-2-line ml-2 text-primary-500"></i>
                        تقارير المبيعات
                    </h2>
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                        <!-- تقرير المبيعات الأساسي -->
                        <a href="{{ url_for('reports.sales_report') }}" class="report-card bg-white dark:bg-dark-100 rounded-xl p-6">
                            <div class="flex items-center justify-between mb-4">
                                <h3 class="text-lg font-medium text-gray-800 dark:text-white">تقرير المبيعات الأساسي</h3>
                                <div class="w-12 h-12 rounded-full bg-blue-100 dark:bg-blue-900/30 flex items-center justify-center text-blue-600 dark:text-blue-400 report-icon">
                                    <i class="ri-line-chart-line text-xl"></i>
                                </div>
                            </div>
                            <p class="text-gray-600 dark:text-gray-400 mb-4">تحليل المبيعات حسب الفترة الزمنية وطرق الدفع والمنتجات الأكثر مبيعًا.</p>
                            <div class="flex items-center text-primary-600 dark:text-primary-400">
                                <span class="font-medium">عرض التقرير</span>
                                <i class="ri-arrow-left-line mr-1"></i>
                            </div>
                        </a>

                        <!-- تقرير المبيعات التفصيلي -->
                        <a href="{{ url_for('reports.sales_detailed_report') }}" class="report-card bg-white dark:bg-dark-100 rounded-xl p-6">
                            <div class="flex items-center justify-between mb-4">
                                <h3 class="text-lg font-medium text-gray-800 dark:text-white">تقرير المبيعات التفصيلي</h3>
                                <div class="w-12 h-12 rounded-full bg-indigo-100 dark:bg-indigo-900/30 flex items-center justify-center text-indigo-600 dark:text-indigo-400 report-icon">
                                    <i class="ri-file-list-3-line text-xl"></i>
                                </div>
                            </div>
                            <p class="text-gray-600 dark:text-gray-400 mb-4">عرض تفاصيل الفواتير والمبيعات مع إمكانية البحث والتصفية المتقدمة.</p>
                            <div class="flex items-center text-primary-600 dark:text-primary-400">
                                <span class="font-medium">عرض التقرير</span>
                                <i class="ri-arrow-left-line mr-1"></i>
                            </div>
                        </a>

                        <!-- تقرير الاتجاهات والتنبؤ -->
                        <a href="{{ url_for('reports.trends_forecast_report') }}" class="report-card bg-white dark:bg-dark-100 rounded-xl p-6">
                            <div class="flex items-center justify-between mb-4">
                                <h3 class="text-lg font-medium text-gray-800 dark:text-white">تقرير الاتجاهات والتنبؤ</h3>
                                <div class="w-12 h-12 rounded-full bg-purple-100 dark:bg-purple-900/30 flex items-center justify-center text-purple-600 dark:text-purple-400 report-icon">
                                    <i class="ri-line-chart-line text-xl"></i>
                                </div>
                            </div>
                            <p class="text-gray-600 dark:text-gray-400 mb-4">تحليل اتجاهات المبيعات والتنبؤ بالمبيعات المستقبلية.</p>
                            <div class="flex items-center text-primary-600 dark:text-primary-400">
                                <span class="font-medium">عرض التقرير</span>
                                <i class="ri-arrow-left-line mr-1"></i>
                            </div>
                        </a>
                    </div>
                </div>

                <!-- تقارير الشيفتات والخزينة -->
                <div class="mb-8">
                    <h2 class="text-xl font-bold text-gray-800 dark:text-white mb-4 flex items-center">
                        <i class="ri-money-dollar-box-line ml-2 text-primary-500"></i>
                        تقارير الشيفتات والخزينة
                    </h2>
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                        <!-- تقرير إغلاق الشيفت -->
                        <a href="{{ url_for('reports.shift_closure_report') }}" class="report-card bg-white dark:bg-dark-100 rounded-xl p-6">
                            <div class="flex items-center justify-between mb-4">
                                <h3 class="text-lg font-medium text-gray-800 dark:text-white">تقرير إغلاق الشيفت</h3>
                                <div class="w-12 h-12 rounded-full bg-green-100 dark:bg-green-900/30 flex items-center justify-center text-green-600 dark:text-green-400 report-icon">
                                    <i class="ri-time-line text-xl"></i>
                                </div>
                            </div>
                            <p class="text-gray-600 dark:text-gray-400 mb-4">تقرير مفصل عن الشيفتات المغلقة مع إحصائيات المبيعات والمعاملات.</p>
                            <div class="flex items-center text-primary-600 dark:text-primary-400">
                                <span class="font-medium">عرض التقرير</span>
                                <i class="ri-arrow-left-line mr-1"></i>
                            </div>
                        </a>

                        <!-- تقرير معاملات الخزينة -->
                        <a href="{{ url_for('reports.cash_transactions_report') }}" class="report-card bg-white dark:bg-dark-100 rounded-xl p-6">
                            <div class="flex items-center justify-between mb-4">
                                <h3 class="text-lg font-medium text-gray-800 dark:text-white">تقرير معاملات الخزينة</h3>
                                <div class="w-12 h-12 rounded-full bg-yellow-100 dark:bg-yellow-900/30 flex items-center justify-center text-yellow-600 dark:text-yellow-400 report-icon">
                                    <i class="ri-exchange-dollar-line text-xl"></i>
                                </div>
                            </div>
                            <p class="text-gray-600 dark:text-gray-400 mb-4">تقرير عن جميع معاملات الخزينة من إيداعات وسحوبات وتحويلات.</p>
                            <div class="flex items-center text-primary-600 dark:text-primary-400">
                                <span class="font-medium">عرض التقرير</span>
                                <i class="ri-arrow-left-line mr-1"></i>
                            </div>
                        </a>

                        <!-- تقرير الإغلاق اليومي -->
                        <a href="{{ url_for('reports.daily_closure_report') }}" class="report-card bg-white dark:bg-dark-100 rounded-xl p-6">
                            <div class="flex items-center justify-between mb-4">
                                <h3 class="text-lg font-medium text-gray-800 dark:text-white">تقرير الإغلاق اليومي</h3>
                                <div class="w-12 h-12 rounded-full bg-red-100 dark:bg-red-900/30 flex items-center justify-center text-red-600 dark:text-red-400 report-icon">
                                    <i class="ri-calendar-check-line text-xl"></i>
                                </div>
                            </div>
                            <p class="text-gray-600 dark:text-gray-400 mb-4">تقرير الإغلاق اليومي للخزينة مع ملخص المبيعات والمشتريات والمعاملات.</p>
                            <div class="flex items-center text-primary-600 dark:text-primary-400">
                                <span class="font-medium">عرض التقرير</span>
                                <i class="ri-arrow-left-line mr-1"></i>
                            </div>
                        </a>
                    </div>
                </div>

                <!-- تقارير المخزون -->
                <div class="mb-8">
                    <h2 class="text-xl font-bold text-gray-800 dark:text-white mb-4 flex items-center">
                        <i class="ri-store-2-line ml-2 text-primary-500"></i>
                        تقارير المخزون
                    </h2>
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                        <!-- تقرير حالة المخزون -->
                        <a href="{{ url_for('reports.inventory_report') }}" class="report-card bg-white dark:bg-dark-100 rounded-xl p-6">
                            <div class="flex items-center justify-between mb-4">
                                <h3 class="text-lg font-medium text-gray-800 dark:text-white">تقرير حالة المخزون</h3>
                                <div class="w-12 h-12 rounded-full bg-teal-100 dark:bg-teal-900/30 flex items-center justify-center text-teal-600 dark:text-teal-400 report-icon">
                                    <i class="ri-stack-line text-xl"></i>
                                </div>
                            </div>
                            <p class="text-gray-600 dark:text-gray-400 mb-4">تقرير عن حالة المخزون الحالية وقيمة المخزون والمنتجات منخفضة المخزون.</p>
                            <div class="flex items-center text-primary-600 dark:text-primary-400">
                                <span class="font-medium">عرض التقرير</span>
                                <i class="ri-arrow-left-line mr-1"></i>
                            </div>
                        </a>

                        <!-- تقرير حركة المخزون -->
                        <a href="{{ url_for('reports.inventory_movement_report') }}" class="report-card bg-white dark:bg-dark-100 rounded-xl p-6">
                            <div class="flex items-center justify-between mb-4">
                                <h3 class="text-lg font-medium text-gray-800 dark:text-white">تقرير حركة المخزون</h3>
                                <div class="w-12 h-12 rounded-full bg-cyan-100 dark:bg-cyan-900/30 flex items-center justify-center text-cyan-600 dark:text-cyan-400 report-icon">
                                    <i class="ri-arrow-left-right-line text-xl"></i>
                                </div>
                            </div>
                            <p class="text-gray-600 dark:text-gray-400 mb-4">تقرير عن حركة المخزون من إضافات وسحوبات وتحويلات بين المخازن.</p>
                            <div class="flex items-center text-primary-600 dark:text-primary-400">
                                <span class="font-medium">عرض التقرير</span>
                                <i class="ri-arrow-left-line mr-1"></i>
                            </div>
                        </a>

                        <!-- تقرير تقييم المخزون -->
                        <a href="{{ url_for('reports.inventory_valuation_report') }}" class="report-card bg-white dark:bg-dark-100 rounded-xl p-6">
                            <div class="flex items-center justify-between mb-4">
                                <h3 class="text-lg font-medium text-gray-800 dark:text-white">تقرير تقييم المخزون</h3>
                                <div class="w-12 h-12 rounded-full bg-pink-100 dark:bg-pink-900/30 flex items-center justify-center text-pink-600 dark:text-pink-400 report-icon">
                                    <i class="ri-funds-line text-xl"></i>
                                </div>
                            </div>
                            <p class="text-gray-600 dark:text-gray-400 mb-4">تقرير عن قيمة المخزون وتكلفة البضاعة المباعة والأرباح الإجمالية.</p>
                            <div class="flex items-center text-primary-600 dark:text-primary-400">
                                <span class="font-medium">عرض التقرير</span>
                                <i class="ri-arrow-left-line mr-1"></i>
                            </div>
                        </a>
                    </div>
                </div>
            </main>
        </div>
    </div>
    
    <!-- Powered By -->
    <div class="powered-by">
        <div class="w-8 h-8 rounded-full bg-primary-50 dark:bg-primary-900/50 flex items-center justify-center ml-2 text-primary-600 dark:text-primary-400">
            <i class="ri-code-s-slash-line"></i>
        </div>
        <div>
            <div class="text-xs text-gray-600 dark:text-gray-400">
                Powered By <span class="font-bold text-primary-600 dark:text-primary-400">ENG/ Fouad Saber</span>
            </div>
            <div class="text-xs text-gray-500 dark:text-gray-500">Tel: 01020073527</div>
        </div>
    </div>
    
    <script>
        // تفعيل وضع الظلام إذا كان مخزنًا
        if (localStorage.getItem('darkMode') === 'enabled') {
            document.documentElement.classList.add('dark');
        }
    </script>
</body>
</html>
