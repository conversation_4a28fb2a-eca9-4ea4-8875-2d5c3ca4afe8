import os
import logging
import sys
import socket
import webbrowser

# إضافة مسار الوحدات المدمجة إلى مسار البحث
vendor_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'vendor_modules')
if vendor_path not in sys.path:
    sys.path.insert(0, vendor_path)
    print(f"تم إضافة مسار الوحدات المدمجة: {vendor_path}")

from app import app  # noqa: F401
from routes import register_routes
from flask_login import LoginManager
from flask import redirect, url_for, flash, request, render_template
from models import User
from security import Security

# إعداد التسجيل
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# تهيئة نظام تسجيل الدخول
login_manager = LoginManager()
login_manager.init_app(app)
login_manager.login_view = 'auth.login'

@login_manager.user_loader
def load_user(user_id):
    return User.query.get(int(user_id))

# الكشف عن وضع التشغيل
app.debug = os.environ.get('FLASK_ENV', 'development') == 'development'

# تسجيل جميع الروتس
register_routes(app)

def get_local_ip():
    """الحصول على عنوان IP المحلي للجهاز"""
    try:
        # إنشاء اتصال مؤقت للحصول على عنوان IP المحلي
        s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        s.connect(("*******", 80))
        local_ip = s.getsockname()[0]
        s.close()
        return local_ip
    except Exception as e:
        logger.error(f"خطأ في الحصول على عنوان IP المحلي: {str(e)}")
        return "127.0.0.1"

if __name__ == "__main__":
    # الحصول على عنوان IP المحلي
    local_ip = get_local_ip()
    port = 5000
    url = f"http://{local_ip}:{port}"

    logger.info(f"جاري بدء التطبيق على العنوان: {url}")

    # فتح المتصفح تلقائيًا
    webbrowser.open(url)

    # تشغيل التطبيق
    app.run(host="0.0.0.0", port=port, debug=app.debug)
