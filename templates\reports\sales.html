{% extends 'layout.html' %}

{% block title %}تقرير المبيعات{% endblock %}

{% block styles %}
<style>
    .report-card {
        transition: all 0.3s ease;
    }
    .report-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 15px rgba(0, 0, 0, 0.1);
    }
</style>
<!-- Chart.js -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
{% endblock %}

{% block page_content %}
<div class="container mx-auto px-4 py-8">
    <div class="mb-6 flex items-center justify-between">
        <div>
            <h1 class="text-2xl font-bold text-gray-800">تقرير المبيعات</h1>
            <p class="text-gray-600">تحليل المبيعات وعرض المؤشرات الرئيسية</p>
        </div>
        <div class="flex space-x-2 space-x-reverse">
            <a href="{{ url_for('reports.sales_detailed_report') }}" class="px-4 py-2 bg-indigo-100 text-indigo-700 rounded-md hover:bg-indigo-200 transition-all">
                <i class="ri-list-check"></i>
                تقرير المبيعات التفصيلي
            </a>
            <a href="{{ url_for('reports.sales_index') }}" class="px-4 py-2 bg-blue-100 text-blue-700 rounded-md hover:bg-blue-200 transition-all">
                <i class="ri-arrow-right-line"></i>
                العودة لتقارير المبيعات
            </a>
            <a href="{{ url_for('reports.index') }}" class="px-4 py-2 bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200 transition-all">
                <i class="ri-home-line"></i>
                الرئيسية
            </a>
        </div>
    </div>

    <!-- Filters -->
    <div class="bg-white p-4 rounded-lg shadow-sm mb-6">
        <form action="{{ url_for('reports.sales_report') }}" method="get" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
            <div>
                <label for="date_from" class="block text-sm font-medium text-gray-700 mb-1">من تاريخ</label>
                <input type="date" id="date_from" name="date_from" value="{{ date_from }}"
                       class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500">
            </div>
            <div>
                <label for="date_to" class="block text-sm font-medium text-gray-700 mb-1">إلى تاريخ</label>
                <input type="date" id="date_to" name="date_to" value="{{ date_to }}"
                       class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500">
            </div>
            <div>
                <label for="payment_method" class="block text-sm font-medium text-gray-700 mb-1">طريقة الدفع</label>
                <select id="payment_method" name="payment_method" class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500">
                    <option value="">الكل</option>
                    {% for method in available_payment_methods %}
                    <option value="{{ method }}" {% if method == payment_method %}selected{% endif %}>
                        {% if method == 'cash' %}نقدي{% elif method == 'card' %}بطاقة{% else %}{{ method }}{% endif %}
                    </option>
                    {% endfor %}
                </select>
            </div>
            <div>
                <label for="status" class="block text-sm font-medium text-gray-700 mb-1">الحالة</label>
                <select id="status" name="status" class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500">
                    <option value="">الكل</option>
                    {% for s in available_statuses %}
                    <option value="{{ s }}" {% if s == status %}selected{% endif %}>
                        {% if s == 'completed' %}مكتمل{% elif s == 'pending' %}معلق{% elif s == 'cancelled' %}ملغي{% else %}{{ s }}{% endif %}
                    </option>
                    {% endfor %}
                </select>
            </div>
            <div>
                <label for="view_type" class="block text-sm font-medium text-gray-700 mb-1">عرض حسب</label>
                <select id="view_type" name="view_type" class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500">
                    <option value="daily" {% if view_type == 'daily' %}selected{% endif %}>يومي</option>
                    <option value="weekly" {% if view_type == 'weekly' %}selected{% endif %}>أسبوعي</option>
                    <option value="monthly" {% if view_type == 'monthly' %}selected{% endif %}>شهري</option>
                    <option value="yearly" {% if view_type == 'yearly' %}selected{% endif %}>سنوي</option>
                </select>
            </div>
            <div class="flex items-end">
                <button type="submit" class="bg-indigo-600 text-white px-4 py-2 rounded-md hover:bg-indigo-700 transition-all">
                    <i class="ri-filter-3-line ml-1"></i>
                    تطبيق الفلتر
                </button>
            </div>
            <div class="flex items-end">
                <a href="{{ url_for('reports.export_sales_report', date_from=date_from, date_to=date_to, payment_method=payment_method, status=status, export_type='summary') }}"
                   class="bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700 transition-all">
                    <i class="ri-file-excel-line ml-1"></i>
                    تصدير التقرير
                </a>
            </div>
        </form>
    </div>

    <!-- Sales Summary -->
    <div class="bg-white p-6 rounded-lg shadow-sm mb-6">
        <h2 class="text-lg font-bold text-gray-800 mb-4">ملخص المبيعات</h2>
        <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
            <div class="p-4 bg-indigo-50 rounded-lg">
                <h3 class="text-sm font-medium text-gray-500 mb-1">إجمالي المبيعات</h3>
                <p class="text-2xl font-bold text-indigo-600">
                    {{ "%.2f"|format(total_sales) }} ج.م
                </p>
            </div>
            <div class="p-4 bg-blue-50 rounded-lg">
                <h3 class="text-sm font-medium text-gray-500 mb-1">عدد الطلبات</h3>
                <p class="text-2xl font-bold text-blue-600">
                    {{ total_orders }}
                </p>
            </div>
            <div class="p-4 bg-green-50 rounded-lg">
                <h3 class="text-sm font-medium text-gray-500 mb-1">متوسط قيمة الطلب</h3>
                <p class="text-2xl font-bold text-green-600">
                    {{ "%.2f"|format(avg_order_value) }} ج.م
                </p>
            </div>
            <div class="p-4 bg-purple-50 rounded-lg">
                <h3 class="text-sm font-medium text-gray-500 mb-1">الفترة الزمنية</h3>
                <p class="text-lg font-bold text-purple-600">
                    {{ date_from }} - {{ date_to }}
                </p>
            </div>
        </div>
    </div>

    <!-- Payment Methods and Status -->
    <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
        <!-- Payment Methods -->
        <div class="bg-white p-6 rounded-lg shadow-sm">
            <h2 class="text-lg font-bold text-gray-800 mb-4">المبيعات حسب طرق الدفع</h2>
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                طريقة الدفع
                            </th>
                            <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                عدد الطلبات
                            </th>
                            <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                إجمالي المبيعات
                            </th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        {% for payment in payment_methods_data %}
                        <tr>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                {{ payment.method }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                {{ payment.orders_count }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                {{ "%.2f"|format(payment.total_amount) }} ج.م
                            </td>
                        </tr>
                        {% else %}
                        <tr>
                            <td colspan="3" class="px-6 py-4 text-center text-gray-500">
                                لا توجد بيانات للفترة المحددة
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>

        <!-- Status -->
        <div class="bg-white p-6 rounded-lg shadow-sm">
            <h2 class="text-lg font-bold text-gray-800 mb-4">المبيعات حسب الحالة</h2>
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                الحالة
                            </th>
                            <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                عدد الطلبات
                            </th>
                            <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                إجمالي المبيعات
                            </th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        {% for status_item in status_data %}
                        <tr>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                {{ status_item.status }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                {{ status_item.orders_count }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                {{ "%.2f"|format(status_item.total_amount) }} ج.م
                            </td>
                        </tr>
                        {% else %}
                        <tr>
                            <td colspan="3" class="px-6 py-4 text-center text-gray-500">
                                لا توجد بيانات للفترة المحددة
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- Top Products and Customers -->
    <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
        <!-- Top Products -->
        <div class="bg-white p-6 rounded-lg shadow-sm">
            <h2 class="text-lg font-bold text-gray-800 mb-4">المنتجات الأكثر مبيعاً</h2>
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                المنتج
                            </th>
                            <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                الكمية المباعة
                            </th>
                            <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                إجمالي المبيعات
                            </th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        {% for product in top_products_data %}
                        <tr>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                {{ product.name }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                {{ product.quantity_sold }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                {{ "%.2f"|format(product.total_amount) }} ج.م
                            </td>
                        </tr>
                        {% else %}
                        <tr>
                            <td colspan="3" class="px-6 py-4 text-center text-gray-500">
                                لا توجد بيانات للفترة المحددة
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>

        <!-- Top Customers -->
        <div class="bg-white p-6 rounded-lg shadow-sm">
            <h2 class="text-lg font-bold text-gray-800 mb-4">العملاء الأكثر شراءً</h2>
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                العميل
                            </th>
                            <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                عدد الطلبات
                            </th>
                            <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                إجمالي المشتريات
                            </th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        {% for customer in top_customers_data %}
                        <tr>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                {{ customer.name }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                {{ customer.orders_count }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                {{ "%.2f"|format(customer.total_amount) }} ج.م
                            </td>
                        </tr>
                        {% else %}
                        <tr>
                            <td colspan="3" class="px-6 py-4 text-center text-gray-500">
                                لا توجد بيانات للفترة المحددة
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- Sales Chart -->
    <div class="bg-white p-6 rounded-lg shadow-sm mb-6">
        <h2 class="text-lg font-bold text-gray-800 mb-4">رسم بياني للمبيعات</h2>
        <div class="h-80">
            <canvas id="salesChart"></canvas>
        </div>
    </div>

    <!-- Sales by Employee -->
    <div class="bg-white p-6 rounded-lg shadow-sm mb-6">
        <h2 class="text-lg font-bold text-gray-800 mb-4">المبيعات حسب الموظفين</h2>
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                            الموظف
                        </th>
                        <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                            عدد الطلبات
                        </th>
                        <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                            إجمالي المبيعات
                        </th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    {% for employee in employees_data %}
                    <tr>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            {{ employee.name }}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            {{ employee.orders_count }}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            {{ "%.2f"|format(employee.total_amount) }} ج.م
                        </td>
                    </tr>
                    {% else %}
                    <tr>
                        <td colspan="3" class="px-6 py-4 text-center text-gray-500">
                            لا توجد بيانات للفترة المحددة
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>

    <!-- Sales Table -->
    <div class="bg-white rounded-lg shadow-sm overflow-hidden mb-6">
        <h2 class="text-lg font-bold text-gray-800 p-6 border-b">تفاصيل المبيعات</h2>
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                            التاريخ
                        </th>
                        <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                            عدد الطلبات
                        </th>
                        <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                            إجمالي المبيعات
                        </th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    {% for sale in sales_data %}
                    <tr>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            {{ sale.date }}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            {{ sale.orders_count }}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            {{ "%.2f"|format(sale.total_amount) }} ج.م
                        </td>
                    </tr>
                    {% else %}
                    <tr>
                        <td colspan="3" class="px-6 py-4 text-center text-gray-500">
                            لا توجد بيانات للفترة المحددة
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Sales Chart
        const ctx = document.getElementById('salesChart').getContext('2d');
        const salesChart = new Chart(ctx, {
            type: 'bar',
            data: {
                labels: [
                    {% for sale in sales_data %}
                        '{{ sale.date }}'{% if not loop.last %},{% endif %}
                    {% endfor %}
                ],
                datasets: [{
                    label: 'المبيعات',
                    data: [
                        {% for sale in sales_data %}
                            {{ sale.total_amount }}{% if not loop.last %},{% endif %}
                        {% endfor %}
                    ],
                    backgroundColor: 'rgba(99, 102, 241, 0.5)',
                    borderColor: 'rgba(99, 102, 241, 1)',
                    borderWidth: 1
                }, {
                    label: 'عدد الطلبات',
                    data: [
                        {% for sale in sales_data %}
                            {{ sale.orders_count }}{% if not loop.last %},{% endif %}
                        {% endfor %}
                    ],
                    backgroundColor: 'rgba(59, 130, 246, 0.5)',
                    borderColor: 'rgba(59, 130, 246, 1)',
                    borderWidth: 1,
                    type: 'line',
                    yAxisID: 'y1'
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true,
                        title: {
                            display: true,
                            text: 'المبيعات (ج.م)'
                        }
                    },
                    y1: {
                        beginAtZero: true,
                        position: 'right',
                        title: {
                            display: true,
                            text: 'عدد الطلبات'
                        },
                        grid: {
                            drawOnChartArea: false
                        }
                    }
                }
            }
        });
    });
</script>
{% endblock %}

