<!DOCTYPE html>
<html lang="ar" dir="rtl" class="light">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>FoUaD - إدارة المرتجعات</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdn.jsdelivr.net/npm/remixicon@2.5.0/fonts/remixicon.css" rel="stylesheet">
    <!-- إضافة مكتبة SweetAlert2 -->
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <script>
        tailwind.config = {
            darkMode: 'class',
            theme: {
                extend: {
                    colors: {
                        primary: '#3B82F6',
                        secondary: '#10b981',
                        dark: {
                            100: '#1E293B',
                            200: '#0F172A',
                            300: '#0B1120',
                            400: '#060A15'
                        },
                        accent: {
                            blue: '#3B82F6',
                            indigo: '#6366F1',
                            purple: '#8B5CF6',
                            green: '#10B981',
                            red: '#EF4444',
                            orange: '#F97316',
                            yellow: '#F59E0B'
                        }
                    }
                }
            }
        }
    </script>
    <style>
        body {
            font-family: 'Tajawal', sans-serif;
            background-color: #f9fafb;
            transition: background-color 0.3s ease, color 0.3s ease;
        }
        body.dark {
            background-color: #0F172A;
            color: #E2E8F0;
        }
        .scrollbar-hide::-webkit-scrollbar {
            display: none;
        }
        .scrollbar-hide {
            -ms-overflow-style: none;
            scrollbar-width: none;
        }
        .bg-pattern {
            background-color: #f9fafb;
            background-image: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%233B82F6' fill-opacity='0.05'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
            transition: background-color 0.3s ease;
        }
        .dark .bg-pattern {
            background-color: #0F172A;
            background-image: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%233B82F6' fill-opacity='0.1'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
        }
        .tooltip {
            position: relative;
        }
        .tooltip:hover::after {
            content: attr(data-tooltip);
            position: absolute;
            bottom: 100%;
            left: 50%;
            transform: translateX(-50%);
            padding: 0.25rem 0.5rem;
            background-color: #1F2937;
            color: white;
            font-size: 0.75rem;
            border-radius: 0.25rem;
            white-space: nowrap;
            z-index: 10;
            margin-bottom: 0.25rem;
        }
    </style>
</head>
<body class="bg-pattern">
    <div class="flex h-screen overflow-hidden">
        <!-- Sidebar -->
        {% include 'partials/sidebar.html' %}

        <!-- Main Content -->
        <div class="flex-1 flex flex-col overflow-hidden">
            <!-- Top Navigation -->
            {% include 'partials/topnav.html' %}

            <!-- Content Area -->
            <main class="flex-1 overflow-y-auto p-6">
                <div class="mb-6 flex flex-col md:flex-row md:items-center justify-between gap-4">
                    <div>
                        <h1 class="text-2xl font-bold text-gray-800 dark:text-white flex items-center">
                            <i class="ri-arrow-go-back-line text-red-500 dark:text-red-400 ml-3"></i>
                            <span>مرتجعات المبيعات</span>
                        </h1>
                        <p class="text-gray-600 dark:text-gray-400 mt-1">إدارة وعرض سجلات مرتجعات المبيعات</p>
                    </div>

                    <div class="flex flex-wrap gap-3 mt-4 md:mt-0">
                        <a href="{{ url_for('pos.index') }}"
                           class="bg-gradient-to-r from-primary to-blue-600 dark:from-blue-600 dark:to-blue-700 text-white px-4 py-2.5 rounded-lg shadow-md hover:shadow-lg hover:shadow-blue-500/20 dark:hover:shadow-blue-800/20 transition-all duration-300 flex items-center gap-2 pulse-animation">
                            <i class="ri-add-line text-lg"></i>
                            <span class="font-medium">إنشاء مرتجع جديد</span>
                        </a>

                        <button type="button" id="filterToggleBtn"
                           class="bg-gradient-to-r from-purple-500 to-indigo-600 dark:from-purple-600 dark:to-indigo-700 text-white px-4 py-2.5 rounded-lg shadow-md hover:shadow-lg hover:shadow-purple-500/20 dark:hover:shadow-purple-800/20 transition-all duration-300 flex items-center gap-2">
                            <i class="ri-filter-3-line text-lg"></i>
                            <span class="font-medium">فلترة المرتجعات</span>
                        </button>
                    </div>
                </div>

                <!-- Stats Cards -->
                <div class="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
                    <!-- إجمالي المرتجعات -->
                    <div class="bg-white dark:bg-dark-100 rounded-lg p-4 transition-all duration-300 hover:shadow-md card-hover border border-gray-200 dark:border-gray-700 shadow-sm relative overflow-hidden">
                        <div class="absolute top-0 left-0 w-1 h-full bg-red-500 dark:bg-red-600"></div>
                        <div class="flex justify-between items-start">
                            <div>
                                <h3 class="text-gray-500 dark:text-gray-400 text-xs font-medium mb-1 flex items-center">
                                    <i class="ri-funds-line ml-1"></i>
                                    إجمالي المرتجعات
                                </h3>
                                <p class="text-xl font-bold text-gray-800 dark:text-white">{{ "%.2f"|format(stats.total_returns) }} <span class="text-sm">ج.م</span></p>
                                <div class="flex items-center mt-1">
                                    <span class="text-xs text-gray-500 dark:text-gray-400">
                                        {{ stats.total_returns_count }} عملية إرجاع
                                    </span>
                                </div>
                            </div>
                            <div class="w-10 h-10 rounded-lg bg-red-50 dark:bg-red-900/20 flex items-center justify-center text-red-500 dark:text-red-400 shadow-sm">
                                <i class="ri-arrow-go-back-line text-xl"></i>
                            </div>
                        </div>
                    </div>

                    <!-- مرتجعات اليوم -->
                    <div class="bg-white dark:bg-dark-100 rounded-lg p-4 transition-all duration-300 hover:shadow-md card-hover border border-gray-200 dark:border-gray-700 shadow-sm relative overflow-hidden">
                        <div class="absolute top-0 left-0 w-1 h-full bg-green-500 dark:bg-green-600"></div>
                        <div class="flex justify-between items-start">
                            <div>
                                <h3 class="text-gray-500 dark:text-gray-400 text-xs font-medium mb-1 flex items-center">
                                    <i class="ri-calendar-todo-line ml-1"></i>
                                    مرتجعات اليوم
                                </h3>
                                <p class="text-xl font-bold text-gray-800 dark:text-white">{{ "%.2f"|format(stats.today_returns) }} <span class="text-sm">ج.م</span></p>
                                <div class="flex items-center mt-1">
                                    <span class="text-xs text-gray-500 dark:text-gray-400">
                                        {{ stats.today_returns_count }} عملية إرجاع
                                    </span>
                                </div>
                            </div>
                            <div class="w-10 h-10 rounded-lg bg-green-50 dark:bg-green-900/20 flex items-center justify-center text-green-500 dark:text-green-400 shadow-sm">
                                <i class="ri-calendar-check-line text-xl"></i>
                            </div>
                        </div>
                    </div>

                    <!-- مرتجعات الشهر -->
                    <div class="bg-white dark:bg-dark-100 rounded-lg p-4 transition-all duration-300 hover:shadow-md card-hover border border-gray-200 dark:border-gray-700 shadow-sm relative overflow-hidden">
                        <div class="absolute top-0 left-0 w-1 h-full bg-purple-500 dark:bg-purple-600"></div>
                        <div class="flex justify-between items-start">
                            <div>
                                <h3 class="text-gray-500 dark:text-gray-400 text-xs font-medium mb-1 flex items-center">
                                    <i class="ri-calendar-line ml-1"></i>
                                    مرتجعات الشهر
                                </h3>
                                <p class="text-xl font-bold text-gray-800 dark:text-white">{{ "%.2f"|format(stats.month_returns) }} <span class="text-sm">ج.م</span></p>
                                <div class="flex items-center mt-1">
                                    <span class="text-xs text-gray-500 dark:text-gray-400">
                                        {{ stats.month_returns_count }} عملية إرجاع
                                    </span>
                                </div>
                            </div>
                            <div class="w-10 h-10 rounded-lg bg-purple-50 dark:bg-purple-900/20 flex items-center justify-center text-purple-500 dark:text-purple-400 shadow-sm">
                                <i class="ri-bar-chart-line text-xl"></i>
                            </div>
                        </div>
                    </div>

                    <!-- طرق الاسترداد -->
                    <div class="bg-white dark:bg-dark-100 rounded-lg p-4 transition-all duration-300 hover:shadow-md card-hover border border-gray-200 dark:border-gray-700 shadow-sm relative overflow-hidden">
                        <div class="absolute top-0 left-0 w-1 h-full bg-orange-500 dark:bg-orange-600"></div>
                        <div class="flex justify-between items-start">
                            <div>
                                <h3 class="text-gray-500 dark:text-gray-400 text-xs font-medium mb-1 flex items-center">
                                    <i class="ri-bank-card-line ml-1"></i>
                                    طرق الاسترداد
                                </h3>
                                {% set cash_returns = stats.returns_by_payment.get('cash', 0) %}
                                {% set card_returns = stats.returns_by_payment.get('card', 0) %}
                                {% set total = cash_returns + card_returns %}
                                <p class="text-xl font-bold text-gray-800 dark:text-white">
                                    {% if total > 0 %}
                                        {{ "%.0f"|format(cash_returns / total * 100) }}٪ <span class="text-sm">نقدي</span>
                                    {% else %}
                                        0٪ <span class="text-sm">نقدي</span>
                                    {% endif %}
                                </p>
                                <div class="flex items-center mt-1">
                                    <span class="text-xs text-gray-500 dark:text-gray-400">
                                        {{ "%.0f"|format((card_returns / total * 100) if total > 0 else 0) }}٪ بطاقة
                                    </span>
                                </div>
                            </div>
                            <div class="w-10 h-10 rounded-lg bg-orange-50 dark:bg-orange-900/20 flex items-center justify-center text-orange-500 dark:text-orange-400 shadow-sm">
                                <i class="ri-wallet-3-line text-xl"></i>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Returns Table -->
                <div class="bg-white dark:bg-dark-100 rounded-lg overflow-hidden border border-gray-200 dark:border-gray-700 shadow-sm">
                    <div class="p-4 bg-gradient-to-r from-red-50 to-white dark:from-red-900/10 dark:to-dark-100 border-b border-gray-200 dark:border-gray-700 flex justify-between items-center">
                        <h3 class="text-base font-bold text-gray-800 dark:text-white flex items-center">
                            <i class="ri-arrow-go-back-line ml-2 text-red-500 dark:text-red-400"></i>
                            <span>قائمة المرتجعات</span>
                        </h3>
                        <div class="flex items-center gap-2">
                            <span class="text-xs text-gray-500 dark:text-gray-400 bg-white dark:bg-dark-200 px-2 py-1 rounded-md shadow-sm">
                                <i class="ri-calendar-line ml-1"></i>
                                <span>{{ stats.month_returns_count }} مرتجع هذا الشهر</span>
                            </span>
                        </div>
                    </div>

                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                            <thead class="bg-gray-50 dark:bg-dark-200">
                                <tr>
                                    <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">رقم المرتجع</th>
                                    <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">رقم الفاتورة</th>
                                    <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">العميل</th>
                                    <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">المخزن</th>
                                    <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">المبلغ</th>
                                    <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">طريقة الاسترداد</th>
                                    <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">التاريخ</th>
                                    <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white dark:bg-dark-100 divide-y divide-gray-200 dark:divide-gray-700">
                                {% for return in returns.items %}
                                <tr class="hover:bg-gray-50 dark:hover:bg-dark-200 transition-colors duration-150">
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm font-medium text-gray-900 dark:text-white">{{ return.reference_number }}</div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm text-gray-900 dark:text-white">{{ return.order.invoice_number }}</div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm text-gray-900 dark:text-white">
                                            {% if return.order.customer %}
                                                {{ return.order.customer.name }}
                                            {% else %}
                                                عميل نقدي
                                            {% endif %}
                                        </div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm text-gray-900 dark:text-white">
                                            {% if return.order.warehouse %}
                                                {{ return.order.warehouse.name }}
                                            {% else %}
                                                المخزن الافتراضي
                                            {% endif %}
                                        </div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm font-bold text-red-500 dark:text-red-400">{{ "%.2f"|format(return.total_amount) }} ج.م</div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full
                                            {% if return.payment_method == 'cash' %}
                                                bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400
                                            {% elif return.payment_method == 'card' %}
                                                bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-400
                                            {% else %}
                                                bg-purple-100 text-purple-800 dark:bg-purple-900/30 dark:text-purple-400
                                            {% endif %}
                                        ">
                                            {% if return.payment_method == 'cash' %}
                                                نقدي
                                            {% elif return.payment_method == 'card' %}
                                                بطاقة
                                            {% elif return.payment_method == 'store_credit' %}
                                                رصيد متجر
                                            {% else %}
                                                {{ return.payment_method }}
                                            {% endif %}
                                        </span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm text-gray-500 dark:text-gray-400">{{ return.created_at.strftime('%Y-%m-%d %H:%M') }}</div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                        <div class="flex items-center space-x-2 space-x-reverse">
                                            <a href="{{ url_for('returns.details', id=return.id) }}"
                                               class="bg-blue-50 dark:bg-blue-900/20 text-blue-600 dark:text-blue-400 hover:bg-blue-100 dark:hover:bg-blue-900/30 p-2 rounded-lg transition-all tooltip"
                                               data-tooltip="عرض التفاصيل">
                                                <i class="ri-eye-line text-lg"></i>
                                            </a>

                                            <a href="{{ url_for('returns.print_return', id=return.id) }}" target="_blank"
                                               class="bg-green-50 dark:bg-green-900/20 text-green-600 dark:text-green-400 hover:bg-green-100 dark:hover:bg-green-900/30 p-2 rounded-lg transition-all tooltip"
                                               data-tooltip="طباعة إيصال المرتجع">
                                                <i class="ri-printer-line text-lg"></i>
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                                {% else %}
                                <tr>
                                    <td colspan="7" class="px-6 py-12 text-center">
                                        <div class="flex flex-col items-center">
                                            <div class="w-20 h-20 rounded-full bg-gray-100 dark:bg-dark-300 flex items-center justify-center text-gray-400 dark:text-gray-500 mb-4">
                                                <i class="ri-arrow-go-back-line text-4xl"></i>
                                            </div>
                                            <p class="text-gray-600 dark:text-gray-300 mb-2 text-lg">لا توجد مرتجعات مطابقة</p>
                                            <p class="text-gray-500 dark:text-gray-400 text-sm">حاول استخدام معايير بحث مختلفة أو <a href="{{ url_for('returns.index') }}" class="text-primary dark:text-blue-400 hover:underline">عرض جميع المرتجعات</a></p>
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <!-- زر تبديل الوضع الداكن -->
    <div class="theme-toggle" id="themeToggle">
        <i class="ri-moon-line dark:hidden"></i>
        <i class="ri-sun-line hidden dark:block"></i>
    </div>

    <!-- Filter Modal -->
    <div id="filterModal" class="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center hidden">
        <div id="filter-modal-content" class="bg-white dark:bg-dark-100 rounded-lg shadow-xl w-full max-w-md transform scale-95 opacity-0 transition-all duration-300 mx-2">
            <div class="p-4 border-b border-gray-200 dark:border-gray-700 flex justify-between items-center bg-gradient-to-r from-purple-50 to-white dark:from-purple-900/20 dark:to-dark-100">
                <h3 class="text-base font-bold text-gray-800 dark:text-white flex items-center">
                    <i class="ri-filter-3-line ml-2 text-purple-500 dark:text-purple-400"></i>
                    <span>فلترة المرتجعات</span>
                </h3>
                <button id="close-filter-modal" class="text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 transition-colors duration-300">
                    <i class="ri-close-line text-xl"></i>
                </button>
            </div>
            <div class="p-4">
                <form action="{{ url_for('returns.index') }}" method="get" class="space-y-4">
                    <!-- البحث -->
                    <div>
                        <label for="search" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">بحث</label>
                        <input type="text" name="search" id="search" value="{{ search }}" placeholder="رقم المرتجع أو رقم الفاتورة..." class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 dark:bg-dark-200 dark:text-white rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary">
                    </div>

                    <!-- العميل -->
                    <div>
                        <label for="customer_id" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">العميل</label>
                        <select name="customer_id" id="customer_id" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 dark:bg-dark-200 dark:text-white rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary">
                            <option value="">جميع العملاء</option>
                            {% for customer in customers %}
                            <option value="{{ customer.id }}" {% if customer_id|string == customer.id|string %}selected{% endif %}>{{ customer.name }}</option>
                            {% endfor %}
                        </select>
                    </div>

                    <!-- المخزن -->
                    <div>
                        <label for="warehouse_id" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">المخزن</label>
                        <select name="warehouse_id" id="warehouse_id" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 dark:bg-dark-200 dark:text-white rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary">
                            <option value="">جميع المخازن</option>
                            {% for warehouse in warehouses %}
                            <option value="{{ warehouse.id }}" {% if warehouse_id|string == warehouse.id|string %}selected{% endif %}>{{ warehouse.name }}</option>
                            {% endfor %}
                        </select>
                    </div>

                    <!-- المستخدم -->
                    <div>
                        <label for="user_id" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">المستخدم</label>
                        <select name="user_id" id="user_id" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 dark:bg-dark-200 dark:text-white rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary">
                            <option value="">جميع المستخدمين</option>
                            {% for user in users %}
                            <option value="{{ user.id }}" {% if user_id|string == user.id|string %}selected{% endif %}>{{ user.username }}</option>
                            {% endfor %}
                        </select>
                    </div>

                    <!-- طريقة الدفع -->
                    <div>
                        <label for="payment_method" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">طريقة الاسترداد</label>
                        <select name="payment_method" id="payment_method" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 dark:bg-dark-200 dark:text-white rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary">
                            <option value="">جميع الطرق</option>
                            <option value="cash" {% if payment_method == 'cash' %}selected{% endif %}>نقدي</option>
                            <option value="card" {% if payment_method == 'card' %}selected{% endif %}>بطاقة</option>
                            <option value="store_credit" {% if payment_method == 'store_credit' %}selected{% endif %}>رصيد متجر</option>
                        </select>
                    </div>

                    <!-- الحالة -->
                    <div>
                        <label for="status" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">الحالة</label>
                        <select name="status" id="status" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 dark:bg-dark-200 dark:text-white rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary">
                            <option value="">جميع الحالات</option>
                            <option value="completed" {% if status == 'completed' %}selected{% endif %}>مكتمل</option>
                            <option value="pending" {% if status == 'pending' %}selected{% endif %}>معلق</option>
                            <option value="cancelled" {% if status == 'cancelled' %}selected{% endif %}>ملغي</option>
                        </select>
                    </div>

                    <!-- التاريخ -->
                    <div class="grid grid-cols-2 gap-4">
                        <div>
                            <label for="date_from" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">من تاريخ</label>
                            <input type="date" name="date_from" id="date_from" value="{{ date_from }}" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 dark:bg-dark-200 dark:text-white rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary">
                        </div>
                        <div>
                            <label for="date_to" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">إلى تاريخ</label>
                            <input type="date" name="date_to" id="date_to" value="{{ date_to }}" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 dark:bg-dark-200 dark:text-white rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary">
                        </div>
                    </div>

                    <div class="flex justify-between pt-4 border-t border-gray-200 dark:border-gray-700">
                        <a href="{{ url_for('returns.index') }}" class="px-4 py-2 bg-gray-100 dark:bg-dark-200 text-gray-700 dark:text-gray-300 rounded-md hover:bg-gray-200 dark:hover:bg-dark-300 transition-colors duration-300">إعادة تعيين</a>
                        <button type="submit" class="px-4 py-2 bg-primary text-white rounded-md hover:bg-blue-600 transition-colors duration-300">تطبيق الفلتر</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script>
        // تبديل الوضع الداكن
        document.addEventListener('DOMContentLoaded', function() {
            const themeToggle = document.getElementById('themeToggle');
            const filterToggleBtn = document.getElementById('filterToggleBtn');
            const filterModal = document.getElementById('filterModal');
            const filterModalContent = document.getElementById('filter-modal-content');
            const closeFilterModal = document.getElementById('close-filter-modal');

            // التحقق من الوضع المحفوظ
            function applyTheme() {
                if (localStorage.getItem('theme') === 'dark') {
                    document.documentElement.classList.add('dark');
                    document.documentElement.classList.remove('light');
                } else {
                    document.documentElement.classList.remove('dark');
                    document.documentElement.classList.add('light');
                }
            }

            // تطبيق الوضع المحفوظ عند تحميل الصفحة
            applyTheme();

            // تبديل الوضع عند النقر على الزر
            themeToggle.addEventListener('click', function() {
                if (document.documentElement.classList.contains('dark')) {
                    localStorage.setItem('theme', 'light');
                } else {
                    localStorage.setItem('theme', 'dark');
                }
                applyTheme();
            });

            // إظهار مودال الفلتر
            filterToggleBtn.addEventListener('click', function() {
                filterModal.classList.remove('hidden');
                setTimeout(() => {
                    filterModalContent.classList.remove('scale-95', 'opacity-0');
                    filterModalContent.classList.add('scale-100', 'opacity-100');
                }, 10);
            });

            // إخفاء مودال الفلتر
            function hideFilterModal() {
                filterModalContent.classList.remove('scale-100', 'opacity-100');
                filterModalContent.classList.add('scale-95', 'opacity-0');
                setTimeout(() => {
                    filterModal.classList.add('hidden');
                }, 300);
            }

            closeFilterModal.addEventListener('click', hideFilterModal);

            // إغلاق المودال عند النقر خارجه
            filterModal.addEventListener('click', function(e) {
                if (e.target === filterModal) {
                    hideFilterModal();
                }
            });
        });
    </script>
</body>
</html>
