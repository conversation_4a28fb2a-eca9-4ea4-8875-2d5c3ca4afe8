
from flask import Blueprint, render_template, jsonify, request
from flask_login import login_required, current_user
from datetime import datetime, timedelta
from sqlalchemy import func, extract, or_, and_
from app import db
from models import (
    Product, Category, Order, OrderItem, Customer,
    Supplier, Purchase, PurchaseItem, Inventory, Warehouse
)
from routes.settings import load_settings

dashboard_blueprint = Blueprint('dashboard', __name__)

@dashboard_blueprint.route('/dashboard')
@dashboard_blueprint.route('/home')
@login_required
def home():
    try:
        # بيانات المبيعات اليومية للوحة التحكم
        today = datetime.now().date()
        today_start = datetime.combine(today, datetime.min.time())
        today_end = datetime.combine(today, datetime.max.time())

        # إجمالي المبيعات اليوم
        daily_sales = db.session.query(func.sum(Order.total)).filter(
            Order.created_at.between(today_start, today_end),
            Order.status == 'completed'
        ).scalar() or 0

        # عدد الطلبات اليوم
        daily_orders_count = db.session.query(func.count(Order.id)).filter(
            Order.created_at.between(today_start, today_end),
            Order.status == 'completed'
        ).scalar() or 0

        # إجمالي المبيعات هذا الشهر
        month_start = datetime(today.year, today.month, 1)
        month_end = today_end

        monthly_sales = db.session.query(func.sum(Order.total)).filter(
            Order.created_at.between(month_start, month_end),
            Order.status == 'completed'
        ).scalar() or 0

        # Get low stock and out of stock products
        # This is a more complex operation since we need to check each inventory
        products = Product.query.filter_by(is_active=True).all()
        low_stock_products = 0
        out_of_stock_products = 0
        total_products = len(products)
        active_products = total_products  # All products are active by our filter

        for product in products:
            if product.stock_quantity <= 0:
                out_of_stock_products += 1
            elif product.stock_quantity <= product.minimum_stock:
                low_stock_products += 1

        # آخر المبيعات
        try:
            # استخدام استعلام محدد يتضمن فقط الأعمدة المؤكد وجودها في قاعدة البيانات
            recent_sales = db.session.query(
                Order.id, Order.invoice_number, Order.customer_id,
                Order.total, Order.status, Order.created_at,
                Customer.name.label('customer_name')
            ).outerjoin(
                Customer, Order.customer_id == Customer.id
            ).filter(
                Order.status.in_(['completed', 'pending', 'cancelled'])
            ).order_by(Order.created_at.desc()).limit(10).all()

            # تحويل النتائج إلى قائمة من القواميس لسهولة الاستخدام في القالب
            recent_orders = []
            for order in recent_sales:
                recent_orders.append({
                    'id': order.id,
                    'invoice_number': order.invoice_number,
                    'total': order.total,
                    'status': order.status,
                    'created_at': order.created_at,
                    'customer': {
                        'name': order.customer_name or 'عميل عادي'
                    }
                })

            # إجمالي عدد الطلبات
            total_orders = db.session.query(func.count(Order.id)).scalar() or 0

        except Exception as e:
            print(f"Error fetching recent sales: {str(e)}")
            recent_orders = []
            total_orders = 0

        # أكثر المنتجات مبيعاً
        top_products_query = db.session.query(
            Product.id,
            Product.name,
            Product.price,
            Product.image_path,
            Category.name.label('category'),
            func.sum(OrderItem.quantity).label('total_sold')
        ).join(
            OrderItem, Product.id == OrderItem.product_id
        ).join(
            Order, OrderItem.order_id == Order.id
        ).outerjoin(
            Category, Product.category_id == Category.id
        ).filter(
            Order.status == 'completed',
            Order.created_at.between(month_start, month_end)
        ).group_by(
            Product.id, Category.name
        ).order_by(
            func.sum(OrderItem.quantity).desc()
        ).limit(5)

        # تحويل نتائج الاستعلام إلى قائمة من القواميس
        top_products = []
        for product in top_products_query.all():
            top_products.append({
                'id': product.id,
                'name': product.name,
                'price': product.price,
                'image': product.image_path,
                'category': product.category or 'بدون تصنيف',
                'total_sold': product.total_sold
            })

        warehouses = Warehouse.query.filter_by(is_active=True).all()

        # إجمالي المبيعات منذ البداية
        total_sales = db.session.query(func.sum(Order.total)).filter(
            Order.status == 'completed'
        ).scalar() or 0

        # إجمالي المشتريات
        total_purchases = db.session.query(func.sum(Purchase.total)).filter(
            Purchase.status == 'received'
        ).scalar() or 0

        # مشتريات اليوم
        today_purchases = db.session.query(func.sum(Purchase.total)).filter(
            Purchase.created_at.between(today_start, today_end),
            Purchase.status == 'received'
        ).scalar() or 0

        # مردودات المبيعات (افتراضية للعرض)
        sales_returns = 0

        # مردودات المشتريات (افتراضية للعرض)
        purchase_returns = 0

        # حساب الأرباح (افتراضي للعرض)
        # في التطبيق الحقيقي يجب حساب الربح بناءً على سعر التكلفة وسعر البيع
        profit = total_sales * 0.25  # افتراض أن هامش الربح 25%

        # عدد العملاء
        customers_count = db.session.query(func.count(Customer.id)).scalar() or 0

        # عدد الموردين
        suppliers_count = db.session.query(func.count(Supplier.id)).scalar() or 0

        # تحضير بيانات لوحة التحكم لاستخدامها في القالب
        stats = {
            'today_sales': daily_sales,
            'today_orders': daily_orders_count,
            'monthly_sales': monthly_sales,
            'total_sales': total_sales,
            'total_purchases': total_purchases,
            'today_purchases': today_purchases,
            'sales_returns': sales_returns,
            'purchase_returns': purchase_returns,
            'profit': profit,
            'customers_count': customers_count,
            'suppliers_count': suppliers_count,
            'low_stock_products': low_stock_products,
            'out_of_stock_products': out_of_stock_products,
            'total_products': total_products,
            'active_products': active_products,
            'recent_orders': recent_orders,
            'total_orders': total_orders,
            'top_products': top_products
        }

        # تحميل إعدادات المتجر
        settings = load_settings()

        return render_template(
            'core/dashboard/dashboard_new.html',
            stats=stats,
            daily_sales=daily_sales,
            daily_orders_count=daily_orders_count,
            monthly_sales=monthly_sales,
            low_stock_products=low_stock_products,
            out_of_stock_products=out_of_stock_products,
            recent_sales=recent_sales,
            top_products=top_products,
            warehouses=warehouses,
            current_user=current_user,
            current_date=today.strftime('%Y-%m-%d'),
            settings=settings
        )
    except Exception as e:
        # طباعة الخطأ للتصحيح
        import traceback
        print(f"Dashboard Error: {str(e)}")
        print(traceback.format_exc())
        # توفير بيانات افتراضية عند حدوث خطأ
        default_stats = {
            'today_sales': 0,
            'today_orders': 0,
            'today_sales_percentage': 0,
            'weekly_sales': 0,
            'weekly_orders': 0,
            'weekly_sales_percentage': 0,
            'monthly_sales': 0,
            'monthly_orders': 0,
            'monthly_sales_percentage': 0,
            'total_sales': 0,
            'total_orders': 0,
            'profit': 0,
            'profit_percentage': 0,
            'today_purchases': 0,
            'today_purchases_percentage': 0,
            'total_products': 0,
            'active_products': 0,
            'active_products_percentage': 0,
            'low_stock_products': 0,
            'low_stock_percentage': 0,
            'out_of_stock_products': 0,
            'out_of_stock_percentage': 0,
            'warehouses_count': 0,
            'customers_count': 0,
            'recent_orders': [],
            'top_products': [],
            'low_stock_products_list': []
        }
        # تحميل إعدادات المتجر
        settings = load_settings()
        return render_template('core/dashboard/dashboard_new.html', error=str(e), stats=default_stats, current_user=current_user, settings=settings)

@dashboard_blueprint.route('/api/sales-chart-data')
@login_required
def sales_chart_data():
    days = int(request.args.get('days', 30))

    # Get date range
    end_date = datetime.now().date()
    start_date = end_date - timedelta(days=days-1)

    # Prepare date list for all days in range
    date_range = [(start_date + timedelta(days=i)) for i in range(days)]

    # Initialize result dictionary with zeros
    sales_data = {date.strftime('%Y-%m-%d'): 0 for date in date_range}
    orders_data = {date.strftime('%Y-%m-%d'): 0 for date in date_range}

    # Query sales data grouped by date
    sales_by_date = db.session.query(
        func.date(Order.created_at).label('date'),
        func.sum(Order.total).label('total_sales'),
        func.count(Order.id).label('order_count')
    ).filter(
        Order.created_at.between(
            datetime.combine(start_date, datetime.min.time()),
            datetime.combine(end_date, datetime.max.time())
        ),
        Order.status == 'completed'
    ).group_by(func.date(Order.created_at)).all()

    # Fill in actual data
    for date, total_sales, order_count in sales_by_date:
        # التحقق من نوع التاريخ وتحويله إلى سلسلة نصية
        if hasattr(date, 'strftime'):
            date_str = date.strftime('%Y-%m-%d')
        else:
            # إذا كان التاريخ بالفعل سلسلة نصية
            date_str = str(date)

        sales_data[date_str] = float(total_sales)
        orders_data[date_str] = order_count

    return jsonify({
        'dates': list(sales_data.keys()),
        'sales': list(sales_data.values()),
        'orders': list(orders_data.values())
    })

@dashboard_blueprint.route('/api/performance-chart-data')
@login_required
def performance_chart_data():
    try:
        period = request.args.get('period', 'month')
        today = datetime.now().date()

        if period == 'month':
            # Current month data
            start_date = datetime(today.year, today.month, 1)
            end_date = datetime.combine(today, datetime.max.time())

            # Generate dates for each day in the month
            date_range = []
            current_date = start_date
            while current_date.date() <= today:
                date_range.append(current_date.date())
                current_date += timedelta(days=1)

            # Format for response
            date_labels = [date.strftime('%d/%m') for date in date_range]

        elif period == 'quarter':
            # Current quarter data
            current_month = today.month
            quarter_start_month = ((current_month - 1) // 3) * 3 + 1
            start_date = datetime(today.year, quarter_start_month, 1)
            end_date = datetime.combine(today, datetime.max.time())

            # Generate dates for each week in the quarter
            date_range = []
            current_date = start_date
            while current_date.date() <= today:
                date_range.append(current_date.date())
                current_date += timedelta(days=7)  # Weekly data points

            # Format for response
            date_labels = [date.strftime('%d/%m') for date in date_range]

        elif period == 'year':
            # Current year data
            start_date = datetime(today.year, 1, 1)
            end_date = datetime.combine(today, datetime.max.time())

            # Generate dates for each month in the year
            date_range = []
            for month in range(1, 13):
                if datetime(today.year, month, 1).date() <= today:
                    date_range.append(datetime(today.year, month, 1).date())

            # Format for response
            date_labels = [date.strftime('%b') for date in date_range]

        # Query sales data - using date() function which is more compatible with SQLite
        if period == 'month':
            # For monthly data, group by day
            sales_data = db.session.query(
                func.date(Order.created_at).label('date'),
                func.sum(Order.total).label('total_sales')
            ).filter(
                Order.created_at.between(start_date, end_date),
                Order.status == 'completed'
            ).group_by(func.date(Order.created_at)).all()

            # Query purchase data
            purchase_data = db.session.query(
                func.date(Purchase.created_at).label('date'),
                func.sum(Purchase.total).label('total_purchases')
            ).filter(
                Purchase.created_at.between(start_date, end_date),
                Purchase.status == 'received'
            ).group_by(func.date(Purchase.created_at)).all()
        elif period == 'quarter':
            # For quarterly data, group by week (using strftime for SQLite compatibility)
            sales_data = db.session.query(
                func.strftime('%Y-%W', Order.created_at).label('date'),
                func.sum(Order.total).label('total_sales')
            ).filter(
                Order.created_at.between(start_date, end_date),
                Order.status == 'completed'
            ).group_by(func.strftime('%Y-%W', Order.created_at)).all()

            # Query purchase data
            purchase_data = db.session.query(
                func.strftime('%Y-%W', Purchase.created_at).label('date'),
                func.sum(Purchase.total).label('total_purchases')
            ).filter(
                Purchase.created_at.between(start_date, end_date),
                Purchase.status == 'received'
            ).group_by(func.strftime('%Y-%W', Purchase.created_at)).all()
        else:  # year
            # For yearly data, group by month
            sales_data = db.session.query(
                func.strftime('%Y-%m', Order.created_at).label('date'),
                func.sum(Order.total).label('total_sales')
            ).filter(
                Order.created_at.between(start_date, end_date),
                Order.status == 'completed'
            ).group_by(func.strftime('%Y-%m', Order.created_at)).all()

            # Query purchase data
            purchase_data = db.session.query(
                func.strftime('%Y-%m', Purchase.created_at).label('date'),
                func.sum(Purchase.total).label('total_purchases')
            ).filter(
                Purchase.created_at.between(start_date, end_date),
                Purchase.status == 'received'
            ).group_by(func.strftime('%Y-%m', Purchase.created_at)).all()

        # Initialize result dictionaries
        sales_by_date = {date_str: 0 for date_str in date_labels}
        purchases_by_date = {date_str: 0 for date_str in date_labels}

        # Fill in sales data
        for date_obj, total in sales_data:
            if period == 'month':
                # For monthly data, convert date object to string
                if isinstance(date_obj, datetime):
                    date_str = date_obj.strftime('%d/%m')
                else:
                    # If it's already a string or date, format it
                    date_str = date_obj.strftime('%d/%m') if hasattr(date_obj, 'strftime') else str(date_obj)
            elif period == 'quarter':
                # For quarterly data, convert week number to a readable format
                # Format is YYYY-WW, we'll convert to "Week WW"
                if isinstance(date_obj, str):
                    year, week = date_obj.split('-')
                    date_str = f"أسبوع {week}"
                else:
                    date_str = str(date_obj)
            else:  # year
                # For yearly data, convert YYYY-MM to month name
                if isinstance(date_obj, str):
                    year, month = date_obj.split('-')
                    # Convert month number to month name
                    month_names = ['يناير', 'فبراير', 'مارس', 'إبريل', 'مايو', 'يونيو',
                                  'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر']
                    date_str = month_names[int(month) - 1]
                else:
                    date_str = str(date_obj)

            # Find the closest matching label if exact match not found
            if date_str in sales_by_date:
                sales_by_date[date_str] = float(total)
            else:
                # Try to find a close match or add a new entry
                closest_match = None
                for label in date_labels:
                    if date_str in label or label in date_str:
                        closest_match = label
                        break

                if closest_match:
                    sales_by_date[closest_match] = float(total)
                else:
                    # If no match found, add to the first date as a fallback
                    if date_labels:
                        sales_by_date[date_labels[0]] = float(total)

        # Fill in purchase data
        for date_obj, total in purchase_data:
            if period == 'month':
                # For monthly data, convert date object to string
                if isinstance(date_obj, datetime):
                    date_str = date_obj.strftime('%d/%m')
                else:
                    # If it's already a string or date, format it
                    date_str = date_obj.strftime('%d/%m') if hasattr(date_obj, 'strftime') else str(date_obj)
            elif period == 'quarter':
                # For quarterly data, convert week number to a readable format
                if isinstance(date_obj, str):
                    year, week = date_obj.split('-')
                    date_str = f"أسبوع {week}"
                else:
                    date_str = str(date_obj)
            else:  # year
                # For yearly data, convert YYYY-MM to month name
                if isinstance(date_obj, str):
                    year, month = date_obj.split('-')
                    # Convert month number to month name
                    month_names = ['يناير', 'فبراير', 'مارس', 'إبريل', 'مايو', 'يونيو',
                                  'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر']
                    date_str = month_names[int(month) - 1]
                else:
                    date_str = str(date_obj)

            # Find the closest matching label if exact match not found
            if date_str in purchases_by_date:
                purchases_by_date[date_str] = float(total)
            else:
                # Try to find a close match or add a new entry
                closest_match = None
                for label in date_labels:
                    if date_str in label or label in date_str:
                        closest_match = label
                        break

                if closest_match:
                    purchases_by_date[closest_match] = float(total)
                else:
                    # If no match found, add to the first date as a fallback
                    if date_labels:
                        purchases_by_date[date_labels[0]] = float(total)

        # Calculate profit
        profit_by_date = {date: sales_by_date[date] - purchases_by_date[date] for date in date_labels}

        return jsonify({
            'dates': date_labels,
            'sales': list(sales_by_date.values()),
            'purchases': list(purchases_by_date.values()),
            'profit': list(profit_by_date.values())
        })
    except Exception as e:
        print(f"Error in performance chart data: {str(e)}")
        # Return empty data in case of error
        return jsonify({
            'dates': [],
            'sales': [],
            'purchases': [],
            'profit': []
        })

@dashboard_blueprint.route('/api/category-distribution')
@login_required
def category_distribution():
    category_data = db.session.query(
        Category.name,
        func.count(Product.id).label('product_count')
    ).join(Product).group_by(Category.id).all()

    return jsonify({
        'categories': [cat[0] for cat in category_data],
        'counts': [cat[1] for cat in category_data]
    })


