"""
Nobara POS System - Authentication Routes
نظام نوبارا لنقاط البيع - مسارات المصادقة
"""

from flask import render_template, request, redirect, url_for, flash, session, jsonify
from flask_login import login_user, logout_user, login_required, current_user
from werkzeug.security import check_password_hash
from app.auth import bp
from app.models import User, db
from datetime import datetime
import logging

logger = logging.getLogger(__name__)

@bp.route('/login', methods=['GET', 'POST'])
def login():
    """صفحة تسجيل الدخول"""
    if current_user.is_authenticated:
        return redirect(url_for('main.dashboard'))
    
    if request.method == 'POST':
        username = request.form.get('username', '').strip()
        password = request.form.get('password', '')
        remember_me = bool(request.form.get('remember_me'))
        
        if not username or not password:
            flash('يرجى إدخال اسم المستخدم وكلمة المرور', 'error')
            return render_template('auth/login.html')
        
        # البحث عن المستخدم
        user = User.query.filter_by(username=username).first()
        
        if user and user.check_password(password):
            if not user.is_active:
                flash('حسابك غير مفعل. يرجى التواصل مع المدير', 'error')
                return render_template('auth/login.html')
            
            if not user.can_login:
                flash('ليس لديك صلاحية تسجيل الدخول', 'error')
                return render_template('auth/login.html')
            
            # تسجيل الدخول
            login_user(user, remember=remember_me)
            
            # تحديث آخر تسجيل دخول
            user.last_login = datetime.utcnow()
            db.session.commit()
            
            # تسجيل العملية
            logger.info(f'User {username} logged in successfully')
            
            # إعداد الجلسة
            session['language'] = user.language
            session['theme'] = user.theme
            
            flash(f'مرحباً بك {user.full_name}', 'success')
            
            # إعادة التوجيه للصفحة المطلوبة أو لوحة التحكم
            next_page = request.args.get('next')
            if next_page:
                return redirect(next_page)
            return redirect(url_for('main.dashboard'))
        
        else:
            flash('اسم المستخدم أو كلمة المرور غير صحيحة', 'error')
            logger.warning(f'Failed login attempt for username: {username}')
    
    return render_template('auth/login.html')

@bp.route('/logout')
@login_required
def logout():
    """تسجيل الخروج"""
    username = current_user.username
    logout_user()
    session.clear()
    
    logger.info(f'User {username} logged out')
    flash('تم تسجيل الخروج بنجاح', 'info')
    
    return redirect(url_for('auth.login'))

@bp.route('/change-password', methods=['POST'])
@login_required
def change_password():
    """تغيير كلمة المرور"""
    try:
        data = request.get_json()
        current_password = data.get('current_password')
        new_password = data.get('new_password')
        
        if not current_password or not new_password:
            return jsonify({'success': False, 'message': 'جميع الحقول مطلوبة'})
        
        if not current_user.check_password(current_password):
            return jsonify({'success': False, 'message': 'كلمة المرور الحالية غير صحيحة'})
        
        if len(new_password) < 6:
            return jsonify({'success': False, 'message': 'كلمة المرور يجب أن تكون 6 أحرف على الأقل'})
        
        # تحديث كلمة المرور
        current_user.set_password(new_password)
        current_user.updated_at = datetime.utcnow()
        db.session.commit()
        
        logger.info(f'User {current_user.username} changed password')
        
        return jsonify({'success': True, 'message': 'تم تغيير كلمة المرور بنجاح'})
        
    except Exception as e:
        logger.error(f'Error changing password: {e}')
        return jsonify({'success': False, 'message': 'حدث خطأ أثناء تغيير كلمة المرور'})

@bp.route('/forgot-password', methods=['GET', 'POST'])
def forgot_password():
    """نسيان كلمة المرور"""
    if request.method == 'POST':
        email = request.form.get('email', '').strip()
        
        if not email:
            flash('يرجى إدخال البريد الإلكتروني', 'error')
            return render_template('auth/forgot_password.html')
        
        user = User.query.filter_by(email=email).first()
        
        if user:
            # إرسال بريد إلكتروني للمطور مع تفاصيل المستخدم
            try:
                # هنا يمكن إضافة كود إرسال البريد الإلكتروني
                logger.info(f'Password reset requested for user: {user.username}, email: {email}')
                
                flash('تم إرسال طلب إعادة تعيين كلمة المرور للمطور', 'info')
            except Exception as e:
                logger.error(f'Error sending password reset email: {e}')
                flash('حدث خطأ أثناء إرسال الطلب', 'error')
        else:
            flash('البريد الإلكتروني غير مسجل في النظام', 'error')
    
    return render_template('auth/forgot_password.html')

@bp.route('/check-session')
@login_required
def check_session():
    """فحص حالة الجلسة"""
    return jsonify({
        'authenticated': True,
        'user': {
            'id': current_user.id,
            'username': current_user.username,
            'full_name': current_user.full_name,
            'role': current_user.role.name if current_user.role else None
        }
    })

@bp.route('/update-preferences', methods=['POST'])
@login_required
def update_preferences():
    """تحديث تفضيلات المستخدم"""
    try:
        data = request.get_json()
        
        if 'language' in data:
            current_user.language = data['language']
            session['language'] = data['language']
        
        if 'theme' in data:
            current_user.theme = data['theme']
            session['theme'] = data['theme']
        
        current_user.updated_at = datetime.utcnow()
        db.session.commit()
        
        return jsonify({'success': True, 'message': 'تم تحديث التفضيلات بنجاح'})
        
    except Exception as e:
        logger.error(f'Error updating preferences: {e}')
        return jsonify({'success': False, 'message': 'حدث خطأ أثناء تحديث التفضيلات'})

# User loader for Flask-Login
from flask_login import LoginManager
from app import login_manager

@login_manager.user_loader
def load_user(user_id):
    """تحميل المستخدم للجلسة"""
    return User.query.get(int(user_id))
