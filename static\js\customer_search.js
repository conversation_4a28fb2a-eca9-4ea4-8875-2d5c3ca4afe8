// Customer search and management functionality
document.addEventListener('DOMContentLoaded', function() {
    setupCustomerSearch();
});

// Setup customer search functionality
function setupCustomerSearch() {
    // Get DOM elements
    const currentCustomerName = document.getElementById('current-customer-name');
    const customerSelect = document.getElementById('customer-select');
    const openCustomerModalBtn = document.getElementById('open-customer-modal');
    const customerModal = document.getElementById('customerModal');

    // Check if required elements exist
    if (!openCustomerModalBtn || !customerModal) {
        console.error('Required customer search elements not found in the DOM');
        return; // Exit the function if required elements don't exist
    }

    const customerModalContent = document.getElementById('customer-modal-content');
    const closeCustomerModalBtn = document.getElementById('close-customer-modal');
    const cancelCustomerModalBtn = document.getElementById('cancel-customer-modal');
    const modalCustomerSearch = document.getElementById('modal-customer-search');
    const modalCustomerResults = document.getElementById('modal-customer-results');
    const searchCustomerTab = document.getElementById('search-customer-tab');
    const addCustomerTab = document.getElementById('add-customer-tab');
    const searchCustomerContent = document.getElementById('search-customer-content');
    const addCustomerContent = document.getElementById('add-customer-content');
    const saveCustomerBtn = document.getElementById('save-customer');
    const selectCustomerBtn = document.getElementById('select-customer');

    // Open customer modal
    openCustomerModalBtn.addEventListener('click', function() {
        customerModal.classList.remove('hidden');
        setTimeout(() => {
            customerModalContent.classList.add('scale-100', 'opacity-100');
            if (modalCustomerSearch) modalCustomerSearch.focus();
        }, 10);
    });

    // Close customer modal
    function closeCustomerModal() {
        customerModalContent.classList.remove('scale-100', 'opacity-100');
        setTimeout(() => {
            customerModal.classList.add('hidden');
        }, 300);
    }

    if (closeCustomerModalBtn) {
        closeCustomerModalBtn.addEventListener('click', closeCustomerModal);
    }

    if (cancelCustomerModalBtn) {
        cancelCustomerModalBtn.addEventListener('click', closeCustomerModal);
    }

    // Switch between tabs
    if (searchCustomerTab && addCustomerTab && searchCustomerContent && addCustomerContent) {
        searchCustomerTab.addEventListener('click', function() {
            // Activate search tab
            searchCustomerTab.classList.add('text-blue-600', 'border-blue-600', 'customer-tab-active');
            addCustomerTab.classList.remove('text-blue-600', 'border-blue-600', 'customer-tab-active');
            addCustomerTab.classList.add('text-gray-500', 'border-transparent');

            // Show search content, hide add content
            searchCustomerContent.classList.remove('hidden');
            addCustomerContent.classList.add('hidden');

            // Show select button, hide save button
            if (selectCustomerBtn) selectCustomerBtn.classList.remove('hidden');
            if (saveCustomerBtn) saveCustomerBtn.classList.add('hidden');

            // Focus search input
            if (modalCustomerSearch) modalCustomerSearch.focus();
        });

        addCustomerTab.addEventListener('click', function() {
            // Activate add tab
            addCustomerTab.classList.add('text-blue-600', 'border-blue-600', 'customer-tab-active');
            searchCustomerTab.classList.remove('text-blue-600', 'border-blue-600', 'customer-tab-active');
            searchCustomerTab.classList.add('text-gray-500', 'border-transparent');

            // Show add content, hide search content
            addCustomerContent.classList.remove('hidden');
            searchCustomerContent.classList.add('hidden');

            // Show save button, hide select button
            if (saveCustomerBtn) saveCustomerBtn.classList.remove('hidden');
            if (selectCustomerBtn) selectCustomerBtn.classList.add('hidden');

            // Focus name input
            const customerNameInput = document.getElementById('customer-name');
            if (customerNameInput) customerNameInput.focus();
        });
    }

    // Search for customers as user types
    modalCustomerSearch.addEventListener('input', debounce(function() {
        const searchTerm = this.value.trim();
        if (searchTerm.length < 2) {
            modalCustomerResults.innerHTML = `
                <div class="text-center text-gray-500 py-4">
                    <div class="w-16 h-16 mx-auto rounded-full bg-gray-100 flex items-center justify-center mb-2">
                        <i class="ri-user-search-line text-2xl text-gray-400"></i>
                    </div>
                    <p class="text-sm">ابحث عن عميل</p>
                    <p class="text-xs mt-1">أدخل اسم العميل أو رقم الهاتف للبحث</p>
                </div>
            `;
            return;
        }

        // Show loading state
        modalCustomerResults.innerHTML = `
            <div class="text-center text-gray-500 py-4">
                <div class="w-16 h-16 mx-auto rounded-full bg-gray-100 flex items-center justify-center mb-2">
                    <i class="ri-loader-4-line text-2xl text-gray-400 animate-spin"></i>
                </div>
                <p class="text-sm">جاري البحث...</p>
            </div>
        `;

        fetch(`/api/customers/search?q=${encodeURIComponent(searchTerm)}`)
            .then(response => response.json())
            .then(data => {
                modalCustomerResults.innerHTML = '';
                if (data.customers.length === 0) {
                    modalCustomerResults.innerHTML = `
                        <div class="text-center text-gray-500 py-4">
                            <div class="w-16 h-16 mx-auto rounded-full bg-gray-100 flex items-center justify-center mb-2">
                                <i class="ri-user-search-line text-2xl text-gray-400"></i>
                            </div>
                            <p class="text-sm">لا توجد نتائج</p>
                            <p class="text-xs mt-1">جرب البحث بكلمات أخرى أو <button id="switch-to-add" class="text-blue-500 hover:underline">أضف عميل جديد</button></p>
                        </div>
                    `;

                    // Add event listener to switch to add tab
                    document.getElementById('switch-to-add').addEventListener('click', function() {
                        addCustomerTab.click();
                    });

                    return;
                }

                data.customers.forEach(customer => {
                    const customerItem = document.createElement('div');
                    customerItem.className = 'bg-white p-3 rounded-lg border border-gray-200 hover:border-blue-300 transition-all duration-300 cursor-pointer shadow-sm hover:shadow-md mb-2';
                    customerItem.setAttribute('data-id', customer.id);
                    customerItem.setAttribute('data-name', customer.name);

                    customerItem.innerHTML = `
                        <div class="flex items-center">
                            <div class="w-10 h-10 rounded-full bg-blue-100 flex items-center justify-center text-blue-500 mr-3">
                                <i class="ri-user-line text-lg"></i>
                            </div>
                            <div class="flex-1">
                                <p class="text-sm font-medium text-gray-800">${customer.name}</p>
                                <p class="text-xs text-gray-500">${customer.phone || 'لا يوجد رقم هاتف'}</p>
                            </div>
                        </div>
                    `;

                    modalCustomerResults.appendChild(customerItem);

                    // Add click event to select customer
                    customerItem.addEventListener('click', function() {
                        const customerId = this.getAttribute('data-id');
                        const customerName = this.getAttribute('data-name');

                        // Highlight selected customer
                        document.querySelectorAll('#modal-customer-results > div').forEach(item => {
                            item.classList.remove('border-blue-500', 'bg-blue-50');
                        });
                        this.classList.add('border-blue-500', 'bg-blue-50');

                        // Store selected customer ID for later use
                        selectCustomerBtn.setAttribute('data-id', customerId);
                        selectCustomerBtn.setAttribute('data-name', customerName);
                    });
                });
            })
            .catch(error => {
                console.error('Error searching for customers:', error);
                modalCustomerResults.innerHTML = `
                    <div class="text-center text-red-500 py-4">
                        <div class="w-16 h-16 mx-auto rounded-full bg-red-50 flex items-center justify-center mb-2">
                            <i class="ri-error-warning-line text-2xl text-red-400"></i>
                        </div>
                        <p class="text-sm">حدث خطأ أثناء البحث</p>
                        <p class="text-xs mt-1">يرجى المحاولة مرة أخرى</p>
                    </div>
                `;
            });
    }, 300));

    // Select customer button click
    selectCustomerBtn.addEventListener('click', function() {
        const customerId = this.getAttribute('data-id');
        const customerName = this.getAttribute('data-name');

        if (customerId && customerName) {
            selectCustomer(customerId, customerName);
            closeCustomerModal();
        } else {
            showNotification('يرجى اختيار عميل أولاً', 'warning');
        }
    });

    // Save new customer
    saveCustomerBtn.addEventListener('click', function() {
        const form = document.getElementById('add-customer-form');
        const formData = new FormData(form);
        const customerData = {};

        formData.forEach((value, key) => {
            customerData[key] = value;
        });

        // Validate form
        if (!customerData.name) {
            showNotification('يرجى إدخال اسم العميل', 'error');
            return;
        }

        // Show loading state
        saveCustomerBtn.disabled = true;
        saveCustomerBtn.innerHTML = '<i class="ri-loader-4-line animate-spin mr-1"></i> جاري الحفظ...';

        // Send data to server
        fetch('/api/customers/add', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(customerData),
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showNotification('تم إضافة العميل بنجاح', 'success');

                // Add customer to select options
                const option = document.createElement('option');
                option.value = data.customer.id;
                option.textContent = data.customer.name;
                customerSelect.appendChild(option);

                // Select the new customer
                selectCustomer(data.customer.id, data.customer.name);

                // Close modal
                closeCustomerModal();

                // Clear form
                form.reset();
            } else {
                showNotification(data.message || 'حدث خطأ أثناء إضافة العميل', 'error');
            }
        })
        .catch(error => {
            console.error('Error adding customer:', error);
            showNotification('حدث خطأ أثناء إضافة العميل', 'error');
        })
        .finally(() => {
            // Reset button state
            saveCustomerBtn.disabled = false;
            saveCustomerBtn.innerHTML = '<i class="ri-save-line mr-1"></i> حفظ العميل';
        });
    });
}

// Select customer function
function selectCustomer(id, name) {
    // Update global variables
    selectedCustomerId = id;
    selectedCustomerName = name || 'عميل نقدي';

    // Update UI
    document.getElementById('customer-select').value = id;
    document.getElementById('current-customer-name').textContent = selectedCustomerName;

    // Show notification
    showNotification(`تم اختيار العميل: ${selectedCustomerName}`, 'success');
}

// Helper function for debouncing
function debounce(func, wait) {
    let timeout;
    return function() {
        const context = this, args = arguments;
        clearTimeout(timeout);
        timeout = setTimeout(() => {
            func.apply(context, args);
        }, wait);
    };
}
