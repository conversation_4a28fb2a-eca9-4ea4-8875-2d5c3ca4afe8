<!DOCTYPE html>
<html lang="ar" dir="rtl" class="light">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>FoUaD - إضافة دفعة للعميل</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdn.jsdelivr.net/npm/remixicon@2.5.0/fonts/remixicon.css" rel="stylesheet">
    <script>
        tailwind.config = {
            darkMode: 'class',
            theme: {
                extend: {
                    colors: {
                        primary: '#3B82F6',
                        secondary: '#10b981',
                        dark: {
                            100: '#1E293B',
                            200: '#0F172A',
                            300: '#0B1120',
                            400: '#060A15'
                        }
                    }
                }
            }
        }
    </script>
    <style>
        body {
            font-family: 'Tajawal', sans-serif;
            background-color: #f1f5f9;
            transition: background-color 0.3s ease, color 0.3s ease;
        }
        body.dark {
            background-color: #0F172A;
            color: #E2E8F0;
        }
        .card {
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }
        .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
        }
        .customer-avatar {
            background: linear-gradient(135deg, #3B82F6 0%, #2563EB 100%);
            box-shadow: 0 10px 15px -3px rgba(59, 130, 246, 0.3);
        }
        .action-button {
            transition: all 0.3s ease;
        }
        .action-button:hover {
            transform: translateY(-2px);
        }
        .page-header {
            background: linear-gradient(to right, #3B82F6, #2563EB);
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
        }
        .dark .page-header {
            background: linear-gradient(to right, #1E40AF, #1E3A8A);
        }
        .payment-method-card {
            cursor: pointer;
            transition: all 0.3s ease;
        }
        .payment-method-card.selected {
            border-color: #3B82F6;
            background-color: rgba(59, 130, 246, 0.1);
        }
        .dark .payment-method-card.selected {
            border-color: #60A5FA;
            background-color: rgba(96, 165, 250, 0.1);
        }
    </style>
</head>
<body class="min-h-screen">
    {% include 'partials/navbar.html' %}

    <!-- Page Header -->
    <div class="page-header py-6 mb-8">
        <div class="container mx-auto px-4">
            <div class="flex flex-col md:flex-row justify-between items-center">
                <div class="flex items-center mb-4 md:mb-0">
                    <div class="customer-avatar w-16 h-16 rounded-full flex items-center justify-center text-white text-2xl font-bold mr-4">
                        {{ customer.name[0].upper() }}
                    </div>
                    <div>
                        <h1 class="text-2xl md:text-3xl font-bold text-white">إضافة دفعة للعميل: {{ customer.name }}</h1>
                        <p class="text-blue-100">
                            {% if customer.phone %}
                                <span class="inline-flex items-center">
                                    <i class="ri-phone-line mr-1"></i> {{ customer.phone }}
                                </span>
                            {% endif %}
                        </p>
                    </div>
                </div>
                <div class="flex space-x-2 space-x-reverse">
                    <a href="{{ url_for('customers.details', id=customer.id) }}" class="action-button bg-white text-primary hover:bg-blue-50 px-4 py-2 rounded-lg flex items-center shadow-md">
                        <i class="ri-arrow-right-line ml-1"></i>
                        العودة
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="container mx-auto px-4 pb-8">
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <!-- نموذج إضافة دفعة -->
            <div class="lg:col-span-2">
                <div class="bg-white dark:bg-gray-800 rounded-xl shadow-md p-6">
                    <h2 class="text-xl font-bold text-gray-800 dark:text-white mb-6 flex items-center">
                        <i class="ri-bank-card-line text-primary dark:text-blue-400 ml-2"></i>
                        تفاصيل الدفعة
                    </h2>

                    <form action="{{ url_for('customers.add_payment', id=customer.id) }}" method="POST" id="paymentForm">
                        <div class="mb-6">
                            <label for="amount" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">المبلغ <span class="text-red-500">*</span></label>
                            <div class="relative">
                                <input type="number" step="0.01" min="0.01" id="amount" name="amount" required class="w-full px-4 py-2 rounded-lg border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-800 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                    <span class="text-gray-500 dark:text-gray-400">ج.م</span>
                                </div>
                            </div>
                        </div>

                        <div class="mb-6">
                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">طريقة الدفع <span class="text-red-500">*</span></label>
                            <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                                <div class="payment-method-card border border-gray-200 dark:border-gray-700 rounded-lg p-4 text-center selected" data-method="cash">
                                    <i class="ri-money-dollar-circle-line text-3xl text-green-600 dark:text-green-400 mb-2"></i>
                                    <p class="text-sm font-medium">نقدي</p>
                                    <input type="radio" name="payment_method" value="cash" checked class="hidden">
                                </div>
                                <div class="payment-method-card border border-gray-200 dark:border-gray-700 rounded-lg p-4 text-center" data-method="card">
                                    <i class="ri-bank-card-line text-3xl text-blue-600 dark:text-blue-400 mb-2"></i>
                                    <p class="text-sm font-medium">بطاقة</p>
                                    <input type="radio" name="payment_method" value="card" class="hidden">
                                </div>
                                <div class="payment-method-card border border-gray-200 dark:border-gray-700 rounded-lg p-4 text-center" data-method="bank_transfer">
                                    <i class="ri-bank-line text-3xl text-purple-600 dark:text-purple-400 mb-2"></i>
                                    <p class="text-sm font-medium">تحويل بنكي</p>
                                    <input type="radio" name="payment_method" value="bank_transfer" class="hidden">
                                </div>
                                <div class="payment-method-card border border-gray-200 dark:border-gray-700 rounded-lg p-4 text-center" data-method="check">
                                    <i class="ri-bill-line text-3xl text-yellow-600 dark:text-yellow-400 mb-2"></i>
                                    <p class="text-sm font-medium">شيك</p>
                                    <input type="radio" name="payment_method" value="check" class="hidden">
                                </div>
                            </div>
                        </div>

                        <div class="mb-6">
                            <label for="order_id" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">الفاتورة (اختياري)</label>
                            <select id="order_id" name="order_id" class="w-full px-4 py-2 rounded-lg border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-800 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <option value="">دفعة عامة (غير مرتبطة بفاتورة محددة)</option>
                                {% for order in orders %}
                                {% set paid_amount = order.payments|sum(attribute='amount') %}
                                {% set remaining = order.total - paid_amount %}
                                <option value="{{ order.id }}" {% if selected_order_id == order.id %}selected{% endif %}>
                                    {{ order.invoice_number }} - {{ "%.2f"|format(order.total) }} ج.م (المتبقي: {{ "%.2f"|format(remaining) }} ج.م)
                                    {% if order.status == 'deferred' or order.payment_method == 'deferred' %}
                                    [آجل]
                                    {% endif %}
                                </option>
                                {% endfor %}
                            </select>
                        </div>

                        <div class="mb-6">
                            <label for="payment_date" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">تاريخ الدفع</label>
                            <input type="date" id="payment_date" name="payment_date" value="{{ now.strftime('%Y-%m-%d') }}" class="w-full px-4 py-2 rounded-lg border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-800 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500">
                        </div>

                        <div class="mb-6">
                            <label for="notes" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">ملاحظات</label>
                            <textarea id="notes" name="notes" rows="3" class="w-full px-4 py-2 rounded-lg border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-800 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500"></textarea>
                        </div>

                        <div class="flex justify-end">
                            <button type="submit" class="bg-primary hover:bg-blue-700 text-white px-6 py-2 rounded-lg transition-colors flex items-center">
                                <i class="ri-save-line ml-1"></i>
                                حفظ الدفعة
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- معلومات العميل -->
            <div>
                <div class="bg-white dark:bg-gray-800 rounded-xl shadow-md p-6 mb-6">
                    <h2 class="text-xl font-bold text-gray-800 dark:text-white mb-4 flex items-center">
                        <i class="ri-information-line text-primary dark:text-blue-400 ml-2"></i>
                        ملخص حساب العميل
                    </h2>

                    <div class="space-y-4">
                        <div class="flex justify-between items-center border-b border-gray-200 dark:border-gray-700 pb-3">
                            <span class="text-gray-600 dark:text-gray-400">إجمالي المشتريات</span>
                            <span class="font-bold text-gray-800 dark:text-white">{{ "%.2f"|format(stats.total_spent) }} ج.م</span>
                        </div>
                        <div class="flex justify-between items-center border-b border-gray-200 dark:border-gray-700 pb-3">
                            <span class="text-gray-600 dark:text-gray-400">المبلغ المدفوع</span>
                            <span class="font-bold text-green-600 dark:text-green-400">{{ "%.2f"|format(stats.paid_amount) }} ج.م</span>
                        </div>
                        <div class="flex justify-between items-center border-b border-gray-200 dark:border-gray-700 pb-3">
                            <span class="text-gray-600 dark:text-gray-400">المبلغ المتبقي</span>
                            <span class="font-bold text-red-600 dark:text-red-400">{{ "%.2f"|format(stats.remaining_amount) }} ج.م</span>
                        </div>
                        <div class="flex justify-between items-center">
                            <span class="text-gray-600 dark:text-gray-400">عدد الطلبات</span>
                            <span class="font-bold text-gray-800 dark:text-white">{{ stats.order_count }}</span>
                        </div>
                    </div>
                </div>

                {% if orders %}
                <div class="bg-white dark:bg-gray-800 rounded-xl shadow-md p-6">
                    <h2 class="text-xl font-bold text-gray-800 dark:text-white mb-4 flex items-center">
                        <i class="ri-file-list-3-line text-primary dark:text-blue-400 ml-2"></i>
                        الفواتير المستحقة
                    </h2>

                    <div class="space-y-4">
                        {% for order in orders %}
                        {% set paid_amount = order.payments|sum(attribute='amount') %}
                        {% set remaining = order.total - paid_amount %}
                        <div class="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
                            <div class="flex justify-between items-center mb-2">
                                <span class="font-medium text-gray-800 dark:text-white">{{ order.invoice_number }}</span>
                                <span class="text-sm text-gray-500 dark:text-gray-400">{{ order.created_at.strftime('%Y-%m-%d') }}</span>
                            </div>
                            <div class="flex justify-between items-center mb-2">
                                <span class="text-sm text-gray-600 dark:text-gray-400">المبلغ الكلي</span>
                                <span class="font-medium text-gray-800 dark:text-white">{{ "%.2f"|format(order.total) }} ج.م</span>
                            </div>
                            <div class="flex justify-between items-center mb-2">
                                <span class="text-sm text-gray-600 dark:text-gray-400">المدفوع</span>
                                <span class="font-medium text-green-600 dark:text-green-400">{{ "%.2f"|format(paid_amount) }} ج.م</span>
                            </div>
                            <div class="flex justify-between items-center">
                                <span class="text-sm text-gray-600 dark:text-gray-400">المتبقي</span>
                                <span class="font-medium text-red-600 dark:text-red-400">{{ "%.2f"|format(remaining) }} ج.م</span>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                </div>
                {% endif %}
            </div>
        </div>
    </div>

    {% include 'partials/footer.html' %}

    <script>
        // تبديل الوضع المظلم
        const darkModeToggle = document.getElementById('darkModeToggle');
        if (darkModeToggle) {
            darkModeToggle.addEventListener('click', function() {
                document.body.classList.toggle('dark');
                if (document.body.classList.contains('dark')) {
                    localStorage.setItem('darkMode', 'enabled');
                } else {
                    localStorage.setItem('darkMode', 'disabled');
                }
            });
        }

        // تحقق من حالة الوضع المظلم عند تحميل الصفحة
        if (localStorage.getItem('darkMode') === 'enabled') {
            document.body.classList.add('dark');
        }

        // اختيار طريقة الدفع
        document.querySelectorAll('.payment-method-card').forEach(card => {
            card.addEventListener('click', function() {
                // إزالة الفئة المحددة من جميع البطاقات
                document.querySelectorAll('.payment-method-card').forEach(c => {
                    c.classList.remove('selected');
                });

                // إضافة الفئة المحددة إلى البطاقة المحددة
                this.classList.add('selected');

                // تحديد زر الراديو المقابل
                const method = this.getAttribute('data-method');
                document.querySelector(`input[name="payment_method"][value="${method}"]`).checked = true;
            });
        });

        // بيانات الفواتير
        const orderData = {
            {% for order in orders %}
            {% set paid_amount = order.payments|sum(attribute='amount') %}
            {% set remaining = order.total - paid_amount %}
            "{{ order.id }}": {
                "total": {{ order.total }},
                "paid": {{ paid_amount }},
                "remaining": {{ remaining }}
            },
            {% endfor %}
        };

        // تحديث المبلغ عند اختيار فاتورة
        const orderSelect = document.getElementById('order_id');
        const amountInput = document.getElementById('amount');

        if (orderSelect && amountInput) {
            orderSelect.addEventListener('change', function() {
                const orderId = this.value;
                if (orderId && orderData[orderId]) {
                    // تعيين المبلغ المتبقي كقيمة افتراضية
                    amountInput.value = orderData[orderId].remaining.toFixed(2);
                }
            });

            // تشغيل الحدث عند تحميل الصفحة إذا كانت هناك فاتورة محددة
            if (orderSelect.value && orderData[orderSelect.value]) {
                amountInput.value = orderData[orderSelect.value].remaining.toFixed(2);
            }
        }
    </script>
</body>
</html>
