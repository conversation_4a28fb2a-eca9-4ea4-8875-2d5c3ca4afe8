<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>فاتورة مرتجع - {{ return_order.reference_number }}</title>
    <style>
        @page {
            size: A4;
            margin: 10mm;
        }
        body {
            font-family: 'Arial', sans-serif;
            margin: 0;
            padding: 0;
            background-color: white;
            font-size: 12px;
        }
        .invoice {
            width: 210mm;
            margin: 0 auto;
            padding: 10mm;
            box-sizing: border-box;
        }
        .header {
            display: flex;
            justify-content: space-between;
            margin-bottom: 20px;
            border-bottom: 2px solid #3B82F6;
            padding-bottom: 20px;
        }
        .logo-container {
            flex: 1;
        }
        .logo {
            max-width: 150px;
            max-height: 80px;
        }
        .store-info {
            flex: 2;
            text-align: center;
        }
        .store-name {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 5px;
            color: #3B82F6;
        }
        .store-details {
            font-size: 12px;
            color: #666;
        }
        .invoice-title {
            flex: 1;
            text-align: left;
        }
        .title {
            font-size: 24px;
            font-weight: bold;
            color: #EF4444;
            margin-bottom: 5px;
        }
        .invoice-number {
            font-size: 14px;
            color: #666;
        }
        .info-section {
            display: flex;
            justify-content: space-between;
            margin-bottom: 20px;
        }
        .info-column {
            flex: 1;
        }
        .info-row {
            margin-bottom: 8px;
            display: flex;
        }
        .info-label {
            font-weight: bold;
            width: 120px;
            color: #3B82F6;
        }
        .info-value {
            flex: 1;
        }
        .items-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }
        .items-table th {
            background-color: #3B82F6;
            color: white;
            padding: 10px;
            text-align: right;
            font-size: 14px;
        }
        .items-table td {
            padding: 10px;
            border-bottom: 1px solid #ddd;
            font-size: 12px;
        }
        .items-table tr:nth-child(even) {
            background-color: #f9f9f9;
        }
        .item-reason {
            font-size: 10px;
            color: #666;
            font-style: italic;
        }
        .totals-section {
            display: flex;
            justify-content: space-between;
            margin-top: 20px;
        }
        .notes {
            flex: 1;
            padding: 10px;
            background-color: #f9f9f9;
            border-radius: 5px;
            font-size: 12px;
        }
        .notes-title {
            font-weight: bold;
            margin-bottom: 5px;
            color: #3B82F6;
        }
        .totals {
            flex: 1;
            text-align: left;
        }
        .total-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 5px;
            padding: 5px 0;
        }
        .total-label {
            font-weight: bold;
            color: #666;
        }
        .total-value {
            font-weight: bold;
        }
        .grand-total {
            font-size: 18px;
            font-weight: bold;
            color: #EF4444;
            border-top: 2px solid #EF4444;
            padding-top: 10px;
            margin-top: 10px;
        }
        .footer {
            margin-top: 40px;
            padding-top: 20px;
            border-top: 1px solid #ddd;
            text-align: center;
            font-size: 12px;
            color: #666;
        }
        .barcode {
            text-align: center;
            margin: 20px 0;
        }
        .barcode img {
            max-width: 200px;
        }
        .powered-by {
            text-align: center;
            font-size: 12px;
            margin-top: 20px;
            font-style: italic;
            color: #3B82F6;
        }
        .signature-section {
            display: flex;
            justify-content: space-between;
            margin-top: 40px;
        }
        .signature-box {
            flex: 1;
            text-align: center;
            margin: 0 20px;
        }
        .signature-title {
            font-weight: bold;
            margin-bottom: 40px;
            color: #666;
        }
        .signature-line {
            border-top: 1px solid #666;
            width: 80%;
            margin: 0 auto;
        }
        @media print {
            .no-print {
                display: none;
            }
            body {
                margin: 0;
                padding: 0;
            }
            .invoice {
                width: 100%;
                margin: 0;
                padding: 0;
            }
        }
    </style>
</head>
<body>
    <div class="invoice">
        <div class="header">
            <div class="logo-container">
                {% if store_info.logo %}
                <img src="{{ store_info.logo }}" alt="Logo" class="logo">
                {% endif %}
            </div>
            <div class="store-info">
                <div class="store-name">{{ store_info.name }}</div>
                <div class="store-details">{{ store_info.address }}</div>
                <div class="store-details">هاتف: {{ store_info.phone }}</div>
                {% if store_info.tax_number %}
                <div class="store-details">الرقم الضريبي: {{ store_info.tax_number }}</div>
                {% endif %}
            </div>
            <div class="invoice-title">
                <div class="title">فاتورة مرتجع</div>
                <div class="invoice-number">رقم المرتجع: {{ return_order.reference_number }}</div>
            </div>
        </div>

        <div class="info-section">
            <div class="info-column">
                <div class="info-row">
                    <div class="info-label">رقم الفاتورة الأصلية:</div>
                    <div class="info-value">{{ order.invoice_number }}</div>
                </div>
                <div class="info-row">
                    <div class="info-label">تاريخ المرتجع:</div>
                    <div class="info-value">{{ return_order.created_at.strftime('%Y-%m-%d %H:%M') }}</div>
                </div>
                <div class="info-row">
                    <div class="info-label">طريقة الاسترداد:</div>
                    <div class="info-value">
                        {% if return_order.payment_method == 'cash' %}
                            نقدي
                        {% elif return_order.payment_method == 'card' %}
                            بطاقة
                        {% elif return_order.payment_method == 'store_credit' %}
                            رصيد متجر
                        {% else %}
                            {{ return_order.payment_method }}
                        {% endif %}
                    </div>
                </div>
            </div>
            <div class="info-column">
                <div class="info-row">
                    <div class="info-label">العميل:</div>
                    <div class="info-value">{{ order.customer.name if order.customer else 'عميل نقدي' }}</div>
                </div>
                <div class="info-row">
                    <div class="info-label">الموظف:</div>
                    <div class="info-value">{{ return_order.user.username }}</div>
                </div>
                <div class="info-row">
                    <div class="info-label">الحالة:</div>
                    <div class="info-value">
                        {% if return_order.status == 'completed' %}
                            <span style="color: green;">مكتمل</span>
                        {% elif return_order.status == 'pending' %}
                            <span style="color: orange;">قيد الانتظار</span>
                        {% elif return_order.status == 'cancelled' %}
                            <span style="color: red;">ملغي</span>
                        {% else %}
                            {{ return_order.status }}
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>

        <table class="items-table">
            <thead>
                <tr>
                    <th style="width: 5%;">#</th>
                    <th style="width: 40%;">المنتج</th>
                    <th style="width: 15%;">السعر</th>
                    <th style="width: 10%;">الكمية</th>
                    <th style="width: 15%;">الإجمالي</th>
                    <th style="width: 15%;">سبب الإرجاع</th>
                </tr>
            </thead>
            <tbody>
                {% for item in return_order.items %}
                <tr>
                    <td>{{ loop.index }}</td>
                    <td>{{ item.product.name }}</td>
                    <td>{{ "%.2f"|format(item.price) }} ج.م</td>
                    <td>{{ item.quantity }}</td>
                    <td>{{ "%.2f"|format(item.total) }} ج.م</td>
                    <td class="item-reason">{{ item.reason }}</td>
                </tr>
                {% endfor %}
            </tbody>
        </table>

        <div class="totals-section">
            {% if return_order.notes %}
            <div class="notes">
                <div class="notes-title">ملاحظات:</div>
                <div>{{ return_order.notes }}</div>
            </div>
            {% endif %}
            <div class="totals">
                <div class="total-row grand-total">
                    <div class="total-label">إجمالي المبلغ المسترد:</div>
                    <div class="total-value">{{ "%.2f"|format(return_order.total_amount) }} ج.م</div>
                </div>
            </div>
        </div>

        <div class="signature-section">
            <div class="signature-box">
                <div class="signature-title">توقيع المستلم</div>
                <div class="signature-line"></div>
            </div>
            <div class="signature-box">
                <div class="signature-title">توقيع الموظف</div>
                <div class="signature-line"></div>
            </div>
        </div>

        <div class="barcode">
            <img src="data:image/png;base64,{{ barcode_data }}" alt="Barcode">
            <div>{{ return_order.reference_number }}</div>
        </div>

        <div class="footer">
            <div>هذه الفاتورة دليل على استرداد المبلغ</div>
            <div>شكراً لتعاملكم معنا</div>
        </div>

        <div class="powered-by">
            Powered By ENG/ Fouad Saber Tel: 01020073527
        </div>
    </div>

    <div class="no-print" style="text-align: center; margin: 20px 0;">
        <button onclick="window.print()" style="padding: 10px 20px; background-color: #3B82F6; color: white; border: none; border-radius: 5px; cursor: pointer;">
            طباعة الفاتورة
        </button>
    </div>

    <script>
        // طباعة تلقائية عند تحميل الصفحة
        window.onload = function() {
            setTimeout(function() {
                window.print();
            }, 1000);
        };
    </script>
</body>
</html>
