<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Nobara - إعدادات الباركود</title>
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <!-- Remixicon -->
    <link href="https://cdn.jsdelivr.net/npm/remixicon@2.5.0/fonts/remixicon.css" rel="stylesheet">
    <!-- Alpine.js -->
    <script defer src="https://cdn.jsdelivr.net/npm/alpinejs@3.x.x/dist/cdn.min.js"></script>
    <script>
        tailwind.config = {
            darkMode: 'class',
            theme: {
                extend: {
                    colors: {
                        primary: {
                            50: '#eff6ff',
                            100: '#dbeafe',
                            200: '#bfdbfe',
                            300: '#93c5fd',
                            400: '#60a5fa',
                            500: '#3b82f6',
                            600: '#2563eb',
                            700: '#1d4ed8',
                            800: '#1e40af',
                            900: '#1e3a8a',
                            950: '#172554',
                        },
                        dark: {
                            100: '#1E293B',
                            200: '#0F172A',
                            300: '#0B1120',
                            400: '#060A15'
                        }
                    }
                }
            }
        }
    </script>
    <style>
        body {
            font-family: 'Cairo', 'Tajawal', sans-serif;
            transition: background-color 0.3s ease, color 0.3s ease;
        }

        .bg-pattern {
            background-color: #f9fafb;
            background-image: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%233B82F6' fill-opacity='0.05'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
        }

        .dark .bg-pattern {
            background-color: #0F172A;
            background-image: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%233B82F6' fill-opacity='0.1'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
        }

        .powered-by {
            position: fixed;
            bottom: 1rem;
            left: 1rem;
            background-color: #ffffff;
            border-radius: 1rem;
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
            padding: 0.75rem 1rem;
            display: flex;
            align-items: center;
            z-index: 40;
            transition: all 0.3s ease;
            border: 1px solid #e5e7eb;
        }

        .dark .powered-by {
            background-color: #1E293B;
            border-color: #2D3748;
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.3), 0 4px 6px -2px rgba(0, 0, 0, 0.2);
        }

        .powered-by:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
        }

        .dark .powered-by:hover {
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.3), 0 10px 10px -5px rgba(0, 0, 0, 0.2);
        }

        /* معاينة الباركود */
        .barcode-preview {
            background-color: white;
            padding: 10px;
            border: 1px solid #e5e7eb;
            border-radius: 0.5rem;
            box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
            text-align: center;
        }

        .dark .barcode-preview {
            background-color: #1f2937;
            border-color: #374151;
        }

        .barcode-image {
            width: 100%;
            height: 80px;
            background-color: white;
            margin-bottom: 5px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .barcode-text {
            font-family: monospace;
            font-size: 14px;
            color: #111827;
        }

        .dark .barcode-text {
            color: #f9fafb;
        }
    </style>
</head>
<body class="bg-pattern dark:bg-dark-200 dark:text-gray-100" x-data="{ darkMode: localStorage.getItem('darkMode') === 'enabled' }">
    <div class="flex h-screen overflow-hidden">
        <!-- Sidebar -->
        {% include 'partials/sidebar.html' %}

        <!-- Main Content -->
        <div class="flex-1 flex flex-col overflow-hidden">
            <!-- Top Navigation -->
            {% include 'partials/topnav.html' %}

            <!-- Content Area -->
            <main class="flex-1 overflow-y-auto p-6">
                <!-- Header with Gradient Background -->
                <div class="mb-8 bg-gradient-to-r from-blue-600 to-indigo-700 rounded-xl shadow-lg p-6 relative overflow-hidden">
                    <!-- Decorative Elements -->
                    <div class="absolute top-0 right-0 w-64 h-64 bg-white opacity-5 rounded-full -mt-16 -mr-16"></div>
                    <div class="absolute bottom-0 left-0 w-32 h-32 bg-white opacity-5 rounded-full -mb-10 -ml-10"></div>

                    <div class="relative flex justify-between items-center">
                        <div>
                            <h1 class="text-2xl md:text-3xl font-bold text-white flex items-center">
                                <i class="ri-barcode-line ml-3 text-blue-200"></i>
                                إعدادات الباركود
                            </h1>
                            <p class="text-blue-100 mt-2 max-w-xl">تكوين تنسيق الباركود وإعدادات الميزان وتخصيص مقاسات الليبل</p>
                        </div>
                        <div class="flex items-center gap-2">
                            <a href="{{ url_for('settings.index') }}" class="flex items-center gap-1 bg-white/20 hover:bg-white/30 text-white py-2 px-4 rounded-lg transition-colors backdrop-blur-sm">
                                <i class="ri-arrow-right-line"></i>
                                <span>العودة للإعدادات</span>
                            </a>
                            <button @click="darkMode = !darkMode; localStorage.setItem('darkMode', darkMode ? 'enabled' : 'disabled'); document.documentElement.classList.toggle('dark')" class="p-2 rounded-full bg-white/20 hover:bg-white/30 text-white transition-colors backdrop-blur-sm">
                                <i class="ri-moon-line dark:hidden"></i>
                                <i class="ri-sun-line hidden dark:block"></i>
                            </button>
                        </div>
                    </div>
                </div>

                <!-- بطاقات الإعدادات -->
                <div class="grid grid-cols-1 gap-8 mb-8">
                    <!-- بطاقة إعدادات الباركود -->
                    <div class="bg-white dark:bg-dark-100 rounded-2xl shadow-lg overflow-hidden border border-gray-200 dark:border-gray-700">
                        <div class="bg-gradient-to-r from-blue-600 to-indigo-700 p-5 relative overflow-hidden">
                            <!-- زخرفة الخلفية -->
                            <div class="absolute top-0 right-0 w-32 h-32 bg-white opacity-5 rounded-full -mt-10 -mr-10"></div>

                            <h2 class="text-xl font-bold text-white flex items-center relative z-10">
                                <span class="flex items-center justify-center w-10 h-10 rounded-full bg-white/20 ml-3">
                                    <i class="ri-barcode-line text-white"></i>
                                </span>
                                إعدادات الباركود
                            </h2>
                            <p class="text-blue-100 text-sm mt-2 mr-12">تكوين نوع وحجم وخصائص الباركود</p>
                        </div>
                        <div class="p-5">
                            <form id="barcodeSettingsForm" action="{{ url_for('settings.update_barcode_settings') }}" method="POST">
                                <div>
                                    <!-- قسم إعدادات الباركود الأساسية -->
                                    <div class="bg-blue-50 dark:bg-blue-900/20 rounded-xl p-5 border border-blue-100 dark:border-blue-900/30 mb-8">
                                        <h3 class="text-lg font-bold text-blue-800 dark:text-blue-200 mb-4 flex items-center">
                                            <i class="ri-barcode-box-line ml-2 text-blue-600"></i>
                                            إعدادات الباركود الأساسية
                                        </h3>

                                        <div class="grid grid-cols-1 md:grid-cols-2 gap-5">
                                            <!-- نوع الباركود -->
                                            <div class="bg-white dark:bg-gray-800 p-4 rounded-lg border border-gray-200 dark:border-gray-700">
                                                <div class="flex items-center mb-2">
                                                    <i class="ri-barcode-box-line text-blue-500 ml-2"></i>
                                                    <label for="barcode_type" class="block text-sm font-medium text-gray-700 dark:text-gray-300">نوع الباركود</label>
                                                </div>
                                                <select id="barcode_type" name="barcode_type" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-dark-200 dark:text-gray-100">
                                                    <option value="CODE128" {% if settings.barcode.barcode_type == 'CODE128' %}selected{% endif %}>CODE128 (الأكثر استخداماً)</option>
                                                    <option value="CODE39" {% if settings.barcode.barcode_type == 'CODE39' %}selected{% endif %}>CODE39 (أحرف وأرقام)</option>
                                                    <option value="EAN13" {% if settings.barcode.barcode_type == 'EAN13' %}selected{% endif %}>EAN13 (13 رقم)</option>
                                                    <option value="EAN8" {% if settings.barcode.barcode_type == 'EAN8' %}selected{% endif %}>EAN8 (8 أرقام)</option>
                                                    <option value="UPC" {% if settings.barcode.barcode_type == 'UPC' %}selected{% endif %}>UPC (12 رقم)</option>
                                                </select>
                                                <div class="text-xs text-gray-500 dark:text-gray-400 mt-1">اختر نوع الباركود المناسب لمتطلبات متجرك</div>
                                            </div>

                                            <!-- حجم الباركود -->
                                            <div class="bg-white dark:bg-gray-800 p-4 rounded-lg border border-gray-200 dark:border-gray-700">
                                                <div class="flex items-center mb-2">
                                                    <i class="ri-ruler-2-line text-blue-500 ml-2"></i>
                                                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">أبعاد الباركود</label>
                                                </div>
                                                <div class="grid grid-cols-2 gap-3">
                                                    <div>
                                                        <label for="barcode_width" class="block text-xs text-gray-600 dark:text-gray-400 mb-1">العرض (مم)</label>
                                                        <input type="number" id="barcode_width" name="barcode_width" value="{{ settings.barcode.barcode_width }}" min="20" max="100" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-dark-200 dark:text-gray-100">
                                                    </div>
                                                    <div>
                                                        <label for="barcode_height" class="block text-xs text-gray-600 dark:text-gray-400 mb-1">الارتفاع (مم)</label>
                                                        <input type="number" id="barcode_height" name="barcode_height" value="{{ settings.barcode.barcode_height }}" min="10" max="50" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-dark-200 dark:text-gray-100">
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- قسم محتويات الباركود -->
                                    <div class="bg-indigo-50 dark:bg-indigo-900/20 rounded-xl p-5 border border-indigo-100 dark:border-indigo-900/30 mb-8">
                                        <h3 class="text-lg font-bold text-indigo-800 dark:text-indigo-200 mb-4 flex items-center">
                                            <i class="ri-list-check-2 ml-2 text-indigo-600"></i>
                                            محتويات الباركود
                                        </h3>

                                        <!-- عناصر الباركود -->
                                        <div class="bg-white dark:bg-gray-800 p-4 rounded-lg border border-gray-200 dark:border-gray-700 mb-5">
                                            <div class="flex items-center mb-3">
                                                <i class="ri-list-check-2 text-indigo-500 ml-2"></i>
                                                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">عناصر الباركود</label>
                                            </div>
                                            <div class="grid grid-cols-2 md:grid-cols-4 gap-3">
                                                <label class="flex items-center bg-indigo-50 dark:bg-indigo-900/30 p-2 rounded border border-indigo-100 dark:border-indigo-800/50">
                                                    <input type="checkbox" id="show_text" name="show_text" {% if settings.barcode.show_text %}checked{% endif %} class="w-4 h-4 text-indigo-600 border-gray-300 rounded focus:ring-indigo-500 dark:bg-dark-200 dark:border-gray-600">
                                                    <span class="mr-2 text-sm text-gray-700 dark:text-gray-300">النص أسفل الباركود</span>
                                                </label>

                                                <label class="flex items-center bg-indigo-50 dark:bg-indigo-900/30 p-2 rounded border border-indigo-100 dark:border-indigo-800/50">
                                                    <input type="checkbox" id="show_product_name" name="show_product_name" {% if settings.barcode.show_product_name %}checked{% endif %} class="w-4 h-4 text-indigo-600 border-gray-300 rounded focus:ring-indigo-500 dark:bg-dark-200 dark:border-gray-600">
                                                    <span class="mr-2 text-sm text-gray-700 dark:text-gray-300">اسم المنتج</span>
                                                </label>

                                                <label class="flex items-center bg-indigo-50 dark:bg-indigo-900/30 p-2 rounded border border-indigo-100 dark:border-indigo-800/50">
                                                    <input type="checkbox" id="show_price" name="show_price" {% if settings.barcode.show_price %}checked{% endif %} class="w-4 h-4 text-indigo-600 border-gray-300 rounded focus:ring-indigo-500 dark:bg-dark-200 dark:border-gray-600">
                                                    <span class="mr-2 text-sm text-gray-700 dark:text-gray-300">سعر المنتج</span>
                                                </label>

                                                <label class="flex items-center bg-indigo-50 dark:bg-indigo-900/30 p-2 rounded border border-indigo-100 dark:border-indigo-800/50">
                                                    <input type="checkbox" id="show_weight" name="show_weight" {% if settings.barcode.show_weight %}checked{% endif %} class="w-4 h-4 text-indigo-600 border-gray-300 rounded focus:ring-indigo-500 dark:bg-dark-200 dark:border-gray-600">
                                                    <span class="mr-2 text-sm text-gray-700 dark:text-gray-300">الوزن والإجمالي</span>
                                                </label>

                                                <label class="flex items-center bg-indigo-50 dark:bg-indigo-900/30 p-2 rounded border border-indigo-100 dark:border-indigo-800/50">
                                                    <input type="checkbox" id="show_date" name="show_date" {% if settings.barcode.show_date %}checked{% endif %} class="w-4 h-4 text-indigo-600 border-gray-300 rounded focus:ring-indigo-500 dark:bg-dark-200 dark:border-gray-600">
                                                    <span class="mr-2 text-sm text-gray-700 dark:text-gray-300">تاريخ الإنتاج</span>
                                                </label>

                                                <label class="flex items-center bg-indigo-50 dark:bg-indigo-900/30 p-2 rounded border border-indigo-100 dark:border-indigo-800/50">
                                                    <input type="checkbox" id="show_time" name="show_time" {% if settings.barcode.show_time %}checked{% endif %} class="w-4 h-4 text-indigo-600 border-gray-300 rounded focus:ring-indigo-500 dark:bg-dark-200 dark:border-gray-600">
                                                    <span class="mr-2 text-sm text-gray-700 dark:text-gray-300">الوقت واليوم</span>
                                                </label>

                                                <label class="flex items-center bg-indigo-50 dark:bg-indigo-900/30 p-2 rounded border border-indigo-100 dark:border-indigo-800/50">
                                                    <input type="checkbox" id="show_store_name" name="show_store_name" {% if settings.barcode.show_store_name %}checked{% endif %} class="w-4 h-4 text-indigo-600 border-gray-300 rounded focus:ring-indigo-500 dark:bg-dark-200 dark:border-gray-600">
                                                    <span class="mr-2 text-sm text-gray-700 dark:text-gray-300">اسم المتجر</span>
                                                </label>
                                            </div>
                                        </div>

                                        <!-- إعدادات الخط -->
                                        <div class="bg-white dark:bg-gray-800 p-4 rounded-lg border border-gray-200 dark:border-gray-700">
                                            <div class="flex items-center mb-3">
                                                <i class="ri-font-size text-indigo-500 ml-2"></i>
                                                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">إعدادات الخط</label>
                                            </div>

                                            <div class="grid grid-cols-2 md:grid-cols-4 gap-3">
                                                <div>
                                                    <label for="font_family" class="block text-xs text-gray-600 dark:text-gray-400 mb-1">نوع الخط</label>
                                                    <select id="font_family" name="font_family" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500 dark:bg-dark-200 dark:text-gray-100">
                                                        <option value="Arial" {% if settings.barcode.font_family == 'Arial' %}selected{% endif %}>Arial</option>
                                                        <option value="Tahoma" {% if settings.barcode.font_family == 'Tahoma' %}selected{% endif %}>Tahoma</option>
                                                        <option value="Calibri" {% if settings.barcode.font_family == 'Calibri' %}selected{% endif %}>Calibri</option>
                                                        <option value="Times New Roman" {% if settings.barcode.font_family == 'Times New Roman' %}selected{% endif %}>Times New Roman</option>
                                                    </select>
                                                </div>

                                                <div>
                                                    <label for="font_size_main" class="block text-xs text-gray-600 dark:text-gray-400 mb-1">حجم الخط الرئيسي</label>
                                                    <input type="number" id="font_size_main" name="font_size_main" value="{{ settings.barcode.font_size_main }}" min="8" max="16" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500 dark:bg-dark-200 dark:text-gray-100">
                                                </div>

                                                <div>
                                                    <label for="font_size_product_name" class="block text-xs text-gray-600 dark:text-gray-400 mb-1">حجم خط اسم المنتج</label>
                                                    <input type="number" id="font_size_product_name" name="font_size_product_name" value="{{ settings.barcode.font_size_product_name }}" min="8" max="16" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500 dark:bg-dark-200 dark:text-gray-100">
                                                </div>

                                                <div>
                                                    <label for="font_size_price" class="block text-xs text-gray-600 dark:text-gray-400 mb-1">حجم خط السعر</label>
                                                    <input type="number" id="font_size_price" name="font_size_price" value="{{ settings.barcode.font_size_price }}" min="8" max="16" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500 dark:bg-dark-200 dark:text-gray-100">
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- قسم مقاسات ليبل الباركود -->
                                    <div class="bg-green-50 dark:bg-green-900/20 rounded-xl p-5 border border-green-100 dark:border-green-900/30 mb-8">
                                        <h3 class="text-lg font-bold text-green-800 dark:text-green-200 mb-4 flex items-center">
                                            <i class="ri-layout-4-line ml-2 text-green-600"></i>
                                            مقاسات ليبل الباركود
                                        </h3>

                                        <div class="grid grid-cols-1 md:grid-cols-2 gap-5">
                                            <!-- اختيار مقاس الليبل -->
                                            <div class="bg-white dark:bg-gray-800 p-4 rounded-lg border border-gray-200 dark:border-gray-700">
                                                <div class="flex items-center mb-3">
                                                    <i class="ri-layout-masonry-line text-green-500 ml-2"></i>
                                                    <label for="label_size" class="block text-sm font-medium text-gray-700 dark:text-gray-300">مقاس الليبل</label>
                                                </div>
                                                <select id="label_size" name="label_size" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 dark:bg-dark-200 dark:text-gray-100">
                                                    <option value="small" {% if settings.barcode.label_size == 'small' %}selected{% endif %}>صغير (30×20 مم)</option>
                                                    <option value="medium" {% if settings.barcode.label_size == 'medium' %}selected{% endif %}>متوسط (50×30 مم)</option>
                                                    <option value="large" {% if settings.barcode.label_size == 'large' %}selected{% endif %}>كبير (70×40 مم)</option>
                                                    <option value="a4_labels" {% if settings.barcode.label_size == 'a4_labels' %}selected{% endif %}>ورقة A4 مقسمة (24 ليبل)</option>
                                                    <option value="thermal_30" {% if settings.barcode.label_size == 'thermal_30' %}selected{% endif %}>ليبل حراري 30 مم</option>
                                                    <option value="thermal_40" {% if settings.barcode.label_size == 'thermal_40' %}selected{% endif %}>ليبل حراري 40 مم</option>
                                                    <option value="thermal_50" {% if settings.barcode.label_size == 'thermal_50' %}selected{% endif %}>ليبل حراري 50 مم</option>
                                                    <option value="custom" {% if settings.barcode.label_size == 'custom' %}selected{% endif %}>مقاس مخصص</option>
                                                </select>

                                                <!-- مقاس مخصص (يظهر فقط عند اختيار مقاس مخصص) -->
                                                <div id="custom_size_container" class="mt-3 p-3 bg-green-50 dark:bg-green-900/20 rounded-lg border border-green-100 dark:border-green-900/30 {% if settings.barcode.label_size != 'custom' %}hidden{% endif %}">
                                                    <div class="grid grid-cols-2 gap-3">
                                                        <div>
                                                            <label for="custom_label_width" class="block text-xs text-gray-600 dark:text-gray-400 mb-1">العرض (مم)</label>
                                                            <input type="number" id="custom_label_width" name="custom_label_width" value="{{ settings.barcode.custom_label_width }}" min="10" max="200" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 dark:bg-dark-200 dark:text-gray-100">
                                                        </div>
                                                        <div>
                                                            <label for="custom_label_height" class="block text-xs text-gray-600 dark:text-gray-400 mb-1">الارتفاع (مم)</label>
                                                            <input type="number" id="custom_label_height" name="custom_label_height" value="{{ settings.barcode.custom_label_height }}" min="10" max="200" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 dark:bg-dark-200 dark:text-gray-100">
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>

                                            <!-- اتجاه الليبل -->
                                            <div class="bg-white dark:bg-gray-800 p-4 rounded-lg border border-gray-200 dark:border-gray-700">
                                                <div class="flex items-center mb-3">
                                                    <i class="ri-repeat-line text-green-500 ml-2"></i>
                                                    <label for="label_orientation" class="block text-sm font-medium text-gray-700 dark:text-gray-300">اتجاه الليبل</label>
                                                </div>
                                                <select id="label_orientation" name="label_orientation" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 dark:bg-dark-200 dark:text-gray-100">
                                                    <option value="portrait" {% if settings.barcode.label_orientation == 'portrait' %}selected{% endif %}>رأسي (طولي)</option>
                                                    <option value="landscape" {% if settings.barcode.label_orientation == 'landscape' %}selected{% endif %}>أفقي (عرضي)</option>
                                                </select>

                                                <div class="grid grid-cols-2 gap-3 mt-3">
                                                    <div class="bg-green-50 dark:bg-green-900/20 p-2 rounded-lg border border-green-100 dark:border-green-900/30 flex items-center justify-center">
                                                        <div class="w-8 h-12 bg-white dark:bg-gray-700 rounded border border-gray-300 dark:border-gray-600 flex items-center justify-center text-xs text-gray-500 dark:text-gray-400">
                                                            رأسي
                                                        </div>
                                                    </div>
                                                    <div class="bg-green-50 dark:bg-green-900/20 p-2 rounded-lg border border-green-100 dark:border-green-900/30 flex items-center justify-center">
                                                        <div class="w-12 h-8 bg-white dark:bg-gray-700 rounded border border-gray-300 dark:border-gray-600 flex items-center justify-center text-xs text-gray-500 dark:text-gray-400">
                                                            أفقي
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="grid grid-cols-1 md:grid-cols-2 gap-5 mt-5">
                                            <!-- إعدادات الطباعة المتعددة -->
                                            <div class="bg-white dark:bg-gray-800 p-4 rounded-lg border border-gray-200 dark:border-gray-700">
                                                <div class="flex items-center mb-3">
                                                    <i class="ri-layout-grid-line text-green-500 ml-2"></i>
                                                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">إعدادات الطباعة المتعددة</label>
                                                </div>
                                                <div class="grid grid-cols-2 gap-3">
                                                    <div>
                                                        <label for="labels_per_row" class="block text-xs text-gray-600 dark:text-gray-400 mb-1">عدد الليبلات في الصف</label>
                                                        <input type="number" id="labels_per_row" name="labels_per_row" value="{{ settings.barcode.labels_per_row }}" min="1" max="10" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 dark:bg-dark-200 dark:text-gray-100">
                                                    </div>
                                                    <div>
                                                        <label for="labels_per_page" class="block text-xs text-gray-600 dark:text-gray-400 mb-1">عدد الليبلات في الصفحة</label>
                                                        <input type="number" id="labels_per_page" name="labels_per_page" value="{{ settings.barcode.labels_per_page }}" min="1" max="100" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 dark:bg-dark-200 dark:text-gray-100">
                                                    </div>
                                                </div>

                                                <div class="mt-3 p-2 bg-green-50 dark:bg-green-900/20 rounded-lg border border-green-100 dark:border-green-900/30 flex justify-center">
                                                    <div class="grid grid-cols-3 gap-1 w-32">
                                                        {% for i in range(9) %}
                                                        <div class="w-full aspect-square bg-white dark:bg-gray-700 rounded border border-gray-300 dark:border-gray-600"></div>
                                                        {% endfor %}
                                                    </div>
                                                </div>
                                            </div>

                                            <!-- هوامش وحشو الليبل -->
                                            <div class="bg-white dark:bg-gray-800 p-4 rounded-lg border border-gray-200 dark:border-gray-700">
                                                <div class="flex items-center mb-3">
                                                    <i class="ri-layout-right-line text-green-500 ml-2"></i>
                                                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">هوامش وحشو الليبل</label>
                                                </div>
                                                <div class="grid grid-cols-2 gap-3">
                                                    <div>
                                                        <label for="label_margin" class="block text-xs text-gray-600 dark:text-gray-400 mb-1">هامش الليبل (مم)</label>
                                                        <input type="number" id="label_margin" name="label_margin" value="{{ settings.barcode.label_margin }}" min="0" max="10" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 dark:bg-dark-200 dark:text-gray-100">
                                                    </div>
                                                    <div>
                                                        <label for="label_padding" class="block text-xs text-gray-600 dark:text-gray-400 mb-1">حشو داخلي (مم)</label>
                                                        <input type="number" id="label_padding" name="label_padding" value="{{ settings.barcode.label_padding }}" min="0" max="10" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 dark:bg-dark-200 dark:text-gray-100">
                                                    </div>
                                                </div>

                                                <div class="mt-3 p-2 bg-green-50 dark:bg-green-900/20 rounded-lg border border-green-100 dark:border-green-900/30 flex justify-center">
                                                    <div class="w-32 h-20 bg-white dark:bg-gray-700 rounded border-2 border-dashed border-gray-300 dark:border-gray-600 flex items-center justify-center">
                                                        <div class="w-24 h-14 bg-gray-100 dark:bg-gray-600 rounded flex items-center justify-center text-xs text-gray-500 dark:text-gray-400">
                                                            محتوى الليبل
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- قسم إعدادات ميزان الباركود -->
                                    <div class="bg-purple-50 dark:bg-purple-900/20 rounded-xl p-5 border border-purple-100 dark:border-purple-900/30 mb-8">
                                        <h3 class="text-lg font-bold text-purple-800 dark:text-purple-200 mb-4 flex items-center">
                                            <i class="ri-scales-3-line ml-2 text-purple-600"></i>
                                            إعدادات ميزان الباركود
                                        </h3>

                                        <!-- تفعيل الميزان -->
                                        <div class="bg-white dark:bg-gray-800 p-4 rounded-lg border border-gray-200 dark:border-gray-700 mb-5">
                                            <label class="flex items-center">
                                                <div class="relative inline-block w-12 h-6 ml-3">
                                                    <input type="checkbox" id="enable_scale" name="enable_scale" {% if settings.barcode.enable_scale %}checked{% endif %} class="sr-only peer">
                                                    <span class="absolute inset-0 bg-gray-300 dark:bg-gray-600 rounded-full transition peer-checked:bg-purple-500"></span>
                                                    <span class="absolute inset-0 w-4 h-4 m-1 bg-white rounded-full transition peer-checked:translate-x-6 rtl:peer-checked:-translate-x-6"></span>
                                                </div>
                                                <span class="text-base font-medium text-gray-700 dark:text-gray-300">تفعيل ميزان الباركود</span>
                                            </label>
                                            <p class="text-sm text-gray-500 dark:text-gray-400 mt-2 mr-15">تفعيل دعم الميزان الإلكتروني للمنتجات الموزونة وقراءة الوزن تلقائيًا</p>
                                        </div>

                                        <div class="grid grid-cols-1 md:grid-cols-2 gap-5">
                                            <!-- إعدادات تنسيق الباركود -->
                                            <div class="bg-white dark:bg-gray-800 p-4 rounded-lg border border-gray-200 dark:border-gray-700">
                                                <div class="flex items-center mb-3">
                                                    <i class="ri-barcode-box-line text-purple-500 ml-2"></i>
                                                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">تنسيق باركود الميزان</label>
                                                </div>

                                                <div class="space-y-3">
                                                    <!-- بادئة الميزان -->
                                                    <div>
                                                        <label for="scale_prefix" class="block text-xs text-gray-600 dark:text-gray-400 mb-1">بادئة الميزان</label>
                                                        <input type="text" id="scale_prefix" name="scale_prefix" value="{{ settings.barcode.scale_prefix }}" maxlength="2" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 dark:bg-dark-200 dark:text-gray-100">
                                                        <div class="text-xs text-gray-500 dark:text-gray-400 mt-1">الأرقام الأولى من الباركود التي تشير إلى أن المنتج موزون (عادة 20 أو 21)</div>
                                                    </div>

                                                    <!-- طول الباركود -->
                                                    <div>
                                                        <label for="barcode_length" class="block text-xs text-gray-600 dark:text-gray-400 mb-1">طول الباركود</label>
                                                        <input type="number" id="barcode_length" name="barcode_length" value="{{ settings.barcode.barcode_length }}" min="8" max="13" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 dark:bg-dark-200 dark:text-gray-100">
                                                    </div>

                                                    <!-- موضع الوزن -->
                                                    <div>
                                                        <label for="weight_position" class="block text-xs text-gray-600 dark:text-gray-400 mb-1">موضع الوزن</label>
                                                        <select id="weight_position" name="weight_position" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 dark:bg-dark-200 dark:text-gray-100">
                                                            <option value="end" {% if settings.barcode.weight_position == 'end' %}selected{% endif %}>نهاية الباركود</option>
                                                            <option value="middle" {% if settings.barcode.weight_position == 'middle' %}selected{% endif %}>وسط الباركود</option>
                                                        </select>
                                                    </div>
                                                </div>
                                            </div>

                                            <!-- إعدادات الوزن -->
                                            <div class="bg-white dark:bg-gray-800 p-4 rounded-lg border border-gray-200 dark:border-gray-700">
                                                <div class="flex items-center mb-3">
                                                    <i class="ri-scales-3-line text-purple-500 ml-2"></i>
                                                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">إعدادات الوزن</label>
                                                </div>

                                                <div class="space-y-3">
                                                    <!-- عدد خانات الوزن -->
                                                    <div>
                                                        <label for="weight_digits" class="block text-xs text-gray-600 dark:text-gray-400 mb-1">عدد خانات الوزن</label>
                                                        <input type="number" id="weight_digits" name="weight_digits" value="{{ settings.barcode.weight_digits }}" min="3" max="6" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 dark:bg-dark-200 dark:text-gray-100">
                                                    </div>

                                                    <!-- عدد الخانات العشرية -->
                                                    <div>
                                                        <label for="decimal_places" class="block text-xs text-gray-600 dark:text-gray-400 mb-1">عدد الخانات العشرية</label>
                                                        <input type="number" id="decimal_places" name="decimal_places" value="{{ settings.barcode.decimal_places }}" min="0" max="3" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 dark:bg-dark-200 dark:text-gray-100">
                                                    </div>

                                                    <!-- تنسيق عرض الوزن -->
                                                    <div>
                                                        <label for="weight_format" class="block text-xs text-gray-600 dark:text-gray-400 mb-1">تنسيق عرض الوزن</label>
                                                        <select id="weight_format" name="weight_format" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 dark:bg-dark-200 dark:text-gray-100">
                                                            <option value="weight_x_price" {% if settings.barcode.weight_format == 'weight_x_price' %}selected{% endif %}>الوزن × السعر = الإجمالي</option>
                                                            <option value="weight_only" {% if settings.barcode.weight_format == 'weight_only' %}selected{% endif %}>الوزن فقط</option>
                                                            <option value="total_only" {% if settings.barcode.weight_format == 'total_only' %}selected{% endif %}>الإجمالي فقط</option>
                                                        </select>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <!-- توضيح تنسيق الباركود -->
                                        <div class="bg-white dark:bg-gray-800 p-4 rounded-lg border border-gray-200 dark:border-gray-700 mt-5">
                                            <div class="flex items-center mb-3">
                                                <i class="ri-information-line text-purple-500 ml-2"></i>
                                                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">مثال لتنسيق باركود الميزان</label>
                                            </div>

                                            <div class="flex flex-col items-center">
                                                <div class="flex items-center justify-center space-x-1 space-x-reverse mb-3">
                                                    <div class="bg-purple-200 dark:bg-purple-800 px-3 py-2 rounded text-sm font-mono">{{ settings.barcode.scale_prefix }}</div>
                                                    <div class="bg-blue-200 dark:bg-blue-800 px-3 py-2 rounded text-sm font-mono">1234</div>
                                                    <div class="bg-green-200 dark:bg-green-800 px-3 py-2 rounded text-sm font-mono">01234</div>
                                                    <div class="bg-gray-200 dark:bg-gray-700 px-3 py-2 rounded text-sm font-mono">0</div>
                                                </div>
                                                <div class="flex items-center justify-center space-x-1 space-x-reverse">
                                                    <div class="text-xs text-purple-600 dark:text-purple-400 w-12 text-center">بادئة</div>
                                                    <div class="text-xs text-blue-600 dark:text-blue-400 w-12 text-center">كود المنتج</div>
                                                    <div class="text-xs text-green-600 dark:text-green-400 w-12 text-center">الوزن</div>
                                                    <div class="text-xs text-gray-600 dark:text-gray-400 w-12 text-center">تحقق</div>
                                                </div>
                                            </div>
                                        </div>

                                    <!-- اختبار الاتصال بالميزان -->
                                    <div class="mt-5 bg-white dark:bg-gray-800 p-5 rounded-lg border border-gray-200 dark:border-gray-700 shadow-sm">
                                        <h4 class="text-base font-bold text-purple-800 dark:text-purple-200 mb-4 flex items-center">
                                            <i class="ri-test-tube-line ml-2 text-purple-600"></i>
                                            اختبار الاتصال بالميزان
                                        </h4>

                                        <p class="text-sm text-gray-600 dark:text-gray-400 mb-4">قم بتوصيل الميزان واختبار القراءة للتأكد من عمله بشكل صحيح مع نظام نقطة البيع</p>

                                        <!-- طريقة الاتصال -->
                                        <div class="mb-4">
                                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">طريقة الاتصال</label>
                                            <div class="flex gap-3">
                                                <label class="flex items-center bg-purple-50 dark:bg-purple-900/20 p-3 rounded-lg border border-purple-100 dark:border-purple-900/30 flex-1 cursor-pointer">
                                                    <input type="radio" name="connection_type" value="serial" checked class="w-4 h-4 text-purple-600 border-gray-300 focus:ring-purple-500 dark:bg-dark-200 dark:border-gray-600">
                                                    <div class="mr-2">
                                                        <div class="text-sm font-medium text-gray-700 dark:text-gray-300">منفذ تسلسلي</div>
                                                        <div class="text-xs text-gray-500 dark:text-gray-400">COM Port</div>
                                                    </div>
                                                </label>
                                                <label class="flex items-center bg-purple-50 dark:bg-purple-900/20 p-3 rounded-lg border border-purple-100 dark:border-purple-900/30 flex-1 cursor-pointer">
                                                    <input type="radio" name="connection_type" value="network" class="w-4 h-4 text-purple-600 border-gray-300 focus:ring-purple-500 dark:bg-dark-200 dark:border-gray-600">
                                                    <div class="mr-2">
                                                        <div class="text-sm font-medium text-gray-700 dark:text-gray-300">شبكة</div>
                                                        <div class="text-xs text-gray-500 dark:text-gray-400">TCP/IP</div>
                                                    </div>
                                                </label>
                                                <label class="flex items-center bg-purple-50 dark:bg-purple-900/20 p-3 rounded-lg border border-purple-100 dark:border-purple-900/30 flex-1 cursor-pointer">
                                                    <input type="radio" name="connection_type" value="usb" class="w-4 h-4 text-purple-600 border-gray-300 focus:ring-purple-500 dark:bg-dark-200 dark:border-gray-600">
                                                    <div class="mr-2">
                                                        <div class="text-sm font-medium text-gray-700 dark:text-gray-300">USB</div>
                                                        <div class="text-xs text-gray-500 dark:text-gray-400">HID Device</div>
                                                    </div>
                                                </label>
                                            </div>
                                        </div>

                                        <!-- إعدادات الاتصال التسلسلي -->
                                        <div id="serial_settings" class="mb-4 p-4 bg-gray-50 dark:bg-gray-900/50 rounded-lg border border-gray-200 dark:border-gray-700">
                                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                                <div>
                                                    <label for="scale_port" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">منفذ الاتصال</label>
                                                    <div class="relative">
                                                        <select id="scale_port" name="scale_port" class="w-full pr-10 pl-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 dark:bg-dark-200 dark:text-gray-100 appearance-none">
                                                            <option value="COM1">COM1</option>
                                                            <option value="COM2">COM2</option>
                                                            <option value="COM3">COM3</option>
                                                            <option value="COM4">COM4</option>
                                                            <option value="COM5">COM5</option>
                                                            <option value="COM6">COM6</option>
                                                            <option value="COM7">COM7</option>
                                                            <option value="COM8">COM8</option>
                                                        </select>
                                                        <div class="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                                                            <i class="ri-arrow-down-s-line text-gray-500"></i>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div>
                                                    <label for="scale_baud_rate" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">معدل الباود</label>
                                                    <div class="relative">
                                                        <select id="scale_baud_rate" name="scale_baud_rate" class="w-full pr-10 pl-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 dark:bg-dark-200 dark:text-gray-100 appearance-none">
                                                            <option value="9600">9600</option>
                                                            <option value="4800">4800</option>
                                                            <option value="19200">19200</option>
                                                            <option value="38400">38400</option>
                                                            <option value="57600">57600</option>
                                                            <option value="115200">115200</option>
                                                        </select>
                                                        <div class="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                                                            <i class="ri-arrow-down-s-line text-gray-500"></i>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div>
                                                    <label for="scale_data_bits" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">بتات البيانات</label>
                                                    <select id="scale_data_bits" name="scale_data_bits" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 dark:bg-dark-200 dark:text-gray-100">
                                                        <option value="8">8</option>
                                                        <option value="7">7</option>
                                                        <option value="6">6</option>
                                                        <option value="5">5</option>
                                                    </select>
                                                </div>
                                                <div>
                                                    <label for="scale_parity" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">التماثل</label>
                                                    <select id="scale_parity" name="scale_parity" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 dark:bg-dark-200 dark:text-gray-100">
                                                        <option value="none">بدون</option>
                                                        <option value="even">زوجي</option>
                                                        <option value="odd">فردي</option>
                                                    </select>
                                                </div>
                                            </div>
                                        </div>

                                        <!-- إعدادات الاتصال بالشبكة -->
                                        <div id="network_settings" class="mb-4 p-4 bg-gray-50 dark:bg-gray-900/50 rounded-lg border border-gray-200 dark:border-gray-700 hidden">
                                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                                <div>
                                                    <label for="scale_ip" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">عنوان IP</label>
                                                    <input type="text" id="scale_ip" name="scale_ip" placeholder="*************" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 dark:bg-dark-200 dark:text-gray-100">
                                                </div>
                                                <div>
                                                    <label for="scale_port_number" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">رقم المنفذ</label>
                                                    <input type="number" id="scale_port_number" name="scale_port_number" placeholder="4001" value="4001" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 dark:bg-dark-200 dark:text-gray-100">
                                                </div>
                                            </div>
                                        </div>

                                        <!-- إعدادات الاتصال USB -->
                                        <div id="usb_settings" class="mb-4 p-4 bg-gray-50 dark:bg-gray-900/50 rounded-lg border border-gray-200 dark:border-gray-700 hidden">
                                            <div class="grid grid-cols-1 gap-4">
                                                <div>
                                                    <label for="scale_device" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">جهاز USB</label>
                                                    <select id="scale_device" name="scale_device" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 dark:bg-dark-200 dark:text-gray-100">
                                                        <option value="">-- اختر جهاز الميزان --</option>
                                                        <option value="VID_0922&PID_8003">Dibal Scale (VID:0922 PID:8003)</option>
                                                        <option value="VID_0EB8&PID_F000">Mettler Toledo (VID:0EB8 PID:F000)</option>
                                                        <option value="VID_1A86&PID_7523">Generic Scale (VID:1A86 PID:7523)</option>
                                                        <option value="VID_2341&PID_0043">Arduino Scale (VID:2341 PID:0043)</option>
                                                    </select>
                                                </div>
                                                <div>
                                                    <button type="button" id="refresh_usb_devices" class="text-purple-600 hover:text-purple-700 text-sm font-medium inline-flex items-center">
                                                        <i class="ri-refresh-line ml-1"></i>
                                                        <span>تحديث قائمة الأجهزة</span>
                                                    </button>
                                                </div>
                                            </div>
                                        </div>

                                        <!-- نموذج الميزان -->
                                        <div class="mb-4">
                                            <label for="scale_model" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">نموذج الميزان</label>
                                            <select id="scale_model" name="scale_model" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 dark:bg-dark-200 dark:text-gray-100">
                                                <option value="generic">عام - Generic Scale</option>
                                                <option value="dibal">Dibal (ديبال)</option>
                                                <option value="cas">CAS (كاس)</option>
                                                <option value="mettler">Mettler Toledo (ميتلر توليدو)</option>
                                                <option value="digi">Digi (ديجي)</option>
                                                <option value="ishida">Ishida (إيشيدا)</option>
                                                <option value="bizerba">Bizerba (بيزيربا)</option>
                                                <option value="custom">مخصص</option>
                                            </select>
                                        </div>

                                        <!-- أزرار الاتصال والاختبار -->
                                        <div class="flex gap-3 mb-4">
                                            <button type="button" id="connect_scale_btn" class="flex-1 bg-purple-600 hover:bg-purple-700 text-white py-2 px-4 rounded-lg inline-flex items-center justify-center transition-colors">
                                                <i class="ri-link ml-2"></i>
                                                <span>اتصال بالميزان</span>
                                            </button>
                                            <button type="button" id="test_scale_btn" class="flex-1 bg-green-600 hover:bg-green-700 text-white py-2 px-4 rounded-lg inline-flex items-center justify-center transition-colors">
                                                <i class="ri-scales-3-line ml-2"></i>
                                                <span>اختبار القراءة</span>
                                            </button>
                                        </div>

                                        <!-- حالة الاتصال -->
                                        <div id="connection_status" class="mb-4 p-3 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg border border-yellow-100 dark:border-yellow-900/30 flex items-center">
                                            <div class="w-3 h-3 rounded-full bg-yellow-500 ml-2"></div>
                                            <span class="text-sm text-yellow-700 dark:text-yellow-300">غير متصل</span>
                                        </div>

                                        <!-- نتائج الاختبار -->
                                        <div id="scale_test_results" class="hidden">
                                            <div class="border-t border-gray-200 dark:border-gray-700 pt-4 mb-3">
                                                <div class="flex items-center mb-3">
                                                    <i class="ri-file-list-3-line text-purple-500 ml-2"></i>
                                                    <h5 class="text-sm font-medium text-gray-700 dark:text-gray-300">نتائج اختبار الميزان</h5>
                                                </div>
                                            </div>

                                            <div class="grid grid-cols-2 gap-3 mb-3">
                                                <div class="bg-gray-50 dark:bg-gray-900/50 p-3 rounded-lg">
                                                    <div class="text-xs text-gray-500 dark:text-gray-400 mb-1">الوزن</div>
                                                    <div id="test_weight" class="text-base font-bold text-gray-800 dark:text-gray-200">0.000 كجم</div>
                                                </div>
                                                <div class="bg-gray-50 dark:bg-gray-900/50 p-3 rounded-lg">
                                                    <div class="text-xs text-gray-500 dark:text-gray-400 mb-1">السعر</div>
                                                    <div id="test_price" class="text-base font-bold text-gray-800 dark:text-gray-200">0.00 ج.م</div>
                                                </div>
                                                <div class="bg-gray-50 dark:bg-gray-900/50 p-3 rounded-lg">
                                                    <div class="text-xs text-gray-500 dark:text-gray-400 mb-1">الإجمالي</div>
                                                    <div id="test_total" class="text-base font-bold text-gray-800 dark:text-gray-200">0.00 ج.م</div>
                                                </div>
                                                <div class="bg-gray-50 dark:bg-gray-900/50 p-3 rounded-lg">
                                                    <div class="text-xs text-gray-500 dark:text-gray-400 mb-1">الباركود</div>
                                                    <div id="test_barcode" class="text-base font-mono font-bold text-gray-800 dark:text-gray-200">-</div>
                                                </div>
                                            </div>

                                            <!-- معاينة في نقطة البيع -->
                                            <div class="p-3 bg-teal-50 dark:bg-teal-900/20 rounded-lg border border-teal-100 dark:border-teal-900/30 mb-3">
                                                <div class="flex items-center mb-2">
                                                    <i class="ri-shopping-cart-line text-teal-500 ml-2"></i>
                                                    <h5 class="text-sm font-medium text-gray-700 dark:text-gray-300">معاينة في نقطة البيع</h5>
                                                </div>

                                                <div class="bg-white dark:bg-gray-800 p-3 rounded border border-gray-200 dark:border-gray-700">
                                                    <div class="flex items-center justify-between pb-2 mb-2 border-b border-gray-100 dark:border-gray-700">
                                                        <div class="text-sm font-medium text-gray-800 dark:text-white">تفاح أحمر</div>
                                                        <div class="text-sm text-gray-600 dark:text-gray-400">كود: 1234</div>
                                                    </div>

                                                    <div class="grid grid-cols-3 gap-2 text-sm">
                                                        <div>
                                                            <span class="text-gray-500 dark:text-gray-400">الوزن:</span>
                                                            <span class="font-medium text-gray-800 dark:text-white">1.234 كجم</span>
                                                        </div>
                                                        <div>
                                                            <span class="text-gray-500 dark:text-gray-400">السعر:</span>
                                                            <span class="font-medium text-gray-800 dark:text-white">40.00 ج.م</span>
                                                        </div>
                                                        <div>
                                                            <span class="text-gray-500 dark:text-gray-400">الإجمالي:</span>
                                                            <span class="font-medium text-teal-600 dark:text-teal-400">49.36 ج.م</span>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>

                                            <div class="text-center">
                                                <span class="text-xs text-gray-500 dark:text-gray-400">تم الاختبار في: <span id="test_time">{{ now.strftime('%Y-%m-%d %H:%M:%S') }}</span></span>
                                            </div>
                                        </div>
                                    </div>

                                <div class="md:col-span-2">
                                    <!-- قسم معاينة الباركود -->
                                    <div class="bg-teal-50 dark:bg-teal-900/20 rounded-xl p-5 border border-teal-100 dark:border-teal-900/30 mb-8">
                                        <h3 class="text-lg font-bold text-teal-800 dark:text-teal-200 mb-4 flex items-center">
                                            <i class="ri-eye-line ml-2 text-teal-600"></i>
                                            معاينة الباركود
                                        </h3>

                                        <p class="text-gray-600 dark:text-gray-400 mb-5">معاينة شكل الباركود قبل الطباعة وتجربة الخيارات المختلفة</p>

                                        <div class="grid grid-cols-1 md:grid-cols-2 gap-5 mb-5">
                                            <!-- اختيار نوع الباركود -->
                                            <div class="bg-white dark:bg-gray-800 p-4 rounded-lg border border-gray-200 dark:border-gray-700">
                                                <div class="flex items-center mb-3">
                                                    <i class="ri-file-type-line text-teal-500 ml-2"></i>
                                                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">نوع الباركود للمعاينة</label>
                                                </div>

                                                <div class="grid grid-cols-3 gap-2">
                                                    <button type="button" class="preview-type-btn bg-teal-600 text-white hover:bg-teal-700 px-3 py-2 rounded-lg text-sm font-medium transition-colors flex items-center justify-center" data-type="regular">
                                                        <i class="ri-price-tag-3-line ml-1"></i>
                                                        <span>منتج عادي</span>
                                                    </button>
                                                    <button type="button" class="preview-type-btn bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600 px-3 py-2 rounded-lg text-sm font-medium transition-colors flex items-center justify-center" data-type="weighted">
                                                        <i class="ri-scales-3-line ml-1"></i>
                                                        <span>منتج موزون</span>
                                                    </button>
                                                    <button type="button" class="preview-type-btn bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600 px-3 py-2 rounded-lg text-sm font-medium transition-colors flex items-center justify-center" data-type="multi">
                                                        <i class="ri-layout-grid-line ml-1"></i>
                                                        <span>طباعة متعددة</span>
                                                    </button>
                                                </div>
                                            </div>

                                            <!-- اختيار مقاس الليبل -->
                                            <div class="bg-white dark:bg-gray-800 p-4 rounded-lg border border-gray-200 dark:border-gray-700">
                                                <div class="flex items-center mb-3">
                                                    <i class="ri-ruler-2-line text-teal-500 ml-2"></i>
                                                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">مقاس الليبل للمعاينة</label>
                                                </div>

                                                <div class="grid grid-cols-4 gap-2">
                                                    <button type="button" class="preview-size-btn bg-teal-600 text-white hover:bg-teal-700 px-2 py-2 rounded-lg text-sm font-medium transition-colors flex items-center justify-center" data-size="medium">
                                                        <i class="ri-aspect-ratio-line ml-1"></i>
                                                        <span>متوسط</span>
                                                    </button>
                                                    <button type="button" class="preview-size-btn bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600 px-2 py-2 rounded-lg text-sm font-medium transition-colors flex items-center justify-center" data-size="small">
                                                        <i class="ri-compress-line ml-1"></i>
                                                        <span>صغير</span>
                                                    </button>
                                                    <button type="button" class="preview-size-btn bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600 px-2 py-2 rounded-lg text-sm font-medium transition-colors flex items-center justify-center" data-size="large">
                                                        <i class="ri-expand-line ml-1"></i>
                                                        <span>كبير</span>
                                                    </button>
                                                    <button type="button" class="preview-size-btn bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600 px-2 py-2 rounded-lg text-sm font-medium transition-colors flex items-center justify-center" data-size="thermal">
                                                        <i class="ri-printer-line ml-1"></i>
                                                        <span>حراري</span>
                                                    </button>
                                                </div>
                                            </div>
                                        </div>

                                        <!-- معلومات المعاينة -->
                                        <div class="bg-white dark:bg-gray-800 p-4 rounded-lg border border-gray-200 dark:border-gray-700 mb-5">
                                            <div class="flex items-center mb-3">
                                                <i class="ri-information-line text-teal-500 ml-2"></i>
                                                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">معلومات المعاينة</label>
                                            </div>

                                            <div class="grid grid-cols-1 md:grid-cols-3 gap-3">
                                                <div class="bg-teal-50 dark:bg-teal-900/20 p-3 rounded-lg border border-teal-100 dark:border-teal-900/30">
                                                    <div class="text-xs text-teal-600 dark:text-teal-400 mb-1">نوع الباركود</div>
                                                    <div class="text-sm font-medium text-gray-800 dark:text-white">CODE128</div>
                                                </div>
                                                <div class="bg-teal-50 dark:bg-teal-900/20 p-3 rounded-lg border border-teal-100 dark:border-teal-900/30">
                                                    <div class="text-xs text-teal-600 dark:text-teal-400 mb-1">مقاس الليبل</div>
                                                    <div class="text-sm font-medium text-gray-800 dark:text-white">متوسط (50×30 مم)</div>
                                                </div>
                                                <div class="bg-teal-50 dark:bg-teal-900/20 p-3 rounded-lg border border-teal-100 dark:border-teal-900/30">
                                                    <div class="text-xs text-teal-600 dark:text-teal-400 mb-1">عدد الليبلات</div>
                                                    <div class="text-sm font-medium text-gray-800 dark:text-white">1 ليبل</div>
                                                </div>
                                            </div>
                                        </div>

                                    <!-- نماذج الباركود -->
                                    <div class="grid grid-cols-1 md:grid-cols-2 gap-5">
                                        <!-- نموذج باركود منتج عادي -->
                                        <div id="regular_preview" class="barcode-preview bg-white dark:bg-gray-800 rounded-lg p-4 shadow border border-gray-200 dark:border-gray-700">
                                            <div class="flex justify-between items-center mb-3">
                                                <div class="text-xs font-medium text-teal-600 dark:text-teal-400 bg-teal-50 dark:bg-teal-900/30 px-3 py-1 rounded-full">منتج عادي</div>
                                                <div class="text-xs font-medium text-gray-500 dark:text-gray-400 bg-gray-100 dark:bg-gray-700 px-3 py-1 rounded-full">{{ settings.business.name }}</div>
                                            </div>

                                            <div class="text-center mb-3 pb-2 border-b border-gray-100 dark:border-gray-700">
                                                <div class="text-lg font-bold product-name text-gray-800 dark:text-white">اسم المنتج بالكامل</div>
                                                <div class="text-sm font-medium text-teal-600 dark:text-teal-400 mt-1">سعر الكيلو: 50.00 ج.م</div>
                                            </div>

                                            <div class="barcode-image flex justify-center items-center my-3 bg-white dark:bg-gray-900 p-2 rounded border border-gray-100 dark:border-gray-700">
                                                <img src="{{ url_for('static', filename='img/barcode-sample.png') }}" alt="نموذج باركود" class="h-14">
                                            </div>

                                            <div class="text-center barcode-text text-sm font-mono font-medium text-gray-700 dark:text-gray-300 bg-gray-50 dark:bg-gray-900 py-1 rounded">1234567890128</div>

                                            <div class="mt-3 text-center text-xs text-gray-500 dark:text-gray-400 bg-gray-50 dark:bg-gray-900/50 p-2 rounded">
                                                <div class="flex justify-center items-center gap-2">
                                                    <i class="ri-calendar-line"></i>
                                                    <span>{{ now.strftime('%Y-%m-%d') }}</span>
                                                </div>
                                                <div class="flex justify-center items-center gap-2 mt-1">
                                                    <i class="ri-time-line"></i>
                                                    <span>{{ now.strftime('%H:%M') }} - {{ now.strftime('%A') }}</span>
                                                </div>
                                            </div>
                                        </div>

                                        <!-- نموذج باركود منتج موزون -->
                                        <div id="weighted_preview" class="barcode-preview bg-white dark:bg-gray-800 rounded-lg p-4 shadow border border-gray-200 dark:border-gray-700 hidden">
                                            <div class="flex justify-between items-center mb-3">
                                                <div class="text-xs font-medium text-green-600 dark:text-green-400 bg-green-50 dark:bg-green-900/30 px-3 py-1 rounded-full">منتج موزون</div>
                                                <div class="text-xs font-medium text-gray-500 dark:text-gray-400 bg-gray-100 dark:bg-gray-700 px-3 py-1 rounded-full">{{ settings.business.name }}</div>
                                            </div>

                                            <div class="text-center mb-3 pb-2 border-b border-gray-100 dark:border-gray-700">
                                                <div class="text-lg font-bold product-name text-gray-800 dark:text-white">منتج موزون</div>
                                                <div class="text-sm font-medium text-green-600 dark:text-green-400 mt-1">سعر الكيلو: 40.00 ج.م</div>
                                            </div>

                                            <div class="grid grid-cols-2 gap-2 mb-3">
                                                <div class="bg-green-50 dark:bg-green-900/20 p-2 rounded text-center">
                                                    <div class="text-xs text-green-600 dark:text-green-400 mb-1">الوزن</div>
                                                    <div class="text-sm font-bold text-gray-800 dark:text-white">1.234 كجم</div>
                                                </div>
                                                <div class="bg-blue-50 dark:bg-blue-900/20 p-2 rounded text-center">
                                                    <div class="text-xs text-blue-600 dark:text-blue-400 mb-1">الإجمالي</div>
                                                    <div class="text-sm font-bold text-gray-800 dark:text-white">49.36 ج.م</div>
                                                </div>
                                            </div>

                                            <div class="barcode-image flex justify-center items-center my-3 bg-white dark:bg-gray-900 p-2 rounded border border-gray-100 dark:border-gray-700">
                                                <img src="{{ url_for('static', filename='img/barcode-sample.png') }}" alt="نموذج باركود" class="h-14">
                                            </div>

                                            <div class="text-center barcode-text text-sm font-mono font-medium text-gray-700 dark:text-gray-300 bg-gray-50 dark:bg-gray-900 py-1 rounded">2012345678908</div>

                                            <div class="mt-3 text-center text-xs text-gray-500 dark:text-gray-400 bg-gray-50 dark:bg-gray-900/50 p-2 rounded">
                                                <div class="flex justify-center items-center gap-2">
                                                    <i class="ri-calendar-line"></i>
                                                    <span>{{ now.strftime('%Y-%m-%d') }}</span>
                                                </div>
                                                <div class="flex justify-center items-center gap-2 mt-1">
                                                    <i class="ri-time-line"></i>
                                                    <span>{{ now.strftime('%H:%M') }} - {{ now.strftime('%A') }}</span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- نموذج طباعة متعددة -->
                                    <div id="multi_preview" class="mt-5 hidden">
                                        <div class="bg-white dark:bg-gray-800 rounded-lg p-4 shadow border border-gray-200 dark:border-gray-700">
                                            <div class="flex items-center justify-between mb-3">
                                                <div class="flex items-center">
                                                    <i class="ri-layout-grid-line text-teal-500 ml-2"></i>
                                                    <h5 class="text-sm font-medium text-gray-700 dark:text-gray-300">معاينة طباعة متعددة</h5>
                                                </div>
                                                <div class="text-xs text-gray-500 dark:text-gray-400">{{ settings.barcode.labels_per_page }} ليبل</div>
                                            </div>

                                            <div class="grid grid-cols-3 md:grid-cols-4 gap-2 p-3 bg-gray-50 dark:bg-gray-900/50 rounded border border-gray-200 dark:border-gray-700">
                                                {% for i in range(8) %}
                                                <div class="barcode-mini-preview bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 p-2 rounded shadow-sm">
                                                    <div class="text-xs font-medium text-center truncate text-gray-800 dark:text-white">منتج {{ i + 1 }}</div>
                                                    <div class="flex justify-center my-1 bg-white dark:bg-gray-900 p-1 rounded border border-gray-100 dark:border-gray-700">
                                                        <img src="{{ url_for('static', filename='img/barcode-sample.png') }}" alt="نموذج باركود" class="h-6">
                                                    </div>
                                                    <div class="text-center text-xs font-mono text-gray-600 dark:text-gray-400">12345678901</div>
                                                </div>
                                                {% endfor %}
                                            </div>
                                        </div>
                                    </div>

                                    <!-- أزرار التحكم -->
                                    <div class="flex justify-center gap-3 mt-5">
                                        <button type="button" id="test_barcode_print" class="bg-teal-600 hover:bg-teal-700 text-white py-2 px-5 rounded-lg inline-flex items-center justify-center transition-colors">
                                            <i class="ri-printer-line ml-2"></i>
                                            <span>اختبار طباعة الباركود</span>
                                        </button>

                                        <button type="button" id="generate_sample" class="bg-gray-600 hover:bg-gray-700 text-white py-2 px-5 rounded-lg inline-flex items-center justify-center transition-colors">
                                            <i class="ri-refresh-line ml-2"></i>
                                            <span>توليد نموذج</span>
                                        </button>
                                    </div>
                                </div>

                                <div class="md:col-span-2 flex justify-end mt-8">
                                    <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-white py-2.5 px-6 rounded-lg inline-flex items-center justify-center transition-colors shadow-md">
                                        <i class="ri-save-line ml-2"></i>
                                        <span>حفظ الإعدادات</span>
                                    </button>
                                </div>
                            </div>

                            <input type="hidden" name="section" value="barcode">
                        </form>
                    </div>
                </div>

                <!-- معلومات الباركود -->
                <div class="bg-gradient-to-br from-gray-50 to-blue-50 dark:from-gray-900 dark:to-blue-900/20 rounded-2xl shadow-lg overflow-hidden border border-gray-200 dark:border-gray-800 mb-8">
                    <div class="p-8 relative">
                        <!-- زخرفة الخلفية -->
                        <div class="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-blue-400 to-indigo-500"></div>
                        <div class="absolute -top-10 -right-10 w-24 h-24 bg-blue-200 dark:bg-blue-700 opacity-20 rounded-full"></div>

                        <h3 class="text-xl font-bold text-gray-800 dark:text-white mb-6 flex items-center">
                            <span class="flex items-center justify-center w-10 h-10 rounded-full bg-blue-100 dark:bg-blue-900/50 text-blue-600 dark:text-blue-300 ml-3">
                                <i class="ri-information-line"></i>
                            </span>
                            معلومات الباركود
                        </h3>

                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div class="bg-white dark:bg-gray-800 rounded-xl p-5 border border-gray-200 dark:border-gray-700 shadow-sm">
                                <h4 class="text-lg font-semibold text-blue-600 dark:text-blue-400 mb-3 flex items-center">
                                    <i class="ri-barcode-box-line ml-2"></i>
                                    أنواع الباركود
                                </h4>

                                <ul class="space-y-3">
                                    <li class="flex items-start">
                                        <div class="flex-shrink-0 w-6 h-6 rounded-full bg-blue-100 dark:bg-blue-900/50 flex items-center justify-center text-blue-600 dark:text-blue-400 mt-0.5 ml-2">
                                            <i class="ri-checkbox-circle-line text-sm"></i>
                                        </div>
                                        <div>
                                            <span class="font-medium text-gray-800 dark:text-white">CODE128:</span>
                                            <span class="text-gray-600 dark:text-gray-300"> يدعم جميع الأحرف ASCII ومناسب لمعظم التطبيقات.</span>
                                        </div>
                                    </li>
                                    <li class="flex items-start">
                                        <div class="flex-shrink-0 w-6 h-6 rounded-full bg-blue-100 dark:bg-blue-900/50 flex items-center justify-center text-blue-600 dark:text-blue-400 mt-0.5 ml-2">
                                            <i class="ri-checkbox-circle-line text-sm"></i>
                                        </div>
                                        <div>
                                            <span class="font-medium text-gray-800 dark:text-white">EAN13:</span>
                                            <span class="text-gray-600 dark:text-gray-300"> باركود قياسي بطول 13 رقم، يستخدم عالميًا للمنتجات التجارية.</span>
                                        </div>
                                    </li>
                                    <li class="flex items-start">
                                        <div class="flex-shrink-0 w-6 h-6 rounded-full bg-blue-100 dark:bg-blue-900/50 flex items-center justify-center text-blue-600 dark:text-blue-400 mt-0.5 ml-2">
                                            <i class="ri-checkbox-circle-line text-sm"></i>
                                        </div>
                                        <div>
                                            <span class="font-medium text-gray-800 dark:text-white">EAN8:</span>
                                            <span class="text-gray-600 dark:text-gray-300"> نسخة مصغرة من EAN13 بطول 8 أرقام، مناسب للمنتجات الصغيرة.</span>
                                        </div>
                                    </li>
                                    <li class="flex items-start">
                                        <div class="flex-shrink-0 w-6 h-6 rounded-full bg-blue-100 dark:bg-blue-900/50 flex items-center justify-center text-blue-600 dark:text-blue-400 mt-0.5 ml-2">
                                            <i class="ri-checkbox-circle-line text-sm"></i>
                                        </div>
                                        <div>
                                            <span class="font-medium text-gray-800 dark:text-white">UPC:</span>
                                            <span class="text-gray-600 dark:text-gray-300"> يستخدم في الولايات المتحدة وكندا، بطول 12 رقم.</span>
                                        </div>
                                    </li>
                                </ul>
                            </div>

                            <div class="bg-white dark:bg-gray-800 rounded-xl p-5 border border-gray-200 dark:border-gray-700 shadow-sm">
                                <h4 class="text-lg font-semibold text-green-600 dark:text-green-400 mb-3 flex items-center">
                                    <i class="ri-scales-3-line ml-2"></i>
                                    باركود الميزان
                                </h4>

                                <div class="mb-4">
                                    <p class="text-gray-600 dark:text-gray-300 leading-relaxed">
                                        يتيح للنظام التعرف على المنتجات الموزونة وحساب السعر تلقائيًا بناءً على الوزن. عادة ما يبدأ بأرقام محددة (مثل 20 أو 21) ويتضمن كود المنتج والوزن.
                                    </p>
                                </div>

                                <div class="bg-green-50 dark:bg-green-900/20 p-4 rounded-lg border border-green-100 dark:border-green-900/30">
                                    <div class="flex items-start">
                                        <div class="flex-shrink-0 w-6 h-6 rounded-full bg-green-100 dark:bg-green-900/50 flex items-center justify-center text-green-600 dark:text-green-400 mt-0.5 ml-2">
                                            <i class="ri-lightbulb-line text-sm"></i>
                                        </div>
                                        <div>
                                            <span class="font-medium text-gray-800 dark:text-white">ملاحظة:</span>
                                            <span class="text-gray-600 dark:text-gray-300"> تأكد من تكوين إعدادات الميزان بشكل صحيح ليتوافق مع الميزان المستخدم في متجرك.</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <!-- Powered By -->
    <div class="powered-by">
        <div class="w-8 h-8 rounded-full bg-primary-50 dark:bg-primary-900/50 flex items-center justify-center ml-2 text-primary-600 dark:text-primary-400">
            <i class="ri-code-s-slash-line"></i>
        </div>
        <div>
            <div class="text-xs text-gray-600 dark:text-gray-400">
                Powered By <span class="font-bold text-primary-600 dark:text-primary-400">ENG/ Fouad Saber</span>
            </div>
            <div class="text-xs text-gray-500 dark:text-gray-500">Tel: 01020073527</div>
        </div>
    </div>

    <script>
        // تفعيل وضع الظلام إذا كان مخزنًا
        if (localStorage.getItem('darkMode') === 'enabled') {
            document.documentElement.classList.add('dark');
        }

        document.addEventListener('DOMContentLoaded', function() {
            // إدارة طرق الاتصال بالميزان
            const connectionTypeRadios = document.querySelectorAll('input[name="connection_type"]');
            const serialSettings = document.getElementById('serial_settings');
            const networkSettings = document.getElementById('network_settings');
            const usbSettings = document.getElementById('usb_settings');
            const connectionStatus = document.getElementById('connection_status');

            // تبديل إعدادات الاتصال بناءً على الطريقة المختارة
            connectionTypeRadios.forEach(radio => {
                radio.addEventListener('change', function() {
                    // إخفاء جميع الإعدادات
                    serialSettings.classList.add('hidden');
                    networkSettings.classList.add('hidden');
                    usbSettings.classList.add('hidden');

                    // إظهار الإعدادات المناسبة
                    if (this.value === 'serial') {
                        serialSettings.classList.remove('hidden');
                    } else if (this.value === 'network') {
                        networkSettings.classList.remove('hidden');
                    } else if (this.value === 'usb') {
                        usbSettings.classList.remove('hidden');
                    }
                });
            });

            // زر تحديث أجهزة USB
            const refreshUsbDevicesBtn = document.getElementById('refresh_usb_devices');
            refreshUsbDevicesBtn.addEventListener('click', function() {
                // محاكاة البحث عن أجهزة USB
                this.innerHTML = '<i class="ri-loader-4-line ml-1 animate-spin"></i><span>جاري البحث...</span>';

                setTimeout(() => {
                    this.innerHTML = '<i class="ri-refresh-line ml-1"></i><span>تحديث قائمة الأجهزة</span>';

                    // إضافة أجهزة جديدة للقائمة (محاكاة)
                    const scaleDeviceSelect = document.getElementById('scale_device');
                    const newOption = document.createElement('option');
                    newOption.value = 'VID_04D8&PID_003F';
                    newOption.textContent = 'Microchip Scale (VID:04D8 PID:003F) - تم العثور عليه حديثًا';
                    scaleDeviceSelect.appendChild(newOption);

                    // إظهار إشعار نجاح
                    alert('تم تحديث قائمة الأجهزة بنجاح');
                }, 1500);
            });

            // زر الاتصال بالميزان
            const connectScaleBtn = document.getElementById('connect_scale_btn');
            connectScaleBtn.addEventListener('click', function() {
                // تغيير حالة الزر
                this.disabled = true;
                this.innerHTML = '<i class="ri-loader-4-line ml-1 animate-spin"></i><span>جاري الاتصال...</span>';

                // محاكاة عملية الاتصال
                setTimeout(() => {
                    this.disabled = false;
                    this.innerHTML = '<i class="ri-link-check ml-2"></i><span>متصل بالميزان</span>';

                    // تحديث حالة الاتصال
                    connectionStatus.innerHTML = `
                        <div class="w-3 h-3 rounded-full bg-green-500 ml-2"></div>
                        <span class="text-sm text-green-700 dark:text-green-300">متصل بنجاح</span>
                    `;
                    connectionStatus.classList.remove('bg-yellow-50', 'dark:bg-yellow-900/20', 'border-yellow-100', 'dark:border-yellow-900/30');
                    connectionStatus.classList.add('bg-green-50', 'dark:bg-green-900/20', 'border-green-100', 'dark:border-green-900/30');

                    // تفعيل زر الاختبار
                    document.getElementById('test_scale_btn').classList.remove('bg-green-600', 'hover:bg-green-700');
                    document.getElementById('test_scale_btn').classList.add('bg-blue-600', 'hover:bg-blue-700');
                }, 2000);
            });

            // زر اختبار القراءة
            const testScaleBtn = document.getElementById('test_scale_btn');
            testScaleBtn.addEventListener('click', function() {
                // التحقق من حالة الاتصال
                if (!connectionStatus.innerHTML.includes('متصل بنجاح')) {
                    alert('يرجى الاتصال بالميزان أولاً');
                    return;
                }

                // تغيير حالة الزر
                this.disabled = true;
                this.innerHTML = '<i class="ri-loader-4-line ml-1 animate-spin"></i><span>جاري القراءة...</span>';

                // محاكاة عملية القراءة
                setTimeout(() => {
                    this.disabled = false;
                    this.innerHTML = '<i class="ri-scales-3-line ml-2"></i><span>اختبار القراءة</span>';

                    // عرض نتائج الاختبار
                    const scaleTestResults = document.getElementById('scale_test_results');
                    scaleTestResults.classList.remove('hidden');

                    // تحديث البيانات
                    document.getElementById('test_weight').textContent = '1.234 كجم';
                    document.getElementById('test_price').textContent = '40.00 ج.م';
                    document.getElementById('test_total').textContent = '49.36 ج.م';
                    document.getElementById('test_barcode').textContent = '2012340123450';
                    document.getElementById('test_time').textContent = new Date().toLocaleString('ar-EG');

                    // توليد باركود للمنتج
                    generateProductBarcode('1234', 1.234, 40.00);
                }, 1500);
            });

            // وظيفة توليد باركود للمنتج
            function generateProductBarcode(productCode, weight, price) {
                // تنسيق الباركود: بادئة الميزان + كود المنتج + الوزن
                const scalePrefix = document.getElementById('scale_prefix').value || '20';
                const weightDigits = 5; // عدد خانات الوزن

                // تحويل الوزن إلى تنسيق مناسب (مثال: 1.234 كجم -> 01234)
                const weightFactor = 1000;
                const weightValue = Math.round(weight * weightFactor);
                const weightStr = weightValue.toString().padStart(weightDigits, '0');

                // توليد الباركود
                const barcode = scalePrefix + productCode.padStart(4, '0') + weightStr + '0';

                // عرض الباركود
                document.getElementById('test_barcode').textContent = barcode;

                console.log(`تم توليد باركود للمنتج: ${barcode}`);
                console.log(`- كود المنتج: ${productCode}`);
                console.log(`- الوزن: ${weight} كجم (${weightStr})`);
                console.log(`- السعر: ${price} ج.م`);
                console.log(`- الإجمالي: ${(weight * price).toFixed(2)} ج.م`);
            }

            // إرسال النموذج
            document.getElementById('barcodeSettingsForm').addEventListener('submit', function(e) {
                e.preventDefault();

                fetch(this.action, {
                    method: 'POST',
                    body: new FormData(this),
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        alert("تم حفظ إعدادات الباركود بنجاح");
                    } else {
                        alert("حدث خطأ أثناء حفظ الإعدادات: " + data.error);
                    }
                })
                .catch(error => {
                    alert("حدث خطأ أثناء الاتصال بالخادم");
                    console.error(error);
                });
            });

            // اختبار طباعة الباركود
            document.getElementById('test_barcode_print').addEventListener('click', function() {
                fetch("{{ url_for('settings.test_barcode_print') }}")
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        alert("تم إرسال اختبار طباعة الباركود بنجاح");
                    } else {
                        alert("حدث خطأ أثناء اختبار طباعة الباركود: " + data.error);
                    }
                })
                .catch(error => {
                    alert("حدث خطأ أثناء الاتصال بالخادم");
                    console.error(error);
                });
            });

            // توليد نموذج باركود
            document.getElementById('generate_sample').addEventListener('click', function() {
                fetch("{{ url_for('settings.generate_barcode_sample') }}")
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        alert("تم توليد نموذج الباركود بنجاح");
                        // تحديث الصورة
                        document.querySelectorAll('.barcode-image img').forEach(function(img) {
                            img.src = data.barcode_url + '?t=' + new Date().getTime();
                        });
                    } else {
                        alert("حدث خطأ أثناء توليد نموذج الباركود: " + data.error);
                    }
                })
                .catch(error => {
                    alert("حدث خطأ أثناء الاتصال بالخادم");
                    console.error(error);
                });
            });

            // تبديل نوع المعاينة
            document.querySelectorAll('.preview-type-btn').forEach(function(btn) {
                btn.addEventListener('click', function() {
                    // إزالة الفئة النشطة من جميع الأزرار
                    document.querySelectorAll('.preview-type-btn').forEach(function(b) {
                        b.classList.remove('bg-primary-500', 'text-white');
                        b.classList.add('bg-gray-200', 'text-gray-700');
                    });

                    // إضافة الفئة النشطة للزر المحدد
                    this.classList.remove('bg-gray-200', 'text-gray-700');
                    this.classList.add('bg-primary-500', 'text-white');

                    // إخفاء جميع المعاينات
                    document.getElementById('regular_preview').classList.add('hidden');
                    document.getElementById('weighted_preview').classList.add('hidden');
                    document.getElementById('multi_preview').classList.add('hidden');

                    // إظهار المعاينة المحددة
                    const type = this.getAttribute('data-type');
                    document.getElementById(type + '_preview').classList.remove('hidden');
                });
            });

            // تبديل حجم المعاينة
            document.querySelectorAll('.preview-size-btn').forEach(function(btn) {
                btn.addEventListener('click', function() {
                    // إزالة الفئة النشطة من جميع الأزرار
                    document.querySelectorAll('.preview-size-btn').forEach(function(b) {
                        b.classList.remove('bg-primary-500', 'text-white');
                        b.classList.add('bg-gray-200', 'text-gray-700');
                    });

                    // إضافة الفئة النشطة للزر المحدد
                    this.classList.remove('bg-gray-200', 'text-gray-700');
                    this.classList.add('bg-primary-500', 'text-white');

                    // تغيير حجم المعاينة
                    const size = this.getAttribute('data-size');
                    const previews = document.querySelectorAll('.barcode-preview');

                    previews.forEach(function(preview) {
                        // إزالة جميع فئات الحجم
                        preview.classList.remove('scale-75', 'scale-100', 'scale-125', 'p-2', 'p-4', 'p-6');

                        // إضافة فئة الحجم المناسبة
                        if (size === 'small') {
                            preview.classList.add('scale-75', 'p-2');
                        } else if (size === 'medium') {
                            preview.classList.add('scale-100', 'p-4');
                        } else if (size === 'large') {
                            preview.classList.add('scale-125', 'p-6');
                        } else if (size === 'thermal') {
                            preview.classList.add('scale-100', 'p-3');
                            preview.style.width = '58mm';
                        }
                    });
                });
            });

            // إظهار/إخفاء حقول المقاس المخصص
            document.getElementById('label_size').addEventListener('change', function() {
                const customSizeContainer = document.getElementById('custom_size_container');
                if (this.value === 'custom') {
                    customSizeContainer.classList.remove('hidden');
                } else {
                    customSizeContainer.classList.add('hidden');
                }
            });

            // اختبار الاتصال بالميزان
            document.getElementById('connect_scale_btn').addEventListener('click', function() {
                const port = document.getElementById('scale_port').value;
                const baudRate = document.getElementById('scale_baud_rate').value;

                // تغيير نص الزر ليظهر أنه جاري الاتصال
                this.innerHTML = '<i class="ri-loader-4-line animate-spin ml-1"></i><span>جاري الاتصال...</span>';
                this.disabled = true;

                // إرسال طلب الاتصال بالميزان
                fetch("{{ url_for('settings.connect_scale') }}", {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: new URLSearchParams({
                        'port': port,
                        'baud_rate': baudRate
                    })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // تغيير نص الزر ليظهر أنه متصل
                        this.innerHTML = '<i class="ri-link-unlink-m ml-1"></i><span>قطع الاتصال</span>';
                        this.classList.remove('bg-blue-600', 'hover:bg-blue-700');
                        this.classList.add('bg-red-600', 'hover:bg-red-700');
                        this.disabled = false;

                        // إظهار رسالة نجاح
                        alert('تم الاتصال بالميزان بنجاح');
                    } else {
                        // إعادة الزر إلى حالته الأصلية
                        this.innerHTML = '<i class="ri-link ml-1"></i><span>اتصال بالميزان</span>';
                        this.disabled = false;

                        // إظهار رسالة الخطأ
                        alert('فشل الاتصال بالميزان: ' + data.message);
                    }
                })
                .catch(error => {
                    // إعادة الزر إلى حالته الأصلية
                    this.innerHTML = '<i class="ri-link ml-1"></i><span>اتصال بالميزان</span>';
                    this.disabled = false;

                    // إظهار رسالة الخطأ
                    alert('حدث خطأ أثناء الاتصال بالخادم');
                    console.error(error);
                });
            });

            // اختبار قراءة الميزان
            document.getElementById('test_scale_btn').addEventListener('click', function() {
                // تغيير نص الزر ليظهر أنه جاري الاختبار
                this.innerHTML = '<i class="ri-loader-4-line animate-spin ml-1"></i><span>جاري الاختبار...</span>';
                this.disabled = true;

                // إرسال طلب اختبار الميزان
                fetch("{{ url_for('settings.test_scale') }}")
                .then(response => response.json())
                .then(data => {
                    // إعادة الزر إلى حالته الأصلية
                    this.innerHTML = '<i class="ri-scales-3-line ml-1"></i><span>اختبار القراءة</span>';
                    this.disabled = false;

                    if (data.success) {
                        // إظهار نتائج الاختبار
                        document.getElementById('scale_test_results').classList.remove('hidden');
                        document.getElementById('test_weight').textContent = data.data.weight + ' كجم';
                        document.getElementById('test_price').textContent = data.data.price + ' ج.م';
                        document.getElementById('test_total').textContent = data.data.total + ' ج.م';
                        document.getElementById('test_barcode').textContent = data.data.barcode;
                    } else {
                        // إظهار رسالة الخطأ
                        alert('فشل اختبار الميزان: ' + data.message);
                    }
                })
                .catch(error => {
                    // إعادة الزر إلى حالته الأصلية
                    this.innerHTML = '<i class="ri-scales-3-line ml-1"></i><span>اختبار القراءة</span>';
                    this.disabled = false;

                    // إظهار رسالة الخطأ
                    alert('حدث خطأ أثناء الاتصال بالخادم');
                    console.error(error);
                });
            });
        });
    </script>
</body>
</html>
