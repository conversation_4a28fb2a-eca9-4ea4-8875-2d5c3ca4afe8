/**
 * نظام استيراد وتصدير المنتجات
 * يوفر واجهة سهلة لاستيراد المنتجات من ملفات CSV/Excel وتصديرها
 */

document.addEventListener('DOMContentLoaded', function() {
    // عناصر واجهة المستخدم
    const importBtn = document.getElementById('import-products-btn');
    const exportBtn = document.getElementById('export-products-btn');
    const importModal = document.getElementById('import-modal');
    const exportModal = document.getElementById('export-modal');
    const closeImportModal = document.getElementById('close-import-modal');
    const closeExportModal = document.getElementById('close-export-modal');
    const selectFileBtn = document.getElementById('select-file-btn');
    const importFileInput = document.getElementById('import-file');
    const selectedFileName = document.getElementById('selected-file-name');
    const nextStep1Btn = document.getElementById('next-step-1');
    const startExportBtn = document.getElementById('start-export');
    const downloadTemplateBtn = document.getElementById('download-import-template');

    // متغيرات عامة
    let importFile = null;
    let importData = null;
    let currentStep = 1;
    let fieldMapping = {};
    let previewData = [];

    // إظهار نافذة الاستيراد
    importBtn.addEventListener('click', function() {
        importModal.classList.remove('hidden');
        resetImportForm();
    });

    // إظهار نافذة التصدير
    exportBtn.addEventListener('click', function() {
        exportModal.classList.remove('hidden');
    });

    // إغلاق نافذة الاستيراد
    closeImportModal.addEventListener('click', function() {
        importModal.classList.add('hidden');
    });

    // إغلاق نافذة التصدير
    closeExportModal.addEventListener('click', function() {
        exportModal.classList.add('hidden');
    });

    // اختيار ملف الاستيراد
    selectFileBtn.addEventListener('click', function() {
        importFileInput.click();
    });

    // تحميل ملف الاستيراد
    importFileInput.addEventListener('change', function(e) {
        if (e.target.files.length > 0) {
            importFile = e.target.files[0];
            selectedFileName.textContent = importFile.name;
            nextStep1Btn.disabled = false;
        } else {
            importFile = null;
            selectedFileName.textContent = '';
            nextStep1Btn.disabled = true;
        }
    });

    // الانتقال للخطوة التالية في الاستيراد
    nextStep1Btn.addEventListener('click', function() {
        if (importFile) {
            // قراءة الملف وتحليله
            readImportFile(importFile);
        }
    });

    // تنزيل قالب الاستيراد
    downloadTemplateBtn.addEventListener('click', function(e) {
        e.preventDefault();
        downloadImportTemplate();
    });

    // بدء عملية التصدير
    startExportBtn.addEventListener('click', function() {
        exportProducts();
    });

    /**
     * قراءة ملف الاستيراد وتحليله
     * @param {File} file - ملف الاستيراد
     */
    function readImportFile(file) {
        console.log("بدء قراءة الملف:", file.name, "الحجم:", file.size, "النوع:", file.type);

        // محاولة اكتشاف ترميز الملف
        const fileReader = new FileReader();

        fileReader.onload = function(event) {
            try {
                const fileExt = file.name.split('.').pop().toLowerCase();
                console.log("امتداد الملف:", fileExt);

                // محاولة اكتشاف ما إذا كان الملف مشفرًا
                const content = event.target.result;
                const isEncoded = detectEncoding(content);

                if (fileExt === 'csv') {
                    // تحليل ملف CSV
                    console.log("بدء تحليل ملف CSV");
                    parseCSV(content);
                } else if (fileExt === 'xlsx' || fileExt === 'xls') {
                    // تحليل ملف Excel (سيتم تنفيذه لاحقًا)
                    showError('دعم ملفات Excel سيتم إضافته قريبًا');
                } else {
                    showError('صيغة الملف غير مدعومة. يرجى استخدام ملف CSV أو Excel.');
                }
            } catch (error) {
                console.error("خطأ في قراءة الملف:", error);
                showError('حدث خطأ أثناء قراءة الملف: ' + error.message);
            }
        };

        fileReader.onerror = function(error) {
            console.error("خطأ في قارئ الملفات:", error);
            showError('حدث خطأ أثناء قراءة الملف.');
        };

        // قراءة الملف كنص
        fileReader.readAsText(file);
    }

    /**
     * اكتشاف ترميز الملف
     * @param {string} content - محتوى الملف
     * @returns {boolean} - هل الملف مشفر
     */
    function detectEncoding(content) {
        // محاولة اكتشاف ما إذا كان الملف مشفرًا
        // هذه طريقة بسيطة للاكتشاف، يمكن تحسينها لاحقًا
        const nonAsciiChars = content.match(/[^\x00-\x7F]/g);
        const isEncoded = nonAsciiChars && nonAsciiChars.length > content.length * 0.1;

        console.log("اكتشاف الترميز:", isEncoded ? "الملف قد يكون مشفرًا" : "الملف غير مشفر");

        return isEncoded;
    }

    /**
     * تحليل ملف CSV
     * @param {string} csvContent - محتوى ملف CSV
     */
    function parseCSV(csvContent) {
        try {
            console.log("بدء تحليل ملف CSV");

            // محاولة اكتشاف ترميز الملف
            let decodedContent = csvContent;

            // تقسيم المحتوى إلى أسطر مع مراعاة أنظمة التشغيل المختلفة
            const lines = decodedContent.split(/\r\n|\n|\r/).filter(line => line.trim() !== '');

            console.log(`عدد الأسطر في الملف: ${lines.length}`);

            // التحقق من وجود بيانات
            if (lines.length < 2) {
                showError('الملف لا يحتوي على بيانات كافية. يجب أن يحتوي على سطر العناوين وسطر واحد على الأقل من البيانات.');
                return;
            }

            // محاولة اكتشاف الفاصل المستخدم (فاصلة أو فاصلة منقوطة)
            const firstLine = lines[0];
            let separator = ',';
            if (firstLine.indexOf(';') > -1) {
                separator = ';';
            } else if (firstLine.indexOf('\t') > -1) {
                separator = '\t';
            }

            console.log(`الفاصل المستخدم في الملف: "${separator}"`);

            // استخراج أسماء الأعمدة (السطر الأول)
            const headers = lines[0].split(separator).map(header => header.trim());

            console.log(`العناوين المكتشفة: ${headers.join(', ')}`);

            // التحقق من وجود أعمدة
            if (headers.length === 0) {
                showError('الملف لا يحتوي على أعمدة. تأكد من أن الملف يحتوي على سطر عناوين صحيح.');
                return;
            }

            // تحويل البيانات إلى مصفوفة من الكائنات
            const data = [];
            let errorCount = 0;

            for (let i = 1; i < lines.length; i++) {
                if (lines[i].trim() === '') continue;

                // معالجة القيم المحاطة بعلامات اقتباس
                let values = [];
                let currentValue = '';
                let inQuotes = false;

                for (let j = 0; j < lines[i].length; j++) {
                    const char = lines[i][j];

                    if (char === '"' && (j === 0 || lines[i][j-1] !== '\\')) {
                        inQuotes = !inQuotes;
                    } else if (char === separator && !inQuotes) {
                        values.push(currentValue.trim());
                        currentValue = '';
                    } else {
                        currentValue += char;
                    }
                }

                // إضافة القيمة الأخيرة
                values.push(currentValue.trim());

                // إزالة علامات الاقتباس من القيم
                values = values.map(value => {
                    if (value.startsWith('"') && value.endsWith('"')) {
                        return value.substring(1, value.length - 1);
                    }
                    return value;
                });

                // التأكد من أن عدد القيم يساوي عدد الأعمدة
                if (values.length !== headers.length) {
                    console.warn(`تم تخطي السطر ${i + 1} لأن عدد القيم (${values.length}) لا يتطابق مع عدد الأعمدة (${headers.length}).`);
                    errorCount++;
                    continue;
                }

                const row = {};
                for (let j = 0; j < headers.length; j++) {
                    row[headers[j]] = values[j];
                }

                data.push(row);
            }

            if (errorCount > 0) {
                console.warn(`تم تخطي ${errorCount} سطر بسبب أخطاء في التنسيق.`);
            }

            console.log(`تم تحليل ${data.length} سطر من البيانات بنجاح.`);

            // حفظ البيانات وعرض الخطوة التالية
            importData = {
                headers: headers,
                data: data
            };

            // عرض الخطوة الثانية (مطابقة الحقول)
            showFieldMappingStep();
        } catch (error) {
            console.error('خطأ في تحليل ملف CSV:', error);
            showError('حدث خطأ أثناء تحليل ملف CSV: ' + error.message);
        }
    }

    /**
     * عرض خطوة مطابقة الحقول
     */
    function showFieldMappingStep() {
        currentStep = 2;

        // إنشاء عنصر الخطوة الثانية
        const step2Element = document.createElement('div');
        step2Element.id = 'step-2';
        step2Element.className = 'import-step';

        // إضافة محتوى الخطوة الثانية
        step2Element.innerHTML = `
            <h3 class="text-lg font-bold text-gray-800 mb-4">الخطوة 2: مطابقة الحقول</h3>
            <p class="text-gray-700 mb-4">قم بمطابقة حقول الملف مع حقول النظام:</p>

            <div class="field-mapping-container mb-4">
                <table class="w-full border-collapse">
                    <thead>
                        <tr class="bg-gray-100">
                            <th class="border p-2 text-right">حقل النظام</th>
                            <th class="border p-2 text-right">حقل الملف</th>
                        </tr>
                    </thead>
                    <tbody id="field-mapping-body">
                        <!-- سيتم إضافة صفوف المطابقة هنا -->
                    </tbody>
                </table>
            </div>

            <div class="flex justify-between">
                <button id="back-step-2" class="bg-gray-500 hover:bg-gray-600 text-white py-2 px-4 rounded-lg flex items-center">
                    <i class="ri-arrow-right-line ml-2"></i>
                    <span>السابق</span>
                </button>
                <button id="next-step-2" class="bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded-lg flex items-center">
                    <span>معاينة البيانات</span>
                    <i class="ri-arrow-left-line mr-2"></i>
                </button>
            </div>
        `;

        // إضافة الخطوة الثانية وإخفاء الخطوة الأولى
        const importSteps = document.getElementById('import-steps');
        const step1Element = document.getElementById('step-1');
        step1Element.style.display = 'none';
        importSteps.appendChild(step2Element);

        // إضافة مستمعي الأحداث للأزرار
        document.getElementById('back-step-2').addEventListener('click', function() {
            step2Element.remove();
            step1Element.style.display = 'block';
            currentStep = 1;
        });

        document.getElementById('next-step-2').addEventListener('click', function() {
            // التحقق من اكتمال المطابقة
            if (validateFieldMapping()) {
                showPreviewStep();
            } else {
                showError('يرجى مطابقة جميع الحقول المطلوبة.');
            }
        });

        // إنشاء خيارات المطابقة
        createFieldMappingOptions();
    }

    /**
     * إنشاء خيارات مطابقة الحقول
     */
    function createFieldMappingOptions() {
        const fieldMappingBody = document.getElementById('field-mapping-body');
        fieldMappingBody.innerHTML = '';

        // قائمة حقول النظام
        const systemFields = [
            { name: 'name', label: 'اسم المنتج', required: true },
            { name: 'code', label: 'الباركود', required: false },
            { name: 'category', label: 'التصنيف', required: false },
            { name: 'price', label: 'سعر البيع', required: true },
            { name: 'cost_price', label: 'سعر التكلفة', required: false },
            { name: 'description', label: 'الوصف', required: false },
            { name: 'stock_quantity', label: 'الكمية', required: false },
            { name: 'minimum_stock', label: 'الحد الأدنى للمخزون', required: false },
            { name: 'brand', label: 'العلامة التجارية', required: false },
            { name: 'is_active', label: 'نشط', required: false }
        ];

        // إنشاء صفوف المطابقة
        systemFields.forEach(field => {
            const row = document.createElement('tr');

            // خلية حقل النظام
            const systemFieldCell = document.createElement('td');
            systemFieldCell.className = 'border p-2';
            systemFieldCell.innerHTML = `
                ${field.label} ${field.required ? '<span class="text-red-500">*</span>' : ''}
            `;

            // خلية حقل الملف
            const fileFieldCell = document.createElement('td');
            fileFieldCell.className = 'border p-2';

            // إنشاء قائمة منسدلة لاختيار حقل الملف
            const select = document.createElement('select');
            select.className = 'w-full bg-gray-100 border border-gray-300 rounded-lg px-3 py-2 text-gray-700 focus:outline-none focus:ring-2 focus:ring-blue-500';
            select.dataset.systemField = field.name;

            // إضافة خيار فارغ
            const emptyOption = document.createElement('option');
            emptyOption.value = '';
            emptyOption.textContent = 'اختر حقل الملف';
            select.appendChild(emptyOption);

            // إضافة خيارات من أعمدة الملف
            importData.headers.forEach(header => {
                const option = document.createElement('option');
                option.value = header;
                option.textContent = header;

                // اختيار الحقل تلقائيًا إذا كان اسمه مشابهًا
                if (header.toLowerCase() === field.name.toLowerCase() ||
                    header.toLowerCase().includes(field.name.toLowerCase())) {
                    option.selected = true;
                    fieldMapping[field.name] = header;
                }

                select.appendChild(option);
            });

            // إضافة مستمع حدث لتحديث المطابقة
            select.addEventListener('change', function() {
                if (this.value) {
                    fieldMapping[field.name] = this.value;
                } else {
                    delete fieldMapping[field.name];
                }
            });

            fileFieldCell.appendChild(select);

            // إضافة الخلايا إلى الصف
            row.appendChild(systemFieldCell);
            row.appendChild(fileFieldCell);

            // إضافة الصف إلى الجدول
            fieldMappingBody.appendChild(row);
        });
    }

    /**
     * التحقق من اكتمال مطابقة الحقول
     * @returns {boolean} - هل المطابقة مكتملة
     */
    function validateFieldMapping() {
        // التحقق من وجود الحقول المطلوبة
        const requiredFields = ['name', 'price'];

        for (const field of requiredFields) {
            if (!fieldMapping[field]) {
                return false;
            }
        }

        return true;
    }

    /**
     * عرض خطوة معاينة البيانات
     */
    function showPreviewStep() {
        currentStep = 3;

        // إنشاء بيانات المعاينة
        previewData = [];
        for (let i = 0; i < Math.min(5, importData.data.length); i++) {
            const row = importData.data[i];
            const previewRow = {};

            // استخراج البيانات باستخدام مطابقة الحقول
            for (const [systemField, fileField] of Object.entries(fieldMapping)) {
                previewRow[systemField] = row[fileField] || '';
            }

            previewData.push(previewRow);
        }

        // إنشاء عنصر الخطوة الثالثة
        const step3Element = document.createElement('div');
        step3Element.id = 'step-3';
        step3Element.className = 'import-step';

        // إضافة محتوى الخطوة الثالثة
        step3Element.innerHTML = `
            <h3 class="text-lg font-bold text-gray-800 mb-4">الخطوة 3: معاينة البيانات</h3>
            <p class="text-gray-700 mb-4">معاينة البيانات قبل الاستيراد النهائي:</p>

            <div class="preview-table mb-4">
                <table class="w-full border-collapse">
                    <thead>
                        <tr class="bg-gray-100">
                            ${Object.keys(fieldMapping).map(field => `<th class="border p-2 text-right">${field}</th>`).join('')}
                        </tr>
                    </thead>
                    <tbody id="preview-body">
                        ${previewData.map(row => `
                            <tr>
                                ${Object.keys(fieldMapping).map(field => `<td class="border p-2">${row[field] || ''}</td>`).join('')}
                            </tr>
                        `).join('')}
                    </tbody>
                </table>
            </div>

            <p class="text-gray-500 mb-4">معاينة ${previewData.length} من أصل ${importData.data.length} صف.</p>

            <div class="flex justify-between">
                <button id="back-step-3" class="bg-gray-500 hover:bg-gray-600 text-white py-2 px-4 rounded-lg flex items-center">
                    <i class="ri-arrow-right-line ml-2"></i>
                    <span>السابق</span>
                </button>
                <button id="start-import" class="bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded-lg flex items-center">
                    <span>بدء الاستيراد</span>
                    <i class="ri-check-line mr-2"></i>
                </button>
            </div>
        `;

        // إضافة الخطوة الثالثة وإخفاء الخطوة الثانية
        const importSteps = document.getElementById('import-steps');
        const step2Element = document.getElementById('step-2');
        step2Element.style.display = 'none';
        importSteps.appendChild(step3Element);

        // إضافة مستمعي الأحداث للأزرار
        document.getElementById('back-step-3').addEventListener('click', function() {
            step3Element.remove();
            step2Element.style.display = 'block';
            currentStep = 2;
        });

        document.getElementById('start-import').addEventListener('click', function() {
            startImport();
        });
    }

    /**
     * تنزيل قالب الاستيراد
     */
    function downloadImportTemplate() {
        // إنشاء محتوى CSV
        const headers = ['Name', 'Code', 'Category', 'Price', 'Cost_Price', 'Description', 'Stock_Quantity', 'Minimum_Stock', 'Brand', 'Is_Active'];
        const csvContent = headers.join(',') + '\n';

        // إنشاء ملف للتنزيل
        const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
        const url = URL.createObjectURL(blob);

        // إنشاء رابط التنزيل
        const link = document.createElement('a');
        link.href = url;
        link.setAttribute('download', 'product_import_template.csv');
        link.style.display = 'none';

        // إضافة الرابط وتنفيذ النقر
        document.body.appendChild(link);
        link.click();

        // تنظيف
        setTimeout(function() {
            document.body.removeChild(link);
            URL.revokeObjectURL(url);
        }, 100);
    }

    /**
     * بدء عملية الاستيراد
     */
    function startImport() {
        // إنشاء عنصر الخطوة الرابعة (التقدم)
        const step4Element = document.createElement('div');
        step4Element.id = 'step-4';
        step4Element.className = 'import-step';

        // إضافة محتوى الخطوة الرابعة
        step4Element.innerHTML = `
            <h3 class="text-lg font-bold text-gray-800 mb-4">الخطوة 4: جاري الاستيراد</h3>
            <p class="text-gray-700 mb-4">جاري استيراد المنتجات، يرجى الانتظار...</p>

            <div class="progress-container">
                <div id="import-progress-bar" class="progress-bar" style="width: 0%"></div>
            </div>

            <p id="import-status" class="text-gray-700 mt-4">جاري التحضير للاستيراد...</p>
        `;

        // إضافة الخطوة الرابعة وإخفاء الخطوة الثالثة
        const importSteps = document.getElementById('import-steps');
        const step3Element = document.getElementById('step-3');
        step3Element.style.display = 'none';
        importSteps.appendChild(step4Element);

        // تحديث شريط التقدم
        const progressBar = document.getElementById('import-progress-bar');
        const statusText = document.getElementById('import-status');
        progressBar.style.width = '10%';

        // إنشاء FormData لإرسال الملف والبيانات
        const formData = new FormData();
        formData.append('file', importFile);

        // إضافة خيارات الاستيراد
        formData.append('update_existing', document.getElementById('update-existing').checked);
        formData.append('skip_errors', document.getElementById('skip-errors').checked);

        // إضافة مطابقة الحقول
        for (const [systemField, fileField] of Object.entries(fieldMapping)) {
            formData.append(`field_${systemField}`, fileField);
        }

        // تحديث شريط التقدم
        progressBar.style.width = '20%';
        statusText.textContent = 'جاري رفع الملف...';

        // إرسال طلب الاستيراد
        fetch('/api/products/import', {
            method: 'POST',
            body: formData
        })
        .then(response => {
            progressBar.style.width = '80%';
            statusText.textContent = 'جاري معالجة البيانات...';
            return response.json();
        })
        .then(data => {
            progressBar.style.width = '100%';

            // إنشاء عنصر الخطوة الخامسة (النتائج)
            const step5Element = document.createElement('div');
            step5Element.id = 'step-5';
            step5Element.className = 'import-step';

            if (data.success) {
                // نجاح الاستيراد
                step5Element.innerHTML = `
                    <h3 class="text-lg font-bold text-gray-800 mb-4">الخطوة 5: اكتمل الاستيراد</h3>
                    <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4">
                        <div class="flex items-center">
                            <i class="ri-check-line text-xl ml-2"></i>
                            <span>${data.message}</span>
                        </div>
                    </div>

                    <div class="mb-4">
                        <h4 class="font-bold text-gray-800 mb-2">المنتجات المستوردة:</h4>
                        <div class="max-h-40 overflow-y-auto">
                            <table class="w-full border-collapse">
                                <thead>
                                    <tr class="bg-gray-100">
                                        <th class="border p-2 text-right">المنتج</th>
                                        <th class="border p-2 text-right">الحالة</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    ${data.imported_products.map(product => `
                                        <tr>
                                            <td class="border p-2">${product.name}</td>
                                            <td class="border p-2">${product.status === 'created' ? 'تم الإنشاء' : 'تم التحديث'}</td>
                                        </tr>
                                    `).join('')}
                                </tbody>
                            </table>
                        </div>
                    </div>

                    ${data.errors.length > 0 ? `
                        <div class="mb-4">
                            <h4 class="font-bold text-gray-800 mb-2">الأخطاء:</h4>
                            <div class="max-h-40 overflow-y-auto">
                                <ul class="list-disc list-inside text-red-600">
                                    ${data.errors.map(error => `<li>${error}</li>`).join('')}
                                </ul>
                            </div>
                        </div>
                    ` : ''}

                    <div class="flex justify-end">
                        <button id="finish-import" class="bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded-lg flex items-center">
                            <span>إنهاء</span>
                            <i class="ri-check-double-line mr-2"></i>
                        </button>
                    </div>
                `;
            } else {
                // فشل الاستيراد
                step5Element.innerHTML = `
                    <h3 class="text-lg font-bold text-gray-800 mb-4">الخطوة 5: فشل الاستيراد</h3>
                    <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
                        <div class="flex items-center">
                            <i class="ri-error-warning-line text-xl ml-2"></i>
                            <span>${data.message}</span>
                        </div>
                    </div>

                    <div class="flex justify-between">
                        <button id="back-step-5" class="bg-gray-500 hover:bg-gray-600 text-white py-2 px-4 rounded-lg flex items-center">
                            <i class="ri-arrow-right-line ml-2"></i>
                            <span>العودة للتعديل</span>
                        </button>
                        <button id="finish-import" class="bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded-lg flex items-center">
                            <span>إنهاء</span>
                            <i class="ri-close-line mr-2"></i>
                        </button>
                    </div>
                `;
            }

            // إضافة الخطوة الخامسة وإخفاء الخطوة الرابعة
            step4Element.style.display = 'none';
            importSteps.appendChild(step5Element);

            // إضافة مستمعي الأحداث للأزرار
            document.getElementById('finish-import').addEventListener('click', function() {
                importModal.classList.add('hidden');
                // إعادة تحميل الصفحة لعرض المنتجات المستوردة
                window.location.reload();
            });

            if (!data.success) {
                document.getElementById('back-step-5').addEventListener('click', function() {
                    step5Element.remove();
                    step3Element.style.display = 'block';
                    currentStep = 3;
                });
            }
        })
        .catch(error => {
            progressBar.style.width = '100%';
            statusText.textContent = 'حدث خطأ أثناء الاستيراد';

            // إنشاء عنصر الخطوة الخامسة (الخطأ)
            const step5Element = document.createElement('div');
            step5Element.id = 'step-5';
            step5Element.className = 'import-step';

            step5Element.innerHTML = `
                <h3 class="text-lg font-bold text-gray-800 mb-4">الخطوة 5: فشل الاستيراد</h3>
                <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
                    <div class="flex items-center">
                        <i class="ri-error-warning-line text-xl ml-2"></i>
                        <span>حدث خطأ أثناء الاستيراد: ${error.message}</span>
                    </div>
                </div>

                <div class="flex justify-between">
                    <button id="back-step-5" class="bg-gray-500 hover:bg-gray-600 text-white py-2 px-4 rounded-lg flex items-center">
                        <i class="ri-arrow-right-line ml-2"></i>
                        <span>العودة للتعديل</span>
                    </button>
                    <button id="finish-import" class="bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded-lg flex items-center">
                        <span>إنهاء</span>
                        <i class="ri-close-line mr-2"></i>
                    </button>
                </div>
            `;

            // إضافة الخطوة الخامسة وإخفاء الخطوة الرابعة
            step4Element.style.display = 'none';
            importSteps.appendChild(step5Element);

            // إضافة مستمعي الأحداث للأزرار
            document.getElementById('finish-import').addEventListener('click', function() {
                importModal.classList.add('hidden');
            });

            document.getElementById('back-step-5').addEventListener('click', function() {
                step5Element.remove();
                step3Element.style.display = 'block';
                currentStep = 3;
            });
        });
    }

    /**
     * تصدير المنتجات
     */
    function exportProducts() {
        // الحصول على معلمات التصفية
        const categoryId = document.getElementById('export-category').value;
        const warehouseId = document.getElementById('export-warehouse').value;
        const withInventory = document.getElementById('export-with-inventory').checked;
        const activeOnly = document.getElementById('export-active-only').checked;
        const arabicHeaders = document.getElementById('export-arabic-headers').checked;
        const format = document.getElementById('export-format').value;

        // بناء URL التصدير
        let exportUrl = `/api/products/export?format=${format}`;

        if (categoryId) {
            exportUrl += `&category_id=${categoryId}`;
        }

        if (warehouseId) {
            exportUrl += `&warehouse_id=${warehouseId}`;
        }

        if (withInventory) {
            exportUrl += '&with_inventory=1';
        } else {
            exportUrl += '&with_inventory=0';
        }

        if (activeOnly) {
            exportUrl += '&active_only=1';
        } else {
            exportUrl += '&active_only=0';
        }

        if (arabicHeaders) {
            exportUrl += '&arabic_headers=1';
        }

        console.log("URL التصدير:", exportUrl);

        try {
            // فتح URL التصدير في نافذة جديدة
            const exportWindow = window.open(exportUrl, '_blank');

            if (!exportWindow) {
                showError('تم حظر النافذة المنبثقة. يرجى السماح بالنوافذ المنبثقة لهذا الموقع.');
            }
        } catch (error) {
            console.error("خطأ في فتح نافذة التصدير:", error);
            showError('حدث خطأ أثناء محاولة تصدير المنتجات: ' + error.message);
        }

        // إغلاق نافذة التصدير
        exportModal.classList.add('hidden');
    }

    /**
     * إعادة تعيين نموذج الاستيراد
     */
    function resetImportForm() {
        importFile = null;
        importData = null;
        currentStep = 1;
        fieldMapping = {};
        previewData = [];

        // إعادة تعيين عناصر النموذج
        selectedFileName.textContent = '';
        nextStep1Btn.disabled = true;

        // إزالة الخطوات الإضافية
        const importSteps = document.getElementById('import-steps');
        const step1 = document.getElementById('step-1');

        // إزالة جميع الخطوات باستثناء الخطوة الأولى
        while (importSteps.children.length > 1) {
            importSteps.removeChild(importSteps.lastChild);
        }

        // إظهار الخطوة الأولى
        step1.style.display = 'block';
    }

    /**
     * عرض رسالة خطأ
     * @param {string} message - رسالة الخطأ
     */
    function showError(message) {
        console.error("خطأ:", message);

        // إنشاء عنصر رسالة الخطأ
        const errorDiv = document.createElement('div');
        errorDiv.className = 'fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center';

        errorDiv.innerHTML = `
            <div class="bg-white rounded-lg shadow-lg w-full max-w-md p-6">
                <div class="flex items-center mb-4 text-red-600">
                    <i class="ri-error-warning-line text-2xl ml-2"></i>
                    <h3 class="text-lg font-bold">خطأ</h3>
                </div>
                <p class="text-gray-700 mb-6">${message}</p>
                <div class="flex justify-end">
                    <button class="bg-red-600 hover:bg-red-700 text-white py-2 px-4 rounded-lg">
                        إغلاق
                    </button>
                </div>
            </div>
        `;

        // إضافة عنصر الخطأ إلى الصفحة
        document.body.appendChild(errorDiv);

        // إضافة مستمع حدث لزر الإغلاق
        const closeButton = errorDiv.querySelector('button');
        closeButton.addEventListener('click', function() {
            document.body.removeChild(errorDiv);
        });
    }
});
