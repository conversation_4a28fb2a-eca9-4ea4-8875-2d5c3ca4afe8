
import os
import base64
import hashlib
import logging
import uuid
import sys
import json
from datetime import datetime, timedelta

# التحقق من وجود وحدة jwt وإنشاء بديل إذا لم تكن موجودة
try:
    import jwt
    JWT_AVAILABLE = True
except ImportError:
    JWT_AVAILABLE = False
    # إنشاء بديل لوحدة jwt
    class DummyJWT:
        class ExpiredSignatureError(Exception):
            pass

        class InvalidTokenError(Exception):
            pass

        @staticmethod
        def encode(payload, key, algorithm=None):
            """ترميز بسيط للبيانات"""
            # تحويل البيانات إلى سلسلة نصية
            payload_str = str(payload)
            # إنشاء توقيع بسيط
            signature = hashlib.sha256((payload_str + key).encode()).hexdigest()
            # ترميز البيانات والتوقيع
            token = base64.urlsafe_b64encode((payload_str + "." + signature).encode()).decode()
            return token

        @staticmethod
        def decode(token, key, algorithms=None):
            """فك ترميز البيانات"""
            try:
                # فك ترميز الرمز
                decoded = base64.urlsafe_b64decode(token.encode()).decode()
                # فصل البيانات والتوقيع
                parts = decoded.split(".")
                if len(parts) != 2:
                    raise DummyJWT.InvalidTokenError()

                payload_str, signature = parts

                # التحقق من التوقيع
                expected_signature = hashlib.sha256((payload_str + key).encode()).hexdigest()
                if signature != expected_signature:
                    raise DummyJWT.InvalidTokenError()

                # تحويل البيانات إلى قاموس
                payload = eval(payload_str)

                # التحقق من انتهاء الصلاحية
                if "exp" in payload and isinstance(payload["exp"], datetime):
                    if payload["exp"] < datetime.utcnow():
                        raise DummyJWT.ExpiredSignatureError()

                return payload
            except Exception as e:
                if isinstance(e, DummyJWT.ExpiredSignatureError):
                    raise e
                raise DummyJWT.InvalidTokenError()

    # تعريف البديل العام
    jwt = DummyJWT

# إعداد التسجيل
logger = logging.getLogger("security")
logger.setLevel(logging.INFO)

# مفتاح JWT السري
JWT_SECRET = os.environ.get("JWT_SECRET", "")
if not JWT_SECRET:
    JWT_SECRET = str(uuid.uuid4())
    logger.warning("No JWT secret provided. Generated a new one. Please set JWT_SECRET in your environment.")

class Security:
    @staticmethod
    def hash_password(password):
        """تشفير كلمة المرور باستخدام خوارزمية تجزئة قوية"""
        salt = os.urandom(32)
        key = hashlib.pbkdf2_hmac('sha256', password.encode(), salt, 100000)
        return salt + key

    @staticmethod
    def verify_password(stored_password, provided_password):
        """التحقق من كلمة المرور"""
        salt = stored_password[:32]
        stored_key = stored_password[32:]
        new_key = hashlib.pbkdf2_hmac('sha256', provided_password.encode(), salt, 100000)
        return new_key == stored_key

    @staticmethod
    def generate_token(user_id, role, expiry_hours=24):
        """إنشاء رمز JWT للمصادقة"""
        payload = {
            "user_id": user_id,
            "role": role,
            "exp": datetime.utcnow() + timedelta(hours=expiry_hours),
            "iat": datetime.utcnow()
        }

        token = jwt.encode(payload, JWT_SECRET, algorithm="HS256")
        return token

    @staticmethod
    def verify_token(token):
        """التحقق من صلاحية رمز JWT"""
        try:
            payload = jwt.decode(token, JWT_SECRET, algorithms=["HS256"])
            return True, payload
        except jwt.ExpiredSignatureError:
            return False, "انتهت صلاحية الرمز"
        except jwt.InvalidTokenError:
            return False, "رمز غير صالح"
        except Exception as e:
            logger.error(f"Error verifying token: {str(e)}")
            return False, "حدث خطأ أثناء التحقق من الرمز"
