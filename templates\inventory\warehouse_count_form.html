{% extends 'layout.html' %}

{% block title %}إنشاء عملية جرد{% endblock %}

{% block page_content %}
<div class="container mx-auto px-4 py-8">
    <div class="mb-6 flex justify-between items-center">
        <div>
            <h1 class="text-2xl font-bold">إنشاء عملية جرد جديدة</h1>
            <p class="text-gray-600">إنشاء عملية جرد دوري للمخزون</p>
        </div>
        <a href="{{ url_for('warehouses.inventory_counts') }}" class="bg-gray-100 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-200 transition-all">
            <i class="ri-arrow-right-line ml-1"></i>العودة لقائمة الجرد
        </a>
    </div>

    <div class="bg-white rounded-lg shadow overflow-hidden">
        <div class="p-4 bg-gray-50 border-b">
            <h2 class="text-xl font-semibold">بيانات عملية الجرد</h2>
        </div>

        <div class="p-6">
            <form method="POST" action="{{ url_for('warehouses.create_inventory_count') }}">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                    <div>
                        <label class="block text-gray-700 mb-2">اسم عملية الجرد <span class="text-red-500">*</span></label>
                        <input type="text" name="name" class="w-full border rounded px-3 py-2" required placeholder="مثال: الجرد الشهري - يناير 2023">
                    </div>
                    <div>
                        <label class="block text-gray-700 mb-2">المخزن <span class="text-red-500">*</span></label>
                        <select name="warehouse_id" class="w-full border rounded px-3 py-2" required>
                            <option value="">-- اختر المخزن --</option>
                            {% for warehouse in warehouses %}
                            <option value="{{ warehouse.id }}">{{ warehouse.name }}</option>
                            {% endfor %}
                        </select>
                    </div>
                </div>
                
                <div class="mb-6">
                    <label class="block text-gray-700 mb-2">ملاحظات</label>
                    <textarea name="notes" class="w-full border rounded px-3 py-2" rows="3" placeholder="أي ملاحظات إضافية حول عملية الجرد"></textarea>
                </div>
                
                <div class="bg-gray-50 p-4 rounded mb-6">
                    <div class="flex items-center text-gray-700 mb-2">
                        <i class="ri-information-line text-blue-500 ml-2"></i>
                        <span class="font-medium">ملاحظات هامة:</span>
                    </div>
                    <ul class="list-disc list-inside text-gray-600 text-sm">
                        <li>سيتم إنشاء عملية جرد تحتوي على جميع المنتجات الموجودة في المخزن المحدد.</li>
                        <li>ستكون الكمية المتوقعة هي الكمية الحالية في المخزون.</li>
                        <li>يمكنك تعديل الكميات الفعلية بعد إنشاء عملية الجرد.</li>
                        <li>عند إكمال عملية الجرد، يمكنك اختيار تطبيق التغييرات على المخزون أو الاحتفاظ بها للمراجعة فقط.</li>
                    </ul>
                </div>
                
                <div class="flex justify-end">
                    <a href="{{ url_for('warehouses.inventory_counts') }}" class="bg-gray-100 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-200 transition-all ml-2">
                        إلغاء
                    </a>
                    <button type="submit" class="bg-green-500 text-white px-4 py-2 rounded-lg hover:bg-green-600 transition-all">
                        <i class="ri-add-line ml-1"></i>إنشاء عملية الجرد
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock page_content %}
