{% extends 'core/base.html' %}

{% block title %}سجلات الأخطاء{% endblock %}

{% block styles %}
<style>
    .log-level-critical {
        background-color: #FEE2E2;
        color: #B91C1C;
    }
    .log-level-error {
        background-color: #FEF2F2;
        color: #DC2626;
    }
    .log-level-warning {
        background-color: #FEF3C7;
        color: #D97706;
    }
    .log-level-info {
        background-color: #DBEAFE;
        color: #2563EB;
    }
    .log-source-server {
        background-color: #E0F2FE;
        color: #0369A1;
    }
    .log-source-client {
        background-color: #F3E8FF;
        color: #7E22CE;
    }
    .log-details-pre {
        max-height: 400px;
        overflow-y: auto;
        direction: ltr;
        text-align: left;
    }
    .dashboard-card {
        transition: all 0.3s ease;
    }
    .dashboard-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    }
</style>
{% endblock %}

{% block content %}
<div class="container mx-auto py-6">
    <div class="mb-6">
        <h1 class="text-2xl font-bold text-gray-800 mb-2">سجلات الأخطاء</h1>
        <p class="text-gray-600">عرض وإدارة سجلات الأخطاء في النظام</p>
    </div>

    <!-- لوحة المعلومات -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
        <div class="dashboard-card bg-white rounded-xl shadow-sm overflow-hidden border border-gray-100 p-6">
            <div class="flex items-center mb-4">
                <div class="w-12 h-12 rounded-lg bg-red-100 text-red-600 flex items-center justify-center mr-4">
                    <i class="ri-error-warning-fill text-2xl"></i>
                </div>
                <div>
                    <h3 class="text-lg font-bold text-gray-800">الأخطاء</h3>
                    <p class="text-3xl font-bold text-red-600">{{ error_count|default(0) }}</p>
                </div>
            </div>
            <div class="h-2 bg-gray-100 rounded-full overflow-hidden">
                <div class="h-full bg-red-500" style="width: {{ error_percent|default(0) }}%;"></div>
            </div>
        </div>

        <div class="dashboard-card bg-white rounded-xl shadow-sm overflow-hidden border border-gray-100 p-6">
            <div class="flex items-center mb-4">
                <div class="w-12 h-12 rounded-lg bg-yellow-100 text-yellow-600 flex items-center justify-center mr-4">
                    <i class="ri-alert-fill text-2xl"></i>
                </div>
                <div>
                    <h3 class="text-lg font-bold text-gray-800">التحذيرات</h3>
                    <p class="text-3xl font-bold text-yellow-600">{{ warning_count|default(0) }}</p>
                </div>
            </div>
            <div class="h-2 bg-gray-100 rounded-full overflow-hidden">
                <div class="h-full bg-yellow-500" style="width: {{ warning_percent|default(0) }}%;"></div>
            </div>
        </div>

        <div class="dashboard-card bg-white rounded-xl shadow-sm overflow-hidden border border-gray-100 p-6">
            <div class="flex items-center mb-4">
                <div class="w-12 h-12 rounded-lg bg-blue-100 text-blue-600 flex items-center justify-center mr-4">
                    <i class="ri-information-fill text-2xl"></i>
                </div>
                <div>
                    <h3 class="text-lg font-bold text-gray-800">المعلومات</h3>
                    <p class="text-3xl font-bold text-blue-600">{{ info_count|default(0) }}</p>
                </div>
            </div>
            <div class="h-2 bg-gray-100 rounded-full overflow-hidden">
                <div class="h-full bg-blue-500" style="width: {{ info_percent|default(0) }}%;"></div>
            </div>
        </div>

        <div class="dashboard-card bg-white rounded-xl shadow-sm overflow-hidden border border-gray-100 p-6">
            <div class="flex items-center mb-4">
                <div class="w-12 h-12 rounded-lg bg-purple-100 text-purple-600 flex items-center justify-center mr-4">
                    <i class="ri-time-fill text-2xl"></i>
                </div>
                <div>
                    <h3 class="text-lg font-bold text-gray-800">آخر تحديث</h3>
                    <p class="text-lg font-bold text-purple-600">{{ last_update|default('لا يوجد') }}</p>
                </div>
            </div>
            <button id="refreshBtn" class="w-full mt-2 px-4 py-2 bg-purple-100 hover:bg-purple-200 text-purple-700 rounded-lg transition-colors duration-300 flex items-center justify-center">
                <i class="ri-refresh-line ml-1"></i>
                تحديث
            </button>
        </div>
    </div>

    <div class="bg-white rounded-xl shadow-sm overflow-hidden border border-gray-100">
        <div class="p-6 border-b border-gray-100 flex justify-between items-center">
            <div class="flex items-center">
                <div class="w-10 h-10 rounded-full bg-red-100 text-red-600 flex items-center justify-center mr-3">
                    <i class="ri-error-warning-line text-xl"></i>
                </div>
                <div>
                    <h3 class="text-lg font-bold text-gray-800">سجلات الأخطاء</h3>
                    <p class="text-sm text-gray-500">عدد السجلات: {{ total_logs }}</p>
                </div>
            </div>
            <div class="flex space-x-2 space-x-reverse">
                <button id="downloadLogsBtn" class="px-4 py-2 bg-green-100 hover:bg-green-200 text-green-700 rounded-lg transition-colors duration-300 flex items-center">
                    <i class="ri-download-line ml-1"></i>
                    تنزيل السجلات
                </button>
                <button id="refreshBtn" class="px-4 py-2 bg-gray-100 hover:bg-gray-200 text-gray-700 rounded-lg transition-colors duration-300 flex items-center">
                    <i class="ri-refresh-line ml-1"></i>
                    تحديث
                </button>
                <button id="clearLogsBtn" class="px-4 py-2 bg-red-100 hover:bg-red-200 text-red-700 rounded-lg transition-colors duration-300 flex items-center">
                    <i class="ri-delete-bin-line ml-1"></i>
                    مسح السجلات
                </button>
            </div>
        </div>

        <!-- فلاتر البحث -->
        <div class="p-6 border-b border-gray-100">
            <form id="filterForm" method="GET" action="{{ url_for('settings.error_logs') }}">
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div>
                        <label for="search" class="block text-sm font-medium text-gray-700 mb-1">بحث</label>
                        <div class="relative">
                            <div class="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                                <i class="ri-search-line text-gray-400"></i>
                            </div>
                            <input type="text" id="search" name="search" value="{{ search }}" placeholder="ابحث في السجلات..." class="w-full px-3 py-2 pr-10 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                        </div>
                    </div>

                    <div>
                        <label for="log_type" class="block text-sm font-medium text-gray-700 mb-1">نوع السجل</label>
                        <select id="log_type" name="log_type" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                            <option value="" {% if not log_type %}selected{% endif %}>جميع الأنواع</option>
                            <option value="critical" {% if log_type == 'critical' %}selected{% endif %}>حرج</option>
                            <option value="error" {% if log_type == 'error' %}selected{% endif %}>خطأ</option>
                            <option value="warning" {% if log_type == 'warning' %}selected{% endif %}>تحذير</option>
                            <option value="info" {% if log_type == 'info' %}selected{% endif %}>معلومات</option>
                        </select>
                    </div>

                    <div>
                        <label for="source" class="block text-sm font-medium text-gray-700 mb-1">المصدر</label>
                        <select id="source" name="source" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                            <option value="" {% if not source %}selected{% endif %}>جميع المصادر</option>
                            <option value="server" {% if source == 'server' %}selected{% endif %}>الخادم</option>
                            <option value="client" {% if source == 'client' %}selected{% endif %}>العميل</option>
                        </select>
                    </div>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mt-4">
                    <div>
                        <label for="start_date" class="block text-sm font-medium text-gray-700 mb-1">من تاريخ</label>
                        <div class="relative">
                            <div class="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                                <i class="ri-calendar-line text-gray-400"></i>
                            </div>
                            <input type="date" id="start_date" name="start_date" value="{{ start_date }}" class="w-full px-3 py-2 pr-10 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                        </div>
                    </div>

                    <div>
                        <label for="end_date" class="block text-sm font-medium text-gray-700 mb-1">إلى تاريخ</label>
                        <div class="relative">
                            <div class="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                                <i class="ri-calendar-line text-gray-400"></i>
                            </div>
                            <input type="date" id="end_date" name="end_date" value="{{ end_date }}" class="w-full px-3 py-2 pr-10 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                        </div>
                    </div>

                    <div>
                        <label for="user_id" class="block text-sm font-medium text-gray-700 mb-1">المستخدم</label>
                        <select id="user_id" name="user_id" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                            <option value="" {% if not user_id %}selected{% endif %}>جميع المستخدمين</option>
                            {% for user in users %}
                                <option value="{{ user.id }}" {% if user_id == user.id|string %}selected{% endif %}>{{ user.username }}</option>
                            {% endfor %}
                        </select>
                    </div>

                    <div>
                        <label for="module" class="block text-sm font-medium text-gray-700 mb-1">الوحدة</label>
                        <select id="module" name="module" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                            <option value="" {% if not module %}selected{% endif %}>جميع الوحدات</option>
                            {% for mod in modules %}
                                <option value="{{ mod }}" {% if module == mod %}selected{% endif %}>{{ mod }}</option>
                            {% endfor %}
                        </select>
                    </div>
                </div>

                <div class="flex flex-wrap justify-between items-center mt-6">
                    <div class="flex items-center space-x-4 space-x-reverse">
                        <div>
                            <label for="sort_by" class="block text-sm font-medium text-gray-700 mb-1">ترتيب حسب</label>
                            <select id="sort_by" name="sort_by" class="px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <option value="timestamp" {% if sort_by == 'timestamp' or not sort_by %}selected{% endif %}>التاريخ</option>
                                <option value="level" {% if sort_by == 'level' %}selected{% endif %}>النوع</option>
                                <option value="message" {% if sort_by == 'message' %}selected{% endif %}>الرسالة</option>
                            </select>
                        </div>

                        <div>
                            <label for="sort_order" class="block text-sm font-medium text-gray-700 mb-1">الترتيب</label>
                            <select id="sort_order" name="sort_order" class="px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <option value="desc" {% if sort_order == 'desc' or not sort_order %}selected{% endif %}>تنازلي</option>
                                <option value="asc" {% if sort_order == 'asc' %}selected{% endif %}>تصاعدي</option>
                            </select>
                        </div>

                        <div>
                            <label for="limit" class="block text-sm font-medium text-gray-700 mb-1">عدد النتائج</label>
                            <select id="limit" name="limit" class="px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <option value="50" {% if limit == 50 %}selected{% endif %}>50</option>
                                <option value="100" {% if limit == 100 or not limit %}selected{% endif %}>100</option>
                                <option value="200" {% if limit == 200 %}selected{% endif %}>200</option>
                                <option value="500" {% if limit == 500 %}selected{% endif %}>500</option>
                            </select>
                        </div>
                    </div>

                    <div class="flex mt-4 md:mt-0">
                        <button type="submit" class="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors duration-300 flex items-center">
                            <i class="ri-filter-line ml-1"></i>
                            تطبيق الفلاتر
                        </button>
                        <a href="{{ url_for('settings.error_logs') }}" class="mr-2 px-4 py-2 bg-gray-100 hover:bg-gray-200 text-gray-700 rounded-lg transition-colors duration-300 flex items-center">
                            <i class="ri-refresh-line ml-1"></i>
                            إعادة ضبط
                        </a>
                    </div>
                </div>
            </form>
        </div>

        <div class="p-6">
            {% if logs %}
                <div class="overflow-x-auto">
                    <table class="w-full">
                        <thead>
                            <tr class="bg-gray-50 text-right">
                                <th class="px-4 py-3 text-sm font-medium text-gray-500">التاريخ والوقت</th>
                                <th class="px-4 py-3 text-sm font-medium text-gray-500">النوع</th>
                                <th class="px-4 py-3 text-sm font-medium text-gray-500">المصدر</th>
                                <th class="px-4 py-3 text-sm font-medium text-gray-500">المستخدم</th>
                                <th class="px-4 py-3 text-sm font-medium text-gray-500">الرسالة</th>
                                <th class="px-4 py-3 text-sm font-medium text-gray-500">الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody class="divide-y divide-gray-100">
                            {% for log in logs %}
                                <tr class="hover:bg-gray-50">
                                    <td class="px-4 py-3 text-sm text-gray-700">{{ log.timestamp }}</td>
                                    <td class="px-4 py-3 text-sm">
                                        {% if log.level == 'critical' %}
                                            <span class="px-2 py-1 bg-red-200 text-red-800 rounded-full text-xs font-medium">حرج</span>
                                        {% elif log.level == 'error' %}
                                            <span class="px-2 py-1 bg-red-100 text-red-700 rounded-full text-xs">خطأ</span>
                                        {% elif log.level == 'warning' %}
                                            <span class="px-2 py-1 bg-yellow-100 text-yellow-700 rounded-full text-xs">تحذير</span>
                                        {% else %}
                                            <span class="px-2 py-1 bg-blue-100 text-blue-700 rounded-full text-xs">معلومات</span>
                                        {% endif %}
                                    </td>
                                    <td class="px-4 py-3 text-sm">
                                        {% if log.source == 'client' %}
                                            <span class="px-2 py-1 bg-purple-100 text-purple-700 rounded-full text-xs">العميل</span>
                                        {% else %}
                                            <span class="px-2 py-1 bg-blue-100 text-blue-700 rounded-full text-xs">الخادم</span>
                                        {% endif %}
                                    </td>
                                    <td class="px-4 py-3 text-sm text-gray-700">
                                        {% if log.username %}
                                            {{ log.username }}
                                        {% else %}
                                            <span class="text-gray-400">-</span>
                                        {% endif %}
                                    </td>
                                    <td class="px-4 py-3 text-sm text-gray-700">{{ log.message|truncate(60) }}</td>
                                    <td class="px-4 py-3 text-sm">
                                        <div class="flex space-x-1 space-x-reverse">
                                            <button class="view-details-btn px-2 py-1 bg-primary-100 hover:bg-primary-200 text-primary-700 rounded transition-colors duration-300 text-xs" data-log-id="{{ loop.index }}">
                                                <i class="ri-eye-line ml-1"></i>
                                                التفاصيل
                                            </button>
                                            <button class="copy-log-btn px-2 py-1 bg-gray-100 hover:bg-gray-200 text-gray-700 rounded transition-colors duration-300 text-xs" data-log-id="{{ loop.index }}">
                                                <i class="ri-file-copy-line ml-1"></i>
                                                نسخ
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            {% else %}
                <div class="text-center py-12">
                    <div class="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                        <i class="ri-check-line text-2xl text-gray-400"></i>
                    </div>
                    <h3 class="text-lg font-medium text-gray-700 mb-1">لا توجد سجلات أخطاء</h3>
                    <p class="text-sm text-gray-500">لم يتم تسجيل أي أخطاء في النظام حتى الآن</p>
                </div>
            {% endif %}
        </div>
    </div>
</div>

<!-- Modal for log details -->
<div id="logDetailsModal" class="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center hidden">
    <div class="bg-white rounded-xl shadow-xl w-full max-w-4xl max-h-[90vh] overflow-hidden">
        <div class="p-6 border-b border-gray-100 flex justify-between items-center" id="modalHeader">
            <div class="flex items-center">
                <div id="logLevelIcon" class="w-10 h-10 rounded-lg bg-red-100 text-red-600 flex items-center justify-center mr-3">
                    <i class="ri-error-warning-line text-xl"></i>
                </div>
                <div>
                    <h3 class="text-lg font-bold text-gray-800">تفاصيل الخطأ</h3>
                    <p id="logTimestamp" class="text-sm text-gray-500"></p>
                </div>
            </div>
            <div class="flex items-center space-x-2 space-x-reverse">
                <button id="copyFullLogBtn" class="p-2 hover:bg-gray-100 rounded-lg text-gray-500 flex items-center">
                    <i class="ri-file-copy-line ml-1"></i>
                    <span class="text-sm">نسخ الكل</span>
                </button>
                <button id="closeModalBtn" class="p-2 hover:bg-gray-100 rounded-full">
                    <i class="ri-close-line text-gray-500"></i>
                </button>
            </div>
        </div>

        <div class="p-6 overflow-y-auto max-h-[calc(90vh-180px)]">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                <div>
                    <h4 class="text-sm font-medium text-gray-500 mb-2">معلومات الخطأ</h4>
                    <div class="bg-gray-50 rounded-lg p-4">
                        <div class="mb-3">
                            <h5 class="text-xs font-medium text-gray-500 mb-1">النوع</h5>
                            <p id="logLevel" class="text-sm text-gray-700"></p>
                        </div>
                        <div class="mb-3">
                            <h5 class="text-xs font-medium text-gray-500 mb-1">المصدر</h5>
                            <p id="logSource" class="text-sm text-gray-700"></p>
                        </div>
                        <div class="mb-3">
                            <h5 class="text-xs font-medium text-gray-500 mb-1">الرسالة</h5>
                            <p id="logMessage" class="text-sm text-gray-700 break-words"></p>
                        </div>
                        <div>
                            <h5 class="text-xs font-medium text-gray-500 mb-1">الملف</h5>
                            <p id="logFile" class="text-sm text-gray-700 font-mono"></p>
                        </div>
                    </div>
                </div>

                <div>
                    <h4 class="text-sm font-medium text-gray-500 mb-2">معلومات المستخدم والنظام</h4>
                    <div class="bg-gray-50 rounded-lg p-4">
                        <div class="mb-3">
                            <h5 class="text-xs font-medium text-gray-500 mb-1">المستخدم</h5>
                            <p id="logUser" class="text-sm text-gray-700"></p>
                        </div>
                        <div class="mb-3">
                            <h5 class="text-xs font-medium text-gray-500 mb-1">عنوان IP</h5>
                            <p id="logIP" class="text-sm text-gray-700 font-mono"></p>
                        </div>
                        <div class="mb-3">
                            <h5 class="text-xs font-medium text-gray-500 mb-1">المتصفح</h5>
                            <p id="logBrowser" class="text-sm text-gray-700"></p>
                        </div>
                        <div>
                            <h5 class="text-xs font-medium text-gray-500 mb-1">نظام التشغيل</h5>
                            <p id="logOS" class="text-sm text-gray-700"></p>
                        </div>
                    </div>
                </div>
            </div>

            <div>
                <div class="flex justify-between items-center mb-2">
                    <h4 class="text-sm font-medium text-gray-500">التفاصيل الكاملة</h4>
                    <div class="flex space-x-2 space-x-reverse">
                        <button id="expandDetailsBtn" class="text-xs text-blue-600 hover:text-blue-800">
                            <i class="ri-fullscreen-line ml-1"></i>
                            توسيع
                        </button>
                        <button id="collapseDetailsBtn" class="text-xs text-blue-600 hover:text-blue-800 hidden">
                            <i class="ri-fullscreen-exit-line ml-1"></i>
                            تصغير
                        </button>
                    </div>
                </div>
                <pre id="logDetails" class="bg-gray-50 p-4 rounded-lg text-gray-700 text-sm overflow-x-auto whitespace-pre-wrap log-details-pre"></pre>
            </div>
        </div>

        <div class="p-4 bg-gray-50 border-t border-gray-100 flex justify-between">
            <div>
                <button id="prevLogBtn" class="px-4 py-2 bg-gray-200 hover:bg-gray-300 text-gray-700 rounded-lg transition-colors duration-300 flex items-center">
                    <i class="ri-arrow-right-line ml-1"></i>
                    السابق
                </button>
            </div>
            <div>
                <button id="closeModalBtn2" class="px-4 py-2 bg-gray-200 hover:bg-gray-300 text-gray-700 rounded-lg transition-colors duration-300">
                    إغلاق
                </button>
            </div>
            <div>
                <button id="nextLogBtn" class="px-4 py-2 bg-gray-200 hover:bg-gray-300 text-gray-700 rounded-lg transition-colors duration-300 flex items-center">
                    التالي
                    <i class="ri-arrow-left-line mr-1"></i>
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Variables
        const refreshBtn = document.getElementById('refreshBtn');
        const clearLogsBtn = document.getElementById('clearLogsBtn');
        const downloadLogsBtn = document.getElementById('downloadLogsBtn');
        const logDetailsModal = document.getElementById('logDetailsModal');
        const closeModalBtn = document.getElementById('closeModalBtn');
        const closeModalBtn2 = document.getElementById('closeModalBtn2');
        const logTimestamp = document.getElementById('logTimestamp');
        const logMessage = document.getElementById('logMessage');
        const logDetails = document.getElementById('logDetails');
        const logLevel = document.getElementById('logLevel');
        const logSource = document.getElementById('logSource');
        const logFile = document.getElementById('logFile');
        const logUser = document.getElementById('logUser');
        const logIP = document.getElementById('logIP');
        const logBrowser = document.getElementById('logBrowser');
        const logOS = document.getElementById('logOS');
        const logLevelIcon = document.getElementById('logLevelIcon');
        const modalHeader = document.getElementById('modalHeader');
        const viewDetailsBtns = document.querySelectorAll('.view-details-btn');
        const copyLogBtns = document.querySelectorAll('.copy-log-btn');
        const copyFullLogBtn = document.getElementById('copyFullLogBtn');
        const expandDetailsBtn = document.getElementById('expandDetailsBtn');
        const collapseDetailsBtn = document.getElementById('collapseDetailsBtn');
        const prevLogBtn = document.getElementById('prevLogBtn');
        const nextLogBtn = document.getElementById('nextLogBtn');

        // Logs data
        const logs = {{ logs|tojson }};
        let currentLogIndex = 0;

        // Refresh button
        refreshBtn.addEventListener('click', function() {
            window.location.reload();
        });

        // Download logs button
        downloadLogsBtn.addEventListener('click', function() {
            // بناء URL التنزيل مع نفس معلمات التصفية الحالية
            const currentUrl = new URL(window.location.href);
            const params = new URLSearchParams(currentUrl.search);

            // تعيين تنسيق التصدير
            params.set('format', 'csv');

            // إنشاء URL التصدير
            const exportUrl = `/api/errors/export?${params.toString()}`;

            // فتح URL التصدير في نافذة جديدة
            window.open(exportUrl, '_blank');
        });

        // Clear logs button
        clearLogsBtn.addEventListener('click', function() {
            if (confirm('هل أنت متأكد من رغبتك في مسح جميع سجلات الأخطاء؟')) {
                // الحصول على نوع السجل المحدد
                const logType = document.getElementById('log_type').value;

                fetch('/api/errors/clear', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        log_type: logType || null
                    })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        window.location.reload();
                    } else {
                        alert('حدث خطأ أثناء مسح السجلات: ' + data.message);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('حدث خطأ أثناء مسح السجلات');
                });
            }
        });

        // تحديث واجهة المستخدم لتفاصيل الخطأ
        function updateLogDetailsUI(log) {
            // تحديث المعلومات الأساسية
            logTimestamp.textContent = log.timestamp;
            logMessage.textContent = log.message;
            logDetails.textContent = log.details.join('\n');

            // تحديث معلومات الخطأ
            logLevel.textContent = getLevelText(log.level);
            logSource.textContent = log.source === 'client' ? 'العميل (JavaScript)' : 'الخادم (Python)';

            // تحديث معلومات الملف
            let fileInfo = '';
            if (log.file) {
                fileInfo += log.file;
                if (log.line) {
                    fileInfo += `:${log.line}`;
                }
                if (log.function) {
                    fileInfo += ` (${log.function})`;
                }
            } else {
                fileInfo = 'غير متاح';
            }
            logFile.textContent = fileInfo;

            // تحديث معلومات المستخدم
            logUser.textContent = log.username || 'غير مسجل الدخول';
            logIP.textContent = log.ip_address || 'غير متاح';
            logBrowser.textContent = log.browser || 'غير متاح';
            logOS.textContent = log.os || 'غير متاح';

            // تحديث أيقونة ولون النوع
            updateLogLevelUI(log.level);
        }

        // تحديث أيقونة ولون نوع الخطأ
        function updateLogLevelUI(level) {
            // إزالة الفئات السابقة
            logLevelIcon.className = 'w-10 h-10 rounded-lg flex items-center justify-center mr-3';
            modalHeader.className = 'p-6 border-b border-gray-100 flex justify-between items-center';

            // إضافة الفئات الجديدة حسب النوع
            if (level === 'critical') {
                logLevelIcon.classList.add('bg-red-200', 'text-red-800');
                logLevelIcon.innerHTML = '<i class="ri-alarm-warning-fill text-xl"></i>';
                modalHeader.classList.add('bg-red-50');
            } else if (level === 'error') {
                logLevelIcon.classList.add('bg-red-100', 'text-red-600');
                logLevelIcon.innerHTML = '<i class="ri-error-warning-line text-xl"></i>';
                modalHeader.classList.add('bg-red-50');
            } else if (level === 'warning') {
                logLevelIcon.classList.add('bg-yellow-100', 'text-yellow-600');
                logLevelIcon.innerHTML = '<i class="ri-alert-line text-xl"></i>';
                modalHeader.classList.add('bg-yellow-50');
            } else {
                logLevelIcon.classList.add('bg-blue-100', 'text-blue-600');
                logLevelIcon.innerHTML = '<i class="ri-information-line text-xl"></i>';
                modalHeader.classList.add('bg-blue-50');
            }
        }

        // الحصول على النص العربي لنوع الخطأ
        function getLevelText(level) {
            switch (level) {
                case 'critical': return 'حرج';
                case 'error': return 'خطأ';
                case 'warning': return 'تحذير';
                case 'info': return 'معلومات';
                default: return level;
            }
        }

        // عرض تفاصيل الخطأ
        function showLogDetails(logIndex) {
            if (logIndex >= 0 && logIndex < logs.length) {
                currentLogIndex = logIndex;
                const log = logs[logIndex];

                // تحديث واجهة المستخدم
                updateLogDetailsUI(log);

                // تحديث حالة أزرار التنقل
                prevLogBtn.disabled = logIndex === 0;
                nextLogBtn.disabled = logIndex === logs.length - 1;

                // إظهار النافذة
                logDetailsModal.classList.remove('hidden');
            }
        }

        // View details buttons
        viewDetailsBtns.forEach(btn => {
            btn.addEventListener('click', function() {
                const logId = parseInt(this.getAttribute('data-log-id')) - 1;
                showLogDetails(logId);
            });
        });

        // Copy log buttons
        copyLogBtns.forEach(btn => {
            btn.addEventListener('click', function() {
                const logId = parseInt(this.getAttribute('data-log-id')) - 1;
                const log = logs[logId];

                // إنشاء نص للنسخ
                const copyText = `تاريخ: ${log.timestamp}
نوع: ${getLevelText(log.level)}
مصدر: ${log.source === 'client' ? 'العميل' : 'الخادم'}
رسالة: ${log.message}
تفاصيل:
${log.details.join('\n')}`;

                // نسخ النص إلى الحافظة
                navigator.clipboard.writeText(copyText).then(() => {
                    // إظهار رسالة نجاح
                    const originalText = btn.innerHTML;
                    btn.innerHTML = '<i class="ri-check-line ml-1"></i> تم النسخ';

                    // إعادة النص الأصلي بعد ثانيتين
                    setTimeout(() => {
                        btn.innerHTML = originalText;
                    }, 2000);
                });
            });
        });

        // Copy full log button
        copyFullLogBtn.addEventListener('click', function() {
            const log = logs[currentLogIndex];

            // إنشاء نص للنسخ
            const copyText = `تاريخ: ${log.timestamp}
نوع: ${getLevelText(log.level)}
مصدر: ${log.source === 'client' ? 'العميل' : 'الخادم'}
مستخدم: ${log.username || 'غير مسجل الدخول'}
عنوان IP: ${log.ip_address || 'غير متاح'}
متصفح: ${log.browser || 'غير متاح'}
نظام التشغيل: ${log.os || 'غير متاح'}
ملف: ${log.file || 'غير متاح'}${log.line ? ':' + log.line : ''}${log.function ? ' (' + log.function + ')' : ''}
رسالة: ${log.message}
تفاصيل:
${log.details.join('\n')}`;

            // نسخ النص إلى الحافظة
            navigator.clipboard.writeText(copyText).then(() => {
                // إظهار رسالة نجاح
                const originalText = copyFullLogBtn.innerHTML;
                copyFullLogBtn.innerHTML = '<i class="ri-check-line ml-1"></i> <span class="text-sm">تم النسخ</span>';

                // إعادة النص الأصلي بعد ثانيتين
                setTimeout(() => {
                    copyFullLogBtn.innerHTML = originalText;
                }, 2000);
            });
        });

        // Expand/collapse details buttons
        expandDetailsBtn.addEventListener('click', function() {
            logDetails.style.maxHeight = 'none';
            expandDetailsBtn.classList.add('hidden');
            collapseDetailsBtn.classList.remove('hidden');
        });

        collapseDetailsBtn.addEventListener('click', function() {
            logDetails.style.maxHeight = '';
            collapseDetailsBtn.classList.add('hidden');
            expandDetailsBtn.classList.remove('hidden');
        });

        // Previous log button
        prevLogBtn.addEventListener('click', function() {
            if (currentLogIndex > 0) {
                showLogDetails(currentLogIndex - 1);
            }
        });

        // Next log button
        nextLogBtn.addEventListener('click', function() {
            if (currentLogIndex < logs.length - 1) {
                showLogDetails(currentLogIndex + 1);
            }
        });

        // Close modal buttons
        closeModalBtn.addEventListener('click', function() {
            logDetailsModal.classList.add('hidden');
        });

        closeModalBtn2.addEventListener('click', function() {
            logDetailsModal.classList.add('hidden');
        });

        // Close modal when clicking outside
        logDetailsModal.addEventListener('click', function(event) {
            if (event.target === logDetailsModal) {
                logDetailsModal.classList.add('hidden');
            }
        });

        // Close modal with Escape key
        document.addEventListener('keydown', function(event) {
            if (event.key === 'Escape' && !logDetailsModal.classList.contains('hidden')) {
                logDetailsModal.classList.add('hidden');
            }
        });

        // التنقل بين السجلات باستخدام مفاتيح الأسهم
        document.addEventListener('keydown', function(event) {
            if (logDetailsModal.classList.contains('hidden')) {
                return;
            }

            if (event.key === 'ArrowRight' && currentLogIndex > 0) {
                showLogDetails(currentLogIndex - 1);
                event.preventDefault();
            } else if (event.key === 'ArrowLeft' && currentLogIndex < logs.length - 1) {
                showLogDetails(currentLogIndex + 1);
                event.preventDefault();
            }
        });
    });
</script>
{% endblock %}
