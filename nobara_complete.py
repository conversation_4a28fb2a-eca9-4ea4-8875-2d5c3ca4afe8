#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Nobara POS System - Complete Working Version
نظام نوبارا لنقاط البيع - النسخة الكاملة والعاملة

Developer: ENG/ Fouad Saber
Phone: ***********
Email: <EMAIL>
"""

import os
import sys
import socket
import webbrowser
import logging
from datetime import datetime
from flask import Flask, render_template_string, request, redirect, url_for, flash, session
from flask_sqlalchemy import SQLAlchemy
from flask_login import LoginManager, UserMixin, login_user, logout_user, login_required, current_user
from werkzeug.security import generate_password_hash, check_password_hash

# Disable Flask logging to avoid encoding issues
log = logging.getLogger('werkzeug')
log.setLevel(logging.ERROR)

# Create Flask app
app = Flask(__name__)

# App configuration
app.config['SECRET_KEY'] = 'nobara-pos-secret-key-2024-fouad-saber'
app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///nobara_complete.db'
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False
app.config['SQLALCHEMY_ENGINE_OPTIONS'] = {
    'pool_pre_ping': True,
    'pool_recycle': 300,
}

# Initialize extensions
db = SQLAlchemy()
db.init_app(app)

login_manager = LoginManager()
login_manager.init_app(app)
login_manager.login_view = 'login'
login_manager.login_message = 'Please log in to access this page'

# Database Models
class User(UserMixin, db.Model):
    __tablename__ = 'users'

    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(80), unique=True, nullable=False)
    email = db.Column(db.String(120), unique=True, nullable=False)
    password_hash = db.Column(db.String(255), nullable=False)
    first_name = db.Column(db.String(50), nullable=False)
    last_name = db.Column(db.String(50), nullable=False)
    is_active = db.Column(db.Boolean, default=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    def __repr__(self):
        return f'<User {self.username}>'

class Product(db.Model):
    __tablename__ = 'products'

    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(200), nullable=False)
    sku = db.Column(db.String(50), unique=True)
    barcode = db.Column(db.String(50), unique=True)
    price = db.Column(db.Float, nullable=False, default=0.0)
    cost = db.Column(db.Float, nullable=False, default=0.0)
    quantity = db.Column(db.Integer, nullable=False, default=0)
    is_active = db.Column(db.Boolean, default=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    def __repr__(self):
        return f'<Product {self.name}>'

class Customer(db.Model):
    __tablename__ = 'customers'

    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(200), nullable=False)
    phone = db.Column(db.String(20))
    email = db.Column(db.String(120))
    address = db.Column(db.Text)
    is_active = db.Column(db.Boolean, default=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    def __repr__(self):
        return f'<Customer {self.name}>'

class Sale(db.Model):
    __tablename__ = 'sales'

    id = db.Column(db.Integer, primary_key=True)
    customer_id = db.Column(db.Integer, db.ForeignKey('customers.id'))
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'))
    total_amount = db.Column(db.Float, nullable=False, default=0.0)
    status = db.Column(db.String(20), default='completed')
    sale_date = db.Column(db.DateTime, default=datetime.utcnow)

    customer = db.relationship('Customer', backref='sales')
    user = db.relationship('User', backref='sales')

    def __repr__(self):
        return f'<Sale {self.id}>'

@login_manager.user_loader
def load_user(user_id):
    return User.query.get(int(user_id))

# Routes
@app.route('/')
def index():
    """Home page - redirect to login or dashboard"""
    if current_user.is_authenticated:
        return redirect(url_for('dashboard'))
    return redirect(url_for('login'))

@app.route('/login', methods=['GET', 'POST'])
def login():
    """Login page"""
    if current_user.is_authenticated:
        return redirect(url_for('dashboard'))

    if request.method == 'POST':
        username = request.form.get('username', '').strip()
        password = request.form.get('password', '').strip()

        if not username or not password:
            flash('Please enter both username and password', 'error')
            return redirect(url_for('login'))

        user = User.query.filter_by(username=username).first()

        if user and check_password_hash(user.password_hash, password):
            login_user(user, remember=True)
            flash(f'Welcome back, {user.first_name}!', 'success')
            return redirect(url_for('dashboard'))
        else:
            flash('Invalid username or password', 'error')
            return redirect(url_for('login'))

    # GET request - show login form
    return render_template_string(LOGIN_TEMPLATE)

@app.route('/logout')
@login_required
def logout():
    """Logout user"""
    logout_user()
    flash('You have been logged out successfully', 'info')
    return redirect(url_for('login'))

@app.route('/dashboard')
@login_required
def dashboard():
    """Main dashboard"""
    try:
        # Get statistics
        total_products = Product.query.filter_by(is_active=True).count()
        total_customers = Customer.query.filter_by(is_active=True).count()
        total_sales = Sale.query.filter_by(status='completed').count()

        # Calculate total revenue
        revenue_result = db.session.query(db.func.sum(Sale.total_amount)).filter_by(status='completed').scalar()
        total_revenue = revenue_result if revenue_result else 0.0

        return render_template_string(
            DASHBOARD_TEMPLATE,
            total_products=total_products,
            total_customers=total_customers,
            total_sales=total_sales,
            total_revenue=total_revenue,
            user=current_user
        )
    except Exception as e:
        flash(f'Error loading dashboard: {str(e)}', 'error')
        return render_template_string(ERROR_TEMPLATE, error=str(e))

@app.route('/pos')
@login_required
def pos():
    """Point of Sale page"""
    return render_template_string(COMING_SOON_TEMPLATE,
                                feature="Point of Sale",
                                description="Complete POS system with barcode scanning and receipt printing")

@app.route('/products')
@login_required
def products():
    """Products management page"""
    return render_template_string(COMING_SOON_TEMPLATE,
                                feature="Product Management",
                                description="Add, edit, and manage your inventory")

@app.route('/customers')
@login_required
def customers():
    """Customers management page"""
    return render_template_string(COMING_SOON_TEMPLATE,
                                feature="Customer Management",
                                description="Manage customer information and purchase history")

@app.route('/sales')
@login_required
def sales():
    """Sales management page"""
    return render_template_string(COMING_SOON_TEMPLATE,
                                feature="Sales Management",
                                description="View and manage all sales transactions")

@app.route('/reports')
@login_required
def reports():
    """Reports page"""
    return render_template_string(COMING_SOON_TEMPLATE,
                                feature="Reports & Analytics",
                                description="Detailed sales reports and business analytics")

@app.route('/test')
def test():
    """Test page to verify system is working"""
    return render_template_string('''
    <h1>✅ Nobara System Test</h1>
    <p>System is working correctly!</p>
    <p>Time: {{ now }}</p>
    <a href="{{ url_for('login') }}">Go to Login</a>
    ''', now=datetime.now().strftime('%Y-%m-%d %H:%M:%S'))

# Utility functions
def get_local_ip():
    """Get local IP address"""
    try:
        s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        s.connect(("*******", 80))
        local_ip = s.getsockname()[0]
        s.close()
        return local_ip
    except Exception:
        return "127.0.0.1"

def setup_database():
    """Initialize database and create sample data"""
    try:
        with app.app_context():
            # Create all tables
            db.create_all()

            # Check if admin user exists
            admin_user = User.query.filter_by(username='admin').first()

            if not admin_user:
                print("Creating default admin user...")

                # Create admin user
                admin = User(
                    username='admin',
                    email='<EMAIL>',
                    password_hash=generate_password_hash('admin'),
                    first_name='Fouad',
                    last_name='Saber'
                )
                db.session.add(admin)

                # Create sample products
                sample_products = [
                    Product(name='Sample Product 1', sku='SKU001', barcode='1234567890', price=100.0, cost=80.0, quantity=50),
                    Product(name='Sample Product 2', sku='SKU002', barcode='1234567891', price=200.0, cost=150.0, quantity=30),
                    Product(name='Sample Product 3', sku='SKU003', barcode='1234567892', price=50.0, cost=30.0, quantity=100),
                ]

                # Create sample customers
                sample_customers = [
                    Customer(name='Cash Customer', phone='01000000000', email='<EMAIL>'),
                    Customer(name='Ahmed Mohamed', phone='01111111111', email='<EMAIL>'),
                    Customer(name='Fatma Ali', phone='01222222222', email='<EMAIL>'),
                ]

                # Add sample data
                for product in sample_products:
                    db.session.add(product)

                for customer in sample_customers:
                    db.session.add(customer)

                # Commit all changes
                db.session.commit()
                print("✅ Database setup completed successfully!")

            else:
                print("✅ Database already exists and is ready!")

    except Exception as e:
        print(f"❌ Database setup error: {e}")
        return False

    return True

# HTML Templates
LOGIN_TEMPLATE = '''
<!DOCTYPE html>
<html lang="en" dir="ltr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Login - Nobara POS</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }
        .login-container {
            background: rgba(255, 255, 255, 0.95);
            padding: 40px;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            width: 100%;
            max-width: 400px;
            backdrop-filter: blur(10px);
        }
        .logo {
            text-align: center;
            margin-bottom: 30px;
        }
        .logo h1 {
            font-size: 2.5rem;
            color: #667eea;
            margin-bottom: 10px;
        }
        .logo p {
            color: #666;
            font-size: 1rem;
        }
        .form-group {
            margin-bottom: 20px;
        }
        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #333;
        }
        .form-group input {
            width: 100%;
            padding: 15px;
            border: 2px solid #e1e5e9;
            border-radius: 10px;
            font-size: 16px;
            transition: all 0.3s ease;
            background: #f8f9fa;
        }
        .form-group input:focus {
            outline: none;
            border-color: #667eea;
            background: white;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }
        .login-btn {
            width: 100%;
            padding: 15px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 10px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-bottom: 20px;
        }
        .login-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
        }
        .login-btn:active {
            transform: translateY(0);
        }
        .alert {
            padding: 12px 16px;
            border-radius: 8px;
            margin-bottom: 20px;
            font-weight: 500;
        }
        .alert-error {
            background: #fee;
            color: #c33;
            border: 1px solid #fcc;
        }
        .alert-success {
            background: #efe;
            color: #363;
            border: 1px solid #cfc;
        }
        .alert-info {
            background: #eef;
            color: #336;
            border: 1px solid #ccf;
        }
        .credentials {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            border: 2px dashed #dee2e6;
        }
        .credentials h4 {
            color: #495057;
            margin-bottom: 10px;
        }
        .credentials p {
            color: #6c757d;
            margin: 5px 0;
        }
        .footer {
            text-align: center;
            margin-top: 30px;
            color: #666;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="logo">
            <h1>🏪 Nobara</h1>
            <p>Professional POS System</p>
        </div>

        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                {% for category, message in messages %}
                    <div class="alert alert-{{ category }}">
                        {{ message }}
                    </div>
                {% endfor %}
            {% endif %}
        {% endwith %}

        <form method="POST" action="{{ url_for('login') }}">
            <div class="form-group">
                <label for="username">Username</label>
                <input type="text" id="username" name="username" required autocomplete="username">
            </div>

            <div class="form-group">
                <label for="password">Password</label>
                <input type="password" id="password" name="password" required autocomplete="current-password">
            </div>

            <button type="submit" class="login-btn">Login to System</button>
        </form>

        <div class="credentials">
            <h4>🔑 Default Credentials</h4>
            <p><strong>Username:</strong> admin</p>
            <p><strong>Password:</strong> admin</p>
        </div>

        <div class="footer">
            <p>&copy; 2024 Nobara POS System</p>
            <p><strong>Powered By ENG/ Fouad Saber - Tel: ***********</strong></p>
        </div>
    </div>
</body>
</html>
'''

DASHBOARD_TEMPLATE = '''
<!DOCTYPE html>
<html lang="en" dir="ltr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard - Nobara POS</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #f8f9fa;
            color: #333;
            line-height: 1.6;
        }
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 1rem 0;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .header-content {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .logo h1 {
            font-size: 1.8rem;
            font-weight: 700;
        }
        .user-info {
            display: flex;
            align-items: center;
            gap: 15px;
        }
        .logout-btn {
            background: rgba(255,255,255,0.2);
            color: white;
            padding: 8px 16px;
            border: 1px solid rgba(255,255,255,0.3);
            border-radius: 6px;
            text-decoration: none;
            transition: all 0.3s ease;
        }
        .logout-btn:hover {
            background: rgba(255,255,255,0.3);
            color: white;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 30px 20px;
        }
        .page-title {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 30px;
            color: #333;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 40px;
        }
        .stat-card {
            background: white;
            padding: 25px;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            border-left: 4px solid #667eea;
            transition: transform 0.3s ease;
        }
        .stat-card:hover {
            transform: translateY(-5px);
        }
        .stat-card:nth-child(2) { border-left-color: #764ba2; }
        .stat-card:nth-child(3) { border-left-color: #f093fb; }
        .stat-card:nth-child(4) { border-left-color: #4facfe; }
        .stat-icon {
            font-size: 2.5rem;
            margin-bottom: 15px;
            opacity: 0.8;
        }
        .stat-value {
            font-size: 2.2rem;
            font-weight: 700;
            margin-bottom: 5px;
            color: #333;
        }
        .stat-label {
            color: #666;
            font-weight: 500;
            text-transform: uppercase;
            font-size: 0.9rem;
            letter-spacing: 0.5px;
        }
        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 25px;
        }
        .feature-card {
            background: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            transition: all 0.3s ease;
            border: 1px solid #e9ecef;
        }
        .feature-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.15);
        }
        .feature-icon {
            font-size: 3rem;
            margin-bottom: 20px;
            opacity: 0.8;
        }
        .feature-title {
            font-size: 1.4rem;
            font-weight: 600;
            margin-bottom: 15px;
            color: #333;
        }
        .feature-description {
            color: #666;
            margin-bottom: 20px;
            line-height: 1.6;
        }
        .feature-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
        }
        .feature-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
            color: white;
        }
        .footer {
            background: #343a40;
            color: white;
            text-align: center;
            padding: 20px;
            margin-top: 50px;
        }
        .alert {
            padding: 15px 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            font-weight: 500;
        }
        .alert-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .alert-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
    </style>
</head>
<body>
    <header class="header">
        <div class="header-content">
            <div class="logo">
                <h1>🏪 Nobara POS Dashboard</h1>
            </div>
            <div class="user-info">
                <span>Welcome, {{ user.first_name }}!</span>
                <a href="{{ url_for('logout') }}" class="logout-btn">Logout</a>
            </div>
        </div>
    </header>

    <div class="container">
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                {% for category, message in messages %}
                    <div class="alert alert-{{ category }}">
                        {{ message }}
                    </div>
                {% endfor %}
            {% endif %}
        {% endwith %}

        <h1 class="page-title">Dashboard Overview</h1>

        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-icon">📦</div>
                <div class="stat-value">{{ total_products }}</div>
                <div class="stat-label">Total Products</div>
            </div>

            <div class="stat-card">
                <div class="stat-icon">👥</div>
                <div class="stat-value">{{ total_customers }}</div>
                <div class="stat-label">Total Customers</div>
            </div>

            <div class="stat-card">
                <div class="stat-icon">🛒</div>
                <div class="stat-value">{{ total_sales }}</div>
                <div class="stat-label">Total Sales</div>
            </div>

            <div class="stat-card">
                <div class="stat-icon">💰</div>
                <div class="stat-value">${{ "%.2f"|format(total_revenue) }}</div>
                <div class="stat-label">Total Revenue</div>
            </div>
        </div>

        <div class="features-grid">
            <div class="feature-card">
                <div class="feature-icon">🛒</div>
                <h3 class="feature-title">Point of Sale</h3>
                <p class="feature-description">Complete POS system with barcode scanning, receipt printing, and payment processing.</p>
                <a href="{{ url_for('pos') }}" class="feature-btn">Open POS</a>
            </div>

            <div class="feature-card">
                <div class="feature-icon">📦</div>
                <h3 class="feature-title">Product Management</h3>
                <p class="feature-description">Add, edit, and manage your inventory with advanced categorization and tracking.</p>
                <a href="{{ url_for('products') }}" class="feature-btn">Manage Products</a>
            </div>

            <div class="feature-card">
                <div class="feature-icon">👥</div>
                <h3 class="feature-title">Customer Management</h3>
                <p class="feature-description">Manage customer information, purchase history, and loyalty programs.</p>
                <a href="{{ url_for('customers') }}" class="feature-btn">Manage Customers</a>
            </div>

            <div class="feature-card">
                <div class="feature-icon">💼</div>
                <h3 class="feature-title">Sales Management</h3>
                <p class="feature-description">View and manage all sales transactions with detailed filtering options.</p>
                <a href="{{ url_for('sales') }}" class="feature-btn">View Sales</a>
            </div>

            <div class="feature-card">
                <div class="feature-icon">📊</div>
                <h3 class="feature-title">Reports & Analytics</h3>
                <p class="feature-description">Detailed sales reports, analytics, and business intelligence insights.</p>
                <a href="{{ url_for('reports') }}" class="feature-btn">View Reports</a>
            </div>

            <div class="feature-card">
                <div class="feature-icon">⚙️</div>
                <h3 class="feature-title">System Test</h3>
                <p class="feature-description">Test system functionality and verify all components are working correctly.</p>
                <a href="{{ url_for('test') }}" class="feature-btn">Run Test</a>
            </div>
        </div>
    </div>

    <footer class="footer">
        <p>&copy; 2024 Nobara POS System | <strong>Powered By ENG/ Fouad Saber - Tel: ***********</strong></p>
    </footer>
</body>
</html>
'''

COMING_SOON_TEMPLATE = '''
<!DOCTYPE html>
<html lang="en" dir="ltr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ feature }} - Nobara POS</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }
        .container {
            background: rgba(255, 255, 255, 0.95);
            padding: 50px;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            text-align: center;
            max-width: 500px;
            width: 100%;
        }
        .icon {
            font-size: 4rem;
            margin-bottom: 20px;
            opacity: 0.8;
        }
        h1 {
            font-size: 2.5rem;
            color: #333;
            margin-bottom: 15px;
        }
        .subtitle {
            font-size: 1.2rem;
            color: #666;
            margin-bottom: 30px;
        }
        .description {
            color: #777;
            line-height: 1.6;
            margin-bottom: 30px;
        }
        .back-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 15px 30px;
            border: none;
            border-radius: 10px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
        }
        .back-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
            color: white;
        }
        .footer {
            margin-top: 30px;
            color: #666;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="icon">🚧</div>
        <h1>{{ feature }}</h1>
        <p class="subtitle">Coming Soon!</p>
        <p class="description">{{ description }}</p>
        <a href="{{ url_for('dashboard') }}" class="back-btn">← Back to Dashboard</a>
        <div class="footer">
            <p><strong>Powered By ENG/ Fouad Saber - Tel: ***********</strong></p>
        </div>
    </div>
</body>
</html>
'''

ERROR_TEMPLATE = '''
<!DOCTYPE html>
<html lang="en" dir="ltr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Error - Nobara POS</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }
        .container {
            background: rgba(255, 255, 255, 0.95);
            padding: 50px;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            text-align: center;
            max-width: 500px;
            width: 100%;
        }
        .icon {
            font-size: 4rem;
            margin-bottom: 20px;
            color: #ff6b6b;
        }
        h1 {
            font-size: 2.5rem;
            color: #333;
            margin-bottom: 15px;
        }
        .error-message {
            color: #666;
            margin-bottom: 30px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 8px;
            border-left: 4px solid #ff6b6b;
        }
        .back-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 15px 30px;
            border: none;
            border-radius: 10px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
        }
        .back-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
            color: white;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="icon">⚠️</div>
        <h1>System Error</h1>
        <div class="error-message">{{ error }}</div>
        <a href="{{ url_for('dashboard') }}" class="back-btn">← Back to Dashboard</a>
    </div>
</body>
</html>
'''

# Main execution
if __name__ == "__main__":
    print("=" * 70)
    print("🏪 NOBARA POS SYSTEM - COMPLETE VERSION")
    print("=" * 70)
    print("📱 Version: 2.0.0")
    print("👨‍💻 Developer: ENG/ Fouad Saber")
    print("📞 Phone: ***********")
    print("📧 Email: <EMAIL>")
    print("=" * 70)
    print()

    print("🔧 Initializing system...")

    # Setup database
    if setup_database():
        print("✅ Database initialized successfully!")
    else:
        print("❌ Database initialization failed!")
        sys.exit(1)

    # Get network info
    local_ip = get_local_ip()

    print()
    print("🌐 ACCESS INFORMATION:")
    print(f"   Local:   http://localhost:5000")
    print(f"   Network: http://{local_ip}:5000")
    print()
    print("🔑 LOGIN CREDENTIALS:")
    print("   Username: admin")
    print("   Password: admin")
    print()
    print("✅ System is ready and running!")
    print("🚀 Opening browser...")
    print("=" * 70)

    # Open browser
    try:
        webbrowser.open('http://localhost:5000')
        print("✅ Browser opened successfully!")
    except Exception as e:
        print(f"⚠️  Could not open browser: {e}")
        print("   Please open http://localhost:5000 manually")

    print()
    print("🔥 SYSTEM STARTED SUCCESSFULLY!")
    print("   Press Ctrl+C to stop the system")
    print("=" * 70)

    # Run the Flask app
    try:
        app.run(
            host='0.0.0.0',
            port=5000,
            debug=False,
            use_reloader=False,
            threaded=True
        )
    except KeyboardInterrupt:
        print("\n" + "=" * 70)
        print("🛑 System stopped by user")
        print("👋 Thank you for using Nobara POS!")
        print("🏆 Powered By ENG/ Fouad Saber - Tel: ***********")
        print("=" * 70)
    except Exception as e:
        print(f"\n❌ System error: {e}")
        print("🔧 Please contact support: ***********")
    finally:
        print("\n🔚 System shutdown complete.")