/**
 * Nobara Theme Manager - مدير الثيمات نوبارا
 * Professional Theme & Language Management System
 * Supports Dark/Light modes and Arabic/English languages
 * Version: 2.0.0
 * Developer: ENG/ Fouad Saber
 */

class NobaraThemeManager {
    constructor() {
        this.currentTheme = localStorage.getItem('nobara-theme') || 'light';
        this.currentLanguage = localStorage.getItem('nobara-language') || 'ar';
        this.isRTL = this.currentLanguage === 'ar';
        
        this.init();
    }
    
    init() {
        this.applyTheme(this.currentTheme);
        this.applyLanguage(this.currentLanguage);
        this.setupEventListeners();
        this.createThemeToggle();
        this.createLanguageToggle();
        
        console.log('🎨 Nobara Theme Manager initialized');
    }
    
    // Theme Management
    applyTheme(theme) {
        document.documentElement.setAttribute('data-theme', theme);
        document.body.classList.toggle('dark-mode', theme === 'dark');
        
        this.currentTheme = theme;
        localStorage.setItem('nobara-theme', theme);
        
        // Update theme toggle button
        this.updateThemeToggle();
        
        // Dispatch theme change event
        window.dispatchEvent(new CustomEvent('themeChanged', {
            detail: { theme: theme }
        }));
        
        console.log(`🌙 Theme changed to: ${theme}`);
    }
    
    toggleTheme() {
        const newTheme = this.currentTheme === 'light' ? 'dark' : 'light';
        this.applyTheme(newTheme);
    }
    
    // Language Management
    applyLanguage(language) {
        document.documentElement.setAttribute('lang', language);
        document.documentElement.setAttribute('dir', language === 'ar' ? 'rtl' : 'ltr');
        document.body.classList.toggle('rtl', language === 'ar');
        document.body.classList.toggle('ltr', language === 'en');
        
        this.currentLanguage = language;
        this.isRTL = language === 'ar';
        localStorage.setItem('nobara-language', language);
        
        // Update language toggle button
        this.updateLanguageToggle();
        
        // Update text content
        this.updateLanguageContent();
        
        // Dispatch language change event
        window.dispatchEvent(new CustomEvent('languageChanged', {
            detail: { language: language, isRTL: this.isRTL }
        }));
        
        console.log(`🌍 Language changed to: ${language}`);
    }
    
    toggleLanguage() {
        const newLanguage = this.currentLanguage === 'ar' ? 'en' : 'ar';
        this.applyLanguage(newLanguage);
        
        // Reload page to apply language changes
        setTimeout(() => {
            window.location.reload();
        }, 300);
    }
    
    // UI Creation
    createThemeToggle() {
        const themeToggle = document.createElement('button');
        themeToggle.id = 'theme-toggle';
        themeToggle.className = 'btn btn-icon btn-secondary theme-toggle';
        themeToggle.setAttribute('aria-label', 'Toggle theme');
        themeToggle.setAttribute('title', this.currentLanguage === 'ar' ? 'تبديل الثيم' : 'Toggle Theme');
        
        this.updateThemeToggleContent(themeToggle);
        
        themeToggle.addEventListener('click', () => this.toggleTheme());
        
        // Add to header
        const header = document.querySelector('.header-actions') || document.querySelector('.topnav');
        if (header) {
            header.appendChild(themeToggle);
        }
    }
    
    createLanguageToggle() {
        const languageToggle = document.createElement('button');
        languageToggle.id = 'language-toggle';
        languageToggle.className = 'btn btn-icon btn-secondary language-toggle';
        languageToggle.setAttribute('aria-label', 'Toggle language');
        languageToggle.setAttribute('title', this.currentLanguage === 'ar' ? 'تبديل اللغة' : 'Toggle Language');
        
        this.updateLanguageToggleContent(languageToggle);
        
        languageToggle.addEventListener('click', () => this.toggleLanguage());
        
        // Add to header
        const header = document.querySelector('.header-actions') || document.querySelector('.topnav');
        if (header) {
            header.appendChild(languageToggle);
        }
    }
    
    updateThemeToggle() {
        const themeToggle = document.getElementById('theme-toggle');
        if (themeToggle) {
            this.updateThemeToggleContent(themeToggle);
        }
    }
    
    updateThemeToggleContent(button) {
        const isDark = this.currentTheme === 'dark';
        button.innerHTML = `<i class="ri-${isDark ? 'sun' : 'moon'}-line"></i>`;
        button.setAttribute('title', this.currentLanguage === 'ar' ? 
            (isDark ? 'الوضع النهاري' : 'الوضع الليلي') : 
            (isDark ? 'Light Mode' : 'Dark Mode')
        );
    }
    
    updateLanguageToggle() {
        const languageToggle = document.getElementById('language-toggle');
        if (languageToggle) {
            this.updateLanguageToggleContent(languageToggle);
        }
    }
    
    updateLanguageToggleContent(button) {
        const isArabic = this.currentLanguage === 'ar';
        button.innerHTML = `<span class="font-bold">${isArabic ? 'EN' : 'ع'}</span>`;
        button.setAttribute('title', isArabic ? 'English' : 'العربية');
    }
    
    // Content Updates
    updateLanguageContent() {
        // Update all elements with data-lang attributes
        const langElements = document.querySelectorAll('[data-lang]');
        langElements.forEach(element => {
            const langData = JSON.parse(element.getAttribute('data-lang') || '{}');
            if (langData[this.currentLanguage]) {
                element.textContent = langData[this.currentLanguage];
            }
        });
        
        // Update placeholders
        const placeholderElements = document.querySelectorAll('[data-placeholder]');
        placeholderElements.forEach(element => {
            const placeholderData = JSON.parse(element.getAttribute('data-placeholder') || '{}');
            if (placeholderData[this.currentLanguage]) {
                element.setAttribute('placeholder', placeholderData[this.currentLanguage]);
            }
        });
        
        // Update titles
        const titleElements = document.querySelectorAll('[data-title]');
        titleElements.forEach(element => {
            const titleData = JSON.parse(element.getAttribute('data-title') || '{}');
            if (titleData[this.currentLanguage]) {
                element.setAttribute('title', titleData[this.currentLanguage]);
            }
        });
    }
    
    // Event Listeners
    setupEventListeners() {
        // Listen for system theme changes
        if (window.matchMedia) {
            const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
            mediaQuery.addEventListener('change', (e) => {
                if (!localStorage.getItem('nobara-theme')) {
                    this.applyTheme(e.matches ? 'dark' : 'light');
                }
            });
        }
        
        // Listen for keyboard shortcuts
        document.addEventListener('keydown', (e) => {
            // Ctrl/Cmd + Shift + T for theme toggle
            if ((e.ctrlKey || e.metaKey) && e.shiftKey && e.key === 'T') {
                e.preventDefault();
                this.toggleTheme();
            }
            
            // Ctrl/Cmd + Shift + L for language toggle
            if ((e.ctrlKey || e.metaKey) && e.shiftKey && e.key === 'L') {
                e.preventDefault();
                this.toggleLanguage();
            }
        });
    }
    
    // Utility Methods
    getTheme() {
        return this.currentTheme;
    }
    
    getLanguage() {
        return this.currentLanguage;
    }
    
    isRTLMode() {
        return this.isRTL;
    }
    
    isDarkMode() {
        return this.currentTheme === 'dark';
    }
    
    // CSS Custom Properties Update
    updateCSSProperties() {
        const root = document.documentElement;
        
        if (this.currentTheme === 'dark') {
            root.style.setProperty('--primary-bg', '#0f172a');
            root.style.setProperty('--secondary-bg', '#1e293b');
            root.style.setProperty('--text-primary', '#f8fafc');
            root.style.setProperty('--text-secondary', '#e2e8f0');
        } else {
            root.style.setProperty('--primary-bg', '#ffffff');
            root.style.setProperty('--secondary-bg', '#f8fafc');
            root.style.setProperty('--text-primary', '#0f172a');
            root.style.setProperty('--text-secondary', '#475569');
        }
    }
    
    // Animation Effects
    addThemeTransition() {
        document.body.style.transition = 'background-color 0.3s ease, color 0.3s ease';
        
        setTimeout(() => {
            document.body.style.transition = '';
        }, 300);
    }
    
    // Save User Preferences
    savePreferences() {
        const preferences = {
            theme: this.currentTheme,
            language: this.currentLanguage,
            timestamp: new Date().toISOString()
        };
        
        localStorage.setItem('nobara-preferences', JSON.stringify(preferences));
        
        // Send to server if user is logged in
        if (window.currentUser) {
            fetch('/api/user/preferences', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': document.querySelector('meta[name=csrf-token]')?.getAttribute('content')
                },
                body: JSON.stringify(preferences)
            }).catch(error => console.warn('Failed to save preferences to server:', error));
        }
    }
    
    // Load User Preferences
    loadPreferences() {
        try {
            const saved = localStorage.getItem('nobara-preferences');
            if (saved) {
                const preferences = JSON.parse(saved);
                this.applyTheme(preferences.theme || 'light');
                this.applyLanguage(preferences.language || 'ar');
            }
        } catch (error) {
            console.warn('Failed to load preferences:', error);
        }
    }
}

// Initialize theme manager when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.nobaraThemeManager = new NobaraThemeManager();
});

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = NobaraThemeManager;
}
