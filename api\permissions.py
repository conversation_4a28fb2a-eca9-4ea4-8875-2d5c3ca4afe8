from flask import Blueprint, jsonify, request
from flask_login import login_required, current_user
from models import User, Permission, UserPermission, Role, db
from permissions_manager import PermissionsManager
from sqlalchemy import or_, and_
import logging

# إعداد التسجيل
logger = logging.getLogger("api.permissions")
logger.setLevel(logging.INFO)

permissions_api = Blueprint('permissions_api', __name__)

@permissions_api.route('/api/permissions', methods=['GET'])
@login_required
def get_permissions():
    """الحصول على قائمة الصلاحيات المتاحة في النظام"""
    try:
        # التحقق من صلاحية المستخدم الحالي
        if not current_user.has_permission('users', 'view'):
            return jsonify({
                'success': False,
                'message': 'ليس لديك صلاحية عرض الصلاحيات'
            }), 403
            
        # تهيئة الصلاحيات إذا لم تكن موجودة
        PermissionsManager.initialize_permissions()
        
        # الحصول على معلمات الاستعلام
        module = request.args.get('module', '')
        action = request.args.get('action', '')
        
        # إعداد الاستعلام الأساسي
        query = Permission.query
        
        # تطبيق الفلاتر
        if module:
            query = query.filter_by(module=module)
        if action:
            query = query.filter_by(action=action)
            
        # ترتيب النتائج
        query = query.order_by(Permission.module, Permission.action)
        
        # الحصول على النتائج
        permissions = query.all()
        
        # تنظيم الصلاحيات حسب الوحدة
        permissions_by_module = {}
        
        for permission in permissions:
            if permission.module not in permissions_by_module:
                permissions_by_module[permission.module] = {
                    'name': PermissionsManager.MODULES.get(permission.module, permission.module),
                    'permissions': []
                }
                
            permissions_by_module[permission.module]['permissions'].append({
                'id': permission.id,
                'name': permission.name,
                'description': permission.description,
                'module': permission.module,
                'action': permission.action,
                'action_name': PermissionsManager.ACTIONS.get(permission.action, permission.action)
            })
            
        return jsonify({
            'success': True,
            'permissions': permissions_by_module
        })
    except Exception as e:
        logger.error(f"خطأ في الحصول على قائمة الصلاحيات: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'حدث خطأ أثناء جلب قائمة الصلاحيات: {str(e)}'
        }), 500

@permissions_api.route('/api/users/<int:user_id>/permissions', methods=['GET'])
@login_required
def get_user_permissions(user_id):
    """الحصول على صلاحيات مستخدم محدد"""
    try:
        # التحقق من صلاحية المستخدم الحالي
        if not current_user.has_permission('users', 'view'):
            return jsonify({
                'success': False,
                'message': 'ليس لديك صلاحية عرض صلاحيات المستخدمين'
            }), 403
            
        # الحصول على المستخدم
        user = User.query.get_or_404(user_id)
        
        # الحصول على صلاحيات المستخدم
        permissions_dict = PermissionsManager.get_user_permissions(user_id)
        
        # تنظيم الصلاحيات حسب الوحدة
        permissions_by_module = {}
        
        for permission_name, granted in permissions_dict.items():
            # استخراج الوحدة والإجراء من اسم الصلاحية
            module, action = permission_name.split('.')
            
            if module not in permissions_by_module:
                permissions_by_module[module] = {
                    'name': PermissionsManager.MODULES.get(module, module),
                    'permissions': []
                }
                
            permissions_by_module[module]['permissions'].append({
                'name': permission_name,
                'action': action,
                'action_name': PermissionsManager.ACTIONS.get(action, action),
                'granted': granted
            })
            
        return jsonify({
            'success': True,
            'user': {
                'id': user.id,
                'username': user.username,
                'role': user.role
            },
            'permissions': permissions_by_module
        })
    except Exception as e:
        logger.error(f"خطأ في الحصول على صلاحيات المستخدم: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'حدث خطأ أثناء جلب صلاحيات المستخدم: {str(e)}'
        }), 500

@permissions_api.route('/api/users/<int:user_id>/permissions', methods=['POST'])
@login_required
def update_user_permissions(user_id):
    """تحديث صلاحيات مستخدم محدد"""
    try:
        # التحقق من صلاحية المستخدم الحالي
        if not current_user.has_permission('users', 'edit'):
            return jsonify({
                'success': False,
                'message': 'ليس لديك صلاحية تعديل صلاحيات المستخدمين'
            }), 403
            
        # الحصول على المستخدم
        user = User.query.get_or_404(user_id)
        
        # لا يمكن تعديل صلاحيات المدير
        if user.role == 'admin':
            return jsonify({
                'success': False,
                'message': 'لا يمكن تعديل صلاحيات المدير'
            }), 400
            
        data = request.json
        
        # التحقق من البيانات المطلوبة
        if not data or 'permissions' not in data:
            return jsonify({
                'success': False,
                'message': 'لم يتم توفير بيانات الصلاحيات'
            }), 400
            
        # الحصول على الصلاحيات المرسلة
        permissions_data = data['permissions']
        
        # حذف الصلاحيات الحالية للمستخدم
        UserPermission.query.filter_by(user_id=user_id).delete()
        
        # إضافة الصلاحيات الجديدة
        for permission_name, granted in permissions_data.items():
            # البحث عن الصلاحية
            permission = Permission.query.filter_by(name=permission_name).first()
            
            if permission:
                # إضافة الصلاحية للمستخدم
                user_permission = UserPermission(
                    user_id=user_id,
                    permission_id=permission.id,
                    granted=granted
                )
                db.session.add(user_permission)
                
        db.session.commit()
        
        # تسجيل النشاط
        user.log_activity(
            activity_type='update_permissions',
            description=f'تم تحديث صلاحيات المستخدم بواسطة {current_user.username}',
            ip_address=request.remote_addr
        )
        db.session.commit()
        
        return jsonify({
            'success': True,
            'message': 'تم تحديث صلاحيات المستخدم بنجاح'
        })
    except Exception as e:
        db.session.rollback()
        logger.error(f"خطأ في تحديث صلاحيات المستخدم: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'حدث خطأ أثناء تحديث صلاحيات المستخدم: {str(e)}'
        }), 500

@permissions_api.route('/api/roles', methods=['GET'])
@login_required
def get_roles():
    """الحصول على قائمة الأدوار المتاحة في النظام"""
    try:
        # التحقق من صلاحية المستخدم الحالي
        if not current_user.has_permission('users', 'view'):
            return jsonify({
                'success': False,
                'message': 'ليس لديك صلاحية عرض الأدوار'
            }), 403
            
        # الحصول على جميع الأدوار
        roles = Role.query.order_by(Role.name).all()
        
        return jsonify({
            'success': True,
            'roles': [role.to_dict() for role in roles]
        })
    except Exception as e:
        logger.error(f"خطأ في الحصول على قائمة الأدوار: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'حدث خطأ أثناء جلب قائمة الأدوار: {str(e)}'
        }), 500

def register_permissions_api(app):
    """تسجيل واجهة برمجة التطبيقات للصلاحيات"""
    app.register_blueprint(permissions_api)
