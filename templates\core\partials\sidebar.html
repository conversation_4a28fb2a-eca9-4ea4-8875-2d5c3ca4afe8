<div class="w-64 bg-white shadow-lg z-10 transition-all duration-300 border-l border-gray-100" id="sidebar">
    <div class="p-5 border-b border-gray-100">
        <div class="flex items-center justify-center">
            <img src="{{ url_for('static', filename='img/nobara-logo.svg') }}" alt="Nobara" class="h-12">
        </div>
    </div>

    <div class="overflow-y-auto h-full scrollbar-hide pb-20">
        <nav class="mt-6">
            <div class="px-5 py-2">
                <h2 class="text-xs font-semibold text-gray-500 tracking-wider">القائمة الرئيسية</h2>
            </div>

            <a href="{{ url_for('dashboard.home') }}" class="flex items-center px-5 py-3 {{ 'text-primary-700 bg-primary-50 border-r-4 border-primary-600 font-medium' if request.endpoint == 'dashboard.home' else 'text-gray-700 hover:bg-gray-50 hover:text-primary-600' }}">
                <div class="w-8 h-8 flex items-center justify-center text-lg {{ 'text-primary-600' if request.endpoint == 'dashboard.home' else 'text-gray-500' }}">
                    <i class="ri-dashboard-line"></i>
                </div>
                <span class="mx-3">لوحة التحكم</span>
            </a>

            <a href="{{ url_for('pos.index') }}" class="flex items-center px-5 py-3 {{ 'text-primary-700 bg-primary-50 border-r-4 border-primary-600 font-medium' if request.endpoint == 'pos.index' else 'text-gray-700 hover:bg-gray-50 hover:text-primary-600' }}">
                <div class="w-8 h-8 flex items-center justify-center text-lg {{ 'text-primary-600' if request.endpoint == 'pos.index' else 'text-gray-500' }}">
                    <i class="ri-shopping-cart-line"></i>
                </div>
                <span class="mx-3">نقطة البيع</span>
            </a>

            <a href="{{ url_for('products.index') }}" class="flex items-center px-5 py-3 {{ 'text-primary-700 bg-primary-50 border-r-4 border-primary-600 font-medium' if 'products.' in request.endpoint and request.endpoint != 'products.categories' else 'text-gray-700 hover:bg-gray-50 hover:text-primary-600' }}">
                <div class="w-8 h-8 flex items-center justify-center text-lg {{ 'text-primary-600' if 'products.' in request.endpoint and request.endpoint != 'products.categories' else 'text-gray-500' }}">
                    <i class="ri-store-line"></i>
                </div>
                <span class="mx-3">المنتجات</span>
            </a>

            <div class="relative" x-data="{ salesOpen: {{ 'true' if 'sales.' in request.endpoint or 'returns.' in request.endpoint else 'false' }} }">
                <button @click="salesOpen = !salesOpen" class="w-full flex items-center px-5 py-3 {{ 'text-primary-700' if 'sales.' in request.endpoint or 'returns.' in request.endpoint else 'text-gray-700 hover:bg-gray-50 hover:text-primary-600' }}">
                    <div class="w-8 h-8 flex items-center justify-center text-lg {{ 'text-primary-600' if 'sales.' in request.endpoint or 'returns.' in request.endpoint else 'text-gray-500' }}">
                        <i class="ri-file-list-3-line"></i>
                    </div>
                    <span class="mx-3 {{ 'font-medium' if 'sales.' in request.endpoint or 'returns.' in request.endpoint else '' }}">المبيعات</span>
                    <i class="ri-arrow-down-s-line mr-auto transition-transform" :class="{ 'rotate-180': salesOpen }"></i>
                </button>

                <div x-show="salesOpen" x-transition:enter="transition ease-out duration-200" x-transition:enter-start="opacity-0 transform scale-95" x-transition:enter-end="opacity-100 transform scale-100" x-transition:leave="transition ease-in duration-150" x-transition:leave-start="opacity-100 transform scale-100" x-transition:leave-end="opacity-0 transform scale-95" class="pr-8 mt-1">
                    <a href="{{ url_for('sales.index') }}" class="flex items-center px-5 py-2 rounded-lg {{ 'text-primary-700 bg-primary-50' if request.endpoint == 'sales.index' else 'text-gray-700 hover:bg-gray-50 hover:text-primary-600' }}">
                        <div class="w-6 h-6 flex items-center justify-center {{ 'text-primary-600' if request.endpoint == 'sales.index' else 'text-gray-500' }}">
                            <i class="ri-file-list-3-line"></i>
                        </div>
                        <span class="mx-3">قائمة المبيعات</span>
                    </a>

                    <a href="{{ url_for('returns.index') }}" class="flex items-center px-5 py-2 rounded-lg {{ 'text-primary-700 bg-primary-50' if 'returns.' in request.endpoint else 'text-gray-700 hover:bg-gray-50 hover:text-primary-600' }}">
                        <div class="w-6 h-6 flex items-center justify-center {{ 'text-primary-600' if 'returns.' in request.endpoint else 'text-gray-500' }}">
                            <i class="ri-arrow-go-back-line"></i>
                        </div>
                        <span class="mx-3">المرتجعات</span>
                    </a>
                </div>
            </div>

            <a href="{{ url_for('purchases.index') }}" class="flex items-center px-5 py-3 {{ 'text-primary-700 bg-primary-50 border-r-4 border-primary-600 font-medium' if 'purchases.' in request.endpoint else 'text-gray-700 hover:bg-gray-50 hover:text-primary-600' }}">
                <div class="w-8 h-8 flex items-center justify-center text-lg {{ 'text-primary-600' if 'purchases.' in request.endpoint else 'text-gray-500' }}">
                    <i class="ri-truck-line"></i>
                </div>
                <span class="mx-3">المشتريات</span>
            </a>

            <a href="{{ url_for('customers.index') }}" class="flex items-center px-5 py-3 {{ 'text-primary-700 bg-primary-50 border-r-4 border-primary-600 font-medium' if 'customers.' in request.endpoint else 'text-gray-700 hover:bg-gray-50 hover:text-primary-600' }}">
                <div class="w-8 h-8 flex items-center justify-center text-lg {{ 'text-primary-600' if 'customers.' in request.endpoint else 'text-gray-500' }}">
                    <i class="ri-user-line"></i>
                </div>
                <span class="mx-3">العملاء</span>
            </a>

            <a href="{{ url_for('suppliers.index') }}" class="flex items-center px-5 py-3 {{ 'text-primary-700 bg-primary-50 border-r-4 border-primary-600 font-medium' if 'suppliers.' in request.endpoint else 'text-gray-700 hover:bg-gray-50 hover:text-primary-600' }}">
                <div class="w-8 h-8 flex items-center justify-center text-lg {{ 'text-primary-600' if 'suppliers.' in request.endpoint else 'text-gray-500' }}">
                    <i class="ri-building-line"></i>
                </div>
                <span class="mx-3">الموردين</span>
            </a>

            <a href="{{ url_for('reports.index') }}" class="flex items-center px-5 py-3 {{ 'text-primary-700 bg-primary-50 border-r-4 border-primary-600 font-medium' if 'reports.' in request.endpoint else 'text-gray-700 hover:bg-gray-50 hover:text-primary-600' }}">
                <div class="w-8 h-8 flex items-center justify-center text-lg {{ 'text-primary-600' if 'reports.' in request.endpoint else 'text-gray-500' }}">
                    <i class="ri-bar-chart-line"></i>
                </div>
                <span class="mx-3">التقارير</span>
            </a>

            <a href="{{ url_for('warehouses.index') }}" class="flex items-center px-5 py-3 {{ 'text-primary-700 bg-primary-50 border-r-4 border-primary-600 font-medium' if 'warehouses.' in request.endpoint else 'text-gray-700 hover:bg-gray-50 hover:text-primary-600' }}">
                <div class="w-8 h-8 flex items-center justify-center text-lg {{ 'text-primary-600' if 'warehouses.' in request.endpoint else 'text-gray-500' }}">
                    <i class="ri-store-2-line"></i>
                </div>
                <span class="mx-3">المخازن</span>
            </a>

            <div class="px-5 py-2 mt-6">
                <h2 class="text-xs font-semibold text-gray-500 tracking-wider">الإدارة</h2>
            </div>

            <a href="{{ url_for('users.index') }}" class="flex items-center px-5 py-3 {{ 'text-primary-700 bg-primary-50 border-r-4 border-primary-600 font-medium' if 'users.' in request.endpoint else 'text-gray-700 hover:bg-gray-50 hover:text-primary-600' }}">
                <div class="w-8 h-8 flex items-center justify-center text-lg {{ 'text-primary-600' if 'users.' in request.endpoint else 'text-gray-500' }}">
                    <i class="ri-user-settings-line"></i>
                </div>
                <span class="mx-3">المستخدمين</span>
            </a>

            <a href="{{ url_for('settings.index') }}" class="flex items-center px-5 py-3 {{ 'text-primary-700 bg-primary-50 border-r-4 border-primary-600 font-medium' if 'settings.' in request.endpoint else 'text-gray-700 hover:bg-gray-50 hover:text-primary-600' }}">
                <div class="w-8 h-8 flex items-center justify-center text-lg {{ 'text-primary-600' if 'settings.' in request.endpoint else 'text-gray-500' }}">
                    <i class="ri-settings-line"></i>
                </div>
                <span class="mx-3">الإعدادات</span>
            </a>

            <!-- Powered By -->
            <div class="absolute bottom-0 left-0 right-0 p-4 border-t border-gray-100">
                <div class="flex items-center justify-center text-xs text-gray-500">
                    <div class="w-6 h-6 rounded-full bg-primary-50 flex items-center justify-center text-primary-600 ml-2">
                        <i class="ri-code-s-slash-line"></i>
                    </div>
                    <div>
                        <div>Powered By <span class="font-bold text-primary-600">ENG/ Fouad Saber</span></div>
                        <div class="text-gray-400">Tel: 01020073527</div>
                    </div>
                </div>
            </div>
        </nav>
    </div>
</div>