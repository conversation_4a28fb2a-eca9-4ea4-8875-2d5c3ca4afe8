from flask import Blueprint, render_template, request, jsonify, flash, redirect, url_for
from flask_login import login_required, current_user
from models import Product, Category, Customer, Order, OrderItem, Inventory, Warehouse, Notification, PaymentMethod
from app import db
import random
import string
import barcode
from barcode.writer import ImageWriter
from datetime import datetime
import os
import sys

# التحقق من وجود وحدة serial وإنشاء وحدة بديلة إذا لم تكن موجودة
try:
    import serial
except ImportError:
    # إنشاء وحدة serial وهمية
    class DummySerial:
        def __init__(self, *args, **kwargs):
            pass

        def write(self, data):
            print("محاكاة فتح درج النقود...")
            return len(data)

        def close(self):
            pass

    # إضافة الوحدة الوهمية إلى sys.modules
    sys.modules["serial"] = DummySerial
    serial = DummySerial
    print("تم إنشاء وحدة serial وهمية. لن يعمل درج النقود.")

pos_blueprint = Blueprint('pos', __name__)

def generate_barcode(order_id):
    # Generate unique barcode for order
    code128 = barcode.Code128(str(order_id).zfill(8))
    return code128.get_fullcode()

def open_cash_drawer():
    try:
        # Configure for your cash drawer - this is an example
        ser = serial.Serial('/dev/usb/lp0', 9600)
        # Send command to open drawer - adjust command per your hardware
        ser.write(b'\x1B\x70\x00\x19\xFA')
        ser.close()
        return True
    except:
        return False

@pos_blueprint.route('/pos')
@login_required
def index():
    # Get all categories for the sidebar
    categories = Category.query.order_by(Category.name).all()

    # Get customers for dropdown
    customers = Customer.query.order_by(Customer.name).all()

    # Get warehouses for dropdown
    warehouses = Warehouse.query.filter_by(is_active=True).order_by(Warehouse.name).all()

    # Get payment methods
    payment_methods = PaymentMethod.query.filter_by(is_active=True).order_by(PaymentMethod.name).all()

    # Get default payment method
    default_payment_method = PaymentMethod.query.filter_by(is_default=True).first()
    if not default_payment_method and payment_methods:
        default_payment_method = payment_methods[0]

    # Get default warehouse
    default_warehouse = Warehouse.query.filter_by(is_default=True).first()
    if not default_warehouse and warehouses:
        default_warehouse = warehouses[0]

    # Check if we're retrieving an invoice
    invoice_number = request.args.get('invoice')
    retrieved_invoice = None
    retrieved_items = []

    if invoice_number:
        # Try to retrieve the invoice
        try:
            order = Order.query.filter_by(invoice_number=invoice_number).first()
            if order:
                # Get customer name if available
                customer_name = None
                if order.customer_id:
                    customer = Customer.query.get(order.customer_id)
                    if customer:
                        customer_name = customer.name

                # Prepare invoice data
                retrieved_invoice = {
                    'id': order.id,
                    'invoice_number': order.invoice_number,
                    'created_at': order.created_at.strftime('%Y-%m-%d %H:%M:%S'),
                    'total': order.total,
                    'subtotal': order.subtotal,
                    'discount': order.discount,
                    'discount_type': order.discount_type,
                    'tax': order.tax,
                    'tax_percentage': order.tax_percentage,
                    'status': order.status,
                    'payment_method': order.payment_method,
                    'customer_id': order.customer_id,
                    'customer_name': customer_name
                }

                # Get order items
                order_items = OrderItem.query.filter_by(order_id=order.id).all()

                # Prepare items data
                for item in order_items:
                    product = Product.query.get(item.product_id)
                    if product:
                        # حساب الكمية المتاحة للاسترجاع (الكمية الأصلية - الكمية المرتجعة)
                        available_quantity = item.quantity
                        if hasattr(item, 'returned_quantity') and item.returned_quantity:
                            available_quantity -= item.returned_quantity

                        if available_quantity > 0:
                            retrieved_items.append({
                                'id': item.id,
                                'product_id': item.product_id,
                                'product_name': product.name,
                                'price': item.price,
                                'quantity': available_quantity,
                                'total': item.price * available_quantity,
                                'original_quantity': item.quantity,
                                'returned_quantity': item.returned_quantity if hasattr(item, 'returned_quantity') else 0
                            })
        except Exception as e:
            print(f"Error retrieving invoice: {str(e)}")
            # Continue without retrieved invoice
    else:
        # Generate a new invoice number
        current_date = datetime.utcnow().strftime('%Y%m%d')
        random_chars = ''.join(random.choices(string.ascii_uppercase + string.digits, k=4))
        invoice_number = f"INV-{current_date}-{random_chars}"

    return render_template(
        'pos/pos_new.html',
        categories=categories,
        customers=customers,
        warehouses=warehouses,
        default_warehouse=default_warehouse,
        payment_methods=payment_methods,
        default_payment_method=default_payment_method,
        invoice_number=invoice_number,
        retrieved_invoice=retrieved_invoice,
        retrieved_items=retrieved_items,
        current_user=current_user
    )

@pos_blueprint.route('/api/pos/products')
@login_required
def get_products():
    category_id = request.args.get('category_id')
    warehouse_id = request.args.get('warehouse_id')
    search = request.args.get('search', '')

    # البحث عن المنتجات النشطة
    query = Product.query.filter(Product.is_active == True)

    # تصفية حسب التصنيف
    if category_id and category_id.isdigit() and int(category_id) > 0:
        query = query.filter(Product.category_id == int(category_id))

    # تصفية حسب البحث
    if search:
        query = query.filter(
            Product.name.ilike(f'%{search}%') | Product.code.ilike(f'%{search}%')
        )

    # الحصول على المنتجات
    products = query.all()

    # إذا تم تحديد مخزن، قم بتصفية المنتجات حسب المخزن
    if warehouse_id and warehouse_id.isdigit() and int(warehouse_id) > 0:
        warehouse_id = int(warehouse_id)
        # الحصول على المنتجات المتوفرة في المخزن المحدد
        inventory_products = []
        for product in products:
            # البحث عن المنتج في المخزن المحدد
            inventory = Inventory.query.filter_by(
                product_id=product.id,
                warehouse_id=warehouse_id
            ).first()

            if inventory and inventory.quantity > 0:
                # تحديث كمية المخزون للمنتج
                product.stock_quantity = inventory.quantity
                inventory_products.append(product)

        # استبدال قائمة المنتجات بالمنتجات المتوفرة في المخزن
        products = inventory_products

    # Prepare product data with stock percentage
    product_data = []
    for p in products:
        # Calculate stock percentage based on min_stock and max_stock if available
        stock_percentage = None
        if hasattr(p, 'min_stock') and hasattr(p, 'max_stock') and p.min_stock is not None and p.max_stock is not None and p.max_stock > p.min_stock:
            stock_range = p.max_stock - p.min_stock
            if stock_range > 0:
                current_stock = max(0, p.stock_quantity - p.min_stock)
                stock_percentage = min(100, int((current_stock / stock_range) * 100))

        # If min_stock/max_stock not available, calculate based on a simple percentage of current stock
        if stock_percentage is None and p.stock_quantity is not None:
            if hasattr(p, 'min_stock') and p.min_stock is not None:
                min_stock = p.min_stock
            else:
                min_stock = 0

            if hasattr(p, 'max_stock') and p.max_stock is not None and p.max_stock > 0:
                max_stock = p.max_stock
            else:
                # Default max stock (if not set) is 3 times the current stock or at least 10
                max_stock = max(10, p.stock_quantity * 3)

            if max_stock > min_stock:
                current_stock = max(0, p.stock_quantity - min_stock)
                stock_range = max_stock - min_stock
                stock_percentage = min(100, int((current_stock / stock_range) * 100))

        # Get product image if available
        image = None
        if hasattr(p, 'image') and p.image:
            image = p.image

        product_data.append({
            'id': p.id,
            'name': p.name,
            'brand': p.brand if hasattr(p, 'brand') else None,
            'price': p.price,
            'stock_quantity': p.stock_quantity,
            'stock_status': p.get_stock_status(),
            'stock_percentage': stock_percentage,
            'category_id': p.category_id,
            'image': image,
            'code': p.code if hasattr(p, 'code') else None
        })

    return jsonify({
        'products': product_data
    })

@pos_blueprint.route('/api/pos/process-sale', methods=['POST'])
@login_required
def process_sale():
    try:
        # Log the start of the process
        print("Starting process_sale function")

        # Get JSON data
        data = request.json
        if not data:
            print("Error: No JSON data received")
            return jsonify({
                'success': False,
                'message': 'لم يتم استلام بيانات صالحة'
            }), 400

        # Log the received data
        print(f"Received data: {data}")

        # Get data from request
        invoice_number = data.get('invoice_number')
        if not invoice_number:
            print("Error: Missing invoice_number")
            return jsonify({
                'success': False,
                'message': 'رقم الفاتورة مفقود'
            }), 400

        customer_id = data.get('customer_id')
        warehouse_id = data.get('warehouse_id')
        items = data.get('items', [])

        if not items or len(items) == 0:
            print("Error: No items in the cart")
            return jsonify({
                'success': False,
                'message': 'لا توجد منتجات في السلة'
            }), 400

        subtotal = data.get('subtotal', 0)
        discount = data.get('discount', 0)
        discount_type = data.get('discount_type', 'fixed')
        tax_percentage = data.get('tax_percentage', 0)
        tax = data.get('tax', 0)
        total = data.get('total', 0)
        payment_method = data.get('payment_method', 'cash')

        # Log the extracted data
        print(f"Extracted data: invoice={invoice_number}, customer={customer_id}, items_count={len(items)}, total={total}")

        # Convert customer_id to int or None
        if customer_id and customer_id.isdigit():
            customer_id = int(customer_id)
        else:
            customer_id = None

        # Convert warehouse_id to int or get default warehouse
        if warehouse_id and warehouse_id.isdigit():
            warehouse_id = int(warehouse_id)
        else:
            # الحصول على المخزن الافتراضي
            default_warehouse = Warehouse.query.filter_by(is_default=True).first()
            if default_warehouse:
                warehouse_id = default_warehouse.id
            else:
                # إذا لم يكن هناك مخزن افتراضي، استخدم أول مخزن
                first_warehouse = Warehouse.query.first()
                warehouse_id = first_warehouse.id if first_warehouse else None

        # Create order
        try:
            print("Creating order")
            # تحديد حالة الطلب بناءً على طريقة الدفع
            status = 'completed'
            if payment_method == 'deferred':
                status = 'deferred'

            order = Order(
                invoice_number=invoice_number,
                # لا يتم تعيين الباركود حالياً لأن العمود غير موجود
                customer_id=customer_id,
                user_id=current_user.id,
                warehouse_id=warehouse_id,  # تعيين المخزن المستخدم
                subtotal=float(subtotal),
                discount=float(discount),
                discount_type=discount_type,
                tax_percentage=float(tax_percentage),
                tax=float(tax),
                total=float(total),
                payment_method=payment_method,
                status=status,
                created_at=datetime.utcnow()
            )

            db.session.add(order)
            # Flush to get the order ID
            db.session.flush()
            print(f"Order created with ID: {order.id}")
        except Exception as e:
            print(f"Error creating order: {str(e)}")
            raise ValueError(f"فشل في إنشاء الطلب: {str(e)}")

        # Create order items and update stock
        print("Processing order items")
        for index, item in enumerate(items):
            try:
                print(f"Processing item {index+1}/{len(items)}")

                # Get item data
                # Try to get product_id from either 'id' or 'product_id' field
                product_id = item.get('id') or item.get('product_id')
                quantity = item.get('quantity', 0)
                price = item.get('price', 0)
                item_total = item.get('total', 0)

                print(f"Item data: product_id={product_id}, quantity={quantity}, price={price}, total={item_total}")
                print(f"Raw item data: {item}")

                # Validate product_id
                if not product_id:
                    print(f"Error: Invalid product_id for item {index+1}")
                    raise ValueError(f"معرف المنتج غير صالح أو مفقود في العنصر {index+1}")

                # Convert product_id to int if it's a string
                if isinstance(product_id, str) and product_id.isdigit():
                    product_id = int(product_id)

                # Check if product exists
                product = Product.query.get(product_id)
                if not product:
                    print(f"Error: Product with ID {product_id} not found")
                    raise ValueError(f"المنتج برقم {product_id} غير موجود")

                print(f"Found product: {product.name}")

                # Add order item
                try:
                    order_item = OrderItem(
                        order=order,
                        product_id=product_id,
                        quantity=quantity,
                        price=float(price),
                        total=float(item_total)
                    )
                    db.session.add(order_item)
                    db.session.flush()  # Flush to catch any database errors
                    print(f"Order item added with ID: {order_item.id}")
                except Exception as e:
                    print(f"Error adding order item: {str(e)}")
                    raise ValueError(f"فشل في إضافة عنصر الطلب: {str(e)}")

                # Update product stock
                try:
                    # تحديث حقل stock_quantity في المنتج للتوافق مع الكود القديم
                    product.stock_quantity = max(0, product.stock_quantity - quantity)
                    print(f"Updated product stock: {product.stock_quantity}")

                    # تحديث المخزون في المخزن المحدد
                    if warehouse_id:
                        # البحث عن المخزن
                        warehouse = Warehouse.query.get(warehouse_id)
                        if warehouse:
                            print(f"Using warehouse: {warehouse.name}")
                            # البحث عن المنتج في المخزن
                            inventory = Inventory.query.filter_by(
                                warehouse_id=warehouse_id,
                                product_id=product_id
                            ).first()

                        if inventory:
                            print(f"Found inventory record with quantity: {inventory.quantity}")
                            # تحديث كمية المخزون وتسجيل الحركة
                            new_quantity = max(0, inventory.quantity - quantity)
                            inventory.update_quantity(
                                new_quantity=new_quantity,
                                movement_type='remove',
                                user_id=current_user.id,
                                reference=f'sale_{invoice_number}',
                                notes=f'تم البيع من خلال نقطة البيع. رقم الفاتورة: {invoice_number}'
                            )
                            print(f"Updated inventory quantity to: {new_quantity}")

                            # إنشاء إشعار للمستخدمين إذا وصل المخزون للحد الأدنى أو نفذ
                            if new_quantity <= 0:
                                Notification.create_inventory_notification(inventory, 'out_of_stock')
                                print("Created out_of_stock notification")
                except Exception as e:
                    print(f"Error updating inventory: {str(e)}")
                    # Continue processing other items even if inventory update fails
                    # But log the error
                    print(f"Warning: Failed to update inventory for product {product_id}: {str(e)}")

            except Exception as e:
                print(f"Error processing item {index+1}: {str(e)}")
                raise ValueError(f"فشل في معالجة العنصر {index+1}: {str(e)}")

        # إضافة دفعة للطلب
        try:
            # الحصول على طريقة الدفع من قاعدة البيانات
            payment_method_obj = PaymentMethod.query.filter_by(code=payment_method).first()

            # إذا كانت طريقة الدفع غير آجلة وحالة الطلب مكتملة، نضيف دفعة
            if payment_method_obj and not payment_method_obj.is_credit and status == 'completed':
                # إنشاء رقم مرجعي للدفعة
                from models import Payment
                reference_number = Payment.generate_reference_number()

                # الحصول على ملاحظات الدفع
                payment_notes = f'دفعة تلقائية من نقطة البيع - {invoice_number}'

                # إضافة تفاصيل مرجعية إذا كانت طريقة الدفع تتطلب ذلك
                if payment_method_obj.requires_reference and 'payment_details' in data:
                    payment_details = data.get('payment_details', {})
                    card_number = payment_details.get('card_number', '')
                    card_holder = payment_details.get('card_holder', '')
                    card_expiry = payment_details.get('card_expiry', '')

                    if card_number:
                        payment_notes += f' | رقم المرجع: **** {card_number}'
                    if card_holder:
                        payment_notes += f' | الاسم: {card_holder}'
                    if card_expiry:
                        payment_notes += f' | التاريخ: {card_expiry}'

                # إنشاء الدفعة
                payment = Payment(
                    reference_number=reference_number,
                    amount=float(total),
                    payment_method=payment_method,
                    payment_date=datetime.utcnow(),
                    notes=payment_notes,
                    created_by=current_user.id,
                    order_id=order.id
                )

                db.session.add(payment)
                print(f"Added payment with reference: {reference_number}")

            # Commit the transaction
            print("Committing transaction")
            db.session.commit()
            print("Transaction committed successfully")

            return jsonify({
                'success': True,
                'order_id': order.id,
                'invoice_number': order.invoice_number,
                'message': 'تم إتمام البيع بنجاح'
            })
        except Exception as e:
            print(f"Error committing transaction: {str(e)}")
            db.session.rollback()
            return jsonify({
                'success': False,
                'message': f'فشل في حفظ البيانات: {str(e)}'
            }), 500

    except Exception as e:
        print(f"Error in process_sale: {str(e)}")
        # Make sure to rollback the session
        try:
            db.session.rollback()
            print("Session rolled back")
        except Exception as rollback_error:
            print(f"Error during rollback: {str(rollback_error)}")

        # Return detailed error message
        error_message = str(e)
        print(f"Returning error: {error_message}")
        return jsonify({
            'success': False,
            'message': f'حدث خطأ أثناء معالجة البيع: {error_message}'
        }), 500

@pos_blueprint.route('/pos/receipt/<int:order_id>')
@login_required
def receipt(order_id):
    order = Order.query.get_or_404(order_id)

    # Generate barcode
    barcode_image = None
    try:
        from io import BytesIO
        import base64
        from barcode import Code128
        from barcode.writer import ImageWriter

        # Generate barcode
        buffer = BytesIO()
        Code128(order.invoice_number, writer=ImageWriter()).write(buffer)
        buffer.seek(0)
        barcode_image = base64.b64encode(buffer.read()).decode('utf-8')
    except Exception as e:
        print(f"Error generating barcode: {str(e)}")

    # Load settings from the settings module
    from routes.settings import load_settings
    settings = load_settings()

    return render_template(
        'pos/receipt.html',
        order=order,
        current_user=current_user,
        barcode_image=barcode_image,
        settings=settings
    )

@pos_blueprint.route('/pos/invoice/<int:order_id>')
@login_required
def invoice(order_id):
    order = Order.query.get_or_404(order_id)

    # Generate barcode
    barcode_image = None
    try:
        from io import BytesIO
        import base64
        from barcode import Code128
        from barcode.writer import ImageWriter

        # Generate barcode
        buffer = BytesIO()
        Code128(order.invoice_number, writer=ImageWriter()).write(buffer)
        buffer.seek(0)
        barcode_image = base64.b64encode(buffer.read()).decode('utf-8')
    except Exception as e:
        print(f"Error generating barcode: {str(e)}")

    # Load settings from the settings module
    from routes.settings import load_settings
    settings = load_settings()

    # Check receipt size to determine which template to use
    receipt_size = settings.get('receipt', {}).get('receipt_size', '80mm')
    if receipt_size in ['a4', 'a5']:
        template = 'pos/invoice_a4.html'
    else:
        template = 'pos/invoice.html'

    return render_template(
        template,
        order=order,
        current_user=current_user,
        barcode_image=barcode_image,
        settings=settings
    )

@pos_blueprint.route('/pos/invoice_a4/<int:order_id>')
@login_required
def invoice_a4(order_id):
    """طباعة فاتورة بحجم A4"""
    order = Order.query.get_or_404(order_id)

    # Generate barcode
    barcode_image = None
    try:
        from io import BytesIO
        import base64
        from barcode import Code128
        from barcode.writer import ImageWriter

        # Generate barcode
        buffer = BytesIO()
        Code128(order.invoice_number, writer=ImageWriter()).write(buffer)
        buffer.seek(0)
        barcode_image = base64.b64encode(buffer.read()).decode('utf-8')
    except Exception as e:
        print(f"Error generating barcode: {str(e)}")

    # Load settings from the settings module
    from routes.settings import load_settings
    settings = load_settings()

    # Force A4 size
    if 'receipt' not in settings:
        settings['receipt'] = {}
    settings['receipt']['receipt_size'] = 'a4'

    return render_template(
        'pos/invoice.html',
        order=order,
        current_user=current_user,
        barcode_image=barcode_image,
        settings=settings
    )

@pos_blueprint.route('/pos/invoice_a5/<int:order_id>')
@login_required
def invoice_a5(order_id):
    """طباعة فاتورة بحجم A5"""
    order = Order.query.get_or_404(order_id)

    # Generate barcode
    barcode_image = None
    try:
        from io import BytesIO
        import base64
        from barcode import Code128
        from barcode.writer import ImageWriter

        # Generate barcode
        buffer = BytesIO()
        Code128(order.invoice_number, writer=ImageWriter()).write(buffer)
        buffer.seek(0)
        barcode_image = base64.b64encode(buffer.read()).decode('utf-8')
    except Exception as e:
        print(f"Error generating barcode: {str(e)}")

    # Load settings from the settings module
    from routes.settings import load_settings
    settings = load_settings()

    # Force A5 size
    if 'receipt' not in settings:
        settings['receipt'] = {}
    settings['receipt']['receipt_size'] = 'a5'

    return render_template(
        'pos/invoice.html',
        order=order,
        current_user=current_user,
        barcode_image=barcode_image,
        settings=settings
    )

@pos_blueprint.route('/pos_customers')
@login_required
def pos_customers():
    customers = Customer.query.order_by(Customer.name).all()
    return render_template('pos_customers.html', customers=customers, current_user=current_user)

@pos_blueprint.route('/customers/create', methods=['GET', 'POST'])
@login_required
def create_customer():
    if request.method == 'POST':
        try:
            name = request.form.get('name')
            phone = request.form.get('phone')
            email = request.form.get('email')
            address = request.form.get('address')

            customer = Customer(
                name=name,
                phone=phone,
                email=email,
                address=address
            )

            db.session.add(customer)
            db.session.commit()

            flash('تم إضافة العميل بنجاح', 'success')

            # Check if we need to return to POS
            if 'from_pos' in request.form:
                return redirect(url_for('pos.index'))
            return redirect(url_for('pos.pos_customers'))

        except Exception as e:
            db.session.rollback()
            flash(f'حدث خطأ أثناء إضافة العميل: {str(e)}', 'danger')

    # Check if request is from POS
    from_pos = 'from_pos' in request.args

    return render_template('customer_form.html', customer=None, action='create', from_pos=from_pos)

@pos_blueprint.route('/api/customers')
@login_required
def api_customers():
    customers = Customer.query.order_by(Customer.name).all()
    return jsonify({
        'customers': [customer.to_dict() for customer in customers]
    })

@pos_blueprint.route('/api/customers/search')
@login_required
def search_customers():
    query = request.args.get('q', '')
    # Return all customers if query is empty
    if not query:
        customers = Customer.query.order_by(Customer.name).limit(20).all()
    else:
        # Search by name, phone, or email with just one character
        customers = Customer.query.filter(
            Customer.name.ilike(f'%{query}%') |
            Customer.phone.ilike(f'%{query}%') |
            Customer.email.ilike(f'%{query}%')
        ).order_by(Customer.name).limit(20).all()

    return jsonify({
        'customers': [
            {
                'id': c.id,
                'name': c.name,
                'phone': c.phone,
                'email': c.email,
                'address': c.address
            } for c in customers
        ]
    })

@pos_blueprint.route('/api/customers/add', methods=['POST'])
@login_required
def add_customer_api():
    try:
        name = request.form.get('name')
        phone = request.form.get('phone')
        email = request.form.get('email')
        address = request.form.get('address')

        if not name:
            return jsonify({'success': False, 'message': 'اسم العميل مطلوب'})

        customer = Customer(
            name=name,
            phone=phone,
            email=email,
            address=address
        )

        db.session.add(customer)
        db.session.commit()

        return jsonify({
            'success': True,
            'message': 'تم إضافة العميل بنجاح',
            'customer': {
                'id': customer.id,
                'name': customer.name,
                'phone': customer.phone,
                'email': customer.email,
                'address': customer.address
            }
        })
    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'message': str(e)})

@pos_blueprint.route('/api/pos/open-drawer', methods=['POST'])
@login_required
def api_open_drawer():
    try:
        success = open_cash_drawer()
        if success:
            return jsonify({'success': True})
        else:
            return jsonify({'success': False, 'message': 'فشل في فتح الدرج النقدي'})
    except Exception as e:
        return jsonify({'success': False, 'message': str(e)})

@pos_blueprint.route('/api/customers/<int:customer_id>/debt')
@login_required
def get_customer_debt(customer_id):
    """Get customer debt information"""
    try:
        # Get customer
        customer = Customer.query.get_or_404(customer_id)

        # Get all deferred orders for this customer
        deferred_orders = Order.query.filter(
            Order.customer_id == customer_id,
            (Order.status == 'deferred') | (Order.payment_method == 'deferred')
        ).all()

        # Calculate total debt
        total_debt = 0
        for order in deferred_orders:
            # حساب المبلغ المدفوع
            paid_amount = sum(payment.amount for payment in order.payments) if hasattr(order, 'payments') else 0
            # حساب المبلغ المتبقي
            remaining_amount = order.total - paid_amount
            total_debt += remaining_amount

        # Prepare debt details
        debt_details = []
        for order in deferred_orders:
            # حساب المبلغ المدفوع
            paid_amount = sum(payment.amount for payment in order.payments) if hasattr(order, 'payments') else 0
            # حساب المبلغ المتبقي
            remaining_amount = order.total - paid_amount

            # إضافة التفاصيل فقط إذا كان هناك مبلغ متبقي
            if remaining_amount > 0:
                debt_details.append({
                    'invoice_number': order.invoice_number,
                    'date': order.created_at.strftime('%Y-%m-%d'),
                    'total': order.total,
                    'paid': paid_amount,
                    'remaining': remaining_amount
                })

        return jsonify({
            'success': True,
            'customer_id': customer_id,
            'customer_name': customer.name,
            'total_debt': total_debt,
            'debt_details': debt_details
        })

    except Exception as e:
        print(f"Error getting customer debt: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'حدث خطأ أثناء جلب بيانات المديونية: {str(e)}'
        }), 500

@pos_blueprint.route('/api/sales/invoice_search')
@login_required
def search_invoice():
    """البحث عن فاتورة باستخدام رقم الفاتورة أو جزء منه"""
    invoice_number = request.args.get('invoice_number', '')

    if not invoice_number:
        return jsonify({
            'success': False,
            'message': 'يرجى إدخال رقم الفاتورة'
        }), 400

    try:
        # البحث عن الفاتورة باستخدام رقم الفاتورة أو جزء منه
        orders = Order.query.filter(Order.invoice_number.ilike(f'%{invoice_number}%')).order_by(Order.created_at.desc()).limit(10).all()

        if not orders:
            return jsonify({
                'success': False,
                'message': 'لم يتم العثور على فواتير مطابقة'
            }), 404

        # إذا كان هناك فاتورة واحدة فقط، قم بإرجاع تفاصيلها
        if len(orders) == 1:
            order = orders[0]
            # الحصول على عناصر الفاتورة
            order_items = OrderItem.query.filter_by(order_id=order.id).all()

            # الحصول على اسم العميل إذا كان متاحًا
            customer_name = None
            if order.customer_id:
                customer = Customer.query.get(order.customer_id)
                if customer:
                    customer_name = customer.name

            # تحضير بيانات الفاتورة
            invoice_data = {
                'id': order.id,
                'invoice_number': order.invoice_number,
                'created_at': order.created_at.strftime('%Y-%m-%d %H:%M:%S'),
                'total': order.total,
                'subtotal': order.subtotal,
                'discount': order.discount,
                'tax': order.tax,
                'status': order.status,
                'payment_method': order.payment_method,
                'customer_id': order.customer_id,
                'customer_name': customer_name,
                'warehouse_id': order.warehouse_id,
                'warehouse_name': order.warehouse.name if order.warehouse else None
            }

            # تحضير بيانات العناصر
            items_data = []
            for item in order_items:
                product = Product.query.get(item.product_id)
                items_data.append({
                    'id': item.id,
                    'product_id': item.product_id,
                    'product_name': product.name if product else 'منتج غير معروف',
                    'price': item.price,
                    'quantity': item.quantity,
                    'returned_quantity': item.returned_quantity if hasattr(item, 'returned_quantity') else 0,
                    'discount': item.discount,
                    'total': item.total
                })

            return jsonify({
                'success': True,
                'invoice': invoice_data,
                'items': items_data
            })

        # إذا كان هناك أكثر من فاتورة، قم بإرجاع قائمة مختصرة
        invoices_list = []
        for order in orders:
            customer_name = None
            if order.customer_id:
                customer = Customer.query.get(order.customer_id)
                if customer:
                    customer_name = customer.name

            invoices_list.append({
                'id': order.id,
                'invoice_number': order.invoice_number,
                'created_at': order.created_at.strftime('%Y-%m-%d %H:%M:%S'),
                'total': order.total,
                'customer_name': customer_name,
                'status': order.status
            })

        return jsonify({
            'success': True,
            'multiple_results': True,
            'invoices': invoices_list
        })

    except Exception as e:
        print(f"Error searching for invoice: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'حدث خطأ أثناء البحث عن الفاتورة: {str(e)}'
        }), 500

@pos_blueprint.route('/api/pos/invoice/<invoice_number>')
@login_required
def get_invoice_by_number(invoice_number):
    """Get invoice by invoice number"""
    try:
        # Find order by invoice number
        order = Order.query.filter_by(invoice_number=invoice_number).first()

        if not order:
            return jsonify({
                'success': False,
                'message': 'لم يتم العثور على الفاتورة'
            }), 404

        # Get order items
        order_items = OrderItem.query.filter_by(order_id=order.id).all()

        # Get customer name if available
        customer_name = None
        if order.customer_id:
            customer = Customer.query.get(order.customer_id)
            if customer:
                customer_name = customer.name

        # Prepare invoice data
        invoice_data = {
            'id': order.id,
            'invoice_number': order.invoice_number,
            'created_at': order.created_at.strftime('%Y-%m-%d %H:%M:%S'),
            'total': order.total,
            'subtotal': order.subtotal,
            'discount': order.discount,
            'tax': order.tax,
            'status': order.status,
            'payment_method': order.payment_method,
            'customer_id': order.customer_id,
            'customer_name': customer_name,
            'warehouse_id': order.warehouse_id,
            'warehouse_name': order.warehouse.name if order.warehouse else None
        }

        # Prepare items data
        items_data = []
        for item in order_items:
            product = Product.query.get(item.product_id)
            items_data.append({
                'id': item.id,
                'product_id': item.product_id,
                'product_name': product.name if product else 'منتج غير معروف',
                'price': item.price,
                'quantity': item.quantity,
                'returned_quantity': item.returned_quantity if hasattr(item, 'returned_quantity') else 0,
                'available_quantity': item.quantity - (item.returned_quantity if hasattr(item, 'returned_quantity') else 0),
                'discount': item.discount,
                'total': item.total
            })

        return jsonify({
            'success': True,
            'invoice': invoice_data,
            'items': items_data
        })

    except Exception as e:
        print(f"Error getting invoice: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'حدث خطأ أثناء جلب بيانات الفاتورة: {str(e)}'
        }), 500