<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>FoUaD - {{ 'إضافة منتج جديد' if action == 'create' else 'تعديل المنتج' }}</title>
    <script src="https://cdn.tailwindcss.com/3.4.16"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#6366F1',
                        secondary: '#10b981'
                    },
                    borderRadius: {
                        'none': '0px',
                        'sm': '4px',
                        DEFAULT: '8px',
                        'md': '12px',
                        'lg': '16px',
                        'xl': '20px',
                        '2xl': '24px',
                        '3xl': '32px',
                        'full': '9999px',
                        'button': '8px'
                    }
                }
            }
        }
    </script>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@400;500;700&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Pacifico&display=swap" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/remixicon@3.5.0/fonts/remixicon.css" rel="stylesheet">
    <style>
        :where([class^="ri-"])::before { content: "\f3c2"; }
        body {
            font-family: 'Tajawal', sans-serif;
            background-color: #f9fafb;
        }
        .scrollbar-hide::-webkit-scrollbar {
            display: none;
        }
        .scrollbar-hide {
            -ms-overflow-style: none;
            scrollbar-width: none;
        }
        input[type="number"]::-webkit-inner-spin-button,
        input[type="number"]::-webkit-outer-spin-button {
            -webkit-appearance: none;
            margin: 0;
        }
        input[type="number"] {
            -moz-appearance: textfield;
        }
        .bg-pattern {
            background-image: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%236366F1' fill-opacity='0.05'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
        }
        .custom-switch {
            position: relative;
            display: inline-block;
            width: 44px;
            height: 24px;
        }
        .custom-switch input {
            opacity: 0;
            width: 0;
            height: 0;
        }
        .slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: #e2e8f0;
            transition: .4s;
            border-radius: 24px;
        }
        .slider:before {
            position: absolute;
            content: "";
            height: 18px;
            width: 18px;
            right: 3px;
            bottom: 3px;
            background-color: white;
            transition: .4s;
            border-radius: 50%;
        }
        input:checked + .slider {
            background-color: #6366F1;
        }
        input:checked + .slider:before {
            transform: translateX(-20px);
        }
    </style>
</head>
<body class="bg-pattern">
    <div class="flex h-screen overflow-hidden">
        <!-- Sidebar -->
        {% include 'partials/sidebar.html' %}

        <!-- Main Content -->
        <div class="flex-1 flex flex-col overflow-hidden">
            <!-- Top Navigation -->
            {% include 'partials/topnav.html' %}

            <!-- Content Area -->
            <main class="flex-1 overflow-y-auto p-6">
                <div class="mb-6 flex flex-wrap items-center justify-between">
                    <div>
                        <h1 class="text-2xl font-bold text-gray-800">{{ 'إضافة منتج جديد' if action == 'create' else 'تعديل المنتج' }}</h1>
                        <p class="text-gray-600">{{ 'أدخل بيانات المنتج الجديد' if action == 'create' else 'قم بتعديل بيانات المنتج' }}</p>
                    </div>
                    <a href="{{ url_for('products.index') }}" class="bg-gray-100 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-200 transition-all duration-300 flex items-center gap-2 whitespace-nowrap text-sm">
                        <div class="w-5 h-5 flex items-center justify-center">
                            <i class="ri-arrow-right-line"></i>
                        </div>
                        <span>العودة للمنتجات</span>
                    </a>
                </div>

                <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-100">
                    <form method="POST" action="{{ url_for('products.create') if action == 'create' else url_for('products.edit', id=product.id) }}" enctype="multipart/form-data">
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <!-- Product Info -->
                            <div class="space-y-4">
                                <h3 class="font-medium text-gray-700 border-b pb-2">معلومات المنتج</h3>

                                <div>
                                    <label for="name" class="block text-sm font-medium text-gray-700 mb-1">اسم المنتج <span class="text-red-500">*</span></label>
                                    <input type="text" id="name" name="name" value="{{ product.name or '' }}" required class="w-full px-4 py-2 bg-gray-50 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:bg-white transition-all">
                                </div>

                                <div>
                                    <label for="code" class="block text-sm font-medium text-gray-700 mb-1">كود المنتج</label>
                                    <input type="text" id="code" name="code" value="{{ product.code or '' }}" placeholder="كود اختياري أو رقم الباركود" class="w-full px-4 py-2 bg-gray-50 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:bg-white transition-all">
                                </div>

                                <div>
                                    <label for="brand" class="block text-sm font-medium text-gray-700 mb-1">العلامة التجارية</label>
                                    <input type="text" id="brand" name="brand" value="{{ product.brand or '' }}" class="w-full px-4 py-2 bg-gray-50 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:bg-white transition-all">
                                </div>

                                <div>
                                    <label for="category_id" class="block text-sm font-medium text-gray-700 mb-1">التصنيف</label>
                                    <select id="category_id" name="category_id" class="w-full px-4 py-2 bg-gray-50 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:bg-white transition-all">
                                        <option value="">-- اختر تصنيف --</option>
                                        {% for category in categories %}
                                        <option value="{{ category.id }}" {% if product and product.category_id == category.id %}selected{% endif %}>{{ category.name }}</option>
                                        {% endfor %}
                                    </select>
                                </div>

                                <div>
                                    <label for="supplier_id" class="block text-sm font-medium text-gray-700 mb-1">المورد</label>
                                    <select id="supplier_id" name="supplier_id" class="w-full px-4 py-2 bg-gray-50 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:bg-white transition-all">
                                        <option value="">-- اختر مورد --</option>
                                        {% for supplier in suppliers %}
                                        <option value="{{ supplier.id }}" {% if product and product.supplier_id == supplier.id %}selected{% endif %}>{{ supplier.name }}</option>
                                        {% endfor %}
                                    </select>
                                </div>

                                <div>
                                    <label for="description" class="block text-sm font-medium text-gray-700 mb-1">وصف المنتج</label>
                                    <textarea id="description" name="description" rows="4" class="w-full px-4 py-2 bg-gray-50 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:bg-white transition-all">{{ product.description or '' }}</textarea>
                                </div>
                            </div>

                            <!-- Pricing and Stock -->
                            <div class="space-y-4">
                                <h3 class="font-medium text-gray-700 border-b pb-2">السعر والمخزون</h3>

                                <div class="grid grid-cols-2 gap-4">
                                    <div>
                                        <label for="price" class="block text-sm font-medium text-gray-700 mb-1">سعر البيع <span class="text-red-500">*</span></label>
                                        <div class="relative">
                                            <input type="number" id="price" name="price" value="{{ product.price or 0 }}" step="0.01" min="0" required class="w-full pl-12 pr-4 py-2 bg-gray-50 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:bg-white transition-all">
                                            <div class="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                                                <span class="text-gray-500">ج.م</span>
                                            </div>
                                        </div>
                                    </div>

                                    <div>
                                        <label for="cost_price" class="block text-sm font-medium text-gray-700 mb-1">سعر التكلفة</label>
                                        <div class="relative">
                                            <input type="number" id="cost_price" name="cost_price" value="{{ product.cost_price or 0 }}" step="0.01" min="0" class="w-full pl-12 pr-4 py-2 bg-gray-50 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:bg-white transition-all">
                                            <div class="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                                                <span class="text-gray-500">ج.م</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="grid grid-cols-2 gap-4">
                                    <div>
                                        <label for="stock_quantity" class="block text-sm font-medium text-gray-700 mb-1">الكمية المتاحة</label>
                                        <input type="number" id="stock_quantity" name="stock_quantity" value="{{ product.stock_quantity or 0 }}" min="0" class="w-full px-4 py-2 bg-gray-50 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:bg-white transition-all">
                                    </div>

                                    <div>
                                        <label for="minimum_stock" class="block text-sm font-medium text-gray-700 mb-1">الحد الأدنى للمخزون</label>
                                        <input type="number" id="minimum_stock" name="minimum_stock" value="{{ product.minimum_stock or 5 }}" min="0" class="w-full px-4 py-2 bg-gray-50 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:bg-white transition-all">
                                    </div>
                                </div>

                                <!-- إضافة قسم المخازن -->
                                <div class="mt-4 border-t pt-4">
                                    <h4 class="font-medium text-gray-700 mb-2">إدارة المخزون</h4>

                                    <div class="grid grid-cols-2 gap-4 mb-2">
                                        <div>
                                            <label for="warehouse_id" class="block text-sm font-medium text-gray-700 mb-1">المخزن</label>
                                            <select id="warehouse_id" name="warehouse_id" class="w-full px-4 py-2 bg-gray-50 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:bg-white transition-all">
                                                <option value="">-- اختر المخزن --</option>
                                                {% for warehouse in warehouses %}
                                                <option value="{{ warehouse.id }}">{{ warehouse.name }}</option>
                                                {% endfor %}
                                            </select>
                                        </div>
                                    </div>

                                    <div class="mb-2">
                                        <label class="flex items-center cursor-pointer">
                                            <input type="checkbox" name="{{ 'add_to_inventory' if action == 'create' else 'update_inventory' }}" class="form-checkbox h-5 w-5 text-primary">
                                            <span class="mr-2 text-sm font-medium text-gray-700">
                                                {% if action == 'create' %}
                                                إضافة المنتج للمخزن المحدد
                                                {% else %}
                                                تحديث المخزون في المخزن المحدد
                                                {% endif %}
                                            </span>
                                        </label>
                                        <p class="text-xs text-gray-500 mt-1">
                                            {% if action == 'create' %}
                                            سيتم إضافة المنتج للمخزن المحدد بالكمية المدخلة أعلاه
                                            {% else %}
                                            سيتم تحديث كمية المنتج في المخزن المحدد
                                            {% endif %}
                                        </p>
                                    </div>

                                    {% if action == 'edit' and inventories %}
                                    <div class="mt-4">
                                        <h5 class="text-sm font-medium text-gray-700 mb-2">المخزون الحالي في المخازن:</h5>
                                        <div class="bg-gray-50 p-3 rounded-md">
                                            <ul class="space-y-1">
                                                {% for inventory in inventories %}
                                                <li class="text-sm">
                                                    <span class="font-medium">{{ inventory.warehouse.name }}:</span>
                                                    <span class="{% if inventory.quantity <= 0 %}text-red-600{% elif inventory.quantity <= inventory.minimum_stock %}text-yellow-600{% else %}text-green-600{% endif %}">
                                                        {{ inventory.quantity }} قطعة
                                                    </span>
                                                </li>
                                                {% endfor %}
                                            </ul>
                                        </div>
                                    </div>
                                    {% endif %}
                                </div>

                                <div>
                                    <label for="image" class="block text-sm font-medium text-gray-700 mb-1">صورة المنتج</label>
                                    <input type="file" id="image" name="image" accept="image/*" class="w-full py-2 text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-primary file:text-white hover:file:bg-indigo-700 cursor-pointer">
                                    {% if product and product.image_path %}
                                    <div class="mt-2 flex items-center space-x-2 space-x-reverse">
                                        <img src="{{ product.image_path }}" alt="{{ product.name }}" class="h-10 w-10 rounded object-cover">
                                        <span class="text-sm text-gray-500">الصورة الحالية</span>
                                    </div>
                                    {% endif %}
                                </div>

                                <div class="mt-4">
                                    <label class="flex items-center cursor-pointer">
                                        <span class="mr-2 text-sm font-medium text-gray-700">منتج نشط</span>
                                        <label class="custom-switch">
                                            <input type="checkbox" name="is_active" {% if product is none or product.is_active %}checked{% endif %}>
                                            <span class="slider"></span>
                                        </label>
                                    </label>
                                    <p class="text-xs text-gray-500 mt-1">المنتجات النشطة فقط هي التي تظهر في نقاط البيع</p>
                                </div>
                            </div>
                        </div>

                        <div class="mt-8 pt-4 border-t border-gray-100 flex justify-between">
                            <a href="{{ url_for('products.index') }}" class="px-4 py-2 bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200 transition-all">
                                إلغاء
                            </a>
                            <button type="submit" class="px-6 py-2 bg-primary text-white rounded-md hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary transition-all font-medium">
                                {{ 'إضافة المنتج' if action == 'create' else 'حفظ التغييرات' }}
                            </button>
                        </div>
                    </form>
                </div>
            </main>
        </div>
    </div>

    <script>
        // Preview image before upload
        document.getElementById('image').addEventListener('change', function(event) {
            const file = event.target.files[0];
            if (file) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    // Create or update image preview
                    let preview = document.querySelector('.image-preview');
                    if (!preview) {
                        preview = document.createElement('div');
                        preview.className = 'mt-2 image-preview';
                        event.target.parentNode.appendChild(preview);
                    }

                    preview.innerHTML = `
                        <div class="mt-2 flex items-center space-x-2 space-x-reverse">
                            <img src="${e.target.result}" alt="Preview" class="h-20 w-20 rounded object-cover">
                            <span class="text-sm text-gray-500">معاينة الصورة الجديدة</span>
                        </div>
                    `;
                };
                reader.readAsDataURL(file);
            }
        });
    </script>
</body>
</html>