from flask import Blueprint, jsonify, request
from flask_login import login_required, current_user
from models import User, UserSession, db
from sqlalchemy import or_, and_
from datetime import datetime
import logging
import os
import secrets
import string
from werkzeug.utils import secure_filename

# إعداد التسجيل
logger = logging.getLogger("api.profile")
logger.setLevel(logging.INFO)

profile_api = Blueprint('profile_api', __name__)

@profile_api.route('/api/profile', methods=['GET'])
@login_required
def get_profile():
    """الحصول على معلومات الملف الشخصي للمستخدم الحالي"""
    try:
        return jsonify({
            'success': True,
            'profile': current_user.to_dict()
        })
    except Exception as e:
        logger.error(f"خطأ في الحصول على الملف الشخصي: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'حدث خطأ أثناء جلب الملف الشخصي: {str(e)}'
        }), 500

@profile_api.route('/api/profile', methods=['PUT'])
@login_required
def update_profile():
    """تحديث معلومات الملف الشخصي للمستخدم الحالي"""
    try:
        data = request.json
        
        # التحقق من البيانات المطلوبة
        if not data:
            return jsonify({
                'success': False,
                'message': 'لم يتم توفير بيانات للتحديث'
            }), 400
            
        # التحقق من وجود البريد الإلكتروني
        if 'email' in data and data['email'] != current_user.email:
            email_exists = User.query.filter(User.email == data['email'], User.id != current_user.id).first()
            if email_exists:
                return jsonify({
                    'success': False,
                    'message': 'البريد الإلكتروني موجود بالفعل'
                }), 400
                
        # تحديث بيانات المستخدم
        if 'full_name' in data:
            current_user.full_name = data['full_name']
        if 'email' in data:
            current_user.email = data['email']
        if 'phone' in data:
            current_user.phone = data['phone']
            
        db.session.commit()
        
        # تسجيل النشاط
        current_user.log_activity(
            activity_type='update_profile',
            description='تم تحديث الملف الشخصي',
            ip_address=request.remote_addr
        )
        db.session.commit()
        
        return jsonify({
            'success': True,
            'message': 'تم تحديث الملف الشخصي بنجاح',
            'profile': current_user.to_dict()
        })
    except Exception as e:
        db.session.rollback()
        logger.error(f"خطأ في تحديث الملف الشخصي: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'حدث خطأ أثناء تحديث الملف الشخصي: {str(e)}'
        }), 500

@profile_api.route('/api/profile/password', methods=['PUT'])
@login_required
def change_password():
    """تغيير كلمة المرور للمستخدم الحالي"""
    try:
        data = request.json
        
        # التحقق من البيانات المطلوبة
        if not data or 'current_password' not in data or 'new_password' not in data:
            return jsonify({
                'success': False,
                'message': 'يجب توفير كلمة المرور الحالية وكلمة المرور الجديدة'
            }), 400
            
        # التحقق من صحة كلمة المرور الحالية
        if not current_user.check_password(data['current_password']):
            return jsonify({
                'success': False,
                'message': 'كلمة المرور الحالية غير صحيحة'
            }), 400
            
        # تعيين كلمة المرور الجديدة
        current_user.set_password(data['new_password'])
        db.session.commit()
        
        # تسجيل النشاط
        current_user.log_activity(
            activity_type='change_password',
            description='تم تغيير كلمة المرور',
            ip_address=request.remote_addr
        )
        db.session.commit()
        
        return jsonify({
            'success': True,
            'message': 'تم تغيير كلمة المرور بنجاح'
        })
    except Exception as e:
        db.session.rollback()
        logger.error(f"خطأ في تغيير كلمة المرور: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'حدث خطأ أثناء تغيير كلمة المرور: {str(e)}'
        }), 500

@profile_api.route('/api/profile/sessions', methods=['GET'])
@login_required
def get_sessions():
    """الحصول على جلسات المستخدم النشطة"""
    try:
        # الحصول على جلسات المستخدم النشطة
        sessions = current_user.get_active_sessions()
        
        return jsonify({
            'success': True,
            'sessions': [session.to_dict() for session in sessions]
        })
    except Exception as e:
        logger.error(f"خطأ في الحصول على جلسات المستخدم: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'حدث خطأ أثناء جلب جلسات المستخدم: {str(e)}'
        }), 500

@profile_api.route('/api/profile/sessions/<int:session_id>', methods=['DELETE'])
@login_required
def invalidate_session(session_id):
    """إبطال جلسة محددة للمستخدم الحالي"""
    try:
        # البحث عن الجلسة
        session = UserSession.query.filter_by(id=session_id, user_id=current_user.id).first()
        
        if not session:
            return jsonify({
                'success': False,
                'message': 'الجلسة غير موجودة'
            }), 404
            
        # إبطال الجلسة
        session.invalidate()
        db.session.commit()
        
        # تسجيل النشاط
        current_user.log_activity(
            activity_type='invalidate_session',
            description=f'تم إبطال الجلسة {session.id}',
            ip_address=request.remote_addr
        )
        db.session.commit()
        
        return jsonify({
            'success': True,
            'message': 'تم إبطال الجلسة بنجاح'
        })
    except Exception as e:
        db.session.rollback()
        logger.error(f"خطأ في إبطال الجلسة: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'حدث خطأ أثناء إبطال الجلسة: {str(e)}'
        }), 500

@profile_api.route('/api/profile/sessions', methods=['DELETE'])
@login_required
def invalidate_all_sessions():
    """إبطال جميع جلسات المستخدم الحالي"""
    try:
        # إبطال جميع الجلسات
        current_user.invalidate_all_sessions()
        db.session.commit()
        
        # تسجيل النشاط
        current_user.log_activity(
            activity_type='invalidate_all_sessions',
            description='تم إبطال جميع الجلسات',
            ip_address=request.remote_addr
        )
        db.session.commit()
        
        return jsonify({
            'success': True,
            'message': 'تم إبطال جميع الجلسات بنجاح'
        })
    except Exception as e:
        db.session.rollback()
        logger.error(f"خطأ في إبطال جميع الجلسات: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'حدث خطأ أثناء إبطال جميع الجلسات: {str(e)}'
        }), 500

@profile_api.route('/api/reset-password/request', methods=['POST'])
def request_password_reset():
    """طلب إعادة تعيين كلمة المرور"""
    try:
        data = request.json
        
        # التحقق من البيانات المطلوبة
        if not data or 'email' not in data:
            return jsonify({
                'success': False,
                'message': 'يجب توفير البريد الإلكتروني'
            }), 400
            
        # البحث عن المستخدم
        user = User.query.filter_by(email=data['email']).first()
        
        if not user:
            return jsonify({
                'success': False,
                'message': 'البريد الإلكتروني غير مسجل في النظام'
            }), 404
            
        # إنشاء رمز إعادة تعيين كلمة المرور
        token = user.generate_reset_token()
        db.session.commit()
        
        # TODO: إرسال بريد إلكتروني برمز إعادة التعيين
        # هنا يمكن إضافة كود لإرسال بريد إلكتروني برمز إعادة التعيين
        
        return jsonify({
            'success': True,
            'message': 'تم إرسال رمز إعادة تعيين كلمة المرور إلى البريد الإلكتروني',
            'token': token  # في الإنتاج، لا ينبغي إرجاع الرمز في الاستجابة
        })
    except Exception as e:
        db.session.rollback()
        logger.error(f"خطأ في طلب إعادة تعيين كلمة المرور: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'حدث خطأ أثناء طلب إعادة تعيين كلمة المرور: {str(e)}'
        }), 500

@profile_api.route('/api/reset-password/verify', methods=['POST'])
def verify_password_reset():
    """التحقق من رمز إعادة تعيين كلمة المرور"""
    try:
        data = request.json
        
        # التحقق من البيانات المطلوبة
        if not data or 'email' not in data or 'token' not in data:
            return jsonify({
                'success': False,
                'message': 'يجب توفير البريد الإلكتروني ورمز إعادة التعيين'
            }), 400
            
        # البحث عن المستخدم
        user = User.query.filter_by(email=data['email']).first()
        
        if not user:
            return jsonify({
                'success': False,
                'message': 'البريد الإلكتروني غير مسجل في النظام'
            }), 404
            
        # التحقق من صحة الرمز
        if not user.verify_reset_token(data['token']):
            return jsonify({
                'success': False,
                'message': 'رمز إعادة التعيين غير صالح أو منتهي الصلاحية'
            }), 400
            
        return jsonify({
            'success': True,
            'message': 'تم التحقق من رمز إعادة التعيين بنجاح'
        })
    except Exception as e:
        logger.error(f"خطأ في التحقق من رمز إعادة تعيين كلمة المرور: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'حدث خطأ أثناء التحقق من رمز إعادة تعيين كلمة المرور: {str(e)}'
        }), 500

def register_profile_api(app):
    """تسجيل واجهة برمجة التطبيقات للملف الشخصي"""
    app.register_blueprint(profile_api)
