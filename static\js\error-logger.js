/**
 * نظام تسجيل الأخطاء في جانب العميل
 * يقوم بتسجيل أخطاء JavaScript وإرسالها إلى الخادم
 */

class ErrorLogger {
    /**
     * إنشاء مسجل الأخطاء
     * @param {Object} options - خيارات التكوين
     * @param {string} options.apiUrl - عنوان API لتسجيل الأخطاء
     * @param {boolean} options.captureGlobalErrors - ما إذا كان يجب التقاط الأخطاء العامة
     * @param {boolean} options.capturePromiseErrors - ما إذا كان يجب التقاط أخطاء الوعود
     * @param {boolean} options.captureConsoleErrors - ما إذا كان يجب التقاط أخطاء وحدة التحكم
     * @param {boolean} options.captureAjaxErrors - ما إذا كان يجب التقاط أخطاء AJAX
     * @param {boolean} options.captureNetworkErrors - ما إذا كان يجب التقاط أخطاء الشبكة
     * @param {Array<string>} options.ignoredErrors - قائمة بأنماط الأخطاء التي يجب تجاهلها
     */
    constructor(options = {}) {
        this.options = Object.assign({
            apiUrl: '/api/log-error',
            captureGlobalErrors: true,
            capturePromiseErrors: true,
            captureConsoleErrors: true,
            captureAjaxErrors: true,
            captureNetworkErrors: true,
            ignoredErrors: [
                'Script error',
                'ResizeObserver loop limit exceeded',
                'ResizeObserver loop completed with undelivered notifications',
                'Non-Error promise rejection captured'
            ]
        }, options);

        this.errorQueue = [];
        this.isProcessing = false;
        this.maxQueueSize = 50;
        this.processInterval = 1000; // 1 ثانية
        this.retryCount = 0;
        this.maxRetries = 3;

        // بدء معالجة الأخطاء
        setInterval(() => this.processErrorQueue(), this.processInterval);

        // تسجيل المعالجات
        if (this.options.captureGlobalErrors) {
            this.setupGlobalErrorHandler();
        }

        if (this.options.capturePromiseErrors) {
            this.setupPromiseErrorHandler();
        }

        if (this.options.captureConsoleErrors) {
            this.setupConsoleErrorHandler();
        }

        if (this.options.captureAjaxErrors) {
            this.setupAjaxErrorHandler();
        }

        if (this.options.captureNetworkErrors) {
            this.setupNetworkErrorHandler();
        }

        console.log('تم تهيئة نظام تسجيل الأخطاء');
    }

    /**
     * إعداد معالج الأخطاء العامة
     */
    setupGlobalErrorHandler() {
        window.addEventListener('error', (event) => {
            const { message, filename, lineno, colno, error } = event;
            
            // تجاهل الأخطاء المحددة
            if (this.shouldIgnoreError(message)) {
                return;
            }

            this.logError({
                message: message,
                stack: error ? error.stack : `Error at ${filename}:${lineno}:${colno}`,
                url: window.location.href,
                component: this.getComponentFromUrl(),
                severity: 'error',
                timestamp: new Date().toISOString(),
                browser: navigator.userAgent,
                os: navigator.platform
            });

            // منع السلوك الافتراضي للمتصفح
            event.preventDefault();
        });
    }

    /**
     * إعداد معالج أخطاء الوعود
     */
    setupPromiseErrorHandler() {
        window.addEventListener('unhandledrejection', (event) => {
            const error = event.reason;
            const message = error instanceof Error ? error.message : String(error);
            
            // تجاهل الأخطاء المحددة
            if (this.shouldIgnoreError(message)) {
                return;
            }

            this.logError({
                message: `Unhandled Promise Rejection: ${message}`,
                stack: error instanceof Error ? error.stack : 'No stack trace available',
                url: window.location.href,
                component: this.getComponentFromUrl(),
                severity: 'error',
                timestamp: new Date().toISOString(),
                browser: navigator.userAgent,
                os: navigator.platform
            });

            // منع السلوك الافتراضي للمتصفح
            event.preventDefault();
        });
    }

    /**
     * إعداد معالج أخطاء وحدة التحكم
     */
    setupConsoleErrorHandler() {
        const originalConsoleError = console.error;
        console.error = (...args) => {
            const message = args.map(arg => 
                typeof arg === 'object' ? JSON.stringify(arg) : String(arg)
            ).join(' ');
            
            // تجاهل الأخطاء المحددة
            if (this.shouldIgnoreError(message)) {
                originalConsoleError.apply(console, args);
                return;
            }

            this.logError({
                message: `Console Error: ${message}`,
                stack: new Error().stack,
                url: window.location.href,
                component: this.getComponentFromUrl(),
                severity: 'warning',
                timestamp: new Date().toISOString(),
                browser: navigator.userAgent,
                os: navigator.platform
            });

            originalConsoleError.apply(console, args);
        };
    }

    /**
     * إعداد معالج أخطاء AJAX
     */
    setupAjaxErrorHandler() {
        const originalFetch = window.fetch;
        window.fetch = async (...args) => {
            try {
                const response = await originalFetch(...args);
                
                if (!response.ok) {
                    this.logError({
                        message: `Fetch Error: ${response.status} ${response.statusText}`,
                        stack: `URL: ${args[0]}\nStatus: ${response.status} ${response.statusText}`,
                        url: window.location.href,
                        component: this.getComponentFromUrl(),
                        severity: 'warning',
                        timestamp: new Date().toISOString(),
                        browser: navigator.userAgent,
                        os: navigator.platform
                    });
                }
                
                return response;
            } catch (error) {
                this.logError({
                    message: `Fetch Network Error: ${error.message}`,
                    stack: error.stack,
                    url: window.location.href,
                    component: this.getComponentFromUrl(),
                    severity: 'error',
                    timestamp: new Date().toISOString(),
                    browser: navigator.userAgent,
                    os: navigator.platform
                });
                
                throw error;
            }
        };

        // اعتراض طلبات XMLHttpRequest
        const originalXhrOpen = XMLHttpRequest.prototype.open;
        const originalXhrSend = XMLHttpRequest.prototype.send;
        
        XMLHttpRequest.prototype.open = function(...args) {
            this._url = args[1];
            this._method = args[0];
            return originalXhrOpen.apply(this, args);
        };
        
        XMLHttpRequest.prototype.send = function(...args) {
            this.addEventListener('error', (event) => {
                errorLogger.logError({
                    message: `XHR Error: ${this._method} ${this._url}`,
                    stack: `XHR failed: ${this._method} ${this._url}`,
                    url: window.location.href,
                    component: errorLogger.getComponentFromUrl(),
                    severity: 'error',
                    timestamp: new Date().toISOString(),
                    browser: navigator.userAgent,
                    os: navigator.platform
                });
            });
            
            this.addEventListener('load', () => {
                if (this.status >= 400) {
                    errorLogger.logError({
                        message: `XHR Error: ${this.status} ${this.statusText}`,
                        stack: `URL: ${this._url}\nStatus: ${this.status} ${this.statusText}`,
                        url: window.location.href,
                        component: errorLogger.getComponentFromUrl(),
                        severity: 'warning',
                        timestamp: new Date().toISOString(),
                        browser: navigator.userAgent,
                        os: navigator.platform
                    });
                }
            });
            
            return originalXhrSend.apply(this, args);
        };
    }

    /**
     * إعداد معالج أخطاء الشبكة
     */
    setupNetworkErrorHandler() {
        window.addEventListener('offline', () => {
            this.logError({
                message: 'Network Offline',
                stack: 'Browser went offline',
                url: window.location.href,
                component: this.getComponentFromUrl(),
                severity: 'info',
                timestamp: new Date().toISOString(),
                browser: navigator.userAgent,
                os: navigator.platform
            });
        });
    }

    /**
     * تسجيل خطأ
     * @param {Object} errorData - بيانات الخطأ
     */
    logError(errorData) {
        // إضافة الخطأ إلى قائمة الانتظار
        if (this.errorQueue.length < this.maxQueueSize) {
            this.errorQueue.push(errorData);
        }
    }

    /**
     * معالجة قائمة انتظار الأخطاء
     */
    async processErrorQueue() {
        if (this.isProcessing || this.errorQueue.length === 0) {
            return;
        }

        this.isProcessing = true;

        try {
            const error = this.errorQueue.shift();
            await this.sendErrorToServer(error);
        } catch (error) {
            console.error('فشل في إرسال الخطأ إلى الخادم:', error);
            
            // إعادة المحاولة
            if (this.retryCount < this.maxRetries) {
                this.retryCount++;
                setTimeout(() => this.processErrorQueue(), 5000 * this.retryCount);
            }
        } finally {
            this.isProcessing = false;
            this.retryCount = 0;
        }
    }

    /**
     * إرسال الخطأ إلى الخادم
     * @param {Object} errorData - بيانات الخطأ
     */
    async sendErrorToServer(errorData) {
        try {
            const response = await fetch(this.options.apiUrl, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(errorData)
            });

            if (!response.ok) {
                throw new Error(`فشل في إرسال الخطأ: ${response.status} ${response.statusText}`);
            }

            return await response.json();
        } catch (error) {
            console.error('فشل في إرسال الخطأ إلى الخادم:', error);
            throw error;
        }
    }

    /**
     * التحقق مما إذا كان يجب تجاهل الخطأ
     * @param {string} message - رسالة الخطأ
     * @returns {boolean} - ما إذا كان يجب تجاهل الخطأ
     */
    shouldIgnoreError(message) {
        return this.options.ignoredErrors.some(pattern => 
            message.includes(pattern)
        );
    }

    /**
     * الحصول على اسم المكون من عنوان URL
     * @returns {string} - اسم المكون
     */
    getComponentFromUrl() {
        const path = window.location.pathname;
        const parts = path.split('/').filter(Boolean);
        
        if (parts.length === 0) {
            return 'home';
        }
        
        return parts[0];
    }
}

// إنشاء مثيل عام من مسجل الأخطاء
const errorLogger = new ErrorLogger();

// تصدير المثيل للاستخدام العام
window.errorLogger = errorLogger;
