#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Nobara POS System - نظام نوبارا لنقاط البيع
Developer: ENG/ Fouad Saber
Phone: 01020073527
Email: <EMAIL>
"""

import os
import sys
import socket
import webbrowser
import logging
from datetime import datetime
from flask import Flask, render_template_string, request, redirect, url_for, flash, session, jsonify
from flask_sqlalchemy import SQLAlchemy
from flask_login import LoginManager, UserMixin, login_user, logout_user, login_required, current_user
from werkzeug.security import generate_password_hash, check_password_hash

# Initialize extensions
db = SQLAlchemy()
login_manager = LoginManager()

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Models
class User(UserMixin, db.Model):
    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(80), unique=True, nullable=False)
    email = db.Column(db.String(120), unique=True, nullable=False)
    password_hash = db.Column(db.String(120), nullable=False)
    first_name = db.Column(db.String(50), nullable=False)
    last_name = db.Column(db.String(50), nullable=False)
    is_active = db.Column(db.Boolean, default=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    @property
    def full_name(self):
        return f"{self.first_name} {self.last_name}"

class Product(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(200), nullable=False)
    sku = db.Column(db.String(50), unique=True)
    barcode = db.Column(db.String(50), unique=True)
    price = db.Column(db.Float, nullable=False, default=0.0)
    cost = db.Column(db.Float, nullable=False, default=0.0)
    quantity = db.Column(db.Integer, nullable=False, default=0)
    is_active = db.Column(db.Boolean, default=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

class Customer(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(200), nullable=False)
    phone = db.Column(db.String(20))
    email = db.Column(db.String(120))
    address = db.Column(db.Text)
    is_active = db.Column(db.Boolean, default=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

class Sale(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    customer_id = db.Column(db.Integer, db.ForeignKey('customer.id'))
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'))
    total_amount = db.Column(db.Float, nullable=False)
    status = db.Column(db.String(20), default='completed')
    sale_date = db.Column(db.DateTime, default=datetime.utcnow)
    
    customer = db.relationship('Customer', backref='sales')
    user = db.relationship('User', backref='sales')

# Create Flask app
app = Flask(__name__)

# Configuration
app.config['SECRET_KEY'] = 'nobara-pos-secret-key-2024'
app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///nobara_pos.db'
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False

# Initialize extensions
db.init_app(app)
login_manager.init_app(app)
login_manager.login_view = 'login'
login_manager.login_message = 'يرجى تسجيل الدخول للوصول إلى هذه الصفحة'

@login_manager.user_loader
def load_user(user_id):
    return User.query.get(int(user_id))

# Create tables and default data
with app.app_context():
    db.create_all()
    
    # Create default admin user
    if not User.query.filter_by(username='admin').first():
        admin = User(
            username='admin',
            email='<EMAIL>',
            password_hash=generate_password_hash('admin'),
            first_name='Fouad',
            last_name='Saber'
        )
        db.session.add(admin)
        
        # Add sample data
        sample_products = [
            Product(name='منتج تجريبي 1', sku='PROD001', barcode='1234567890', price=100.0, cost=80.0, quantity=50),
            Product(name='منتج تجريبي 2', sku='PROD002', barcode='1234567891', price=200.0, cost=150.0, quantity=30),
            Product(name='منتج تجريبي 3', sku='PROD003', barcode='1234567892', price=50.0, cost=30.0, quantity=100),
        ]
        
        sample_customers = [
            Customer(name='عميل نقدي', phone='01000000000', email='<EMAIL>'),
            Customer(name='أحمد محمد', phone='01111111111', email='<EMAIL>'),
            Customer(name='فاطمة علي', phone='01222222222', email='<EMAIL>'),
        ]
        
        for product in sample_products:
            db.session.add(product)
        
        for customer in sample_customers:
            db.session.add(customer)
        
        db.session.commit()
        logger.info("تم إنشاء البيانات الافتراضية بنجاح")

# Routes
@app.route('/')
def index():
    if current_user.is_authenticated:
        return redirect(url_for('dashboard'))
    return redirect(url_for('login'))

@app.route('/login', methods=['GET', 'POST'])
def login():
    if current_user.is_authenticated:
        return redirect(url_for('dashboard'))
    
    if request.method == 'POST':
        username = request.form.get('username')
        password = request.form.get('password')
        
        user = User.query.filter_by(username=username).first()
        
        if user and check_password_hash(user.password_hash, password):
            login_user(user)
            flash('تم تسجيل الدخول بنجاح!', 'success')
            return redirect(url_for('dashboard'))
        else:
            flash('اسم المستخدم أو كلمة المرور غير صحيحة', 'error')
    
    return render_template_string('''
    <!DOCTYPE html>
    <html lang="ar" dir="rtl">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>تسجيل الدخول - نوبارا</title>
        <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@400;600;700&display=swap" rel="stylesheet">
        <style>
            * { margin: 0; padding: 0; box-sizing: border-box; }
            body { 
                font-family: 'Cairo', sans-serif; 
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                min-height: 100vh;
                display: flex;
                align-items: center;
                justify-content: center;
            }
            .login-card {
                background: rgba(255,255,255,0.95);
                padding: 3rem;
                border-radius: 2rem;
                box-shadow: 0 20px 40px rgba(0,0,0,0.1);
                width: 100%;
                max-width: 400px;
                color: #333;
            }
            .logo { 
                text-align: center; 
                margin-bottom: 2rem;
                font-size: 2rem;
                color: #2563eb;
            }
            .form-group { margin-bottom: 1.5rem; }
            label { display: block; margin-bottom: 0.5rem; font-weight: 600; }
            input {
                width: 100%;
                padding: 1rem;
                border: 2px solid #e5e7eb;
                border-radius: 0.75rem;
                font-size: 1rem;
                transition: border-color 0.3s ease;
            }
            input:focus {
                outline: none;
                border-color: #2563eb;
            }
            .btn {
                width: 100%;
                padding: 1rem;
                background: linear-gradient(135deg, #2563eb 0%, #dc2626 100%);
                color: white;
                border: none;
                border-radius: 0.75rem;
                font-size: 1rem;
                font-weight: 600;
                cursor: pointer;
                transition: transform 0.3s ease;
            }
            .btn:hover { transform: translateY(-2px); }
            .alert {
                padding: 1rem;
                margin-bottom: 1rem;
                border-radius: 0.75rem;
                font-weight: 600;
            }
            .alert-error {
                background: #fef2f2;
                color: #dc2626;
                border: 1px solid #fecaca;
            }
            .alert-success {
                background: #f0fdf4;
                color: #16a34a;
                border: 1px solid #bbf7d0;
            }
        </style>
    </head>
    <body>
        <div class="login-card">
            <div class="logo">🏪 نوبارا</div>
            
            {% with messages = get_flashed_messages(with_categories=true) %}
                {% if messages %}
                    {% for category, message in messages %}
                        <div class="alert alert-{{ 'error' if category == 'error' else 'success' }}">
                            {{ message }}
                        </div>
                    {% endfor %}
                {% endif %}
            {% endwith %}
            
            <form method="POST">
                <div class="form-group">
                    <label>اسم المستخدم</label>
                    <input type="text" name="username" required>
                </div>
                
                <div class="form-group">
                    <label>كلمة المرور</label>
                    <input type="password" name="password" required>
                </div>
                
                <button type="submit" class="btn">تسجيل الدخول</button>
            </form>
            
            <div style="margin-top: 2rem; padding: 1rem; background: #f3f4f6; border-radius: 0.75rem; text-align: center;">
                <strong>بيانات الدخول الافتراضية:</strong><br>
                اسم المستخدم: admin<br>
                كلمة المرور: admin
            </div>
        </div>
    </body>
    </html>
    ''')

@app.route('/logout')
@login_required
def logout():
    logout_user()
    flash('تم تسجيل الخروج بنجاح', 'success')
    return redirect(url_for('login'))

@app.route('/dashboard')
@login_required
def dashboard():
    # Get statistics
    total_products = Product.query.filter_by(is_active=True).count()
    total_customers = Customer.query.filter_by(is_active=True).count()
    total_sales = Sale.query.filter_by(status='completed').count()
    total_revenue = db.session.query(db.func.sum(Sale.total_amount)).filter_by(status='completed').scalar() or 0
    
    return render_template_string('''
    <!DOCTYPE html>
    <html lang="ar" dir="rtl">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>لوحة التحكم - نوبارا</title>
        <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@400;600;700&display=swap" rel="stylesheet">
        <link href="https://cdn.jsdelivr.net/npm/remixicon@3.5.0/fonts/remixicon.css" rel="stylesheet">
        <style>
            * { margin: 0; padding: 0; box-sizing: border-box; }
            body {
                font-family: 'Cairo', sans-serif;
                background: #f8fafc;
                color: #1f2937;
                line-height: 1.6;
            }
            .header {
                background: white;
                border-bottom: 1px solid #e5e7eb;
                padding: 1rem 0;
                box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            }
            .container { max-width: 1200px; margin: 0 auto; padding: 0 1rem; }
            .header-content {
                display: flex;
                justify-content: space-between;
                align-items: center;
            }
            .logo {
                display: flex;
                align-items: center;
                gap: 0.75rem;
                font-size: 1.5rem;
                font-weight: 700;
                color: #2563eb;
            }
            .nav-menu {
                display: flex;
                gap: 1rem;
                list-style: none;
            }
            .nav-link {
                padding: 0.5rem 1rem;
                border-radius: 0.5rem;
                text-decoration: none;
                color: #1f2937;
                transition: all 0.3s ease;
                display: flex;
                align-items: center;
                gap: 0.5rem;
            }
            .nav-link:hover, .nav-link.active {
                background: linear-gradient(135deg, #2563eb, #dc2626);
                color: white;
            }
            .main-content {
                padding: 2rem 0;
            }
            .page-title {
                font-size: 2rem;
                font-weight: 700;
                margin-bottom: 2rem;
            }
            .stats-grid {
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
                gap: 1.5rem;
                margin-bottom: 2rem;
            }
            .stat-card {
                background: white;
                border-radius: 1rem;
                padding: 1.5rem;
                box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
                border: 1px solid #e5e7eb;
                position: relative;
                overflow: hidden;
            }
            .stat-card::before {
                content: '';
                position: absolute;
                top: 0;
                right: 0;
                width: 4px;
                height: 100%;
                background: linear-gradient(135deg, #2563eb, #dc2626);
            }
            .stat-icon {
                font-size: 2.5rem;
                margin-bottom: 1rem;
            }
            .stat-value {
                font-size: 2rem;
                font-weight: 700;
                margin-bottom: 0.5rem;
            }
            .stat-label {
                color: #6b7280;
                font-weight: 500;
            }
            .card-grid {
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
                gap: 1.5rem;
            }
            .card {
                background: white;
                border-radius: 1rem;
                padding: 1.5rem;
                box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
                border: 1px solid #e5e7eb;
            }
            .btn {
                padding: 0.75rem 1.5rem;
                border-radius: 0.75rem;
                border: none;
                font-weight: 600;
                cursor: pointer;
                transition: all 0.3s ease;
                text-decoration: none;
                display: inline-flex;
                align-items: center;
                gap: 0.5rem;
                font-family: inherit;
                background: linear-gradient(135deg, #2563eb, #dc2626);
                color: white;
            }
            .btn:hover {
                transform: translateY(-2px);
                box-shadow: 0 8px 25px rgba(37, 99, 235, 0.3);
                color: white;
            }
            .user-menu {
                display: flex;
                align-items: center;
                gap: 1rem;
            }
            .btn-secondary {
                background: #f8fafc;
                color: #1f2937;
                border: 1px solid #e5e7eb;
            }
            .btn-secondary:hover {
                background: #e5e7eb;
                color: #1f2937;
            }
        </style>
    </head>
    <body>
        <header class="header">
            <div class="container">
                <div class="header-content">
                    <div class="logo">
                        <span>🏪</span>
                        نوبارا
                    </div>
                    
                    <nav>
                        <ul class="nav-menu">
                            <li><a href="{{ url_for('dashboard') }}" class="nav-link active">
                                <i class="ri-dashboard-line"></i> لوحة التحكم
                            </a></li>
                            <li><a href="#" onclick="alert('قيد التطوير')" class="nav-link">
                                <i class="ri-shopping-cart-line"></i> نقطة البيع
                            </a></li>
                            <li><a href="#" onclick="alert('قيد التطوير')" class="nav-link">
                                <i class="ri-product-hunt-line"></i> المنتجات
                            </a></li>
                            <li><a href="#" onclick="alert('قيد التطوير')" class="nav-link">
                                <i class="ri-user-line"></i> العملاء
                            </a></li>
                            <li><a href="#" onclick="alert('قيد التطوير')" class="nav-link">
                                <i class="ri-line-chart-line"></i> المبيعات
                            </a></li>
                            <li><a href="#" onclick="alert('قيد التطوير')" class="nav-link">
                                <i class="ri-file-chart-line"></i> التقارير
                            </a></li>
                        </ul>
                    </nav>
                    
                    <div class="user-menu">
                        <span>مرحباً، {{ current_user.first_name }}</span>
                        <a href="{{ url_for('logout') }}" class="btn btn-secondary">
                            <i class="ri-logout-box-line"></i> خروج
                        </a>
                    </div>
                </div>
            </div>
        </header>
        
        <main class="main-content">
            <div class="container">
                <h1 class="page-title">لوحة التحكم</h1>
                
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-icon" style="color: #2563eb;">
                            <i class="ri-product-hunt-line"></i>
                        </div>
                        <div class="stat-value">{{ total_products }}</div>
                        <div class="stat-label">إجمالي المنتجات</div>
                    </div>
                    
                    <div class="stat-card">
                        <div class="stat-icon" style="color: #dc2626;">
                            <i class="ri-user-line"></i>
                        </div>
                        <div class="stat-value">{{ total_customers }}</div>
                        <div class="stat-label">إجمالي العملاء</div>
                    </div>
                    
                    <div class="stat-card">
                        <div class="stat-icon" style="color: #10b981;">
                            <i class="ri-shopping-cart-line"></i>
                        </div>
                        <div class="stat-value">{{ total_sales }}</div>
                        <div class="stat-label">إجمالي المبيعات</div>
                    </div>
                    
                    <div class="stat-card">
                        <div class="stat-icon" style="color: #f59e0b;">
                            <i class="ri-money-dollar-circle-line"></i>
                        </div>
                        <div class="stat-value">{{ "%.2f"|format(total_revenue) }} ج.م</div>
                        <div class="stat-label">إجمالي الإيرادات</div>
                    </div>
                </div>
                
                <div class="card-grid">
                    <div class="card">
                        <h3 style="margin-bottom: 1rem; color: #2563eb;">
                            <i class="ri-shopping-cart-line"></i>
                            نقطة البيع
                        </h3>
                        <p style="color: #6b7280; margin-bottom: 1.5rem;">
                            ابدأ عملية بيع جديدة وإدارة المعاملات
                        </p>
                        <button onclick="alert('نقطة البيع قيد التطوير')" class="btn">
                            <i class="ri-arrow-left-line"></i>
                            انتقال لنقطة البيع
                        </button>
                    </div>
                    
                    <div class="card">
                        <h3 style="margin-bottom: 1rem; color: #dc2626;">
                            <i class="ri-product-hunt-line"></i>
                            إدارة المنتجات
                        </h3>
                        <p style="color: #6b7280; margin-bottom: 1.5rem;">
                            إضافة وتعديل وإدارة المنتجات والمخزون
                        </p>
                        <button onclick="alert('إدارة المنتجات قيد التطوير')" class="btn">
                            <i class="ri-arrow-left-line"></i>
                            إدارة المنتجات
                        </button>
                    </div>
                    
                    <div class="card">
                        <h3 style="margin-bottom: 1rem; color: #10b981;">
                            <i class="ri-user-line"></i>
                            إدارة العملاء
                        </h3>
                        <p style="color: #6b7280; margin-bottom: 1.5rem;">
                            إضافة وتعديل بيانات العملاء
                        </p>
                        <button onclick="alert('إدارة العملاء قيد التطوير')" class="btn">
                            <i class="ri-arrow-left-line"></i>
                            إدارة العملاء
                        </button>
                    </div>
                    
                    <div class="card">
                        <h3 style="margin-bottom: 1rem; color: #f59e0b;">
                            <i class="ri-file-chart-line"></i>
                            التقارير
                        </h3>
                        <p style="color: #6b7280; margin-bottom: 1.5rem;">
                            عرض التقارير المالية والإحصائيات
                        </p>
                        <button onclick="alert('التقارير قيد التطوير')" class="btn">
                            <i class="ri-arrow-left-line"></i>
                            عرض التقارير
                        </button>
                    </div>
                </div>
            </div>
        </main>
        
        <footer style="background: white; border-top: 1px solid #e5e7eb; padding: 1rem 0; text-align: center; color: #6b7280;">
            <div class="container">
                <p>&copy; 2024 نوبارا - نظام نقاط البيع | Powered By ENG/ Fouad Saber - Tel: 01020073527</p>
            </div>
        </footer>
    </body>
    </html>
    ''', total_products=total_products, total_customers=total_customers, total_sales=total_sales, total_revenue=total_revenue)

def get_local_ip():
    """الحصول على عنوان IP المحلي للجهاز"""
    try:
        s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        s.connect(("*******", 80))
        local_ip = s.getsockname()[0]
        s.close()
        return local_ip
    except Exception as e:
        logger.error(f"خطأ في الحصول على عنوان IP المحلي: {str(e)}")
        return "127.0.0.1"

if __name__ == "__main__":
    print("""
🏪 ═══════════════════════════════════════════════════════════════
   نوبارا - نظام نقاط البيع الاحترافي
   Nobara Professional POS System
   
   📱 Version: 2.0.0
   👨‍💻 Developer: ENG/ Fouad Saber
   📞 Phone: 01020073527
   📧 Email: <EMAIL>
   
   🌐 Access: http://localhost:5000
   🌐 Local IP: http://{}:5000
   
   🎯 بيانات الدخول:
      اسم المستخدم: admin
      كلمة المرور: admin
   
   ✅ النظام يعمل بنجاح!
   🚀 جاري تحميل المتصفح...
═══════════════════════════════════════════════════════════════
    """.format(get_local_ip()))
    
    # Open browser automatically
    try:
        webbrowser.open('http://localhost:5000')
    except Exception as e:
        logger.error(f"تعذر فتح المتصفح تلقائياً: {e}")
        print("يرجى فتح المتصفح يدوياً والذهاب إلى: http://localhost:5000")
    
    # Run the app
    try:
        app.run(host='0.0.0.0', port=5000, debug=False, use_reloader=False)
    except KeyboardInterrupt:
        print("\n\n🛑 تم إيقاف النظام بواسطة المستخدم")
    except Exception as e:
        print(f"\n❌ خطأ في تشغيل النظام: {e}")
    finally:
        print("\n👋 شكراً لاستخدام نظام نوبارا!")
        print("🏆 Powered By ENG/ Fouad Saber - Tel: 01020073527")
