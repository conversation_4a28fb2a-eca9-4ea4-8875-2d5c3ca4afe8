from flask import Blueprint, render_template, request, jsonify, flash, redirect, url_for
from flask_login import login_required, current_user
from models import Supplier, Purchase, PurchaseItem, Payment, Product
from app import db
from sqlalchemy import func, desc, and_, or_
from datetime import datetime, timedelta

suppliers_blueprint = Blueprint('suppliers', __name__)

@suppliers_blueprint.route('/suppliers')
@login_required
def index():
    # Get filter parameters
    search = request.args.get('search', '')

    # Prepare query
    query = Supplier.query

    # Apply filters
    if search:
        query = query.filter(
            Supplier.name.ilike(f'%{search}%') |
            Supplier.phone.ilike(f'%{search}%') |
            Supplier.email.ilike(f'%{search}%')
        )

    # Execute query with pagination
    page = request.args.get('page', 1, type=int)
    per_page = 10
    suppliers_paginated = query.order_by(Supplier.name).paginate(page=page, per_page=per_page)

    # Get supplier statistics
    total_suppliers = Supplier.query.count()

    # Top suppliers by purchase value
    top_suppliers = db.session.query(
        Supplier,
        func.sum(Purchase.total).label('total_purchased')
    ).join(
        Purchase
    ).group_by(
        Supplier.id
    ).order_by(
        func.sum(Purchase.total).desc()
    ).limit(5).all()

    # Recent suppliers
    recent_suppliers = Supplier.query.order_by(Supplier.created_at.desc()).limit(5).all()

    # Stats to pass to template
    stats = {
        'total_suppliers': total_suppliers,
        'top_suppliers': top_suppliers,
        'recent_suppliers': recent_suppliers
    }

    return render_template(
        'suppliers/suppliers.html',
        suppliers=suppliers_paginated,
        stats=stats,
        search=search,
        current_user=current_user
    )

@suppliers_blueprint.route('/suppliers/create', methods=['GET', 'POST'])
@login_required
def create():
    if request.method == 'POST':
        try:
            # Get form data
            name = request.form.get('name')
            contact_person = request.form.get('contact_person')
            phone = request.form.get('phone')
            email = request.form.get('email')
            address = request.form.get('address')

            # Create supplier
            supplier = Supplier(
                name=name,
                contact_person=contact_person,
                phone=phone,
                email=email,
                address=address
            )

            db.session.add(supplier)
            db.session.commit()

            flash('تم إضافة المورد بنجاح', 'success')
            return redirect(url_for('suppliers.index'))

        except Exception as e:
            db.session.rollback()
            flash(f'حدث خطأ أثناء إضافة المورد: {str(e)}', 'danger')

    return render_template('suppliers/supplier_form.html', supplier=None, action='create')

@suppliers_blueprint.route('/suppliers/<int:id>/edit', methods=['GET', 'POST'])
@login_required
def edit(id):
    supplier = Supplier.query.get_or_404(id)

    if request.method == 'POST':
        try:
            # Get form data
            supplier.name = request.form.get('name')
            supplier.contact_person = request.form.get('contact_person')
            supplier.phone = request.form.get('phone')
            supplier.email = request.form.get('email')
            supplier.address = request.form.get('address')

            db.session.commit()

            flash('تم تحديث بيانات المورد بنجاح', 'success')
            return redirect(url_for('suppliers.index'))

        except Exception as e:
            db.session.rollback()
            flash(f'حدث خطأ أثناء تحديث بيانات المورد: {str(e)}', 'danger')

    return render_template('suppliers/supplier_form.html', supplier=supplier, action='edit')

@suppliers_blueprint.route('/suppliers/<int:id>/delete', methods=['POST'])
@login_required
def delete(id):
    supplier = Supplier.query.get_or_404(id)

    try:
        # Check if supplier has purchases
        if supplier.purchases:
            flash('لا يمكن حذف المورد لأنه لديه مشتريات مسجلة', 'warning')
            return redirect(url_for('suppliers.index'))

        db.session.delete(supplier)
        db.session.commit()
        flash('تم حذف المورد بنجاح', 'success')
    except Exception as e:
        db.session.rollback()
        flash(f'حدث خطأ أثناء حذف المورد: {str(e)}', 'danger')

    return redirect(url_for('suppliers.index'))

@suppliers_blueprint.route('/api/suppliers')
@login_required
def api_suppliers():
    search = request.args.get('search', '')

    query = Supplier.query

    if search:
        query = query.filter(
            Supplier.name.ilike(f'%{search}%') |
            Supplier.phone.ilike(f'%{search}%')
        )

    suppliers = query.order_by(Supplier.name).all()

    return jsonify({
        'suppliers': [supplier.to_dict() for supplier in suppliers]
    })

@suppliers_blueprint.route('/suppliers/<int:id>/details')
@login_required
def details(id):
    supplier = Supplier.query.get_or_404(id)

    # Get supplier's purchases
    purchases = Purchase.query.filter_by(supplier_id=id).order_by(Purchase.created_at.desc()).all()

    # Calculate total spent
    total_purchased = db.session.query(func.sum(Purchase.total)).filter(Purchase.supplier_id == id).scalar() or 0

    # Calculate number of purchases
    purchase_count = len(purchases)

    # Calculate average purchase value
    avg_purchase_value = total_purchased / purchase_count if purchase_count > 0 else 0

    # Get supplier's payments
    payments = Payment.query.join(Purchase).filter(Purchase.supplier_id == id).order_by(Payment.payment_date.desc()).all()

    # Calculate paid amount from actual payments
    paid_amount = db.session.query(func.sum(Payment.amount)).join(Purchase).filter(Purchase.supplier_id == id).scalar() or 0
    remaining_amount = total_purchased - paid_amount

    # Get supplier's products
    supplier_products = db.session.query(
        Product,
        func.sum(PurchaseItem.quantity).label('total_purchased_quantity'),
        func.avg(PurchaseItem.cost_price).label('avg_purchase_price'),
        func.max(Purchase.created_at).label('last_purchase_date')
    ).join(
        PurchaseItem, Product.id == PurchaseItem.product_id
    ).join(
        Purchase, PurchaseItem.purchase_id == Purchase.id
    ).filter(
        Purchase.supplier_id == id
    ).group_by(
        Product.id
    ).all()

    # Process supplier products to add last purchase price
    for product_data in supplier_products:
        product = product_data[0]
        # Get the last purchase item for this product from this supplier
        last_purchase_item = PurchaseItem.query.join(Purchase).filter(
            Purchase.supplier_id == id,
            PurchaseItem.product_id == product.id
        ).order_by(Purchase.created_at.desc()).first()

        # Add last purchase price to the product data
        product.last_purchase_price = last_purchase_item.cost_price if last_purchase_item else 0
        product.total_purchased_quantity = product_data[1]
        product.avg_purchase_price = product_data[2]
        product.last_purchase_date = product_data[3]

    # Prepare statistics for the supplier
    stats = {
        'total_purchased': total_purchased,
        'purchase_count': purchase_count,
        'avg_purchase_value': avg_purchase_value,
        'last_purchase_date': purchases[0].created_at if purchases else None,
        'paid_amount': paid_amount,
        'remaining_amount': remaining_amount
    }

    # Get monthly purchase data for reports
    monthly_purchases = db.session.query(
        func.strftime('%Y-%m', Purchase.created_at).label('month'),
        func.sum(Purchase.total).label('total')
    ).filter(
        Purchase.supplier_id == id,
        Purchase.created_at >= datetime.now() - timedelta(days=365)  # Last year
    ).group_by(
        func.strftime('%Y-%m', Purchase.created_at)
    ).order_by(
        func.strftime('%Y-%m', Purchase.created_at)
    ).all()

    # Get top products by purchase value
    top_products = db.session.query(
        Product,
        func.sum(PurchaseItem.total).label('total_value')
    ).join(
        PurchaseItem, Product.id == PurchaseItem.product_id
    ).join(
        Purchase, PurchaseItem.purchase_id == Purchase.id
    ).filter(
        Purchase.supplier_id == id
    ).group_by(
        Product.id
    ).order_by(
        func.sum(PurchaseItem.total).desc()
    ).limit(5).all()

    # Prepare report data
    report_data = {
        'monthly_purchases': monthly_purchases,
        'top_products': top_products
    }

    return render_template(
        'suppliers/supplier_details.html',
        supplier=supplier,
        purchases=purchases,
        payments=payments,
        supplier_products=supplier_products,
        stats=stats,
        report_data=report_data,
        current_user=current_user,
        now=datetime.now()
    )

@suppliers_blueprint.route('/api/suppliers/<int:id>/purchases')
@login_required
def api_supplier_purchases(id):
    """الحصول على قائمة مشتريات المورد"""
    try:
        # الحصول على معلمات البحث والفلترة
        search = request.args.get('search', '')
        date_from = request.args.get('date_from', '')
        date_to = request.args.get('date_to', '')
        status = request.args.get('status', '')

        # إعداد الاستعلام
        query = Purchase.query.filter(Purchase.supplier_id == id)

        # تطبيق الفلاتر
        if search:
            query = query.filter(Purchase.reference_number.ilike(f'%{search}%'))

        if date_from:
            try:
                date_from_obj = datetime.strptime(date_from, '%Y-%m-%d')
                query = query.filter(Purchase.created_at >= date_from_obj)
            except:
                pass

        if date_to:
            try:
                date_to_obj = datetime.strptime(date_to, '%Y-%m-%d')
                # Add one day to include the end date
                date_to_obj = date_to_obj + timedelta(days=1)
                query = query.filter(Purchase.created_at < date_to_obj)
            except:
                pass

        if status:
            query = query.filter(Purchase.status == status)

        # التصنيف والترقيم
        sort_by = request.args.get('sort_by', 'created_at')
        sort_dir = request.args.get('sort_dir', 'desc')

        if sort_dir == 'desc':
            query = query.order_by(getattr(Purchase, sort_by).desc())
        else:
            query = query.order_by(getattr(Purchase, sort_by).asc())

        # الحصول على نتائج الاستعلام
        purchases = query.all()

        # تحويل النتائج إلى قاموس
        purchases_data = []
        for purchase in purchases:
            # حساب المبلغ المدفوع
            paid_amount = sum(payment.amount for payment in purchase.payments) if hasattr(purchase, 'payments') else 0

            purchases_data.append({
                'id': purchase.id,
                'reference_number': purchase.reference_number,
                'created_at': purchase.created_at.strftime('%Y-%m-%d %H:%M'),
                'total': purchase.total,
                'paid_amount': paid_amount,
                'remaining_amount': purchase.total - paid_amount,
                'status': purchase.status,
                'items_count': len(purchase.items) if hasattr(purchase, 'items') else 0
            })

        # إرجاع النتائج
        return jsonify({
            'purchases': purchases_data
        })
    except Exception as e:
        return jsonify({'error': f'فشل في الحصول على مشتريات المورد: {str(e)}'}), 500

@suppliers_blueprint.route('/api/suppliers/<int:id>/payments')
@login_required
def api_supplier_payments(id):
    """الحصول على قائمة مدفوعات المورد"""
    try:
        # الحصول على معلمات البحث والفلترة
        date_from = request.args.get('date_from', '')
        date_to = request.args.get('date_to', '')
        payment_method = request.args.get('payment_method', '')

        # إعداد الاستعلام
        query = Payment.query.join(Purchase).filter(Purchase.supplier_id == id)

        # تطبيق الفلاتر
        if date_from:
            try:
                date_from_obj = datetime.strptime(date_from, '%Y-%m-%d')
                query = query.filter(Payment.payment_date >= date_from_obj)
            except:
                pass

        if date_to:
            try:
                date_to_obj = datetime.strptime(date_to, '%Y-%m-%d')
                # Add one day to include the end date
                date_to_obj = date_to_obj + timedelta(days=1)
                query = query.filter(Payment.payment_date < date_to_obj)
            except:
                pass

        if payment_method:
            query = query.filter(Payment.payment_method == payment_method)

        # التصنيف والترقيم
        sort_by = request.args.get('sort_by', 'payment_date')
        sort_dir = request.args.get('sort_dir', 'desc')

        if sort_dir == 'desc':
            query = query.order_by(getattr(Payment, sort_by).desc())
        else:
            query = query.order_by(getattr(Payment, sort_by).asc())

        # الحصول على نتائج الاستعلام
        payments = query.all()

        # تحويل النتائج إلى قاموس
        payments_data = []
        for payment in payments:
            payments_data.append({
                'id': payment.id,
                'reference_number': payment.reference_number,
                'purchase_reference': payment.purchase.reference_number if payment.purchase else '',
                'payment_date': payment.payment_date.strftime('%Y-%m-%d'),
                'amount': payment.amount,
                'payment_method': payment.payment_method,
                'notes': payment.notes
            })

        # إرجاع النتائج
        return jsonify({
            'payments': payments_data
        })
    except Exception as e:
        return jsonify({'error': f'فشل في الحصول على مدفوعات المورد: {str(e)}'}), 500

@suppliers_blueprint.route('/api/suppliers/<int:id>/products')
@login_required
def api_supplier_products(id):
    """الحصول على قائمة منتجات المورد"""
    try:
        # الحصول على معلمات البحث
        search = request.args.get('search', '')

        # إعداد الاستعلام الأساسي
        base_query = db.session.query(
            Product,
            func.sum(PurchaseItem.quantity).label('total_purchased_quantity'),
            func.avg(PurchaseItem.cost_price).label('avg_purchase_price'),
            func.max(Purchase.created_at).label('last_purchase_date')
        ).join(
            PurchaseItem, Product.id == PurchaseItem.product_id
        ).join(
            Purchase, PurchaseItem.purchase_id == Purchase.id
        ).filter(
            Purchase.supplier_id == id
        )

        # تطبيق البحث
        if search:
            base_query = base_query.filter(
                or_(
                    Product.name.ilike(f'%{search}%'),
                    Product.code.ilike(f'%{search}%')
                )
            )

        # تجميع النتائج حسب المنتج
        supplier_products = base_query.group_by(Product.id).all()

        # تحويل النتائج إلى قاموس
        products_data = []
        for product_data in supplier_products:
            product = product_data[0]

            # الحصول على آخر سعر شراء
            last_purchase_item = PurchaseItem.query.join(Purchase).filter(
                Purchase.supplier_id == id,
                PurchaseItem.product_id == product.id
            ).order_by(Purchase.created_at.desc()).first()

            last_purchase_price = last_purchase_item.cost_price if last_purchase_item else 0

            products_data.append({
                'id': product.id,
                'name': product.name,
                'code': product.code,
                'total_purchased_quantity': product_data[1],
                'avg_purchase_price': product_data[2],
                'last_purchase_price': last_purchase_price,
                'last_purchase_date': product_data[3].strftime('%Y-%m-%d') if product_data[3] else None
            })

        # إرجاع النتائج
        return jsonify({
            'products': products_data
        })
    except Exception as e:
        return jsonify({'error': f'فشل في الحصول على منتجات المورد: {str(e)}'}), 500

@suppliers_blueprint.route('/api/suppliers/<int:id>/reports')
@login_required
def api_supplier_reports(id):
    """الحصول على تقارير المورد"""
    try:
        # الحصول على إحصائيات المشتريات الشهرية
        monthly_purchases = db.session.query(
            func.strftime('%Y-%m', Purchase.created_at).label('month'),
            func.sum(Purchase.total).label('total')
        ).filter(
            Purchase.supplier_id == id,
            Purchase.created_at >= datetime.now() - timedelta(days=365)  # Last year
        ).group_by(
            func.strftime('%Y-%m', Purchase.created_at)
        ).order_by(
            func.strftime('%Y-%m', Purchase.created_at)
        ).all()

        # تحويل النتائج إلى قاموس
        monthly_data = [{'month': month, 'total': total} for month, total in monthly_purchases]

        # الحصول على المنتجات الأكثر شراءً
        top_products = db.session.query(
            Product.id,
            Product.name,
            func.sum(PurchaseItem.quantity).label('quantity'),
            func.sum(PurchaseItem.total).label('total_value')
        ).join(
            PurchaseItem, Product.id == PurchaseItem.product_id
        ).join(
            Purchase, PurchaseItem.purchase_id == Purchase.id
        ).filter(
            Purchase.supplier_id == id
        ).group_by(
            Product.id
        ).order_by(
            func.sum(PurchaseItem.total).desc()
        ).limit(10).all()

        # تحويل النتائج إلى قاموس
        top_products_data = [{
            'id': id,
            'name': name,
            'quantity': quantity,
            'total_value': total_value
        } for id, name, quantity, total_value in top_products]

        # الحصول على إحصائيات المدفوعات
        payment_stats = db.session.query(
            func.strftime('%Y-%m', Payment.payment_date).label('month'),
            func.sum(Payment.amount).label('total')
        ).join(
            Purchase, Payment.purchase_id == Purchase.id
        ).filter(
            Purchase.supplier_id == id,
            Payment.payment_date >= datetime.now() - timedelta(days=365)  # Last year
        ).group_by(
            func.strftime('%Y-%m', Payment.payment_date)
        ).order_by(
            func.strftime('%Y-%m', Payment.payment_date)
        ).all()

        # تحويل النتائج إلى قاموس
        payment_data = [{'month': month, 'total': total} for month, total in payment_stats]

        # إرجاع النتائج
        return jsonify({
            'monthly_purchases': monthly_data,
            'top_products': top_products_data,
            'payment_stats': payment_data
        })
    except Exception as e:
        return jsonify({'error': f'فشل في الحصول على تقارير المورد: {str(e)}'}), 500