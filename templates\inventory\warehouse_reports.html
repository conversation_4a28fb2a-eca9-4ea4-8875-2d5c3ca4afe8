{% extends 'layout.html' %}

{% block title %}تقارير المخزون{% endblock %}

{% block page_content %}
<div class="container mx-auto px-4 py-8">
    <div class="mb-6 flex justify-between items-center">
        <div>
            <h1 class="text-2xl font-bold">تقارير المخزون</h1>
            <p class="text-gray-600">عرض تقارير وإحصائيات المخزون</p>
        </div>
        <a href="{{ url_for('warehouses.index') }}" class="bg-gray-100 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-200 transition-all">
            العودة للمخازن
        </a>
    </div>

    <!-- أنواع التقارير -->
    <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        <div class="bg-white rounded-lg shadow p-6 border-r-4 border-blue-500 hover:shadow-lg transition-all cursor-pointer" onclick="showReport('low_stock')">
            <h2 class="text-xl font-semibold mb-2">المخزون المنخفض</h2>
            <p class="text-gray-600 mb-4">عرض المنتجات ذات المخزون المنخفض في جميع المخازن</p>
            <div class="flex justify-between items-center">
                <span class="text-blue-500 text-2xl">
                    <i class="ri-error-warning-line"></i>
                </span>
                <span class="text-lg font-bold">{{ low_stock_count }}</span>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow p-6 border-r-4 border-red-500 hover:shadow-lg transition-all cursor-pointer" onclick="showReport('out_of_stock')">
            <h2 class="text-xl font-semibold mb-2">نفذ من المخزون</h2>
            <p class="text-gray-600 mb-4">عرض المنتجات التي نفذت من المخزون في جميع المخازن</p>
            <div class="flex justify-between items-center">
                <span class="text-red-500 text-2xl">
                    <i class="ri-close-circle-line"></i>
                </span>
                <span class="text-lg font-bold">{{ out_of_stock_count }}</span>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow p-6 border-r-4 border-green-500 hover:shadow-lg transition-all cursor-pointer" onclick="showReport('inventory_value')">
            <h2 class="text-xl font-semibold mb-2">قيمة المخزون</h2>
            <p class="text-gray-600 mb-4">عرض قيمة المخزون الإجمالية في جميع المخازن</p>
            <div class="flex justify-between items-center">
                <span class="text-green-500 text-2xl">
                    <i class="ri-money-pound-circle-line"></i>
                </span>
                <span class="text-lg font-bold">{{ inventory_value|round(2) }} ج.م</span>
            </div>
        </div>
    </div>

    <!-- فلاتر التقارير -->
    <div class="bg-white rounded-lg shadow p-4 mb-6">
        <form id="reportFilterForm" method="GET" class="flex flex-wrap items-center gap-4">
            <div>
                <label class="block text-gray-700 mb-2">المخزن</label>
                <select id="warehouse_filter" name="warehouse_id" class="border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500">
                    <option value="">جميع المخازن</option>
                    {% for warehouse in warehouses %}
                    <option value="{{ warehouse.id }}" {% if warehouse_id == warehouse.id|string %}selected{% endif %}>{{ warehouse.name }}</option>
                    {% endfor %}
                </select>
            </div>
            <div>
                <label class="block text-gray-700 mb-2">التصنيف</label>
                <select id="category_filter" name="category_id" class="border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500">
                    <option value="">جميع التصنيفات</option>
                    {% for category in categories %}
                    <option value="{{ category.id }}" {% if category_id == category.id|string %}selected{% endif %}>{{ category.name }}</option>
                    {% endfor %}
                </select>
            </div>
            <div>
                <label class="block text-gray-700 mb-2">البحث</label>
                <input type="text" id="search_filter" name="search" value="{{ search }}" placeholder="البحث عن منتج..."
                       class="border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500">
            </div>
            <div class="self-end">
                <button type="submit" class="bg-blue-500 text-white px-4 py-2 rounded-md hover:bg-blue-600 transition-all">
                    تطبيق الفلتر
                </button>
            </div>
        </form>
    </div>

    <!-- محتوى التقرير -->
    <div id="reportContent" class="bg-white rounded-lg shadow overflow-hidden">
        <div id="reportHeader" class="p-4 bg-gray-50 border-b flex justify-between items-center">
            <h2 id="reportTitle" class="text-xl font-semibold">المخزون المنخفض</h2>
            <button id="exportBtn" class="bg-green-100 text-green-700 hover:bg-green-200 px-3 py-1 rounded-md text-sm transition-all">
                <i class="ri-file-excel-line ml-1"></i>تصدير إلى Excel
            </button>
        </div>

        <div id="lowStockReport" class="report-section">
            <table class="min-w-full">
                <thead>
                    <tr class="bg-gray-100">
                        <th class="px-6 py-3 border-b text-right">المنتج</th>
                        <th class="px-6 py-3 border-b text-right">الباركود</th>
                        <th class="px-6 py-3 border-b text-right">المخزن</th>
                        <th class="px-6 py-3 border-b text-right">الكمية</th>
                        <th class="px-6 py-3 border-b text-right">الحد الأدنى</th>
                        <th class="px-6 py-3 border-b text-right">الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    {% for item in low_stock %}
                    <tr class="hover:bg-gray-50 bg-yellow-50">
                        <td class="px-6 py-4 border-b font-medium">{{ item.product.name }}</td>
                        <td class="px-6 py-4 border-b">{{ item.product.code or '-' }}</td>
                        <td class="px-6 py-4 border-b">{{ item.warehouse.name }}</td>
                        <td class="px-6 py-4 border-b">{{ item.quantity }}</td>
                        <td class="px-6 py-4 border-b">{{ item.minimum_stock }}</td>
                        <td class="px-6 py-4 border-b">
                            <a href="{{ url_for('warehouses.inventory', id=item.warehouse.id) }}" class="bg-blue-100 text-blue-700 hover:bg-blue-200 px-2 py-1 rounded-md text-xs transition-all">
                                <i class="ri-edit-line ml-1"></i>تعديل المخزون
                            </a>
                        </td>
                    </tr>
                    {% endfor %}

                    {% if low_stock|length == 0 %}
                    <tr>
                        <td colspan="6" class="px-6 py-8 text-center text-gray-500">
                            <div class="flex flex-col items-center">
                                <i class="ri-checkbox-circle-line text-4xl mb-2 text-green-500"></i>
                                <p>لا توجد منتجات ذات مخزون منخفض</p>
                            </div>
                        </td>
                    </tr>
                    {% endif %}
                </tbody>
            </table>
        </div>

        <div id="outOfStockReport" class="report-section hidden">
            <table class="min-w-full">
                <thead>
                    <tr class="bg-gray-100">
                        <th class="px-6 py-3 border-b text-right">المنتج</th>
                        <th class="px-6 py-3 border-b text-right">الباركود</th>
                        <th class="px-6 py-3 border-b text-right">المخزن</th>
                        <th class="px-6 py-3 border-b text-right">الحد الأدنى</th>
                        <th class="px-6 py-3 border-b text-right">الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    {% for item in out_of_stock %}
                    <tr class="hover:bg-gray-50 bg-red-50">
                        <td class="px-6 py-4 border-b font-medium">{{ item.product.name }}</td>
                        <td class="px-6 py-4 border-b">{{ item.product.code or '-' }}</td>
                        <td class="px-6 py-4 border-b">{{ item.warehouse.name }}</td>
                        <td class="px-6 py-4 border-b">{{ item.minimum_stock }}</td>
                        <td class="px-6 py-4 border-b">
                            <a href="{{ url_for('warehouses.inventory', id=item.warehouse.id) }}" class="bg-blue-100 text-blue-700 hover:bg-blue-200 px-2 py-1 rounded-md text-xs transition-all">
                                <i class="ri-edit-line ml-1"></i>تعديل المخزون
                            </a>
                        </td>
                    </tr>
                    {% endfor %}

                    {% if out_of_stock|length == 0 %}
                    <tr>
                        <td colspan="5" class="px-6 py-8 text-center text-gray-500">
                            <div class="flex flex-col items-center">
                                <i class="ri-checkbox-circle-line text-4xl mb-2 text-green-500"></i>
                                <p>لا توجد منتجات نفذت من المخزون</p>
                            </div>
                        </td>
                    </tr>
                    {% endif %}
                </tbody>
            </table>
        </div>

        <div id="inventoryValueReport" class="report-section hidden">
            <table class="min-w-full">
                <thead>
                    <tr class="bg-gray-100">
                        <th class="px-6 py-3 border-b text-right">المنتج</th>
                        <th class="px-6 py-3 border-b text-right">الباركود</th>
                        <th class="px-6 py-3 border-b text-right">المخزن</th>
                        <th class="px-6 py-3 border-b text-right">الكمية</th>
                        <th class="px-6 py-3 border-b text-right">سعر التكلفة</th>
                        <th class="px-6 py-3 border-b text-right">القيمة الإجمالية</th>
                    </tr>
                </thead>
                <tbody>
                    {% for item in inventory_items %}
                    <tr class="hover:bg-gray-50">
                        <td class="px-6 py-4 border-b font-medium">{{ item.product.name }}</td>
                        <td class="px-6 py-4 border-b">{{ item.product.code or '-' }}</td>
                        <td class="px-6 py-4 border-b">{{ item.warehouse.name }}</td>
                        <td class="px-6 py-4 border-b">{{ item.quantity }}</td>
                        <td class="px-6 py-4 border-b">{{ item.product.cost_price }} ج.م</td>
                        <td class="px-6 py-4 border-b font-medium">{{ (item.quantity * item.product.cost_price)|round(2) }} ج.م</td>
                    </tr>
                    {% endfor %}

                    {% if inventory_items|length == 0 %}
                    <tr>
                        <td colspan="6" class="px-6 py-8 text-center text-gray-500">
                            <div class="flex flex-col items-center">
                                <i class="ri-inbox-line text-4xl mb-2"></i>
                                <p>لا توجد منتجات في المخزون</p>
                            </div>
                        </td>
                    </tr>
                    {% else %}
                    <tr class="bg-gray-100 font-bold">
                        <td colspan="5" class="px-6 py-4 border-b text-left">إجمالي قيمة المخزون:</td>
                        <td class="px-6 py-4 border-b">{{ inventory_value|round(2) }} ج.م</td>
                    </tr>
                    {% endif %}
                </tbody>
            </table>
        </div>
    </div>
</div>

<script>
    function showReport(reportType) {
        // إخفاء جميع التقارير
        document.querySelectorAll('.report-section').forEach(section => {
            section.classList.add('hidden');
        });

        // إظهار التقرير المطلوب
        let reportTitle = '';

        if (reportType === 'low_stock') {
            document.getElementById('lowStockReport').classList.remove('hidden');
            reportTitle = 'المخزون المنخفض';
        } else if (reportType === 'out_of_stock') {
            document.getElementById('outOfStockReport').classList.remove('hidden');
            reportTitle = 'نفذ من المخزون';
        } else if (reportType === 'inventory_value') {
            document.getElementById('inventoryValueReport').classList.remove('hidden');
            reportTitle = 'قيمة المخزون';
        }

        // تحديث عنوان التقرير
        document.getElementById('reportTitle').textContent = reportTitle;

        // تحديث نموذج الفلتر
        document.getElementById('reportFilterForm').dataset.reportType = reportType;
    }

    // تصدير التقرير إلى Excel
    document.getElementById('exportBtn').addEventListener('click', function() {
        const reportType = document.getElementById('reportFilterForm').dataset.reportType || 'low_stock';
        const warehouseId = document.getElementById('warehouse_filter').value;
        const categoryId = document.getElementById('category_filter').value;
        const search = document.getElementById('search_filter').value;

        let url = `/api/warehouses/reports/export?report_type=${reportType}`;
        if (warehouseId) url += `&warehouse_id=${warehouseId}`;
        if (categoryId) url += `&category_id=${categoryId}`;
        if (search) url += `&search=${search}`;

        window.location.href = url;
    });

    // تحديد التقرير الافتراضي عند تحميل الصفحة
    document.addEventListener('DOMContentLoaded', function() {
        const urlParams = new URLSearchParams(window.location.search);
        const reportType = urlParams.get('report_type') || 'low_stock';
        showReport(reportType);

        // تحديث نموذج الفلتر
        document.getElementById('reportFilterForm').dataset.reportType = reportType;

        // إضافة معلمة نوع التقرير عند تقديم النموذج
        document.getElementById('reportFilterForm').addEventListener('submit', function(e) {
            e.preventDefault();

            const reportType = this.dataset.reportType || 'low_stock';
            const warehouseId = document.getElementById('warehouse_filter').value;
            const categoryId = document.getElementById('category_filter').value;
            const search = document.getElementById('search_filter').value;

            let url = `?report_type=${reportType}`;
            if (warehouseId) url += `&warehouse_id=${warehouseId}`;
            if (categoryId) url += `&category_id=${categoryId}`;
            if (search) url += `&search=${search}`;

            window.location.href = url;
        });
    });
</script>
{% endblock page_content %}
