from flask import Blueprint, render_template, request, jsonify, flash, redirect, url_for, Response
from flask_login import login_required, current_user, login_user, logout_user
from models import (
    User, Product, Category, Customer, Order, OrderItem, Supplier, Purchase,
    PurchaseItem, Warehouse, Inventory, InventoryMovement, Settings, Payment,
    Notification, InventoryAlert, InventoryCount, InventoryCountItem
)
from app import db
from sqlalchemy import or_, func, and_, text
import csv
from io import StringIO
from datetime import datetime, timedelta
import secrets
import os
from werkzeug.utils import secure_filename
from security import Security

# تعريف البلوبرنت
auth_blueprint = Blueprint('auth', __name__)
dashboard_blueprint = Blueprint('dashboard', __name__)
products_blueprint = Blueprint('products', __name__)
customers_blueprint = Blueprint('customers', __name__)
suppliers_blueprint = Blueprint('suppliers', __name__)
sales_blueprint = Blueprint('sales', __name__)
purchases_blueprint = Blueprint('purchases', __name__)
pos_blueprint = Blueprint('pos', __name__)
warehouses_blueprint = Blueprint('warehouses', __name__)
reports_blueprint = Blueprint('reports', __name__)
settings_blueprint = Blueprint('settings', __name__)
users_blueprint = Blueprint('users', __name__)
notifications_blueprint = Blueprint('notifications', __name__)
deferred_sales_blueprint = Blueprint('deferred_sales', __name__)

# تسجيل جميع البلوبرنت
def register_routes(app):
    """
    تسجيل جميع البلوبرنت في التطبيق
    """
    # تسجيل البلوبرنت الرئيسية
    app.register_blueprint(auth_blueprint)
    app.register_blueprint(dashboard_blueprint)
    app.register_blueprint(products_blueprint)
    app.register_blueprint(customers_blueprint)
    app.register_blueprint(suppliers_blueprint)
    app.register_blueprint(sales_blueprint)
    app.register_blueprint(purchases_blueprint)
    app.register_blueprint(pos_blueprint)
    app.register_blueprint(warehouses_blueprint)
    app.register_blueprint(reports_blueprint)
    app.register_blueprint(settings_blueprint)
    app.register_blueprint(users_blueprint)
    app.register_blueprint(notifications_blueprint)
    app.register_blueprint(deferred_sales_blueprint)

# ===== مسارات المصادقة =====
@auth_blueprint.route('/login', methods=['GET', 'POST'])
def login():
    """صفحة تسجيل الدخول"""
    if current_user.is_authenticated:
        return redirect(url_for('dashboard.index'))

    if request.method == 'POST':
        username = request.form.get('username')
        password = request.form.get('password')
        remember = request.form.get('remember') == 'on'

        user = User.query.filter_by(username=username).first()

        if user and user.check_password(password):
            login_user(user, remember=remember)
            next_page = request.args.get('next')
            return redirect(next_page or url_for('dashboard.index'))
        else:
            flash('اسم المستخدم أو كلمة المرور غير صحيحة', 'danger')

    return render_template('login.html')

@auth_blueprint.route('/logout')
@login_required
def logout():
    """تسجيل الخروج"""
    logout_user()
    return redirect(url_for('auth.login'))

# ===== مسارات لوحة التحكم =====
@dashboard_blueprint.route('/')
@login_required
def index():
    """الصفحة الرئيسية للوحة التحكم"""
    # إحصائيات المبيعات
    total_sales = db.session.query(func.sum(Order.total)).scalar() or 0
    today_sales = db.session.query(func.sum(Order.total)).filter(
        func.date(Order.created_at) == datetime.now().date()
    ).scalar() or 0

    # إحصائيات المشتريات
    total_purchases = db.session.query(func.sum(Purchase.total)).scalar() or 0

    # إحصائيات المنتجات
    total_products = Product.query.count()
    low_stock_count = db.session.query(Inventory).filter(
        Inventory.quantity > 0,
        Inventory.quantity <= Inventory.minimum_stock
    ).count()

    # إحصائيات العملاء والموردين
    total_customers = Customer.query.count()
    total_suppliers = Supplier.query.count()

    # المبيعات الأخيرة
    recent_sales = Order.query.order_by(Order.created_at.desc()).limit(5).all()

    # المشتريات الأخيرة
    recent_purchases = Purchase.query.order_by(Purchase.created_at.desc()).limit(5).all()

    # المنتجات الأكثر مبيعًا
    top_selling_products = db.session.query(
        Product,
        func.sum(OrderItem.quantity).label('total_quantity')
    ).join(OrderItem).group_by(Product).order_by(func.sum(OrderItem.quantity).desc()).limit(5).all()

    return render_template('dashboard.html',
                          total_sales=total_sales,
                          today_sales=today_sales,
                          total_purchases=total_purchases,
                          total_products=total_products,
                          low_stock_count=low_stock_count,
                          total_customers=total_customers,
                          total_suppliers=total_suppliers,
                          recent_sales=recent_sales,
                          recent_purchases=recent_purchases,
                          top_selling_products=top_selling_products)

# ===== مسارات المنتجات =====
@products_blueprint.route('/products')
@login_required
def index():
    """صفحة قائمة المنتجات"""
    search = request.args.get('search', '')
    category_id = request.args.get('category_id', '')
    status = request.args.get('status', 'all')

    # إعداد الاستعلام الأساسي
    query = Product.query

    # تطبيق فلتر البحث
    if search:
        query = query.filter(
            or_(
                Product.name.ilike(f'%{search}%'),
                Product.code.ilike(f'%{search}%'),
                Product.brand.ilike(f'%{search}%')
            )
        )

    # تطبيق فلتر التصنيف
    if category_id and category_id.isdigit():
        query = query.filter_by(category_id=int(category_id))

    # تطبيق فلتر الحالة
    if status == 'active':
        query = query.filter_by(is_active=True)
    elif status == 'inactive':
        query = query.filter_by(is_active=False)

    # الحصول على المنتجات
    products = query.order_by(Product.name).all()

    # الحصول على التصنيفات
    categories = Category.query.order_by(Category.name).all()

    return render_template('products.html',
                          products=products,
                          categories=categories,
                          search=search,
                          category_id=category_id,
                          status=status)

# ===== مسارات الإشعارات =====
@notifications_blueprint.route('/api/notifications')
@login_required
def get_notifications():
    """الحصول على إشعارات المستخدم الحالي"""
    unread_count = Notification.query.filter_by(user_id=current_user.id, is_read=False).count()
    notifications = Notification.query.filter_by(user_id=current_user.id).order_by(Notification.created_at.desc()).limit(10).all()

    return jsonify({
        'unread_count': unread_count,
        'notifications': [notification.to_dict() for notification in notifications]
    })

@notifications_blueprint.route('/notifications/<int:id>/mark-read', methods=['POST'])
@login_required
def mark_read(id):
    """تحديد الإشعار كمقروء"""
    notification = Notification.query.get_or_404(id)

    # التحقق من أن الإشعار ينتمي للمستخدم الحالي
    if notification.user_id != current_user.id:
        return jsonify({'success': False, 'message': 'غير مصرح لك بتحديث هذا الإشعار'}), 403

    notification.is_read = True
    db.session.commit()

    return jsonify({'success': True})

@notifications_blueprint.route('/notifications/mark-all-read', methods=['POST'])
@login_required
def mark_all_read():
    """تحديد جميع الإشعارات كمقروءة"""
    Notification.query.filter_by(user_id=current_user.id, is_read=False).update({'is_read': True})
    db.session.commit()

    return jsonify({'success': True})

# ===== مسارات الإعدادات =====
@settings_blueprint.route('/settings')
@login_required
def index():
    """صفحة الإعدادات"""
    # الحصول على إعدادات المتجر
    settings = Settings.query.first()
    if not settings:
        settings = Settings(store_name="متجري")
        db.session.add(settings)
        db.session.commit()

    return render_template('settings/settings.html', settings=settings)

@settings_blueprint.route('/settings/update', methods=['POST'])
@login_required
def update():
    """تحديث إعدادات المتجر"""
    settings = Settings.query.first()
    if not settings:
        settings = Settings()
        db.session.add(settings)

    # تحديث البيانات
    settings.store_name = request.form.get('store_name', 'متجري')
    settings.address = request.form.get('address', '')
    settings.phone = request.form.get('phone', '')
    settings.email = request.form.get('email', '')
    settings.tax_percentage = float(request.form.get('tax_percentage', 0))
    settings.currency = request.form.get('currency', 'ج.م')
    settings.receipt_footer = request.form.get('receipt_footer', '')

    # معالجة الشعار
    logo_file = request.files.get('logo')
    if logo_file and logo_file.filename:
        filename = secure_filename(logo_file.filename)
        file_path = os.path.join('static', 'uploads', filename)
        logo_file.save(file_path)
        settings.logo = file_path

    db.session.commit()
    flash('تم تحديث الإعدادات بنجاح', 'success')

    return redirect(url_for('settings.index'))
