import os
import logging
from datetime import datetime

from flask import Flask
from flask_sqlalchemy import SQLAlchemy
from flask_migrate import Migrate
from sqlalchemy.orm import DeclarativeBase
from werkzeug.middleware.proxy_fix import ProxyFix

# إعداد التسجيل
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler(os.path.join('logs', f'app_{datetime.now().strftime("%Y%m%d")}.log'), encoding='utf-8')
    ]
)
logger = logging.getLogger(__name__)

# التأكد من وجود مجلد السجلات
os.makedirs('logs', exist_ok=True)

class Base(DeclarativeBase):
    pass


db = SQLAlchemy(model_class=Base)
migrate = Migrate()
# إنشاء التطبيق
app = Flask(__name__)
app.secret_key = os.environ.get("SECRET_KEY", "fouad_pos_secret_key")
app.wsgi_app = ProxyFix(app.wsgi_app, x_proto=1, x_host=1)  # مطلوب لتوليد url_for مع https

# استخدام متغيرات بيئية للاتصال بقاعدة البيانات
DB_HOST = os.environ.get("DB_HOST", "localhost")
DB_PORT = os.environ.get("DB_PORT", "5432")
DB_NAME = os.environ.get("DB_NAME", "fouad_pos")
DB_USER = os.environ.get("DB_USER", "postgres")
DB_PASSWORD = os.environ.get("DB_PASSWORD", "postgres")

# تكوين قاعدة البيانات
if os.environ.get("USE_EXTERNAL_DB", "false").lower() == "true":
    # استخدام قاعدة بيانات PostgreSQL خارجية
    DATABASE_URL = f"postgresql://{DB_USER}:{DB_PASSWORD}@{DB_HOST}:{DB_PORT}/{DB_NAME}"
    logger.info(f"الاتصال بقاعدة البيانات الخارجية: {DB_HOST}:{DB_PORT}/{DB_NAME}")
else:
    # استخدام قاعدة بيانات SQLite المحلية
    # استخدام مسار قاعدة البيانات داخل مجلد instance
    db_path = os.path.join(os.path.abspath(os.path.dirname(__file__)), 'instance', 'fouad_pos.db')

    # التأكد من وجود مجلد instance
    os.makedirs(os.path.dirname(db_path), exist_ok=True)

    # التأكد من وجود قاعدة البيانات وإنشائها إذا لم تكن موجودة
    if not os.path.exists(db_path):
        print(f"قاعدة البيانات غير موجودة في: {db_path}")
        try:
            # إنشاء قاعدة بيانات فارغة
            import sqlite3
            conn = sqlite3.connect(db_path)
            conn.close()
            print(f"تم إنشاء قاعدة بيانات جديدة في: {db_path}")
        except Exception as e:
            print(f"خطأ أثناء إنشاء قاعدة البيانات: {str(e)}")

    # استخدام مسار مطلق لقاعدة البيانات
    DATABASE_URL = os.environ.get(
        "DATABASE_URL",
        f"sqlite:///{db_path}"
    )

    # تعيين متغير بيئي لمسار قاعدة البيانات
    os.environ["DATABASE_URL"] = f"sqlite:///{db_path}"

    # طباعة المسار الكامل لقاعدة البيانات للتأكد من صحته
    print(f"مسار قاعدة البيانات: {db_path}")
    logger.info(f"استخدام قاعدة البيانات المحلية: {DATABASE_URL}")

# إعدادات التطبيق
app.config["SQLALCHEMY_DATABASE_URI"] = DATABASE_URL
app.config["SQLALCHEMY_TRACK_MODIFICATIONS"] = False
app.config["SQLALCHEMY_ENGINE_OPTIONS"] = {
    "pool_recycle": 300,
    "pool_pre_ping": True,
    "connect_args": {
        "connect_timeout": 10,
    } if "postgresql" in DATABASE_URL else {
        "check_same_thread": False,  # السماح بالوصول من خيوط متعددة
        "timeout": 30  # زيادة مهلة الاتصال
    }
}

# إعدادات البريد الإلكتروني
app.config["MAIL_SERVER"] = os.environ.get("MAIL_SERVER", "smtp.gmail.com")
app.config["MAIL_PORT"] = int(os.environ.get("MAIL_PORT", 587))
app.config["MAIL_USE_TLS"] = os.environ.get("MAIL_USE_TLS", "True").lower() == "true"
app.config["MAIL_USERNAME"] = os.environ.get("MAIL_USERNAME", "")
app.config["MAIL_PASSWORD"] = os.environ.get("MAIL_PASSWORD", "")
app.config["MAIL_DEFAULT_SENDER"] = os.environ.get("MAIL_DEFAULT_SENDER", "")

# تهيئة التطبيق مع الامتدادات
db.init_app(app)
migrate.init_app(app, db)

# تهيئة نظام تسجيل الأخطاء
from utils.error_logger import error_logger
error_logger.init_app(app)

# تهيئة النظام المحاسبي
try:
    from utils.accounting_hooks import register_accounting_hooks
    register_accounting_hooks()
    app.logger.info("تم تهيئة النظام المحاسبي بنجاح")
except Exception as e:
    app.logger.error(f"خطأ أثناء تهيئة النظام المحاسبي: {str(e)}")

with app.app_context():
    # Import models
    import models  # noqa: F401
    import sqlite3
    import threading
    import time
    from sqlalchemy import text

    # محاولة استيراد مكتبة schedule
    try:
        import schedule
    except ImportError:
        app.logger.warning("مكتبة schedule غير متوفرة. لن يتم تفعيل النسخ الاحتياطي التلقائي.")

    # إنشاء جميع الجداول
    try:
        db.create_all()
        print("تم إنشاء جميع الجداول بنجاح")
    except Exception as e:
        print(f"خطأ أثناء إنشاء الجداول: {str(e)}")
        # محاولة إنشاء قاعدة بيانات جديدة
        try:
            # إنشاء اتصال مباشر بقاعدة البيانات
            if "sqlite" in DATABASE_URL:
                import sqlite3
                db_path = os.path.join(os.path.abspath(os.path.dirname(__file__)), 'fouad_pos.db')
                # التأكد من وجود المجلد
                os.makedirs(os.path.dirname(db_path), exist_ok=True)
                # إنشاء قاعدة بيانات فارغة
                conn = sqlite3.connect(db_path)
                conn.close()
                print(f"تم إنشاء قاعدة بيانات جديدة في: {db_path}")
                # محاولة إنشاء الجداول مرة أخرى
                db.create_all()
                print("تم إنشاء جميع الجداول بنجاح بعد إنشاء قاعدة بيانات جديدة")
        except Exception as e2:
            print(f"فشل في إنشاء قاعدة بيانات جديدة: {str(e2)}")

    # تهيئة جدولة النسخ الاحتياطي التلقائي
    try:
        # التحقق من وجود مكتبة schedule
        if 'schedule' in globals():
            try:
                from routes.settings import load_backup_settings, schedule_auto_backup, SCHEDULE_AVAILABLE

                # تحميل إعدادات النسخ الاحتياطي
                backup_settings = load_backup_settings()

                # جدولة النسخ الاحتياطي التلقائي
                schedule_auto_backup(backup_settings)
            except Exception as e:
                app.logger.error(f"خطأ أثناء تهيئة النسخ الاحتياطي: {str(e)}")

            # بدء خيط لتشغيل المهام المجدولة
            def run_scheduler():
                while True:
                    schedule.run_pending()
                    time.sleep(60)  # التحقق كل دقيقة

            scheduler_thread = threading.Thread(target=run_scheduler, daemon=True)
            scheduler_thread.start()
            app._scheduler_thread = scheduler_thread

            app.logger.info("تم تهيئة جدولة النسخ الاحتياطي التلقائي")
        else:
            app.logger.warning("تم تخطي تهيئة النسخ الاحتياطي التلقائي لأن مكتبة schedule غير متوفرة")
    except Exception as e:
        app.logger.error(f"خطأ أثناء تهيئة جدولة النسخ الاحتياطي التلقائي: {str(e)}")

    # التحقق من وجود عمود warehouse_id في جدول order وإضافته إذا لم يكن موجودًا
    try:
        # التحقق من نوع قاعدة البيانات
        if "sqlite" in DATABASE_URL:
            # الحصول على مسار قاعدة البيانات SQLite
            db_path = DATABASE_URL.replace("sqlite:///", "")

            # استخدام نفس مسار قاعدة البيانات المستخدم في التهيئة
            db_path = os.path.join(os.path.abspath(os.path.dirname(__file__)), 'fouad_pos.db')

            # التأكد من وجود قاعدة البيانات
            if not os.path.exists(db_path):
                print(f"قاعدة البيانات غير موجودة في: {db_path}")
                # إنشاء قاعدة بيانات فارغة
                conn = sqlite3.connect(db_path)
                conn.close()
                print(f"تم إنشاء قاعدة بيانات جديدة في: {db_path}")

            # الاتصال بقاعدة البيانات
            try:
                conn = sqlite3.connect(db_path)
                cursor = conn.cursor()
                print(f"تم الاتصال بقاعدة البيانات بنجاح: {db_path}")
            except Exception as e:
                print(f"خطأ أثناء الاتصال بقاعدة البيانات: {str(e)}")
                raise

            # التحقق من وجود عمود warehouse_id في جدول order
            cursor.execute("PRAGMA table_info(\"order\")")
            columns = cursor.fetchall()
            column_names = [column[1] for column in columns]

            # إضافة عمود warehouse_id إذا لم يكن موجودًا
            if 'warehouse_id' not in column_names:
                app.logger.info("إضافة عمود warehouse_id إلى جدول order...")
                cursor.execute('ALTER TABLE "order" ADD COLUMN warehouse_id INTEGER')

                # تعيين قيمة افتراضية للمخزن (المخزن الافتراضي)
                default_warehouse = None
                cursor.execute("SELECT id FROM warehouse WHERE is_default = 1")
                result = cursor.fetchone()

                if result:
                    default_warehouse = result[0]
                else:
                    cursor.execute("SELECT id FROM warehouse LIMIT 1")
                    result = cursor.fetchone()
                    if result:
                        default_warehouse = result[0]

                if default_warehouse:
                    cursor.execute('UPDATE "order" SET warehouse_id = ?', (default_warehouse,))
                    app.logger.info(f"تم تعيين المخزن الافتراضي (ID: {default_warehouse}) لجميع الطلبات الموجودة")

                # حفظ التغييرات
                conn.commit()
                app.logger.info("تم إضافة عمود warehouse_id بنجاح")
            else:
                app.logger.info("عمود warehouse_id موجود بالفعل في جدول order")

            # إغلاق الاتصال
            conn.close()
        else:
            # للقواعد الأخرى مثل PostgreSQL، يمكن استخدام SQLAlchemy مباشرة
            app.logger.info("التحقق من وجود عمود warehouse_id في جدول order...")

            # استخدام SQLAlchemy للتحقق من وجود العمود
            result = db.session.execute(text("SELECT column_name FROM information_schema.columns WHERE table_name = 'order' AND column_name = 'warehouse_id'"))
            if not result.fetchone():
                app.logger.info("إضافة عمود warehouse_id إلى جدول order...")
                db.session.execute(text("ALTER TABLE \"order\" ADD COLUMN warehouse_id INTEGER"))

                # تعيين قيمة افتراضية للمخزن (المخزن الافتراضي)
                result = db.session.execute(text("SELECT id FROM warehouse WHERE is_default = TRUE LIMIT 1"))
                default_warehouse = result.fetchone()

                if not default_warehouse:
                    result = db.session.execute(text("SELECT id FROM warehouse LIMIT 1"))
                    default_warehouse = result.fetchone()

                if default_warehouse:
                    db.session.execute(text(f"UPDATE \"order\" SET warehouse_id = {default_warehouse[0]}"))
                    app.logger.info(f"تم تعيين المخزن الافتراضي (ID: {default_warehouse[0]}) لجميع الطلبات الموجودة")

                db.session.commit()
                app.logger.info("تم إضافة عمود warehouse_id بنجاح")
            else:
                app.logger.info("عمود warehouse_id موجود بالفعل في جدول order")
    except Exception as e:
        app.logger.error(f"خطأ أثناء التحقق من عمود warehouse_id: {str(e)}")

if __name__ == "__main__":
    app.run(host="0.0.0.0", port=5000, debug=True)

