"""
ملف للتحقق من بيئة Python وتثبيت المكتبات المطلوبة
"""

import os
import sys
import subprocess

def main():
    """التحقق من بيئة Python وتثبيت المكتبات المطلوبة"""
    print("=" * 50)
    print("           نظام نقاط البيع - م/ فؤاد صابر")
    print("=" * 50)
    print()
    print("جاري التحقق من بيئة Python...")
    print()

    # عرض معلومات عن بيئة Python
    print(f"إصدار Python: {sys.version}")
    print(f"مسار Python: {sys.executable}")
    print()

    # تثبيت المكتبات المطلوبة مباشرة في بيئة Python الرئيسية
    print("جاري تثبيت المكتبات المطلوبة...")
    try:
        # التحقق من وجود ملف requirements.txt
        if os.path.exists('requirements.txt'):
            print("تثبيت المكتبات من ملف requirements.txt...")
            subprocess.run([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"], check=True)
        else:
            # تثبيت المكتبات الأساسية
            print("تثبيت المكتبات الأساسية...")
            subprocess.run([sys.executable, "-m", "pip", "install",
                            "flask", "flask-sqlalchemy", "flask-login",
                            "werkzeug", "sqlalchemy", "jwt", "schedule"], check=True)
        print("تم تثبيت المكتبات بنجاح")
    except Exception as e:
        print(f"خطأ أثناء تثبيت المكتبات: {e}")

    # التحقق من تثبيت مكتبة schedule
    try:
        import schedule
        print("مكتبة schedule متوفرة")
    except ImportError:
        print("تحذير: مكتبة schedule غير متوفرة. لن يتم تفعيل النسخ الاحتياطي التلقائي.")

    print()
    print("اضغط Enter للخروج...")
    input()

if __name__ == "__main__":
    main()
