<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Nobara - معلومات الترخيص</title>
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <!-- Remixicon -->
    <link href="https://cdn.jsdelivr.net/npm/remixicon@2.5.0/fonts/remixicon.css" rel="stylesheet">
    <!-- Alpine.js -->
    <script defer src="https://cdn.jsdelivr.net/npm/alpinejs@3.x.x/dist/cdn.min.js"></script>
    <script>
        tailwind.config = {
            darkMode: 'class',
            theme: {
                extend: {
                    colors: {
                        primary: {
                            50: '#eff6ff',
                            100: '#dbeafe',
                            200: '#bfdbfe',
                            300: '#93c5fd',
                            400: '#60a5fa',
                            500: '#3b82f6',
                            600: '#2563eb',
                            700: '#1d4ed8',
                            800: '#1e40af',
                            900: '#1e3a8a',
                            950: '#172554',
                        },
                        dark: {
                            100: '#1E293B',
                            200: '#0F172A',
                            300: '#0B1120',
                            400: '#060A15'
                        }
                    }
                }
            }
        }
    </script>
    <style>
        body {
            font-family: 'Cairo', 'Tajawal', sans-serif;
            transition: background-color 0.3s ease, color 0.3s ease;
        }
        
        .bg-pattern {
            background-color: #f9fafb;
            background-image: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%233B82F6' fill-opacity='0.05'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
        }
        
        .dark .bg-pattern {
            background-color: #0F172A;
            background-image: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%233B82F6' fill-opacity='0.1'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
        }
        
        .powered-by {
            position: fixed;
            bottom: 1rem;
            left: 1rem;
            background-color: #ffffff;
            border-radius: 1rem;
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
            padding: 0.75rem 1rem;
            display: flex;
            align-items: center;
            z-index: 40;
            transition: all 0.3s ease;
            border: 1px solid #e5e7eb;
        }
        
        .dark .powered-by {
            background-color: #1E293B;
            border-color: #2D3748;
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.3), 0 4px 6px -2px rgba(0, 0, 0, 0.2);
        }
        
        .powered-by:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
        }
        
        .dark .powered-by:hover {
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.3), 0 10px 10px -5px rgba(0, 0, 0, 0.2);
        }
        
        .license-badge {
            position: relative;
            overflow: hidden;
        }
        
        .license-badge::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, rgba(59, 130, 246, 0.1) 0%, rgba(59, 130, 246, 0) 50%);
            z-index: 0;
        }
        
        .license-badge::after {
            content: '';
            position: absolute;
            width: 200px;
            height: 200px;
            background: radial-gradient(circle, rgba(59, 130, 246, 0.1) 0%, rgba(59, 130, 246, 0) 70%);
            top: -100px;
            right: -100px;
            z-index: 0;
        }
        
        .license-badge > * {
            position: relative;
            z-index: 1;
        }
    </style>
</head>
<body class="bg-pattern dark:bg-dark-200 dark:text-gray-100" x-data="{ darkMode: localStorage.getItem('darkMode') === 'enabled' }">
    <div class="flex h-screen overflow-hidden">
        <!-- Sidebar -->
        {% include 'partials/sidebar.html' %}

        <!-- Main Content -->
        <div class="flex-1 flex flex-col overflow-hidden">
            <!-- Top Navigation -->
            {% include 'partials/topnav.html' %}

            <!-- Content Area -->
            <main class="flex-1 overflow-y-auto p-6">
                <div class="mb-6 flex justify-between items-center">
                    <div>
                        <h1 class="text-2xl font-bold text-gray-800 dark:text-white">معلومات الترخيص</h1>
                        <p class="text-gray-600 dark:text-gray-400">عرض معلومات ترخيص البرنامج وتفعيله</p>
                    </div>
                    <div class="flex items-center gap-2">
                        <a href="{{ url_for('settings.index') }}" class="flex items-center gap-1 bg-gray-100 dark:bg-dark-100 hover:bg-gray-200 dark:hover:bg-dark-300 text-gray-700 dark:text-gray-300 py-2 px-4 rounded-lg transition-colors">
                            <i class="ri-arrow-right-line"></i>
                            <span>العودة للإعدادات</span>
                        </a>
                        <button @click="darkMode = !darkMode; localStorage.setItem('darkMode', darkMode ? 'enabled' : 'disabled'); document.documentElement.classList.toggle('dark')" class="p-2 rounded-full bg-gray-100 dark:bg-dark-100 hover:bg-gray-200 dark:hover:bg-dark-300 transition-colors">
                            <i class="ri-moon-line dark:hidden"></i>
                            <i class="ri-sun-line hidden dark:block"></i>
                        </button>
                    </div>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
                    <!-- معلومات الترخيص -->
                    <div class="md:col-span-2 bg-white dark:bg-dark-100 rounded-xl shadow-sm overflow-hidden">
                        <div class="p-6">
                            <h3 class="text-lg font-medium text-gray-800 dark:text-white mb-4 flex items-center">
                                <i class="ri-shield-check-line ml-2 text-primary-500"></i>
                                معلومات الترخيص
                            </h3>
                            
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div>
                                    <div class="mb-4">
                                        <div class="text-sm text-gray-500 dark:text-gray-400">اسم البرنامج</div>
                                        <div class="text-lg font-medium text-gray-800 dark:text-white">{{ license_info.name }}</div>
                                    </div>
                                    
                                    <div class="mb-4">
                                        <div class="text-sm text-gray-500 dark:text-gray-400">الإصدار</div>
                                        <div class="text-lg font-medium text-gray-800 dark:text-white">{{ license_info.version }}</div>
                                    </div>
                                    
                                    <div class="mb-4">
                                        <div class="text-sm text-gray-500 dark:text-gray-400">مفتاح الترخيص</div>
                                        <div class="text-lg font-medium text-gray-800 dark:text-white">{{ license_info.license_key }}</div>
                                    </div>
                                    
                                    <div class="mb-4">
                                        <div class="text-sm text-gray-500 dark:text-gray-400">حالة الترخيص</div>
                                        <div class="text-lg font-medium {% if license_info.is_valid %}text-green-500 dark:text-green-400{% else %}text-red-500 dark:text-red-400{% endif %}">
                                            {% if license_info.is_valid %}
                                            <i class="ri-checkbox-circle-line"></i> مفعل
                                            {% else %}
                                            <i class="ri-close-circle-line"></i> غير مفعل
                                            {% endif %}
                                        </div>
                                    </div>
                                </div>
                                
                                <div>
                                    <div class="mb-4">
                                        <div class="text-sm text-gray-500 dark:text-gray-400">مرخص لـ</div>
                                        <div class="text-lg font-medium text-gray-800 dark:text-white">{{ license_info.licensed_to }}</div>
                                    </div>
                                    
                                    <div class="mb-4">
                                        <div class="text-sm text-gray-500 dark:text-gray-400">البريد الإلكتروني</div>
                                        <div class="text-lg font-medium text-gray-800 dark:text-white">{{ license_info.email }}</div>
                                    </div>
                                    
                                    <div class="mb-4">
                                        <div class="text-sm text-gray-500 dark:text-gray-400">رقم الهاتف</div>
                                        <div class="text-lg font-medium text-gray-800 dark:text-white">{{ license_info.phone }}</div>
                                    </div>
                                    
                                    <div class="mb-4">
                                        <div class="text-sm text-gray-500 dark:text-gray-400">تاريخ انتهاء الترخيص</div>
                                        <div class="text-lg font-medium text-gray-800 dark:text-white">{{ license_info.expiry_date.strftime('%Y-%m-%d') }}</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- شارة الترخيص -->
                    <div class="bg-white dark:bg-dark-100 rounded-xl shadow-sm overflow-hidden">
                        <div class="p-6">
                            <div class="license-badge bg-gradient-to-br from-primary-50 to-white dark:from-primary-900/20 dark:to-dark-100 rounded-lg p-6 border border-primary-100 dark:border-primary-900/30 flex flex-col items-center justify-center text-center">
                                <div class="w-20 h-20 rounded-full bg-primary-100 dark:bg-primary-900/30 flex items-center justify-center text-primary-600 dark:text-primary-400 mb-4">
                                    <i class="ri-verified-badge-line text-4xl"></i>
                                </div>
                                <h3 class="text-xl font-bold text-primary-700 dark:text-primary-400 mb-2">ترخيص رسمي</h3>
                                <p class="text-gray-600 dark:text-gray-400 mb-4">هذه نسخة مرخصة من برنامج نوبارا</p>
                                <div class="text-sm text-gray-500 dark:text-gray-500">
                                    Powered By <span class="font-bold text-primary-600 dark:text-primary-400">ENG/ Fouad Saber</span>
                                </div>
                                <div class="text-xs text-gray-500 dark:text-gray-500">Tel: 01020073527</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- تفعيل الترخيص -->
                <div class="bg-white dark:bg-dark-100 rounded-xl shadow-sm overflow-hidden mb-6">
                    <div class="p-6">
                        <h3 class="text-lg font-medium text-gray-800 dark:text-white mb-4 flex items-center">
                            <i class="ri-key-2-line ml-2 text-primary-500"></i>
                            تفعيل الترخيص
                        </h3>
                        
                        <form id="activateLicenseForm" action="{{ url_for('settings.activate_license') }}" method="POST">
                            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                                <div class="md:col-span-2">
                                    <label for="license_key" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">مفتاح الترخيص</label>
                                    <input type="text" id="license_key" name="license_key" placeholder="XXXX-XXXX-XXXX-XXXX" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 dark:bg-dark-200 dark:text-gray-100">
                                </div>
                                
                                <div class="flex items-end">
                                    <button type="submit" class="w-full bg-primary-500 hover:bg-primary-600 text-white py-2 px-4 rounded-lg inline-flex items-center justify-center transition-colors">
                                        <i class="ri-shield-keyhole-line ml-1"></i>
                                        <span>تفعيل</span>
                                    </button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
                
                <!-- معلومات الدعم الفني -->
                <div class="bg-white dark:bg-dark-100 rounded-xl shadow-sm overflow-hidden">
                    <div class="p-6">
                        <h3 class="text-lg font-medium text-gray-800 dark:text-white mb-4 flex items-center">
                            <i class="ri-customer-service-2-line ml-2 text-primary-500"></i>
                            الدعم الفني
                        </h3>
                        
                        <div class="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4 border border-blue-100 dark:border-blue-900/30">
                            <p class="text-gray-700 dark:text-gray-300 mb-4">
                                للحصول على مساعدة أو استفسار حول الترخيص، يرجى التواصل مع فريق الدعم الفني:
                            </p>
                            
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div class="flex items-center">
                                    <div class="w-10 h-10 rounded-full bg-blue-100 dark:bg-blue-900/30 flex items-center justify-center text-blue-600 dark:text-blue-400 ml-3">
                                        <i class="ri-phone-line"></i>
                                    </div>
                                    <div>
                                        <div class="text-sm text-gray-500 dark:text-gray-400">الهاتف</div>
                                        <div class="text-gray-700 dark:text-gray-300">01020073527</div>
                                    </div>
                                </div>
                                
                                <div class="flex items-center">
                                    <div class="w-10 h-10 rounded-full bg-blue-100 dark:bg-blue-900/30 flex items-center justify-center text-blue-600 dark:text-blue-400 ml-3">
                                        <i class="ri-mail-line"></i>
                                    </div>
                                    <div>
                                        <div class="text-sm text-gray-500 dark:text-gray-400">البريد الإلكتروني</div>
                                        <div class="text-gray-700 dark:text-gray-300"><EMAIL></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>
    
    <!-- Powered By -->
    <div class="powered-by">
        <div class="w-8 h-8 rounded-full bg-primary-50 dark:bg-primary-900/50 flex items-center justify-center ml-2 text-primary-600 dark:text-primary-400">
            <i class="ri-code-s-slash-line"></i>
        </div>
        <div>
            <div class="text-xs text-gray-600 dark:text-gray-400">
                Powered By <span class="font-bold text-primary-600 dark:text-primary-400">ENG/ Fouad Saber</span>
            </div>
            <div class="text-xs text-gray-500 dark:text-gray-500">Tel: 01020073527</div>
        </div>
    </div>
    
    <script>
        // تفعيل وضع الظلام إذا كان مخزنًا
        if (localStorage.getItem('darkMode') === 'enabled') {
            document.documentElement.classList.add('dark');
        }
        
        document.addEventListener('DOMContentLoaded', function() {
            // إرسال نموذج تفعيل الترخيص
            document.getElementById('activateLicenseForm').addEventListener('submit', function(e) {
                e.preventDefault();
                
                const licenseKey = document.getElementById('license_key').value.trim();
                if (!licenseKey) {
                    alert('يرجى إدخال مفتاح الترخيص');
                    return;
                }
                
                fetch(this.action, {
                    method: 'POST',
                    body: new FormData(this),
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        alert("تم تفعيل الترخيص بنجاح");
                        location.reload();
                    } else {
                        alert("حدث خطأ أثناء تفعيل الترخيص: " + data.error);
                    }
                })
                .catch(error => {
                    alert("حدث خطأ أثناء الاتصال بالخادم");
                    console.error(error);
                });
            });
        });
    </script>
</body>
</html>
