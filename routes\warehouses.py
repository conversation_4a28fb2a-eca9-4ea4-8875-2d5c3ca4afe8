
from flask import Blueprint, render_template, request, jsonify, flash, redirect, url_for, Response
from flask_login import login_required, current_user
from models import Warehouse, Inventory, Product, Category, InventoryMovement, InventoryAlert, InventoryCount, InventoryCountItem, User
from app import db
from sqlalchemy import or_, func, and_, text
import csv
from io import StringIO
from datetime import datetime, timedelta

warehouses_blueprint = Blueprint('warehouses', __name__)

@warehouses_blueprint.route('/warehouses')
@login_required
def index():
    # الحصول على معلمات البحث
    search = request.args.get('search', '')
    status = request.args.get('status', 'all')  # all, active, inactive

    # إعداد الاستعلام
    query = Warehouse.query

    # تطبيق البحث
    if search:
        # استخدام الدالة الجديدة للبحث
        warehouses = Warehouse.search_by_name_or_location(search)
        return render_template('warehouses/warehouses.html', warehouses=warehouses, current_user=current_user, search=search, status=status)

    # تطبيق فلتر الحالة
    if status == 'active':
        query = query.filter(Warehouse.is_active == True)
    elif status == 'inactive':
        query = query.filter(Warehouse.is_active == False)

    # تنفيذ الاستعلام مع الترتيب (تجنب استخدام عمود updated_at)
    warehouses = query.order_by(Warehouse.name).all()

    # إحصائيات المخازن
    stats = {
        'total': Warehouse.query.count(),
        'active': Warehouse.query.filter_by(is_active=True).count(),
        'inactive': Warehouse.query.filter_by(is_active=False).count(),
    }

    return render_template('warehouses/warehouses.html',
                          warehouses=warehouses,
                          current_user=current_user,
                          search=search,
                          status=status,
                          stats=stats)

@warehouses_blueprint.route('/warehouses/create', methods=['GET', 'POST'])
@login_required
def create():
    if request.method == 'POST':
        try:
            warehouse = Warehouse(
                name=request.form.get('name'),
                location=request.form.get('location'),
                description=request.form.get('description'),
                is_active=True if request.form.get('is_active') == 'on' else False
            )
            db.session.add(warehouse)
            db.session.commit()
            flash('تم إضافة المخزن بنجاح', 'success')
            return redirect(url_for('warehouses.index'))
        except Exception as e:
            db.session.rollback()
            flash(f'حدث خطأ: {str(e)}', 'danger')

    return render_template('warehouses/warehouse_form.html', warehouse=None, action='create')

@warehouses_blueprint.route('/warehouses/<int:id>/inventory')
@login_required
def inventory(id):
    warehouse = Warehouse.query.get_or_404(id)

    # الحصول على معلمات البحث
    search = request.args.get('search', '')
    status = request.args.get('status', 'all')  # all, in_stock, low_stock, out_of_stock

    # استعلام المخزون
    inventory_query = Inventory.query.filter_by(warehouse_id=id)

    # استعلام المنتجات للقائمة المنسدلة
    products_query = Product.query.filter_by(is_active=True)

    # تطبيق البحث على المخزون
    if search:
        # استخدام الدالة الجديدة للبحث عن المنتجات
        products = Product.search_by_name_or_code(search)
        product_ids = [p.id for p in products]

        if product_ids:
            inventory_query = inventory_query.filter(Inventory.product_id.in_(product_ids))
        else:
            # إذا لم يتم العثور على منتجات، إرجاع قائمة فارغة
            inventory_query = inventory_query.filter(Inventory.id == -1)

    # تطبيق فلتر الحالة
    if status == 'in_stock':
        inventory_query = inventory_query.filter(Inventory.quantity > Inventory.minimum_stock)
    elif status == 'low_stock':
        inventory_query = inventory_query.filter(Inventory.quantity > 0, Inventory.quantity <= Inventory.minimum_stock)
    elif status == 'out_of_stock':
        inventory_query = inventory_query.filter(Inventory.quantity <= 0)

    # تنفيذ الاستعلامات
    inventories = inventory_query.all()
    products = products_query.order_by(Product.name).all()

    # الحصول على قائمة المخازن النشطة للنقل
    warehouses = Warehouse.query.filter_by(is_active=True).all()

    # إحصائيات المخزون
    stats = {
        'total_products': len(warehouse.inventories),
        'in_stock': sum(1 for inv in warehouse.inventories if inv.quantity > inv.minimum_stock),
        'low_stock': warehouse.get_low_stock_count(),
        'out_of_stock': warehouse.get_out_of_stock_count()
    }

    return render_template('warehouses/warehouse_inventory.html',
                         warehouse=warehouse,
                         inventories=inventories,
                         products=products,
                         warehouses=warehouses,
                         current_user=current_user,
                         search=search,
                         status=status,
                         stats=stats)

@warehouses_blueprint.route('/warehouses/<int:id>/inventory/update', methods=['POST'])
@login_required
def update_inventory(id):
    try:
        product_id = request.form.get('product_id')
        quantity = int(request.form.get('quantity', 0))
        minimum_stock = int(request.form.get('minimum_stock', 5))
        notes = request.form.get('notes', '')

        inventory = Inventory.query.filter_by(
            warehouse_id=id,
            product_id=product_id
        ).first()

        if inventory:
            # تحديث المخزون الحالي وتسجيل الحركة
            movement_type = 'add' if quantity > inventory.quantity else 'remove'
            inventory.update_quantity(
                new_quantity=quantity,
                movement_type=movement_type,
                user_id=current_user.id,
                reference='manual_update',
                notes=notes
            )
            inventory.minimum_stock = minimum_stock
        else:
            # إنشاء مخزون جديد
            inventory = Inventory(
                warehouse_id=id,
                product_id=product_id,
                quantity=0,  # سيتم تحديثه لاحقاً
                minimum_stock=minimum_stock
            )
            db.session.add(inventory)
            db.session.flush()  # للحصول على معرف المخزون

            # تحديث الكمية وتسجيل الحركة
            inventory.update_quantity(
                new_quantity=quantity,
                movement_type='add',
                user_id=current_user.id,
                reference='initial_stock',
                notes=notes
            )

        db.session.commit()
        flash('تم تحديث المخزون بنجاح', 'success')
    except Exception as e:
        db.session.rollback()
        flash(f'حدث خطأ: {str(e)}', 'danger')

    return redirect(url_for('warehouses.inventory', id=id))

@warehouses_blueprint.route('/api/warehouses/<int:id>/inventory')
@login_required
def api_inventory(id):
    """الحصول على قائمة المخزون لمخزن محدد"""
    try:
        # الحصول على معلمات البحث
        search = request.args.get('search', '')

        # استعلام المخزون
        query = Inventory.query.filter_by(warehouse_id=id)

        # تطبيق البحث
        if search:
            # البحث في المنتجات المرتبطة بالمخزون
            product_ids = Product.query.filter(
                (Product.name.ilike(f'%{search}%') | Product.code.ilike(f'%{search}%')) &
                (Product.is_active == True)
            ).with_entities(Product.id).all()

            product_ids = [p.id for p in product_ids]

            if product_ids:
                query = query.filter(Inventory.product_id.in_(product_ids))
            else:
                # إذا لم يتم العثور على منتجات، إرجاع قائمة فارغة
                return jsonify({'inventories': []})

        # تنفيذ الاستعلام
        inventories = query.all()

        # تحويل النتائج إلى قاموس
        result = []
        for inventory in inventories:
            product = inventory.product
            result.append({
                'id': inventory.id,
                'product_id': product.id,
                'product_name': product.name,
                'product_code': product.code,
                'quantity': inventory.quantity,
                'minimum_stock': inventory.minimum_stock,
                'status': 'out_of_stock' if inventory.quantity <= 0 else 'low_stock' if inventory.quantity <= inventory.minimum_stock else 'in_stock'
            })

        # إرجاع النتائج
        return jsonify({'inventories': result})
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@warehouses_blueprint.route('/warehouses/<int:id>/inventory/transfer', methods=['POST'])
@login_required
def transfer_inventory(id):
    """نقل المخزون بين المخازن"""
    try:
        product_id = request.form.get('product_id')
        target_warehouse_id = request.form.get('target_warehouse_id')
        quantity = int(request.form.get('quantity', 0))
        notes = request.form.get('notes', '')

        if not product_id or not target_warehouse_id or quantity <= 0:
            flash('يرجى تحديد المنتج والمخزن المستهدف والكمية', 'warning')
            return redirect(url_for('warehouses.inventory', id=id))

        # التحقق من وجود المنتج في المخزن المصدر
        source_inventory = Inventory.query.filter_by(
            warehouse_id=id,
            product_id=product_id
        ).first()

        if not source_inventory or source_inventory.quantity < quantity:
            flash('الكمية المطلوبة غير متوفرة في المخزن', 'danger')
            return redirect(url_for('warehouses.inventory', id=id))

        # البحث عن المنتج في المخزن المستهدف
        target_inventory = Inventory.query.filter_by(
            warehouse_id=target_warehouse_id,
            product_id=product_id
        ).first()

        # بدء المعاملة
        try:
            # إنشاء مرجع للنقل
            transfer_reference = f'transfer_{datetime.now().strftime("%Y%m%d%H%M%S")}'

            # تقليل الكمية من المخزن المصدر وتسجيل الحركة
            new_source_quantity = source_inventory.quantity - quantity
            source_inventory.update_quantity(
                new_quantity=new_source_quantity,
                movement_type='transfer_out',
                user_id=current_user.id,
                reference=transfer_reference,
                notes=f'نقل إلى {Warehouse.query.get(target_warehouse_id).name}: {notes}'
            )

            # إضافة الكمية إلى المخزن المستهدف
            if target_inventory:
                # تحديث المخزون الحالي وتسجيل الحركة
                new_target_quantity = target_inventory.quantity + quantity
                target_inventory.update_quantity(
                    new_quantity=new_target_quantity,
                    movement_type='transfer_in',
                    user_id=current_user.id,
                    reference=transfer_reference,
                    notes=f'نقل من {Warehouse.query.get(id).name}: {notes}'
                )
            else:
                # إنشاء مخزون جديد في المخزن المستهدف
                target_inventory = Inventory(
                    warehouse_id=target_warehouse_id,
                    product_id=product_id,
                    quantity=0,  # سيتم تحديثه لاحقاً
                    minimum_stock=source_inventory.minimum_stock
                )
                db.session.add(target_inventory)
                db.session.flush()  # للحصول على معرف المخزون

                # تحديث الكمية وتسجيل الحركة
                target_inventory.update_quantity(
                    new_quantity=quantity,
                    movement_type='transfer_in',
                    user_id=current_user.id,
                    reference=transfer_reference,
                    notes=f'نقل من {Warehouse.query.get(id).name}: {notes}'
                )

            db.session.commit()
            flash(f'تم نقل {quantity} وحدة من المنتج بنجاح', 'success')
        except Exception as e:
            db.session.rollback()
            flash(f'حدث خطأ أثناء نقل المخزون: {str(e)}', 'danger')

        return redirect(url_for('warehouses.inventory', id=id))
    except Exception as e:
        flash(f'حدث خطأ: {str(e)}', 'danger')
        return redirect(url_for('warehouses.inventory', id=id))

@warehouses_blueprint.route('/warehouses/reports')
@login_required
def reports():
    """صفحة تقارير المخازن"""
    # الحصول على معلمات الفلتر
    warehouse_id = request.args.get('warehouse_id', '')
    report_type = request.args.get('report_type', 'inventory_status')
    date_from = request.args.get('date_from', '')
    date_to = request.args.get('date_to', '')

    # الحصول على قائمة المخازن
    warehouses = Warehouse.query.filter_by(is_active=True).all()

    # تحضير البيانات حسب نوع التقرير
    report_data = {}

    if report_type == 'inventory_status':
        # تقرير حالة المخزون
        report_data = generate_inventory_status_report(warehouse_id)
    elif report_type == 'inventory_value':
        # تقرير قيمة المخزون
        report_data = generate_inventory_value_report(warehouse_id)
    elif report_type == 'inventory_movement':
        # تقرير حركة المخزون
        report_data = generate_inventory_movement_report(warehouse_id, date_from, date_to)
    elif report_type == 'low_stock':
        # تقرير المنتجات منخفضة المخزون
        report_data = generate_low_stock_report(warehouse_id)
    elif report_type == 'out_of_stock':
        # تقرير المنتجات النافذة من المخزون
        report_data = generate_out_of_stock_report(warehouse_id)

    return render_template('warehouses/warehouse_reports_new.html',
                          warehouses=warehouses,
                          warehouse_id=warehouse_id,
                          report_type=report_type,
                          date_from=date_from,
                          date_to=date_to,
                          report_data=report_data)

@warehouses_blueprint.route('/warehouses/movements')
@login_required
def movements():
    """عرض حركة المخزون"""
    # الحصول على معلمات الفلتر
    warehouse_id = request.args.get('warehouse_id', '')
    product_id = request.args.get('product_id', '')
    movement_type = request.args.get('movement_type', '')
    start_date = request.args.get('start_date', '')
    end_date = request.args.get('end_date', '')
    page = int(request.args.get('page', 1))
    per_page = 50  # عدد العناصر في الصفحة الواحدة

    # إعداد الاستعلام
    query = InventoryMovement.query

    # تطبيق الفلاتر
    if warehouse_id and warehouse_id.isdigit():
        query = query.filter_by(warehouse_id=int(warehouse_id))

    if product_id and product_id.isdigit():
        query = query.filter_by(product_id=int(product_id))

    if movement_type:
        query = query.filter_by(movement_type=movement_type)

    if start_date:
        start_datetime = datetime.strptime(start_date, '%Y-%m-%d')
        query = query.filter(InventoryMovement.created_at >= start_datetime)

    if end_date:
        end_datetime = datetime.strptime(end_date, '%Y-%m-%d') + timedelta(days=1)  # لتضمين اليوم الأخير كاملاً
        query = query.filter(InventoryMovement.created_at < end_datetime)

    # حساب إجمالي العناصر للترقيم
    total_items = query.count()
    total_pages = (total_items + per_page - 1) // per_page  # تقريب لأعلى

    # تطبيق الترتيب والترقيم
    movements = query.order_by(InventoryMovement.created_at.desc()).offset((page - 1) * per_page).limit(per_page).all()

    # الحصول على قوائم المخازن والمنتجات
    warehouses = Warehouse.query.filter_by(is_active=True).all()
    products = Product.query.filter_by(is_active=True).all()

    return render_template('warehouses/warehouse_movements.html',
                          movements=movements,
                          warehouses=warehouses,
                          products=products,
                          warehouse_id=warehouse_id,
                          product_id=product_id,
                          movement_type=movement_type,
                          start_date=start_date,
                          end_date=end_date,
                          page=page,
                          total_pages=total_pages)

@warehouses_blueprint.route('/api/warehouses/movements/export')
@login_required
def export_movements():
    """تصدير حركة المخزون إلى ملف CSV"""
    # الحصول على معلمات الفلتر
    warehouse_id = request.args.get('warehouse_id', '')
    product_id = request.args.get('product_id', '')
    movement_type = request.args.get('movement_type', '')
    start_date = request.args.get('start_date', '')
    end_date = request.args.get('end_date', '')

    # إعداد الاستعلام
    query = InventoryMovement.query

    # تطبيق الفلاتر
    if warehouse_id and warehouse_id.isdigit():
        query = query.filter_by(warehouse_id=int(warehouse_id))

    if product_id and product_id.isdigit():
        query = query.filter_by(product_id=int(product_id))

    if movement_type:
        query = query.filter_by(movement_type=movement_type)

    if start_date:
        start_datetime = datetime.strptime(start_date, '%Y-%m-%d')
        query = query.filter(InventoryMovement.created_at >= start_datetime)

    if end_date:
        end_datetime = datetime.strptime(end_date, '%Y-%m-%d') + timedelta(days=1)  # لتضمين اليوم الأخير كاملاً
        query = query.filter(InventoryMovement.created_at < end_datetime)

    # تنفيذ الاستعلام
    movements = query.order_by(InventoryMovement.created_at.desc()).all()

    # إنشاء ملف CSV
    output = StringIO()
    writer = csv.writer(output)

    # كتابة الترويسة
    headers = ['التاريخ', 'المنتج', 'المخزن', 'نوع الحركة', 'الكمية', 'الكمية السابقة', 'الكمية الجديدة', 'المستخدم', 'ملاحظات']
    writer.writerow(headers)

    # كتابة البيانات
    for movement in movements:
        movement_type_ar = {
            'add': 'إضافة',
            'remove': 'سحب',
            'transfer_in': 'نقل وارد',
            'transfer_out': 'نقل صادر'
        }.get(movement.movement_type, movement.movement_type)

        writer.writerow([
            movement.created_at.strftime('%Y-%m-%d %H:%M'),
            movement.product.name,
            movement.warehouse.name,
            movement_type_ar,
            movement.quantity,
            movement.previous_quantity,
            movement.new_quantity,
            movement.user.username if movement.user else '-',
            movement.notes or '-'
        ])

    # إعداد الاستجابة
    output.seek(0)
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    return Response(
        output.getvalue(),
        mimetype='text/csv',
        headers={
            'Content-Disposition': f'attachment; filename=inventory_movements_{timestamp}.csv'
        }
    )

@warehouses_blueprint.route('/api/warehouses/reports/export')
@login_required
def export_report():
    """تصدير تقارير المخزون إلى ملف CSV"""
    # الحصول على معلمات الفلتر
    warehouse_id = request.args.get('warehouse_id', '')
    category_id = request.args.get('category_id', '')
    search = request.args.get('search', '')
    report_type = request.args.get('report_type', 'low_stock')

    # إعداد الاستعلام حسب نوع التقرير
    if report_type == 'low_stock':
        query = Inventory.query.filter(
            Inventory.quantity > 0,
            Inventory.quantity <= Inventory.minimum_stock
        )
        filename = 'low_stock_report'
        headers = ['المنتج', 'الباركود', 'المخزن', 'الكمية', 'الحد الأدنى']
    elif report_type == 'out_of_stock':
        query = Inventory.query.filter(
            Inventory.quantity <= 0
        )
        filename = 'out_of_stock_report'
        headers = ['المنتج', 'الباركود', 'المخزن', 'الحد الأدنى']
    else:  # inventory_value
        query = Inventory.query.filter(
            Inventory.quantity > 0
        )
        filename = 'inventory_value_report'
        headers = ['المنتج', 'الباركود', 'المخزن', 'الكمية', 'سعر التكلفة', 'القيمة الإجمالية']

    # تطبيق الفلاتر
    if warehouse_id and warehouse_id.isdigit():
        query = query.filter_by(warehouse_id=int(warehouse_id))

    if category_id and category_id.isdigit():
        query = query.join(Product).filter(Product.category_id == int(category_id))

    if search:
        query = query.join(Product).filter(
            or_(Product.name.ilike(f'%{search}%'), Product.code.ilike(f'%{search}%'))
        )

    # تنفيذ الاستعلام
    items = query.all()

    # إنشاء ملف CSV
    output = StringIO()
    writer = csv.writer(output)

    # كتابة الترويسة
    writer.writerow(headers)

    # كتابة البيانات
    for item in items:
        if report_type == 'low_stock':
            writer.writerow([
                item.product.name,
                item.product.code or '-',
                item.warehouse.name,
                item.quantity,
                item.minimum_stock
            ])
        elif report_type == 'out_of_stock':
            writer.writerow([
                item.product.name,
                item.product.code or '-',
                item.warehouse.name,
                item.minimum_stock
            ])
        else:  # inventory_value
            total_value = item.quantity * (item.product.cost_price or 0)
            writer.writerow([
                item.product.name,
                item.product.code or '-',
                item.warehouse.name,
                item.quantity,
                item.product.cost_price or 0,
                total_value
            ])

    # إضافة صف إجمالي لتقرير قيمة المخزون
    if report_type == 'inventory_value' and items:
        total_value = sum(item.quantity * (item.product.cost_price or 0) for item in items)
        writer.writerow(['', '', '', '', 'الإجمالي:', total_value])

    # إعداد الاستجابة
    output.seek(0)
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    return Response(
        output.getvalue(),
        mimetype='text/csv',
        headers={
            'Content-Disposition': f'attachment; filename={filename}_{timestamp}.csv'
        }
    )

@warehouses_blueprint.route('/warehouses/alerts')
@login_required
def alerts():
    """عرض تنبيهات المخزون"""
    # الحصول على معلمات الفلتر
    warehouse_id = request.args.get('warehouse_id', '')
    alert_type = request.args.get('alert_type', '')
    is_resolved = request.args.get('is_resolved', '')
    page = int(request.args.get('page', 1))
    per_page = 20  # عدد العناصر في الصفحة الواحدة

    # إعداد الاستعلام
    query = InventoryAlert.query.join(Inventory)

    # تطبيق الفلاتر
    if warehouse_id and warehouse_id.isdigit():
        query = query.filter(Inventory.warehouse_id == int(warehouse_id))

    if alert_type:
        query = query.filter(InventoryAlert.alert_type == alert_type)

    if is_resolved:
        if is_resolved == 'yes':
            query = query.filter(InventoryAlert.is_resolved == True)
        elif is_resolved == 'no':
            query = query.filter(InventoryAlert.is_resolved == False)

    # حساب إجمالي العناصر للترقيم
    total_items = query.count()
    total_pages = (total_items + per_page - 1) // per_page  # تقريب لأعلى

    # تطبيق الترتيب والترقيم
    alerts = query.order_by(InventoryAlert.created_at.desc()).offset((page - 1) * per_page).limit(per_page).all()

    # الحصول على قوائم المخازن
    warehouses = Warehouse.query.filter_by(is_active=True).all()

    # إحصائيات التنبيهات
    unread_count = InventoryAlert.query.filter_by(is_read=False).count()
    unresolved_count = InventoryAlert.query.filter_by(is_resolved=False).count()
    low_stock_count = InventoryAlert.query.filter_by(alert_type='low_stock', is_resolved=False).count()
    out_of_stock_count = InventoryAlert.query.filter_by(alert_type='out_of_stock', is_resolved=False).count()

    stats = {
        'unread': unread_count,
        'unresolved': unresolved_count,
        'low_stock': low_stock_count,
        'out_of_stock': out_of_stock_count
    }

    return render_template('warehouse_alerts.html',
                          alerts=alerts,
                          warehouses=warehouses,
                          warehouse_id=warehouse_id,
                          alert_type=alert_type,
                          is_resolved=is_resolved,
                          page=page,
                          total_pages=total_pages,
                          stats=stats)

@warehouses_blueprint.route('/warehouses/alerts/<int:id>/mark-read', methods=['POST'])
@login_required
def mark_alert_read(id):
    """تحديد التنبيه كمقروء"""
    alert = InventoryAlert.query.get_or_404(id)
    alert.is_read = True
    db.session.commit()
    return jsonify({'success': True})

@warehouses_blueprint.route('/warehouses/alerts/<int:id>/mark-resolved', methods=['POST'])
@login_required
def mark_alert_resolved(id):
    """تحديد التنبيه كمحلول"""
    alert = InventoryAlert.query.get_or_404(id)
    alert.is_resolved = True
    alert.resolved_at = datetime.utcnow()
    db.session.commit()
    return jsonify({'success': True})

@warehouses_blueprint.route('/warehouses/alerts/mark-all-read', methods=['POST'])
@login_required
def mark_all_alerts_read():
    """تحديد جميع التنبيهات كمقروءة"""
    InventoryAlert.query.filter_by(is_read=False).update({'is_read': True})
    db.session.commit()
    return jsonify({'success': True})

@warehouses_blueprint.route('/api/warehouses/alerts/count')
@login_required
def api_alerts_count():
    """الحصول على عدد التنبيهات غير المقروءة"""
    unread_count = InventoryAlert.query.filter_by(is_read=False).count()
    return jsonify({'count': unread_count})

@warehouses_blueprint.route('/warehouses/counts')
@login_required
def inventory_counts():
    """عرض قائمة عمليات الجرد"""
    # الحصول على معلمات الفلتر
    warehouse_id = request.args.get('warehouse_id', '')
    status = request.args.get('status', '')
    page = int(request.args.get('page', 1))
    per_page = 20  # عدد العناصر في الصفحة الواحدة

    # إعداد الاستعلام
    query = InventoryCount.query

    # تطبيق الفلاتر
    if warehouse_id and warehouse_id.isdigit():
        query = query.filter_by(warehouse_id=int(warehouse_id))

    if status:
        query = query.filter_by(status=status)

    # حساب إجمالي العناصر للترقيم
    total_items = query.count()
    total_pages = (total_items + per_page - 1) // per_page  # تقريب لأعلى

    # تطبيق الترتيب والترقيم
    counts = query.order_by(InventoryCount.created_at.desc()).offset((page - 1) * per_page).limit(per_page).all()

    # الحصول على قوائم المخازن
    warehouses = Warehouse.query.filter_by(is_active=True).all()

    # إحصائيات الجرد
    draft_count = InventoryCount.query.filter_by(status='draft').count()
    in_progress_count = InventoryCount.query.filter_by(status='in_progress').count()
    completed_count = InventoryCount.query.filter_by(status='completed').count()

    stats = {
        'draft': draft_count,
        'in_progress': in_progress_count,
        'completed': completed_count,
        'total': total_items
    }

    return render_template('warehouse_counts.html',
                          counts=counts,
                          warehouses=warehouses,
                          warehouse_id=warehouse_id,
                          status=status,
                          page=page,
                          total_pages=total_pages,
                          stats=stats)

@warehouses_blueprint.route('/warehouses/counts/create', methods=['GET', 'POST'])
@login_required
def create_inventory_count():
    """إنشاء عملية جرد جديدة"""
    if request.method == 'POST':
        warehouse_id = request.form.get('warehouse_id')
        name = request.form.get('name')
        notes = request.form.get('notes')

        if not warehouse_id or not name:
            flash('يرجى تحديد المخزن واسم عملية الجرد', 'warning')
            return redirect(url_for('warehouses.create_inventory_count'))

        try:
            # إنشاء عملية جرد جديدة
            count = InventoryCount(
                warehouse_id=warehouse_id,
                name=name,
                notes=notes,
                status='draft',
                created_by=current_user.id
            )
            db.session.add(count)
            db.session.flush()  # للحصول على معرف عملية الجرد

            # إضافة جميع المنتجات في المخزن إلى عملية الجرد
            inventories = Inventory.query.filter_by(warehouse_id=warehouse_id).all()

            for inventory in inventories:
                count_item = InventoryCountItem(
                    count_id=count.id,
                    product_id=inventory.product_id,
                    expected_quantity=inventory.quantity
                )
                db.session.add(count_item)

            db.session.commit()
            flash('تم إنشاء عملية الجرد بنجاح', 'success')
            return redirect(url_for('warehouses.view_inventory_count', id=count.id))
        except Exception as e:
            db.session.rollback()
            flash(f'حدث خطأ: {str(e)}', 'danger')

    # الحصول على قوائم المخازن
    warehouses = Warehouse.query.filter_by(is_active=True).all()

    return render_template('warehouse_count_form.html',
                          warehouses=warehouses,
                          count=None,
                          action='create')

@warehouses_blueprint.route('/warehouses/counts/<int:id>')
@login_required
def view_inventory_count(id):
    """عرض تفاصيل عملية الجرد"""
    count = InventoryCount.query.get_or_404(id)

    # الحصول على عناصر الجرد
    count_items = InventoryCountItem.query.filter_by(count_id=id).all()

    # إحصائيات عملية الجرد
    total_items = len(count_items)
    counted_items = sum(1 for item in count_items if item.actual_quantity is not None)
    discrepancy_items = sum(1 for item in count_items if item.difference != 0 and item.difference is not None)

    stats = {
        'total_items': total_items,
        'counted_items': counted_items,
        'discrepancy_items': discrepancy_items,
        'progress': (counted_items / total_items * 100) if total_items > 0 else 0
    }

    return render_template('warehouse_count_view.html',
                          count=count,
                          count_items=count_items,
                          stats=stats)

@warehouses_blueprint.route('/warehouses/counts/<int:id>/update-item', methods=['POST'])
@login_required
def update_count_item(id):
    """تحديث عنصر في عملية الجرد"""
    count = InventoryCount.query.get_or_404(id)

    # التحقق من أن عملية الجرد في حالة تسمح بالتعديل
    if count.status not in ['draft', 'in_progress']:
        flash('لا يمكن تعديل عملية الجرد في الحالة الحالية', 'warning')
        return redirect(url_for('warehouses.view_inventory_count', id=id))

    item_id = request.form.get('item_id')
    actual_quantity = request.form.get('actual_quantity')
    notes = request.form.get('notes')

    if not item_id or not actual_quantity:
        flash('يرجى تحديد العنصر والكمية الفعلية', 'warning')
        return redirect(url_for('warehouses.view_inventory_count', id=id))

    try:
        # تحديث عنصر الجرد
        item = InventoryCountItem.query.get_or_404(item_id)
        item.actual_quantity = int(actual_quantity)
        item.difference = item.actual_quantity - item.expected_quantity
        item.notes = notes

        # تحديث حالة عملية الجرد إلى قيد التنفيذ إذا كانت مسودة
        if count.status == 'draft':
            count.status = 'in_progress'

        db.session.commit()
        flash('تم تحديث عنصر الجرد بنجاح', 'success')
    except Exception as e:
        db.session.rollback()
        flash(f'حدث خطأ: {str(e)}', 'danger')

    return redirect(url_for('warehouses.view_inventory_count', id=id))

@warehouses_blueprint.route('/warehouses/counts/<int:id>/complete', methods=['POST'])
@login_required
def complete_inventory_count(id):
    """إكمال عملية الجرد وتطبيق التغييرات"""
    count = InventoryCount.query.get_or_404(id)

    # التحقق من أن عملية الجرد في حالة تسمح بالإكمال
    if count.status not in ['in_progress']:
        flash('لا يمكن إكمال عملية الجرد في الحالة الحالية', 'warning')
        return redirect(url_for('warehouses.view_inventory_count', id=id))

    apply_changes = request.form.get('apply_changes') == 'yes'

    try:
        # تحديث حالة عملية الجرد إلى مكتملة
        count.status = 'completed'
        count.completed_at = datetime.utcnow()

        # تطبيق التغييرات على المخزون إذا تم اختيار ذلك
        if apply_changes:
            count_items = InventoryCountItem.query.filter_by(count_id=id).all()

            for item in count_items:
                if item.actual_quantity is not None and item.difference != 0:
                    # البحث عن المنتج في المخزن
                    inventory = Inventory.query.filter_by(
                        warehouse_id=count.warehouse_id,
                        product_id=item.product_id
                    ).first()

                    if inventory:
                        # تحديث كمية المخزون وتسجيل الحركة
                        movement_type = 'add' if item.difference > 0 else 'remove'
                        inventory.update_quantity(
                            new_quantity=item.actual_quantity,
                            movement_type=movement_type,
                            user_id=current_user.id,
                            reference=f'inventory_count_{count.id}',
                            notes=f'تعديل بناءً على عملية الجرد: {count.name}'
                        )

        db.session.commit()
        flash('تم إكمال عملية الجرد بنجاح', 'success')
    except Exception as e:
        db.session.rollback()
        flash(f'حدث خطأ: {str(e)}', 'danger')

    return redirect(url_for('warehouses.view_inventory_count', id=id))

@warehouses_blueprint.route('/warehouses/counts/<int:id>/cancel', methods=['POST'])
@login_required
def cancel_inventory_count(id):
    """إلغاء عملية الجرد"""
    count = InventoryCount.query.get_or_404(id)

    # التحقق من أن عملية الجرد في حالة تسمح بالإلغاء
    if count.status in ['completed']:
        flash('لا يمكن إلغاء عملية الجرد المكتملة', 'warning')
        return redirect(url_for('warehouses.view_inventory_count', id=id))

    try:
        # تحديث حالة عملية الجرد إلى ملغاة
        count.status = 'cancelled'
        db.session.commit()
        flash('تم إلغاء عملية الجرد بنجاح', 'success')
    except Exception as e:
        db.session.rollback()
        flash(f'حدث خطأ: {str(e)}', 'danger')

    return redirect(url_for('warehouses.inventory_counts'))

@warehouses_blueprint.route('/warehouses/dashboard')
@login_required
def dashboard():
    """لوحة تحكم المخزون"""
    # إحصائيات عامة
    total_products = Product.query.filter_by(is_active=True).count()
    total_warehouses = Warehouse.query.filter_by(is_active=True).count()

    # إحصائيات المخزون
    low_stock_count = db.session.query(Inventory).filter(
        Inventory.quantity > 0,
        Inventory.quantity <= Inventory.minimum_stock
    ).count()

    out_of_stock_count = db.session.query(Inventory).filter(
        Inventory.quantity <= 0
    ).count()

    stats = {
        'total_products': total_products,
        'total_warehouses': total_warehouses,
        'low_stock_count': low_stock_count,
        'out_of_stock_count': out_of_stock_count
    }

    # المنتجات الأكثر مخزوناً
    top_inventory = db.session.query(Inventory).filter(
        Inventory.quantity > 0
    ).order_by(Inventory.quantity.desc()).limit(5).all()

    # المنتجات ذات المخزون المنخفض
    low_inventory = db.session.query(Inventory).filter(
        db.or_(
            db.and_(Inventory.quantity > 0, Inventory.quantity <= Inventory.minimum_stock),
            Inventory.quantity <= 0
        )
    ).order_by(Inventory.quantity).limit(5).all()

    # بيانات الرسومات البيانية

    # توزيع المخزون حسب المخازن
    warehouses = Warehouse.query.filter_by(is_active=True).all()
    warehouse_labels = [w.name for w in warehouses]
    warehouse_data = []

    for warehouse in warehouses:
        count = Inventory.query.filter_by(warehouse_id=warehouse.id).filter(Inventory.quantity > 0).count()
        warehouse_data.append(count)

    # حالة المخزون
    in_stock_count = db.session.query(Inventory).filter(
        Inventory.quantity > Inventory.minimum_stock
    ).count()

    status_data = {
        'labels': ['مخزون كافي', 'مخزون منخفض', 'نفذ من المخزون'],
        'data': [in_stock_count, low_stock_count, out_of_stock_count]
    }

    # قيمة المخزون حسب المخازن
    value_labels = []
    value_data = []

    for warehouse in warehouses:
        inventory_items = Inventory.query.filter_by(warehouse_id=warehouse.id).filter(Inventory.quantity > 0).all()
        total_value = sum(item.quantity * (item.product.cost_price or 0) for item in inventory_items)
        value_labels.append(warehouse.name)
        value_data.append(round(total_value, 2))

    # توزيع المنتجات حسب التصنيفات
    categories = Category.query.all()
    category_labels = [c.name for c in categories]
    category_data = []

    for category in categories:
        count = Product.query.filter_by(category_id=category.id, is_active=True).count()
        category_data.append(count)

    return render_template('warehouse_dashboard.html',
                          stats=stats,
                          top_inventory=top_inventory,
                          low_inventory=low_inventory,
                          warehouse_data={'labels': warehouse_labels, 'data': warehouse_data},
                          status_data=status_data,
                          value_data={'labels': value_labels, 'data': value_data},
                          category_data={'labels': category_labels, 'data': category_data})

@warehouses_blueprint.route('/api/products/search')
@login_required
def api_search_products():
    """البحث عن المنتجات بالاسم أو الباركود"""
    try:
        search = request.args.get('search', '')
        warehouse_id = request.args.get('warehouse_id')

        if not search:
            return jsonify({'products': []})

        # استخدام الدالة الجديدة للبحث
        products = Product.search_by_name_or_code(search)

        result = []
        for p in products:
            product_data = {
                'id': p.id,
                'name': p.name,
                'code': p.code,
                'price': p.price,
                'stock_quantity': p.stock_quantity,
                'total_inventory': p.get_total_inventory(),
                'category': p.category.name if p.category else None
            }

            # إضافة معلومات المخزون في المخزن المحدد إذا تم تحديده
            if warehouse_id and warehouse_id.isdigit():
                warehouse_id = int(warehouse_id)
                inventory = Inventory.query.filter_by(
                    product_id=p.id,
                    warehouse_id=warehouse_id
                ).first()

                if inventory:
                    product_data['warehouse_quantity'] = inventory.quantity
                    product_data['warehouse_minimum_stock'] = inventory.minimum_stock
                    product_data['warehouse_status'] = inventory.get_status()
                else:
                    product_data['warehouse_quantity'] = 0
                    product_data['warehouse_minimum_stock'] = 0
                    product_data['warehouse_status'] = 'not_in_warehouse'

            result.append(product_data)

        return jsonify({'products': result})
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@warehouses_blueprint.route('/warehouses/<int:id>/toggle-status', methods=['POST'])
@login_required
def toggle_status(id):
    """تفعيل/تعطيل المخزن"""
    warehouse = Warehouse.query.get_or_404(id)
    warehouse.is_active = not warehouse.is_active
    db.session.commit()

    status = "تفعيل" if warehouse.is_active else "تعطيل"
    flash(f'تم {status} المخزن بنجاح', 'success')

    return redirect(url_for('warehouses.index'))

@warehouses_blueprint.route('/warehouses/<int:id>/set-default', methods=['POST'])
@login_required
def set_default(id):
    """تعيين المخزن كمخزن افتراضي"""
    # إلغاء تعيين المخزن الافتراضي الحالي
    Warehouse.query.filter_by(is_default=True).update({'is_default': False})

    # تعيين المخزن الجديد كمخزن افتراضي
    warehouse = Warehouse.query.get_or_404(id)
    warehouse.is_default = True
    db.session.commit()

    flash(f'تم تعيين {warehouse.name} كمخزن افتراضي بنجاح', 'success')

    return redirect(url_for('warehouses.index'))

def generate_inventory_status_report(warehouse_id=None):
    """توليد تقرير حالة المخزون"""
    query = db.session.query(
        Inventory,
        Product.name.label('product_name'),
        Product.code.label('product_code'),
        Category.name.label('category_name'),
        Warehouse.name.label('warehouse_name')
    ).join(
        Product, Inventory.product_id == Product.id
    ).join(
        Warehouse, Inventory.warehouse_id == Warehouse.id
    ).join(
        Category, Product.category_id == Category.id
    )

    if warehouse_id and warehouse_id.isdigit():
        query = query.filter(Inventory.warehouse_id == int(warehouse_id))

    results = query.all()

    # تحضير البيانات للتقرير
    report_items = []
    for item in results:
        status = 'متوفر'
        if item.Inventory.quantity <= 0:
            status = 'نفذ من المخزون'
        elif item.Inventory.quantity <= item.Inventory.minimum_stock:
            status = 'مخزون منخفض'

        report_items.append({
            'product_name': item.product_name,
            'product_code': item.product_code,
            'category_name': item.category_name,
            'warehouse_name': item.warehouse_name,
            'quantity': item.Inventory.quantity,
            'minimum_stock': item.Inventory.minimum_stock,
            'status': status
        })

    # إحصائيات التقرير
    total_items = len(report_items)
    in_stock = sum(1 for item in report_items if item['status'] == 'متوفر')
    low_stock = sum(1 for item in report_items if item['status'] == 'مخزون منخفض')
    out_of_stock = sum(1 for item in report_items if item['status'] == 'نفذ من المخزون')

    return {
        'title': 'تقرير حالة المخزون',
        'items': report_items,
        'stats': {
            'total_items': total_items,
            'in_stock': in_stock,
            'low_stock': low_stock,
            'out_of_stock': out_of_stock
        }
    }

def generate_inventory_value_report(warehouse_id=None):
    """توليد تقرير قيمة المخزون"""
    query = db.session.query(
        Inventory,
        Product.name.label('product_name'),
        Product.code.label('product_code'),
        Product.price.label('product_price'),
        Category.name.label('category_name'),
        Warehouse.name.label('warehouse_name')
    ).join(
        Product, Inventory.product_id == Product.id
    ).join(
        Warehouse, Inventory.warehouse_id == Warehouse.id
    ).join(
        Category, Product.category_id == Category.id
    )

    if warehouse_id and warehouse_id.isdigit():
        query = query.filter(Inventory.warehouse_id == int(warehouse_id))

    results = query.all()

    # تحضير البيانات للتقرير
    report_items = []
    total_value = 0
    for item in results:
        item_value = item.Inventory.quantity * item.product_price
        total_value += item_value

        report_items.append({
            'product_name': item.product_name,
            'product_code': item.product_code,
            'category_name': item.category_name,
            'warehouse_name': item.warehouse_name,
            'quantity': item.Inventory.quantity,
            'price': item.product_price,
            'value': item_value
        })

    # تجميع البيانات حسب المخزن
    warehouse_values = {}
    for item in report_items:
        warehouse_name = item['warehouse_name']
        if warehouse_name not in warehouse_values:
            warehouse_values[warehouse_name] = 0
        warehouse_values[warehouse_name] += item['value']

    # تجميع البيانات حسب التصنيف
    category_values = {}
    for item in report_items:
        category_name = item['category_name']
        if category_name not in category_values:
            category_values[category_name] = 0
        category_values[category_name] += item['value']

    return {
        'title': 'تقرير قيمة المخزون',
        'items': report_items,
        'stats': {
            'total_value': total_value,
            'warehouse_values': warehouse_values,
            'category_values': category_values
        }
    }

def generate_inventory_movement_report(warehouse_id=None, date_from=None, date_to=None):
    """توليد تقرير حركة المخزون"""
    query = db.session.query(
        InventoryMovement,
        Product.name.label('product_name'),
        Product.code.label('product_code'),
        Warehouse.name.label('warehouse_name'),
        User.username.label('user_username')
    ).join(
        Inventory, InventoryMovement.inventory_id == Inventory.id
    ).join(
        Product, Inventory.product_id == Product.id
    ).join(
        Warehouse, Inventory.warehouse_id == Warehouse.id
    ).outerjoin(
        User, InventoryMovement.user_id == User.id
    )

    if warehouse_id and warehouse_id.isdigit():
        query = query.filter(Inventory.warehouse_id == int(warehouse_id))

    if date_from:
        try:
            date_from_obj = datetime.strptime(date_from, '%Y-%m-%d')
            query = query.filter(InventoryMovement.created_at >= date_from_obj)
        except ValueError:
            pass

    if date_to:
        try:
            date_to_obj = datetime.strptime(date_to, '%Y-%m-%d') + timedelta(days=1)  # لتضمين اليوم الأخير كاملاً
            query = query.filter(InventoryMovement.created_at < date_to_obj)
        except ValueError:
            pass

    # تنفيذ الاستعلام مع الترتيب
    results = query.order_by(InventoryMovement.created_at.desc()).all()

    # تحضير البيانات للتقرير
    report_items = []
    for item in results:
        movement_type_ar = {
            'add': 'إضافة',
            'remove': 'إزالة',
            'update': 'تحديث',
            'transfer_in': 'نقل وارد',
            'transfer_out': 'نقل صادر'
        }.get(item.InventoryMovement.movement_type, item.InventoryMovement.movement_type)

        report_items.append({
            'product_name': item.product_name,
            'product_code': item.product_code,
            'warehouse_name': item.warehouse_name,
            'movement_type': movement_type_ar,
            'quantity_before': item.InventoryMovement.previous_quantity,
            'quantity_after': item.InventoryMovement.new_quantity,
            'quantity_change': item.InventoryMovement.quantity,
            'reference': item.InventoryMovement.reference or '-',
            'notes': item.InventoryMovement.notes or '-',
            'user': item.user_username or '-',
            'created_at': item.InventoryMovement.created_at.strftime('%Y-%m-%d %H:%M:%S')
        })

    # إحصائيات التقرير
    total_movements = len(report_items)
    add_movements = sum(1 for item in report_items if item['movement_type'] == 'إضافة')
    remove_movements = sum(1 for item in report_items if item['movement_type'] == 'إزالة')
    update_movements = sum(1 for item in report_items if item['movement_type'] == 'تحديث')
    transfer_movements = sum(1 for item in report_items if item['movement_type'] in ['نقل وارد', 'نقل صادر'])

    return {
        'title': 'تقرير حركة المخزون',
        'items': report_items,
        'stats': {
            'total_movements': total_movements,
            'add_movements': add_movements,
            'remove_movements': remove_movements,
            'update_movements': update_movements,
            'transfer_movements': transfer_movements
        }
    }

def generate_low_stock_report(warehouse_id=None):
    """توليد تقرير المنتجات منخفضة المخزون"""
    query = db.session.query(
        Inventory,
        Product.name.label('product_name'),
        Product.code.label('product_code'),
        Product.price.label('product_price'),
        Category.name.label('category_name'),
        Warehouse.name.label('warehouse_name')
    ).join(
        Product, Inventory.product_id == Product.id
    ).join(
        Warehouse, Inventory.warehouse_id == Warehouse.id
    ).join(
        Category, Product.category_id == Category.id
    ).filter(
        Inventory.quantity > 0,
        Inventory.quantity <= Inventory.minimum_stock
    )

    if warehouse_id and warehouse_id.isdigit():
        query = query.filter(Inventory.warehouse_id == int(warehouse_id))

    results = query.all()

    # تحضير البيانات للتقرير
    report_items = []
    for item in results:
        report_items.append({
            'product_name': item.product_name,
            'product_code': item.product_code,
            'category_name': item.category_name,
            'warehouse_name': item.warehouse_name,
            'quantity': item.Inventory.quantity,
            'minimum_stock': item.Inventory.minimum_stock,
            'needed_quantity': item.Inventory.minimum_stock - item.Inventory.quantity,
            'price': item.product_price
        })

    # تجميع البيانات حسب التصنيف
    category_counts = {}
    for item in report_items:
        category_name = item['category_name']
        if category_name not in category_counts:
            category_counts[category_name] = 0
        category_counts[category_name] += 1

    return {
        'title': 'تقرير المنتجات منخفضة المخزون',
        'items': report_items,
        'stats': {
            'total_items': len(report_items),
            'category_counts': category_counts
        }
    }

def generate_out_of_stock_report(warehouse_id=None):
    """توليد تقرير المنتجات النافذة من المخزون"""
    query = db.session.query(
        Inventory,
        Product.name.label('product_name'),
        Product.code.label('product_code'),
        Product.price.label('product_price'),
        Category.name.label('category_name'),
        Warehouse.name.label('warehouse_name')
    ).join(
        Product, Inventory.product_id == Product.id
    ).join(
        Warehouse, Inventory.warehouse_id == Warehouse.id
    ).join(
        Category, Product.category_id == Category.id
    ).filter(
        Inventory.quantity <= 0
    )

    if warehouse_id and warehouse_id.isdigit():
        query = query.filter(Inventory.warehouse_id == int(warehouse_id))

    results = query.all()

    # تحضير البيانات للتقرير
    report_items = []
    for item in results:
        report_items.append({
            'product_name': item.product_name,
            'product_code': item.product_code,
            'category_name': item.category_name,
            'warehouse_name': item.warehouse_name,
            'minimum_stock': item.Inventory.minimum_stock,
            'price': item.product_price
        })

    # تجميع البيانات حسب التصنيف
    category_counts = {}
    for item in report_items:
        category_name = item['category_name']
        if category_name not in category_counts:
            category_counts[category_name] = 0
        category_counts[category_name] += 1

    return {
        'title': 'تقرير المنتجات النافذة من المخزون',
        'items': report_items,
        'stats': {
            'total_items': len(report_items),
            'category_counts': category_counts
        }
    }

@warehouses_blueprint.route('/api/warehouses/reports/export-new')
@login_required
def export_report_new():
    """تصدير تقارير المخزون إلى ملف CSV"""
    # الحصول على معلمات الفلتر
    warehouse_id = request.args.get('warehouse_id', '')
    report_type = request.args.get('report_type', 'inventory_status')
    date_from = request.args.get('date_from', '')
    date_to = request.args.get('date_to', '')

    # تحضير البيانات حسب نوع التقرير
    if report_type == 'inventory_status':
        report_data = generate_inventory_status_report(warehouse_id)
        filename = 'inventory_status_report'
        headers = ['المنتج', 'الباركود', 'التصنيف', 'المخزن', 'الكمية', 'الحد الأدنى', 'الحالة']
    elif report_type == 'inventory_value':
        report_data = generate_inventory_value_report(warehouse_id)
        filename = 'inventory_value_report'
        headers = ['المنتج', 'الباركود', 'التصنيف', 'المخزن', 'الكمية', 'السعر', 'القيمة']
    elif report_type == 'inventory_movement':
        report_data = generate_inventory_movement_report(warehouse_id, date_from, date_to)
        filename = 'inventory_movement_report'
        headers = ['المنتج', 'الباركود', 'المخزن', 'نوع الحركة', 'الكمية قبل', 'الكمية بعد', 'التغيير', 'المرجع', 'الملاحظات', 'المستخدم', 'التاريخ']
    elif report_type == 'low_stock':
        report_data = generate_low_stock_report(warehouse_id)
        filename = 'low_stock_report'
        headers = ['المنتج', 'الباركود', 'التصنيف', 'المخزن', 'الكمية', 'الحد الأدنى', 'الكمية المطلوبة', 'السعر']
    elif report_type == 'out_of_stock':
        report_data = generate_out_of_stock_report(warehouse_id)
        filename = 'out_of_stock_report'
        headers = ['المنتج', 'الباركود', 'التصنيف', 'المخزن', 'الحد الأدنى', 'السعر']
    else:
        return jsonify({'error': 'نوع التقرير غير صالح'}), 400

    # إنشاء ملف CSV
    output = StringIO()
    writer = csv.writer(output)

    # كتابة الترويسة
    writer.writerow(headers)

    # كتابة البيانات
    for item in report_data['items']:
        if report_type == 'inventory_status':
            writer.writerow([
                item['product_name'],
                item['product_code'],
                item['category_name'],
                item['warehouse_name'],
                item['quantity'],
                item['minimum_stock'],
                item['status']
            ])
        elif report_type == 'inventory_value':
            writer.writerow([
                item['product_name'],
                item['product_code'],
                item['category_name'],
                item['warehouse_name'],
                item['quantity'],
                item['price'],
                item['value']
            ])
        elif report_type == 'inventory_movement':
            writer.writerow([
                item['product_name'],
                item['product_code'],
                item['warehouse_name'],
                item['movement_type'],
                item['quantity_before'],
                item['quantity_after'],
                item['quantity_change'],
                item['reference'],
                item['notes'],
                item['user'],
                item['created_at']
            ])
        elif report_type == 'low_stock':
            writer.writerow([
                item['product_name'],
                item['product_code'],
                item['category_name'],
                item['warehouse_name'],
                item['quantity'],
                item['minimum_stock'],
                item['needed_quantity'],
                item['price']
            ])
        elif report_type == 'out_of_stock':
            writer.writerow([
                item['product_name'],
                item['product_code'],
                item['category_name'],
                item['warehouse_name'],
                item['minimum_stock'],
                item['price']
            ])

    # إضافة صف إجمالي لتقرير قيمة المخزون
    if report_type == 'inventory_value' and report_data['items']:
        writer.writerow(['', '', '', '', '', 'الإجمالي:', report_data['stats']['total_value']])

    # إعداد الاستجابة
    output.seek(0)
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    return Response(
        output.getvalue(),
        mimetype='text/csv',
        headers={
            'Content-Disposition': f'attachment; filename={filename}_{timestamp}.csv'
        }
    )