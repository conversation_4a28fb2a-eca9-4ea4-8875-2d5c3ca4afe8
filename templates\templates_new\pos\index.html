{% extends "base.html" %}

{% block title %}نقطة البيع - نوبارا{% endblock %}

{% block page_title %}نقطة البيع{% endblock %}
{% block page_subtitle %}إدارة المبيعات والفواتير{% endblock %}

{% block extra_css %}
<style>
    .pos-container {
        height: calc(100vh - 200px);
        overflow: hidden;
    }
    
    .products-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
        gap: 1rem;
        max-height: 500px;
        overflow-y: auto;
    }
    
    .product-card {
        border: 2px solid transparent;
        transition: all 0.3s ease;
        cursor: pointer;
    }
    
    .product-card:hover {
        border-color: var(--primary-blue-500);
        transform: translateY(-2px);
    }
    
    .cart-item {
        border-bottom: 1px solid var(--border-primary);
        padding: 1rem 0;
    }
    
    .cart-item:last-child {
        border-bottom: none;
    }
    
    .quantity-controls {
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }
    
    .quantity-btn {
        width: 2rem;
        height: 2rem;
        border: 1px solid var(--border-primary);
        background: var(--bg-secondary);
        border-radius: 0.375rem;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        transition: all 0.2s ease;
    }
    
    .quantity-btn:hover {
        background: var(--bg-hover);
        border-color: var(--primary-blue-500);
    }
    
    .checkout-section {
        background: var(--bg-card);
        border: 1px solid var(--border-primary);
        border-radius: 1rem;
        padding: 1.5rem;
        position: sticky;
        top: 1rem;
    }
    
    .total-display {
        font-size: 2rem;
        font-weight: bold;
        color: var(--primary-blue-600);
        text-align: center;
        padding: 1rem;
        background: var(--bg-secondary);
        border-radius: 0.75rem;
        margin-bottom: 1rem;
    }
</style>
{% endblock %}

{% block content %}
<div class="pos-container">
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6 h-full">
        <!-- قسم المنتجات والبحث -->
        <div class="lg:col-span-2 space-y-4">
            <!-- شريط البحث والفلاتر -->
            <div class="card">
                <div class="card-body">
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <!-- البحث -->
                        <div class="relative">
                            <input type="text" 
                                   id="product-search" 
                                   placeholder="البحث في المنتجات..."
                                   class="form-control pl-10">
                            <i class="ri-search-line absolute left-3 top-1/2 transform -translate-y-1/2 text-muted"></i>
                        </div>
                        
                        <!-- المخزن -->
                        <div>
                            <select id="warehouse-select" class="form-control">
                                {% for warehouse in warehouses %}
                                <option value="{{ warehouse.id }}" 
                                        {% if warehouse == default_warehouse %}selected{% endif %}>
                                    {{ warehouse.name }}
                                </option>
                                {% endfor %}
                            </select>
                        </div>
                        
                        <!-- العميل -->
                        <div class="relative">
                            <input type="text" 
                                   id="customer-search" 
                                   placeholder="البحث عن عميل..."
                                   class="form-control pl-10">
                            <i class="ri-user-line absolute left-3 top-1/2 transform -translate-y-1/2 text-muted"></i>
                            <input type="hidden" id="selected-customer-id">
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- شبكة المنتجات -->
            <div class="card flex-1">
                <div class="card-header">
                    <h3 class="card-title">المنتجات</h3>
                </div>
                <div class="card-body">
                    <div id="products-container" class="products-grid">
                        {% for product in products %}
                        <div class="product-card card hover:shadow-md" 
                             onclick="addToCart({{ product.id }})">
                            <div class="card-body p-4">
                                {% if product.image_url %}
                                <img src="{{ product.image_url }}" 
                                     alt="{{ product.name }}"
                                     class="w-full h-24 object-cover rounded-lg mb-3">
                                {% else %}
                                <div class="w-full h-24 bg-gradient-secondary rounded-lg mb-3 flex items-center justify-center">
                                    <i class="ri-product-hunt-line text-white text-2xl"></i>
                                </div>
                                {% endif %}
                                
                                <h4 class="font-semibold text-sm text-primary mb-2 line-clamp-2">
                                    {{ product.name }}
                                </h4>
                                
                                <div class="flex justify-between items-center">
                                    <span class="text-lg font-bold text-blue-600">
                                        {{ "%.2f"|format(product.selling_price) }} ج.م
                                    </span>
                                    <span class="text-xs text-secondary">
                                        {{ product.unit }}
                                    </span>
                                </div>
                                
                                {% if product.track_inventory %}
                                <div class="mt-2">
                                    <span class="text-xs px-2 py-1 rounded-full 
                                               {% if product.total_stock > product.min_stock_level %}
                                               bg-green-100 text-green-800
                                               {% else %}
                                               bg-red-100 text-red-800
                                               {% endif %}">
                                        المخزون: {{ product.total_stock }}
                                    </span>
                                </div>
                                {% endif %}
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                </div>
            </div>
        </div>
        
        <!-- قسم السلة والدفع -->
        <div class="space-y-4">
            <!-- معلومات العميل المحدد -->
            <div id="selected-customer-info" class="card hidden">
                <div class="card-body">
                    <div class="flex items-center justify-between">
                        <div>
                            <h4 class="font-semibold text-primary" id="customer-name"></h4>
                            <p class="text-sm text-secondary" id="customer-phone"></p>
                        </div>
                        <button onclick="clearCustomer()" class="btn btn-xs btn-secondary">
                            <i class="ri-close-line"></i>
                        </button>
                    </div>
                </div>
            </div>
            
            <!-- السلة -->
            <div class="checkout-section">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-semibold text-primary">سلة التسوق</h3>
                    <button onclick="clearCart()" class="btn btn-xs btn-secondary">
                        <i class="ri-delete-bin-line ml-1"></i>
                        مسح الكل
                    </button>
                </div>
                
                <!-- عناصر السلة -->
                <div id="cart-items" class="space-y-3 mb-4 max-h-64 overflow-y-auto">
                    <div class="text-center py-8 text-muted">
                        <i class="ri-shopping-cart-line text-4xl mb-2"></i>
                        <p>السلة فارغة</p>
                    </div>
                </div>
                
                <!-- ملخص المبالغ -->
                <div class="space-y-2 mb-4 border-t border-primary pt-4">
                    <div class="flex justify-between text-sm">
                        <span>المجموع الفرعي:</span>
                        <span id="subtotal">0.00 ج.م</span>
                    </div>
                    <div class="flex justify-between text-sm">
                        <span>الضريبة:</span>
                        <span id="tax-amount">0.00 ج.م</span>
                    </div>
                    <div class="flex justify-between text-sm">
                        <span>الخصم:</span>
                        <input type="number" 
                               id="discount-amount" 
                               value="0" 
                               min="0" 
                               step="0.01"
                               class="form-control w-20 h-6 text-xs text-right"
                               onchange="updateTotals()">
                    </div>
                </div>
                
                <!-- المجموع الإجمالي -->
                <div class="total-display" id="total-amount">0.00 ج.م</div>
                
                <!-- طرق الدفع -->
                <div class="space-y-3 mb-4">
                    <label class="block text-sm font-medium text-primary">طريقة الدفع:</label>
                    <div class="grid grid-cols-2 gap-2">
                        <button onclick="setPaymentMethod('cash')" 
                                class="payment-method-btn btn btn-secondary active" 
                                data-method="cash">
                            <i class="ri-money-dollar-circle-line ml-1"></i>
                            نقدي
                        </button>
                        <button onclick="setPaymentMethod('card')" 
                                class="payment-method-btn btn btn-secondary" 
                                data-method="card">
                            <i class="ri-bank-card-line ml-1"></i>
                            بطاقة
                        </button>
                        <button onclick="setPaymentMethod('credit')" 
                                class="payment-method-btn btn btn-secondary" 
                                data-method="credit">
                            <i class="ri-time-line ml-1"></i>
                            آجل
                        </button>
                        <button onclick="setPaymentMethod('installment')" 
                                class="payment-method-btn btn btn-secondary" 
                                data-method="installment">
                            <i class="ri-calendar-line ml-1"></i>
                            أقساط
                        </button>
                    </div>
                </div>
                
                <!-- أزرار العمليات -->
                <div class="space-y-2">
                    <button onclick="processPayment()" 
                            id="checkout-btn"
                            class="btn btn-primary w-full btn-lg">
                        <i class="ri-shopping-cart-line ml-2"></i>
                        إتمام البيع
                    </button>
                    
                    <div class="grid grid-cols-2 gap-2">
                        <button onclick="suspendSale()" class="btn btn-secondary btn-sm">
                            <i class="ri-pause-line ml-1"></i>
                            تعليق
                        </button>
                        <button onclick="printQuote()" class="btn btn-secondary btn-sm">
                            <i class="ri-printer-line ml-1"></i>
                            عرض سعر
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal للدفع -->
<div id="payment-modal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50">
    <div class="flex items-center justify-center min-h-screen p-4">
        <div class="bg-card rounded-lg shadow-xl max-w-md w-full">
            <div class="p-6">
                <div class="flex justify-between items-center mb-4">
                    <h3 class="text-lg font-semibold">تأكيد الدفع</h3>
                    <button onclick="closePaymentModal()" class="text-muted hover:text-primary">
                        <i class="ri-close-line text-xl"></i>
                    </button>
                </div>
                
                <div class="space-y-4">
                    <div class="text-center">
                        <div class="text-3xl font-bold text-primary" id="modal-total">0.00 ج.م</div>
                        <p class="text-secondary">المبلغ الإجمالي</p>
                    </div>
                    
                    <div id="cash-payment-section">
                        <label class="block text-sm font-medium text-primary mb-2">المبلغ المستلم:</label>
                        <input type="number" 
                               id="received-amount" 
                               class="form-control text-center text-lg"
                               placeholder="0.00"
                               step="0.01">
                        <div class="mt-2 text-center">
                            <span class="text-sm text-secondary">الباقي: </span>
                            <span id="change-amount" class="font-bold text-primary">0.00 ج.م</span>
                        </div>
                    </div>
                    
                    <div class="flex gap-3">
                        <button onclick="closePaymentModal()" class="btn btn-secondary flex-1">إلغاء</button>
                        <button onclick="confirmPayment()" class="btn btn-primary flex-1">تأكيد</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script src="{{ url_for('static', filename='js/pos-system.js') }}?v=2.0.0"></script>
<script>
// متغيرات عامة
let cart = [];
let selectedCustomer = null;
let selectedWarehouse = {{ default_warehouse.id if default_warehouse else 'null' }};
let currentPaymentMethod = 'cash';

// تهيئة النظام
document.addEventListener('DOMContentLoaded', function() {
    initPOSSystem();
});

function initPOSSystem() {
    // تهيئة البحث في المنتجات
    const productSearch = document.getElementById('product-search');
    productSearch.addEventListener('input', debounce(searchProducts, 300));
    
    // تهيئة البحث في العملاء
    const customerSearch = document.getElementById('customer-search');
    customerSearch.addEventListener('input', debounce(searchCustomers, 300));
    
    // تهيئة تغيير المخزن
    const warehouseSelect = document.getElementById('warehouse-select');
    warehouseSelect.addEventListener('change', function() {
        selectedWarehouse = this.value;
        updateProductsDisplay();
    });
    
    // تهيئة حقل المبلغ المستلم
    const receivedAmount = document.getElementById('received-amount');
    receivedAmount.addEventListener('input', calculateChange);
    
    console.log('🏪 POS System initialized');
}

// إضافة منتج للسلة
function addToCart(productId) {
    fetch(`/pos/api/product/${productId}?warehouse_id=${selectedWarehouse}`)
        .then(response => response.json())
        .then(product => {
            if (product.error) {
                showNotification('خطأ في جلب بيانات المنتج', 'error');
                return;
            }
            
            // التحقق من المخزون
            if (product.track_inventory && product.stock <= 0) {
                showNotification('المنتج غير متوفر في المخزون', 'warning');
                return;
            }
            
            // البحث عن المنتج في السلة
            const existingItem = cart.find(item => item.product_id === productId);
            
            if (existingItem) {
                // زيادة الكمية
                if (product.track_inventory && existingItem.quantity >= product.stock) {
                    showNotification('الكمية المطلوبة تتجاوز المخزون المتاح', 'warning');
                    return;
                }
                existingItem.quantity++;
            } else {
                // إضافة منتج جديد
                cart.push({
                    product_id: productId,
                    name: product.name,
                    unit_price: product.selling_price,
                    quantity: 1,
                    discount_amount: 0,
                    tax_rate: product.tax_rate,
                    stock: product.stock,
                    track_inventory: product.track_inventory
                });
            }
            
            updateCartDisplay();
            updateTotals();
        })
        .catch(error => {
            console.error('Error adding to cart:', error);
            showNotification('حدث خطأ أثناء إضافة المنتج', 'error');
        });
}

// تحديث عرض السلة
function updateCartDisplay() {
    const cartContainer = document.getElementById('cart-items');
    
    if (cart.length === 0) {
        cartContainer.innerHTML = `
            <div class="text-center py-8 text-muted">
                <i class="ri-shopping-cart-line text-4xl mb-2"></i>
                <p>السلة فارغة</p>
            </div>
        `;
        return;
    }
    
    cartContainer.innerHTML = cart.map((item, index) => `
        <div class="cart-item">
            <div class="flex items-center justify-between mb-2">
                <h4 class="font-medium text-primary text-sm">${item.name}</h4>
                <button onclick="removeFromCart(${index})" class="text-red-500 hover:text-red-700">
                    <i class="ri-close-line"></i>
                </button>
            </div>
            
            <div class="flex items-center justify-between">
                <div class="quantity-controls">
                    <button class="quantity-btn" onclick="updateQuantity(${index}, ${item.quantity - 1})">
                        <i class="ri-subtract-line"></i>
                    </button>
                    <span class="w-8 text-center text-sm">${item.quantity}</span>
                    <button class="quantity-btn" onclick="updateQuantity(${index}, ${item.quantity + 1})">
                        <i class="ri-add-line"></i>
                    </button>
                </div>
                
                <div class="text-right">
                    <div class="text-sm font-medium">${(item.quantity * item.unit_price).toFixed(2)} ج.م</div>
                    <div class="text-xs text-secondary">${item.unit_price.toFixed(2)} × ${item.quantity}</div>
                </div>
            </div>
        </div>
    `).join('');
}

// تحديث الكمية
function updateQuantity(index, newQuantity) {
    if (newQuantity <= 0) {
        removeFromCart(index);
        return;
    }
    
    const item = cart[index];
    
    // التحقق من المخزون
    if (item.track_inventory && newQuantity > item.stock) {
        showNotification('الكمية المطلوبة تتجاوز المخزون المتاح', 'warning');
        return;
    }
    
    item.quantity = newQuantity;
    updateCartDisplay();
    updateTotals();
}

// حذف من السلة
function removeFromCart(index) {
    cart.splice(index, 1);
    updateCartDisplay();
    updateTotals();
}

// مسح السلة
function clearCart() {
    cart = [];
    updateCartDisplay();
    updateTotals();
}

// تحديث المجاميع
function updateTotals() {
    let subtotal = 0;
    let totalTax = 0;
    
    cart.forEach(item => {
        const itemSubtotal = (item.quantity * item.unit_price) - item.discount_amount;
        const itemTax = itemSubtotal * item.tax_rate;
        
        subtotal += itemSubtotal;
        totalTax += itemTax;
    });
    
    const discountAmount = parseFloat(document.getElementById('discount-amount').value) || 0;
    const total = subtotal + totalTax - discountAmount;
    
    document.getElementById('subtotal').textContent = subtotal.toFixed(2) + ' ج.م';
    document.getElementById('tax-amount').textContent = totalTax.toFixed(2) + ' ج.م';
    document.getElementById('total-amount').textContent = total.toFixed(2) + ' ج.م';
    
    // تحديث modal الدفع
    document.getElementById('modal-total').textContent = total.toFixed(2) + ' ج.م';
    document.getElementById('received-amount').value = total.toFixed(2);
    calculateChange();
}

// تعيين طريقة الدفع
function setPaymentMethod(method) {
    currentPaymentMethod = method;
    
    // تحديث الأزرار
    document.querySelectorAll('.payment-method-btn').forEach(btn => {
        btn.classList.remove('active');
    });
    document.querySelector(`[data-method="${method}"]`).classList.add('active');
}

// معالجة الدفع
function processPayment() {
    if (cart.length === 0) {
        showNotification('السلة فارغة', 'warning');
        return;
    }
    
    document.getElementById('payment-modal').classList.remove('hidden');
    updateTotals();
}

// تأكيد الدفع
function confirmPayment() {
    const saleData = {
        items: cart.map(item => ({
            product_id: item.product_id,
            quantity: item.quantity,
            unit_price: item.unit_price,
            discount_amount: item.discount_amount
        })),
        customer_id: selectedCustomer ? selectedCustomer.id : null,
        warehouse_id: selectedWarehouse,
        payment_method: currentPaymentMethod,
        discount_amount: parseFloat(document.getElementById('discount-amount').value) || 0,
        notes: ''
    };
    
    // إظهار حالة التحميل
    const confirmBtn = document.querySelector('#payment-modal .btn-primary');
    const originalText = confirmBtn.innerHTML;
    confirmBtn.innerHTML = '<i class="ri-loader-4-line animate-spin ml-2"></i>جاري المعالجة...';
    confirmBtn.disabled = true;
    
    fetch('/pos/api/sale/create', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': window.csrfToken
        },
        body: JSON.stringify(saleData)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification('تم إنشاء الفاتورة بنجاح', 'success');
            
            // مسح السلة
            clearCart();
            clearCustomer();
            closePaymentModal();
            
            // طباعة الإيصال
            if (confirm('هل تريد طباعة الإيصال؟')) {
                window.open(`/pos/receipt/${data.sale_id}`, '_blank');
            }
        } else {
            showNotification(data.message, 'error');
        }
    })
    .catch(error => {
        console.error('Error processing payment:', error);
        showNotification('حدث خطأ أثناء معالجة الدفع', 'error');
    })
    .finally(() => {
        confirmBtn.innerHTML = originalText;
        confirmBtn.disabled = false;
    });
}

// إغلاق modal الدفع
function closePaymentModal() {
    document.getElementById('payment-modal').classList.add('hidden');
}

// حساب الباقي
function calculateChange() {
    const total = parseFloat(document.getElementById('modal-total').textContent.replace(' ج.م', ''));
    const received = parseFloat(document.getElementById('received-amount').value) || 0;
    const change = received - total;
    
    document.getElementById('change-amount').textContent = change.toFixed(2) + ' ج.م';
}

// البحث في المنتجات
function searchProducts() {
    const query = document.getElementById('product-search').value;
    
    if (query.length < 2) {
        updateProductsDisplay();
        return;
    }
    
    fetch(`/pos/api/products/search?q=${encodeURIComponent(query)}&warehouse_id=${selectedWarehouse}`)
        .then(response => response.json())
        .then(products => {
            displayProducts(products);
        })
        .catch(error => {
            console.error('Error searching products:', error);
        });
}

// عرض المنتجات
function displayProducts(products) {
    const container = document.getElementById('products-container');
    
    if (products.length === 0) {
        container.innerHTML = `
            <div class="col-span-full text-center py-8 text-muted">
                <i class="ri-search-line text-4xl mb-2"></i>
                <p>لا توجد منتجات</p>
            </div>
        `;
        return;
    }
    
    container.innerHTML = products.map(product => `
        <div class="product-card card hover:shadow-md" onclick="addToCart(${product.id})">
            <div class="card-body p-4">
                ${product.image_url ? 
                    `<img src="${product.image_url}" alt="${product.name}" class="w-full h-24 object-cover rounded-lg mb-3">` :
                    `<div class="w-full h-24 bg-gradient-secondary rounded-lg mb-3 flex items-center justify-center">
                        <i class="ri-product-hunt-line text-white text-2xl"></i>
                    </div>`
                }
                
                <h4 class="font-semibold text-sm text-primary mb-2 line-clamp-2">${product.name}</h4>
                
                <div class="flex justify-between items-center">
                    <span class="text-lg font-bold text-blue-600">${product.selling_price.toFixed(2)} ج.م</span>
                    <span class="text-xs text-secondary">${product.unit}</span>
                </div>
                
                ${product.track_inventory ? 
                    `<div class="mt-2">
                        <span class="text-xs px-2 py-1 rounded-full ${product.stock > 0 ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}">
                            المخزون: ${product.stock}
                        </span>
                    </div>` : ''
                }
            </div>
        </div>
    `).join('');
}

// تحديث عرض المنتجات
function updateProductsDisplay() {
    // إعادة تحميل المنتجات حسب المخزن المحدد
    location.reload();
}

// البحث في العملاء
function searchCustomers() {
    const query = document.getElementById('customer-search').value;
    
    if (query.length < 2) {
        return;
    }
    
    fetch(`/pos/api/customers/search?q=${encodeURIComponent(query)}`)
        .then(response => response.json())
        .then(customers => {
            // يمكن إضافة dropdown للعملاء هنا
            console.log('Customers found:', customers);
        })
        .catch(error => {
            console.error('Error searching customers:', error);
        });
}

// مسح العميل المحدد
function clearCustomer() {
    selectedCustomer = null;
    document.getElementById('customer-search').value = '';
    document.getElementById('selected-customer-id').value = '';
    document.getElementById('selected-customer-info').classList.add('hidden');
}

// دالة مساعدة للتأخير
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// إظهار الإشعارات
function showNotification(message, type = 'info') {
    // يمكن تطوير نظام إشعارات أكثر تقدماً
    if (type === 'error') {
        alert('خطأ: ' + message);
    } else if (type === 'warning') {
        alert('تحذير: ' + message);
    } else if (type === 'success') {
        alert('نجح: ' + message);
    } else {
        alert(message);
    }
}
</script>
{% endblock %}
