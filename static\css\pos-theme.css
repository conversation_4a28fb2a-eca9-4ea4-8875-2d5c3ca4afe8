:root {
    --primary: #1e40af;
    --primary-dark: #1e3a8a;
    --primary-light: #3b82f6;
    --secondary: #6b7280;
    --success: #10b981;
    --danger: #ef4444;
    --warning: #f59e0b;
    --info: #3b82f6;
    --dark-bg: #1e293b;
    --dark-surface: #0f172a;
    --dark-border: #334155;
    --dark-text: #e2e8f0;
}

/* Dark Mode Support */
.dark {
    --primary: #3b82f6;
    --primary-dark: #2563eb;
    --primary-light: #60a5fa;
    color-scheme: dark;
}

/* Animations */
@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes slideInUp {
    from {
        transform: translateY(20px);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

@keyframes slideInRight {
    from {
        transform: translateX(20px);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes pulse {
    0%, 100% {
        transform: scale(1);
        box-shadow: 0 0 0 rgba(59, 130, 246, 0);
    }
    50% {
        transform: scale(1.02);
        box-shadow: 0 0 10px rgba(59, 130, 246, 0.5);
    }
}

.pulse-animation {
    animation: pulse 0.8s cubic-bezier(0.4, 0, 0.6, 1) 3;
    border-color: var(--primary) !important;
}

.slide-in-up {
    animation: slideInUp 0.5s ease-out forwards;
}

.slide-in-right {
    animation: slideInRight 0.5s ease-out forwards;
}

.fade-in {
    animation: fadeIn 0.5s ease-out forwards;
}

.pulse {
    animation: pulse 2s infinite;
}

/* Notification Styles */
.notification-container {
    position: fixed;
    top: 1rem;
    right: 1rem;
    z-index: 50;
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    max-width: 24rem;
}

.notification {
    display: flex;
    align-items: center;
    padding: 1rem;
    border-radius: 0.5rem;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    animation: slideInRight 0.3s ease-out forwards;
}

.notification-info {
    background-color: #ebf5ff;
    border-left: 4px solid var(--primary);
}

.notification-success {
    background-color: #ecfdf5;
    border-left: 4px solid var(--success);
}

.notification-warning {
    background-color: #fffbeb;
    border-left: 4px solid var(--warning);
}

.notification-error {
    background-color: #fef2f2;
    border-left: 4px solid var(--danger);
}

/* Custom Scrollbar */
::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 10px;
}

::-webkit-scrollbar-thumb {
    background: #d1d5db;
    border-radius: 10px;
}

::-webkit-scrollbar-thumb:hover {
    background: #9ca3af;
}

/* Product Card Styles */
.product-card {
    transition: all 0.3s ease;
    border-radius: 0.75rem;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    height: 100%;
}

.product-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 15px rgba(0, 0, 0, 0.1);
    border-color: var(--primary-light);
}

.product-image {
    position: relative;
    height: 120px;
    background-color: #f9fafb;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
}

.product-stock-badge {
    position: absolute;
    top: 8px;
    right: 8px;
    padding: 2px 8px;
    border-radius: 9999px;
    font-size: 0.7rem;
    font-weight: 500;
    z-index: 10;
}

.product-stock-indicator {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    padding: 4px;
    font-size: 0.7rem;
    text-align: center;
}

.product-details {
    padding: 0.75rem;
    display: flex;
    flex-direction: column;
    flex-grow: 1;
    background-color: white;
}

.product-name {
    font-weight: 500;
    font-size: 0.875rem;
    margin-bottom: 0.25rem;
    color: #1f2937;
    line-height: 1.25;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.product-price {
    font-weight: 600;
    color: var(--primary);
    font-size: 0.875rem;
}

/* Cart Item Styles */
.cart-item {
    transition: all 0.3s ease;
    border-radius: 0.75rem;
    padding: 0.75rem;
    margin-bottom: 0.75rem;
    background-color: white;
    border: 1px solid #e5e7eb;
    position: relative;
    overflow: hidden;
}

.cart-item:hover {
    border-color: var(--primary);
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
    transform: translateY(-2px);
}

.cart-item-name {
    color: #1f2937;
    display: -webkit-box;
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;
    overflow: hidden;
    font-weight: 500;
}

.cart-item-price {
    color: #6b7280;
    font-size: 0.85rem;
}

.cart-item-total {
    color: var(--primary);
    font-weight: 600;
}

.cart-item-image {
    width: 40px;
    height: 40px;
    border-radius: 8px;
    background-color: #f0f9ff;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
    background-size: cover;
    background-position: center;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

/* Animation for new cart items */
@keyframes highlightItem {
    0% {
        background-color: rgba(59, 130, 246, 0.2);
        transform: scale(0.98);
    }
    50% {
        background-color: rgba(59, 130, 246, 0.1);
        transform: scale(1.02);
    }
    100% {
        background-color: transparent;
        transform: scale(1);
    }
}

.animate-new-item {
    animation: highlightItem 1s ease-out;
}

/* Highlight container animation */
@keyframes highlightContainer {
    0% {
        box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.5);
    }
    50% {
        box-shadow: 0 0 0 4px rgba(59, 130, 246, 0.3);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(59, 130, 246, 0);
    }
}

.highlight-container {
    animation: highlightContainer 1.5s ease-out;
}

/* Button Hover Effects */
button {
    transition: all 0.2s ease;
}

/* Numpad Button Hover Effects */
.numpad-button:hover {
    background-color: #f9fafb;
    border-color: #d1d5db;
}

/* Modal Animations */
.modal-content {
    transition: all 0.3s ease;
}

/* Responsive Adjustments */
@media (max-width: 1024px) {
    .product-grid {
        grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
    }
}

@media (max-width: 768px) {
    .product-grid {
        grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
    }

    .product-image {
        height: 100px;
    }
}

/* Print Styles */
@media print {
    body * {
        visibility: hidden;
    }

    .print-content, .print-content * {
        visibility: visible;
    }

    .print-content {
        position: absolute;
        left: 0;
        top: 0;
        width: 100%;
    }

    .no-print {
        display: none !important;
    }
}

/* Dark Mode Styles */
.dark .bg-white {
    background-color: var(--dark-bg);
}

.dark .bg-gray-50 {
    background-color: var(--dark-surface);
}

.dark .bg-gray-100 {
    background-color: #334155;
}

.dark .text-gray-800 {
    color: var(--dark-text);
}

.dark .text-gray-700 {
    color: #e5e7eb;
}

.dark .text-gray-600 {
    color: #d1d5db;
}

.dark .text-gray-500 {
    color: #9ca3af;
}

.dark .border-gray-200 {
    border-color: var(--dark-border);
}

.dark .border-gray-300 {
    border-color: #475569;
}

.dark .hover\:bg-gray-50:hover {
    background-color: #334155;
}

.dark .hover\:bg-gray-100:hover {
    background-color: #475569;
}

.dark .hover\:text-gray-700:hover {
    color: #f9fafb;
}

/* Blue Shades in Dark Mode */
.dark .bg-blue-50 {
    background-color: rgba(59, 130, 246, 0.15);
}

.dark .bg-blue-100 {
    background-color: rgba(59, 130, 246, 0.25);
}

.dark .text-blue-500 {
    color: #60a5fa;
}

.dark .text-blue-600 {
    color: #3b82f6;
}

.dark .border-blue-500 {
    border-color: #3b82f6;
}

/* Other Colors in Dark Mode */
.dark .bg-green-50 {
    background-color: rgba(16, 185, 129, 0.15);
}

.dark .bg-yellow-50 {
    background-color: rgba(245, 158, 11, 0.15);
}

.dark .bg-red-50 {
    background-color: rgba(239, 68, 68, 0.15);
}

.dark .notification-info {
    background-color: rgba(59, 130, 246, 0.15);
    border-left: 4px solid var(--primary);
}

.dark .notification-success {
    background-color: rgba(16, 185, 129, 0.15);
    border-left: 4px solid var(--success);
}

.dark .notification-warning {
    background-color: rgba(245, 158, 11, 0.15);
    border-left: 4px solid var(--warning);
}

.dark .notification-error {
    background-color: rgba(239, 68, 68, 0.15);
    border-left: 4px solid var(--danger);
}

/* Product Card in Dark Mode */
.dark .product-card {
    background-color: var(--dark-bg);
    border-color: var(--dark-border);
}

.dark .product-card:hover {
    border-color: var(--primary);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
}

/* Cart Item in Dark Mode */
.dark .cart-item {
    background-color: var(--dark-bg);
    border-color: var(--dark-border);
}

.dark .cart-item:hover {
    border-color: var(--primary);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

/* Remixicon Fix */
[class^="ri-"]::before, [class*=" ri-"]::before {
    display: inline-block;
    font-family: 'remixicon' !important;
}
