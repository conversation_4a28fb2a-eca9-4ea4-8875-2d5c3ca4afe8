# تعريف المجلد كحزمة Python وتسجيل جميع الروتس

# استيراد البلوبرنت من ملفات الروتس
from routes.auth import auth_blueprint
from routes.dashboard import dashboard_blueprint
from routes.products import products_blueprint
from routes.customers import customers_blueprint
from routes.suppliers import suppliers_blueprint
from routes.sales import sales_blueprint
from routes.purchases import purchases_blueprint
from routes.pos import pos_blueprint
from routes.warehouses import warehouses_blueprint
from routes.reports import reports_blueprint
from routes.settings import settings_blueprint
from routes.users import users_blueprint
from routes.notifications import notifications_blueprint
from routes.deferred_sales import deferred_sales_blueprint
from routes.deferred_purchases import deferred_purchases_blueprint
from routes.returns import returns_blueprint
from routes.api import api_blueprint

# استيراد وظيفة تسجيل API
try:
    from api import register_api
except ImportError:
    # إذا لم يكن هناك وحدة API، نستخدم دالة فارغة
    def register_api(app):
        pass

def register_routes(app):
    """
    تسجيل جميع البلوبرنت في التطبيق
    """
    # تسجيل البلوبرنت الرئيسية
    app.register_blueprint(auth_blueprint)
    app.register_blueprint(dashboard_blueprint)
    app.register_blueprint(products_blueprint)
    app.register_blueprint(customers_blueprint)
    app.register_blueprint(suppliers_blueprint)
    app.register_blueprint(sales_blueprint)
    app.register_blueprint(purchases_blueprint)
    app.register_blueprint(pos_blueprint)
    app.register_blueprint(warehouses_blueprint)
    app.register_blueprint(reports_blueprint)
    app.register_blueprint(settings_blueprint)
    app.register_blueprint(users_blueprint)
    app.register_blueprint(notifications_blueprint)

    # تسجيل بلوبرنت المبيعات والمشتريات الآجلة
    app.register_blueprint(deferred_sales_blueprint)
    app.register_blueprint(deferred_purchases_blueprint)

    # تسجيل بلوبرنت المرتجعات
    app.register_blueprint(returns_blueprint)

    # تسجيل بلوبرنت API
    app.register_blueprint(api_blueprint)

    # تسجيل API القديم
    register_api(app)
