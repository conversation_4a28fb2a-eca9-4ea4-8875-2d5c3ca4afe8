
{% extends "base.html" %}
{% block content %}
<div class="p-6">
    <h2 class="text-2xl font-bold mb-4">الفواتير المعلقة</h2>
    <div class="bg-white rounded-lg shadow p-6">
        <div class="overflow-x-auto">
            <table class="min-w-full">
                <thead>
                    <tr>
                        <th class="px-6 py-3 border-b text-right">رقم الفاتورة</th>
                        <th class="px-6 py-3 border-b text-right">العميل</th>
                        <th class="px-6 py-3 border-b text-right">المبلغ</th>
                        <th class="px-6 py-3 border-b text-right">التاريخ</th>
                        <th class="px-6 py-3 border-b text-right">الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    {% for order in orders %}
                    <tr>
                        <td class="px-6 py-4 border-b">{{ order.invoice_number }}</td>
                        <td class="px-6 py-4 border-b">{{ order.customer.name if order.customer else 'عميل نقدي' }}</td>
                        <td class="px-6 py-4 border-b">{{ "%.2f"|format(order.total) }}</td>
                        <td class="px-6 py-4 border-b">{{ order.created_at.strftime('%Y-%m-%d %H:%M') }}</td>
                        <td class="px-6 py-4 border-b">
                            <button onclick="resumeOrder('{{ order.id }}')" class="bg-primary text-white px-4 py-2 rounded">
                                استئناف
                            </button>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
</div>
{% endblock %}
