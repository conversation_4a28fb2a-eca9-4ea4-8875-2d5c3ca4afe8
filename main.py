#!/usr/bin/env python3
"""
Nobara POS System - Main Application
نظام نوبارا لنقاط البيع - التطبيق الرئيسي

Developer: ENG/ Fouad Saber
Phone: 01020073527
Email: <EMAIL>
"""

import os
import sys
import socket
import webbrowser
from datetime import datetime
from flask import Flask, render_template_string, request, redirect, url_for, flash, session, jsonify
from flask_sqlalchemy import SQLAlchemy
from flask_login import LoginManager, UserMixin, login_user, logout_user, login_required, current_user
from werkzeug.security import generate_password_hash, check_password_hash

# Initialize extensions
db = SQLAlchemy()
login_manager = LoginManager()

# إعداد التسجيل
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# تهيئة نظام تسجيل الدخول
login_manager = LoginManager()
login_manager.init_app(app)
login_manager.login_view = 'auth.login'

@login_manager.user_loader
def load_user(user_id):
    return User.query.get(int(user_id))

# الكشف عن وضع التشغيل
app.debug = os.environ.get('FLASK_ENV', 'development') == 'development'

# تسجيل جميع الروتس
register_routes(app)

def get_local_ip():
    """الحصول على عنوان IP المحلي للجهاز"""
    try:
        # إنشاء اتصال مؤقت للحصول على عنوان IP المحلي
        s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        s.connect(("*******", 80))
        local_ip = s.getsockname()[0]
        s.close()
        return local_ip
    except Exception as e:
        logger.error(f"خطأ في الحصول على عنوان IP المحلي: {str(e)}")
        return "127.0.0.1"

if __name__ == "__main__":
    # الحصول على عنوان IP المحلي
    local_ip = get_local_ip()
    port = 5000
    url = f"http://{local_ip}:{port}"

    logger.info(f"جاري بدء التطبيق على العنوان: {url}")

    # فتح المتصفح تلقائيًا
    webbrowser.open(url)

    # تشغيل التطبيق
    app.run(host="0.0.0.0", port=port, debug=app.debug)
