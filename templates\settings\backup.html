
{% extends 'base.html' %}

{% block content %}
<div class="mb-6 flex items-center justify-between">
    <div>
        <h1 class="text-2xl font-bold text-gray-800 dark:text-gray-100">النسخ الاحتياطي واستعادة البيانات</h1>
        <p class="text-gray-600 dark:text-gray-400">إدارة النسخ الاحتياطية لقاعدة البيانات واستعادتها</p>
    </div>
    <a href="{{ url_for('settings.index') }}" class="flex items-center text-gray-700 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400">
        <i class="ri-arrow-right-line ml-1"></i>
        <span>العودة للإعدادات</span>
    </a>
</div>

<div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
    <!-- إعد<PERSON>ات النسخ الاحتياطي التلقائي -->
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm overflow-hidden">
        <div class="p-6">
            <h2 class="text-lg font-medium text-gray-800 dark:text-gray-100 mb-4">إعدادات النسخ الاحتياطي التلقائي</h2>
            <p class="text-gray-600 dark:text-gray-400 mb-4">قم بتكوين إعدادات النسخ الاحتياطي التلقائي لقاعدة البيانات.</p>

            <form id="backup-settings-form" class="space-y-4">
                <div class="flex items-center">
                    <label class="inline-flex items-center cursor-pointer">
                        <input type="checkbox" id="auto-backup" class="sr-only" {{ 'checked' if backup_settings.auto_backup else '' }}>
                        <div class="relative w-11 h-6 bg-gray-200 dark:bg-gray-700 rounded-full peer peer-checked:after:translate-x-full rtl:peer-checked:after:-translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-0.5 after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                        <span class="mr-3 text-sm font-medium text-gray-700 dark:text-gray-300">تفعيل النسخ الاحتياطي التلقائي</span>
                    </label>
                </div>

                <div id="auto-backup-settings" class="space-y-4 {{ 'hidden' if not backup_settings.auto_backup else '' }}">
                    <div>
                        <label for="backup-frequency" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">تكرار النسخ الاحتياطي</label>
                        <select id="backup-frequency" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-300">
                            <option value="daily" {{ 'selected' if backup_settings.backup_frequency == 'daily' else '' }}>يومي</option>
                            <option value="weekly" {{ 'selected' if backup_settings.backup_frequency == 'weekly' else '' }}>أسبوعي</option>
                            <option value="monthly" {{ 'selected' if backup_settings.backup_frequency == 'monthly' else '' }}>شهري</option>
                        </select>
                    </div>

                    <div id="backup-day-container" class="{{ 'hidden' if backup_settings.backup_frequency == 'daily' else '' }}">
                        <label for="backup-day" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                            <span id="day-label-weekly" class="{{ 'hidden' if backup_settings.backup_frequency != 'weekly' else '' }}">يوم الأسبوع</span>
                            <span id="day-label-monthly" class="{{ 'hidden' if backup_settings.backup_frequency != 'monthly' else '' }}">يوم الشهر</span>
                        </label>
                        <select id="backup-day" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-300">
                            <!-- سيتم ملء هذا القائمة بواسطة JavaScript -->
                        </select>
                    </div>

                    <div>
                        <label for="backup-time" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">وقت النسخ الاحتياطي</label>
                        <input type="time" id="backup-time" value="{{ backup_settings.backup_time }}" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-300">
                    </div>

                    <div>
                        <label for="backup-path" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">مسار حفظ النسخ الاحتياطية</label>
                        <input type="text" id="backup-path" value="{{ backup_settings.backup_path }}" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-300">
                        <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">المسار النسبي من مجلد البرنامج</p>
                    </div>

                    <div>
                        <label for="keep-backups" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">عدد النسخ الاحتياطية للاحتفاظ بها</label>
                        <input type="number" id="keep-backups" value="{{ backup_settings.keep_backups }}" min="1" max="100" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-300">
                        <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">سيتم حذف النسخ الاحتياطية القديمة تلقائيًا عند تجاوز هذا العدد</p>
                    </div>
                </div>

                <div class="pt-4">
                    <button type="submit" id="save-settings-btn" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 dark:focus:ring-offset-gray-800">
                        حفظ الإعدادات
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- إنشاء نسخة احتياطية يدوية -->
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm overflow-hidden">
        <div class="p-6">
            <h2 class="text-lg font-medium text-gray-800 dark:text-gray-100 mb-4">إنشاء نسخة احتياطية يدوية</h2>
            <p class="text-gray-600 dark:text-gray-400 mb-4">قم بإنشاء نسخة احتياطية من قاعدة البيانات بالكامل لحفظها على جهازك.</p>

            <div class="mb-6">
                <button id="create-backup-btn" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 dark:focus:ring-offset-gray-800">
                    <i class="ri-download-cloud-line ml-1"></i>
                    إنشاء نسخة احتياطية
                </button>
                <p class="text-xs text-gray-500 dark:text-gray-400 mt-2">سيتم حفظ النسخة الاحتياطية في المسار المحدد في الإعدادات</p>
            </div>

            <div class="border-t border-gray-200 dark:border-gray-700 pt-4">
                <h3 class="text-md font-medium text-gray-800 dark:text-gray-100 mb-3">استعادة قاعدة البيانات</h3>
                <p class="text-gray-600 dark:text-gray-400 mb-4">استعادة قاعدة البيانات من نسخة احتياطية موجودة (سيتم استبدال البيانات الحالية).</p>

                <div class="border border-yellow-200 dark:border-yellow-900 rounded-md p-4 bg-yellow-50 dark:bg-yellow-900/20 mb-4">
                    <div class="flex">
                        <div class="flex-shrink-0">
                            <i class="ri-alert-line text-yellow-600 dark:text-yellow-500"></i>
                        </div>
                        <div class="mr-3">
                            <p class="text-sm text-yellow-700 dark:text-yellow-500">
                                <strong>تنبيه:</strong> استعادة النسخة الاحتياطية ستؤدي إلى حذف جميع البيانات الحالية واستبدالها بالبيانات من النسخة الاحتياطية.
                            </p>
                        </div>
                    </div>
                </div>

                <form id="restore-form" class="space-y-4">
                    <div>
                        <label for="backup-file" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">اختر ملف النسخة الاحتياطية</label>
                        <input type="file" id="backup-file" name="backup_file" accept=".db" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-300">
                    </div>

                    <div>
                        <button type="submit" id="restore-btn" class="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 dark:focus:ring-offset-gray-800">
                            <i class="ri-refresh-line ml-1"></i>
                            استعادة البيانات
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- سجل النسخ الاحتياطية -->
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm overflow-hidden lg:col-span-2">
        <div class="p-6">
            <h2 class="text-lg font-medium text-gray-800 dark:text-gray-100 mb-4">سجل النسخ الاحتياطية</h2>

            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                    <thead class="bg-gray-50 dark:bg-gray-700">
                        <tr>
                            <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">اسم الملف</th>
                            <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">الحجم</th>
                            <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">التاريخ</th>
                            <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700" id="backup-files-container">
                        {% if backup_files %}
                            {% for file in backup_files %}
                                <tr>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">{{ file.filename }}</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">{{ file.size }}</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">{{ file.date }}</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                        <div class="flex space-x-2 space-x-reverse">
                                            <a href="{{ url_for('settings.download_backup', filename=file.filename) }}" class="text-blue-600 dark:text-blue-400 hover:text-blue-900 dark:hover:text-blue-300" title="تنزيل">
                                                <i class="ri-download-line"></i>
                                            </a>
                                            <button class="delete-backup-btn text-red-600 dark:text-red-400 hover:text-red-900 dark:hover:text-red-300" data-filename="{{ file.filename }}" title="حذف">
                                                <i class="ri-delete-bin-line"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                            {% endfor %}
                        {% else %}
                            <tr>
                                <td colspan="4" class="px-6 py-4 text-center text-sm text-gray-500 dark:text-gray-400">لا توجد نسخ احتياطية</td>
                            </tr>
                        {% endif %}
                    </tbody>
                </table>
                            </div>
                        </div>
                    </div>
                </div>

<!-- Notification Container -->
<div id="notification-container" class="fixed top-4 left-1/2 transform -translate-x-1/2 z-50"></div>

<!-- Loading Overlay -->
<div id="loading-overlay" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden">
    <div class="bg-white dark:bg-gray-800 rounded-lg p-6 max-w-md w-full">
        <div class="flex items-center justify-center mb-4">
            <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
        </div>
        <p class="text-center text-gray-700 dark:text-gray-300" id="loading-message">جاري المعالجة...</p>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // تهيئة قائمة أيام الأسبوع والشهر
        const backupDaySelect = document.getElementById('backup-day');
        const backupFrequency = document.getElementById('backup-frequency');
        const dayLabelWeekly = document.getElementById('day-label-weekly');
        const dayLabelMonthly = document.getElementById('day-label-monthly');
        const backupDayContainer = document.getElementById('backup-day-container');
        const autoBackupCheckbox = document.getElementById('auto-backup');
        const autoBackupSettings = document.getElementById('auto-backup-settings');

        // تحديث قائمة الأيام بناءً على التكرار
        function updateDayOptions() {
            backupDaySelect.innerHTML = '';

            if (backupFrequency.value === 'weekly') {
                dayLabelWeekly.classList.remove('hidden');
                dayLabelMonthly.classList.add('hidden');
                backupDayContainer.classList.remove('hidden');

                const days = [
                    { value: 1, text: 'الإثنين' },
                    { value: 2, text: 'الثلاثاء' },
                    { value: 3, text: 'الأربعاء' },
                    { value: 4, text: 'الخميس' },
                    { value: 5, text: 'الجمعة' },
                    { value: 6, text: 'السبت' },
                    { value: 7, text: 'الأحد' }
                ];

                days.forEach(day => {
                    const option = document.createElement('option');
                    option.value = day.value;
                    option.textContent = day.text;
                    option.selected = day.value == {{ backup_settings.backup_day }};
                    backupDaySelect.appendChild(option);
                });
            } else if (backupFrequency.value === 'monthly') {
                dayLabelWeekly.classList.add('hidden');
                dayLabelMonthly.classList.remove('hidden');
                backupDayContainer.classList.remove('hidden');

                for (let i = 1; i <= 28; i++) {
                    const option = document.createElement('option');
                    option.value = i;
                    option.textContent = i;
                    option.selected = i == {{ backup_settings.backup_day }};
                    backupDaySelect.appendChild(option);
                }
            } else {
                backupDayContainer.classList.add('hidden');
            }
        }

        // تحديث إعدادات النسخ الاحتياطي التلقائي
        autoBackupCheckbox.addEventListener('change', function() {
            if (this.checked) {
                autoBackupSettings.classList.remove('hidden');
            } else {
                autoBackupSettings.classList.add('hidden');
            }
        });

        // تحديث قائمة الأيام عند تغيير التكرار
        backupFrequency.addEventListener('change', updateDayOptions);

        // تهيئة قائمة الأيام
        updateDayOptions();

        // حفظ إعدادات النسخ الاحتياطي
        document.getElementById('backup-settings-form').addEventListener('submit', function(e) {
            e.preventDefault();

            const loadingOverlay = document.getElementById('loading-overlay');
            const loadingMessage = document.getElementById('loading-message');

            loadingMessage.textContent = 'جاري حفظ الإعدادات...';
            loadingOverlay.classList.remove('hidden');

            const formData = new FormData();
            formData.append('auto_backup', autoBackupCheckbox.checked);
            formData.append('backup_frequency', backupFrequency.value);
            formData.append('backup_time', document.getElementById('backup-time').value);
            formData.append('backup_day', backupDaySelect.value);
            formData.append('backup_path', document.getElementById('backup-path').value);
            formData.append('keep_backups', document.getElementById('keep-backups').value);

            fetch('{{ url_for("settings.update_backup_settings") }}', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                loadingOverlay.classList.add('hidden');

                if (data.success) {
                    showNotification(data.message, 'success');
                } else {
                    showNotification(data.error, 'error');
                }
            })
            .catch(error => {
                loadingOverlay.classList.add('hidden');
                showNotification('حدث خطأ أثناء حفظ الإعدادات', 'error');
                console.error(error);
            });
        });

        // إنشاء نسخة احتياطية
        document.getElementById('create-backup-btn').addEventListener('click', function() {
            const loadingOverlay = document.getElementById('loading-overlay');
            const loadingMessage = document.getElementById('loading-message');

            loadingMessage.textContent = 'جاري إنشاء النسخة الاحتياطية...';
            loadingOverlay.classList.remove('hidden');

            fetch('{{ url_for("settings.create_backup") }}', {
                method: 'POST'
            })
            .then(response => response.json())
            .then(data => {
                loadingOverlay.classList.add('hidden');

                if (data.success) {
                    showNotification(data.message, 'success');
                    // تحديث قائمة النسخ الاحتياطية
                    window.location.reload();
                } else {
                    showNotification(data.error, 'error');
                }
            })
            .catch(error => {
                loadingOverlay.classList.add('hidden');
                showNotification('حدث خطأ أثناء إنشاء النسخة الاحتياطية', 'error');
                console.error(error);
            });
        });

        // استعادة النسخة الاحتياطية
        document.getElementById('restore-form').addEventListener('submit', function(e) {
            e.preventDefault();

            const backupFile = document.getElementById('backup-file').files[0];

            if (!backupFile) {
                showNotification('يرجى اختيار ملف النسخة الاحتياطية', 'error');
                return;
            }

            if (!confirm('هل أنت متأكد من استعادة قاعدة البيانات؟ سيتم حذف جميع البيانات الحالية!')) {
                return;
            }

            const loadingOverlay = document.getElementById('loading-overlay');
            const loadingMessage = document.getElementById('loading-message');

            loadingMessage.textContent = 'جاري استعادة قاعدة البيانات...';
            loadingOverlay.classList.remove('hidden');

            const formData = new FormData();
            formData.append('backup_file', backupFile);

            fetch('{{ url_for("settings.restore_backup") }}', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                loadingOverlay.classList.add('hidden');

                if (data.success) {
                    showNotification(data.message, 'success');

                    // إعادة تحميل الصفحة بعد 3 ثوانٍ
                    setTimeout(() => {
                        window.location.reload();
                    }, 3000);
                } else {
                    showNotification(data.error, 'error');
                }
            })
            .catch(error => {
                loadingOverlay.classList.add('hidden');
                showNotification('حدث خطأ أثناء استعادة قاعدة البيانات', 'error');
                console.error(error);
            });
        });

        // حذف النسخة الاحتياطية
        document.querySelectorAll('.delete-backup-btn').forEach(button => {
            button.addEventListener('click', function() {
                const filename = this.getAttribute('data-filename');

                if (!confirm(`هل أنت متأكد من حذف النسخة الاحتياطية "${filename}"؟`)) {
                    return;
                }

                const loadingOverlay = document.getElementById('loading-overlay');
                const loadingMessage = document.getElementById('loading-message');

                loadingMessage.textContent = 'جاري حذف النسخة الاحتياطية...';
                loadingOverlay.classList.remove('hidden');

                fetch(`{{ url_for("settings.delete_backup", filename="PLACEHOLDER") }}`.replace('PLACEHOLDER', filename), {
                    method: 'POST'
                })
                .then(response => response.json())
                .then(data => {
                    loadingOverlay.classList.add('hidden');

                    if (data.success) {
                        showNotification(data.message, 'success');
                        // تحديث قائمة النسخ الاحتياطية
                        window.location.reload();
                    } else {
                        showNotification(data.error, 'error');
                    }
                })
                .catch(error => {
                    loadingOverlay.classList.add('hidden');
                    showNotification('حدث خطأ أثناء حذف النسخة الاحتياطية', 'error');
                    console.error(error);
                });
            });
        });

        // عرض الإشعارات
        function showNotification(message, type) {
            const container = document.getElementById('notification-container');
            const notification = document.createElement('div');

            notification.className = `notification notification-${type} bg-white dark:bg-gray-800 shadow-lg rounded-lg p-4 mb-4 flex items-start`;

            let icon = '';
            switch (type) {
                case 'success':
                    icon = '<div class="flex-shrink-0 w-6 h-6 text-green-500 mr-3"><i class="ri-checkbox-circle-line text-xl"></i></div>';
                    break;
                case 'error':
                    icon = '<div class="flex-shrink-0 w-6 h-6 text-red-500 mr-3"><i class="ri-error-warning-line text-xl"></i></div>';
                    break;
                case 'warning':
                    icon = '<div class="flex-shrink-0 w-6 h-6 text-yellow-500 mr-3"><i class="ri-alert-line text-xl"></i></div>';
                    break;
                default:
                    icon = '<div class="flex-shrink-0 w-6 h-6 text-blue-500 mr-3"><i class="ri-information-line text-xl"></i></div>';
            }

            notification.innerHTML = `
                ${icon}
                <div class="flex-1">
                    <p class="text-sm text-gray-800 dark:text-gray-200">${message}</p>
                </div>
                <button class="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 mr-2">
                    <i class="ri-close-line"></i>
                </button>
            `;

            container.appendChild(notification);

            // إزالة الإشعار بعد 5 ثوانٍ
            setTimeout(() => {
                notification.classList.add('opacity-0');
                setTimeout(() => {
                    notification.remove();
                }, 300);
            }, 5000);

            // إزالة الإشعار عند النقر على زر الإغلاق
            notification.querySelector('button').addEventListener('click', () => {
                notification.remove();
            });
        }
    });
</script>
{% endblock %}
