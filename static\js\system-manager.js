/**
 * مدير النظام
 * يوفر واجهة برمجية موحدة لإدارة وظائف النظام المختلفة
 */

class SystemManager {
    constructor() {
        // تهيئة المكونات الفرعية
        this.notifications = null;
        this.settings = {};
        this.cache = {};
        this.modules = {};

        // تهيئة المدير
        this.init();
    }

    /**
     * تهيئة مدير النظام
     */
    init() {
        // تهيئة نظام الإشعارات
        this.initNotifications();

        // تحميل إعدادات النظام
        this.loadSettings();

        // إضافة مستمعي الأحداث
        this.setupEventListeners();

        console.log('تم تهيئة مدير النظام بنجاح');
    }

    /**
     * تهيئة نظام الإشعارات
     */
    initNotifications() {
        if (window.NotificationsSystem) {
            this.notifications = new NotificationsSystem({
                position: 'top-left',
                darkMode: this.isDarkMode()
            });

            console.log('تم تهيئة نظام الإشعارات');
        } else {
            console.warn('لم يتم العثور على نظام الإشعارات');
        }
    }

    /**
     * تهيئة مدير وضع الدارك مود - تم إلغاء هذه الوظيفة
     */
    initDarkMode() {
        // تم إلغاء وضع الدارك مود من البرنامج
        console.log('تم إلغاء وضع الدارك مود من البرنامج');
    }

    /**
     * تحميل إعدادات النظام
     */
    loadSettings() {
        // محاولة تحميل الإعدادات من التخزين المحلي
        const savedSettings = localStorage.getItem('system-settings');

        if (savedSettings) {
            try {
                this.settings = JSON.parse(savedSettings);
                console.log('تم تحميل إعدادات النظام من التخزين المحلي');
            } catch (error) {
                console.error('خطأ في تحليل إعدادات النظام:', error);
                this.settings = {};
            }
        }

        // تحميل الإعدادات من الخادم
        fetch('/api/settings')
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // دمج الإعدادات المحلية مع إعدادات الخادم
                    this.settings = { ...this.settings, ...data.settings };

                    // حفظ الإعدادات في التخزين المحلي
                    this.saveSettings();

                    console.log('تم تحميل إعدادات النظام من الخادم');
                }
            })
            .catch(error => {
                console.error('خطأ في تحميل إعدادات النظام من الخادم:', error);
            });
    }

    /**
     * حفظ إعدادات النظام
     */
    saveSettings() {
        try {
            localStorage.setItem('system-settings', JSON.stringify(this.settings));
            console.log('تم حفظ إعدادات النظام في التخزين المحلي');
            return true;
        } catch (error) {
            console.error('خطأ في حفظ إعدادات النظام:', error);
            return false;
        }
    }

    /**
     * إعداد مستمعي الأحداث
     */
    setupEventListeners() {
        // مستمع لتحميل الصفحة
        window.addEventListener('load', () => {
            this.onPageLoad();
        });

        // مستمع للخروج من الصفحة
        window.addEventListener('beforeunload', () => {
            this.onPageUnload();
        });

        // مستمع للأخطاء
        window.addEventListener('error', (event) => {
            this.handleError(event.error);
        });

        // مستمع للوعود المرفوضة غير المعالجة
        window.addEventListener('unhandledrejection', (event) => {
            this.handleError(event.reason);
        });

        // مستمع لزر الإشعارات
        const notificationBtn = document.getElementById('notificationBtn');
        if (notificationBtn) {
            notificationBtn.addEventListener('click', () => {
                const notificationsMenu = document.getElementById('notificationsMenu');
                if (notificationsMenu) {
                    // تبديل عرض قائمة الإشعارات
                    const isVisible = !notificationsMenu.classList.contains('hidden');
                    if (isVisible) {
                        notificationsMenu.classList.add('hidden');
                        notificationsMenu.classList.remove('scale-100', 'opacity-100');
                        notificationsMenu.classList.add('scale-95', 'opacity-0');
                    } else {
                        // تحديث قائمة الإشعارات قبل عرضها
                        this.updateNotificationsList();

                        notificationsMenu.classList.remove('hidden');
                        setTimeout(() => {
                            notificationsMenu.classList.remove('scale-95', 'opacity-0');
                            notificationsMenu.classList.add('scale-100', 'opacity-100');
                        }, 10);
                    }
                }
            });
        }

        // مستمع لزر تحديد جميع الإشعارات كمقروءة
        const markAllReadBtn = document.getElementById('markAllNotificationsRead');
        if (markAllReadBtn) {
            markAllReadBtn.addEventListener('click', (e) => {
                e.preventDefault();
                this.markAllNotificationsAsRead();
            });
        }

        // إغلاق قائمة الإشعارات عند النقر خارجها
        document.addEventListener('click', (e) => {
            const notificationsMenu = document.getElementById('notificationsMenu');
            const notificationBtn = document.getElementById('notificationBtn');

            if (notificationsMenu && !notificationsMenu.classList.contains('hidden')) {
                if (!notificationsMenu.contains(e.target) && !notificationBtn.contains(e.target)) {
                    notificationsMenu.classList.add('hidden');
                    notificationsMenu.classList.remove('scale-100', 'opacity-100');
                    notificationsMenu.classList.add('scale-95', 'opacity-0');
                }
            }
        });
    }

    /**
     * معالجة تحميل الصفحة
     */
    onPageLoad() {
        // تحديث عدد الإشعارات غير المقروءة
        this.updateNotificationCount();

        // تحديث قائمة الإشعارات
        this.updateNotificationsList();

        // تهيئة المخططات والرسوم البيانية
        this.initCharts();

        // بدء التحديث الدوري للإشعارات
        this.startPeriodicUpdates();

        console.log('تم تحميل الصفحة بنجاح');
    }

    /**
     * بدء التحديثات الدورية
     */
    startPeriodicUpdates() {
        // تحديث الإشعارات كل دقيقة
        setInterval(() => {
            this.updateNotificationCount();
        }, 60000);

        // تحديث البيانات الإحصائية كل 5 دقائق
        setInterval(() => {
            if (window.dashboardManager) {
                window.dashboardManager.refreshDashboard();
            }
        }, 300000);
    }

    /**
     * معالجة الخروج من الصفحة
     */
    onPageUnload() {
        // حفظ أي بيانات مؤقتة إذا لزم الأمر
        console.log('جاري الخروج من الصفحة');
    }

    /**
     * معالجة الأخطاء
     * @param {Error} error - الخطأ المراد معالجته
     */
    handleError(error) {
        console.error('حدث خطأ في النظام:', error);

        // عرض إشعار للمستخدم
        if (this.notifications) {
            this.notifications.show(
                'خطأ في النظام',
                error.message || 'حدث خطأ غير متوقع في النظام',
                'error'
            );
        }

        // إرسال الخطأ إلى الخادم للتسجيل
        this.logError(error);
    }

    /**
     * تسجيل الخطأ في الخادم
     * @param {Error} error - الخطأ المراد تسجيله
     */
    logError(error) {
        fetch('/api/log-error', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                message: error.message,
                stack: error.stack,
                url: window.location.href,
                timestamp: new Date().toISOString()
            })
        }).catch(err => {
            console.error('فشل في إرسال سجل الخطأ إلى الخادم:', err);
        });
    }

    /**
     * تحديث واجهة المستخدم - تم إلغاء وظيفة الدارك مود
     */
    updateUIForDarkMode() {
        // تم إلغاء وضع الدارك مود من البرنامج
    }

    /**
     * تحديث عدد الإشعارات غير المقروءة
     */
    updateNotificationCount() {
        fetch('/api/notifications/count')
            .then(response => response.json())
            .then(data => {
                const badge = document.getElementById('notificationUnreadBadge');
                if (badge) {
                    if (data.count > 0) {
                        badge.textContent = data.count;
                        badge.classList.remove('hidden');
                    } else {
                        badge.classList.add('hidden');
                    }
                }
            })
            .catch(error => {
                console.error('خطأ في تحديث عدد الإشعارات:', error);
            });
    }

    /**
     * تحديث قائمة الإشعارات
     */
    updateNotificationsList() {
        fetch('/api/notifications/recent')
            .then(response => response.json())
            .then(data => {
                // تحديث عدد الإشعارات غير المقروءة
                const badge = document.getElementById('notificationUnreadBadge');
                if (badge) {
                    if (data.unread_count > 0) {
                        badge.textContent = data.unread_count;
                        badge.classList.remove('hidden');
                    } else {
                        badge.classList.add('hidden');
                    }
                }

                // تحديث قائمة الإشعارات
                const container = document.querySelector('.notifications-container');
                const noNotificationsMessage = document.getElementById('noNotificationsMessage');

                if (container) {
                    if (data.notifications && data.notifications.length > 0) {
                        // إخفاء رسالة عدم وجود إشعارات
                        if (noNotificationsMessage) {
                            noNotificationsMessage.style.display = 'none';
                        }

                        // إنشاء قائمة الإشعارات
                        let notificationsHTML = '';

                        data.notifications.forEach(notification => {
                            const isReadClass = notification.is_read ? 'bg-gray-50 dark:bg-gray-800' : 'bg-blue-50 dark:bg-blue-900';
                            const iconClass = this.getNotificationIconClass(notification.type);

                            notificationsHTML += `
                            <a href="${notification.link || '#'}" class="dropdown-item notification-item ${isReadClass} d-flex align-items-center p-3 border-bottom" data-id="${notification.id}">
                                <div class="mr-3">
                                    <div class="icon-circle ${this.getNotificationIconBgClass(notification.type)}">
                                        <i class="${iconClass} text-white"></i>
                                    </div>
                                </div>
                                <div>
                                    <div class="small text-gray-500">${notification.created_at}</div>
                                    <span class="font-weight-bold">${notification.title}</span>
                                    <div class="small text-gray-600">${notification.message}</div>
                                </div>
                            </a>
                            `;
                        });

                        container.innerHTML = notificationsHTML;

                        // إضافة معالجات الأحداث للإشعارات
                        document.querySelectorAll('.notification-item').forEach(item => {
                            item.addEventListener('click', () => {
                                const id = item.dataset.id;
                                if (id) {
                                    this.markNotificationAsRead(id);
                                }
                            });
                        });
                    } else {
                        // عرض رسالة عدم وجود إشعارات
                        if (noNotificationsMessage) {
                            noNotificationsMessage.style.display = 'block';
                        }

                        // إفراغ القائمة
                        container.innerHTML = '';
                    }
                }
            })
            .catch(error => {
                console.error('خطأ في تحديث قائمة الإشعارات:', error);
            });
    }

    /**
     * تحديد إشعار كمقروء
     * @param {number} id - معرف الإشعار
     */
    markNotificationAsRead(id) {
        fetch(`/api/notifications/mark-read/${id}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            }
        })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // تحديث قائمة الإشعارات
                    this.updateNotificationsList();
                }
            })
            .catch(error => {
                console.error('خطأ في تحديد الإشعار كمقروء:', error);
            });
    }

    /**
     * تحديد جميع الإشعارات كمقروءة
     */
    markAllNotificationsAsRead() {
        fetch('/api/notifications/mark-all-read', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            }
        })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // تحديث قائمة الإشعارات
                    this.updateNotificationsList();
                }
            })
            .catch(error => {
                console.error('خطأ في تحديد جميع الإشعارات كمقروءة:', error);
            });
    }

    /**
     * الحصول على صنف أيقونة الإشعار
     * @param {string} type - نوع الإشعار
     * @returns {string} - صنف الأيقونة
     */
    getNotificationIconClass(type) {
        switch (type) {
            case 'success': return 'ri-check-line';
            case 'error': return 'ri-error-warning-line';
            case 'warning': return 'ri-alert-line';
            case 'info': return 'ri-information-line';
            default: return 'ri-notification-3-line';
        }
    }

    /**
     * الحصول على صنف خلفية أيقونة الإشعار
     * @param {string} type - نوع الإشعار
     * @returns {string} - صنف خلفية الأيقونة
     */
    getNotificationIconBgClass(type) {
        switch (type) {
            case 'success': return 'bg-success';
            case 'error': return 'bg-danger';
            case 'warning': return 'bg-warning';
            case 'info': return 'bg-info';
            default: return 'bg-primary';
        }
    }

    /**
     * تهيئة المخططات والرسوم البيانية
     */
    initCharts() {
        // تهيئة مخططات ApexCharts
        if (window.ApexCharts) {
            // تطبيق سمة الوضع الداكن إذا كان مفعلاً
            if (this.isDarkMode()) {
                ApexCharts.exec(undefined, 'updateOptions', {
                    theme: {
                        mode: 'dark'
                    }
                }, false, true);
            }
        }

        // تهيئة مخططات ECharts
        if (window.echarts) {
            // تطبيق سمة الوضع الداكن إذا كان مفعلاً
            if (this.isDarkMode()) {
                const darkTheme = {
                    backgroundColor: '#1e293b',
                    textStyle: {
                        color: '#e2e8f0'
                    },
                    title: {
                        textStyle: {
                            color: '#e2e8f0'
                        }
                    },
                    legend: {
                        textStyle: {
                            color: '#e2e8f0'
                        }
                    },
                    xAxis: {
                        axisLine: {
                            lineStyle: {
                                color: '#334155'
                            }
                        },
                        axisLabel: {
                            color: '#e2e8f0'
                        },
                        splitLine: {
                            lineStyle: {
                                color: '#334155'
                            }
                        }
                    },
                    yAxis: {
                        axisLine: {
                            lineStyle: {
                                color: '#334155'
                            }
                        },
                        axisLabel: {
                            color: '#e2e8f0'
                        },
                        splitLine: {
                            lineStyle: {
                                color: '#334155'
                            }
                        }
                    }
                };

                document.querySelectorAll('.echarts').forEach(el => {
                    const chart = echarts.getInstanceByDom(el);
                    if (chart) {
                        chart.setOption({
                            backgroundColor: darkTheme.backgroundColor,
                            textStyle: darkTheme.textStyle,
                            title: darkTheme.title,
                            legend: darkTheme.legend,
                            xAxis: darkTheme.xAxis,
                            yAxis: darkTheme.yAxis
                        });
                    }
                });
            }
        }
    }

    /**
     * تحديث الرسوم البيانية
     * @param {boolean} darkMode - حالة وضع الدارك مود
     */
    updateCharts(darkMode) {
        // تحديث مخططات ApexCharts
        if (window.ApexCharts) {
            ApexCharts.exec(undefined, 'updateOptions', {
                theme: {
                    mode: darkMode ? 'dark' : 'light'
                }
            }, false, true);
        }

        // تحديث مخططات ECharts
        if (window.echarts) {
            const theme = darkMode ? {
                backgroundColor: '#1e293b',
                textStyle: {
                    color: '#e2e8f0'
                },
                title: {
                    textStyle: {
                        color: '#e2e8f0'
                    }
                },
                legend: {
                    textStyle: {
                        color: '#e2e8f0'
                    }
                },
                xAxis: {
                    axisLine: {
                        lineStyle: {
                            color: '#334155'
                        }
                    },
                    axisLabel: {
                        color: '#e2e8f0'
                    },
                    splitLine: {
                        lineStyle: {
                            color: '#334155'
                        }
                    }
                },
                yAxis: {
                    axisLine: {
                        lineStyle: {
                            color: '#334155'
                        }
                    },
                    axisLabel: {
                        color: '#e2e8f0'
                    },
                    splitLine: {
                        lineStyle: {
                            color: '#334155'
                        }
                    }
                }
            } : {
                backgroundColor: '#ffffff',
                textStyle: {
                    color: '#374151'
                },
                title: {
                    textStyle: {
                        color: '#111827'
                    }
                },
                legend: {
                    textStyle: {
                        color: '#374151'
                    }
                },
                xAxis: {
                    axisLine: {
                        lineStyle: {
                            color: '#e5e7eb'
                        }
                    },
                    axisLabel: {
                        color: '#374151'
                    },
                    splitLine: {
                        lineStyle: {
                            color: '#e5e7eb'
                        }
                    }
                },
                yAxis: {
                    axisLine: {
                        lineStyle: {
                            color: '#e5e7eb'
                        }
                    },
                    axisLabel: {
                        color: '#374151'
                    },
                    splitLine: {
                        lineStyle: {
                            color: '#e5e7eb'
                        }
                    }
                }
            };

            document.querySelectorAll('.echarts').forEach(el => {
                const chart = echarts.getInstanceByDom(el);
                if (chart) {
                    chart.setOption({
                        backgroundColor: theme.backgroundColor,
                        textStyle: theme.textStyle,
                        title: {
                            textStyle: theme.title.textStyle
                        },
                        legend: {
                            textStyle: theme.legend.textStyle
                        },
                        xAxis: {
                            axisLine: {
                                lineStyle: theme.xAxis.axisLine.lineStyle
                            },
                            axisLabel: {
                                color: theme.xAxis.axisLabel.color
                            },
                            splitLine: {
                                lineStyle: theme.xAxis.splitLine.lineStyle
                            }
                        },
                        yAxis: {
                            axisLine: {
                                lineStyle: theme.yAxis.axisLine.lineStyle
                            },
                            axisLabel: {
                                color: theme.yAxis.axisLabel.color
                            },
                            splitLine: {
                                lineStyle: theme.yAxis.splitLine.lineStyle
                            }
                        }
                    });
                }
            });
        }
    }

    /**
     * التحقق من تفعيل وضع الدارك مود - تم إلغاء هذه الوظيفة
     * @returns {boolean} - حالة وضع الدارك مود
     */
    isDarkMode() {
        return false; // تم إلغاء وضع الدارك مود من البرنامج
    }

    /**
     * تبديل وضع الدارك مود - تم إلغاء هذه الوظيفة
     */
    toggleDarkMode() {
        // تم إلغاء وضع الدارك مود من البرنامج
    }

    /**
     * عرض إشعار
     * @param {string} title - عنوان الإشعار
     * @param {string} message - نص الإشعار
     * @param {string} type - نوع الإشعار (success, error, warning, info)
     * @param {object} options - خيارات إضافية
     */
    showNotification(title, message, type = 'info', options = {}) {
        if (this.notifications) {
            return this.notifications.show(title, message, type, options);
        } else {
            console.warn('نظام الإشعارات غير متاح');
            alert(`${title}: ${message}`);
            return null;
        }
    }
}

// تصدير الكلاس للاستخدام العالمي
window.SystemManager = SystemManager;

// تهيئة مدير النظام عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', () => {
    window.systemManager = new SystemManager();
});
