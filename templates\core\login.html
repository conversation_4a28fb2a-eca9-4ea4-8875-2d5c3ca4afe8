<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    {% include 'partials/head.html' %}
    <style>
        .login-bg {
            background-color: #f9fafb;
            background-image: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%234F46E5' fill-opacity='0.05'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
        }

        .login-card {
            backdrop-filter: blur(10px);
            background-color: rgba(255, 255, 255, 0.9);
            box-shadow: 0 10px 25px rgba(79, 70, 229, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.3);
            transition: all 0.3s ease;
        }

        .login-card:hover {
            box-shadow: 0 15px 30px rgba(79, 70, 229, 0.15);
            transform: translateY(-5px);
        }

        .input-field {
            transition: all 0.3s ease;
            border: 1px solid #e5e7eb;
        }

        .input-field:focus {
            border-color: #4F46E5;
            box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.2);
        }

        .login-button {
            background: linear-gradient(135deg, #4F46E5 0%, #6366F1 100%);
            transition: all 0.3s ease;
        }

        .login-button:hover {
            background: linear-gradient(135deg, #4338CA 0%, #4F46E5 100%);
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(79, 70, 229, 0.3);
        }

        .logo-container {
            animation: fadeInDown 1s ease-out;
        }

        @keyframes fadeInDown {
            from {
                opacity: 0;
                transform: translateY(-20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .form-container {
            animation: fadeIn 1s ease-out 0.3s forwards;
            opacity: 0;
        }

        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }
    </style>
</head>
<body class="login-bg min-h-screen flex items-center justify-center p-4">
    <div class="w-full max-w-md login-card rounded-2xl overflow-hidden">
        <div class="p-8 sm:p-10">
            <div class="text-center mb-8 logo-container">
                {% if settings and settings.business and settings.business.logo %}
                <img src="{{ settings.business.logo }}" alt="{{ settings.business.name or 'Nobara' }}" class="h-20 mx-auto mb-3">
                {% else %}
                <img src="{{ url_for('static', filename='img/nobara-logo.svg') }}" alt="Nobara" class="h-20 mx-auto mb-3">
                {% endif %}
                <p class="text-gray-600 mt-2 text-lg">نظام نقاط البيع الذكي</p>
            </div>

            {% with messages = get_flashed_messages(with_categories=true) %}
              {% if messages %}
                {% for category, message in messages %}
                  <div class="mb-4 p-4 rounded-md {% if category == 'success' %}bg-green-50 text-green-700{% elif category == 'danger' %}bg-red-50 text-red-700{% else %}bg-blue-50 text-blue-700{% endif %}">
                    {{ message }}
                  </div>
                {% endfor %}
              {% endif %}
            {% endwith %}

            <form method="POST" action="{{ url_for('auth.login') }}" class="space-y-6 form-container">
                <div class="relative">
                    <label for="username" class="block text-sm font-medium text-gray-700 mb-2">اسم المستخدم</label>
                    <div class="relative">
                        <div class="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none text-gray-400">
                            <i class="ri-user-line"></i>
                        </div>
                        <input id="username" name="username" type="text" required class="input-field w-full px-4 py-3 pr-10 bg-white border border-gray-200 rounded-lg focus:outline-none focus:ring-0 focus:border-primary focus:bg-white transition-all">
                    </div>
                </div>

                <div class="relative">
                    <div class="flex items-center justify-between mb-2">
                        <label for="password" class="block text-sm font-medium text-gray-700">كلمة المرور</label>
                        <a href="#" class="text-xs text-primary hover:text-indigo-700">نسيت كلمة المرور؟</a>
                    </div>
                    <div class="relative">
                        <div class="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none text-gray-400">
                            <i class="ri-lock-line"></i>
                        </div>
                        <input id="password" name="password" type="password" required class="input-field w-full px-4 py-3 pr-10 bg-white border border-gray-200 rounded-lg focus:outline-none focus:ring-0 focus:border-primary focus:bg-white transition-all">
                        <div class="absolute inset-y-0 left-0 flex items-center pl-3 cursor-pointer" id="togglePassword">
                            <i class="ri-eye-off-line text-gray-400 hover:text-gray-600"></i>
                        </div>
                    </div>
                </div>

                <div class="flex items-center">
                    <input id="remember" name="remember" type="checkbox" class="h-4 w-4 text-primary border-gray-300 rounded">
                    <label for="remember" class="mr-2 block text-sm text-gray-700">تذكرني</label>
                </div>

                <div>
                    <button type="submit" class="login-button w-full py-3 px-4 text-white rounded-lg focus:outline-none transition-all font-medium">
                        <i class="ri-login-box-line ml-1"></i> تسجيل الدخول
                    </button>
                </div>
            </form>

            <!-- تم إزالة رابط إنشاء حساب المسؤول بناءً على طلب العميل -->

            <div class="mt-8 pt-6 border-t border-gray-200">
                <div class="flex flex-col items-center justify-center">
                    <div class="text-center">
                        <p class="text-sm text-gray-500 mb-1">Powered By</p>
                        <p class="text-base font-bold text-primary-600">ENG/ Fouad Saber</p>
                        <p class="text-xs text-gray-500 mt-1">Tel: 01020073527</p>
                    </div>
                </div>
            </div>
        </div>

        <div class="bg-gray-50 py-4 text-center border-t border-gray-100">
            <p class="text-xs text-gray-500">
                © 2025 Nobara. جميع الحقوق محفوظة.
            </p>
        </div>
    </div>

    <!-- مكتبة تسجيل الأخطاء -->
    <script src="{{ url_for('static', filename='js/error-logger.js') }}?v=1.0.0"></script>

    <script>
        // Toggle password visibility
        document.getElementById('togglePassword').addEventListener('click', function() {
            const passwordInput = document.getElementById('password');
            const icon = this.querySelector('i');

            if (passwordInput.type === 'password') {
                passwordInput.type = 'text';
                icon.classList.remove('ri-eye-off-line');
                icon.classList.add('ri-eye-line');
            } else {
                passwordInput.type = 'password';
                icon.classList.remove('ri-eye-line');
                icon.classList.add('ri-eye-off-line');
            }
        });

        // تهيئة نظام تسجيل الأخطاء
        document.addEventListener('DOMContentLoaded', function() {
            // تسجيل أخطاء النموذج
            const loginForm = document.querySelector('form');
            if (loginForm) {
                loginForm.addEventListener('submit', function(event) {
                    const username = document.getElementById('username').value;
                    const password = document.getElementById('password').value;

                    // التحقق من صحة البيانات
                    if (!username || !password) {
                        event.preventDefault();

                        // تسجيل الخطأ
                        errorLogger.logError({
                            message: 'محاولة تسجيل دخول غير صالحة: حقول فارغة',
                            stack: 'Form validation error: Empty fields',
                            url: window.location.href,
                            component: 'login',
                            severity: 'warning',
                            timestamp: new Date().toISOString(),
                            browser: navigator.userAgent,
                            os: navigator.platform
                        });

                        // عرض رسالة خطأ
                        alert('يرجى إدخال اسم المستخدم وكلمة المرور');
                    }
                });
            }
        });
    </script>
</body>
</html>