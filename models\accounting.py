"""
نماذج النظام المحاسبي وفقاً للمعايير المحاسبية الدولية
"""

from datetime import datetime
from app import db
from sqlalchemy.orm import relationship
from sqlalchemy import func, and_, or_


class AccountGroup(db.Model):
    """نموذج مجموعات الحسابات (Account Groups)"""
    id = db.Column(db.Integer, primary_key=True)
    code = db.Column(db.String(10), nullable=False, unique=True)
    name = db.Column(db.String(128), nullable=False)
    description = db.Column(db.Text)
    group_type = db.Column(db.String(20), nullable=False, index=True)  # asset, liability, equity, revenue, expense
    is_active = db.Column(db.Bo<PERSON>an, default=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # العلاقات
    accounts = relationship('Account', backref='group')

    def __repr__(self):
        return f"<AccountGroup {self.code}: {self.name}>"


class Account(db.Model):
    """نموذج دليل الحسابات (Chart of Accounts) وفقاً للمعايير المحاسبية الدولية"""
    id = db.Column(db.Integer, primary_key=True)
    code = db.Column(db.String(20), nullable=False, unique=True)
    name = db.Column(db.String(128), nullable=False)
    description = db.Column(db.Text)
    account_type = db.Column(db.String(20), nullable=False, index=True)  # asset, liability, equity, revenue, expense
    account_subtype = db.Column(db.String(30))  # تصنيف فرعي للحساب (مثل: current_asset, fixed_asset, etc.)
    group_id = db.Column(db.Integer, db.ForeignKey('account_group.id'), index=True)
    parent_id = db.Column(db.Integer, db.ForeignKey('account.id'), index=True)
    level = db.Column(db.Integer, default=0)  # مستوى الحساب في الهيكل الهرمي (0 للحسابات الرئيسية)
    is_active = db.Column(db.Boolean, default=True)
    is_control_account = db.Column(db.Boolean, default=False)  # حساب مراقبة (مثل: حسابات العملاء، حسابات الموردين)
    is_bank_account = db.Column(db.Boolean, default=False)  # حساب بنكي
    is_cash_account = db.Column(db.Boolean, default=False)  # حساب نقدي
    currency_id = db.Column(db.Integer, db.ForeignKey('currency.id'))  # العملة الخاصة بالحساب
    allow_manual_transactions = db.Column(db.Boolean, default=True)  # السماح بالمعاملات اليدوية
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # العلاقات
    parent = relationship('Account', remote_side=[id], backref='children')
    journal_items = relationship('JournalEntryItem', backref='account')
    currency = relationship('Currency')

    def __repr__(self):
        return f"<Account {self.code}: {self.name}>"

    @property
    def full_name(self):
        """الاسم الكامل للحساب مع الكود"""
        return f"{self.code} - {self.name}"

    @property
    def balance(self):
        """رصيد الحساب"""
        debit_sum = sum(item.debit for item in self.journal_items if item.journal_entry.status == 'posted')
        credit_sum = sum(item.credit for item in self.journal_items if item.journal_entry.status == 'posted')

        if self.account_type in ['asset', 'expense']:
            return debit_sum - credit_sum
        else:
            return credit_sum - debit_sum

    @property
    def balance_with_children(self):
        """رصيد الحساب مع الحسابات الفرعية"""
        balance = self.balance

        # إضافة أرصدة الحسابات الفرعية
        for child in self.children:
            balance += child.balance_with_children

        return balance

    def get_balance_for_period(self, start_date, end_date):
        """الحصول على رصيد الحساب لفترة محددة"""
        from sqlalchemy import and_

        # الحصول على مجموع المدين والدائن للفترة المحددة
        debit_sum = db.session.query(func.sum(JournalEntryItem.debit)).join(
            JournalEntry, JournalEntryItem.journal_entry_id == JournalEntry.id
        ).filter(
            and_(
                JournalEntryItem.account_id == self.id,
                JournalEntry.status == 'posted',
                JournalEntry.date >= start_date,
                JournalEntry.date <= end_date
            )
        ).scalar() or 0

        credit_sum = db.session.query(func.sum(JournalEntryItem.credit)).join(
            JournalEntry, JournalEntryItem.journal_entry_id == JournalEntry.id
        ).filter(
            and_(
                JournalEntryItem.account_id == self.id,
                JournalEntry.status == 'posted',
                JournalEntry.date >= start_date,
                JournalEntry.date <= end_date
            )
        ).scalar() or 0

        if self.account_type in ['asset', 'expense']:
            return debit_sum - credit_sum
        else:
            return credit_sum - debit_sum


class JournalEntry(db.Model):
    """نموذج القيود المحاسبية (Journal Entries) وفقاً للمعايير المحاسبية الدولية"""
    id = db.Column(db.Integer, primary_key=True)
    reference_number = db.Column(db.String(20), nullable=False, unique=True)
    date = db.Column(db.Date, nullable=False, index=True)
    description = db.Column(db.Text)
    status = db.Column(db.String(20), nullable=False, default='draft', index=True)  # draft, posted, cancelled
    entry_type = db.Column(db.String(30), index=True)  # manual, sales, purchase, payment, receipt, etc.
    source_document_type = db.Column(db.String(30))  # invoice, receipt, payment, etc.
    source_document_id = db.Column(db.Integer)  # معرف المستند المصدر
    fiscal_period_id = db.Column(db.Integer, db.ForeignKey('fiscal_period.id'), index=True)
    currency_id = db.Column(db.Integer, db.ForeignKey('currency.id'))
    exchange_rate = db.Column(db.Float, default=1.0)
    is_recurring = db.Column(db.Boolean, default=False)  # هل هو قيد متكرر
    recurrence_frequency = db.Column(db.String(20))  # daily, weekly, monthly, yearly
    recurrence_end_date = db.Column(db.Date)
    notes = db.Column(db.Text)
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'))
    approved_by = db.Column(db.Integer, db.ForeignKey('user.id'))
    approved_at = db.Column(db.DateTime)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # العلاقات
    items = relationship('JournalEntryItem', backref='journal_entry', cascade='all, delete-orphan')
    user = relationship('User', foreign_keys=[user_id], backref='journal_entries')
    approver = relationship('User', foreign_keys=[approved_by], backref='approved_journal_entries')
    fiscal_period = relationship('FiscalPeriod', backref='journal_entries')
    currency = relationship('Currency', backref='journal_entries')

    def __repr__(self):
        return f"<JournalEntry {self.reference_number}>"

    @property
    def total_debit(self):
        """إجمالي الجانب المدين"""
        return sum(item.debit for item in self.items)

    @property
    def total_credit(self):
        """إجمالي الجانب الدائن"""
        return sum(item.credit for item in self.items)

    @property
    def is_balanced(self):
        """التحقق من توازن القيد"""
        return round(self.total_debit, 2) == round(self.total_credit, 2)

    def post(self, user_id=None):
        """ترحيل القيد المحاسبي"""
        if self.status == 'posted':
            return False, 'القيد مرحل بالفعل'

        # التحقق من توازن القيد
        if not self.is_balanced:
            return False, 'القيد غير متوازن'

        # التحقق من وجود عناصر في القيد
        if not self.items:
            return False, 'لا يوجد عناصر في القيد'

        # التحقق من الفترة المالية
        if self.fiscal_period and self.fiscal_period.status == 'closed':
            return False, 'الفترة المالية مغلقة'

        # تحديث حالة القيد
        self.status = 'posted'
        self.approved_by = user_id
        self.approved_at = datetime.utcnow()

        return True, 'تم ترحيل القيد بنجاح'

    def cancel(self, user_id=None, reason=None):
        """إلغاء القيد المحاسبي"""
        if self.status == 'cancelled':
            return False, 'القيد ملغي بالفعل'

        # تحديث حالة القيد
        self.status = 'cancelled'
        if reason:
            self.notes = f"{self.notes or ''}\nسبب الإلغاء: {reason}"

        return True, 'تم إلغاء القيد بنجاح'

    def create_reversal(self, user_id=None, reason=None):
        """إنشاء قيد عكسي"""
        if self.status != 'posted':
            return None, 'لا يمكن إنشاء قيد عكسي لقيد غير مرحل'

        # إنشاء قيد جديد بنفس البيانات
        reversal = JournalEntry(
            reference_number=f"REV-{self.reference_number}",
            date=datetime.now().date(),
            description=f"عكس القيد {self.reference_number}",
            entry_type='reversal',
            source_document_type=self.source_document_type,
            source_document_id=self.source_document_id,
            fiscal_period_id=self.fiscal_period_id,
            currency_id=self.currency_id,
            exchange_rate=self.exchange_rate,
            notes=f"قيد عكسي للقيد {self.reference_number}. {reason or ''}",
            user_id=user_id
        )

        # إنشاء عناصر القيد العكسي
        for item in self.items:
            reversal_item = JournalEntryItem(
                account_id=item.account_id,
                description=f"عكس {item.description or ''}",
                debit=item.credit,  # عكس المدين والدائن
                credit=item.debit
            )
            reversal.items.append(reversal_item)

        return reversal, 'تم إنشاء القيد العكسي بنجاح'


class JournalEntryItem(db.Model):
    """نموذج تفاصيل القيود المحاسبية (Journal Entry Items) وفقاً للمعايير المحاسبية الدولية"""
    id = db.Column(db.Integer, primary_key=True)
    journal_entry_id = db.Column(db.Integer, db.ForeignKey('journal_entry.id', ondelete='CASCADE'), nullable=False, index=True)
    account_id = db.Column(db.Integer, db.ForeignKey('account.id'), nullable=False, index=True)
    description = db.Column(db.Text)
    debit = db.Column(db.Float, nullable=False, default=0)
    credit = db.Column(db.Float, nullable=False, default=0)
    currency_id = db.Column(db.Integer, db.ForeignKey('currency.id'))  # العملة الخاصة بالبند
    original_debit = db.Column(db.Float, default=0)  # المبلغ المدين بالعملة الأصلية
    original_credit = db.Column(db.Float, default=0)  # المبلغ الدائن بالعملة الأصلية
    exchange_rate = db.Column(db.Float, default=1.0)  # سعر الصرف
    tax_amount = db.Column(db.Float, default=0)  # مبلغ الضريبة
    tax_type_id = db.Column(db.Integer, db.ForeignKey('tax_type.id'))  # نوع الضريبة
    reference = db.Column(db.String(128))  # مرجع إضافي للبند
    cost_center_id = db.Column(db.Integer)  # مركز التكلفة
    project_id = db.Column(db.Integer)  # المشروع
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    # العلاقات
    currency = relationship('Currency')
    tax_type = relationship('TaxType')

    def __repr__(self):
        return f"<JournalEntryItem {self.id}: {self.debit} DR, {self.credit} CR>"

    @property
    def net_amount(self):
        """صافي المبلغ (المدين - الدائن)"""
        return self.debit - self.credit

    @property
    def amount_with_tax(self):
        """المبلغ مع الضريبة"""
        return self.net_amount + self.tax_amount


class FiscalYear(db.Model):
    """نموذج السنة المالية (Fiscal Year) وفقاً للمعايير المحاسبية الدولية"""
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(128), nullable=False)
    code = db.Column(db.String(20), unique=True)
    start_date = db.Column(db.Date, nullable=False)
    end_date = db.Column(db.Date, nullable=False)
    status = db.Column(db.String(20), nullable=False, default='open', index=True)  # open, closed, locked
    is_current = db.Column(db.Boolean, default=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    def __repr__(self):
        return f"<FiscalYear {self.name}>"

    def close(self):
        """إغلاق السنة المالية"""
        if self.status == 'closed':
            return False, 'السنة المالية مغلقة بالفعل'

        # التحقق من إغلاق جميع الفترات المالية
        open_periods = FiscalPeriod.query.filter(
            FiscalPeriod.fiscal_year_id == self.id,
            FiscalPeriod.status == 'open'
        ).count()

        if open_periods > 0:
            return False, f'لا يمكن إغلاق السنة المالية. يوجد {open_periods} فترة مالية مفتوحة'

        # تحديث حالة السنة المالية
        self.status = 'closed'

        return True, 'تم إغلاق السنة المالية بنجاح'

    def create_periods(self, period_type='month'):
        """إنشاء الفترات المالية للسنة المالية"""
        if period_type == 'month':
            # إنشاء 12 فترة شهرية
            from dateutil.relativedelta import relativedelta

            start_date = self.start_date
            for i in range(12):
                period_start = start_date + relativedelta(months=i)
                period_end = period_start + relativedelta(months=1, days=-1)

                # التأكد من أن نهاية الفترة لا تتجاوز نهاية السنة المالية
                if period_end > self.end_date:
                    period_end = self.end_date

                # إنشاء الفترة المالية
                period = FiscalPeriod(
                    name=f"الشهر {i+1} - {self.name}",
                    code=f"{self.code}-{i+1:02d}",
                    start_date=period_start,
                    end_date=period_end,
                    fiscal_year_id=self.id,
                    period_type='month',
                    period_number=i+1
                )
                db.session.add(period)

                # إذا وصلنا إلى نهاية السنة المالية، نتوقف
                if period_end == self.end_date:
                    break

            return True, 'تم إنشاء الفترات المالية بنجاح'

        elif period_type == 'quarter':
            # إنشاء 4 فترات ربع سنوية
            from dateutil.relativedelta import relativedelta

            start_date = self.start_date
            for i in range(4):
                period_start = start_date + relativedelta(months=i*3)
                period_end = period_start + relativedelta(months=3, days=-1)

                # التأكد من أن نهاية الفترة لا تتجاوز نهاية السنة المالية
                if period_end > self.end_date:
                    period_end = self.end_date

                # إنشاء الفترة المالية
                period = FiscalPeriod(
                    name=f"الربع {i+1} - {self.name}",
                    code=f"{self.code}-Q{i+1}",
                    start_date=period_start,
                    end_date=period_end,
                    fiscal_year_id=self.id,
                    period_type='quarter',
                    period_number=i+1
                )
                db.session.add(period)

                # إذا وصلنا إلى نهاية السنة المالية، نتوقف
                if period_end == self.end_date:
                    break

            return True, 'تم إنشاء الفترات المالية بنجاح'

        else:
            return False, 'نوع الفترة غير صحيح'


class FiscalPeriod(db.Model):
    """نموذج الفترات المالية (Fiscal Periods) وفقاً للمعايير المحاسبية الدولية"""
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(128), nullable=False)
    code = db.Column(db.String(20), unique=True)
    start_date = db.Column(db.Date, nullable=False)
    end_date = db.Column(db.Date, nullable=False)
    status = db.Column(db.String(20), nullable=False, default='open', index=True)  # open, closed, locked
    fiscal_year_id = db.Column(db.Integer, db.ForeignKey('fiscal_year.id'), index=True)
    period_type = db.Column(db.String(20), default='month')  # month, quarter, year
    period_number = db.Column(db.Integer)  # رقم الفترة في السنة المالية
    closed_by = db.Column(db.Integer, db.ForeignKey('user.id'))
    closed_at = db.Column(db.DateTime)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # العلاقات
    fiscal_year = relationship('FiscalYear', backref='periods')
    user = relationship('User', foreign_keys=[closed_by])

    def __repr__(self):
        return f"<FiscalPeriod {self.name}>"

    def close(self, user_id=None):
        """إغلاق الفترة المالية"""
        if self.status == 'closed':
            return False, 'الفترة المالية مغلقة بالفعل'

        # التحقق من عدم وجود قيود محاسبية في حالة مسودة
        draft_entries = JournalEntry.query.filter(
            JournalEntry.fiscal_period_id == self.id,
            JournalEntry.status == 'draft'
        ).count()

        if draft_entries > 0:
            return False, f'لا يمكن إغلاق الفترة المالية. يوجد {draft_entries} قيد محاسبي في حالة مسودة'

        # تحديث حالة الفترة المالية
        self.status = 'closed'
        self.closed_by = user_id
        self.closed_at = datetime.utcnow()

        return True, 'تم إغلاق الفترة المالية بنجاح'

    def reopen(self, user_id=None):
        """إعادة فتح الفترة المالية"""
        if self.status == 'open':
            return False, 'الفترة المالية مفتوحة بالفعل'

        if self.status == 'locked':
            return False, 'الفترة المالية مقفلة ولا يمكن إعادة فتحها'

        # تحديث حالة الفترة المالية
        self.status = 'open'

        return True, 'تم إعادة فتح الفترة المالية بنجاح'

    def lock(self, user_id=None):
        """قفل الفترة المالية بشكل نهائي"""
        if self.status == 'locked':
            return False, 'الفترة المالية مقفلة بالفعل'

        # تحديث حالة الفترة المالية
        self.status = 'locked'

        return True, 'تم قفل الفترة المالية بنجاح'


class Currency(db.Model):
    """نموذج العملات (Currencies) وفقاً للمعايير المحاسبية الدولية"""
    id = db.Column(db.Integer, primary_key=True)
    code = db.Column(db.String(3), nullable=False, unique=True)
    name = db.Column(db.String(64), nullable=False)
    symbol = db.Column(db.String(10))
    exchange_rate = db.Column(db.Float, nullable=False, default=1)
    decimal_places = db.Column(db.Integer, default=2)  # عدد المنازل العشرية
    symbol_position = db.Column(db.String(10), default='after')  # before, after
    is_default = db.Column(db.Boolean, default=False, index=True)
    is_active = db.Column(db.Boolean, default=True)
    last_update_date = db.Column(db.Date)  # تاريخ آخر تحديث لسعر الصرف
    notes = db.Column(db.Text)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    def __repr__(self):
        return f"<Currency {self.code}>"

    @classmethod
    def get_default_currency(cls):
        """الحصول على العملة الافتراضية"""
        return cls.query.filter_by(is_default=True).first()

    @classmethod
    def set_default_currency(cls, currency_id):
        """تعيين العملة الافتراضية"""
        # إلغاء تعيين العملة الافتراضية الحالية
        current_default = cls.get_default_currency()
        if current_default:
            current_default.is_default = False
            db.session.add(current_default)

        # تعيين العملة الجديدة كافتراضية
        new_default = cls.query.get(currency_id)
        if new_default:
            new_default.is_default = True
            db.session.add(new_default)
            db.session.commit()
            return True
        return False

    def format_amount(self, amount):
        """تنسيق المبلغ بالعملة"""
        formatted = f"{amount:.{self.decimal_places}f}"
        if self.symbol_position == 'before':
            return f"{self.symbol}{formatted}"
        else:
            return f"{formatted} {self.symbol}"


class AccountingPaymentMethod(db.Model):
    """نموذج طرق الدفع المحاسبية (Accounting Payment Methods) وفقاً للمعايير المحاسبية الدولية"""
    __tablename__ = 'accounting_payment_method'

    id = db.Column(db.Integer, primary_key=True)
    code = db.Column(db.String(20), nullable=False, unique=True)
    name = db.Column(db.String(64), nullable=False)
    description = db.Column(db.Text)
    is_active = db.Column(db.Boolean, default=True)
    is_default = db.Column(db.Boolean, default=False)
    requires_reference = db.Column(db.Boolean, default=False)
    affects_cash_register = db.Column(db.Boolean, default=True)
    is_credit = db.Column(db.Boolean, default=False)
    allow_partial_payment = db.Column(db.Boolean, default=True)
    requires_approval = db.Column(db.Boolean, default=False)
    payment_category = db.Column(db.String(20), default='cash')  # cash, electronic, credit, other
    debit_account_id = db.Column(db.Integer, db.ForeignKey('account.id'))
    credit_account_id = db.Column(db.Integer, db.ForeignKey('account.id'))
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # العلاقات
    debit_account = relationship('Account', foreign_keys=[debit_account_id])
    credit_account = relationship('Account', foreign_keys=[credit_account_id])

    def __repr__(self):
        return f"<AccountingPaymentMethod {self.code}: {self.name}>"

    @classmethod
    def get_default_method(cls):
        """الحصول على طريقة الدفع الافتراضية"""
        return cls.query.filter_by(is_default=True).first()

    @classmethod
    def set_default_method(cls, method_id):
        """تعيين طريقة الدفع الافتراضية"""
        # إلغاء تعيين طريقة الدفع الافتراضية الحالية
        current_default = cls.get_default_method()
        if current_default:
            current_default.is_default = False
            db.session.add(current_default)

        # تعيين طريقة الدفع الجديدة كافتراضية
        new_default = cls.query.get(method_id)
        if new_default:
            new_default.is_default = True
            db.session.add(new_default)
            db.session.commit()
            return True
        return False


class AccountingSettings(db.Model):
    """نموذج إعدادات المحاسبة (Accounting Settings) وفقاً للمعايير المحاسبية الدولية"""
    __tablename__ = 'accounting_settings'
    id = db.Column(db.Integer, primary_key=True)
    company_name = db.Column(db.String(128), nullable=False)
    company_tax_id = db.Column(db.String(50))
    fiscal_year_start_month = db.Column(db.Integer, default=1)  # 1 = January
    fiscal_year_start_day = db.Column(db.Integer, default=1)
    default_currency_id = db.Column(db.Integer, db.ForeignKey('currency.id'))
    base_currency_id = db.Column(db.Integer, db.ForeignKey('currency.id'))
    default_tax_id = db.Column(db.Integer, db.ForeignKey('tax_type.id'))
    enable_multi_currency = db.Column(db.Boolean, default=False)
    enable_cost_centers = db.Column(db.Boolean, default=False)
    enable_projects = db.Column(db.Boolean, default=False)
    enable_budgets = db.Column(db.Boolean, default=False)
    enable_assets = db.Column(db.Boolean, default=False)
    lock_date = db.Column(db.Date)  # تاريخ قفل الفترات المالية
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # العلاقات
    default_currency = relationship('Currency', foreign_keys=[default_currency_id])
    base_currency = relationship('Currency', foreign_keys=[base_currency_id])
    default_tax = relationship('TaxType')

    def __repr__(self):
        return f"<AccountingSettings {self.company_name}>"

    @classmethod
    def get_settings(cls):
        """الحصول على إعدادات المحاسبة"""
        settings = cls.query.first()
        if not settings:
            # إنشاء إعدادات افتراضية
            settings = cls(
                company_name="شركتي",
                fiscal_year_start_month=1,
                fiscal_year_start_day=1
            )
            db.session.add(settings)
            db.session.commit()
        return settings


class TaxType(db.Model):
    """نموذج أنواع الضرائب (Tax Types) وفقاً للمعايير المحاسبية الدولية"""
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(64), nullable=False)
    code = db.Column(db.String(20), nullable=False, unique=True)
    rate = db.Column(db.Float, nullable=False)
    description = db.Column(db.Text)
    tax_category = db.Column(db.String(30), index=True)  # sales_tax, vat, income_tax, etc.
    is_compound = db.Column(db.Boolean, default=False)  # هل هي ضريبة مركبة
    is_recoverable = db.Column(db.Boolean, default=True)  # هل يمكن استردادها
    account_id = db.Column(db.Integer, db.ForeignKey('account.id'))  # حساب الضريبة
    is_default = db.Column(db.Boolean, default=False)  # هل هي الضريبة الافتراضية
    applies_to_sales = db.Column(db.Boolean, default=True)  # هل تطبق على المبيعات
    applies_to_purchases = db.Column(db.Boolean, default=True)  # هل تطبق على المشتريات
    is_active = db.Column(db.Boolean, default=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # العلاقات
    account = relationship('Account', backref='tax_types')

    def __repr__(self):
        return f"<TaxType {self.code}: {self.rate}%>"

    def calculate_tax(self, amount, is_tax_inclusive=False):
        """حساب الضريبة على المبلغ"""
        if is_tax_inclusive:
            # المبلغ شامل الضريبة
            tax_amount = amount - (amount / (1 + (self.rate / 100)))
        else:
            # المبلغ غير شامل الضريبة
            tax_amount = amount * (self.rate / 100)

        return round(tax_amount, 2)

    def get_tax_inclusive_amount(self, amount):
        """الحصول على المبلغ شامل الضريبة"""
        return amount + self.calculate_tax(amount, False)

    def get_tax_exclusive_amount(self, amount):
        """الحصول على المبلغ بدون الضريبة"""
        return amount - self.calculate_tax(amount, True)

    @classmethod
    def get_default_tax(cls, for_sales=True):
        """الحصول على الضريبة الافتراضية"""
        if for_sales:
            return cls.query.filter_by(is_default=True, applies_to_sales=True, is_active=True).first()
        else:
            return cls.query.filter_by(is_default=True, applies_to_purchases=True, is_active=True).first()


class PaymentMethodAccount(db.Model):
    """نموذج ربط طرق الدفع بالحسابات المحاسبية"""
    __tablename__ = 'payment_method_account'

    id = db.Column(db.Integer, primary_key=True)
    payment_method_id = db.Column(db.Integer, db.ForeignKey('payment_method.id'), nullable=False)
    account_id = db.Column(db.Integer, db.ForeignKey('account.id'), nullable=False)
    for_sales = db.Column(db.Boolean, default=True)  # هل هي لعمليات البيع
    for_purchases = db.Column(db.Boolean, default=True)  # هل هي لعمليات الشراء
    is_default = db.Column(db.Boolean, default=False)  # هل هي الطريقة الافتراضية
    description = db.Column(db.Text)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # العلاقات
    payment_method = relationship('PaymentMethod', backref='accounting_settings')
    account = relationship('Account', backref='payment_methods')

    def __repr__(self):
        return f"<PaymentMethodAccount {self.id}>"

    @classmethod
    def get_account_for_payment_method(cls, payment_method_id, for_sales=True):
        """الحصول على الحساب المرتبط بطريقة الدفع"""
        payment_method_account = cls.query.filter_by(
            payment_method_id=payment_method_id,
            for_sales=for_sales if for_sales else True,
            for_purchases=not for_sales if not for_sales else True
        ).first()

        if payment_method_account:
            return payment_method_account.account
        return None

    @classmethod
    def get_default_payment_method(cls, for_sales=True):
        """الحصول على طريقة الدفع الافتراضية"""
        if for_sales:
            return cls.query.filter_by(is_default=True, for_sales=True).first()
        else:
            return cls.query.filter_by(is_default=True, for_purchases=True).first()


class AccountingConfiguration(db.Model):
    """نموذج الإعدادات المحاسبية (Accounting Configuration) وفقاً للمعايير المحاسبية الدولية"""
    __tablename__ = 'accounting_configuration'
    id = db.Column(db.Integer, primary_key=True)
    # الحسابات الافتراضية
    default_sales_account_id = db.Column(db.Integer, db.ForeignKey('account.id'))
    default_purchases_account_id = db.Column(db.Integer, db.ForeignKey('account.id'))
    default_inventory_account_id = db.Column(db.Integer, db.ForeignKey('account.id'))
    default_expenses_account_id = db.Column(db.Integer, db.ForeignKey('account.id'))
    default_tax_account_id = db.Column(db.Integer, db.ForeignKey('account.id'))
    default_cash_account_id = db.Column(db.Integer, db.ForeignKey('account.id'))
    default_bank_account_id = db.Column(db.Integer, db.ForeignKey('account.id'))
    default_accounts_receivable_account_id = db.Column(db.Integer, db.ForeignKey('account.id'))
    default_accounts_payable_account_id = db.Column(db.Integer, db.ForeignKey('account.id'))
    default_cost_of_goods_sold_account_id = db.Column(db.Integer, db.ForeignKey('account.id'))
    default_sales_discount_account_id = db.Column(db.Integer, db.ForeignKey('account.id'))
    default_purchase_discount_account_id = db.Column(db.Integer, db.ForeignKey('account.id'))
    default_sales_return_account_id = db.Column(db.Integer, db.ForeignKey('account.id'))
    default_purchase_return_account_id = db.Column(db.Integer, db.ForeignKey('account.id'))
    default_retained_earnings_account_id = db.Column(db.Integer, db.ForeignKey('account.id'))

    # إعدادات عامة
    fiscal_year_start_month = db.Column(db.Integer, default=1)  # شهر بداية السنة المالية
    fiscal_year_start_day = db.Column(db.Integer, default=1)  # يوم بداية السنة المالية
    default_currency_id = db.Column(db.Integer, db.ForeignKey('currency.id'))
    auto_post_journal_entries = db.Column(db.Boolean, default=False)  # ترحيل القيود المحاسبية تلقائياً
    require_journal_approval = db.Column(db.Boolean, default=True)  # طلب موافقة على القيود المحاسبية
    enable_multi_currency = db.Column(db.Boolean, default=False)  # تفعيل تعدد العملات
    enable_cost_centers = db.Column(db.Boolean, default=False)  # تفعيل مراكز التكلفة
    enable_projects = db.Column(db.Boolean, default=False)  # تفعيل المشاريع
    enable_budgets = db.Column(db.Boolean, default=False)  # تفعيل الميزانيات

    # إعدادات الضرائب
    default_tax_type_id = db.Column(db.Integer, db.ForeignKey('tax_type.id'))
    tax_calculation_method = db.Column(db.String(20), default='exclusive')  # exclusive, inclusive

    # إعدادات التقارير
    report_basis = db.Column(db.String(20), default='accrual')  # accrual, cash

    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # العلاقات
    default_sales_account = relationship('Account', foreign_keys=[default_sales_account_id])
    default_purchases_account = relationship('Account', foreign_keys=[default_purchases_account_id])
    default_inventory_account = relationship('Account', foreign_keys=[default_inventory_account_id])
    default_expenses_account = relationship('Account', foreign_keys=[default_expenses_account_id])
    default_tax_account = relationship('Account', foreign_keys=[default_tax_account_id])
    default_cash_account = relationship('Account', foreign_keys=[default_cash_account_id])
    default_bank_account = relationship('Account', foreign_keys=[default_bank_account_id])
    default_accounts_receivable_account = relationship('Account', foreign_keys=[default_accounts_receivable_account_id])
    default_accounts_payable_account = relationship('Account', foreign_keys=[default_accounts_payable_account_id])
    default_cost_of_goods_sold_account = relationship('Account', foreign_keys=[default_cost_of_goods_sold_account_id])
    default_sales_discount_account = relationship('Account', foreign_keys=[default_sales_discount_account_id])
    default_purchase_discount_account = relationship('Account', foreign_keys=[default_purchase_discount_account_id])
    default_sales_return_account = relationship('Account', foreign_keys=[default_sales_return_account_id])
    default_purchase_return_account = relationship('Account', foreign_keys=[default_purchase_return_account_id])
    default_retained_earnings_account = relationship('Account', foreign_keys=[default_retained_earnings_account_id])
    default_currency = relationship('Currency', foreign_keys=[default_currency_id])
    default_tax_type = relationship('TaxType', foreign_keys=[default_tax_type_id])

    def __repr__(self):
        return f"<AccountingSettings {self.id}>"

    @classmethod
    def get_settings(cls):
        """الحصول على إعدادات المحاسبة"""
        settings = cls.query.first()
        if not settings:
            settings = cls()
            db.session.add(settings)
            db.session.commit()
        return settings


class CostCenter(db.Model):
    """نموذج مراكز التكلفة (Cost Centers) وفقاً للمعايير المحاسبية الدولية"""
    __tablename__ = 'cost_center'
    __table_args__ = {'extend_existing': True}
    id = db.Column(db.Integer, primary_key=True)
    code = db.Column(db.String(20), nullable=False, unique=True)
    name = db.Column(db.String(128), nullable=False)
    description = db.Column(db.Text)
    parent_id = db.Column(db.Integer, db.ForeignKey('cost_center.id'), index=True)
    is_active = db.Column(db.Boolean, default=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # العلاقات
    parent = relationship('CostCenter', remote_side=[id], backref='children')

    def __repr__(self):
        return f"<CostCenter {self.code}: {self.name}>"


class Project(db.Model):
    """نموذج المشاريع (Projects) وفقاً للمعايير المحاسبية الدولية"""
    __tablename__ = 'project'
    __table_args__ = {'extend_existing': True}
    id = db.Column(db.Integer, primary_key=True)
    code = db.Column(db.String(20), nullable=False, unique=True)
    name = db.Column(db.String(128), nullable=False)
    description = db.Column(db.Text)
    start_date = db.Column(db.Date)
    end_date = db.Column(db.Date)
    status = db.Column(db.String(20), default='active', index=True)  # active, completed, cancelled
    budget = db.Column(db.Float, default=0)
    cost_center_id = db.Column(db.Integer, db.ForeignKey('cost_center.id'))
    manager_id = db.Column(db.Integer, db.ForeignKey('user.id'))
    is_active = db.Column(db.Boolean, default=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # العلاقات
    cost_center = relationship('CostCenter', backref='projects')
    manager = relationship('User', backref='managed_projects')

    def __repr__(self):
        return f"<Project {self.code}: {self.name}>"


class Budget(db.Model):
    """نموذج الميزانيات (Budgets) وفقاً للمعايير المحاسبية الدولية"""
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(128), nullable=False)
    description = db.Column(db.Text)
    fiscal_year_id = db.Column(db.Integer, db.ForeignKey('fiscal_year.id'), index=True)
    start_date = db.Column(db.Date, nullable=False)
    end_date = db.Column(db.Date, nullable=False)
    status = db.Column(db.String(20), default='draft', index=True)  # draft, approved, closed
    approved_by = db.Column(db.Integer, db.ForeignKey('user.id'))
    approved_at = db.Column(db.DateTime)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # العلاقات
    fiscal_year = relationship('FiscalYear', backref='budgets')
    approver = relationship('User', backref='approved_budgets')
    items = relationship('BudgetItem', backref='budget', cascade='all, delete-orphan')

    def __repr__(self):
        return f"<Budget {self.name}>"


class BudgetItem(db.Model):
    """نموذج بنود الميزانية (Budget Items) وفقاً للمعايير المحاسبية الدولية"""
    id = db.Column(db.Integer, primary_key=True)
    budget_id = db.Column(db.Integer, db.ForeignKey('budget.id', ondelete='CASCADE'), nullable=False, index=True)
    account_id = db.Column(db.Integer, db.ForeignKey('account.id'), nullable=False, index=True)
    cost_center_id = db.Column(db.Integer, db.ForeignKey('cost_center.id'))
    project_id = db.Column(db.Integer, db.ForeignKey('project.id'))
    amount = db.Column(db.Float, nullable=False, default=0)
    notes = db.Column(db.Text)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    # العلاقات
    account = relationship('Account', backref='budget_items')
    cost_center = relationship('CostCenter', backref='budget_items')
    project = relationship('Project', backref='budget_items')

    def __repr__(self):
        return f"<BudgetItem {self.id}: {self.amount}>"


class AssetCategory(db.Model):
    """نموذج فئات الأصول (Asset Categories) وفقاً للمعايير المحاسبية الدولية"""
    id = db.Column(db.Integer, primary_key=True)
    code = db.Column(db.String(20), nullable=False, unique=True)
    name = db.Column(db.String(128), nullable=False)
    description = db.Column(db.Text)
    parent_id = db.Column(db.Integer, db.ForeignKey('asset_category.id'), index=True)
    depreciation_method = db.Column(db.String(20), default='straight_line')  # straight_line, declining_balance, units_of_production
    useful_life_years = db.Column(db.Integer)
    depreciation_rate = db.Column(db.Float)
    asset_account_id = db.Column(db.Integer, db.ForeignKey('account.id'))
    depreciation_account_id = db.Column(db.Integer, db.ForeignKey('account.id'))
    accumulated_depreciation_account_id = db.Column(db.Integer, db.ForeignKey('account.id'))
    is_active = db.Column(db.Boolean, default=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # العلاقات
    parent = relationship('AssetCategory', remote_side=[id], backref='children')
    asset_account = relationship('Account', foreign_keys=[asset_account_id])
    depreciation_account = relationship('Account', foreign_keys=[depreciation_account_id])
    accumulated_depreciation_account = relationship('Account', foreign_keys=[accumulated_depreciation_account_id])

    def __repr__(self):
        return f"<AssetCategory {self.code}: {self.name}>"


class Asset(db.Model):
    """نموذج الأصول (Assets) وفقاً للمعايير المحاسبية الدولية"""
    id = db.Column(db.Integer, primary_key=True)
    code = db.Column(db.String(20), nullable=False, unique=True)
    name = db.Column(db.String(128), nullable=False)
    description = db.Column(db.Text)
    category_id = db.Column(db.Integer, db.ForeignKey('asset_category.id'), index=True)
    purchase_date = db.Column(db.Date, nullable=False)
    purchase_cost = db.Column(db.Float, nullable=False)
    salvage_value = db.Column(db.Float, default=0)
    useful_life_years = db.Column(db.Integer)
    depreciation_method = db.Column(db.String(20))  # straight_line, declining_balance, units_of_production
    depreciation_rate = db.Column(db.Float)
    current_value = db.Column(db.Float)
    status = db.Column(db.String(20), default='active', index=True)  # active, disposed, written_off
    location = db.Column(db.String(128))
    cost_center_id = db.Column(db.Integer, db.ForeignKey('cost_center.id'))
    project_id = db.Column(db.Integer, db.ForeignKey('project.id'))
    last_depreciation_date = db.Column(db.Date)
    disposal_date = db.Column(db.Date)
    disposal_value = db.Column(db.Float)
    notes = db.Column(db.Text)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # العلاقات
    category = relationship('AssetCategory', backref='assets')
    cost_center = relationship('CostCenter', backref='assets')
    project = relationship('Project', backref='assets')
    depreciation_entries = relationship('AssetDepreciation', backref='asset', cascade='all, delete-orphan')

    def __repr__(self):
        return f"<Asset {self.code}: {self.name}>"

    def calculate_depreciation(self, date=None):
        """حساب قسط الإهلاك"""
        if not date:
            date = datetime.now().date()

        # التحقق من أن الأصل نشط
        if self.status != 'active':
            return 0

        # التحقق من تاريخ آخر إهلاك
        if self.last_depreciation_date and self.last_depreciation_date >= date:
            return 0

        # حساب القيمة القابلة للإهلاك
        depreciable_value = self.purchase_cost - self.salvage_value

        # حساب الإهلاك بناءً على الطريقة
        if self.depreciation_method == 'straight_line':
            # طريقة القسط الثابت
            annual_depreciation = depreciable_value / self.useful_life_years
            monthly_depreciation = annual_depreciation / 12

            # حساب عدد الأشهر منذ آخر إهلاك
            if self.last_depreciation_date:
                from dateutil.relativedelta import relativedelta
                months = (date.year - self.last_depreciation_date.year) * 12 + date.month - self.last_depreciation_date.month
            else:
                # إذا لم يكن هناك إهلاك سابق، نحسب من تاريخ الشراء
                from dateutil.relativedelta import relativedelta
                months = (date.year - self.purchase_date.year) * 12 + date.month - self.purchase_date.month

            return round(monthly_depreciation * months, 2)

        elif self.depreciation_method == 'declining_balance':
            # طريقة القسط المتناقص
            if not self.depreciation_rate:
                return 0

            # حساب القيمة الحالية
            current_value = self.current_value or self.purchase_cost

            # حساب الإهلاك السنوي
            annual_depreciation = current_value * self.depreciation_rate
            monthly_depreciation = annual_depreciation / 12

            # حساب عدد الأشهر منذ آخر إهلاك
            if self.last_depreciation_date:
                from dateutil.relativedelta import relativedelta
                months = (date.year - self.last_depreciation_date.year) * 12 + date.month - self.last_depreciation_date.month
            else:
                # إذا لم يكن هناك إهلاك سابق، نحسب من تاريخ الشراء
                from dateutil.relativedelta import relativedelta
                months = (date.year - self.purchase_date.year) * 12 + date.month - self.purchase_date.month

            return round(monthly_depreciation * months, 2)

        return 0


class AssetDepreciation(db.Model):
    """نموذج إهلاك الأصول (Asset Depreciation) وفقاً للمعايير المحاسبية الدولية"""
    id = db.Column(db.Integer, primary_key=True)
    asset_id = db.Column(db.Integer, db.ForeignKey('asset.id', ondelete='CASCADE'), nullable=False, index=True)
    date = db.Column(db.Date, nullable=False)
    amount = db.Column(db.Float, nullable=False)
    journal_entry_id = db.Column(db.Integer, db.ForeignKey('journal_entry.id'))
    notes = db.Column(db.Text)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    # العلاقات
    journal_entry = relationship('JournalEntry', backref='asset_depreciations')

    def __repr__(self):
        return f"<AssetDepreciation {self.id}: {self.amount}>"


class FinancialRatio(db.Model):
    """نموذج النسب المالية (Financial Ratios) وفقاً للمعايير المحاسبية الدولية"""
    id = db.Column(db.Integer, primary_key=True)
    code = db.Column(db.String(20), nullable=False, unique=True)
    name = db.Column(db.String(128), nullable=False)
    description = db.Column(db.Text)
    category = db.Column(db.String(30))  # liquidity, profitability, solvency, efficiency
    formula = db.Column(db.Text, nullable=False)
    target_value = db.Column(db.Float)
    is_active = db.Column(db.Boolean, default=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    def __repr__(self):
        return f"<FinancialRatio {self.code}: {self.name}>"


class FinancialStatement(db.Model):
    """نموذج القوائم المالية (Financial Statements) وفقاً للمعايير المحاسبية الدولية"""
    id = db.Column(db.Integer, primary_key=True)
    statement_type = db.Column(db.String(30), nullable=False, index=True)  # balance_sheet, income_statement, cash_flow, changes_in_equity
    fiscal_period_id = db.Column(db.Integer, db.ForeignKey('fiscal_period.id'), index=True)
    date = db.Column(db.Date, nullable=False)
    status = db.Column(db.String(20), default='draft', index=True)  # draft, published, archived
    notes = db.Column(db.Text)
    created_by = db.Column(db.Integer, db.ForeignKey('user.id'))
    approved_by = db.Column(db.Integer, db.ForeignKey('user.id'))
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # العلاقات
    fiscal_period = relationship('FiscalPeriod', backref='financial_statements')
    creator = relationship('User', foreign_keys=[created_by], backref='created_financial_statements')
    approver = relationship('User', foreign_keys=[approved_by], backref='approved_financial_statements')
    items = relationship('FinancialStatementItem', backref='statement', cascade='all, delete-orphan')

    def __repr__(self):
        return f"<FinancialStatement {self.id}: {self.statement_type}>"


class FinancialStatementItem(db.Model):
    """نموذج بنود القوائم المالية (Financial Statement Items) وفقاً للمعايير المحاسبية الدولية"""
    id = db.Column(db.Integer, primary_key=True)
    statement_id = db.Column(db.Integer, db.ForeignKey('financial_statement.id', ondelete='CASCADE'), nullable=False, index=True)
    account_id = db.Column(db.Integer, db.ForeignKey('account.id'))
    line_number = db.Column(db.Integer)
    section = db.Column(db.String(50))  # assets, liabilities, equity, revenue, expenses, etc.
    subsection = db.Column(db.String(50))  # current_assets, fixed_assets, etc.
    description = db.Column(db.String(256))
    amount = db.Column(db.Float, nullable=False, default=0)
    comparative_amount = db.Column(db.Float, default=0)  # المبلغ المقارن (الفترة السابقة)
    formula = db.Column(db.Text)  # صيغة حسابية إذا كان البند محسوب
    is_total = db.Column(db.Boolean, default=False)  # هل هو إجمالي
    is_subtotal = db.Column(db.Boolean, default=False)  # هل هو إجمالي فرعي
    parent_id = db.Column(db.Integer, db.ForeignKey('financial_statement_item.id'))
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    # العلاقات
    account = relationship('Account', backref='financial_statement_items')
    parent = relationship('FinancialStatementItem', remote_side=[id], backref='children')

    def __repr__(self):
        return f"<FinancialStatementItem {self.id}: {self.description}>"


class RecurringTransaction(db.Model):
    """نموذج المعاملات المتكررة (Recurring Transactions) وفقاً للمعايير المحاسبية الدولية"""
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(128), nullable=False)
    description = db.Column(db.Text)
    transaction_type = db.Column(db.String(30), nullable=False)  # journal_entry, payment, receipt, invoice, bill
    frequency = db.Column(db.String(20), nullable=False)  # daily, weekly, monthly, quarterly, yearly
    start_date = db.Column(db.Date, nullable=False)
    end_date = db.Column(db.Date)
    next_due_date = db.Column(db.Date)
    amount = db.Column(db.Float)
    template_data = db.Column(db.Text)  # JSON data for the transaction template
    is_active = db.Column(db.Boolean, default=True)
    created_by = db.Column(db.Integer, db.ForeignKey('user.id'))
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # العلاقات
    creator = relationship('User', backref='recurring_transactions')

    def __repr__(self):
        return f"<RecurringTransaction {self.id}: {self.name}>"


class AccountingTransaction(db.Model):
    """نموذج المعاملات المحاسبية (Accounting Transactions) وفقاً للمعايير المحاسبية الدولية"""
    id = db.Column(db.Integer, primary_key=True)
    transaction_type = db.Column(db.String(30), nullable=False, index=True)  # sale, purchase, payment, receipt, adjustment
    reference_number = db.Column(db.String(30), nullable=False, unique=True)
    date = db.Column(db.Date, nullable=False, index=True)
    description = db.Column(db.Text)
    amount = db.Column(db.Float, nullable=False)
    currency_id = db.Column(db.Integer, db.ForeignKey('currency.id'))
    exchange_rate = db.Column(db.Float, default=1.0)
    related_entity_type = db.Column(db.String(30))  # customer, supplier, employee, etc.
    related_entity_id = db.Column(db.Integer)
    journal_entry_id = db.Column(db.Integer, db.ForeignKey('journal_entry.id'))
    fiscal_period_id = db.Column(db.Integer, db.ForeignKey('fiscal_period.id'), index=True)
    status = db.Column(db.String(20), default='completed', index=True)  # completed, pending, cancelled
    created_by = db.Column(db.Integer, db.ForeignKey('user.id'))
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # العلاقات
    currency = relationship('Currency')
    journal_entry = relationship('JournalEntry', backref='transactions')
    fiscal_period = relationship('FiscalPeriod', backref='transactions')
    creator = relationship('User', backref='accounting_transactions')

    def __repr__(self):
        return f"<AccountingTransaction {self.reference_number}: {self.amount}>"

    def create_journal_entry(self, user_id=None):
        """إنشاء قيد محاسبي للمعاملة"""
        # التحقق من عدم وجود قيد محاسبي سابق
        if self.journal_entry_id:
            return None, 'يوجد قيد محاسبي مرتبط بالمعاملة بالفعل'

        # إنشاء قيد محاسبي جديد
        journal_entry = JournalEntry(
            reference_number=f"JE-{self.reference_number}",
            date=self.date,
            description=f"قيد محاسبي للمعاملة {self.reference_number}: {self.description or ''}",
            entry_type=self.transaction_type,
            source_document_type=self.transaction_type,
            source_document_id=self.id,
            fiscal_period_id=self.fiscal_period_id,
            currency_id=self.currency_id,
            exchange_rate=self.exchange_rate,
            user_id=user_id or self.created_by
        )

        # إضافة القيد المحاسبي إلى قاعدة البيانات
        db.session.add(journal_entry)
        db.session.flush()  # للحصول على معرف القيد المحاسبي

        # تحديث المعاملة بمعرف القيد المحاسبي
        self.journal_entry_id = journal_entry.id

        return journal_entry, 'تم إنشاء القيد المحاسبي بنجاح'


# تم نقل نموذج CostCenter إلى أعلى الملف


# تم نقل نموذج Project إلى أعلى الملف


# تم نقل نموذج Budget إلى أعلى الملف


# تم نقل نموذج BudgetItem إلى أعلى الملف