
{% extends "layout.html" %}

{% block page_content %}
<div class="container mx-auto px-4 py-8">
    <div class="mb-6">
        <div class="flex flex-col md:flex-row md:justify-between md:items-center">
            <div class="mb-4 md:mb-0">
                <h1 class="text-2xl font-bold">مخزون {{ warehouse.name }}</h1>
                <p class="text-gray-600">{{ warehouse.location }}</p>
            </div>
            <div class="flex flex-wrap gap-2">
                <a href="{{ url_for('warehouses.reports') }}?warehouse_id={{ warehouse.id }}" class="bg-indigo-500 text-white px-4 py-2 rounded-lg hover:bg-indigo-600 transition-all text-sm md:text-base">
                    <i class="ri-file-chart-line ml-1"></i><span class="hidden sm:inline">تقارير المخزون</span><span class="inline sm:hidden">التقارير</span>
                </a>
                <a href="{{ url_for('warehouses.movements') }}?warehouse_id={{ warehouse.id }}" class="bg-purple-500 text-white px-4 py-2 rounded-lg hover:bg-purple-600 transition-all text-sm md:text-base">
                    <i class="ri-history-line ml-1"></i><span class="hidden sm:inline">حركة المخزون</span><span class="inline sm:hidden">الحركة</span>
                </a>
                <a href="{{ url_for('warehouses.index') }}" class="bg-gray-100 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-200 transition-all text-sm md:text-base">
                    <i class="ri-arrow-right-line ml-1"></i>العودة
                </a>
            </div>
        </div>
    </div>

    <!-- إحصائيات المخزون -->
    {% if stats %}
    <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
        <div class="bg-white rounded-lg shadow p-4 border-r-4 border-blue-500">
            <div class="flex justify-between items-center">
                <div>
                    <p class="text-gray-500 text-sm">إجمالي المنتجات</p>
                    <p class="text-2xl font-bold">{{ stats.total_products }}</p>
                </div>
                <div class="text-blue-500 text-3xl">
                    <i class="ri-archive-line"></i>
                </div>
            </div>
        </div>
        <div class="bg-white rounded-lg shadow p-4 border-r-4 border-green-500">
            <div class="flex justify-between items-center">
                <div>
                    <p class="text-gray-500 text-sm">متوفر في المخزون</p>
                    <p class="text-2xl font-bold">{{ stats.in_stock }}</p>
                </div>
                <div class="text-green-500 text-3xl">
                    <i class="ri-checkbox-circle-line"></i>
                </div>
            </div>
        </div>
        <div class="bg-white rounded-lg shadow p-4 border-r-4 border-yellow-500">
            <div class="flex justify-between items-center">
                <div>
                    <p class="text-gray-500 text-sm">مخزون منخفض</p>
                    <p class="text-2xl font-bold">{{ stats.low_stock }}</p>
                </div>
                <div class="text-yellow-500 text-3xl">
                    <i class="ri-error-warning-line"></i>
                </div>
            </div>
        </div>
        <div class="bg-white rounded-lg shadow p-4 border-r-4 border-red-500">
            <div class="flex justify-between items-center">
                <div>
                    <p class="text-gray-500 text-sm">نفذ من المخزون</p>
                    <p class="text-2xl font-bold">{{ stats.out_of_stock }}</p>
                </div>
                <div class="text-red-500 text-3xl">
                    <i class="ri-close-circle-line"></i>
                </div>
            </div>
        </div>
    </div>
    {% endif %}

    <!-- حقل البحث والفلترة -->
    <div class="bg-white rounded-lg shadow p-4 mb-6">
        <form method="GET" action="{{ url_for('warehouses.inventory', id=warehouse.id) }}" class="flex flex-wrap items-center gap-4">
            <div class="relative flex-grow">
                <div class="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                    <i class="ri-search-line text-gray-400"></i>
                </div>
                <input type="text" name="search" value="{{ search }}" placeholder="البحث عن منتج بالاسم أو الباركود..."
                       class="w-full pr-10 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
            </div>
            <div>
                <select name="status" class="border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500">
                    <option value="all" {% if status == 'all' %}selected{% endif %}>جميع المنتجات</option>
                    <option value="in_stock" {% if status == 'in_stock' %}selected{% endif %}>متوفر في المخزون</option>
                    <option value="low_stock" {% if status == 'low_stock' %}selected{% endif %}>مخزون منخفض</option>
                    <option value="out_of_stock" {% if status == 'out_of_stock' %}selected{% endif %}>نفذ من المخزون</option>
                </select>
            </div>
            <button type="submit" class="bg-blue-500 text-white px-4 py-2 rounded-md hover:bg-blue-600 transition-all">
                بحث
            </button>
        </form>
    </div>

    <!-- نماذج إدارة المخزون -->
    <div x-data="{ activeTab: 'update' }" class="mb-6">
        <!-- أزرار التبديل بين النماذج للشاشات الصغيرة -->
        <div class="md:hidden flex rounded-lg bg-gray-100 p-1 mb-4">
            <button
                @click="activeTab = 'update'"
                :class="activeTab === 'update' ? 'bg-blue-500 text-white' : 'bg-transparent text-gray-700'"
                class="flex-1 py-2 px-4 rounded-lg transition-all text-center">
                إضافة/تحديث
            </button>
            <button
                @click="activeTab = 'transfer'"
                :class="activeTab === 'transfer' ? 'bg-indigo-500 text-white' : 'bg-transparent text-gray-700'"
                class="flex-1 py-2 px-4 rounded-lg transition-all text-center">
                نقل المخزون
            </button>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <!-- إضافة/تحديث المخزون -->
            <div class="bg-white rounded-lg shadow p-6" x-show="activeTab === 'update' || window.innerWidth >= 768">
                <h2 class="text-xl font-semibold mb-4">إضافة/تحديث المخزون</h2>
                <form action="{{ url_for('warehouses.update_inventory', id=warehouse.id) }}" method="POST" id="inventoryForm">
                    <div class="grid grid-cols-1 gap-4">
                        <div>
                            <label class="block text-gray-700 mb-2">المنتج</label>
                            <div class="relative">
                                <input type="text" id="product_search" placeholder="البحث عن منتج..."
                                       class="w-full border rounded px-3 py-2 mb-2 focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <div id="product_search_results" class="absolute z-10 bg-white border border-gray-300 rounded w-full max-h-60 overflow-y-auto hidden"></div>
                                <select name="product_id" id="product_id" class="w-full border rounded px-3 py-2">
                                    {% for product in products %}
                                    <option value="{{ product.id }}">{{ product.name }} {% if product.code %}({{ product.code }}){% endif %}</option>
                                    {% endfor %}
                                </select>
                            </div>
                        </div>
                        <div class="grid grid-cols-1 sm:grid-cols-2 gap-4">
                            <div>
                                <label class="block text-gray-700 mb-2">الكمية</label>
                                <input type="number" name="quantity" id="quantity" class="w-full border rounded px-3 py-2" min="0">
                            </div>
                            <div>
                                <label class="block text-gray-700 mb-2">الحد الأدنى</label>
                                <input type="number" name="minimum_stock" id="minimum_stock" class="w-full border rounded px-3 py-2" min="0" value="5">
                            </div>
                        </div>
                        <div>
                            <label class="block text-gray-700 mb-2">ملاحظات</label>
                            <textarea name="notes" id="notes" class="w-full border rounded px-3 py-2" rows="2" placeholder="أي ملاحظات إضافية حول تحديث المخزون"></textarea>
                        </div>
                    </div>
                    <div class="mt-4">
                        <button type="submit" class="w-full sm:w-auto bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600 transition-all">
                            حفظ
                        </button>
                    </div>
                </form>
            </div>

            <!-- نقل المخزون -->
            <div class="bg-white rounded-lg shadow p-6" x-show="activeTab === 'transfer' || window.innerWidth >= 768">
                <h2 class="text-xl font-semibold mb-4">نقل المخزون إلى مخزن آخر</h2>
                <form action="{{ url_for('warehouses.transfer_inventory', id=warehouse.id) }}" method="POST" id="transferForm">
                    <div class="grid grid-cols-1 gap-4">
                        <div>
                            <label class="block text-gray-700 mb-2">المنتج</label>
                            <div class="relative">
                                <input type="text" id="transfer_product_search" placeholder="البحث عن منتج..."
                                       class="w-full border rounded px-3 py-2 mb-2 focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <div id="transfer_product_results" class="absolute z-10 bg-white border border-gray-300 rounded w-full max-h-60 overflow-y-auto hidden"></div>
                                <select name="product_id" id="transfer_product_id" class="w-full border rounded px-3 py-2">
                                    <option value="">-- اختر المنتج --</option>
                                    {% for inventory in inventories %}
                                    {% if inventory.quantity > 0 %}
                                    <option value="{{ inventory.product_id }}" data-quantity="{{ inventory.quantity }}">
                                        {{ inventory.product.name }} (المتوفر: {{ inventory.quantity }})
                                    </option>
                                    {% endif %}
                                    {% endfor %}
                                </select>
                            </div>
                        </div>
                        <div class="grid grid-cols-1 sm:grid-cols-2 gap-4">
                            <div>
                                <label class="block text-gray-700 mb-2">المخزن المستهدف</label>
                                <select name="target_warehouse_id" id="target_warehouse_id" class="w-full border rounded px-3 py-2" required>
                                    <option value="">-- اختر المخزن --</option>
                                    {% for w in warehouses %}
                                    {% if w.id != warehouse.id and w.is_active %}
                                    <option value="{{ w.id }}">{{ w.name }}</option>
                                    {% endif %}
                                    {% endfor %}
                                </select>
                            </div>
                            <div>
                                <label class="block text-gray-700 mb-2">الكمية المراد نقلها</label>
                                <input type="number" name="quantity" id="transfer_quantity" class="w-full border rounded px-3 py-2" min="1" required>
                            </div>
                        </div>
                        <div>
                            <label class="block text-gray-700 mb-2">ملاحظات</label>
                            <textarea name="notes" id="transfer_notes" class="w-full border rounded px-3 py-2" rows="2" placeholder="أي ملاحظات إضافية حول عملية النقل"></textarea>
                        </div>
                    </div>
                    <div class="mt-4">
                        <button type="submit" class="w-full sm:w-auto bg-indigo-500 text-white px-4 py-2 rounded hover:bg-indigo-600 transition-all">
                            نقل المخزون
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // ===== نموذج إضافة/تحديث المخزون =====
            const productSearchInput = document.getElementById('product_search');
            const productSearchResults = document.getElementById('product_search_results');
            const productSelect = document.getElementById('product_id');

            // البحث عن المنتجات عند الكتابة
            productSearchInput.addEventListener('input', function() {
                const searchTerm = this.value.trim();

                if (searchTerm.length < 2) {
                    productSearchResults.classList.add('hidden');
                    return;
                }

                // استدعاء API البحث
                fetch(`/api/products/search?search=${encodeURIComponent(searchTerm)}`)
                    .then(response => response.json())
                    .then(data => {
                        if (data.products && data.products.length > 0) {
                            // عرض نتائج البحث
                            productSearchResults.innerHTML = '';
                            data.products.forEach(product => {
                                const div = document.createElement('div');
                                div.className = 'p-2 hover:bg-gray-100 cursor-pointer';
                                div.textContent = `${product.name} ${product.code ? '(' + product.code + ')' : ''}`;
                                div.dataset.id = product.id;
                                div.dataset.name = product.name;
                                div.dataset.code = product.code || '';

                                div.addEventListener('click', function() {
                                    // تحديد المنتج في القائمة المنسدلة
                                    productSelect.value = this.dataset.id;
                                    productSearchInput.value = this.dataset.name + (this.dataset.code ? ' (' + this.dataset.code + ')' : '');
                                    productSearchResults.classList.add('hidden');
                                });

                                productSearchResults.appendChild(div);
                            });

                            productSearchResults.classList.remove('hidden');
                        } else {
                            productSearchResults.innerHTML = '<div class="p-2 text-gray-500">لا توجد نتائج</div>';
                            productSearchResults.classList.remove('hidden');
                        }
                    })
                    .catch(error => {
                        console.error('Error searching products:', error);
                    });
            });

            // إخفاء نتائج البحث عند النقر خارجها
            document.addEventListener('click', function(event) {
                if (!productSearchInput.contains(event.target) && !productSearchResults.contains(event.target)) {
                    productSearchResults.classList.add('hidden');
                }
            });

            // ===== نموذج نقل المخزون =====
            const transferProductSearch = document.getElementById('transfer_product_search');
            const transferProductResults = document.getElementById('transfer_product_results');
            const transferProductSelect = document.getElementById('transfer_product_id');
            const transferQuantityInput = document.getElementById('transfer_quantity');

            // البحث عن المنتجات عند الكتابة في نموذج النقل
            if (transferProductSearch) {
                transferProductSearch.addEventListener('input', function() {
                    const searchTerm = this.value.trim();

                    if (searchTerm.length < 2) {
                        transferProductResults.classList.add('hidden');
                        return;
                    }

                    // استدعاء API البحث مع تحديد المخزن الحالي
                    fetch(`/api/products/search?search=${encodeURIComponent(searchTerm)}&warehouse_id={{ warehouse.id }}`)
                        .then(response => response.json())
                        .then(data => {
                            if (data.products && data.products.length > 0) {
                                // تصفية المنتجات التي لها كمية في المخزن الحالي
                                const availableProducts = data.products.filter(p => p.warehouse_quantity > 0);

                                if (availableProducts.length > 0) {
                                    // عرض نتائج البحث
                                    transferProductResults.innerHTML = '';
                                    availableProducts.forEach(product => {
                                        const div = document.createElement('div');
                                        div.className = 'p-2 hover:bg-gray-100 cursor-pointer';
                                        div.textContent = `${product.name} ${product.code ? '(' + product.code + ')' : ''} - المتوفر: ${product.warehouse_quantity}`;
                                        div.dataset.id = product.id;
                                        div.dataset.name = product.name;
                                        div.dataset.code = product.code || '';
                                        div.dataset.quantity = product.warehouse_quantity;

                                        div.addEventListener('click', function() {
                                            // تحديد المنتج في القائمة المنسدلة
                                            transferProductSelect.value = this.dataset.id;
                                            transferProductSearch.value = this.dataset.name + (this.dataset.code ? ' (' + this.dataset.code + ')' : '') + ` - المتوفر: ${this.dataset.quantity}`;
                                            transferProductResults.classList.add('hidden');

                                            // تحديد الحد الأقصى للكمية
                                            transferQuantityInput.max = this.dataset.quantity;
                                            transferQuantityInput.value = 1;
                                        });

                                        transferProductResults.appendChild(div);
                                    });

                                    transferProductResults.classList.remove('hidden');
                                } else {
                                    transferProductResults.innerHTML = '<div class="p-2 text-gray-500">لا توجد منتجات متوفرة في المخزن</div>';
                                    transferProductResults.classList.remove('hidden');
                                }
                            } else {
                                transferProductResults.innerHTML = '<div class="p-2 text-gray-500">لا توجد نتائج</div>';
                                transferProductResults.classList.remove('hidden');
                            }
                        })
                        .catch(error => {
                            console.error('Error searching products:', error);
                        });
                });

                // إخفاء نتائج البحث عند النقر خارجها
                document.addEventListener('click', function(event) {
                    if (!transferProductSearch.contains(event.target) && !transferProductResults.contains(event.target)) {
                        transferProductResults.classList.add('hidden');
                    }
                });
            }

            // التحقق من الكمية المدخلة في نموذج النقل
            if (transferProductSelect && transferQuantityInput) {
                transferProductSelect.addEventListener('change', function() {
                    const selectedOption = this.options[this.selectedIndex];
                    if (selectedOption && selectedOption.dataset.quantity) {
                        const maxQuantity = parseInt(selectedOption.dataset.quantity);
                        transferQuantityInput.max = maxQuantity;
                        transferQuantityInput.value = Math.min(transferQuantityInput.value || 1, maxQuantity);
                    }
                });

                // التحقق من صحة الكمية المدخلة
                transferQuantityInput.addEventListener('input', function() {
                    const selectedOption = transferProductSelect.options[transferProductSelect.selectedIndex];
                    if (selectedOption && selectedOption.dataset.quantity) {
                        const maxQuantity = parseInt(selectedOption.dataset.quantity);
                        if (parseInt(this.value) > maxQuantity) {
                            this.value = maxQuantity;
                        }
                    }
                });
            }
        });
    </script>

    <!-- عرض الجدول للشاشات الكبيرة -->
    <div class="hidden md:block bg-white rounded-lg shadow overflow-hidden">
        <table class="min-w-full">
            <thead>
                <tr class="bg-gray-100">
                    <th class="px-6 py-3 border-b text-right">المنتج</th>
                    <th class="px-6 py-3 border-b text-right">الباركود</th>
                    <th class="px-6 py-3 border-b text-right">الكمية</th>
                    <th class="px-6 py-3 border-b text-right">الحد الأدنى</th>
                    <th class="px-6 py-3 border-b text-right">الحالة</th>
                    <th class="px-6 py-3 border-b text-right">الإجراءات</th>
                </tr>
            </thead>
            <tbody>
                {% for inventory in inventories %}
                <tr class="hover:bg-gray-50 {% if inventory.quantity <= 0 %}bg-red-50{% elif inventory.quantity <= inventory.minimum_stock %}bg-yellow-50{% endif %}">
                    <td class="px-6 py-4 border-b font-medium">{{ inventory.product.name }}</td>
                    <td class="px-6 py-4 border-b">{{ inventory.product.code or '-' }}</td>
                    <td class="px-6 py-4 border-b">{{ inventory.quantity }}</td>
                    <td class="px-6 py-4 border-b">{{ inventory.minimum_stock }}</td>
                    <td class="px-6 py-4 border-b">
                        {% if inventory.quantity <= 0 %}
                        <span class="bg-red-100 text-red-800 text-xs font-medium px-2.5 py-0.5 rounded">نفذ المخزون</span>
                        {% elif inventory.quantity <= inventory.minimum_stock %}
                        <span class="bg-yellow-100 text-yellow-800 text-xs font-medium px-2.5 py-0.5 rounded">مخزون منخفض</span>
                        {% else %}
                        <span class="bg-green-100 text-green-800 text-xs font-medium px-2.5 py-0.5 rounded">متوفر</span>
                        {% endif %}
                    </td>
                    <td class="px-6 py-4 border-b">
                        <div class="flex space-x-2 space-x-reverse">
                            <button type="button" onclick="editInventory('{{ inventory.product.id }}', '{{ inventory.product.name }}', {{ inventory.quantity }}, {{ inventory.minimum_stock }})"
                                   class="bg-blue-100 text-blue-700 hover:bg-blue-200 px-2 py-1 rounded-md text-xs transition-all">
                                <i class="ri-edit-line ml-1"></i>تعديل
                            </button>
                            {% if inventory.quantity > 0 %}
                            <button type="button" onclick="transferInventory('{{ inventory.product.id }}', '{{ inventory.product.name }}', {{ inventory.quantity }})"
                                   class="bg-indigo-100 text-indigo-700 hover:bg-indigo-200 px-2 py-1 rounded-md text-xs transition-all">
                                <i class="ri-arrow-left-right-line ml-1"></i>نقل
                            </button>
                            {% endif %}
                        </div>
                    </td>
                </tr>
                {% endfor %}

                {% if inventories|length == 0 %}
                <tr>
                    <td colspan="6" class="px-6 py-8 text-center text-gray-500">
                        <div class="flex flex-col items-center">
                            <i class="ri-inbox-line text-4xl mb-2"></i>
                            <p>لا توجد منتجات في المخزون</p>
                            {% if search %}
                            <p class="text-sm mt-1">جرب البحث بكلمات مختلفة</p>
                            {% else %}
                            <p class="text-sm mt-1">استخدم نموذج الإضافة لإضافة منتجات للمخزون</p>
                            {% endif %}
                        </div>
                    </td>
                </tr>
                {% endif %}
            </tbody>
        </table>
    </div>

    <!-- عرض البطاقات للشاشات الصغيرة -->
    <div class="md:hidden space-y-4">
        {% for inventory in inventories %}
        <div class="bg-white rounded-lg shadow p-4 {% if inventory.quantity <= 0 %}border-r-4 border-red-500{% elif inventory.quantity <= inventory.minimum_stock %}border-r-4 border-yellow-500{% else %}border-r-4 border-green-500{% endif %}">
            <div class="flex justify-between items-start mb-2">
                <h3 class="font-bold text-lg">{{ inventory.product.name }}</h3>
                {% if inventory.quantity <= 0 %}
                <span class="bg-red-100 text-red-800 text-xs font-medium px-2.5 py-0.5 rounded">نفذ المخزون</span>
                {% elif inventory.quantity <= inventory.minimum_stock %}
                <span class="bg-yellow-100 text-yellow-800 text-xs font-medium px-2.5 py-0.5 rounded">مخزون منخفض</span>
                {% else %}
                <span class="bg-green-100 text-green-800 text-xs font-medium px-2.5 py-0.5 rounded">متوفر</span>
                {% endif %}
            </div>

            <div class="grid grid-cols-2 gap-2 mb-3 text-sm">
                <div>
                    <span class="text-gray-500">الباركود:</span>
                    <span>{{ inventory.product.code or '-' }}</span>
                </div>
                <div>
                    <span class="text-gray-500">الكمية:</span>
                    <span class="font-medium">{{ inventory.quantity }}</span>
                </div>
                <div>
                    <span class="text-gray-500">الحد الأدنى:</span>
                    <span>{{ inventory.minimum_stock }}</span>
                </div>
            </div>

            <div class="flex space-x-2 space-x-reverse">
                <button type="button" onclick="editInventory('{{ inventory.product.id }}', '{{ inventory.product.name }}', {{ inventory.quantity }}, {{ inventory.minimum_stock }})"
                       class="flex-1 bg-blue-100 text-blue-700 hover:bg-blue-200 px-3 py-2 rounded-md text-sm transition-all">
                    <i class="ri-edit-line ml-1"></i>تعديل
                </button>
                {% if inventory.quantity > 0 %}
                <button type="button" onclick="transferInventory('{{ inventory.product.id }}', '{{ inventory.product.name }}', {{ inventory.quantity }})"
                       class="flex-1 bg-indigo-100 text-indigo-700 hover:bg-indigo-200 px-3 py-2 rounded-md text-sm transition-all">
                    <i class="ri-arrow-left-right-line ml-1"></i>نقل
                </button>
                {% endif %}
            </div>
        </div>
        {% endfor %}

        {% if inventories|length == 0 %}
        <div class="bg-white rounded-lg shadow p-8 text-center text-gray-500">
            <div class="flex flex-col items-center">
                <i class="ri-inbox-line text-4xl mb-2"></i>
                <p>لا توجد منتجات في المخزون</p>
                {% if search %}
                <p class="text-sm mt-1">جرب البحث بكلمات مختلفة</p>
                {% else %}
                <p class="text-sm mt-1">استخدم نموذج الإضافة لإضافة منتجات للمخزون</p>
                {% endif %}
            </div>
        </div>
        {% endif %}
    </div>

    <script>
        // وظائف تعديل ونقل المخزون
        function editInventory(productId, productName, quantity, minimumStock) {
            // تعبئة نموذج التعديل
            document.getElementById('product_id').value = productId;
            document.getElementById('product_search').value = productName;
            document.getElementById('quantity').value = quantity;
            document.getElementById('minimum_stock').value = minimumStock;

            // التمرير إلى النموذج
            document.getElementById('inventoryForm').scrollIntoView({ behavior: 'smooth' });
        }

        function transferInventory(productId, productName, quantity) {
            // تعبئة نموذج النقل
            document.getElementById('transfer_product_id').value = productId;
            document.getElementById('transfer_product_search').value = productName + ` - المتوفر: ${quantity}`;
            document.getElementById('transfer_quantity').max = quantity;
            document.getElementById('transfer_quantity').value = 1;

            // التمرير إلى النموذج
            document.getElementById('transferForm').scrollIntoView({ behavior: 'smooth' });
        }
    </script>
</div>
{% endblock page_content %}
