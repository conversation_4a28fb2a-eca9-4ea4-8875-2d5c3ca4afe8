<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>فاتورة #{{ order.invoice_number }}</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='css/invoice.css') }}">
    <style>
        @page {
            {% if settings.receipt.receipt_size == '58mm' %}
            size: 58mm auto;
            margin: 2mm;
            {% elif settings.receipt.receipt_size == '80mm' %}
            size: 80mm auto;
            margin: 3mm;
            {% elif settings.receipt.receipt_size == 'a5' %}
            size: A5;
            margin: 5mm;
            {% elif settings.receipt.receipt_size == 'a4' %}
            size: A4;
            margin: 8mm;
            {% elif settings.receipt.receipt_size == 'custom' %}
            size: {{ settings.receipt.custom_width }}mm {{ settings.receipt.custom_height }}mm;
            margin: 5mm;
            {% else %}
            size: A4;
            margin: 8mm;
            {% endif %}
        }

        @media print {
            body {
                margin: 0;
                padding: 0;
                font-family: 'Cairo', sans-serif;
                {% if settings.receipt.receipt_size == '58mm' %}
                font-size: 8pt;
                {% elif settings.receipt.receipt_size == '80mm' %}
                font-size: 9pt;
                {% elif settings.receipt.receipt_size == 'a5' %}
                font-size: 10pt;
                {% elif settings.receipt.receipt_size == 'a4' %}
                font-size: 12pt;
                {% elif settings.receipt.receipt_size == 'custom' %}
                font-size: {{ 8 + (settings.receipt.custom_width / 30)|int }}pt;
                {% else %}
                font-size: 12pt;
                {% endif %}
            }

            .no-print {
                display: none !important;
            }

            .print-only {
                display: block !important;
            }

            .invoice-container {
                width: 100%;
                max-width: 100%;
                box-shadow: none;
                border: none;
                padding: 0;
                margin: 0;
            }
        }

        body {
            font-family: 'Cairo', sans-serif;
            background-color: #f3f4f6;
            margin: 0;
            padding: 20px;
            color: #1f2937;
        }

        .print-only {
            display: none;
        }

        .invoice-container {
            max-width: 800px;
            margin: 0 auto;
            background-color: white;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            border-radius: 8px;
            padding: 40px;
        }

        .invoice-header {
            display: flex;
            justify-content: space-between;
            margin-bottom: 40px;
            border-bottom: 1px solid #e5e7eb;
            padding-bottom: 20px;
        }

        .store-info {
            text-align: right;
        }

        .store-logo {
            max-width: 150px;
            max-height: 80px;
            margin-bottom: 10px;
        }

        .store-name {
            font-size: 24px;
            font-weight: bold;
            margin: 0 0 5px 0;
        }

        .store-contact {
            font-size: 14px;
            color: #6b7280;
            margin: 0 0 3px 0;
        }

        .invoice-info {
            text-align: left;
        }

        .invoice-title {
            font-size: 28px;
            font-weight: bold;
            color: #3b82f6;
            margin: 0 0 10px 0;
        }

        .invoice-number {
            font-size: 16px;
            margin: 0 0 5px 0;
        }

        .invoice-date {
            font-size: 14px;
            color: #6b7280;
            margin: 0;
        }

        .customer-info {
            margin-bottom: 30px;
            padding: 15px;
            background-color: #f9fafb;
            border-radius: 8px;
        }

        .customer-title {
            font-size: 16px;
            font-weight: bold;
            margin: 0 0 10px 0;
        }

        .customer-name {
            font-size: 18px;
            font-weight: bold;
            margin: 0 0 5px 0;
        }

        .customer-details {
            font-size: 14px;
            color: #6b7280;
            margin: 0 0 3px 0;
        }

        .invoice-items {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 30px;
        }

        .invoice-items th {
            background-color: #f3f4f6;
            padding: 12px 15px;
            text-align: right;
            font-weight: bold;
            font-size: 14px;
            border-bottom: 1px solid #e5e7eb;
        }

        .invoice-items td {
            padding: 12px 15px;
            text-align: right;
            font-size: 14px;
            border-bottom: 1px solid #e5e7eb;
        }

        .invoice-items .item-total {
            text-align: left;
            font-weight: bold;
        }

        .invoice-summary {
            width: 350px;
            margin-left: 0;
            margin-right: auto;
        }

        .summary-row {
            display: flex;
            justify-content: space-between;
            padding: 8px 0;
            font-size: 14px;
        }

        .summary-row.total {
            font-size: 18px;
            font-weight: bold;
            border-top: 2px solid #e5e7eb;
            padding-top: 12px;
            margin-top: 8px;
        }

        .payment-info {
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #e5e7eb;
            font-size: 14px;
        }

        .payment-method {
            margin-bottom: 10px;
        }

        .payment-status {
            display: inline-block;
            padding: 5px 10px;
            border-radius: 4px;
            font-weight: bold;
            background-color: #ecfdf5;
            color: #10b981;
        }

        .footer {
            margin-top: 40px;
            text-align: center;
            font-size: 14px;
            color: #6b7280;
            border-top: 1px solid #e5e7eb;
            padding-top: 20px;
        }

        .print-button {
            display: block;
            margin: 20px auto;
            padding: 10px 20px;
            background-color: #3b82f6;
            color: white;
            border: none;
            border-radius: 4px;
            font-size: 16px;
            cursor: pointer;
            transition: background-color 0.3s;
        }

        .print-button:hover {
            background-color: #2563eb;
        }

        .barcode {
            text-align: center;
            margin-top: 15px;
        }

        .barcode img {
            max-width: 180px;
            max-height: 40px;
        }
    </style>
</head>
<body>
    <div class="invoice-container">
        <div class="invoice-header">
            <div class="store-info">
                {% if settings.logo %}
                <img src="{{ url_for('static', filename='uploads/' + settings.logo) }}" alt="شعار المتجر" class="store-logo">
                {% endif %}
                <h1 class="store-name">{{ settings.business.name }}</h1>
                {% if settings.receipt.show_tax_number and settings.business.tax_number %}
                <p class="store-contact">الرقم الضريبي: {{ settings.business.tax_number }}</p>
                {% endif %}
                {% if settings.receipt.show_address and settings.business.address %}
                <p class="store-contact">{{ settings.business.address }}</p>
                {% endif %}
                {% if settings.receipt.show_phone and settings.business.phone %}
                <p class="store-contact">هاتف: {{ settings.business.phone }}</p>
                {% endif %}
                {% if settings.business.email %}
                <p class="store-contact">{{ settings.business.email }}</p>
                {% endif %}
            </div>
            <div class="invoice-info">
                <h2 class="invoice-title">فاتورة ضريبية</h2>
                <p class="invoice-number">رقم الفاتورة: {{ order.invoice_number }}</p>
                <p class="invoice-date">التاريخ: {{ order.created_at.strftime('%Y-%m-%d %H:%M') }}</p>
            </div>
        </div>

        <div class="customer-info">
            <h3 class="customer-title">بيانات العميل</h3>
            {% if order.customer %}
            <p class="customer-name">{{ order.customer.name }}</p>
            {% if order.customer.phone %}
            <p class="customer-details">هاتف: {{ order.customer.phone }}</p>
            {% endif %}
            {% if order.customer.email %}
            <p class="customer-details">البريد الإلكتروني: {{ order.customer.email }}</p>
            {% endif %}
            {% if order.customer.address %}
            <p class="customer-details">العنوان: {{ order.customer.address }}</p>
            {% endif %}
            {% else %}
            <p class="customer-name">عميل نقدي</p>
            {% endif %}

            {% if order.warehouse %}
            <p class="customer-details" style="margin-top: 10px;"><strong>المخزن:</strong> {{ order.warehouse.name }}</p>
            {% endif %}
        </div>

        <table class="invoice-items">
            <thead>
                <tr>
                    <th>#</th>
                    <th>المنتج</th>
                    <th>السعر</th>
                    <th>الكمية</th>
                    <th>الإجمالي</th>
                </tr>
            </thead>
            <tbody>
                {% for item in order.items %}
                <tr>
                    <td>{{ loop.index }}</td>
                    <td>{{ item.product.name }}</td>
                    <td>{{ item.price }} ج.م</td>
                    <td>{{ item.quantity }}</td>
                    <td class="item-total">{{ item.total }} ج.م</td>
                </tr>
                {% endfor %}
            </tbody>
        </table>

        <div class="invoice-summary">
            <div class="summary-row">
                <span>المجموع:</span>
                <span>{{ order.subtotal }} ج.م</span>
            </div>
            <div class="summary-row">
                <span>الخصم:</span>
                <span>{{ order.discount }} ج.م</span>
            </div>
            <div class="summary-row">
                <span>الضريبة ({{ order.tax_percentage }}%):</span>
                <span>{{ order.tax }} ج.م</span>
            </div>
            <div class="summary-row total">
                <span>الإجمالي:</span>
                <span>{{ order.total }} ج.م</span>
            </div>
        </div>

        <div class="payment-info">
            <div class="payment-method">
                <strong>طريقة الدفع:</strong>
                {% if order.payment_method == 'cash' %}
                نقدي
                {% elif order.payment_method == 'card' %}
                بطاقة ائتمان
                {% elif order.payment_method == 'credit' %}
                آجل
                {% else %}
                {{ order.payment_method }}
                {% endif %}
            </div>
            <div>
                <strong>حالة الدفع:</strong>
                <span class="payment-status">{{ order.status }}</span>
            </div>
        </div>

        {% if settings.receipt.show_customer_signature %}
        <div class="customer-signature">
            <div class="signature-container">
                <h3 class="signature-title">توقيع العميل</h3>
                <div class="signature-box"></div>
            </div>
        </div>
        <style>
            .customer-signature {
                margin-top: 30px;
                padding-top: 20px;
                border-top: 1px solid #e5e7eb;
            }
            .signature-container {
                width: 250px;
                margin-right: 0;
            }
            .signature-title {
                font-size: 16px;
                margin-bottom: 10px;
            }
            .signature-box {
                border: 1px dashed #6b7280;
                height: 60px;
                margin-bottom: 20px;
            }
        </style>
        {% endif %}

        <div class="footer">
            <p>شكراً لتعاملكم معنا</p>
            <p>Powered By ENG/ Fouad Saber Tel: 01020073527</p>
        </div>

        {% if settings.receipt.show_barcode %}
        <div class="barcode">
            <img src="data:image/png;base64,{{ barcode_image }}" alt="Barcode">
        </div>
        {% endif %}
    </div>

    <button class="print-button no-print" onclick="window.print()">طباعة الفاتورة</button>

    <script>
        // Auto print when page loads
        window.onload = function() {
            // Delay printing to ensure everything is loaded
            setTimeout(function() {
                window.print();
            }, 500);
        };
    </script>
</body>
</html>
