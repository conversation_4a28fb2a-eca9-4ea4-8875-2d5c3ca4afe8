<!-- ملفات JavaScript الإضافية -->
<script>
    // التحقق من تحميل الملفات لتجنب التحميل المزدوج
    if (!window.nobaraScriptsLoaded) {
        window.nobaraScriptsLoaded = true;

        // تحميل ملفات JavaScript
        function loadScript(src) {
            return new Promise((resolve, reject) => {
                const script = document.createElement('script');
                script.src = src;
                script.onload = resolve;
                script.onerror = reject;
                document.head.appendChild(script);
            });
        }

        // تحميل الملفات بالترتيب
        Promise.all([
            loadScript("{{ url_for('static', filename='js/error-logger.js') }}?v=1.0.2"),
            loadScript("{{ url_for('static', filename='js/notifications-system.js') }}?v=1.0.2"),
            loadScript("{{ url_for('static', filename='js/system-manager.js') }}?v=1.0.2"),
            loadScript("{{ url_for('static', filename='js/dashboard-manager.js') }}?v=1.0.2"),
            loadScript("{{ url_for('static', filename='js/dashboard-pro.js') }}?v=1.0.2")
        ]).catch(error => console.error('خطأ في تحميل الملفات:', error));
    }
</script>

<script>
    // تهيئة مدير النظام عند تحميل الصفحة
    document.addEventListener('DOMContentLoaded', function() {
        // تهيئة مدير النظام
        if (typeof SystemManager !== 'undefined') {
            window.systemManager = new SystemManager();
        }

        // تهيئة مدير لوحة التحكم إذا كنا في صفحة لوحة التحكم
        if (typeof DashboardManager !== 'undefined' && document.querySelector('.dashboard-title')) {
            window.dashboardManager = new DashboardManager();
        }
    });
</script>
