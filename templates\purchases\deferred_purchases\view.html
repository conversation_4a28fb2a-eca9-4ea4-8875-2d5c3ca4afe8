<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تفاصيل الشراء الآجل - نظام إدارة المبيعات</title>
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <!-- Remixicon -->
    <link href="https://cdn.jsdelivr.net/npm/remixicon@2.5.0/fonts/remixicon.css" rel="stylesheet">
    <style>
        .bg-pattern {
            background-color: #f9fafb;
            background-image: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23e5e7eb' fill-opacity='0.4'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
        }
        .glass-effect {
            background: rgba(255, 255, 255, 0.9);
            box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.07);
            backdrop-filter: blur(4px);
            -webkit-backdrop-filter: blur(4px);
            border: 1px solid rgba(255, 255, 255, 0.18);
            transition: all 0.3s ease;
        }
        .glass-effect:hover {
            box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.1);
        }
    </style>
</head>
<body class="bg-pattern min-h-screen">
    <div class="flex h-screen bg-gray-50">
        <!-- Sidebar -->
        {% include 'partials/sidebar.html' %}

        <!-- Main Content -->
        <div class="flex-1 overflow-x-hidden overflow-y-auto">
            <!-- Top Navigation -->
            {% include 'partials/topnav.html' %}

            <!-- Content Area -->
            <main class="flex-1 overflow-y-auto p-6">
                <div class="mb-6 flex flex-wrap items-center justify-between">
                    <div>
                        <h1 class="text-2xl font-bold text-gray-800">تفاصيل الشراء الآجل</h1>
                        <p class="text-gray-600">عرض تفاصيل الشراء الآجل وإدارة المدفوعات</p>
                    </div>
                    <div class="flex space-x-2 space-x-reverse">
                        <a href="{{ url_for('deferred_purchases.index') }}" class="bg-gray-200 text-gray-700 px-4 py-2 rounded-lg shadow-md hover:bg-gray-300 transition-all duration-300 flex items-center gap-2">
                            <i class="ri-arrow-right-line"></i>
                            <span>العودة للمشتريات الآجلة</span>
                        </a>
                    </div>
                </div>

                <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
                    <!-- Purchase Details -->
                    <div class="lg:col-span-2">
                        <div class="glass-effect rounded-lg overflow-hidden mb-6">
                            <div class="bg-blue-600 text-white px-6 py-4 flex justify-between items-center">
                                <h2 class="text-lg font-semibold">معلومات الشراء</h2>
                                <span class="px-3 py-1 bg-yellow-500 text-white rounded-full text-sm">آجل</span>
                            </div>
                            <div class="p-6">
                                <div class="grid grid-cols-2 gap-4 mb-4">
                                    <div>
                                        <p class="text-sm text-gray-500">رقم المرجع</p>
                                        <p class="font-semibold">{{ purchase.reference_number }}</p>
                                    </div>
                                    <div>
                                        <p class="text-sm text-gray-500">التاريخ</p>
                                        <p class="font-semibold">{{ purchase.created_at.strftime('%Y-%m-%d %H:%M') }}</p>
                                    </div>
                                    <div>
                                        <p class="text-sm text-gray-500">المورد</p>
                                        <p class="font-semibold">{{ purchase.supplier.name if purchase.supplier else 'غير محدد' }}</p>
                                    </div>
                                    <div>
                                        <p class="text-sm text-gray-500">رقم الهاتف</p>
                                        <p class="font-semibold">{{ purchase.supplier.phone if purchase.supplier and purchase.supplier.phone else 'غير محدد' }}</p>
                                    </div>
                                </div>

                                <div class="border-t border-gray-200 pt-4 mt-4">
                                    <h3 class="font-semibold mb-3">المنتجات</h3>
                                    <div class="overflow-x-auto">
                                        <table class="min-w-full divide-y divide-gray-200">
                                            <thead>
                                                <tr>
                                                    <th class="px-4 py-2 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">المنتج</th>
                                                    <th class="px-4 py-2 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الكمية</th>
                                                    <th class="px-4 py-2 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">سعر التكلفة</th>
                                                    <th class="px-4 py-2 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الإجمالي</th>
                                                </tr>
                                            </thead>
                                            <tbody class="divide-y divide-gray-200">
                                                {% for item in purchase.items %}
                                                <tr>
                                                    <td class="px-4 py-2 text-sm text-gray-900">{{ item.product.name }}</td>
                                                    <td class="px-4 py-2 text-sm text-gray-900">{{ item.quantity }}</td>
                                                    <td class="px-4 py-2 text-sm text-gray-900">{{ "%.2f"|format(item.cost_price) }} ج.م</td>
                                                    <td class="px-4 py-2 text-sm text-gray-900">{{ "%.2f"|format(item.total) }} ج.م</td>
                                                </tr>
                                                {% endfor %}
                                            </tbody>
                                        </table>
                                    </div>
                                </div>

                                <div class="border-t border-gray-200 pt-4 mt-4">
                                    <div class="flex justify-between pt-2">
                                        <span class="text-gray-800 font-bold">الإجمالي</span>
                                        <span class="font-bold text-lg">{{ "%.2f"|format(purchase.total) }} ج.م</span>
                                    </div>
                                    <div class="flex justify-between pt-2 border-t border-gray-200 mt-2">
                                        <span class="text-green-600 font-bold">المدفوع</span>
                                        <span class="font-bold text-green-600">{{ "%.2f"|format(total_paid) }} ج.م</span>
                                    </div>
                                    <div class="flex justify-between pt-2">
                                        <span class="text-red-600 font-bold">المتبقي</span>
                                        <span class="font-bold text-red-600">{{ "%.2f"|format(remaining_amount) }} ج.م</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Payments and Actions -->
                    <div class="lg:col-span-1">
                        <!-- Add Payment -->
                        <div class="glass-effect rounded-lg overflow-hidden mb-6">
                            <div class="bg-green-600 text-white px-6 py-4">
                                <h2 class="text-lg font-semibold">إضافة دفعة جديدة</h2>
                            </div>
                            <div class="p-6">
                                <form action="{{ url_for('deferred_purchases.add_payment', id=purchase.id) }}" method="POST">
                                    <div class="mb-4">
                                        <label for="amount" class="block text-sm font-medium text-gray-700 mb-1">المبلغ</label>
                                        <input type="number" step="0.01" id="amount" name="amount" max="{{ remaining_amount }}" required class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500">
                                        <p class="text-sm text-gray-500 mt-1">المبلغ المتبقي: {{ "%.2f"|format(remaining_amount) }} ج.م</p>
                                    </div>
                                    <div class="mb-4">
                                        <label for="payment_method" class="block text-sm font-medium text-gray-700 mb-1">طريقة الدفع</label>
                                        <select id="payment_method" name="payment_method" required class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500">
                                            <option value="cash">نقدي</option>
                                            <option value="card">بطاقة ائتمان</option>
                                            <option value="bank_transfer">تحويل بنكي</option>
                                            <option value="check">شيك</option>
                                        </select>
                                    </div>
                                    <div class="mb-4">
                                        <label for="notes" class="block text-sm font-medium text-gray-700 mb-1">ملاحظات</label>
                                        <textarea id="notes" name="notes" rows="2" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500"></textarea>
                                    </div>
                                    <button type="submit" class="w-full bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700 transition-all duration-300">
                                        <i class="ri-money-dollar-circle-line ml-1"></i>
                                        إضافة دفعة
                                    </button>
                                </form>
                            </div>
                        </div>

                        <!-- Payment Plan -->
                        {% if not payment_plan %}
                        <div class="glass-effect rounded-lg overflow-hidden mb-6">
                            <div class="bg-indigo-600 text-white px-6 py-4">
                                <h2 class="text-lg font-semibold">إنشاء خطة دفع</h2>
                            </div>
                            <div class="p-6">
                                <form action="{{ url_for('deferred_purchases.create_payment_plan', id=purchase.id) }}" method="POST">
                                    <div class="mb-4">
                                        <label for="installments_count" class="block text-sm font-medium text-gray-700 mb-1">عدد الأقساط</label>
                                        <input type="number" id="installments_count" name="installments_count" min="1" value="3" required class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500">
                                    </div>
                                    <div class="mb-4">
                                        <label for="start_date" class="block text-sm font-medium text-gray-700 mb-1">تاريخ البدء</label>
                                        <input type="date" id="start_date" name="start_date" required class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500">
                                    </div>
                                    <div class="mb-4">
                                        <label for="end_date" class="block text-sm font-medium text-gray-700 mb-1">تاريخ الانتهاء</label>
                                        <input type="date" id="end_date" name="end_date" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500">
                                    </div>
                                    <div class="mb-4">
                                        <label for="notes" class="block text-sm font-medium text-gray-700 mb-1">ملاحظات</label>
                                        <textarea id="notes" name="notes" rows="2" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500"></textarea>
                                    </div>
                                    <button type="submit" class="w-full bg-indigo-600 text-white px-4 py-2 rounded-md hover:bg-indigo-700 transition-all duration-300">
                                        <i class="ri-calendar-check-line ml-1"></i>
                                        إنشاء خطة دفع
                                    </button>
                                </form>
                            </div>
                        </div>
                        {% else %}
                        <!-- Payment Plan Details -->
                        <div class="glass-effect rounded-lg overflow-hidden mb-6">
                            <div class="bg-indigo-600 text-white px-6 py-4">
                                <h2 class="text-lg font-semibold">خطة الدفع</h2>
                            </div>
                            <div class="p-6">
                                <div class="mb-4">
                                    <p class="text-sm text-gray-500">المبلغ الإجمالي</p>
                                    <p class="font-semibold">{{ "%.2f"|format(payment_plan.total_amount) }} ج.م</p>
                                </div>
                                <div class="mb-4">
                                    <p class="text-sm text-gray-500">المبلغ المدفوع</p>
                                    <p class="font-semibold text-green-600">{{ "%.2f"|format(payment_plan.paid_amount) }} ج.م</p>
                                </div>
                                <div class="mb-4">
                                    <p class="text-sm text-gray-500">المبلغ المتبقي</p>
                                    <p class="font-semibold text-red-600">{{ "%.2f"|format(payment_plan.remaining_amount) }} ج.م</p>
                                </div>
                                <div class="mb-4">
                                    <p class="text-sm text-gray-500">عدد الأقساط</p>
                                    <p class="font-semibold">{{ payment_plan.installments_count }}</p>
                                </div>
                                <div class="mb-4">
                                    <p class="text-sm text-gray-500">تاريخ البدء</p>
                                    <p class="font-semibold">{{ payment_plan.start_date.strftime('%Y-%m-%d') }}</p>
                                </div>
                                <div class="mb-4">
                                    <p class="text-sm text-gray-500">تاريخ الانتهاء</p>
                                    <p class="font-semibold">{{ payment_plan.end_date.strftime('%Y-%m-%d') if payment_plan.end_date else 'غير محدد' }}</p>
                                </div>
                                <div class="mb-4">
                                    <p class="text-sm text-gray-500">الحالة</p>
                                    <p class="font-semibold">
                                        {% if payment_plan.status == 'active' %}
                                        <span class="px-2 py-1 bg-blue-100 text-blue-800 rounded-full text-xs">نشط</span>
                                        {% elif payment_plan.status == 'completed' %}
                                        <span class="px-2 py-1 bg-green-100 text-green-800 rounded-full text-xs">مكتمل</span>
                                        {% else %}
                                        <span class="px-2 py-1 bg-red-100 text-red-800 rounded-full text-xs">ملغي</span>
                                        {% endif %}
                                    </p>
                                </div>

                                <!-- Installments -->
                                <div class="mt-6">
                                    <h3 class="font-semibold mb-3">الأقساط</h3>
                                    <div class="space-y-3">
                                        {% for installment in payment_plan.installments %}
                                        <div class="border border-gray-200 rounded-md p-3 {% if installment.status == 'paid' %}bg-green-50{% elif installment.status == 'overdue' %}bg-red-50{% endif %}">
                                            <div class="flex justify-between items-center">
                                                <div>
                                                    <p class="font-medium">القسط {{ loop.index }}</p>
                                                    <p class="text-sm text-gray-500">{{ "%.2f"|format(installment.amount) }} ج.م</p>
                                                </div>
                                                <div class="text-right">
                                                    <p class="text-sm text-gray-500">تاريخ الاستحقاق: {{ installment.due_date.strftime('%Y-%m-%d') }}</p>
                                                    <p class="text-sm">
                                                        {% if installment.status == 'paid' %}
                                                        <span class="text-green-600">تم الدفع: {{ installment.payment_date.strftime('%Y-%m-%d') }}</span>
                                                        {% elif installment.status == 'overdue' %}
                                                        <span class="text-red-600">متأخر</span>
                                                        {% else %}
                                                        <span class="text-blue-600">قيد الانتظار</span>
                                                        {% endif %}
                                                    </p>
                                                </div>
                                            </div>
                                        </div>
                                        {% endfor %}
                                    </div>
                                </div>
                            </div>
                        </div>
                        {% endif %}

                        <!-- Payment History -->
                        <div class="glass-effect rounded-lg overflow-hidden">
                            <div class="bg-blue-600 text-white px-6 py-4">
                                <h2 class="text-lg font-semibold">سجل المدفوعات</h2>
                            </div>
                            <div class="p-6">
                                {% if payments %}
                                <div class="space-y-4">
                                    {% for payment in payments %}
                                    <div class="border border-gray-200 rounded-md p-4">
                                        <div class="flex justify-between items-center mb-2">
                                            <span class="font-medium">{{ "%.2f"|format(payment.amount) }} ج.م</span>
                                            <span class="text-sm text-gray-500">{{ payment.payment_date.strftime('%Y-%m-%d %H:%M') }}</span>
                                        </div>
                                        <div class="flex justify-between items-center text-sm">
                                            <span>
                                                {% if payment.payment_method == 'cash' %}
                                                <span class="px-2 py-1 bg-green-100 text-green-800 rounded-full text-xs">نقدي</span>
                                                {% elif payment.payment_method == 'card' %}
                                                <span class="px-2 py-1 bg-blue-100 text-blue-800 rounded-full text-xs">بطاقة ائتمان</span>
                                                {% elif payment.payment_method == 'bank_transfer' %}
                                                <span class="px-2 py-1 bg-purple-100 text-purple-800 rounded-full text-xs">تحويل بنكي</span>
                                                {% elif payment.payment_method == 'check' %}
                                                <span class="px-2 py-1 bg-yellow-100 text-yellow-800 rounded-full text-xs">شيك</span>
                                                {% endif %}
                                            </span>
                                            <span class="text-gray-500">{{ payment.reference_number }}</span>
                                        </div>
                                        {% if payment.notes %}
                                        <div class="mt-2 text-sm text-gray-600">
                                            <p>{{ payment.notes }}</p>
                                        </div>
                                        {% endif %}
                                    </div>
                                    {% endfor %}
                                </div>
                                {% else %}
                                <div class="text-center py-4 text-gray-500">
                                    <p>لا توجد مدفوعات حتى الآن</p>
                                </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <script>
        // تحديث تاريخ البدء تلقائيًا
        document.addEventListener('DOMContentLoaded', function() {
            const startDateInput = document.getElementById('start_date');
            if (startDateInput) {
                const today = new Date();
                const formattedDate = today.toISOString().split('T')[0];
                startDateInput.value = formattedDate;
                
                // حساب تاريخ الانتهاء الافتراضي (بعد 3 أشهر)
                const endDateInput = document.getElementById('end_date');
                if (endDateInput) {
                    const endDate = new Date();
                    endDate.setMonth(endDate.getMonth() + 3);
                    const formattedEndDate = endDate.toISOString().split('T')[0];
                    endDateInput.value = formattedEndDate;
                }
            }
        });
    </script>
</body>
</html>
