"""
Nobara POS System - API Routes
نظام نوبارا لنقاط البيع - مسارات API
"""

from flask import request, jsonify, session
from flask_login import login_required, current_user
from app.api import bp
from app.models import Product, Customer, Sale, db
from datetime import datetime
import logging

logger = logging.getLogger(__name__)

@bp.route('/search')
@login_required
def global_search():
    """البحث العام"""
    try:
        query = request.args.get('q', '').strip()
        if not query or len(query) < 2:
            return jsonify([])

        results = []

        # البحث في المنتجات
        products = Product.query.filter(
            Product.is_active == True,
            db.or_(
                Product.name.contains(query),
                Product.sku.contains(query),
                Product.barcode.contains(query)
            )
        ).limit(5).all()

        for product in products:
            results.append({
                'type': 'product',
                'id': product.id,
                'title': product.name,
                'subtitle': f'SKU: {product.sku or "غير محدد"}',
                'url': f'/products/{product.id}',
                'icon': 'ri-product-hunt-line'
            })

        # البحث في العملاء
        customers = Customer.query.filter(
            Customer.is_active == True,
            db.or_(
                Customer.name.contains(query),
                Customer.phone.contains(query),
                Customer.email.contains(query)
            )
        ).limit(5).all()

        for customer in customers:
            results.append({
                'type': 'customer',
                'id': customer.id,
                'title': customer.name,
                'subtitle': customer.phone or customer.email or '',
                'url': f'/customers/{customer.id}',
                'icon': 'ri-user-line'
            })

        return jsonify(results)

    except Exception as e:
        logger.error(f'Error in global search: {e}')
        return jsonify({'error': str(e)}), 500

@bp.route('/user/preferences', methods=['POST'])
@login_required
def update_preferences():
    """تحديث تفضيلات المستخدم"""
    try:
        data = request.get_json()

        if 'language' in data:
            current_user.language = data['language']
            session['language'] = data['language']

        if 'theme' in data:
            current_user.theme = data['theme']
            session['theme'] = data['theme']

        current_user.updated_at = datetime.utcnow()
        db.session.commit()

        return jsonify({'success': True, 'message': 'تم تحديث التفضيلات بنجاح'})

    except Exception as e:
        logger.error(f'Error updating preferences: {e}')
        return jsonify({'success': False, 'message': 'حدث خطأ أثناء تحديث التفضيلات'})

@bp.route('/dashboard/stats')
@login_required
def dashboard_stats():
    """API لإحصائيات لوحة التحكم"""
    try:
        from datetime import datetime, timedelta
        from sqlalchemy import func

        today = datetime.utcnow().date()
        today_start = datetime.combine(today, datetime.min.time())
        today_end = datetime.combine(today, datetime.max.time())

        stats = {
            'today_sales': float(db.session.query(func.sum(Sale.total_amount)).filter(
                Sale.sale_date.between(today_start, today_end),
                Sale.status == 'completed'
            ).scalar() or 0),
            'today_invoices': Sale.query.filter(
                Sale.sale_date.between(today_start, today_end),
                Sale.status == 'completed'
            ).count(),
            'total_customers': Customer.query.filter_by(is_active=True).count(),
            'total_products': Product.query.filter_by(is_active=True).count(),
            'timestamp': datetime.utcnow().isoformat()
        }

        return jsonify(stats)

    except Exception as e:
        logger.error(f'Error getting dashboard stats: {e}')
        return jsonify({'error': str(e)}), 500
