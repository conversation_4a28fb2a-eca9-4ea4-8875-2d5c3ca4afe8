"""
Nobara POS System - Settings Routes
نظام نوبارا لنقاط البيع - مسارات الإعدادات
"""

from flask import render_template, request, jsonify, redirect, url_for, flash, session
from flask_login import login_required, current_user
from app.settings import bp
from app.models import User, db
from datetime import datetime
import logging

logger = logging.getLogger(__name__)

@bp.route('/')
@login_required
def index():
    """صفحة الإعدادات الرئيسية"""
    return render_template('settings/index.html')

@bp.route('/users')
@login_required
def users():
    """إدارة المستخدمين"""
    users = User.query.all()
    return render_template('settings/users.html', users=users)

@bp.route('/system')
@login_required
def system():
    """إعدادات النظام"""
    return render_template('settings/system.html')

@bp.route('/backup')
@login_required
def backup():
    """النسخ الاحتياطي"""
    return render_template('settings/backup.html')

@bp.route('/language/<language>')
@login_required
def set_language(language):
    """تغيير اللغة"""
    if language in ['ar', 'en']:
        session['language'] = language
        if current_user.is_authenticated:
            current_user.language = language
            current_user.updated_at = datetime.utcnow()
            db.session.commit()
        
        logger.info(f'Language changed to {language}')
    
    return redirect(request.referrer or url_for('main.dashboard'))

@bp.route('/theme/<theme>')
@login_required
def set_theme(theme):
    """تغيير الثيم"""
    if theme in ['light', 'dark']:
        session['theme'] = theme
        if current_user.is_authenticated:
            current_user.theme = theme
            current_user.updated_at = datetime.utcnow()
            db.session.commit()
        
        logger.info(f'Theme changed to {theme}')
    
    return redirect(request.referrer or url_for('main.dashboard'))
