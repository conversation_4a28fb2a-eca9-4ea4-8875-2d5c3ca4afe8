<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Nobara - معلومات المتجر</title>
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <!-- Remixicon -->
    <link href="https://cdn.jsdelivr.net/npm/remixicon@2.5.0/fonts/remixicon.css" rel="stylesheet">
    <!-- Alpine.js -->
    <script defer src="https://cdn.jsdelivr.net/npm/alpinejs@3.x.x/dist/cdn.min.js"></script>
    <!-- Dropzone.js -->
    <script src="https://unpkg.com/dropzone@5/dist/min/dropzone.min.js"></script>
    <link rel="stylesheet" href="https://unpkg.com/dropzone@5/dist/min/dropzone.min.css" type="text/css" />
    <script>
        tailwind.config = {
            darkMode: 'class',
            theme: {
                extend: {
                    colors: {
                        primary: {
                            50: '#eff6ff',
                            100: '#dbeafe',
                            200: '#bfdbfe',
                            300: '#93c5fd',
                            400: '#60a5fa',
                            500: '#3b82f6',
                            600: '#2563eb',
                            700: '#1d4ed8',
                            800: '#1e40af',
                            900: '#1e3a8a',
                            950: '#172554',
                        },
                        dark: {
                            100: '#1E293B',
                            200: '#0F172A',
                            300: '#0B1120',
                            400: '#060A15'
                        }
                    }
                }
            }
        }
    </script>
    <style>
        body {
            font-family: 'Cairo', 'Tajawal', sans-serif;
            transition: background-color 0.3s ease, color 0.3s ease;
        }
        
        .bg-pattern {
            background-color: #f9fafb;
            background-image: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%233B82F6' fill-opacity='0.05'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
        }
        
        .dark .bg-pattern {
            background-color: #0F172A;
            background-image: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%233B82F6' fill-opacity='0.1'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
        }
        
        .powered-by {
            position: fixed;
            bottom: 1rem;
            left: 1rem;
            background-color: #ffffff;
            border-radius: 1rem;
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
            padding: 0.75rem 1rem;
            display: flex;
            align-items: center;
            z-index: 40;
            transition: all 0.3s ease;
            border: 1px solid #e5e7eb;
        }
        
        .dark .powered-by {
            background-color: #1E293B;
            border-color: #2D3748;
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.3), 0 4px 6px -2px rgba(0, 0, 0, 0.2);
        }
        
        .powered-by:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
        }
        
        .dark .powered-by:hover {
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.3), 0 10px 10px -5px rgba(0, 0, 0, 0.2);
        }
        
        /* تخصيص Dropzone */
        .dropzone {
            border: 2px dashed #d1d5db;
            border-radius: 0.5rem;
            background: #f9fafb;
            transition: all 0.3s ease;
        }
        
        .dark .dropzone {
            border-color: #374151;
            background: #1f2937;
        }
        
        .dropzone:hover {
            border-color: #3b82f6;
        }
        
        .dropzone .dz-message {
            margin: 2em 0;
        }
        
        .dropzone .dz-preview .dz-image {
            border-radius: 0.5rem;
        }
    </style>
</head>
<body class="bg-pattern dark:bg-dark-200 dark:text-gray-100" x-data="{ darkMode: localStorage.getItem('darkMode') === 'enabled' }">
    <div class="flex h-screen overflow-hidden">
        <!-- Sidebar -->
        {% include 'partials/sidebar.html' %}

        <!-- Main Content -->
        <div class="flex-1 flex flex-col overflow-hidden">
            <!-- Top Navigation -->
            {% include 'partials/topnav.html' %}

            <!-- Content Area -->
            <main class="flex-1 overflow-y-auto p-6">
                <div class="mb-6 flex justify-between items-center">
                    <div>
                        <h1 class="text-2xl font-bold text-gray-800 dark:text-white">معلومات المتجر</h1>
                        <p class="text-gray-600 dark:text-gray-400">تعديل معلومات المتجر الأساسية والشعار</p>
                    </div>
                    <div class="flex items-center gap-2">
                        <a href="{{ url_for('settings.index') }}" class="flex items-center gap-1 bg-gray-100 dark:bg-dark-100 hover:bg-gray-200 dark:hover:bg-dark-300 text-gray-700 dark:text-gray-300 py-2 px-4 rounded-lg transition-colors">
                            <i class="ri-arrow-right-line"></i>
                            <span>العودة للإعدادات</span>
                        </a>
                        <button @click="darkMode = !darkMode; localStorage.setItem('darkMode', darkMode ? 'enabled' : 'disabled'); document.documentElement.classList.toggle('dark')" class="p-2 rounded-full bg-gray-100 dark:bg-dark-100 hover:bg-gray-200 dark:hover:bg-dark-300 transition-colors">
                            <i class="ri-moon-line dark:hidden"></i>
                            <i class="ri-sun-line hidden dark:block"></i>
                        </button>
                    </div>
                </div>

                <div class="bg-white dark:bg-dark-100 rounded-xl shadow-sm overflow-hidden mb-6">
                    <div class="p-6">
                        <form id="storeInfoForm" action="{{ url_for('settings.update_store_info') }}" method="POST" enctype="multipart/form-data">
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div class="md:col-span-2">
                                    <h3 class="text-lg font-medium text-gray-800 dark:text-white mb-4 flex items-center">
                                        <i class="ri-store-3-line ml-2 text-primary-500"></i>
                                        المعلومات الأساسية
                                    </h3>
                                </div>
                                
                                <!-- اسم المتجر -->
                                <div>
                                    <label for="store_name" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">اسم المتجر <span class="text-red-500">*</span></label>
                                    <input type="text" id="store_name" name="store_name" value="{{ settings.business.name }}" required class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 dark:bg-dark-200 dark:text-gray-100">
                                </div>
                                
                                <!-- الرقم الضريبي -->
                                <div>
                                    <label for="tax_number" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">الرقم الضريبي</label>
                                    <input type="text" id="tax_number" name="tax_number" value="{{ settings.business.tax_number }}" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 dark:bg-dark-200 dark:text-gray-100">
                                </div>
                                
                                <!-- العنوان -->
                                <div class="md:col-span-2">
                                    <label for="address" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">العنوان</label>
                                    <textarea id="address" name="address" rows="2" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 dark:bg-dark-200 dark:text-gray-100">{{ settings.business.address }}</textarea>
                                </div>
                                
                                <!-- رقم الهاتف -->
                                <div>
                                    <label for="phone" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">رقم الهاتف</label>
                                    <input type="tel" id="phone" name="phone" value="{{ settings.business.phone }}" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 dark:bg-dark-200 dark:text-gray-100">
                                </div>
                                
                                <!-- البريد الإلكتروني -->
                                <div>
                                    <label for="email" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">البريد الإلكتروني</label>
                                    <input type="email" id="email" name="email" value="{{ settings.business.email }}" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 dark:bg-dark-200 dark:text-gray-100">
                                </div>
                                
                                <!-- الموقع الإلكتروني -->
                                <div>
                                    <label for="website" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">الموقع الإلكتروني</label>
                                    <input type="url" id="website" name="website" value="{{ settings.business.website }}" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 dark:bg-dark-200 dark:text-gray-100">
                                </div>
                                
                                <!-- العملة -->
                                <div>
                                    <label for="currency" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">العملة</label>
                                    <select id="currency" name="currency" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 dark:bg-dark-200 dark:text-gray-100">
                                        <option value="ج.م" {% if settings.display.currency == 'ج.م' %}selected{% endif %}>جنيه مصري (ج.م)</option>
                                        <option value="$" {% if settings.display.currency == '$' %}selected{% endif %}>دولار أمريكي ($)</option>
                                        <option value="€" {% if settings.display.currency == '€' %}selected{% endif %}>يورو (€)</option>
                                        <option value="﷼" {% if settings.display.currency == '﷼' %}selected{% endif %}>ريال سعودي (﷼)</option>
                                        <option value="د.إ" {% if settings.display.currency == 'د.إ' %}selected{% endif %}>درهم إماراتي (د.إ)</option>
                                        <option value="د.ك" {% if settings.display.currency == 'د.ك' %}selected{% endif %}>دينار كويتي (د.ك)</option>
                                    </select>
                                </div>
                                
                                <div class="md:col-span-2">
                                    <h3 class="text-lg font-medium text-gray-800 dark:text-white mb-4 flex items-center">
                                        <i class="ri-image-2-line ml-2 text-primary-500"></i>
                                        شعار المتجر
                                    </h3>
                                </div>
                                
                                <!-- الشعار الحالي -->
                                <div class="md:col-span-2 flex flex-col md:flex-row gap-6">
                                    <div class="flex-shrink-0">
                                        <div class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">الشعار الحالي</div>
                                        <div class="w-40 h-40 border border-gray-300 dark:border-gray-600 rounded-lg flex items-center justify-center overflow-hidden bg-white">
                                            {% if settings.business.logo %}
                                            <img src="{{ settings.business.logo }}" alt="شعار المتجر" class="max-w-full max-h-full object-contain">
                                            {% else %}
                                            <div class="text-gray-400 dark:text-gray-500 text-center">
                                                <i class="ri-image-line text-4xl"></i>
                                                <p class="text-sm mt-2">لا يوجد شعار</p>
                                            </div>
                                            {% endif %}
                                        </div>
                                    </div>
                                    
                                    <div class="flex-1">
                                        <div class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">تحميل شعار جديد</div>
                                        <div id="logoDropzone" class="dropzone">
                                            <div class="dz-message" data-dz-message>
                                                <div class="text-center">
                                                    <i class="ri-upload-cloud-2-line text-3xl text-gray-400 dark:text-gray-500"></i>
                                                    <p class="mt-2 text-sm text-gray-600 dark:text-gray-400">اسحب الشعار هنا أو انقر للتحميل</p>
                                                    <p class="text-xs text-gray-500 dark:text-gray-500 mt-1">PNG, JPG, SVG (الحد الأقصى: 2MB)</p>
                                                </div>
                                            </div>
                                            <input type="hidden" name="logo" id="logo_path">
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="md:col-span-2 flex justify-end mt-4">
                                    <button type="submit" class="bg-primary-500 hover:bg-primary-600 text-white py-2 px-6 rounded-lg inline-flex items-center transition-colors">
                                        <i class="ri-save-line ml-1"></i>
                                        <span>حفظ المعلومات</span>
                                    </button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </main>
        </div>
    </div>
    
    <!-- Powered By -->
    <div class="powered-by">
        <div class="w-8 h-8 rounded-full bg-primary-50 dark:bg-primary-900/50 flex items-center justify-center ml-2 text-primary-600 dark:text-primary-400">
            <i class="ri-code-s-slash-line"></i>
        </div>
        <div>
            <div class="text-xs text-gray-600 dark:text-gray-400">
                Powered By <span class="font-bold text-primary-600 dark:text-primary-400">ENG/ Fouad Saber</span>
            </div>
            <div class="text-xs text-gray-500 dark:text-gray-500">Tel: 01020073527</div>
        </div>
    </div>
    
    <script>
        // تفعيل وضع الظلام إذا كان مخزنًا
        if (localStorage.getItem('darkMode') === 'enabled') {
            document.documentElement.classList.add('dark');
        }
        
        // إعداد Dropzone
        Dropzone.autoDiscover = false;
        
        document.addEventListener('DOMContentLoaded', function() {
            var logoDropzone = new Dropzone("#logoDropzone", {
                url: "{{ url_for('settings.upload_logo') }}",
                paramName: "logo",
                maxFilesize: 2, // MB
                acceptedFiles: "image/jpeg,image/png,image/svg+xml",
                maxFiles: 1,
                addRemoveLinks: true,
                dictRemoveFile: "حذف",
                dictCancelUpload: "إلغاء",
                dictDefaultMessage: "اسحب الشعار هنا أو انقر للتحميل",
                init: function() {
                    this.on("success", function(file, response) {
                        document.getElementById('logo_path').value = response.path;
                    });
                    
                    this.on("error", function(file, errorMessage) {
                        alert("خطأ في تحميل الملف: " + errorMessage);
                    });
                }
            });
            
            // إرسال النموذج
            document.getElementById('storeInfoForm').addEventListener('submit', function(e) {
                e.preventDefault();
                
                fetch(this.action, {
                    method: 'POST',
                    body: new FormData(this),
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        alert("تم حفظ معلومات المتجر بنجاح");
                    } else {
                        alert("حدث خطأ أثناء حفظ المعلومات: " + data.error);
                    }
                })
                .catch(error => {
                    alert("حدث خطأ أثناء الاتصال بالخادم");
                    console.error(error);
                });
            });
        });
    </script>
</body>
</html>
