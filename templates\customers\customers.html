<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>FoUaD - إدارة العملاء</title>
    <script src="https://cdn.tailwindcss.com/3.4.16"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#6366F1',
                        secondary: '#10b981',
                        success: '#22c55e',
                        warning: '#f59e0b',
                        danger: '#ef4444',
                        info: '#3b82f6'
                    },
                    container: {
                        center: true,
                        padding: {
                            DEFAULT: '1rem',
                            sm: '2rem',
                            lg: '4rem',
                            xl: '5rem',
                            '2xl': '6rem',
                        },
                    },
                    borderRadius: {
                        'none': '0px',
                        'sm': '4px',
                        DEFAULT: '8px',
                        'md': '12px',
                        'lg': '16px',
                        'xl': '20px',
                        '2xl': '24px',
                        '3xl': '32px',
                        'full': '9999px'
                    }
                }
            }
        }
    </script>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@400;500;700&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Pacifico&display=swap" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/remixicon@3.5.0/fonts/remixicon.css" rel="stylesheet">
    <style>
        :where([class^="ri-"])::before { content: "\f3c2"; }
        body {
            font-family: 'Tajawal', sans-serif;
            background-color: #f9fafb;
        }
        .scrollbar-hide::-webkit-scrollbar {
            display: none;
        }
        .scrollbar-hide {
            -ms-overflow-style: none;
            scrollbar-width: none;
        }
        .bg-pattern {
            background-image: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%236366F1' fill-opacity='0.05'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
        }
        .glass-effect {
            backdrop-filter: blur(8px);
            background: rgba(255, 255, 255, 0.8);
            border: 1px solid rgba(255, 255, 255, 0.3);
            box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.07);
        }
        .pulse-animation {
            animation: pulse 2s infinite;
        }
        @keyframes pulse {
            0% {
                box-shadow: 0 0 0 0 rgba(99, 102, 241, 0.4);
            }
            70% {
                box-shadow: 0 0 0 10px rgba(99, 102, 241, 0);
            }
            100% {
                box-shadow: 0 0 0 0 rgba(99, 102, 241, 0);
            }
        }
        .card-hover {
            transition: all 0.3s ease;
        }
        .card-hover:hover {
            transform: translateY(-5px);
        }
    </style>
</head>
<body class="bg-pattern">
    <div class="flex h-screen overflow-hidden">
        <!-- Sidebar -->
        {% include 'partials/sidebar.html' %}
        
        <!-- Main Content -->
        <div class="flex-1 flex flex-col overflow-hidden">
            <!-- Top Navigation -->
            {% include 'partials/topnav.html' %}
            
            <!-- Content Area -->
            <main class="flex-1 overflow-y-auto p-6">
                <!-- Page Header -->
                <div class="mb-6 flex flex-wrap items-center justify-between">
                    <div>
                        <h1 class="text-2xl font-bold text-gray-800">إدارة العملاء</h1>
                        <p class="text-gray-600">عرض وإدارة العملاء في النظام</p>
                    </div>
                    <a href="{{ url_for('customers.create') }}" class="bg-gradient-to-r from-primary to-indigo-600 text-white px-4 py-2.5 rounded-lg shadow-md hover:shadow-lg transition-all duration-300 flex items-center gap-2 pulse-animation whitespace-nowrap">
                        <div class="w-5 h-5 flex items-center justify-center">
                            <i class="ri-user-add-line"></i>
                        </div>
                        <span>إضافة عميل جديد</span>
                    </a>
                </div>
                
                <!-- Stats Cards -->
                <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
                    <div class="bg-white rounded-xl shadow-sm p-6 border border-gray-100 card-hover">
                        <div class="flex items-center justify-between">
                            <div>
                                <h3 class="text-gray-500 text-sm font-medium">إجمالي العملاء</h3>
                                <div class="mt-2 flex items-baseline">
                                    <span class="text-3xl font-bold text-gray-800">{{ stats.total_customers }}</span>
                                    <span class="mr-2 text-sm text-gray-500">عميل</span>
                                </div>
                            </div>
                            <div class="w-12 h-12 rounded-full bg-primary bg-opacity-10 text-primary flex items-center justify-center">
                                <i class="ri-user-line text-xl"></i>
                            </div>
                        </div>
                    </div>
                    
                    <div class="bg-white rounded-xl shadow-sm p-6 border border-gray-100 card-hover">
                        <div class="flex items-center justify-between">
                            <div>
                                <h3 class="text-gray-500 text-sm font-medium">العملاء الجدد</h3>
                                <div class="mt-2 flex items-baseline">
                                    <span class="text-3xl font-bold text-gray-800">{{ stats.recent_customers|length }}</span>
                                    <span class="mr-2 text-sm text-gray-500">في الآونة الأخيرة</span>
                                </div>
                            </div>
                            <div class="w-12 h-12 rounded-full bg-secondary bg-opacity-10 text-secondary flex items-center justify-center">
                                <i class="ri-user-add-line text-xl"></i>
                            </div>
                        </div>
                    </div>
                    
                    <div class="bg-white rounded-xl shadow-sm p-6 border border-gray-100 card-hover">
                        <div class="flex items-center justify-between">
                            <div>
                                <h3 class="text-gray-500 text-sm font-medium">أكثر العملاء شراءً</h3>
                                <div class="mt-2 text-gray-800">
                                    {% if stats.top_customers %}
                                        <span class="font-bold">{{ stats.top_customers[0][0].name }}</span>
                                    {% else %}
                                        <span class="text-gray-500">لا يوجد بيانات</span>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="w-12 h-12 rounded-full bg-yellow-500 bg-opacity-10 text-yellow-500 flex items-center justify-center">
                                <i class="ri-vip-crown-line text-xl"></i>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Search and Filter -->
                <div class="bg-white p-4 rounded-lg shadow-sm mb-6">
                    <form method="GET" action="{{ url_for('customers.index') }}" class="grid grid-cols-1 md:grid-cols-4 gap-4">
                        <div>
                            <label for="search" class="block text-sm font-medium text-gray-700 mb-1">بحث</label>
                            <div class="relative">
                                <div class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                                    <i class="ri-search-line text-gray-400"></i>
                                </div>
                                <input type="text" id="search" name="search" value="{{ search }}" class="pr-10 block w-full bg-gray-50 border border-gray-300 rounded-md py-2 text-sm placeholder-gray-400 focus:outline-none focus:bg-white focus:border-primary focus:ring-1 focus:ring-primary" placeholder="اسم، رقم هاتف، بريد إلكتروني">
                            </div>
                        </div>
                        
                        <div class="md:col-span-3 self-end">
                            <div class="flex flex-wrap gap-2">
                                <button type="submit" class="px-4 py-2 bg-primary text-white rounded-md hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary">
                                    بحث
                                </button>
                                <a href="{{ url_for('customers.index') }}" class="px-4 py-2 bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500">
                                    إعادة تعيين
                                </a>
                            </div>
                        </div>
                    </form>
                </div>
                
                <!-- Customers Table -->
                <div class="bg-white rounded-lg shadow-sm overflow-hidden">
                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        اسم العميل
                                    </th>
                                    <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        رقم الهاتف
                                    </th>
                                    <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        البريد الإلكتروني
                                    </th>
                                    <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        العنوان
                                    </th>
                                    <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        تاريخ التسجيل
                                    </th>
                                    <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        الإجراءات
                                    </th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                {% for customer in customers.items %}
                                <tr class="hover:bg-gray-50">
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="flex items-center">
                                            <div class="flex-shrink-0 h-10 w-10 flex items-center justify-center rounded-full bg-primary bg-opacity-10 text-primary">
                                                <span class="text-lg font-medium">{{ customer.name[0]|upper }}</span>
                                            </div>
                                            <div class="mr-4">
                                                <div class="text-sm font-medium text-gray-900">
                                                    {{ customer.name }}
                                                </div>
                                                <div class="text-xs text-gray-500">
                                                    {{ customer.orders|length }} طلب
                                                </div>
                                            </div>
                                        </div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm text-gray-900">{{ customer.phone or '-' }}</div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm text-gray-900">{{ customer.email or '-' }}</div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm text-gray-900 truncate max-w-xs">{{ customer.address or '-' }}</div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                        {{ customer.created_at.strftime('%Y-%m-%d') }}
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                        <div class="flex space-x-2 space-x-reverse">
                                            <a href="{{ url_for('customers.details', id=customer.id) }}" class="text-indigo-600 hover:text-indigo-900 p-1 rounded-full hover:bg-indigo-50 transition-all">
                                                <div class="w-5 h-5 flex items-center justify-center">
                                                    <i class="ri-eye-line"></i>
                                                </div>
                                            </a>
                                            <a href="{{ url_for('customers.edit', id=customer.id) }}" class="text-blue-600 hover:text-blue-900 p-1 rounded-full hover:bg-blue-50 transition-all">
                                                <div class="w-5 h-5 flex items-center justify-center">
                                                    <i class="ri-edit-line"></i>
                                                </div>
                                            </a>
                                            <button type="button" onclick="confirmDelete({{ customer.id }}, '{{ customer.name }}')" class="text-red-600 hover:text-red-900 p-1 rounded-full hover:bg-red-50 transition-all">
                                                <div class="w-5 h-5 flex items-center justify-center">
                                                    <i class="ri-delete-bin-line"></i>
                                                </div>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                {% else %}
                                <tr>
                                    <td colspan="6" class="px-6 py-10 text-center">
                                        <div class="flex flex-col items-center">
                                            <div class="w-16 h-16 rounded-full bg-gray-100 flex items-center justify-center text-gray-400 mb-4">
                                                <i class="ri-user-line text-3xl"></i>
                                            </div>
                                            <h3 class="text-lg font-medium text-gray-800 mb-2">لا يوجد عملاء</h3>
                                            <p class="text-gray-500 max-w-md mb-6">لم يتم إضافة عملاء بعد، يمكنك إضافة عملاء جدد من خلال زر "إضافة عميل جديد".</p>
                                            <a href="{{ url_for('customers.create') }}" class="px-4 py-2 bg-primary text-white rounded-md hover:bg-indigo-700 transition-all">
                                                إضافة عميل جديد
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    
                    <!-- Pagination -->
                    {% if customers.pages > 1 %}
                    <div class="px-6 py-4 border-t border-gray-200">
                        <div class="flex items-center justify-between">
                            <div class="text-sm text-gray-500">
                                عرض {{ customers.page }} من {{ customers.pages }} صفحة
                            </div>
                            <div class="flex space-x-1 space-x-reverse">
                                {% if customers.has_prev %}
                                <a href="{{ url_for('customers.index', page=customers.prev_num, search=search) }}" class="px-3 py-1 bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200 transition-all">
                                    السابق
                                </a>
                                {% else %}
                                <span class="px-3 py-1 bg-gray-50 text-gray-400 rounded-md cursor-not-allowed">
                                    السابق
                                </span>
                                {% endif %}
                                
                                {% if customers.has_next %}
                                <a href="{{ url_for('customers.index', page=customers.next_num, search=search) }}" class="px-3 py-1 bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200 transition-all">
                                    التالي
                                </a>
                                {% else %}
                                <span class="px-3 py-1 bg-gray-50 text-gray-400 rounded-md cursor-not-allowed">
                                    التالي
                                </span>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    {% endif %}
                </div>
            </main>
        </div>
    </div>
    
    <!-- Delete Confirmation Modal -->
    <div id="deleteModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden">
        <div class="bg-white rounded-lg w-80 sm:w-96 overflow-hidden">
            <div class="p-5">
                <div class="flex items-center justify-center w-12 h-12 rounded-full bg-red-100 text-red-600 mx-auto mb-4">
                    <i class="ri-delete-bin-line ri-lg"></i>
                </div>
                <h3 class="text-lg font-medium text-gray-900 text-center mb-2">تأكيد الحذف</h3>
                <p class="text-sm text-gray-500 text-center mb-5">
                    هل أنت متأكد من حذف العميل:<br>
                    <span id="deleteCustomerName" class="font-medium"></span>؟
                </p>
                <div class="flex justify-center gap-2">
                    <button id="confirmDelete" class="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 transition-all whitespace-nowrap text-sm">
                        نعم، حذف
                    </button>
                    <button id="cancelDelete" class="px-4 py-2 bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 transition-all whitespace-nowrap text-sm">
                        إلغاء
                    </button>
                </div>
            </div>
        </div>
    </div>
    
    <form id="deleteForm" method="POST" style="display: none;">
    </form>
    
    <script>
        let customerIdToDelete = null;
        const deleteModal = document.getElementById('deleteModal');
        const deleteForm = document.getElementById('deleteForm');
        const deleteCustomerName = document.getElementById('deleteCustomerName');
        
        function confirmDelete(id, name) {
            customerIdToDelete = id;
            deleteCustomerName.textContent = name;
            deleteModal.classList.remove('hidden');
        }
        
        document.getElementById('confirmDelete').addEventListener('click', function() {
            if (customerIdToDelete) {
                deleteForm.action = `/customers/${customerIdToDelete}/delete`;
                deleteForm.submit();
            }
        });
        
        document.getElementById('cancelDelete').addEventListener('click', function() {
            closeModal();
        });
        
        document.addEventListener('click', function(event) {
            if (event.target === deleteModal) {
                closeModal();
            }
        });
        
        function closeModal() {
            deleteModal.classList.add('hidden');
            customerIdToDelete = null;
        }
    </script>
</body>
</html>