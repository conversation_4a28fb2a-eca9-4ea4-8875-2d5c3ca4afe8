{% extends "base.html" %}

{% block title %}الإغلاق اليومي للخزينة{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">الإغلاق اليومي للخزينة</h3>
                </div>
                <div class="card-body">
                    {% if open_closure %}
                        <div class="alert alert-info">
                            يوجد إغلاق مفتوح بالفعل. 
                            <a href="{{ url_for('cash.closure_details', closure_id=open_closure.id) }}" class="alert-link">
                                اضغط هنا للانتقال إلى تفاصيل الإغلاق
                            </a>
                        </div>
                    {% else %}
                        <form method="POST" class="needs-validation" novalidate>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="cash_register_id">الخزينة</label>
                                        <select class="form-control" id="cash_register_id" name="cash_register_id" required>
                                            <option value="">اختر الخزينة</option>
                                            {% for register in cash_registers %}
                                                <option value="{{ register.id }}" data-last-balance="{{ last_closures[register.id] }}">
                                                    {{ register.name }} (الرصيد الحالي: {{ register.current_balance }})
                                                </option>
                                            {% endfor %}
                                        </select>
                                        <div class="invalid-feedback">
                                            يرجى اختيار الخزينة
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="opening_balance">الرصيد الافتتاحي</label>
                                        <input type="number" step="0.01" class="form-control" id="opening_balance" 
                                               name="opening_balance" required>
                                        <div class="invalid-feedback">
                                            يرجى إدخال الرصيد الافتتاحي
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="row mt-3">
                                <div class="col-12">
                                    <div class="form-group">
                                        <label for="notes">ملاحظات</label>
                                        <textarea class="form-control" id="notes" name="notes" rows="3"></textarea>
                                    </div>
                                </div>
                            </div>
                            <div class="row mt-3">
                                <div class="col-12">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-save"></i> بدء عملية الإغلاق
                                    </button>
                                    <a href="{{ url_for('cash.cash_index') }}" class="btn btn-secondary">
                                        <i class="fas fa-times"></i> إلغاء
                                    </a>
                                </div>
                            </div>
                        </form>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // تعيين الرصيد الافتتاحي تلقائياً عند اختيار الخزينة
    const registerSelect = document.getElementById('cash_register_id');
    const openingBalanceInput = document.getElementById('opening_balance');
    
    registerSelect.addEventListener('change', function() {
        const selectedOption = this.options[this.selectedIndex];
        const lastBalance = selectedOption.dataset.lastBalance;
        if (lastBalance) {
            openingBalanceInput.value = lastBalance;
        }
    });
    
    // التحقق من صحة النموذج
    const form = document.querySelector('form');
    form.addEventListener('submit', function(event) {
        if (!form.checkValidity()) {
            event.preventDefault();
            event.stopPropagation();
        }
        form.classList.add('was-validated');
    });
});
</script>
{% endblock %} 