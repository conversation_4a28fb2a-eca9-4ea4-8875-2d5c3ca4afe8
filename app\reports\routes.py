"""
Nobara POS System - Reports Routes
نظام نوبارا لنقاط البيع - مسارات التقارير
"""

from flask import render_template, request, jsonify, redirect, url_for, flash
from flask_login import login_required, current_user
from app.reports import bp
import logging

logger = logging.getLogger(__name__)

@bp.route('/')
@login_required
def index():
    """صفحة التقارير الرئيسية"""
    return render_template('reports/index.html')

@bp.route('/sales')
@login_required
def sales():
    """تقارير المبيعات"""
    return render_template('reports/sales.html')

@bp.route('/inventory')
@login_required
def inventory():
    """تقارير المخزون"""
    return render_template('reports/inventory.html')

@bp.route('/financial')
@login_required
def financial():
    """التقارير المالية"""
    return render_template('reports/financial.html')

@bp.route('/customers')
@login_required
def customers():
    """تقارير العملاء"""
    return render_template('reports/customers.html')
