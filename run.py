#!/usr/bin/env python3
"""
Nobara POS System - Application Runner
نظام نوبارا لنقاط البيع - ملف التشغيل الرئيسي

Developer: ENG/ Fouad Saber
Phone: 01020073527
Email: <EMAIL>
"""

import os
import sys
from flask import Flask
from app import create_app, db
from app.models import User, Role, Permission, Warehouse, Category, Product, Customer
from config import Config

# إنشاء التطبيق
app = create_app(os.getenv('FLASK_ENV') or 'development')

@app.shell_context_processor
def make_shell_context():
    """إعداد سياق Shell للتطبيق"""
    return {
        'db': db,
        'User': User,
        'Role': Role,
        'Permission': Permission,
        'Warehouse': Warehouse,
        'Category': Category,
        'Product': Product,
        'Customer': Customer
    }

@app.cli.command()
def init_db():
    """تهيئة قاعدة البيانات"""
    print("🗄️  تهيئة قاعدة البيانات...")

    # إنشاء الجداول
    db.create_all()

    # إنشاء البيانات الافتراضية
    create_default_data()

    print("✅ تم تهيئة قاعدة البيانات بنجاح!")

def create_default_data():
    """إنشاء البيانات الافتراضية"""
    from werkzeug.security import generate_password_hash

    # إنشاء الصلاحيات الافتراضية
    permissions_data = [
        # صلاحيات النظام
        ('system_admin', 'مدير النظام', 'إدارة كاملة للنظام', 'system'),
        ('system_settings', 'إعدادات النظام', 'تعديل إعدادات النظام', 'system'),
        ('system_backup', 'النسخ الاحتياطي', 'إنشاء واستعادة النسخ الاحتياطية', 'system'),

        # صلاحيات المستخدمين
        ('users_view', 'عرض المستخدمين', 'عرض قائمة المستخدمين', 'users'),
        ('users_create', 'إنشاء مستخدمين', 'إنشاء مستخدمين جدد', 'users'),
        ('users_edit', 'تعديل المستخدمين', 'تعديل بيانات المستخدمين', 'users'),
        ('users_delete', 'حذف المستخدمين', 'حذف المستخدمين', 'users'),

        # صلاحيات المنتجات
        ('products_view', 'عرض المنتجات', 'عرض قائمة المنتجات', 'products'),
        ('products_create', 'إنشاء منتجات', 'إنشاء منتجات جديدة', 'products'),
        ('products_edit', 'تعديل المنتجات', 'تعديل بيانات المنتجات', 'products'),
        ('products_delete', 'حذف المنتجات', 'حذف المنتجات', 'products'),

        # صلاحيات المبيعات
        ('sales_view', 'عرض المبيعات', 'عرض قائمة المبيعات', 'sales'),
        ('sales_create', 'إنشاء مبيعات', 'إنشاء فواتير مبيعات', 'sales'),
        ('sales_edit', 'تعديل المبيعات', 'تعديل فواتير المبيعات', 'sales'),
        ('sales_delete', 'حذف المبيعات', 'حذف فواتير المبيعات', 'sales'),
        ('sales_return', 'مرتجع المبيعات', 'إنشاء مرتجع مبيعات', 'sales'),

        # صلاحيات نقطة البيع
        ('pos_access', 'الوصول لنقطة البيع', 'استخدام نقطة البيع', 'pos'),
        ('pos_discount', 'خصم نقطة البيع', 'تطبيق خصومات في نقطة البيع', 'pos'),
        ('pos_refund', 'استرداد نقطة البيع', 'إجراء استردادات في نقطة البيع', 'pos'),

        # صلاحيات المخازن
        ('inventory_view', 'عرض المخازن', 'عرض المخازن والمخزون', 'inventory'),
        ('inventory_manage', 'إدارة المخازن', 'إدارة المخازن والمخزون', 'inventory'),
        ('inventory_adjust', 'تسوية المخزون', 'إجراء تسويات المخزون', 'inventory'),

        # صلاحيات العملاء
        ('customers_view', 'عرض العملاء', 'عرض قائمة العملاء', 'customers'),
        ('customers_create', 'إنشاء عملاء', 'إنشاء عملاء جدد', 'customers'),
        ('customers_edit', 'تعديل العملاء', 'تعديل بيانات العملاء', 'customers'),
        ('customers_delete', 'حذف العملاء', 'حذف العملاء', 'customers'),

        # صلاحيات التقارير
        ('reports_view', 'عرض التقارير', 'عرض جميع التقارير', 'reports'),
        ('reports_export', 'تصدير التقارير', 'تصدير التقارير', 'reports'),
        ('reports_financial', 'التقارير المالية', 'عرض التقارير المالية', 'reports'),
    ]

    for name, name_ar, description, category in permissions_data:
        if not Permission.query.filter_by(name=name).first():
            permission = Permission(
                name=name,
                name_ar=name_ar,
                description=description,
                category=category
            )
            db.session.add(permission)

    # إنشاء الأدوار الافتراضية
    roles_data = [
        ('admin', 'مدير النظام', 'مدير النظام مع صلاحيات كاملة'),
        ('manager', 'مدير', 'مدير مع صلاحيات إدارية'),
        ('cashier', 'كاشير', 'كاشير نقطة البيع'),
        ('user', 'مستخدم', 'مستخدم عادي'),
    ]

    for name, name_ar, description in roles_data:
        if not Role.query.filter_by(name=name).first():
            role = Role(
                name=name,
                name_ar=name_ar,
                description=description
            )
            db.session.add(role)

    db.session.commit()

    # ربط الصلاحيات بالأدوار
    admin_role = Role.query.filter_by(name='admin').first()
    if admin_role:
        # إعطاء جميع الصلاحيات للمدير
        all_permissions = Permission.query.all()
        for permission in all_permissions:
            if permission not in admin_role.permissions:
                admin_role.permissions.append(permission)

    # صلاحيات الكاشير
    cashier_role = Role.query.filter_by(name='cashier').first()
    if cashier_role:
        cashier_permissions = [
            'pos_access', 'pos_discount', 'sales_view', 'sales_create',
            'products_view', 'customers_view', 'customers_create'
        ]
        for perm_name in cashier_permissions:
            permission = Permission.query.filter_by(name=perm_name).first()
            if permission and permission not in cashier_role.permissions:
                cashier_role.permissions.append(permission)

    # إنشاء المخزن الافتراضي
    if not Warehouse.query.first():
        main_warehouse = Warehouse(
            name='المخزن الرئيسي',
            name_ar='المخزن الرئيسي',
            location='المقر الرئيسي',
            description='المخزن الرئيسي للشركة',
            is_main=True,
            is_active=True
        )
        db.session.add(main_warehouse)

    # إنشاء فئات افتراضية
    categories_data = [
        ('عام', 'فئة عامة للمنتجات'),
        ('إلكترونيات', 'الأجهزة الإلكترونية'),
        ('ملابس', 'الملابس والأزياء'),
        ('طعام ومشروبات', 'المواد الغذائية والمشروبات'),
        ('مستحضرات تجميل', 'مستحضرات التجميل والعناية'),
    ]

    for name, description in categories_data:
        if not Category.query.filter_by(name=name).first():
            category = Category(
                name=name,
                name_ar=name,
                description=description,
                is_active=True
            )
            db.session.add(category)

    # إنشاء المستخدم الافتراضي
    if not User.query.filter_by(username='admin').first():
        admin_role = Role.query.filter_by(name='admin').first()
        admin_user = User(
            username='admin',
            email='<EMAIL>',
            first_name='Fouad',
            last_name='Saber',
            password_hash=generate_password_hash('admin'),
            role=admin_role,
            is_active=True,
            can_login=True,
            language='ar',
            theme='light'
        )
        db.session.add(admin_user)

    # إنشاء عميل افتراضي
    if not Customer.query.filter_by(name='عميل نقدي').first():
        cash_customer = Customer(
            name='عميل نقدي',
            customer_type='retail',
            is_active=True
        )
        db.session.add(cash_customer)

    db.session.commit()
    print("✅ تم إنشاء البيانات الافتراضية بنجاح!")

@app.cli.command()
def reset_db():
    """إعادة تعيين قاعدة البيانات"""
    print("⚠️  إعادة تعيين قاعدة البيانات...")

    if input("هل أنت متأكد؟ (y/N): ").lower() != 'y':
        print("تم الإلغاء.")
        return

    db.drop_all()
    db.create_all()
    create_default_data()

    print("✅ تم إعادة تعيين قاعدة البيانات بنجاح!")

@app.cli.command()
def create_user():
    """إنشاء مستخدم جديد"""
    from werkzeug.security import generate_password_hash

    print("إنشاء مستخدم جديد:")

    username = input("اسم المستخدم: ")
    if User.query.filter_by(username=username).first():
        print("❌ اسم المستخدم موجود مسبقاً!")
        return

    email = input("البريد الإلكتروني: ")
    first_name = input("الاسم الأول: ")
    last_name = input("الاسم الأخير: ")
    password = input("كلمة المرور: ")

    # عرض الأدوار المتاحة
    roles = Role.query.all()
    print("\nالأدوار المتاحة:")
    for i, role in enumerate(roles, 1):
        print(f"{i}. {role.name_ar} ({role.name})")

    role_choice = input("اختر رقم الدور: ")
    try:
        role = roles[int(role_choice) - 1]
    except (ValueError, IndexError):
        print("❌ اختيار غير صحيح!")
        return

    user = User(
        username=username,
        email=email,
        first_name=first_name,
        last_name=last_name,
        password_hash=generate_password_hash(password),
        role=role,
        is_active=True,
        can_login=True
    )

    db.session.add(user)
    db.session.commit()

    print(f"✅ تم إنشاء المستخدم {username} بنجاح!")

if __name__ == '__main__':
    # التحقق من وجود قاعدة البيانات
    if not os.path.exists('instance'):
        os.makedirs('instance')

    with app.app_context():
        # إنشاء الجداول إذا لم تكن موجودة
        db.create_all()

        # إنشاء البيانات الافتراضية إذا لم تكن موجودة
        if not User.query.first():
            create_default_data()

    print("""
🏪 ═══════════════════════════════════════════════════════════════
   نوبارا - نظام نقاط البيع الاحترافي
   Nobara Professional POS System

   📱 Version: 2.0.0
   👨‍💻 Developer: ENG/ Fouad Saber
   📞 Phone: 01020073527
   📧 Email: <EMAIL>

   🔐 Default Login:
   Username: admin
   Password: admin

   🌐 Access: http://localhost:5000
═══════════════════════════════════════════════════════════════
    """)

    # تشغيل التطبيق
    app.run(
        host='0.0.0.0',
        port=5000,
        debug=True,
        threaded=True
    )
