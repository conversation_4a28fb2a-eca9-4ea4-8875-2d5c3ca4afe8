<meta charset="UTF-8">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<title>Nobara - نظام نقاط البيع الذكي</title>
<!-- Tailwind CSS -->
<script src="https://cdn.tailwindcss.com/3.4.16"></script>
<script>
    tailwind.config = {
        theme: {
            extend: {
                colors: {
                    primary: {
                        50: '#eff6ff',
                        100: '#dbeafe',
                        200: '#bfdbfe',
                        300: '#93c5fd',
                        400: '#60a5fa',
                        500: '#3b82f6',
                        600: '#2563eb',
                        700: '#1d4ed8',
                        800: '#1e40af',
                        900: '#1e3a8a',
                        950: '#172554',
                    },
                    secondary: '#6b7280',
                    success: '#10b981',
                    warning: '#f59e0b',
                    danger: '#ef4444',
                    info: '#3b82f6',
                    dark: {
                        100: '#1E293B',
                        200: '#0F172A',
                        300: '#0B1120',
                        400: '#060A15'
                    }
                },
                borderRadius: {
                    'none': '0px',
                    'sm': '4px',
                    DEFAULT: '8px',
                    'md': '12px',
                    'lg': '16px',
                    'xl': '20px',
                    '2xl': '24px',
                    '3xl': '32px',
                    'full': '9999px',
                    'button': '8px'
                }
            }
        }
    }
</script>
<!-- Nobara Pro CSS -->
<link rel="stylesheet" href="{{ url_for('static', filename='css/nobara-pro.css') }}?v=1.0.0">
<!-- Dashboard Pro CSS -->
<link rel="stylesheet" href="{{ url_for('static', filename='css/dashboard-pro.css') }}?v=1.0.1">
<!-- Google Fonts -->
<link rel="preconnect" href="https://fonts.googleapis.com">
<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
<link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
<!-- Remixicon -->
<link href="https://cdn.jsdelivr.net/npm/remixicon@2.5.0/fonts/remixicon.css" rel="stylesheet">
<!-- Alpine.js -->
<script defer src="https://cdn.jsdelivr.net/npm/alpinejs@3.x.x/dist/cdn.min.js"></script>
<!-- Chart.js -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<!-- Custom Styles -->
<style>
    body {
        font-family: 'Cairo', sans-serif;
    }

    .bg-pattern {
        background-color: #f9fafb;
        background-image: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%234F46E5' fill-opacity='0.05'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
    }

    .rtl {
        direction: rtl;
    }

    /* Animations */
    @keyframes fadeIn {
        from { opacity: 0; }
        to { opacity: 1; }
    }

    @keyframes slideInUp {
        from { transform: translateY(20px); opacity: 0; }
        to { transform: translateY(0); opacity: 1; }
    }

    @keyframes pulse {
        0% { box-shadow: 0 0 0 0 rgba(79, 70, 229, 0.4); }
        70% { box-shadow: 0 0 0 10px rgba(79, 70, 229, 0); }
        100% { box-shadow: 0 0 0 0 rgba(79, 70, 229, 0); }
    }

    .fade-in {
        animation: fadeIn 0.5s ease-in-out;
    }

    .slide-in-up {
        animation: slideInUp 0.5s ease-in-out;
    }

    .pulse-animation {
        animation: pulse 2s infinite;
    }

    /* Transitions */
    .card-hover {
        transition: all 0.3s ease;
    }

    .card-hover:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 15px rgba(0, 0, 0, 0.1);
    }

    /* Scrollbar Styling */
    .scrollbar-hide::-webkit-scrollbar {
        display: none;
    }

    .scrollbar-hide {
        -ms-overflow-style: none;
        scrollbar-width: none;
    }

    /* Dark Mode */
    .dark-mode {
        background-color: #1a1a1a;
        color: #f1f1f1;
    }

    .dark-mode .bg-white {
        background-color: #2a2a2a !important;
        color: #f1f1f1 !important;
    }

    .dark-mode .text-gray-700 {
        color: #e1e1e1 !important;
    }

    .dark-mode .text-gray-600 {
        color: #c1c1c1 !important;
    }

    .dark-mode .text-gray-500 {
        color: #b1b1b1 !important;
    }

    .dark-mode .text-gray-800 {
        color: #f1f1f1 !important;
    }

    .dark-mode .border-gray-100 {
        border-color: #3a3a3a !important;
    }

    .dark-mode .border-gray-200 {
        border-color: #3a3a3a !important;
    }

    .dark-mode .border-gray-300 {
        border-color: #4a4a4a !important;
    }

    .dark-mode .bg-gray-50 {
        background-color: #333333 !important;
    }

    .dark-mode .bg-gray-100 {
        background-color: #383838 !important;
    }

    .dark-mode .shadow-lg {
        box-shadow: 0 10px 15px rgba(0, 0, 0, 0.3) !important;
    }

    .dark-mode .shadow-md {
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.3) !important;
    }

    .dark-mode .shadow {
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3) !important;
    }

    .dark-mode input,
    .dark-mode select,
    .dark-mode textarea {
        background-color: #2a2a2a !important;
        color: #f1f1f1 !important;
        border-color: #4a4a4a !important;
    }

    .dark-mode .bg-pattern {
        background-color: #1a1a1a !important;
        background-image: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%236366F1' fill-opacity='0.05'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E") !important;
    }

    .dark-mode .hover\:bg-gray-50:hover {
        background-color: #333333 !important;
    }

    .dark-mode .hover\:bg-gray-100:hover {
        background-color: #383838 !important;
    }

    .dark-mode table thead {
        background-color: #333333 !important;
    }

    .dark-mode table tbody {
        background-color: #2a2a2a !important;
    }

    .dark-mode .hover\:bg-gray-50:hover {
        background-color: #333333 !important;
    }

    .dark-mode .login-bg {
        background-color: #1a1a1a !important;
        background-image: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%236366F1' fill-opacity='0.05'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E") !important;
    }

    .dark-mode .login-card {
        background-color: rgba(42, 42, 42, 0.9) !important;
        border-color: rgba(74, 74, 74, 0.3) !important;
    }
</style>

<!-- System JavaScript -->
<script src="{{ url_for('static', filename='js/notifications-system.js') }}?v=1.0.0"></script>
<script src="{{ url_for('static', filename='js/dark-mode-manager.js') }}?v=1.0.0"></script>
<script src="{{ url_for('static', filename='js/system-manager.js') }}?v=1.0.0"></script>
