// Functions for handling credit sales and customer debt

// Global variables
let customerDebt = 0;
let customerDebtDetails = [];

// Show customer credit info when payment method is credit
function handleCreditPayment() {
    const customerId = document.getElementById('customer-select').value;
    const paymentMethod = selectedPaymentMethod;

    // Show/hide credit info based on payment method
    const creditInfoContainer = document.getElementById('customer-credit-info');

    // Check if the selected payment method is a credit type
    const selectedCard = document.querySelector(`.payment-method-card[data-method="${paymentMethod}"]`);
    const isCredit = selectedCard && selectedCard.dataset.isCredit === 'true';

    if (isCredit) {
        if (customerId && customerId !== 'null' && customerId !== '') {
            // Fetch customer debt information
            fetchCustomerDebt(customerId);
            creditInfoContainer.classList.remove('hidden');
        } else {
            creditInfoContainer.classList.add('hidden');
            // Show notification to select a customer
            showNotification('يرجى اختيار عميل للبيع الآجل', 'warning');

            // Focus on customer select dropdown
            document.getElementById('customer-select').focus();
        }
    } else {
        creditInfoContainer.classList.add('hidden');
    }
}

// Fetch customer debt information from the server
function fetchCustomerDebt(customerId) {
    // Show loading state
    document.getElementById('customer-debt-details').innerHTML = '<div class="text-center"><i class="ri-loader-4-line animate-spin"></i> جاري تحميل البيانات...</div>';

    fetch(`/api/customers/${customerId}/debt`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                customerDebt = data.total_debt || 0;
                customerDebtDetails = data.debt_details || [];

                // Update UI with debt information
                updateDebtDisplay();
            } else {
                console.error('Error fetching customer debt:', data.message);
                document.getElementById('customer-debt-details').innerHTML = '<div class="text-red-500">حدث خطأ أثناء تحميل بيانات المديونية</div>';
            }
        })
        .catch(error => {
            console.error('Error fetching customer debt:', error);
            document.getElementById('customer-debt-details').innerHTML = '<div class="text-red-500">حدث خطأ أثناء تحميل بيانات المديونية</div>';
        });
}

// Update the debt display with customer debt information
function updateDebtDisplay() {
    const debtDetailsContainer = document.getElementById('customer-debt-details');
    const totalDebtElement = document.getElementById('customer-total-debt');
    const totalWithCurrentElement = document.getElementById('total-with-current');

    // Update total debt amount
    totalDebtElement.textContent = `${customerDebt.toFixed(2)} ج.م`;

    // Calculate total with current invoice
    const currentTotal = calculateTotal();
    const totalWithCurrent = customerDebt + currentTotal;
    totalWithCurrentElement.textContent = `${totalWithCurrent.toFixed(2)} ج.م`;

    // Update debt details
    if (customerDebtDetails.length === 0) {
        debtDetailsContainer.innerHTML = '<div class="text-green-500">لا توجد مديونية سابقة</div>';
    } else {
        let detailsHtml = '<div class="max-h-20 overflow-y-auto">';
        detailsHtml += '<table class="w-full text-xs">';
        detailsHtml += '<tr class="border-b border-yellow-200"><th class="text-right">رقم الفاتورة</th><th>التاريخ</th><th class="text-left">المبلغ</th></tr>';

        customerDebtDetails.forEach(debt => {
            detailsHtml += `<tr class="border-b border-yellow-100">
                <td class="py-1">${debt.invoice_number}</td>
                <td class="py-1">${formatDate(debt.date)}</td>
                <td class="py-1 text-left">${debt.amount.toFixed(2)} ج.م</td>
            </tr>`;
        });

        detailsHtml += '</table></div>';
        debtDetailsContainer.innerHTML = detailsHtml;
    }
}

// Format date for display
function formatDate(dateString) {
    const date = new Date(dateString);
    return date.toLocaleDateString('ar-EG');
}

// Handle full debt payment
function handleFullDebtPayment() {
    // Calculate total with current invoice
    const currentTotal = calculateTotal();
    const totalWithCurrent = customerDebt + currentTotal;

    if (confirm(`هل تريد سداد كامل المديونية بمبلغ ${totalWithCurrent.toFixed(2)} ج.م؟`)) {
        // Set received amount to total debt + current invoice
        document.getElementById('received-amount').value = totalWithCurrent.toFixed(2);

        // Update change amount
        document.getElementById('received-amount').dispatchEvent(new Event('input'));

        // Change payment method to cash
        document.querySelectorAll('.payment-method-card').forEach(card => {
            if (card.dataset.method === 'cash') {
                card.click();
            }
        });
    }
}

// Handle partial debt payment
function handlePartialDebtPayment() {
    // Calculate total with current invoice
    const currentTotal = calculateTotal();
    const totalWithCurrent = customerDebt + currentTotal;

    const amount = prompt(`أدخل المبلغ المراد سداده (الإجمالي: ${totalWithCurrent.toFixed(2)} ج.م):`);

    if (amount !== null) {
        const parsedAmount = parseFloat(amount);

        if (!isNaN(parsedAmount) && parsedAmount > 0) {
            if (parsedAmount > totalWithCurrent) {
                alert(`المبلغ المدخل أكبر من إجمالي المديونية (${totalWithCurrent.toFixed(2)} ج.م)`);
                return;
            }

            // Set received amount to entered amount
            document.getElementById('received-amount').value = parsedAmount.toFixed(2);

            // Update change amount
            document.getElementById('received-amount').dispatchEvent(new Event('input'));

            // Change payment method to cash
            document.querySelectorAll('.payment-method-card').forEach(card => {
                if (card.dataset.method === 'cash') {
                    card.click();
                }
            });
        } else {
            alert('يرجى إدخال مبلغ صحيح');
        }
    }
}

// Initialize credit payment functionality
function initCreditPayment() {
    // Add event listeners for payment method changes
    document.querySelectorAll('.payment-method-card').forEach(card => {
        card.addEventListener('click', function() {
            setTimeout(handleCreditPayment, 100);
        });
    });

    // Add event listeners for debt payment buttons
    document.getElementById('pay-full-debt').addEventListener('click', handleFullDebtPayment);
    document.getElementById('pay-partial-debt').addEventListener('click', handlePartialDebtPayment);

    // Add event listener for change customer button
    document.getElementById('change-customer-btn').addEventListener('click', function() {
        hideModal('checkoutModal', 'checkout-modal-content');
        document.getElementById('customer-select').focus();
    });

    // Add event listener for customer select changes
    document.getElementById('customer-select').addEventListener('change', function() {
        if (selectedPaymentMethod === 'credit') {
            const customerId = this.value;
            if (customerId && customerId !== 'null' && customerId !== '') {
                fetchCustomerDebt(customerId);
            }
        }
    });
}

// Call this function when the page loads
document.addEventListener('DOMContentLoaded', function() {
    initCreditPayment();
});
