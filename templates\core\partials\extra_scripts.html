<!-- ملفات JavaScript الأساسية - تحميل واحد فقط لتجنب التكرار -->
<script>
    // التحقق من عدم تحميل الملفات مسبقاً
    if (typeof NotificationsSystem === 'undefined') {
        document.write('<script src="{{ url_for("static", filename="js/notifications-system.js") }}?v=1.0.1"><\/script>');
    }
    if (typeof SystemManager === 'undefined') {
        document.write('<script src="{{ url_for("static", filename="js/system-manager.js") }}?v=1.0.1"><\/script>');
    }
    if (typeof DashboardManager === 'undefined') {
        document.write('<script src="{{ url_for("static", filename="js/dashboard-manager.js") }}?v=1.0.1"><\/script>');
    }
</script>
<script src="{{ url_for('static', filename='js/dashboard-pro.js') }}?v=1.0.1"></script>

<script>
    // تهيئة مدير النظام عند تحميل الصفحة
    document.addEventListener('DOMContentLoaded', function() {
        // تهيئة مدير النظام
        if (typeof SystemManager !== 'undefined') {
            window.systemManager = new SystemManager();
        }

        // تهيئة مدير لوحة التحكم إذا كنا في صفحة لوحة التحكم
        if (typeof DashboardManager !== 'undefined' && document.querySelector('.dashboard-title')) {
            window.dashboardManager = new DashboardManager({
                darkMode: document.documentElement.classList.contains('dark-mode')
            });
        }
    });
</script>
