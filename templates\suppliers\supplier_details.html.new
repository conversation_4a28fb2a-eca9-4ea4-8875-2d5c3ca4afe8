<!DOCTYPE html>
<html lang="ar" dir="rtl" class="light">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>FoUaD - تفاصيل المورد</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdn.jsdelivr.net/npm/remixicon@2.5.0/fonts/remixicon.css" rel="stylesheet">
    <script>
        tailwind.config = {
            darkMode: 'class',
            theme: {
                extend: {
                    colors: {
                        primary: '#3B82F6',
                        secondary: '#10b981',
                        dark: {
                            100: '#1E293B',
                            200: '#0F172A',
                            300: '#0B1120',
                            400: '#060A15'
                        }
                    }
                }
            }
        }
    </script>
    <style>
        body {
            font-family: 'Tajawal', sans-serif;
            background-color: #f1f5f9;
            transition: background-color 0.3s ease, color 0.3s ease;
        }
        body.dark {
            background-color: #0F172A;
            color: #E2E8F0;
        }
        .card {
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }
        .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
        }
        .stat-card {
            position: relative;
            overflow: hidden;
        }
        .stat-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(45deg, rgba(16, 185, 129, 0.05) 0%, rgba(16, 185, 129, 0) 70%);
            z-index: 0;
        }
        .stat-card .content {
            position: relative;
            z-index: 1;
        }
        .supplier-avatar {
            background: linear-gradient(135deg, #10B981 0%, #059669 100%);
            box-shadow: 0 10px 15px -3px rgba(16, 185, 129, 0.3);
        }
        .action-button {
            transition: all 0.3s ease;
        }
        .action-button:hover {
            transform: translateY(-2px);
        }
        .page-header {
            background: linear-gradient(to right, #10B981, #059669);
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
        }
        .dark .page-header {
            background: linear-gradient(to right, #065F46, #064E3B);
        }
        .badge {
            @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;
        }
        .badge-green {
            @apply bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300;
        }
        .badge-blue {
            @apply bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300;
        }
        .badge-red {
            @apply bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300;
        }
        .badge-yellow {
            @apply bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-300;
        }
        .badge-purple {
            @apply bg-purple-100 text-purple-800 dark:bg-purple-900/30 dark:text-purple-300;
        }
        .badge-gray {
            @apply bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300;
        }
    </style>
</head>
<body class="min-h-screen">
    {% include 'partials/navbar.html' %}
    
    <!-- Page Header -->
    <div class="page-header py-6 mb-8">
        <div class="container mx-auto px-4">
            <div class="flex flex-col md:flex-row justify-between items-center">
                <div class="flex items-center mb-4 md:mb-0">
                    <div class="supplier-avatar w-16 h-16 rounded-full flex items-center justify-center text-white text-2xl font-bold mr-4">
                        {{ supplier.name[0].upper() }}
                    </div>
                    <div>
                        <h1 class="text-2xl md:text-3xl font-bold text-white">{{ supplier.name }}</h1>
                        <p class="text-green-100">
                            {% if supplier.phone %}
                                <span class="inline-flex items-center">
                                    <i class="ri-phone-line mr-1"></i> {{ supplier.phone }}
                                </span>
                            {% endif %}
                            {% if supplier.email %}
                                <span class="inline-flex items-center mr-4">
                                    <i class="ri-mail-line mr-1"></i> {{ supplier.email }}
                                </span>
                            {% endif %}
                        </p>
                    </div>
                </div>
                <div class="flex space-x-2 space-x-reverse">
                    <a href="{{ url_for('purchases.create', supplier_id=supplier.id) }}" class="action-button bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg flex items-center shadow-md">
                        <i class="ri-shopping-cart-line ml-1"></i>
                        إنشاء طلب شراء جديد
                    </a>
                    <a href="{{ url_for('suppliers.edit', id=supplier.id) }}" class="action-button bg-white text-green-600 hover:bg-green-50 px-4 py-2 rounded-lg flex items-center shadow-md">
                        <i class="ri-edit-line ml-1"></i>
                        تعديل
                    </a>
                    <a href="{{ url_for('suppliers.index') }}" class="action-button bg-white text-green-600 hover:bg-green-50 px-4 py-2 rounded-lg flex items-center shadow-md">
                        <i class="ri-arrow-right-line ml-1"></i>
                        العودة
                    </a>
                </div>
            </div>
        </div>
    </div>
    
    <div class="container mx-auto px-4 pb-8">
        <!-- Stats Cards -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <!-- إجمالي المشتريات -->
            <div class="stat-card bg-white dark:bg-gray-800 rounded-xl shadow-md p-6 card">
                <div class="content flex items-center">
                    <div class="w-12 h-12 bg-green-100 dark:bg-green-900/50 rounded-full flex items-center justify-center mr-4">
                        <i class="ri-money-dollar-circle-line text-2xl text-green-600 dark:text-green-400"></i>
                    </div>
                    <div>
                        <p class="text-sm text-gray-500 dark:text-gray-400">إجمالي المشتريات</p>
                        <p class="text-2xl font-bold text-gray-800 dark:text-white">{{ "%.2f"|format(stats.total_purchased) }} <span class="text-sm">ج.م</span></p>
                    </div>
                </div>
            </div>
            
            <!-- عدد المشتريات -->
            <div class="stat-card bg-white dark:bg-gray-800 rounded-xl shadow-md p-6 card">
                <div class="content flex items-center">
                    <div class="w-12 h-12 bg-blue-100 dark:bg-blue-900/50 rounded-full flex items-center justify-center mr-4">
                        <i class="ri-shopping-bag-line text-2xl text-blue-600 dark:text-blue-400"></i>
                    </div>
                    <div>
                        <p class="text-sm text-gray-500 dark:text-gray-400">عدد المشتريات</p>
                        <p class="text-2xl font-bold text-gray-800 dark:text-white">{{ stats.purchase_count }}</p>
                    </div>
                </div>
            </div>
            
            <!-- متوسط قيمة المشتريات -->
            <div class="stat-card bg-white dark:bg-gray-800 rounded-xl shadow-md p-6 card">
                <div class="content flex items-center">
                    <div class="w-12 h-12 bg-purple-100 dark:bg-purple-900/50 rounded-full flex items-center justify-center mr-4">
                        <i class="ri-bar-chart-line text-2xl text-purple-600 dark:text-purple-400"></i>
                    </div>
                    <div>
                        <p class="text-sm text-gray-500 dark:text-gray-400">متوسط قيمة المشتريات</p>
                        <p class="text-2xl font-bold text-gray-800 dark:text-white">{{ "%.2f"|format(stats.avg_purchase_value) }} <span class="text-sm">ج.م</span></p>
                    </div>
                </div>
            </div>
            
            <!-- آخر مشتريات -->
            <div class="stat-card bg-white dark:bg-gray-800 rounded-xl shadow-md p-6 card">
                <div class="content flex items-center">
                    <div class="w-12 h-12 bg-yellow-100 dark:bg-yellow-900/50 rounded-full flex items-center justify-center mr-4">
                        <i class="ri-time-line text-2xl text-yellow-600 dark:text-yellow-400"></i>
                    </div>
                    <div>
                        <p class="text-sm text-gray-500 dark:text-gray-400">آخر مشتريات</p>
                        <p class="text-2xl font-bold text-gray-800 dark:text-white">
                            {% if stats.last_purchase_date %}
                                {{ stats.last_purchase_date.strftime('%Y-%m-%d') }}
                            {% else %}
                                لا يوجد
                            {% endif %}
                        </p>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Supplier Info & Actions -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
            <!-- معلومات المورد -->
            <div class="bg-white dark:bg-gray-800 rounded-xl shadow-md overflow-hidden">
                <div class="bg-gradient-to-r from-green-500 to-green-600 dark:from-green-700 dark:to-green-800 px-6 py-4">
                    <h2 class="text-xl font-bold text-white flex items-center">
                        <i class="ri-user-line mr-2"></i>
                        معلومات المورد
                    </h2>
                </div>
                <div class="p-6">
                    <div class="space-y-4">
                        <div class="flex items-center border-b border-gray-200 dark:border-gray-700 pb-3">
                            <div class="w-8 h-8 rounded-full bg-green-100 dark:bg-green-900/50 flex items-center justify-center mr-3">
                                <i class="ri-user-line text-green-600 dark:text-green-400"></i>
                            </div>
                            <div class="flex-1">
                                <p class="text-sm text-gray-500 dark:text-gray-400">الاسم</p>
                                <p class="font-medium text-gray-800 dark:text-white">{{ supplier.name }}</p>
                            </div>
                        </div>
                        
                        <div class="flex items-center border-b border-gray-200 dark:border-gray-700 pb-3">
                            <div class="w-8 h-8 rounded-full bg-green-100 dark:bg-green-900/50 flex items-center justify-center mr-3">
                                <i class="ri-phone-line text-green-600 dark:text-green-400"></i>
                            </div>
                            <div class="flex-1">
                                <p class="text-sm text-gray-500 dark:text-gray-400">رقم الهاتف</p>
                                <p class="font-medium text-gray-800 dark:text-white">{{ supplier.phone or 'غير متوفر' }}</p>
                            </div>
                        </div>
                        
                        <div class="flex items-center border-b border-gray-200 dark:border-gray-700 pb-3">
                            <div class="w-8 h-8 rounded-full bg-green-100 dark:bg-green-900/50 flex items-center justify-center mr-3">
                                <i class="ri-mail-line text-green-600 dark:text-green-400"></i>
                            </div>
                            <div class="flex-1">
                                <p class="text-sm text-gray-500 dark:text-gray-400">البريد الإلكتروني</p>
                                <p class="font-medium text-gray-800 dark:text-white">{{ supplier.email or 'غير متوفر' }}</p>
                            </div>
                        </div>
                        
                        <div class="flex items-center border-b border-gray-200 dark:border-gray-700 pb-3">
                            <div class="w-8 h-8 rounded-full bg-green-100 dark:bg-green-900/50 flex items-center justify-center mr-3">
                                <i class="ri-map-pin-line text-green-600 dark:text-green-400"></i>
                            </div>
                            <div class="flex-1">
                                <p class="text-sm text-gray-500 dark:text-gray-400">العنوان</p>
                                <p class="font-medium text-gray-800 dark:text-white">{{ supplier.address or 'غير متوفر' }}</p>
                            </div>
                        </div>
                        
                        <div class="flex items-center">
                            <div class="w-8 h-8 rounded-full bg-green-100 dark:bg-green-900/50 flex items-center justify-center mr-3">
                                <i class="ri-calendar-line text-green-600 dark:text-green-400"></i>
                            </div>
                            <div class="flex-1">
                                <p class="text-sm text-gray-500 dark:text-gray-400">تاريخ التسجيل</p>
                                <p class="font-medium text-gray-800 dark:text-white">{{ supplier.created_at.strftime('%Y-%m-%d') }}</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
