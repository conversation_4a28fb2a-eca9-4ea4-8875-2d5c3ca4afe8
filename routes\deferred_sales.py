from flask import Blueprint, render_template, request, jsonify, flash, redirect, url_for
from flask_login import login_required, current_user
from models import Order, OrderItem, Customer, Product, Payment, DeferredPaymentPlan, DeferredPaymentInstallment
from app import db
from sqlalchemy import func, desc
from datetime import datetime, timedelta
import random
import string

deferred_sales_blueprint = Blueprint('deferred_sales', __name__)

@deferred_sales_blueprint.route('/deferred-sales')
@login_required
def index():
    """عرض قائمة المبيعات الآجلة"""
    # الحصول على معلمات الفلترة
    search = request.args.get('search', '')
    customer_id = request.args.get('customer', '')
    date_from = request.args.get('date_from', '')
    date_to = request.args.get('date_to', '')

    # إعداد الاستعلام
    query = Order.query.filter(
        (Order.status == 'deferred') |
        (Order.payment_method == 'deferred')
    )

    # تطبيق الفلاتر
    if search:
        query = query.filter(Order.invoice_number.ilike(f'%{search}%'))

    if customer_id and customer_id.isdigit():
        query = query.filter(Order.customer_id == int(customer_id))

    if date_from:
        try:
            date_from_obj = datetime.strptime(date_from, '%Y-%m-%d')
            query = query.filter(Order.created_at >= date_from_obj)
        except:
            pass

    if date_to:
        try:
            date_to_obj = datetime.strptime(date_to, '%Y-%m-%d')
            date_to_obj = date_to_obj.replace(hour=23, minute=59, second=59)
            query = query.filter(Order.created_at <= date_to_obj)
        except:
            pass

    # تنفيذ الاستعلام مع التقسيم إلى صفحات
    page = request.args.get('page', 1, type=int)
    per_page = 10
    orders_paginated = query.order_by(Order.created_at.desc()).paginate(page=page, per_page=per_page)

    # الحصول على العملاء لقائمة الفلترة
    customers = Customer.query.order_by(Customer.name).all()

    # إحصائيات المبيعات الآجلة
    try:
        total_deferred_orders = query.count()
        total_deferred_amount = db.session.query(func.sum(Order.total)).filter(
            (Order.status == 'deferred') |
            (Order.payment_method == 'deferred')
        ).scalar() or 0

        # حساب إجمالي المدفوعات
        total_paid = db.session.query(func.sum(Payment.amount)).join(
            Order, Payment.order_id == Order.id
        ).filter(
            (Order.status == 'deferred') |
            (Order.payment_method == 'deferred')
        ).scalar() or 0

        # حساب المبلغ المتبقي
        total_remaining = total_deferred_amount - total_paid

    except Exception as e:
        print(f"خطأ في الحصول على إحصائيات المبيعات الآجلة: {str(e)}")
        total_deferred_orders = 0
        total_deferred_amount = 0
        total_paid = 0
        total_remaining = 0

    # الإحصائيات
    stats = {
        'total_deferred_orders': total_deferred_orders,
        'total_deferred_amount': total_deferred_amount,
        'total_paid': total_paid,
        'total_remaining': total_remaining
    }

    return render_template(
        'deferred_sales/index.html',
        orders=orders_paginated,
        customers=customers,
        stats=stats,
        search=search,
        customer_id=customer_id,
        date_from=date_from,
        date_to=date_to,
        current_user=current_user
    )

@deferred_sales_blueprint.route('/deferred-sales/<int:id>')
@login_required
def view(id):
    """عرض تفاصيل البيع الآجل"""
    order = Order.query.get_or_404(id)

    # التحقق من أن الطلب آجل
    if order.status != 'deferred' and order.payment_method != 'deferred':
        flash('هذا الطلب ليس بيعاً آجلاً', 'warning')
        return redirect(url_for('deferred_sales.index'))

    # الحصول على المدفوعات
    payments = Payment.query.filter_by(order_id=id).order_by(Payment.payment_date).all()

    # حساب إجمالي المدفوعات
    total_paid = sum(payment.amount for payment in payments)
    remaining_amount = order.total - total_paid

    # الحصول على خطة الدفع إن وجدت
    payment_plan = DeferredPaymentPlan.query.filter_by(order_id=id).first()

    return render_template(
        'deferred_sales/view.html',
        order=order,
        payments=payments,
        total_paid=total_paid,
        remaining_amount=remaining_amount,
        payment_plan=payment_plan,
        current_user=current_user
    )

@deferred_sales_blueprint.route('/deferred-sales/<int:id>/add-payment', methods=['POST'])
@login_required
def add_payment(id):
    """إضافة دفعة جديدة للبيع الآجل"""
    order = Order.query.get_or_404(id)

    # التحقق من أن الطلب آجل
    if order.status != 'deferred' and order.payment_method != 'deferred':
        flash('هذا الطلب ليس بيعاً آجلاً', 'warning')
        return redirect(url_for('deferred_sales.index'))

    try:
        # الحصول على بيانات الدفعة
        amount = float(request.form.get('amount', 0))
        payment_method = request.form.get('payment_method', 'cash')
        notes = request.form.get('notes', '')

        # التحقق من صحة المبلغ
        if amount <= 0:
            flash('يجب أن يكون المبلغ أكبر من صفر', 'danger')
            return redirect(url_for('deferred_sales.view', id=id))

        # حساب المبلغ المتبقي
        payments = Payment.query.filter_by(order_id=id).all()
        total_paid = sum(payment.amount for payment in payments)
        remaining_amount = order.total - total_paid

        # التحقق من أن المبلغ لا يتجاوز المبلغ المتبقي
        if amount > remaining_amount:
            flash(f'المبلغ يتجاوز المبلغ المتبقي ({remaining_amount})', 'danger')
            return redirect(url_for('deferred_sales.view', id=id))

        # إنشاء رقم مرجعي للدفعة
        reference_number = Payment.generate_reference_number()

        # إنشاء الدفعة
        payment = Payment(
            reference_number=reference_number,
            amount=amount,
            payment_method=payment_method,
            payment_date=datetime.now(),
            notes=notes,
            created_by=current_user.id,
            order_id=id
        )

        db.session.add(payment)

        # تحديث حالة الطلب بناءً على المدفوعات
        new_total_paid = total_paid + amount

        # استخدام دالة update_payment_status لتحديث حالة الطلب
        order.update_payment_status()
        db.session.add(order)

        if order.status == 'completed':
            flash('تم دفع كامل المبلغ وتحديث حالة الطلب إلى مكتمل', 'success')
        else:
            flash(f'تم إضافة الدفعة بنجاح. المبلغ المتبقي: {order.total - new_total_paid}', 'success')

        # تحديث خطة الدفع إن وجدت
        payment_plan = DeferredPaymentPlan.query.filter_by(order_id=id).first()
        if payment_plan:
            # البحث عن القسط المستحق
            installment = DeferredPaymentInstallment.query.filter_by(
                plan_id=payment_plan.id,
                status='pending'
            ).order_by(DeferredPaymentInstallment.due_date).first()

            if installment:
                installment.mark_as_paid(payment.id)

        db.session.commit()

        flash('تم إضافة الدفعة بنجاح', 'success')

    except Exception as e:
        db.session.rollback()
        flash(f'حدث خطأ أثناء إضافة الدفعة: {str(e)}', 'danger')

    return redirect(url_for('deferred_sales.view', id=id))

@deferred_sales_blueprint.route('/deferred-sales/<int:id>/create-plan', methods=['POST'])
@login_required
def create_payment_plan(id):
    """إنشاء خطة دفع للبيع الآجل"""
    order = Order.query.get_or_404(id)

    # التحقق من أن الطلب آجل
    if order.status != 'deferred' and order.payment_method != 'deferred':
        flash('هذا الطلب ليس بيعاً آجلاً', 'warning')
        return redirect(url_for('deferred_sales.index'))

    # التحقق من عدم وجود خطة دفع سابقة
    existing_plan = DeferredPaymentPlan.query.filter_by(order_id=id).first()
    if existing_plan:
        flash('يوجد بالفعل خطة دفع لهذا الطلب', 'warning')
        return redirect(url_for('deferred_sales.view', id=id))

    try:
        # الحصول على بيانات خطة الدفع
        installments_count = int(request.form.get('installments_count', 1))
        start_date_str = request.form.get('start_date')
        end_date_str = request.form.get('end_date')
        notes = request.form.get('notes', '')

        # التحقق من صحة عدد الأقساط
        if installments_count <= 0:
            flash('يجب أن يكون عدد الأقساط أكبر من صفر', 'danger')
            return redirect(url_for('deferred_sales.view', id=id))

        # تحويل التواريخ
        start_date = datetime.strptime(start_date_str, '%Y-%m-%d') if start_date_str else datetime.now()
        end_date = datetime.strptime(end_date_str, '%Y-%m-%d') if end_date_str else None

        # حساب المبلغ المتبقي
        payments = Payment.query.filter_by(order_id=id).all()
        total_paid = sum(payment.amount for payment in payments)
        remaining_amount = order.total - total_paid

        # إنشاء خطة الدفع
        payment_plan = DeferredPaymentPlan(
            total_amount=remaining_amount,
            paid_amount=0,
            remaining_amount=remaining_amount,
            installments_count=installments_count,
            start_date=start_date,
            end_date=end_date,
            status='active',
            notes=notes,
            created_by=current_user.id,
            order_id=id
        )

        db.session.add(payment_plan)
        db.session.flush()  # للحصول على معرف خطة الدفع

        # إنشاء الأقساط
        installment_amount = remaining_amount / installments_count

        # حساب الفترة بين الأقساط
        if end_date:
            days_between = (end_date - start_date).days / installments_count
        else:
            days_between = 30  # شهر افتراضي

        for i in range(installments_count):
            due_date = start_date + timedelta(days=i * days_between)

            installment = DeferredPaymentInstallment(
                plan_id=payment_plan.id,
                amount=installment_amount,
                due_date=due_date,
                status='pending',
                notes=f'القسط رقم {i+1} من {installments_count}'
            )

            db.session.add(installment)

        db.session.commit()

        flash('تم إنشاء خطة الدفع بنجاح', 'success')

    except Exception as e:
        db.session.rollback()
        flash(f'حدث خطأ أثناء إنشاء خطة الدفع: {str(e)}', 'danger')

    return redirect(url_for('deferred_sales.view', id=id))

@deferred_sales_blueprint.route('/api/deferred-sales')
@login_required
def api_deferred_sales():
    """الحصول على قائمة المبيعات الآجلة"""
    try:
        # الحصول على معلمات الفلترة
        search = request.args.get('search', '')
        customer_id = request.args.get('customer_id', '')
        date_from = request.args.get('date_from', '')
        date_to = request.args.get('date_to', '')
        limit = request.args.get('limit', 20, type=int)

        # إعداد الاستعلام
        query = Order.query.filter(
            (Order.status == 'deferred') |
            (Order.payment_method == 'deferred')
        )

        # تطبيق الفلاتر
        if search:
            query = query.filter(Order.invoice_number.ilike(f'%{search}%'))

        if customer_id and customer_id.isdigit():
            query = query.filter(Order.customer_id == int(customer_id))

        if date_from:
            try:
                date_from_obj = datetime.strptime(date_from, '%Y-%m-%d')
                query = query.filter(Order.created_at >= date_from_obj)
            except:
                pass

        if date_to:
            try:
                date_to_obj = datetime.strptime(date_to, '%Y-%m-%d')
                date_to_obj = date_to_obj + timedelta(days=1)  # لتضمين اليوم المحدد كاملاً
                query = query.filter(Order.created_at < date_to_obj)
            except:
                pass

        # ترتيب النتائج
        query = query.order_by(Order.created_at.desc())

        # تحديد عدد النتائج
        orders = query.limit(limit).all()

        # تحضير البيانات
        orders_data = []
        for order in orders:
            # حساب المبلغ المدفوع
            payments = Payment.query.filter_by(order_id=order.id).all()
            total_paid = sum(payment.amount for payment in payments)
            remaining_amount = order.total - total_paid

            customer_name = order.customer.name if order.customer else 'عميل نقدي'

            orders_data.append({
                'id': order.id,
                'invoice_number': order.invoice_number,
                'created_at': order.created_at.strftime('%Y-%m-%d %H:%M:%S'),
                'total': order.total,
                'paid_amount': total_paid,
                'remaining_amount': remaining_amount,
                'status': order.status,
                'payment_method': order.payment_method,
                'customer_id': order.customer_id,
                'customer_name': customer_name,
                'user': order.user.username if hasattr(order, 'user') and order.user else None,
                'items_count': len(order.items)
            })

        return jsonify({
            'success': True,
            'orders': orders_data
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'حدث خطأ أثناء جلب قائمة المبيعات الآجلة: {str(e)}'
        }), 500

@deferred_sales_blueprint.route('/api/deferred-sales/<int:id>')
@login_required
def api_deferred_sale_details(id):
    """الحصول على تفاصيل البيع الآجل"""
    try:
        order = Order.query.get_or_404(id)

        # التحقق من أن الطلب آجل
        if order.status != 'deferred' and order.payment_method != 'deferred':
            return jsonify({
                'success': False,
                'message': 'هذا الطلب ليس بيعاً آجلاً'
            }), 400

        # الحصول على المدفوعات
        payments = Payment.query.filter_by(order_id=id).order_by(Payment.payment_date).all()

        # حساب إجمالي المدفوعات
        total_paid = sum(payment.amount for payment in payments)
        remaining_amount = order.total - total_paid

        # الحصول على خطة الدفع إن وجدت
        payment_plan = DeferredPaymentPlan.query.filter_by(order_id=id).first()

        # تحضير بيانات الطلب
        order_data = {
            'id': order.id,
            'invoice_number': order.invoice_number,
            'created_at': order.created_at.strftime('%Y-%m-%d %H:%M:%S'),
            'total': order.total,
            'paid_amount': total_paid,
            'remaining_amount': remaining_amount,
            'status': order.status,
            'payment_method': order.payment_method,
            'customer_id': order.customer_id,
            'customer_name': order.customer.name if order.customer else 'عميل نقدي',
            'user': order.user.username if hasattr(order, 'user') and order.user else None
        }

        # تحضير بيانات المنتجات
        items_data = []
        for item in order.items:
            product = Product.query.get(item.product_id)
            items_data.append({
                'id': item.id,
                'product_id': item.product_id,
                'product_name': product.name if product else 'منتج غير معروف',
                'quantity': item.quantity,
                'price': item.price,
                'total': item.total
            })

        # تحضير بيانات المدفوعات
        payments_data = []
        for payment in payments:
            payments_data.append({
                'id': payment.id,
                'reference_number': payment.reference_number,
                'amount': payment.amount,
                'payment_method': payment.payment_method,
                'payment_date': payment.payment_date.strftime('%Y-%m-%d %H:%M:%S'),
                'notes': payment.notes,
                'created_by': payment.created_by
            })

        # تحضير بيانات خطة الدفع
        payment_plan_data = None
        if payment_plan:
            installments_data = []
            for installment in payment_plan.installments:
                installments_data.append({
                    'id': installment.id,
                    'amount': installment.amount,
                    'due_date': installment.due_date.strftime('%Y-%m-%d'),
                    'payment_date': installment.payment_date.strftime('%Y-%m-%d %H:%M:%S') if installment.payment_date else None,
                    'status': installment.status,
                    'payment_id': installment.payment_id,
                    'notes': installment.notes
                })

            payment_plan_data = {
                'id': payment_plan.id,
                'total_amount': payment_plan.total_amount,
                'paid_amount': payment_plan.paid_amount,
                'remaining_amount': payment_plan.remaining_amount,
                'installments_count': payment_plan.installments_count,
                'start_date': payment_plan.start_date.strftime('%Y-%m-%d'),
                'end_date': payment_plan.end_date.strftime('%Y-%m-%d') if payment_plan.end_date else None,
                'status': payment_plan.status,
                'notes': payment_plan.notes,
                'installments': installments_data
            }

        return jsonify({
            'success': True,
            'order': order_data,
            'items': items_data,
            'payments': payments_data,
            'payment_plan': payment_plan_data
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'حدث خطأ أثناء جلب تفاصيل البيع الآجل: {str(e)}'
        }), 500

@deferred_sales_blueprint.route('/api/deferred-sales/stats')
@login_required
def api_deferred_sales_stats():
    """الحصول على إحصائيات المبيعات الآجلة"""
    try:
        # إجمالي المبيعات الآجلة
        total_deferred_amount = db.session.query(func.sum(Order.total)).filter(
            (Order.status == 'deferred') |
            (Order.payment_method == 'deferred')
        ).scalar() or 0

        # عدد المبيعات الآجلة
        total_deferred_orders = Order.query.filter(
            (Order.status == 'deferred') |
            (Order.payment_method == 'deferred')
        ).count()

        # إجمالي المدفوعات
        total_paid = db.session.query(func.sum(Payment.amount)).join(
            Order, Payment.order_id == Order.id
        ).filter(
            (Order.status == 'deferred') |
            (Order.payment_method == 'deferred')
        ).scalar() or 0

        # المبلغ المتبقي
        total_remaining = total_deferred_amount - total_paid

        # المبيعات الآجلة حسب العميل
        deferred_by_customer_query = db.session.query(
            Customer.id,
            Customer.name,
            func.sum(Order.total).label('total_amount'),
            func.count(Order.id).label('orders_count')
        ).join(
            Order, Customer.id == Order.customer_id
        ).filter(
            (Order.status == 'deferred') |
            (Order.payment_method == 'deferred')
        ).group_by(
            Customer.id,
            Customer.name
        ).all()

        deferred_by_customer = []
        for customer_id, name, total_amount, orders_count in deferred_by_customer_query:
            deferred_by_customer.append({
                'customer_id': customer_id,
                'customer_name': name,
                'total_amount': total_amount,
                'orders_count': orders_count
            })

        # المبيعات الآجلة حسب الشهر
        deferred_by_month_query = db.session.query(
            func.strftime('%Y-%m', Order.created_at).label('month'),
            func.sum(Order.total).label('total_amount'),
            func.count(Order.id).label('orders_count')
        ).filter(
            (Order.status == 'deferred') |
            (Order.payment_method == 'deferred')
        ).group_by(
            'month'
        ).order_by(
            'month'
        ).all()

        deferred_by_month = []
        for month, total_amount, orders_count in deferred_by_month_query:
            deferred_by_month.append({
                'month': month,
                'total_amount': total_amount,
                'orders_count': orders_count
            })

        return jsonify({
            'success': True,
            'stats': {
                'total_deferred_orders': total_deferred_orders,
                'total_deferred_amount': total_deferred_amount,
                'total_paid': total_paid,
                'total_remaining': total_remaining,
                'deferred_by_customer': deferred_by_customer,
                'deferred_by_month': deferred_by_month
            }
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'حدث خطأ أثناء جلب إحصائيات المبيعات الآجلة: {str(e)}'
        }), 500

@deferred_sales_blueprint.route('/api/deferred-sales/<int:id>/add-payment', methods=['POST'])
@login_required
def api_add_deferred_payment(id):
    """إضافة دفعة جديدة للبيع الآجل"""
    try:
        order = Order.query.get_or_404(id)

        # التحقق من أن الطلب آجل
        if order.status != 'deferred' and order.payment_method != 'deferred':
            return jsonify({
                'success': False,
                'message': 'هذا الطلب ليس بيعاً آجلاً'
            }), 400

        data = request.json
        if not data:
            return jsonify({
                'success': False,
                'message': 'لم يتم توفير بيانات الدفعة'
            }), 400

        # الحصول على بيانات الدفعة
        amount = float(data.get('amount', 0))
        payment_method = data.get('payment_method', 'cash')
        notes = data.get('notes', '')

        # التحقق من صحة المبلغ
        if amount <= 0:
            return jsonify({
                'success': False,
                'message': 'يجب أن يكون المبلغ أكبر من صفر'
            }), 400

        # حساب المبلغ المتبقي
        payments = Payment.query.filter_by(order_id=id).all()
        total_paid = sum(payment.amount for payment in payments)
        remaining_amount = order.total - total_paid

        # التحقق من أن المبلغ لا يتجاوز المبلغ المتبقي
        if amount > remaining_amount:
            return jsonify({
                'success': False,
                'message': f'المبلغ يتجاوز المبلغ المتبقي ({remaining_amount})'
            }), 400

        # إنشاء رقم مرجعي للدفعة
        reference_number = Payment.generate_reference_number()

        # إنشاء الدفعة
        payment = Payment(
            reference_number=reference_number,
            amount=amount,
            payment_method=payment_method,
            payment_date=datetime.now(),
            notes=notes,
            created_by=current_user.id,
            order_id=id
        )

        db.session.add(payment)

        # تحديث حالة الطلب بناءً على المدفوعات
        new_total_paid = total_paid + amount

        # استخدام دالة update_payment_status لتحديث حالة الطلب
        order.update_payment_status()
        db.session.add(order)

        # تحديث خطة الدفع إن وجدت
        payment_plan = DeferredPaymentPlan.query.filter_by(order_id=id).first()
        if payment_plan:
            # البحث عن القسط المستحق
            installment = DeferredPaymentInstallment.query.filter_by(
                plan_id=payment_plan.id,
                status='pending'
            ).order_by(DeferredPaymentInstallment.due_date).first()

            if installment:
                installment.mark_as_paid(payment.id)

        db.session.commit()

        return jsonify({
            'success': True,
            'message': 'تم إضافة الدفعة بنجاح',
            'payment': {
                'id': payment.id,
                'reference_number': payment.reference_number,
                'amount': payment.amount,
                'payment_method': payment.payment_method,
                'payment_date': payment.payment_date.strftime('%Y-%m-%d %H:%M:%S')
            },
            'order': {
                'id': order.id,
                'invoice_number': order.invoice_number,
                'total': order.total,
                'total_paid': new_total_paid,
                'remaining_amount': order.total - new_total_paid,
                'status': order.status
            }
        })
    except Exception as e:
        db.session.rollback()
        return jsonify({
            'success': False,
            'message': f'حدث خطأ أثناء إضافة الدفعة: {str(e)}'
        }), 500

@deferred_sales_blueprint.route('/api/deferred-sales/<int:id>/payment-plan')
@login_required
def api_deferred_sale_payment_plan(id):
    """الحصول على خطة دفع البيع الآجل"""
    try:
        order = Order.query.get_or_404(id)

        # التحقق من أن الطلب آجل
        if order.status != 'deferred' and order.payment_method != 'deferred':
            return jsonify({
                'success': False,
                'message': 'هذا الطلب ليس بيعاً آجلاً'
            }), 400

        # الحصول على خطة الدفع
        payment_plan = DeferredPaymentPlan.query.filter_by(order_id=id).first()

        if not payment_plan:
            return jsonify({
                'success': False,
                'message': 'لا توجد خطة دفع لهذا الطلب'
            }), 404

        # تحضير بيانات خطة الدفع
        installments_data = []
        for installment in payment_plan.installments:
            installments_data.append({
                'id': installment.id,
                'amount': installment.amount,
                'due_date': installment.due_date.strftime('%Y-%m-%d'),
                'payment_date': installment.payment_date.strftime('%Y-%m-%d %H:%M:%S') if installment.payment_date else None,
                'status': installment.status,
                'payment_id': installment.payment_id,
                'notes': installment.notes
            })

        payment_plan_data = {
            'id': payment_plan.id,
            'total_amount': payment_plan.total_amount,
            'paid_amount': payment_plan.paid_amount,
            'remaining_amount': payment_plan.remaining_amount,
            'installments_count': payment_plan.installments_count,
            'start_date': payment_plan.start_date.strftime('%Y-%m-%d'),
            'end_date': payment_plan.end_date.strftime('%Y-%m-%d') if payment_plan.end_date else None,
            'status': payment_plan.status,
            'notes': payment_plan.notes,
            'installments': installments_data
        }

        return jsonify({
            'success': True,
            'payment_plan': payment_plan_data
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'حدث خطأ أثناء جلب خطة دفع البيع الآجل: {str(e)}'
        }), 500

@deferred_sales_blueprint.route('/deferred-sales/reports')
@login_required
def reports():
    """تقارير المبيعات الآجلة"""
    # إحصائيات المبيعات الآجلة
    try:
        # إجمالي المبيعات الآجلة
        total_deferred_amount = db.session.query(func.sum(Order.total)).filter(
            (Order.status == 'deferred') |
            (Order.payment_method == 'deferred')
        ).scalar() or 0

        # عدد المبيعات الآجلة
        total_deferred_orders = Order.query.filter(
            (Order.status == 'deferred') |
            (Order.payment_method == 'deferred')
        ).count()

        # إجمالي المدفوعات
        total_paid = db.session.query(func.sum(Payment.amount)).join(
            Order, Payment.order_id == Order.id
        ).filter(
            (Order.status == 'deferred') |
            (Order.payment_method == 'deferred')
        ).scalar() or 0

        # المبلغ المتبقي
        total_remaining = total_deferred_amount - total_paid

        # المبيعات الآجلة حسب العميل
        deferred_by_customer = db.session.query(
            Customer.name,
            func.sum(Order.total).label('total_amount'),
            func.count(Order.id).label('orders_count')
        ).join(
            Order, Customer.id == Order.customer_id
        ).filter(
            (Order.status == 'deferred') |
            (Order.payment_method == 'deferred')
        ).group_by(
            Customer.name
        ).all()

        # المبيعات الآجلة حسب الشهر
        deferred_by_month = db.session.query(
            func.strftime('%Y-%m', Order.created_at).label('month'),
            func.sum(Order.total).label('total_amount'),
            func.count(Order.id).label('orders_count')
        ).filter(
            (Order.status == 'deferred') |
            (Order.payment_method == 'deferred')
        ).group_by(
            'month'
        ).order_by(
            'month'
        ).all()

    except Exception as e:
        print(f"خطأ في الحصول على إحصائيات المبيعات الآجلة: {str(e)}")
        total_deferred_orders = 0
        total_deferred_amount = 0
        total_paid = 0
        total_remaining = 0
        deferred_by_customer = []
        deferred_by_month = []

    # الإحصائيات
    stats = {
        'total_deferred_orders': total_deferred_orders,
        'total_deferred_amount': total_deferred_amount,
        'total_paid': total_paid,
        'total_remaining': total_remaining,
        'deferred_by_customer': deferred_by_customer,
        'deferred_by_month': deferred_by_month
    }

    return render_template(
        'deferred_sales/reports.html',
        stats=stats,
        current_user=current_user
    )
