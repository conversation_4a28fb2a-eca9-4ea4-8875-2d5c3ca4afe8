/**
 * مدير لوحة التحكم
 * يوفر واجهة برمجية لإدارة لوحة التحكم وتخصيصها
 */

class DashboardManager {
    constructor(options = {}) {
        // الإعدادات الافتراضية
        this.options = {
            refreshInterval: options.refreshInterval || 300000, // 5 دقائق
            animateCharts: options.animateCharts !== undefined ? options.animateCharts : true,
            saveSettings: options.saveSettings !== undefined ? options.saveSettings : true,
            settingsKey: options.settingsKey || 'dashboard-settings',
            apiEndpoint: options.apiEndpoint || '/api/dashboard',
            darkMode: options.darkMode || false
        };

        // حالة لوحة التحكم
        this.state = {
            loading: false,
            lastUpdate: null,
            charts: {},
            data: {},
            visibleCards: [],
            visibleCharts: []
        };

        // تهيئة المدير
        this.init();
    }

    /**
     * تهيئة مدير لوحة التحكم
     */
    init() {
        console.log('تهيئة مدير لوحة التحكم...');

        // تحميل الإعدادات المحفوظة
        this.loadSettings();

        // إعداد مستمعي الأحداث
        this.setupEventListeners();

        // تحميل بيانات لوحة التحكم
        this.loadDashboardData();

        // بدء التحديث التلقائي
        this.startAutoRefresh();

        // تهيئة الرسوم البيانية
        this.initCharts();

        // تطبيق وضع الدارك مود إذا كان مفعلاً
        if (this.options.darkMode) {
            this.applyDarkMode();
        }

        console.log('تم تهيئة مدير لوحة التحكم بنجاح');
    }

    /**
     * إعداد مستمعي الأحداث
     */
    setupEventListeners() {
        // زر تحديث لوحة التحكم
        const refreshBtn = document.getElementById('refresh-dashboard');
        if (refreshBtn) {
            refreshBtn.addEventListener('click', () => {
                this.refreshDashboard();
            });
        }

        // تم إلغاء وضع الدارك مود من البرنامج

        // زر تخصيص لوحة التحكم
        const customizeBtn = document.getElementById('customize-dashboard');
        const customizerPanel = document.getElementById('dashboard-customizer');
        const closeCustomizerBtn = document.getElementById('close-customizer');

        if (customizeBtn && customizerPanel) {
            customizeBtn.addEventListener('click', () => {
                customizerPanel.classList.remove('hidden');
                setTimeout(() => {
                    const panel = customizerPanel.querySelector('.customizer-panel');
                    if (panel) {
                        panel.classList.add('scale-100', 'opacity-100');
                    }
                }, 10);
            });
        }

        if (closeCustomizerBtn && customizerPanel) {
            closeCustomizerBtn.addEventListener('click', () => {
                const panel = customizerPanel.querySelector('.customizer-panel');
                if (panel) {
                    panel.classList.remove('scale-100', 'opacity-100');
                }
                setTimeout(() => {
                    customizerPanel.classList.add('hidden');
                }, 300);
            });
        }

        // أزرار تحديد/إلغاء تحديد الكل
        const selectAllBtn = document.getElementById('select-all-cards');
        const deselectAllBtn = document.getElementById('deselect-all-cards');

        if (selectAllBtn) {
            selectAllBtn.addEventListener('click', () => {
                document.querySelectorAll('.dashboard-toggle').forEach(toggle => {
                    toggle.checked = true;
                    this.toggleCardVisibility(toggle.dataset.target, true);
                });
            });
        }

        if (deselectAllBtn) {
            deselectAllBtn.addEventListener('click', () => {
                document.querySelectorAll('.dashboard-toggle').forEach(toggle => {
                    toggle.checked = false;
                    this.toggleCardVisibility(toggle.dataset.target, false);
                });
            });
        }

        // مستمعي أحداث مفاتيح التبديل
        document.querySelectorAll('.dashboard-toggle').forEach(toggle => {
            toggle.addEventListener('change', () => {
                this.toggleCardVisibility(toggle.dataset.target, toggle.checked);
            });
        });

        // زر حفظ إعدادات لوحة التحكم
        const saveSettingsBtn = document.getElementById('save-dashboard-settings');
        if (saveSettingsBtn) {
            saveSettingsBtn.addEventListener('click', () => {
                this.saveSettings();

                // إغلاق لوحة التخصيص
                const panel = customizerPanel.querySelector('.customizer-panel');
                if (panel) {
                    panel.classList.remove('scale-100', 'opacity-100');
                }
                setTimeout(() => {
                    customizerPanel.classList.add('hidden');
                }, 300);

                // عرض إشعار
                if (window.systemManager) {
                    window.systemManager.showNotification(
                        'تم حفظ الإعدادات',
                        'تم حفظ إعدادات لوحة التحكم بنجاح',
                        'success'
                    );
                }
            });
        }

        // زر إعادة تعيين إعدادات لوحة التحكم
        const resetSettingsBtn = document.getElementById('reset-dashboard-settings');
        if (resetSettingsBtn) {
            resetSettingsBtn.addEventListener('click', () => {
                this.resetSettings();

                // عرض إشعار
                if (window.systemManager) {
                    window.systemManager.showNotification(
                        'تم إعادة التعيين',
                        'تم إعادة تعيين إعدادات لوحة التحكم إلى الوضع الافتراضي',
                        'info'
                    );
                }
            });
        }

        // مستمع تغيير فترة الرسم البياني
        const chartPeriodSelect = document.getElementById('chart-period');
        if (chartPeriodSelect) {
            chartPeriodSelect.addEventListener('change', () => {
                this.updateChartPeriod(chartPeriodSelect.value);
            });
        }

        // مستمع تغيير نوع الرسم البياني
        const chartTypeToggle = document.getElementById('chart-type-toggle');
        if (chartTypeToggle) {
            chartTypeToggle.addEventListener('click', () => {
                this.toggleChartType();
            });
        }
    }

    /**
     * تحميل بيانات لوحة التحكم
     */
    loadDashboardData() {
        this.state.loading = true;
        this.updateLoadingState(true);

        fetch(this.options.apiEndpoint)
            .then(response => response.json())
            .then(data => {
                this.state.data = data;
                this.state.lastUpdate = new Date();
                this.updateDashboard();
                this.state.loading = false;
                this.updateLoadingState(false);
            })
            .catch(error => {
                console.error('خطأ في تحميل بيانات لوحة التحكم:', error);
                this.state.loading = false;
                this.updateLoadingState(false);

                // عرض إشعار
                if (window.systemManager) {
                    window.systemManager.showNotification(
                        'خطأ في التحميل',
                        'حدث خطأ أثناء تحميل بيانات لوحة التحكم',
                        'error'
                    );
                }
            });
    }

    /**
     * تحديث حالة التحميل
     * @param {boolean} loading - حالة التحميل
     */
    updateLoadingState(loading) {
        const refreshBtn = document.getElementById('refresh-dashboard');
        if (refreshBtn) {
            if (loading) {
                refreshBtn.disabled = true;
                refreshBtn.innerHTML = '<i class="ri-loader-4-line animate-spin"></i><span class="mr-1">جاري التحديث...</span>';
            } else {
                refreshBtn.disabled = false;
                refreshBtn.innerHTML = '<i class="ri-refresh-line"></i><span class="mr-1">تحديث</span>';
            }
        }
    }

    /**
     * تحديث لوحة التحكم
     */
    updateDashboard() {
        // تحديث البطاقات الإحصائية
        this.updateStatsCards();

        // تحديث الرسوم البيانية
        this.updateCharts();

        // تحديث الجداول
        this.updateTables();

        // تحديث وقت آخر تحديث
        this.updateLastUpdateTime();
    }

    /**
     * تحديث البطاقات الإحصائية
     */
    updateStatsCards() {
        if (!this.state.data) return;

        // تحديث بطاقات المبيعات
        const salesData = this.state.data.sales || {};
        this.updateCardValue('daily-sales-card', salesData.daily || 0, 'ج.م');
        this.updateCardValue('today-sales-card', salesData.daily || 0, 'ج.م');
        this.updateCardValue('weekly-sales-card', salesData.weekly || 0, 'ج.م');
        this.updateCardValue('monthly-sales-card', salesData.monthly || 0, 'ج.م');
        this.updateCardValue('total-sales-card', salesData.total || 0, 'ج.م');
        this.updateCardValue('total-sales-card-2', salesData.total || 0, 'ج.م');

        // تحديث بطاقات المشتريات
        const purchasesData = this.state.data.purchases || {};
        this.updateCardValue('daily-purchases-card', purchasesData.daily || 0, 'ج.م');
        this.updateCardValue('total-purchases-card', purchasesData.total || 0, 'ج.م');

        // تحديث بطاقات المخزون
        const inventoryData = this.state.data.inventory || {};
        this.updateCardValue('total-products-card', inventoryData.total_products || 0);
        this.updateCardValue('low-stock-card', inventoryData.low_stock || 0);
        this.updateCardValue('low-stock-card-2', inventoryData.low_stock || 0);
        this.updateCardValue('out-of-stock-card', inventoryData.out_of_stock || 0);

        // تحديث بطاقات العملاء والموردين
        const customersData = this.state.data.customers || {};
        this.updateCardValue('customers-card', customersData.total || 0);

        // تحديث بطاقة الأرباح
        const profitData = this.state.data.profit || {};
        this.updateCardValue('profit-card', profitData.total || 0, 'ج.م');

        console.log('تم تحديث البطاقات الإحصائية');
    }

    /**
     * تحديث قيمة بطاقة إحصائية
     * @param {string} cardId - معرف البطاقة
     * @param {number} value - القيمة الجديدة
     * @param {string} unit - وحدة القياس (اختياري)
     */
    updateCardValue(cardId, value, unit = '') {
        const card = document.getElementById(cardId);
        if (!card) return;

        const valueElement = card.querySelector('p.text-2xl');
        if (valueElement) {
            // تنسيق الرقم بفواصل الآلاف
            const formattedValue = this.formatNumber(value);
            valueElement.textContent = unit ? `${formattedValue} ${unit}` : formattedValue;
        }
    }

    /**
     * تنسيق الرقم بفواصل الآلاف
     * @param {number} number - الرقم المراد تنسيقه
     * @returns {string} - الرقم المنسق
     */
    formatNumber(number) {
        return number.toLocaleString('ar-EG', {
            minimumFractionDigits: 2,
            maximumFractionDigits: 2
        });
    }

    /**
     * تحديث الرسوم البيانية
     */
    updateCharts() {
        if (!this.state.data) return;

        // تحديث رسم بياني المبيعات
        this.updateSalesChart();

        // تحديث رسم بياني توزيع المنتجات
        this.updateCategoriesChart();

        console.log('تم تحديث الرسوم البيانية');
    }

    /**
     * تحديث الجداول
     */
    updateTables() {
        if (!this.state.data) return;

        // تحديث جدول آخر المبيعات
        this.updateRecentSalesTable();

        // تحديث جدول المنتجات منخفضة المخزون
        this.updateLowStockTable();

        // تحديث جدول المنتجات الأكثر مبيعاً
        this.updateTopProductsTable();

        console.log('تم تحديث الجداول');
    }

    /**
     * تحديث وقت آخر تحديث
     */
    updateLastUpdateTime() {
        const lastUpdateElement = document.getElementById('last-update-time');
        if (lastUpdateElement && this.state.lastUpdate) {
            const formattedTime = this.state.lastUpdate.toLocaleTimeString('ar-EG');
            lastUpdateElement.textContent = `آخر تحديث: ${formattedTime}`;
        }
    }

    /**
     * تحديث لوحة التحكم
     */
    refreshDashboard() {
        if (!this.state.loading) {
            this.loadDashboardData();

            // عرض إشعار
            if (window.systemManager) {
                window.systemManager.showNotification(
                    'جاري التحديث',
                    'جاري تحديث بيانات لوحة التحكم',
                    'info'
                );
            }
        }
    }

    /**
     * بدء التحديث التلقائي
     */
    startAutoRefresh() {
        if (this.refreshInterval) {
            clearInterval(this.refreshInterval);
        }

        this.refreshInterval = setInterval(() => {
            this.refreshDashboard();
        }, this.options.refreshInterval);

        console.log(`تم بدء التحديث التلقائي كل ${this.options.refreshInterval / 60000} دقيقة`);
    }

    /**
     * إيقاف التحديث التلقائي
     */
    stopAutoRefresh() {
        if (this.refreshInterval) {
            clearInterval(this.refreshInterval);
            console.log('تم إيقاف التحديث التلقائي');
        }
    }

    /**
     * تبديل رؤية البطاقة
     * @param {string} cardId - معرف البطاقة
     * @param {boolean} visible - حالة الرؤية
     */
    toggleCardVisibility(cardId, visible) {
        const card = document.getElementById(cardId);
        if (card) {
            if (visible) {
                card.classList.remove('hidden');
                if (!this.state.visibleCards.includes(cardId)) {
                    this.state.visibleCards.push(cardId);
                }
            } else {
                card.classList.add('hidden');
                this.state.visibleCards = this.state.visibleCards.filter(id => id !== cardId);
            }
        }
    }

    /**
     * تهيئة الرسوم البيانية
     */
    initCharts() {
        // تهيئة رسم بياني المبيعات
        this.initSalesChart();

        // تهيئة رسم بياني توزيع المنتجات
        this.initCategoriesChart();

        console.log('تم تهيئة الرسوم البيانية');
    }

    /**
     * تهيئة رسم بياني المبيعات
     */
    initSalesChart() {
        const salesChartElement = document.getElementById('sales-chart');
        if (!salesChartElement) return;

        // إنشاء رسم بياني المبيعات باستخدام ECharts
        this.state.charts.salesChart = echarts.init(salesChartElement);

        // إعداد خيارات الرسم البياني
        const options = {
            tooltip: {
                trigger: 'axis',
                axisPointer: {
                    type: 'shadow'
                }
            },
            legend: {
                data: ['المبيعات', 'الطلبات'],
                right: 10,
                top: 0
            },
            grid: {
                left: '3%',
                right: '4%',
                bottom: '3%',
                containLabel: true
            },
            xAxis: {
                type: 'category',
                data: ['الأحد', 'الإثنين', 'الثلاثاء', 'الأربعاء', 'الخميس', 'الجمعة', 'السبت'],
                axisLabel: {
                    rotate: 0
                }
            },
            yAxis: {
                type: 'value'
            },
            series: [
                {
                    name: 'المبيعات',
                    type: 'bar',
                    data: [0, 0, 0, 0, 0, 0, 0],
                    itemStyle: {
                        color: '#3b82f6'
                    }
                },
                {
                    name: 'الطلبات',
                    type: 'line',
                    data: [0, 0, 0, 0, 0, 0, 0],
                    itemStyle: {
                        color: '#8b5cf6'
                    }
                }
            ]
        };

        // تطبيق الخيارات
        this.state.charts.salesChart.setOption(options);

        // تحديث الرسم البياني عند تغيير حجم النافذة
        window.addEventListener('resize', () => {
            if (this.state.charts.salesChart) {
                this.state.charts.salesChart.resize();
            }
        });
    }

    /**
     * تهيئة رسم بياني توزيع المنتجات
     */
    initCategoriesChart() {
        const categoriesChartElement = document.getElementById('categories-chart');
        if (!categoriesChartElement) return;

        // إنشاء رسم بياني توزيع المنتجات باستخدام ECharts
        this.state.charts.categoriesChart = echarts.init(categoriesChartElement);

        // إعداد خيارات الرسم البياني
        const options = {
            tooltip: {
                trigger: 'item',
                formatter: '{a} <br/>{b}: {c} ({d}%)'
            },
            legend: {
                orient: 'vertical',
                right: 10,
                top: 'center',
                data: ['بدون تصنيف']
            },
            series: [
                {
                    name: 'توزيع المنتجات',
                    type: 'pie',
                    radius: ['50%', '70%'],
                    avoidLabelOverlap: false,
                    itemStyle: {
                        borderRadius: 10,
                        borderColor: '#fff',
                        borderWidth: 2
                    },
                    label: {
                        show: false,
                        position: 'center'
                    },
                    emphasis: {
                        label: {
                            show: true,
                            fontSize: '16',
                            fontWeight: 'bold'
                        }
                    },
                    labelLine: {
                        show: false
                    },
                    data: [
                        { value: 100, name: 'بدون تصنيف' }
                    ]
                }
            ]
        };

        // تطبيق الخيارات
        this.state.charts.categoriesChart.setOption(options);

        // تحديث الرسم البياني عند تغيير حجم النافذة
        window.addEventListener('resize', () => {
            if (this.state.charts.categoriesChart) {
                this.state.charts.categoriesChart.resize();
            }
        });
    }

    /**
     * تحديث رسم بياني المبيعات
     */
    updateSalesChart() {
        if (!this.state.charts.salesChart || !this.state.data) return;

        const salesData = this.state.data.sales_chart || {};
        const dates = salesData.dates || [];
        const sales = salesData.sales || [];
        const orders = salesData.orders || [];

        // تحديث بيانات الرسم البياني
        this.state.charts.salesChart.setOption({
            xAxis: {
                data: dates
            },
            series: [
                {
                    name: 'المبيعات',
                    data: sales
                },
                {
                    name: 'الطلبات',
                    data: orders
                }
            ]
        });
    }

    /**
     * تحديث رسم بياني توزيع المنتجات
     */
    updateCategoriesChart() {
        if (!this.state.charts.categoriesChart || !this.state.data) return;

        const categoriesData = this.state.data.categories_chart || {};
        const categories = categoriesData.categories || [];
        const values = categoriesData.values || [];

        // تحويل البيانات إلى التنسيق المطلوب
        const chartData = categories.map((category, index) => {
            return {
                name: category,
                value: values[index] || 0
            };
        });

        // إذا لم تكن هناك بيانات، استخدم بيانات افتراضية
        if (chartData.length === 0) {
            chartData.push({ name: 'بدون تصنيف', value: 100 });
        }

        // تحديث بيانات الرسم البياني
        this.state.charts.categoriesChart.setOption({
            legend: {
                data: categories.length > 0 ? categories : ['بدون تصنيف']
            },
            series: [
                {
                    name: 'توزيع المنتجات',
                    data: chartData
                }
            ]
        });
    }

    /**
     * تحديث جدول آخر المبيعات
     */
    updateRecentSalesTable() {
        const tableBody = document.querySelector('#orders-table-container tbody');
        if (!tableBody || !this.state.data) return;

        const recentSales = this.state.data.recent_sales || [];

        // إنشاء محتوى الجدول
        let tableContent = '';

        if (recentSales.length > 0) {
            recentSales.forEach(sale => {
                let statusClass = '';
                let statusText = '';

                switch (sale.status) {
                    case 'completed':
                        statusClass = 'bg-green-100 text-green-800';
                        statusText = 'مكتمل';
                        break;
                    case 'pending':
                        statusClass = 'bg-yellow-100 text-yellow-800';
                        statusText = 'معلق';
                        break;
                    case 'cancelled':
                        statusClass = 'bg-red-100 text-red-800';
                        statusText = 'ملغي';
                        break;
                    default:
                        statusClass = 'bg-gray-100 text-gray-800';
                        statusText = sale.status;
                }

                tableContent += `
                <tr class="border-b border-gray-100 text-sm text-gray-700 hover:bg-gray-50 transition-colors duration-200">
                    <td class="py-3 px-4">${sale.invoice_number}</td>
                    <td class="py-3 px-4">${sale.customer_name}</td>
                    <td class="py-3 px-4">${this.formatNumber(sale.total)} ج.م</td>
                    <td class="py-3 px-4">
                        <span class="px-2 py-1 ${statusClass} rounded-full text-xs">${statusText}</span>
                    </td>
                    <td class="py-3 px-4">${sale.date}</td>
                </tr>
                `;
            });
        } else {
            tableContent = `
            <tr class="border-b border-gray-100 text-sm text-gray-700">
                <td class="py-4 px-4 text-center" colspan="5">لا توجد مبيعات حديثة</td>
            </tr>
            `;
        }

        // تحديث محتوى الجدول
        tableBody.innerHTML = tableContent;
    }

    /**
     * تحديث جدول المنتجات منخفضة المخزون
     */
    updateLowStockTable() {
        const tableBody = document.querySelector('#low-stock-table tbody');
        if (!tableBody || !this.state.data) return;

        const lowStockProducts = this.state.data.low_stock_products || [];

        // إنشاء محتوى الجدول
        let tableContent = '';

        if (lowStockProducts.length > 0) {
            lowStockProducts.forEach(product => {
                let statusClass = product.quantity <= 0 ? 'bg-red-100 text-red-800' : 'bg-yellow-100 text-yellow-800';
                let statusText = product.quantity <= 0 ? 'نفذت' : 'منخفضة';

                tableContent += `
                <tr class="border-b border-gray-100 text-sm text-gray-700 hover:bg-gray-50 transition-colors duration-200">
                    <td class="py-3 px-4">${product.name}</td>
                    <td class="py-3 px-4">${product.barcode || '-'}</td>
                    <td class="py-3 px-4">${product.warehouse || '-'}</td>
                    <td class="py-3 px-4">${product.quantity}</td>
                    <td class="py-3 px-4">${product.min_quantity}</td>
                    <td class="py-3 px-4">
                        <span class="px-2 py-1 ${statusClass} rounded-full text-xs">${statusText}</span>
                    </td>
                    <td class="py-3 px-4">
                        <a href="/purchases/create" class="text-primary hover:text-indigo-700 transition-colors duration-300">
                            <i class="ri-shopping-cart-2-line"></i> طلب شراء
                        </a>
                    </td>
                </tr>
                `;
            });
        } else {
            tableContent = `
            <tr class="border-b border-gray-100 text-sm text-gray-700">
                <td class="py-4 px-4 text-center" colspan="7">لا توجد منتجات منخفضة المخزون</td>
            </tr>
            `;
        }

        // تحديث محتوى الجدول
        tableBody.innerHTML = tableContent;
    }

    /**
     * تحديث جدول المنتجات الأكثر مبيعاً
     */
    updateTopProductsTable() {
        const tableBody = document.querySelector('#products-list-container tbody');
        if (!tableBody || !this.state.data) return;

        const topProducts = this.state.data.top_products || [];

        // إنشاء محتوى الجدول
        let tableContent = '';

        if (topProducts.length > 0) {
            topProducts.forEach(product => {
                tableContent += `
                <tr class="border-b border-gray-100 text-sm text-gray-700 hover:bg-gray-50 transition-colors duration-200">
                    <td class="py-3 px-4">${product.name}</td>
                    <td class="py-3 px-4">${product.category || 'بدون تصنيف'}</td>
                    <td class="py-3 px-4">${this.formatNumber(product.price)} ج.م</td>
                    <td class="py-3 px-4">${product.total_sold}</td>
                    <td class="py-3 px-4">${this.formatNumber(product.total_sales)} ج.م</td>
                </tr>
                `;
            });
        } else {
            tableContent = `
            <tr class="border-b border-gray-100 text-sm text-gray-700">
                <td class="py-4 px-4 text-center" colspan="5">لا توجد منتجات مباعة</td>
            </tr>
            `;
        }

        // تحديث محتوى الجدول
        tableBody.innerHTML = tableContent;
    }

    /**
     * تحديث فترة الرسم البياني
     * @param {string} period - الفترة الزمنية
     */
    updateChartPeriod(period) {
        // تحديث فترة الرسم البياني
        fetch(`/api/sales-chart-data?days=${period}`)
            .then(response => response.json())
            .then(data => {
                if (data && this.state.charts.salesChart) {
                    // تحديث بيانات الرسم البياني
                    this.state.charts.salesChart.setOption({
                        xAxis: {
                            data: data.dates || []
                        },
                        series: [
                            {
                                name: 'المبيعات',
                                data: data.sales || []
                            },
                            {
                                name: 'الطلبات',
                                data: data.orders || []
                            }
                        ]
                    });
                }
            })
            .catch(error => {
                console.error('خطأ في تحديث بيانات الرسم البياني:', error);
            });

        console.log(`تم تحديث فترة الرسم البياني إلى ${period} يوم`);
    }

    /**
     * تبديل نوع الرسم البياني
     */
    toggleChartType() {
        if (!this.state.charts.salesChart) return;

        // الحصول على نوع الرسم البياني الحالي
        const currentType = this.state.charts.salesChart.getOption().series[0].type;

        // تبديل نوع الرسم البياني
        const newType = currentType === 'bar' ? 'line' : 'bar';

        // تحديث نوع الرسم البياني
        this.state.charts.salesChart.setOption({
            series: [
                {
                    name: 'المبيعات',
                    type: newType
                }
            ]
        });

        // تحديث أيقونة الزر
        const chartTypeToggle = document.getElementById('chart-type-toggle');
        if (chartTypeToggle) {
            chartTypeToggle.innerHTML = newType === 'bar' ?
                '<i class="ri-line-chart-line"></i>' :
                '<i class="ri-bar-chart-2-line"></i>';
        }

        // حفظ نوع الرسم البياني في الحالة
        this.state.chartType = newType;

        console.log(`تم تبديل نوع الرسم البياني إلى ${newType}`);
    }

    /**
     * تطبيق وضع الدارك مود - تم إلغاء هذه الوظيفة
     */
    applyDarkMode() {
        // تم إلغاء وضع الدارك مود من البرنامج
        console.log('تم إلغاء وضع الدارك مود من البرنامج');
    }

    /**
     * إلغاء وضع الدارك مود - تم إلغاء هذه الوظيفة
     */
    removeDarkMode() {
        // تم إلغاء وضع الدارك مود من البرنامج
        console.log('تم إلغاء وضع الدارك مود من البرنامج');
    }

    /**
     * تحميل إعدادات لوحة التحكم
     */
    loadSettings() {
        if (this.options.saveSettings) {
            const savedSettings = localStorage.getItem(this.options.settingsKey);
            if (savedSettings) {
                try {
                    const settings = JSON.parse(savedSettings);

                    // تطبيق الإعدادات المحفوظة
                    if (settings.visibleCards) {
                        this.state.visibleCards = settings.visibleCards;

                        // تحديث حالة مفاتيح التبديل
                        document.querySelectorAll('.dashboard-toggle').forEach(toggle => {
                            const targetId = toggle.dataset.target;
                            toggle.checked = settings.visibleCards.includes(targetId);
                            this.toggleCardVisibility(targetId, toggle.checked);
                        });
                    }

                    if (settings.chartPeriod) {
                        const chartPeriodSelect = document.getElementById('chart-period');
                        if (chartPeriodSelect) {
                            chartPeriodSelect.value = settings.chartPeriod;
                        }
                    }

                    if (settings.chartType) {
                        this.state.chartType = settings.chartType;
                    }

                    console.log('تم تحميل إعدادات لوحة التحكم');
                } catch (error) {
                    console.error('خطأ في تحليل إعدادات لوحة التحكم:', error);
                }
            }
        }
    }

    /**
     * حفظ إعدادات لوحة التحكم
     */
    saveSettings() {
        if (this.options.saveSettings) {
            const settings = {
                visibleCards: this.state.visibleCards,
                chartPeriod: document.getElementById('chart-period')?.value,
                chartType: this.state.chartType
            };

            localStorage.setItem(this.options.settingsKey, JSON.stringify(settings));
            console.log('تم حفظ إعدادات لوحة التحكم');
        }
    }

    /**
     * إعادة تعيين إعدادات لوحة التحكم
     */
    resetSettings() {
        // إعادة تعيين حالة مفاتيح التبديل
        document.querySelectorAll('.dashboard-toggle').forEach(toggle => {
            toggle.checked = true;
            this.toggleCardVisibility(toggle.dataset.target, true);
        });

        // إعادة تعيين فترة الرسم البياني
        const chartPeriodSelect = document.getElementById('chart-period');
        if (chartPeriodSelect) {
            chartPeriodSelect.value = '7';
            this.updateChartPeriod('7');
        }

        // إعادة تعيين نوع الرسم البياني
        this.state.chartType = 'line';

        // حفظ الإعدادات
        this.saveSettings();

        console.log('تم إعادة تعيين إعدادات لوحة التحكم');
    }
}

// تصدير الكلاس للاستخدام العالمي
window.DashboardManager = DashboardManager;

// تهيئة مدير لوحة التحكم عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', () => {
    if (document.querySelector('.dashboard-title')) {
        window.dashboardManager = new DashboardManager({
            darkMode: document.documentElement.classList.contains('dark-mode')
        });
    }
});
