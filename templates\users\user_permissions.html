<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة صلاحيات المستخدم - نظام إدارة المبيعات</title>
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com/3.4.16"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#6366F1',
                        secondary: '#10b981',
                        success: '#22c55e',
                        warning: '#f59e0b',
                        danger: '#ef4444',
                        info: '#3b82f6'
                    },
                    container: {
                        center: true,
                        padding: {
                            DEFAULT: '1rem',
                            sm: '2rem',
                            lg: '4rem',
                            xl: '5rem',
                            '2xl': '6rem',
                        },
                    },
                    borderRadius: {
                        'none': '0px',
                        'sm': '4px',
                        DEFAULT: '8px',
                        'md': '12px',
                        'lg': '16px',
                        'xl': '20px',
                        '2xl': '24px',
                        '3xl': '32px',
                        'full': '9999px'
                    }
                }
            }
        }
    </script>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@400;500;700&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Pacifico&display=swap" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/remixicon@3.5.0/fonts/remixicon.css" rel="stylesheet">
    <style>
        :where([class^="ri-"])::before { content: "\f3c2"; }
        body {
            font-family: 'Tajawal', sans-serif;
            background-color: #f9fafb;
        }
        .scrollbar-hide::-webkit-scrollbar {
            display: none;
        }
        .scrollbar-hide {
            -ms-overflow-style: none;
            scrollbar-width: none;
        }
        .bg-pattern {
            background-image: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%236366F1' fill-opacity='0.05'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
        }
        .glass-effect {
            backdrop-filter: blur(8px);
            background: rgba(255, 255, 255, 0.8);
            border: 1px solid rgba(255, 255, 255, 0.3);
            box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.07);
        }
        .pulse-animation {
            animation: pulse 2s infinite;
        }
        @keyframes pulse {
            0% {
                box-shadow: 0 0 0 0 rgba(99, 102, 241, 0.4);
            }
            70% {
                box-shadow: 0 0 0 10px rgba(99, 102, 241, 0);
            }
            100% {
                box-shadow: 0 0 0 0 rgba(99, 102, 241, 0);
            }
        }
        .card-hover {
            transition: all 0.3s ease;
        }
        .card-hover:hover {
            transform: translateY(-5px);
        }
        .permission-card {
            transition: all 0.3s ease;
        }
        .permission-card:hover {
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
        }
        .switch {
            position: relative;
            display: inline-block;
            width: 48px;
            height: 24px;
        }
        .switch input {
            opacity: 0;
            width: 0;
            height: 0;
        }
        .slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: #ccc;
            transition: .4s;
            border-radius: 24px;
        }
        .slider:before {
            position: absolute;
            content: "";
            height: 18px;
            width: 18px;
            left: 3px;
            bottom: 3px;
            background-color: white;
            transition: .4s;
            border-radius: 50%;
        }
        input:checked + .slider {
            background-color: #6366F1;
        }
        input:focus + .slider {
            box-shadow: 0 0 1px #6366F1;
        }
        input:checked + .slider:before {
            transform: translateX(24px);
        }
    </style>
</head>
<body class="bg-pattern">
    <div class="flex h-screen overflow-hidden">
        <!-- Sidebar -->
        {% include 'partials/sidebar.html' %}
        
        <!-- Main Content -->
        <div class="flex-1 flex flex-col overflow-hidden">
            <!-- Top Navigation -->
            {% include 'partials/topnav.html' %}
            
            <!-- Content Area -->
            <main class="flex-1 overflow-y-auto p-6">
                <!-- Page Header -->
                <div class="mb-6 flex flex-wrap items-center justify-between">
                    <div>
                        <h1 class="text-2xl font-bold text-gray-800">إدارة صلاحيات المستخدم</h1>
                        <p class="text-gray-600">تعديل صلاحيات المستخدم: {{ user.full_name or user.username }}</p>
                    </div>
                    <a href="{{ url_for('users.index') }}" class="px-4 py-2 bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 transition-all whitespace-nowrap">
                        <i class="ri-arrow-right-line"></i>
                        العودة للمستخدمين
                    </a>
                </div>
                
                <!-- User Info Card -->
                <div class="bg-white rounded-xl shadow-sm p-6 border border-gray-100 mb-6">
                    <div class="flex items-center">
                        <div class="flex-shrink-0 h-16 w-16 flex items-center justify-center rounded-full {{ 'bg-yellow-100 text-yellow-500' if user.role == 'admin' else 'bg-blue-100 text-blue-500' }}">
                            <span class="text-2xl font-medium">{{ user.username[0]|upper }}</span>
                        </div>
                        <div class="mr-4">
                            <h2 class="text-xl font-bold text-gray-800">{{ user.full_name or user.username }}</h2>
                            <div class="flex flex-wrap gap-3 mt-2">
                                <div class="text-sm text-gray-600">
                                    <i class="ri-mail-line"></i> {{ user.email }}
                                </div>
                                <div class="text-sm text-gray-600">
                                    <i class="ri-user-settings-line"></i> {{ 'مدير' if user.role == 'admin' else 'موظف' }}
                                </div>
                                <div class="text-sm text-gray-600">
                                    <i class="ri-calendar-line"></i> تاريخ التسجيل: {{ user.created_at.strftime('%Y-%m-%d') }}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Permissions Form -->
                <form method="POST" action="{{ url_for('users.user_permissions', id=user.id) }}">
                    <div class="bg-white rounded-xl shadow-sm overflow-hidden mb-6">
                        <div class="p-6 border-b border-gray-100">
                            <h3 class="text-lg font-bold text-gray-800 mb-2">إدارة الصلاحيات</h3>
                            <p class="text-gray-600">حدد الصلاحيات التي تريد منحها للمستخدم</p>
                        </div>
                        
                        <!-- Admin Override Notice -->
                        {% if user.role == 'admin' %}
                        <div class="p-4 bg-yellow-50 border-b border-yellow-100">
                            <div class="flex items-center">
                                <div class="flex-shrink-0">
                                    <i class="ri-information-line text-yellow-500 text-xl"></i>
                                </div>
                                <div class="mr-3">
                                    <h3 class="text-sm font-medium text-yellow-800">ملاحظة</h3>
                                    <div class="text-sm text-yellow-700 mt-1">
                                        هذا المستخدم لديه صلاحية "مدير" وبالتالي يملك جميع الصلاحيات تلقائياً بغض النظر عن الإعدادات أدناه.
                                    </div>
                                </div>
                            </div>
                        </div>
                        {% endif %}
                        
                        <!-- Permissions Grid -->
                        <div class="p-6 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                            {% for module_code, module_data in permissions_by_module.items() %}
                            <div id="module_{{ module_code }}" class="permission-card bg-white rounded-lg border border-gray-200 overflow-hidden">
                                <div class="bg-gray-50 p-4 border-b border-gray-200 flex justify-between items-center">
                                    <h4 class="font-bold text-gray-800">{{ module_data.name }}</h4>
                                    {% if user.role != 'admin' %}
                                    <div class="flex space-x-2 space-x-reverse">
                                        <button type="button" onclick="toggleModulePermissions('{{ module_code }}', true)" class="text-xs px-2 py-1 bg-indigo-100 text-indigo-700 rounded hover:bg-indigo-200 transition-all">
                                            تحديد الكل
                                        </button>
                                        <button type="button" onclick="toggleModulePermissions('{{ module_code }}', false)" class="text-xs px-2 py-1 bg-gray-100 text-gray-700 rounded hover:bg-gray-200 transition-all">
                                            إلغاء الكل
                                        </button>
                                    </div>
                                    {% endif %}
                                </div>
                                <div class="p-4 space-y-3">
                                    {% for permission in module_data.permissions %}
                                    <div class="flex items-center justify-between hover:bg-gray-50 p-2 rounded-lg transition-all">
                                        <div>
                                            <span class="text-sm font-medium text-gray-700">{{ permission.action_name }}</span>
                                            <p class="text-xs text-gray-500">{{ permission.description }}</p>
                                        </div>
                                        <label class="switch">
                                            <input type="checkbox" name="permission_{{ permission.id }}" 
                                                id="permission_{{ permission.id }}"
                                                data-module="{{ module_code }}"
                                                {% if user.role == 'admin' or user_permissions.get(permission.name, False) %}checked{% endif %}
                                                {% if user.role == 'admin' %}disabled{% endif %}>
                                            <span class="slider"></span>
                                        </label>
                                    </div>
                                    {% endfor %}
                                </div>
                            </div>
                            {% endfor %}
                        </div>
                    </div>
                    
                    <!-- Submit Buttons -->
                    <div class="flex justify-end space-x-2 space-x-reverse">
                        <button type="submit" class="px-6 py-2.5 bg-primary text-white rounded-md hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary transition-all whitespace-nowrap">
                            <i class="ri-save-line"></i>
                            حفظ الصلاحيات
                        </button>
                        <a href="{{ url_for('users.index') }}" class="px-6 py-2.5 bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 transition-all whitespace-nowrap">
                            إلغاء
                        </a>
                    </div>
                </form>
            </main>
        </div>
    </div>
    
    <script>
        // تحديد/إلغاء تحديد جميع الصلاحيات لوحدة معينة
        function toggleModulePermissions(moduleCode, checked) {
            const checkboxes = document.querySelectorAll(`input[data-module="${moduleCode}"]`);
            checkboxes.forEach(checkbox => {
                if (!checkbox.disabled) {
                    checkbox.checked = checked;
                }
            });
        }
        
        // تحديد/إلغاء تحديد جميع الصلاحيات
        function toggleAllPermissions(checked) {
            const checkboxes = document.querySelectorAll('input[type="checkbox"]');
            checkboxes.forEach(checkbox => {
                if (!checkbox.disabled) {
                    checkbox.checked = checked;
                }
            });
        }
        
        // إضافة أزرار تحديد/إلغاء تحديد الكل في أعلى الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            const permissionsHeader = document.querySelector('.p-6.border-b.border-gray-100');
            if (permissionsHeader && '{{ user.role }}' !== 'admin') {
                const buttonsDiv = document.createElement('div');
                buttonsDiv.className = 'flex items-center gap-2 mt-2';
                buttonsDiv.innerHTML = `
                    <button type="button" onclick="toggleAllPermissions(true)" class="px-3 py-1.5 bg-indigo-600 text-white rounded-md hover:bg-indigo-700 transition-all text-sm flex items-center gap-1">
                        <i class="ri-checkbox-multiple-line"></i>
                        تحديد جميع الصلاحيات
                    </button>
                    <button type="button" onclick="toggleAllPermissions(false)" class="px-3 py-1.5 bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200 transition-all text-sm flex items-center gap-1">
                        <i class="ri-checkbox-multiple-blank-line"></i>
                        إلغاء تحديد الكل
                    </button>
                `;
                permissionsHeader.appendChild(buttonsDiv);
            }
        });
    </script>
</body>
</html>
