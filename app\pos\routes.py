"""
Nobara POS System - POS Routes
نظام نوبارا لنقاط البيع - مسارات نقطة البيع
"""

from flask import render_template, request, jsonify, session, redirect, url_for, flash
from flask_login import login_required, current_user
from app.pos import bp
from app.models import Product, Customer, Sale, SaleItem, SalePayment, Warehouse, InventoryItem, db
from datetime import datetime
import logging
import uuid

logger = logging.getLogger(__name__)

@bp.route('/')
@login_required
def index():
    """صفحة نقطة البيع الرئيسية"""
    try:
        # جلب المنتجات النشطة
        products = Product.query.filter_by(is_active=True).limit(50).all()
        
        # جلب العملاء النشطين
        customers = Customer.query.filter_by(is_active=True).limit(100).all()
        
        # جلب المخازن النشطة
        warehouses = Warehouse.query.filter_by(is_active=True).all()
        
        # المخزن الافتراضي للمستخدم
        default_warehouse = current_user.warehouse or warehouses[0] if warehouses else None
        
        return render_template('pos/index.html', 
                             products=products,
                             customers=customers,
                             warehouses=warehouses,
                             default_warehouse=default_warehouse)
        
    except Exception as e:
        logger.error(f'Error loading POS: {e}')
        flash('حدث خطأ أثناء تحميل نقطة البيع', 'error')
        return redirect(url_for('main.dashboard'))

@bp.route('/api/products/search')
@login_required
def search_products():
    """البحث في المنتجات"""
    try:
        query = request.args.get('q', '').strip()
        warehouse_id = request.args.get('warehouse_id', type=int)
        
        if not query:
            return jsonify([])
        
        # البحث في المنتجات
        products_query = Product.query.filter(
            Product.is_active == True,
            db.or_(
                Product.name.contains(query),
                Product.sku.contains(query),
                Product.barcode.contains(query)
            )
        )
        
        products = products_query.limit(20).all()
        
        result = []
        for product in products:
            # جلب المخزون للمخزن المحدد
            stock = 0
            if warehouse_id:
                inventory_item = InventoryItem.query.filter_by(
                    product_id=product.id,
                    warehouse_id=warehouse_id
                ).first()
                stock = inventory_item.available_quantity if inventory_item else 0
            else:
                stock = product.total_stock
            
            result.append({
                'id': product.id,
                'name': product.name,
                'sku': product.sku,
                'barcode': product.barcode,
                'selling_price': float(product.selling_price),
                'cost_price': float(product.cost_price),
                'stock': stock,
                'unit': product.unit,
                'tax_rate': float(product.tax_rate),
                'image_url': product.image_url,
                'track_inventory': product.track_inventory
            })
        
        return jsonify(result)
        
    except Exception as e:
        logger.error(f'Error searching products: {e}')
        return jsonify({'error': str(e)}), 500

@bp.route('/api/customers/search')
@login_required
def search_customers():
    """البحث في العملاء"""
    try:
        query = request.args.get('q', '').strip()
        
        if not query:
            return jsonify([])
        
        customers = Customer.query.filter(
            Customer.is_active == True,
            db.or_(
                Customer.name.contains(query),
                Customer.phone.contains(query),
                Customer.email.contains(query)
            )
        ).limit(20).all()
        
        result = []
        for customer in customers:
            result.append({
                'id': customer.id,
                'name': customer.name,
                'phone': customer.phone,
                'email': customer.email,
                'credit_limit': float(customer.credit_limit),
                'outstanding_balance': float(customer.outstanding_balance)
            })
        
        return jsonify(result)
        
    except Exception as e:
        logger.error(f'Error searching customers: {e}')
        return jsonify({'error': str(e)}), 500

@bp.route('/api/sale/create', methods=['POST'])
@login_required
def create_sale():
    """إنشاء فاتورة مبيعات جديدة"""
    try:
        data = request.get_json()
        
        # التحقق من البيانات المطلوبة
        items = data.get('items', [])
        if not items:
            return jsonify({'success': False, 'message': 'لا توجد منتجات في الفاتورة'})
        
        customer_id = data.get('customer_id')
        warehouse_id = data.get('warehouse_id')
        payment_method = data.get('payment_method', 'cash')
        discount_amount = float(data.get('discount_amount', 0))
        notes = data.get('notes', '')
        
        # التحقق من المخزن
        warehouse = Warehouse.query.get(warehouse_id)
        if not warehouse:
            return jsonify({'success': False, 'message': 'المخزن غير موجود'})
        
        # إنشاء رقم الفاتورة
        sale_number = f"INV-{datetime.now().strftime('%Y%m%d')}-{str(uuid.uuid4())[:8].upper()}"
        
        # إنشاء الفاتورة
        sale = Sale(
            sale_number=sale_number,
            customer_id=customer_id,
            user_id=current_user.id,
            warehouse_id=warehouse_id,
            payment_method=payment_method,
            discount_amount=discount_amount,
            notes=notes,
            status='pending'
        )
        
        db.session.add(sale)
        db.session.flush()  # للحصول على ID الفاتورة
        
        subtotal = 0
        total_tax = 0
        
        # إضافة عناصر الفاتورة
        for item_data in items:
            product_id = item_data.get('product_id')
            quantity = int(item_data.get('quantity', 1))
            unit_price = float(item_data.get('unit_price', 0))
            item_discount = float(item_data.get('discount_amount', 0))
            
            # التحقق من المنتج
            product = Product.query.get(product_id)
            if not product:
                db.session.rollback()
                return jsonify({'success': False, 'message': f'المنتج غير موجود'})
            
            # التحقق من المخزون
            if product.track_inventory:
                inventory_item = InventoryItem.query.filter_by(
                    product_id=product_id,
                    warehouse_id=warehouse_id
                ).first()
                
                if not inventory_item or inventory_item.available_quantity < quantity:
                    db.session.rollback()
                    return jsonify({'success': False, 'message': f'مخزون غير كافي للمنتج: {product.name}'})
            
            # حساب المبالغ
            item_subtotal = (quantity * unit_price) - item_discount
            item_tax = item_subtotal * product.tax_rate
            item_total = item_subtotal + item_tax
            
            # إنشاء عنصر الفاتورة
            sale_item = SaleItem(
                sale_id=sale.id,
                product_id=product_id,
                quantity=quantity,
                unit_price=unit_price,
                discount_amount=item_discount,
                tax_rate=product.tax_rate,
                total_amount=item_total
            )
            
            db.session.add(sale_item)
            
            subtotal += item_subtotal
            total_tax += item_tax
        
        # تحديث مبالغ الفاتورة
        sale.subtotal = subtotal
        sale.tax_amount = total_tax
        sale.total_amount = subtotal + total_tax - discount_amount
        
        # إنشاء دفعة إذا كانت نقدية
        if payment_method == 'cash':
            payment = SalePayment(
                sale_id=sale.id,
                amount=sale.total_amount,
                payment_method='cash'
            )
            db.session.add(payment)
            sale.paid_amount = sale.total_amount
            sale.payment_status = 'paid'
        
        # تحديث حالة الفاتورة
        sale.status = 'completed'
        sale.update_payment_status()
        
        # تحديث المخزون
        for item_data in items:
            product_id = item_data.get('product_id')
            quantity = int(item_data.get('quantity', 1))
            
            product = Product.query.get(product_id)
            if product.track_inventory:
                inventory_item = InventoryItem.query.filter_by(
                    product_id=product_id,
                    warehouse_id=warehouse_id
                ).first()
                
                if inventory_item:
                    inventory_item.quantity -= quantity
                    inventory_item.updated_at = datetime.utcnow()
        
        db.session.commit()
        
        logger.info(f'Sale created: {sale_number} by user {current_user.username}')
        
        return jsonify({
            'success': True,
            'message': 'تم إنشاء الفاتورة بنجاح',
            'sale_id': sale.id,
            'sale_number': sale_number,
            'total_amount': float(sale.total_amount)
        })
        
    except Exception as e:
        db.session.rollback()
        logger.error(f'Error creating sale: {e}')
        return jsonify({'success': False, 'message': 'حدث خطأ أثناء إنشاء الفاتورة'})

@bp.route('/api/product/<int:product_id>')
@login_required
def get_product_details():
    """جلب تفاصيل منتج"""
    try:
        product_id = request.view_args['product_id']
        warehouse_id = request.args.get('warehouse_id', type=int)
        
        product = Product.query.get_or_404(product_id)
        
        # جلب المخزون
        stock = 0
        if warehouse_id:
            inventory_item = InventoryItem.query.filter_by(
                product_id=product_id,
                warehouse_id=warehouse_id
            ).first()
            stock = inventory_item.available_quantity if inventory_item else 0
        else:
            stock = product.total_stock
        
        return jsonify({
            'id': product.id,
            'name': product.name,
            'sku': product.sku,
            'barcode': product.barcode,
            'selling_price': float(product.selling_price),
            'cost_price': float(product.cost_price),
            'stock': stock,
            'unit': product.unit,
            'tax_rate': float(product.tax_rate),
            'image_url': product.image_url,
            'track_inventory': product.track_inventory,
            'min_price': float(product.min_price) if product.min_price else None
        })
        
    except Exception as e:
        logger.error(f'Error getting product details: {e}')
        return jsonify({'error': str(e)}), 500

@bp.route('/receipt/<int:sale_id>')
@login_required
def print_receipt(sale_id):
    """طباعة الإيصال"""
    try:
        sale = Sale.query.get_or_404(sale_id)
        
        return render_template('pos/receipt.html', sale=sale)
        
    except Exception as e:
        logger.error(f'Error loading receipt: {e}')
        flash('حدث خطأ أثناء تحميل الإيصال', 'error')
        return redirect(url_for('pos.index'))

@bp.route('/invoice/<int:sale_id>')
@login_required
def print_invoice(sale_id):
    """طباعة الفاتورة"""
    try:
        sale = Sale.query.get_or_404(sale_id)
        
        return render_template('pos/invoice.html', sale=sale)
        
    except Exception as e:
        logger.error(f'Error loading invoice: {e}')
        flash('حدث خطأ أثناء تحميل الفاتورة', 'error')
        return redirect(url_for('pos.index'))
