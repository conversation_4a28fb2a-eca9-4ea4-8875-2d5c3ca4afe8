from flask import Blueprint, render_template, request, jsonify, flash, redirect, url_for
from flask_login import login_required, current_user
from models import CashRegister, CashTransaction, Shift, User, Notification, DailyCashClosure
from app import db
from sqlalchemy import func, desc
from datetime import datetime, timedelta

cash_blueprint = Blueprint('cash', __name__)

@cash_blueprint.route('/cash')
@login_required
def cash_index():
    """صفحة الخزينة الرئيسية"""
    # التحقق من صلاحيات المستخدم
    if not current_user.has_permission('cash', 'view'):
        flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'error')
        return redirect(url_for('dashboard.index'))
    
    # الحصول على جميع الخزائن
    cash_registers = CashRegister.query.all()
    
    # الحصول على إجمالي الرصيد في جميع الخزائن
    total_balance = sum(register.current_balance for register in cash_registers)
    
    # الحصول على آخر 10 معاملات
    recent_transactions = CashTransaction.query.order_by(desc(CashTransaction.created_at)).limit(10).all()
    
    # الحصول على الشيفتات المفتوحة
    open_shifts = Shift.query.filter_by(status='open').all()
    
    return render_template(
        'cash/index.html',
        cash_registers=cash_registers,
        total_balance=total_balance,
        recent_transactions=recent_transactions,
        open_shifts=open_shifts
    )

@cash_blueprint.route('/cash/registers')
@login_required
def cash_registers():
    """صفحة إدارة الخزائن"""
    # التحقق من صلاحيات المستخدم
    if not current_user.has_permission('cash', 'view'):
        flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'error')
        return redirect(url_for('dashboard.index'))
    
    # الحصول على جميع الخزائن
    cash_registers = CashRegister.query.all()
    
    return render_template(
        'cash/registers.html',
        cash_registers=cash_registers
    )

@cash_blueprint.route('/cash/registers/add', methods=['GET', 'POST'])
@login_required
def add_cash_register():
    """إضافة خزينة جديدة"""
    # التحقق من صلاحيات المستخدم
    if not current_user.has_permission('cash', 'add'):
        flash('ليس لديك صلاحية لإضافة خزينة جديدة', 'error')
        return redirect(url_for('cash.cash_registers'))
    
    if request.method == 'POST':
        name = request.form.get('name')
        description = request.form.get('description')
        is_default = request.form.get('is_default') == 'on'
        current_balance = float(request.form.get('current_balance', 0))
        
        # التحقق من البيانات
        if not name:
            flash('يرجى إدخال اسم الخزينة', 'error')
            return redirect(url_for('cash.add_cash_register'))
        
        # إذا كانت الخزينة الافتراضية، قم بإلغاء تعيين الخزائن الأخرى كافتراضية
        if is_default:
            default_registers = CashRegister.query.filter_by(is_default=True).all()
            for register in default_registers:
                register.is_default = False
        
        # إنشاء خزينة جديدة
        cash_register = CashRegister(
            name=name,
            description=description,
            is_default=is_default,
            current_balance=current_balance
        )
        
        db.session.add(cash_register)
        db.session.commit()
        
        flash('تم إضافة الخزينة بنجاح', 'success')
        return redirect(url_for('cash.cash_registers'))
    
    return render_template('cash/add_register.html')

@cash_blueprint.route('/cash/registers/<int:register_id>/edit', methods=['GET', 'POST'])
@login_required
def edit_cash_register(register_id):
    """تعديل خزينة"""
    # التحقق من صلاحيات المستخدم
    if not current_user.has_permission('cash', 'edit'):
        flash('ليس لديك صلاحية لتعديل الخزينة', 'error')
        return redirect(url_for('cash.cash_registers'))
    
    # الحصول على الخزينة
    cash_register = CashRegister.query.get_or_404(register_id)
    
    if request.method == 'POST':
        name = request.form.get('name')
        description = request.form.get('description')
        is_default = request.form.get('is_default') == 'on'
        is_active = request.form.get('is_active') == 'on'
        
        # التحقق من البيانات
        if not name:
            flash('يرجى إدخال اسم الخزينة', 'error')
            return redirect(url_for('cash.edit_cash_register', register_id=register_id))
        
        # إذا كانت الخزينة الافتراضية، قم بإلغاء تعيين الخزائن الأخرى كافتراضية
        if is_default and not cash_register.is_default:
            default_registers = CashRegister.query.filter_by(is_default=True).all()
            for register in default_registers:
                register.is_default = False
        
        # تحديث الخزينة
        cash_register.name = name
        cash_register.description = description
        cash_register.is_default = is_default
        cash_register.is_active = is_active
        
        db.session.commit()
        
        flash('تم تحديث الخزينة بنجاح', 'success')
        return redirect(url_for('cash.cash_registers'))
    
    return render_template('cash/edit_register.html', cash_register=cash_register)

@cash_blueprint.route('/cash/registers/<int:register_id>/delete', methods=['POST'])
@login_required
def delete_cash_register(register_id):
    """حذف خزينة"""
    # التحقق من صلاحيات المستخدم
    if not current_user.has_permission('cash', 'delete'):
        flash('ليس لديك صلاحية لحذف الخزينة', 'error')
        return redirect(url_for('cash.cash_registers'))
    
    # الحصول على الخزينة
    cash_register = CashRegister.query.get_or_404(register_id)
    
    # التحقق من عدم وجود معاملات أو شيفتات مرتبطة بالخزينة
    if cash_register.transactions or cash_register.shifts:
        flash('لا يمكن حذف الخزينة لأنها تحتوي على معاملات أو شيفتات', 'error')
        return redirect(url_for('cash.cash_registers'))
    
    # حذف الخزينة
    db.session.delete(cash_register)
    db.session.commit()
    
    flash('تم حذف الخزينة بنجاح', 'success')
    return redirect(url_for('cash.cash_registers'))

@cash_blueprint.route('/cash/transactions')
@login_required
def transactions():
    """صفحة المعاملات"""
    # التحقق من صلاحيات المستخدم
    if not current_user.has_permission('cash', 'view'):
        flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'error')
        return redirect(url_for('dashboard.index'))
    
    # الحصول على جميع الخزائن
    cash_registers = CashRegister.query.all()
    
    # الحصول على المعاملات
    transactions_query = CashTransaction.query
    
    # تطبيق الفلاتر
    register_id = request.args.get('register_id')
    transaction_type = request.args.get('type')
    date_from = request.args.get('date_from')
    date_to = request.args.get('date_to')
    
    if register_id:
        transactions_query = transactions_query.filter_by(cash_register_id=register_id)
    
    if transaction_type:
        transactions_query = transactions_query.filter_by(transaction_type=transaction_type)
    
    if date_from:
        date_from = datetime.strptime(date_from, '%Y-%m-%d')
        transactions_query = transactions_query.filter(CashTransaction.created_at >= date_from)
    
    if date_to:
        date_to = datetime.strptime(date_to, '%Y-%m-%d')
        date_to = date_to + timedelta(days=1)  # لتضمين اليوم المحدد
        transactions_query = transactions_query.filter(CashTransaction.created_at < date_to)
    
    # ترتيب المعاملات
    transactions = transactions_query.order_by(desc(CashTransaction.created_at)).all()
    
    return render_template(
        'cash/transactions.html',
        transactions=transactions,
        cash_registers=cash_registers,
        selected_register=register_id,
        selected_type=transaction_type,
        date_from=date_from.strftime('%Y-%m-%d') if date_from else '',
        date_to=date_to.strftime('%Y-%m-%d') if date_to and isinstance(date_to, datetime) else ''
    )

@cash_blueprint.route('/cash/transactions/add', methods=['GET', 'POST'])
@login_required
def add_transaction():
    """إضافة معاملة جديدة"""
    # التحقق من صلاحيات المستخدم
    if not current_user.has_permission('cash', 'add'):
        flash('ليس لديك صلاحية لإضافة معاملة جديدة', 'error')
        return redirect(url_for('cash.transactions'))
    
    # الحصول على جميع الخزائن
    cash_registers = CashRegister.query.filter_by(is_active=True).all()
    
    if request.method == 'POST':
        register_id = request.form.get('register_id')
        transaction_type = request.form.get('transaction_type')
        amount = float(request.form.get('amount', 0))
        notes = request.form.get('notes')
        
        # التحقق من البيانات
        if not register_id or not transaction_type or amount <= 0:
            flash('يرجى إدخال جميع البيانات المطلوبة', 'error')
            return redirect(url_for('cash.add_transaction'))
        
        # الحصول على الخزينة
        cash_register = CashRegister.query.get_or_404(register_id)
        
        # حساب الرصيد الجديد
        previous_balance = cash_register.current_balance
        if transaction_type == 'deposit':
            new_balance = previous_balance + amount
        elif transaction_type == 'withdraw':
            if amount > previous_balance:
                flash('المبلغ المطلوب سحبه أكبر من الرصيد الحالي', 'error')
                return redirect(url_for('cash.add_transaction'))
            new_balance = previous_balance - amount
        else:
            flash('نوع المعاملة غير صالح', 'error')
            return redirect(url_for('cash.add_transaction'))
        
        # إنشاء معاملة جديدة
        transaction = CashTransaction(
            cash_register_id=register_id,
            transaction_type=transaction_type,
            amount=amount,
            previous_balance=previous_balance,
            new_balance=new_balance,
            notes=notes,
            created_by=current_user.id
        )
        
        # تحديث رصيد الخزينة
        cash_register.current_balance = new_balance
        
        db.session.add(transaction)
        db.session.commit()
        
        flash('تم إضافة المعاملة بنجاح', 'success')
        return redirect(url_for('cash.transactions'))
    
    return render_template('cash/add_transaction.html', cash_registers=cash_registers)

@cash_blueprint.route('/cash/daily-closure', methods=['GET', 'POST'])
@login_required
def daily_closure():
    """صفحة الإغلاق اليومي للخزينة"""
    # التحقق من صلاحيات المستخدم
    if not current_user.has_permission('cash', 'edit'):
        flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'error')
        return redirect(url_for('cash.cash_index'))
    
    # التحقق من وجود إغلاق مفتوح
    today = datetime.utcnow().date()
    open_closure = DailyCashClosure.query.filter_by(
        closure_date=today,
        status='pending'
    ).first()
    
    if request.method == 'POST':
        cash_register_id = request.form.get('cash_register_id')
        opening_balance = float(request.form.get('opening_balance', 0))
        notes = request.form.get('notes')
        
        # التحقق من البيانات
        if not cash_register_id:
            flash('يرجى اختيار الخزينة', 'error')
            return redirect(url_for('cash.daily_closure'))
        
        # التحقق من عدم وجود إغلاق مفتوح لنفس الخزينة
        existing_closure = DailyCashClosure.query.filter_by(
            cash_register_id=cash_register_id,
            closure_date=today,
            status='pending'
        ).first()
        
        if existing_closure:
            flash('يوجد إغلاق مفتوح بالفعل لهذه الخزينة', 'error')
            return redirect(url_for('cash.daily_closure'))
        
        # إنشاء إغلاق جديد
        closure = DailyCashClosure(
            cash_register_id=cash_register_id,
            user_id=current_user.id,
            closure_date=today,
            opening_balance=opening_balance,
            notes=notes
        )
        
        db.session.add(closure)
        db.session.commit()
        
        flash('تم بدء عملية الإغلاق اليومي بنجاح', 'success')
        return redirect(url_for('cash.closure_details', closure_id=closure.id))
    
    # الحصول على الخزائن النشطة
    cash_registers = CashRegister.query.filter_by(is_active=True).all()
    
    # الحصول على آخر إغلاق لكل خزينة
    last_closures = {}
    for register in cash_registers:
        last_closure = DailyCashClosure.query.filter_by(
            cash_register_id=register.id,
            status='completed'
        ).order_by(DailyCashClosure.closure_date.desc()).first()
        
        if last_closure:
            last_closures[register.id] = last_closure.closing_balance
        else:
            last_closures[register.id] = 0
    
    return render_template(
        'cash/daily_closure.html',
        cash_registers=cash_registers,
        last_closures=last_closures,
        open_closure=open_closure
    )

@cash_blueprint.route('/cash/closure/<int:closure_id>')
@login_required
def closure_details(closure_id):
    """تفاصيل الإغلاق اليومي"""
    # التحقق من صلاحيات المستخدم
    if not current_user.has_permission('cash', 'view'):
        flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'error')
        return redirect(url_for('cash.cash_index'))
    
    # الحصول على الإغلاق
    closure = DailyCashClosure.query.get_or_404(closure_id)
    
    # حساب الإجماليات إذا كان الإغلاق مفتوحاً
    if closure.status == 'pending':
        closure.calculate_daily_totals()
        db.session.commit()
    
    # الحصول على المعاملات المرتبطة بالإغلاق
    transactions = CashTransaction.query.filter(
        CashTransaction.cash_register_id == closure.cash_register_id,
        CashTransaction.created_at.between(
            datetime.combine(closure.closure_date, datetime.min.time()),
            datetime.combine(closure.closure_date, datetime.max.time())
        )
    ).order_by(desc(CashTransaction.created_at)).all()
    
    return render_template(
        'cash/closure_details.html',
        closure=closure,
        transactions=transactions
    )

@cash_blueprint.route('/cash/closure/<int:closure_id>/complete', methods=['GET', 'POST'])
@login_required
def complete_closure(closure_id):
    """إكمال عملية الإغلاق اليومي"""
    # التحقق من صلاحيات المستخدم
    if not current_user.has_permission('cash', 'edit'):
        flash('ليس لديك صلاحية لإكمال عملية الإغلاق', 'error')
        return redirect(url_for('cash.cash_index'))
    
    # الحصول على الإغلاق
    closure = DailyCashClosure.query.get_or_404(closure_id)
    
    # التحقق من أن الإغلاق مفتوح
    if closure.status != 'pending':
        flash('تم إغلاق الخزينة بالفعل', 'error')
        return redirect(url_for('cash.closure_details', closure_id=closure.id))
    
    if request.method == 'POST':
        closing_balance = float(request.form.get('closing_balance', 0))
        notes = request.form.get('notes')
        
        # إكمال عملية الإغلاق
        success, message = closure.complete_closure(closing_balance, notes)
        
        if success:
            db.session.commit()
            flash(message, 'success')
            return redirect(url_for('cash.closure_details', closure_id=closure.id))
        else:
            flash(message, 'error')
            return redirect(url_for('cash.complete_closure', closure_id=closure.id))
    
    # حساب الرصيد المتوقع
    closure.calculate_daily_totals()
    db.session.commit()
    
    return render_template(
        'cash/complete_closure.html',
        closure=closure
    )

@cash_blueprint.route('/cash/closure/reports')
@login_required
def closure_reports():
    """تقارير الإغلاق اليومي"""
    # التحقق من صلاحيات المستخدم
    if not current_user.has_permission('cash', 'view'):
        flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'error')
        return redirect(url_for('cash.cash_index'))
    
    # تطبيق الفلاتر
    register_id = request.args.get('register_id')
    date_from = request.args.get('date_from')
    date_to = request.args.get('date_to')
    status = request.args.get('status')
    
    # بناء الاستعلام
    query = DailyCashClosure.query
    
    if register_id:
        query = query.filter_by(cash_register_id=register_id)
    
    if date_from:
        date_from = datetime.strptime(date_from, '%Y-%m-%d').date()
        query = query.filter(DailyCashClosure.closure_date >= date_from)
    
    if date_to:
        date_to = datetime.strptime(date_to, '%Y-%m-%d').date()
        query = query.filter(DailyCashClosure.closure_date <= date_to)
    
    if status:
        query = query.filter_by(status=status)
    
    # ترتيب النتائج
    closures = query.order_by(desc(DailyCashClosure.closure_date)).all()
    
    # الحصول على الخزائن للفلترة
    cash_registers = CashRegister.query.filter_by(is_active=True).all()
    
    return render_template(
        'cash/closure_reports.html',
        closures=closures,
        cash_registers=cash_registers,
        selected_register=register_id,
        date_from=date_from.strftime('%Y-%m-%d') if date_from else '',
        date_to=date_to.strftime('%Y-%m-%d') if date_to else '',
        selected_status=status
    )
