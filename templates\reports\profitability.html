<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تقرير الربحية - نظام إدارة المبيعات</title>
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <!-- Remixicon -->
    <link href="https://cdn.jsdelivr.net/npm/remixicon@2.5.0/fonts/remixicon.css" rel="stylesheet">
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        .report-card {
            transition: all 0.3s ease;
        }
        .report-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 15px rgba(0, 0, 0, 0.1);
        }
    </style>
</head>
<body class="bg-gray-50">
    <div class="flex h-screen overflow-hidden">
        <!-- Sidebar -->
        {% include 'partials/sidebar.html' %}

        <!-- Main Content -->
        <div class="flex-1 flex flex-col overflow-hidden">
            <!-- Top Navigation -->
            {% include 'partials/topnav.html' %}

            <!-- Content Area -->
            <main class="flex-1 overflow-y-auto p-6">
                <div class="mb-6 flex items-center justify-between">
                    <div>
                        <h1 class="text-2xl font-bold text-gray-800">تقرير الربحية</h1>
                        <p class="text-gray-600">تحليل ربحية المنتجات والفئات</p>
                    </div>
                    <div class="flex space-x-2 space-x-reverse">
                        <a href="{{ url_for('reports.sales_index') }}" class="px-4 py-2 bg-blue-100 text-blue-700 rounded-md hover:bg-blue-200 transition-all">
                            <i class="ri-arrow-right-line"></i>
                            العودة لتقارير المبيعات
                        </a>
                        <a href="{{ url_for('reports.index') }}" class="px-4 py-2 bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200 transition-all">
                            <i class="ri-home-line"></i>
                            الرئيسية
                        </a>
                    </div>
                </div>

                <!-- Filters -->
                <div class="bg-white p-4 rounded-lg shadow-sm mb-6">
                    <form action="{{ url_for('reports.profitability_report') }}" method="get" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                        <div>
                            <label for="date_from" class="block text-sm font-medium text-gray-700 mb-1">من تاريخ</label>
                            <input type="date" id="date_from" name="date_from" value="{{ date_from }}"
                                   class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500">
                        </div>
                        <div>
                            <label for="date_to" class="block text-sm font-medium text-gray-700 mb-1">إلى تاريخ</label>
                            <input type="date" id="date_to" name="date_to" value="{{ date_to }}"
                                   class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500">
                        </div>
                        <div>
                            <label for="view_type" class="block text-sm font-medium text-gray-700 mb-1">عرض حسب</label>
                            <select id="view_type" name="view_type" class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500">
                                <option value="product" {% if view_type == 'product' %}selected{% endif %}>المنتجات</option>
                                <option value="category" {% if view_type == 'category' %}selected{% endif %}>الفئات</option>
                                <option value="daily" {% if view_type == 'daily' %}selected{% endif %}>يومي</option>
                            </select>
                        </div>
                        <div>
                            <label for="category_id" class="block text-sm font-medium text-gray-700 mb-1">الفئة</label>
                            <select id="category_id" name="category_id" class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500">
                                <option value="">الكل</option>
                                {% for category in categories %}
                                <option value="{{ category.id }}" {% if category.id|string == category_id %}selected{% endif %}>{{ category.name }}</option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="flex items-end">
                            <button type="submit" class="bg-indigo-600 text-white px-4 py-2 rounded-md hover:bg-indigo-700 transition-all">
                                <i class="ri-filter-3-line ml-1"></i>
                                تطبيق الفلتر
                            </button>
                        </div>
                    </form>
                </div>

                <!-- Profitability Summary -->
                <div class="bg-white p-6 rounded-lg shadow-sm mb-6">
                    <h2 class="text-lg font-bold text-gray-800 mb-4">ملخص الربحية</h2>
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                        <div class="p-4 bg-green-50 rounded-lg">
                            <h3 class="text-sm font-medium text-gray-500 mb-1">إجمالي الإيرادات</h3>
                            <p class="text-2xl font-bold text-green-600">
                                {{ "%.2f"|format(total_revenue) }} ج.م
                            </p>
                        </div>
                        <div class="p-4 bg-blue-50 rounded-lg">
                            <h3 class="text-sm font-medium text-gray-500 mb-1">إجمالي الأرباح</h3>
                            <p class="text-2xl font-bold text-blue-600">
                                {{ "%.2f"|format(total_profit) }} ج.م
                            </p>
                        </div>
                        <div class="p-4 bg-indigo-50 rounded-lg">
                            <h3 class="text-sm font-medium text-gray-500 mb-1">متوسط هامش الربح</h3>
                            <p class="text-2xl font-bold text-indigo-600">
                                {{ "%.2f"|format(avg_profit_margin) }}%
                            </p>
                        </div>
                    </div>
                </div>

                <!-- Profitability Chart -->
                <div class="bg-white p-6 rounded-lg shadow-sm mb-6">
                    <h2 class="text-lg font-bold text-gray-800 mb-4">رسم بياني للربحية</h2>
                    <div class="h-80">
                        <canvas id="profitabilityChart"></canvas>
                    </div>
                </div>

                <!-- Profitability Table -->
                <div class="bg-white rounded-lg shadow-sm overflow-hidden">
                    <h2 class="text-lg font-bold text-gray-800 p-6 border-b">
                        {% if view_type == 'product' %}
                            تفاصيل ربحية المنتجات
                        {% elif view_type == 'category' %}
                            تفاصيل ربحية الفئات
                        {% else %}
                            تفاصيل الربحية اليومية
                        {% endif %}
                    </h2>
                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-50">
                                <tr>
                                    {% if view_type == 'product' %}
                                        <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            المنتج
                                        </th>
                                        <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            سعر التكلفة
                                        </th>
                                        <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            سعر البيع
                                        </th>
                                    {% elif view_type == 'category' %}
                                        <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            الفئة
                                        </th>
                                    {% else %}
                                        <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            التاريخ
                                        </th>
                                    {% endif %}
                                    <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        الكمية المباعة
                                    </th>
                                    <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        الإيرادات
                                    </th>
                                    <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        الأرباح
                                    </th>
                                    <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        هامش الربح
                                    </th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                {% for item in profitability_data %}
                                <tr class="hover:bg-gray-50">
                                    {% if view_type == 'product' %}
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                            {{ item.name }}
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                            {{ "%.2f"|format(item.cost_price) }} ج.م
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                            {{ "%.2f"|format(item.price) }} ج.م
                                        </td>
                                    {% elif view_type == 'category' %}
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                            {{ item.name }}
                                        </td>
                                    {% else %}
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                            {{ item.date }}
                                        </td>
                                    {% endif %}
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                        {{ item.quantity_sold }}
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                        {{ "%.2f"|format(item.revenue) }} ج.م
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium {% if item.profit > 0 %}text-green-600{% else %}text-red-600{% endif %}">
                                        {{ "%.2f"|format(item.profit) }} ج.م
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium {% if item.profit_margin > 0 %}text-green-600{% else %}text-red-600{% endif %}">
                                        {{ "%.2f"|format(item.profit_margin) }}%
                                    </td>
                                </tr>
                                {% else %}
                                <tr>
                                    <td colspan="7" class="px-6 py-4 text-center text-gray-500">
                                        لا توجد بيانات للفترة المحددة
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <script>
        // Prepare data for chart
        const labels = [
            {% for item in profitability_data %}
                {% if view_type == 'product' or view_type == 'category' %}
                    '{{ item.name }}'{% if not loop.last %},{% endif %}
                {% else %}
                    '{{ item.date }}'{% if not loop.last %},{% endif %}
                {% endif %}
            {% endfor %}
        ];

        const revenueData = [
            {% for item in profitability_data %}
                {{ item.revenue }}{% if not loop.last %},{% endif %}
            {% endfor %}
        ];

        const profitData = [
            {% for item in profitability_data %}
                {{ item.profit }}{% if not loop.last %},{% endif %}
            {% endfor %}
        ];

        const profitMarginData = [
            {% for item in profitability_data %}
                {{ item.profit_margin }}{% if not loop.last %},{% endif %}
            {% endfor %}
        ];

        // Create chart
        const ctx = document.getElementById('profitabilityChart').getContext('2d');
        const profitabilityChart = new Chart(ctx, {
            type: 'bar',
            data: {
                labels: labels,
                datasets: [{
                    label: 'الإيرادات',
                    data: revenueData,
                    backgroundColor: 'rgba(59, 130, 246, 0.5)',
                    borderColor: 'rgba(59, 130, 246, 1)',
                    borderWidth: 1
                }, {
                    label: 'الأرباح',
                    data: profitData,
                    backgroundColor: 'rgba(16, 185, 129, 0.5)',
                    borderColor: 'rgba(16, 185, 129, 1)',
                    borderWidth: 1
                }, {
                    label: 'هامش الربح %',
                    data: profitMarginData,
                    backgroundColor: 'rgba(99, 102, 241, 0.5)',
                    borderColor: 'rgba(99, 102, 241, 1)',
                    borderWidth: 1,
                    type: 'line',
                    yAxisID: 'y1'
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true,
                        title: {
                            display: true,
                            text: 'القيمة (ج.م)'
                        }
                    },
                    y1: {
                        beginAtZero: true,
                        position: 'right',
                        title: {
                            display: true,
                            text: 'هامش الربح (%)'
                        },
                        grid: {
                            drawOnChartArea: false
                        }
                    }
                }
            }
        });
    </script>
</body>
</html>
