{% extends 'layout.html' %}

{% block title %}تفاصيل عملية الجرد{% endblock %}

{% block page_content %}
<div class="container mx-auto px-4 py-8">
    <div class="mb-6 flex justify-between items-center">
        <div>
            <h1 class="text-2xl font-bold">{{ count.name }}</h1>
            <p class="text-gray-600">{{ count.warehouse.name }} - {{ count.created_at.strftime('%Y-%m-%d') }}</p>
        </div>
        <div class="flex space-x-2 space-x-reverse">
            {% if count.status == 'in_progress' %}
            <button id="completeCountBtn" class="bg-green-500 text-white px-4 py-2 rounded-lg hover:bg-green-600 transition-all ml-2">
                <i class="ri-check-double-line ml-1"></i>إكمال عملية الجرد
            </button>
            {% endif %}
            
            <a href="{{ url_for('warehouses.inventory_counts') }}" class="bg-gray-100 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-200 transition-all">
                <i class="ri-arrow-right-line ml-1"></i>العودة لقائمة الجرد
            </a>
        </div>
    </div>

    <!-- معلومات عملية الجرد -->
    <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
        <div class="bg-white rounded-lg shadow p-6">
            <h2 class="text-lg font-semibold mb-4">معلومات عملية الجرد</h2>
            <div class="grid grid-cols-2 gap-4">
                <div>
                    <p class="text-gray-500 text-sm">المخزن</p>
                    <p class="font-medium">{{ count.warehouse.name }}</p>
                </div>
                <div>
                    <p class="text-gray-500 text-sm">الحالة</p>
                    <p>
                        {% if count.status == 'draft' %}
                        <span class="bg-blue-100 text-blue-800 text-xs font-medium px-2.5 py-0.5 rounded">مسودة</span>
                        {% elif count.status == 'in_progress' %}
                        <span class="bg-yellow-100 text-yellow-800 text-xs font-medium px-2.5 py-0.5 rounded">قيد التنفيذ</span>
                        {% elif count.status == 'completed' %}
                        <span class="bg-green-100 text-green-800 text-xs font-medium px-2.5 py-0.5 rounded">مكتملة</span>
                        {% elif count.status == 'cancelled' %}
                        <span class="bg-gray-100 text-gray-800 text-xs font-medium px-2.5 py-0.5 rounded">ملغاة</span>
                        {% endif %}
                    </p>
                </div>
                <div>
                    <p class="text-gray-500 text-sm">تاريخ الإنشاء</p>
                    <p>{{ count.created_at.strftime('%Y-%m-%d %H:%M') }}</p>
                </div>
                <div>
                    <p class="text-gray-500 text-sm">تاريخ الإكمال</p>
                    <p>{{ count.completed_at.strftime('%Y-%m-%d %H:%M') if count.completed_at else '-' }}</p>
                </div>
                <div>
                    <p class="text-gray-500 text-sm">المنشئ</p>
                    <p>{{ count.user.username if count.user else '-' }}</p>
                </div>
                <div>
                    <p class="text-gray-500 text-sm">عدد العناصر</p>
                    <p>{{ stats.total_items }}</p>
                </div>
            </div>
            
            {% if count.notes %}
            <div class="mt-4">
                <p class="text-gray-500 text-sm">ملاحظات</p>
                <p class="text-gray-700">{{ count.notes }}</p>
            </div>
            {% endif %}
        </div>
        
        <div class="bg-white rounded-lg shadow p-6">
            <h2 class="text-lg font-semibold mb-4">إحصائيات عملية الجرد</h2>
            <div class="mb-4">
                <p class="text-gray-500 text-sm mb-1">تقدم عملية الجرد</p>
                <div class="w-full bg-gray-200 rounded-full h-2.5">
                    <div class="bg-blue-600 h-2.5 rounded-full" style="width: {{ stats.progress }}%"></div>
                </div>
                <p class="text-sm text-gray-600 mt-1">{{ stats.counted_items }} من {{ stats.total_items }} ({{ stats.progress|round|int }}%)</p>
            </div>
            
            <div class="grid grid-cols-2 gap-4">
                <div>
                    <p class="text-gray-500 text-sm">إجمالي العناصر</p>
                    <p class="font-medium">{{ stats.total_items }}</p>
                </div>
                <div>
                    <p class="text-gray-500 text-sm">العناصر المجرودة</p>
                    <p class="font-medium">{{ stats.counted_items }}</p>
                </div>
                <div>
                    <p class="text-gray-500 text-sm">العناصر المتبقية</p>
                    <p class="font-medium">{{ stats.total_items - stats.counted_items }}</p>
                </div>
                <div>
                    <p class="text-gray-500 text-sm">العناصر ذات الاختلاف</p>
                    <p class="font-medium">{{ stats.discrepancy_items }}</p>
                </div>
            </div>
        </div>
    </div>

    <!-- جدول عناصر الجرد -->
    <div class="bg-white rounded-lg shadow overflow-hidden">
        <div class="p-4 bg-gray-50 border-b flex justify-between items-center">
            <h2 class="text-xl font-semibold">عناصر الجرد</h2>
            
            {% if count.status == 'draft' or count.status == 'in_progress' %}
            <div class="flex items-center">
                <div class="relative ml-2">
                    <input type="text" id="searchInput" placeholder="البحث عن منتج..." class="border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500">
                    <i class="ri-search-line absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"></i>
                </div>
                <select id="filterSelect" class="border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500">
                    <option value="all">جميع العناصر</option>
                    <option value="counted">المجرودة فقط</option>
                    <option value="uncounted">غير المجرودة فقط</option>
                    <option value="discrepancy">ذات اختلاف فقط</option>
                </select>
            </div>
            {% endif %}
        </div>

        <table class="min-w-full">
            <thead>
                <tr class="bg-gray-100">
                    <th class="px-6 py-3 border-b text-right">المنتج</th>
                    <th class="px-6 py-3 border-b text-right">الباركود</th>
                    <th class="px-6 py-3 border-b text-right">الكمية المتوقعة</th>
                    <th class="px-6 py-3 border-b text-right">الكمية الفعلية</th>
                    <th class="px-6 py-3 border-b text-right">الفرق</th>
                    <th class="px-6 py-3 border-b text-right">ملاحظات</th>
                    {% if count.status == 'draft' or count.status == 'in_progress' %}
                    <th class="px-6 py-3 border-b text-right">الإجراءات</th>
                    {% endif %}
                </tr>
            </thead>
            <tbody>
                {% for item in count_items %}
                <tr class="hover:bg-gray-50 inventory-item {% if item.actual_quantity is none %}bg-gray-50{% elif item.difference != 0 %}bg-yellow-50{% endif %}" 
                    data-product="{{ item.product.name }}" 
                    data-code="{{ item.product.code or '' }}"
                    data-status="{% if item.actual_quantity is none %}uncounted{% elif item.difference != 0 %}discrepancy{% else %}counted{% endif %}">
                    <td class="px-6 py-4 border-b font-medium">{{ item.product.name }}</td>
                    <td class="px-6 py-4 border-b">{{ item.product.code or '-' }}</td>
                    <td class="px-6 py-4 border-b">{{ item.expected_quantity }}</td>
                    <td class="px-6 py-4 border-b">{{ item.actual_quantity if item.actual_quantity is not none else '-' }}</td>
                    <td class="px-6 py-4 border-b">
                        {% if item.difference is not none %}
                            {% if item.difference > 0 %}
                            <span class="text-green-600">+{{ item.difference }}</span>
                            {% elif item.difference < 0 %}
                            <span class="text-red-600">{{ item.difference }}</span>
                            {% else %}
                            <span class="text-gray-600">0</span>
                            {% endif %}
                        {% else %}
                            -
                        {% endif %}
                    </td>
                    <td class="px-6 py-4 border-b text-sm">{{ item.notes or '-' }}</td>
                    {% if count.status == 'draft' or count.status == 'in_progress' %}
                    <td class="px-6 py-4 border-b">
                        <button onclick="openUpdateModal({{ item.id }}, '{{ item.product.name }}', {{ item.expected_quantity }})" class="bg-blue-100 text-blue-700 hover:bg-blue-200 px-2 py-1 rounded-md text-xs transition-all">
                            <i class="ri-edit-line ml-1"></i>تحديث الكمية
                        </button>
                    </td>
                    {% endif %}
                </tr>
                {% endfor %}
                
                {% if count_items|length == 0 %}
                <tr>
                    <td colspan="{% if count.status == 'draft' or count.status == 'in_progress' %}7{% else %}6{% endif %}" class="px-6 py-8 text-center text-gray-500">
                        <div class="flex flex-col items-center">
                            <i class="ri-inbox-line text-4xl mb-2"></i>
                            <p>لا توجد عناصر في عملية الجرد</p>
                        </div>
                    </td>
                </tr>
                {% endif %}
            </tbody>
        </table>
    </div>
</div>

<!-- نموذج تحديث الكمية -->
<div id="updateModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden">
    <div class="bg-white rounded-lg shadow-lg w-full max-w-md mx-4">
        <div class="p-4 border-b flex justify-between items-center">
            <h3 class="text-lg font-semibold" id="modalTitle">تحديث كمية المنتج</h3>
            <button onclick="closeUpdateModal()" class="text-gray-500 hover:text-gray-700">
                <i class="ri-close-line text-xl"></i>
            </button>
        </div>
        <form action="{{ url_for('warehouses.update_count_item', id=count.id) }}" method="POST">
            <div class="p-6">
                <input type="hidden" name="item_id" id="itemId">
                
                <div class="mb-4">
                    <label class="block text-gray-700 mb-2">الكمية المتوقعة</label>
                    <input type="text" id="expectedQuantity" class="w-full border rounded px-3 py-2 bg-gray-100" readonly>
                </div>
                
                <div class="mb-4">
                    <label class="block text-gray-700 mb-2">الكمية الفعلية <span class="text-red-500">*</span></label>
                    <input type="number" name="actual_quantity" id="actualQuantity" class="w-full border rounded px-3 py-2" required min="0">
                </div>
                
                <div class="mb-4">
                    <label class="block text-gray-700 mb-2">ملاحظات</label>
                    <textarea name="notes" id="itemNotes" class="w-full border rounded px-3 py-2" rows="2" placeholder="أي ملاحظات إضافية حول الكمية"></textarea>
                </div>
            </div>
            <div class="p-4 bg-gray-50 flex justify-end rounded-b-lg">
                <button type="button" onclick="closeUpdateModal()" class="bg-gray-100 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-200 transition-all ml-2">
                    إلغاء
                </button>
                <button type="submit" class="bg-blue-500 text-white px-4 py-2 rounded-lg hover:bg-blue-600 transition-all">
                    <i class="ri-check-line ml-1"></i>حفظ
                </button>
            </div>
        </form>
    </div>
</div>

<!-- نموذج إكمال عملية الجرد -->
<div id="completeModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden">
    <div class="bg-white rounded-lg shadow-lg w-full max-w-md mx-4">
        <div class="p-4 border-b flex justify-between items-center">
            <h3 class="text-lg font-semibold">إكمال عملية الجرد</h3>
            <button onclick="closeCompleteModal()" class="text-gray-500 hover:text-gray-700">
                <i class="ri-close-line text-xl"></i>
            </button>
        </div>
        <form action="{{ url_for('warehouses.complete_inventory_count', id=count.id) }}" method="POST">
            <div class="p-6">
                <p class="mb-4">هل أنت متأكد من إكمال عملية الجرد؟ لن تتمكن من تعديل الكميات بعد الإكمال.</p>
                
                <div class="mb-4">
                    <label class="flex items-center">
                        <input type="checkbox" name="apply_changes" value="yes" class="ml-2">
                        <span>تطبيق التغييرات على المخزون</span>
                    </label>
                    <p class="text-gray-500 text-sm mt-1">سيتم تحديث كميات المخزون لتتطابق مع الكميات الفعلية في عملية الجرد.</p>
                </div>
                
                {% if stats.counted_items < stats.total_items %}
                <div class="bg-yellow-50 border border-yellow-200 rounded p-3 mb-4">
                    <div class="flex items-center text-yellow-800 mb-1">
                        <i class="ri-error-warning-line ml-2"></i>
                        <span class="font-medium">تنبيه</span>
                    </div>
                    <p class="text-yellow-700 text-sm">
                        لم يتم جرد جميع العناصر ({{ stats.counted_items }} من {{ stats.total_items }}). هل أنت متأكد من إكمال عملية الجرد؟
                    </p>
                </div>
                {% endif %}
            </div>
            <div class="p-4 bg-gray-50 flex justify-end rounded-b-lg">
                <button type="button" onclick="closeCompleteModal()" class="bg-gray-100 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-200 transition-all ml-2">
                    إلغاء
                </button>
                <button type="submit" class="bg-green-500 text-white px-4 py-2 rounded-lg hover:bg-green-600 transition-all">
                    <i class="ri-check-double-line ml-1"></i>إكمال عملية الجرد
                </button>
            </div>
        </form>
    </div>
</div>

<script>
    // البحث والفلترة
    const searchInput = document.getElementById('searchInput');
    const filterSelect = document.getElementById('filterSelect');
    const inventoryItems = document.querySelectorAll('.inventory-item');
    
    function filterItems() {
        const searchTerm = searchInput.value.toLowerCase();
        const filterValue = filterSelect.value;
        
        inventoryItems.forEach(item => {
            const productName = item.dataset.product.toLowerCase();
            const productCode = item.dataset.code.toLowerCase();
            const status = item.dataset.status;
            
            const matchesSearch = productName.includes(searchTerm) || productCode.includes(searchTerm);
            const matchesFilter = filterValue === 'all' || status === filterValue;
            
            item.style.display = matchesSearch && matchesFilter ? '' : 'none';
        });
    }
    
    if (searchInput && filterSelect) {
        searchInput.addEventListener('input', filterItems);
        filterSelect.addEventListener('change', filterItems);
    }
    
    // نموذج تحديث الكمية
    function openUpdateModal(itemId, productName, expectedQuantity) {
        document.getElementById('itemId').value = itemId;
        document.getElementById('modalTitle').textContent = `تحديث كمية ${productName}`;
        document.getElementById('expectedQuantity').value = expectedQuantity;
        document.getElementById('updateModal').classList.remove('hidden');
    }
    
    function closeUpdateModal() {
        document.getElementById('updateModal').classList.add('hidden');
    }
    
    // نموذج إكمال عملية الجرد
    const completeCountBtn = document.getElementById('completeCountBtn');
    
    if (completeCountBtn) {
        completeCountBtn.addEventListener('click', function() {
            document.getElementById('completeModal').classList.remove('hidden');
        });
    }
    
    function closeCompleteModal() {
        document.getElementById('completeModal').classList.add('hidden');
    }
</script>
{% endblock page_content %}
