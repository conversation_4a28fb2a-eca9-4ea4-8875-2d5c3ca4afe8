<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>فاتورة #{{ order.invoice_number }}</title>
    <style>
        @page {
            {% if settings.receipt.receipt_size == 'a5' %}
            size: A5;
            margin: 10mm;
            {% else %}
            size: A4;
            margin: 15mm;
            {% endif %}
        }
        
        body {
            font-family: 'Cairo', 'Arial', sans-serif;
            margin: 0;
            padding: 0;
            color: #333;
            background-color: white;
            font-size: 12pt;
        }
        
        .invoice-container {
            max-width: 100%;
            margin: 0 auto;
        }
        
        .invoice-header {
            display: flex;
            justify-content: space-between;
            margin-bottom: 20px;
            padding-bottom: 20px;
            border-bottom: 1px solid #ddd;
        }
        
        .store-info {
            flex: 1;
        }
        
        .logo-container {
            text-align: center;
            margin-bottom: 10px;
        }
        
        .logo-container img {
            max-width: 150px;
            max-height: 80px;
        }
        
        .store-name {
            font-size: 24px;
            font-weight: bold;
            margin: 0 0 5px 0;
            color: #2563eb;
        }
        
        .store-contact {
            margin: 2px 0;
            font-size: 14px;
        }
        
        .invoice-info {
            flex: 1;
            text-align: left;
        }
        
        .invoice-title {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 10px;
            color: #2563eb;
        }
        
        .invoice-details {
            margin-bottom: 5px;
            font-size: 14px;
        }
        
        .customer-info {
            margin-bottom: 20px;
            padding: 15px;
            background-color: #f8fafc;
            border-radius: 8px;
        }
        
        .customer-title {
            font-weight: bold;
            margin-bottom: 10px;
            font-size: 16px;
            color: #2563eb;
        }
        
        .customer-details {
            margin-bottom: 5px;
        }
        
        .items-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }
        
        .items-table th {
            background-color: #2563eb;
            color: white;
            padding: 10px;
            text-align: right;
        }
        
        .items-table td {
            padding: 10px;
            border-bottom: 1px solid #ddd;
        }
        
        .items-table tr:nth-child(even) {
            background-color: #f8fafc;
        }
        
        .totals {
            margin-top: 20px;
            margin-right: auto;
            margin-left: 0;
            width: 300px;
        }
        
        .totals-row {
            display: flex;
            justify-content: space-between;
            padding: 8px 0;
            border-bottom: 1px solid #eee;
        }
        
        .totals-row.grand-total {
            font-weight: bold;
            font-size: 18px;
            border-top: 2px solid #2563eb;
            border-bottom: 2px solid #2563eb;
            padding: 10px 0;
            margin-top: 10px;
        }
        
        .payment-info {
            margin-top: 30px;
            padding: 15px;
            background-color: #f8fafc;
            border-radius: 8px;
        }
        
        .payment-title {
            font-weight: bold;
            margin-bottom: 10px;
            font-size: 16px;
            color: #2563eb;
        }
        
        .payment-details {
            margin-bottom: 5px;
        }
        
        .footer {
            margin-top: 40px;
            text-align: center;
            font-size: 14px;
            color: #666;
            padding-top: 20px;
            border-top: 1px solid #ddd;
        }
        
        .barcode {
            text-align: center;
            margin-top: 20px;
        }
        
        .barcode img {
            max-width: 250px;
        }
        
        .signature-section {
            margin-top: 40px;
            display: flex;
            justify-content: space-between;
        }
        
        .signature-box {
            width: 45%;
        }
        
        .signature-title {
            font-weight: bold;
            margin-bottom: 10px;
        }
        
        .signature-line {
            margin-top: 50px;
            border-top: 1px solid #333;
        }
        
        .no-print {
            display: block;
        }
        
        @media print {
            .no-print {
                display: none !important;
            }
        }
    </style>
</head>
<body>
    <div class="invoice-container">
        <div class="invoice-header">
            <div class="store-info">
                {% if settings.business.logo and settings.receipt.show_logo %}
                <div class="logo-container">
                    <img src="{{ settings.business.logo }}" alt="{{ settings.business.name }}">
                </div>
                {% endif %}
                <h1 class="store-name">{{ settings.business.name }}</h1>
                {% if settings.receipt.show_tax_number and settings.business.tax_number %}
                <p class="store-contact">الرقم الضريبي: {{ settings.business.tax_number }}</p>
                {% endif %}
                {% if settings.receipt.show_address and settings.business.address %}
                <p class="store-contact">{{ settings.business.address }}</p>
                {% endif %}
                {% if settings.receipt.show_phone and settings.business.phone %}
                <p class="store-contact">هاتف: {{ settings.business.phone }}</p>
                {% endif %}
                {% if settings.business.email %}
                <p class="store-contact">{{ settings.business.email }}</p>
                {% endif %}
            </div>
            <div class="invoice-info">
                <div class="invoice-title">فاتورة ضريبية</div>
                <div class="invoice-details">رقم الفاتورة: {{ order.invoice_number }}</div>
                <div class="invoice-details">التاريخ: {{ order.created_at.strftime('%Y-%m-%d %H:%M') }}</div>
                <div class="invoice-details">الموظف: {{ order.user.username if order.user else 'غير محدد' }}</div>
            </div>
        </div>
        
        <div class="customer-info">
            <div class="customer-title">معلومات العميل</div>
            <div class="customer-details">الاسم: {{ order.customer.name if order.customer else 'عميل نقدي' }}</div>
            {% if order.customer and order.customer.phone %}
            <div class="customer-details">الهاتف: {{ order.customer.phone }}</div>
            {% endif %}
            {% if order.customer and order.customer.email %}
            <div class="customer-details">البريد الإلكتروني: {{ order.customer.email }}</div>
            {% endif %}
            {% if order.customer and order.customer.address %}
            <div class="customer-details">العنوان: {{ order.customer.address }}</div>
            {% endif %}
        </div>
        
        <table class="items-table">
            <thead>
                <tr>
                    <th>#</th>
                    <th>المنتج</th>
                    <th>الكمية</th>
                    <th>السعر</th>
                    <th>الخصم</th>
                    <th>الإجمالي</th>
                </tr>
            </thead>
            <tbody>
                {% for item in order.items %}
                <tr>
                    <td>{{ loop.index }}</td>
                    <td>{{ item.product.name }}</td>
                    <td>{{ item.quantity }}</td>
                    <td>{{ item.price }} {{ settings.display.currency }}</td>
                    <td>{{ item.discount }} {{ settings.display.currency }}</td>
                    <td>{{ item.total }} {{ settings.display.currency }}</td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
        
        <div class="totals">
            <div class="totals-row">
                <span>المجموع</span>
                <span>{{ order.subtotal }} {{ settings.display.currency }}</span>
            </div>
            {% if settings.receipt.show_discount %}
            <div class="totals-row">
                <span>الخصم</span>
                <span>{{ order.discount }} {{ settings.display.currency }}</span>
            </div>
            {% endif %}
            {% if settings.receipt.show_tax %}
            <div class="totals-row">
                <span>الضريبة ({{ order.tax_percentage }}%)</span>
                <span>{{ order.tax }} {{ settings.display.currency }}</span>
            </div>
            {% endif %}
            <div class="totals-row grand-total">
                <span>الإجمالي</span>
                <span>{{ order.total }} {{ settings.display.currency }}</span>
            </div>
        </div>
        
        <div class="payment-info">
            <div class="payment-title">معلومات الدفع</div>
            <div class="payment-details">طريقة الدفع: {{ 'نقدي' if order.payment_method == 'cash' else 'بطاقة' if order.payment_method == 'card' else order.payment_method }}</div>
            <div class="payment-details">حالة الطلب: {{ 'مكتمل' if order.status == 'completed' else 'معلق' if order.status == 'pending' else 'ملغي' if order.status == 'cancelled' else 'مؤجل' if order.status == 'deferred' else order.status }}</div>
        </div>
        
        {% if settings.receipt.show_customer_signature %}
        <div class="signature-section">
            <div class="signature-box">
                <div class="signature-title">توقيع العميل</div>
                <div class="signature-line"></div>
            </div>
            <div class="signature-box">
                <div class="signature-title">توقيع البائع</div>
                <div class="signature-line"></div>
            </div>
        </div>
        {% endif %}
        
        {% if settings.receipt.show_barcode %}
        <div class="barcode">
            <img src="data:image/png;base64,{{ barcode_image }}" alt="Barcode">
        </div>
        {% endif %}
        
        <div class="footer">
            <p>{{ settings.receipt.footer or 'شكراً لتعاملكم معنا' }}</p>
            <p>Powered By ENG/ Fouad Saber Tel: 01020073527</p>
        </div>
    </div>
    
    <div class="no-print" style="text-align: center; margin: 20px 0;">
        <button onclick="window.print()" style="padding: 10px 20px; background-color: #2563eb; color: white; border: none; border-radius: 5px; cursor: pointer;">
            طباعة الفاتورة
        </button>
        <button onclick="window.close()" style="padding: 10px 20px; background-color: #64748b; color: white; border: none; border-radius: 5px; cursor: pointer; margin-right: 10px;">
            إغلاق
        </button>
    </div>
    
    <script>
        // طباعة تلقائية عند تحميل الصفحة
        window.onload = function() {
            setTimeout(function() {
                window.print();
            }, 1000);
        };
    </script>
</body>
</html>
