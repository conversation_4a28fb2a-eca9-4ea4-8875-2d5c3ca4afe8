{% extends "layout.html" %}

{% block page_content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h1 class="text-2xl font-bold">
            {% if action == 'create' %}
                إضافة مخزن جديد
            {% else %}
                تعديل المخزن
            {% endif %}
        </h1>
        <a href="{{ url_for('warehouses.index') }}" class="bg-gray-500 text-white px-4 py-2 rounded hover:bg-gray-600">
            العودة للمخازن
        </a>
    </div>

    <div class="bg-white rounded-lg shadow p-6">
        <form method="POST" action="{{ url_for('warehouses.create') if action == 'create' else url_for('warehouses.update', id=warehouse.id) }}">
            <div class="mb-4">
                <label for="name" class="block text-gray-700 font-bold mb-2">اسم المخزن</label>
                <input type="text" id="name" name="name" value="{{ warehouse.name if warehouse else '' }}" 
                       class="w-full px-3 py-2 border border-gray-300 rounded focus:outline-none focus:border-blue-500" 
                       required>
            </div>
            
            <div class="mb-4">
                <label for="location" class="block text-gray-700 font-bold mb-2">الموقع</label>
                <input type="text" id="location" name="location" value="{{ warehouse.location if warehouse else '' }}" 
                       class="w-full px-3 py-2 border border-gray-300 rounded focus:outline-none focus:border-blue-500" 
                       required>
            </div>
            
            <div class="mb-4">
                <label for="description" class="block text-gray-700 font-bold mb-2">الوصف</label>
                <textarea id="description" name="description" 
                          class="w-full px-3 py-2 border border-gray-300 rounded focus:outline-none focus:border-blue-500" 
                          rows="3">{{ warehouse.description if warehouse else '' }}</textarea>
            </div>
            
            <div class="mb-4">
                <label class="flex items-center">
                    <input type="checkbox" name="is_active" 
                           {% if warehouse and warehouse.is_active %}checked{% endif %} 
                           class="form-checkbox h-5 w-5 text-blue-600">
                    <span class="mr-2 text-gray-700">نشط</span>
                </label>
            </div>
            
            <div class="flex justify-end">
                <button type="submit" class="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600">
                    {% if action == 'create' %}
                        إضافة المخزن
                    {% else %}
                        حفظ التغييرات
                    {% endif %}
                </button>
            </div>
        </form>
    </div>
</div>
{% endblock page_content %}
