<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Nobara - إعدادات الطباعة</title>
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <!-- Remixicon -->
    <link href="https://cdn.jsdelivr.net/npm/remixicon@2.5.0/fonts/remixicon.css" rel="stylesheet">
    <!-- Alpine.js -->
    <script defer src="https://cdn.jsdelivr.net/npm/alpinejs@3.x.x/dist/cdn.min.js"></script>
    <script>
        tailwind.config = {
            darkMode: 'class',
            theme: {
                extend: {
                    colors: {
                        primary: {
                            50: '#eff6ff',
                            100: '#dbeafe',
                            200: '#bfdbfe',
                            300: '#93c5fd',
                            400: '#60a5fa',
                            500: '#3b82f6',
                            600: '#2563eb',
                            700: '#1d4ed8',
                            800: '#1e40af',
                            900: '#1e3a8a',
                            950: '#172554',
                        },
                        dark: {
                            100: '#1E293B',
                            200: '#0F172A',
                            300: '#0B1120',
                            400: '#060A15'
                        }
                    }
                }
            }
        }
    </script>
    <style>
        body {
            font-family: 'Cairo', 'Tajawal', sans-serif;
            transition: background-color 0.3s ease, color 0.3s ease;
        }
        
        .bg-pattern {
            background-color: #f9fafb;
            background-image: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%233B82F6' fill-opacity='0.05'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
        }
        
        .dark .bg-pattern {
            background-color: #0F172A;
            background-image: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%233B82F6' fill-opacity='0.1'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
        }
        
        .powered-by {
            position: fixed;
            bottom: 1rem;
            left: 1rem;
            background-color: #ffffff;
            border-radius: 1rem;
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
            padding: 0.75rem 1rem;
            display: flex;
            align-items: center;
            z-index: 40;
            transition: all 0.3s ease;
            border: 1px solid #e5e7eb;
        }
        
        .dark .powered-by {
            background-color: #1E293B;
            border-color: #2D3748;
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.3), 0 4px 6px -2px rgba(0, 0, 0, 0.2);
        }
        
        .powered-by:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
        }
        
        .dark .powered-by:hover {
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.3), 0 10px 10px -5px rgba(0, 0, 0, 0.2);
        }
    </style>
</head>
<body class="bg-pattern dark:bg-dark-200 dark:text-gray-100" x-data="{ darkMode: localStorage.getItem('darkMode') === 'enabled' }">
    <div class="flex h-screen overflow-hidden">
        <!-- Sidebar -->
        {% include 'partials/sidebar.html' %}

        <!-- Main Content -->
        <div class="flex-1 flex flex-col overflow-hidden">
            <!-- Top Navigation -->
            {% include 'partials/topnav.html' %}

            <!-- Content Area -->
            <main class="flex-1 overflow-y-auto p-6">
                <div class="mb-6 flex justify-between items-center">
                    <div>
                        <h1 class="text-2xl font-bold text-gray-800 dark:text-white">إعدادات الطباعة</h1>
                        <p class="text-gray-600 dark:text-gray-400">تكوين الطابعات وإعدادات الطباعة التلقائية</p>
                    </div>
                    <div class="flex items-center gap-2">
                        <a href="{{ url_for('settings.index') }}" class="flex items-center gap-1 bg-gray-100 dark:bg-dark-100 hover:bg-gray-200 dark:hover:bg-dark-300 text-gray-700 dark:text-gray-300 py-2 px-4 rounded-lg transition-colors">
                            <i class="ri-arrow-right-line"></i>
                            <span>العودة للإعدادات</span>
                        </a>
                        <button @click="darkMode = !darkMode; localStorage.setItem('darkMode', darkMode ? 'enabled' : 'disabled'); document.documentElement.classList.toggle('dark')" class="p-2 rounded-full bg-gray-100 dark:bg-dark-100 hover:bg-gray-200 dark:hover:bg-dark-300 transition-colors">
                            <i class="ri-moon-line dark:hidden"></i>
                            <i class="ri-sun-line hidden dark:block"></i>
                        </button>
                    </div>
                </div>

                <div class="bg-white dark:bg-dark-100 rounded-xl shadow-sm overflow-hidden mb-6">
                    <div class="p-6">
                        <form id="printSettingsForm" action="{{ url_for('settings.update_print_settings') }}" method="POST">
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div>
                                    <h3 class="text-lg font-medium text-gray-800 dark:text-white mb-4 flex items-center">
                                        <i class="ri-printer-line ml-2 text-primary-500"></i>
                                        إعدادات الطابعة
                                    </h3>
                                    
                                    <!-- الطابعة الافتراضية -->
                                    <div class="mb-4">
                                        <label for="default_printer" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">الطابعة الافتراضية</label>
                                        <select id="default_printer" name="default_printer" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 dark:bg-dark-200 dark:text-gray-100">
                                            <option value="">-- اختر الطابعة --</option>
                                            {% for printer in printers %}
                                            <option value="{{ printer }}" {% if settings.printing.default_printer == printer %}selected{% endif %}>{{ printer }}</option>
                                            {% endfor %}
                                        </select>
                                        <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">الطابعة المستخدمة لطباعة الفواتير</p>
                                    </div>
                                    
                                    <!-- طابعة الباركود -->
                                    <div class="mb-4">
                                        <label for="barcode_printer" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">طابعة الباركود</label>
                                        <select id="barcode_printer" name="barcode_printer" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 dark:bg-dark-200 dark:text-gray-100">
                                            <option value="">-- اختر الطابعة --</option>
                                            {% for printer in printers %}
                                            <option value="{{ printer }}" {% if settings.printing.barcode_printer == printer %}selected{% endif %}>{{ printer }}</option>
                                            {% endfor %}
                                        </select>
                                        <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">الطابعة المستخدمة لطباعة الباركود</p>
                                    </div>
                                    
                                    <!-- حجم الورق -->
                                    <div class="mb-4">
                                        <label for="paper_size" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">حجم الورق</label>
                                        <select id="paper_size" name="paper_size" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 dark:bg-dark-200 dark:text-gray-100">
                                            <option value="58mm" {% if settings.printing.paper_size == '58mm' %}selected{% endif %}>58mm (صغير)</option>
                                            <option value="80mm" {% if settings.printing.paper_size == '80mm' %}selected{% endif %}>80mm (متوسط)</option>
                                            <option value="a4" {% if settings.printing.paper_size == 'a4' %}selected{% endif %}>A4 (كبير)</option>
                                        </select>
                                    </div>
                                    
                                    <!-- عدد النسخ -->
                                    <div class="mb-4">
                                        <label for="copies" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">عدد النسخ</label>
                                        <input type="number" id="copies" name="copies" value="{{ settings.printing.copies }}" min="1" max="10" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 dark:bg-dark-200 dark:text-gray-100">
                                    </div>
                                </div>
                                
                                <div>
                                    <h3 class="text-lg font-medium text-gray-800 dark:text-white mb-4 flex items-center">
                                        <i class="ri-settings-3-line ml-2 text-primary-500"></i>
                                        إعدادات الطباعة التلقائية
                                    </h3>
                                    
                                    <!-- الطباعة التلقائية للفواتير -->
                                    <div class="mb-4">
                                        <label class="flex items-center">
                                            <input type="checkbox" id="auto_print_receipt" name="auto_print_receipt" {% if settings.printing.auto_print_receipt %}checked{% endif %} class="w-4 h-4 text-primary-600 border-gray-300 rounded focus:ring-primary-500 dark:bg-dark-200 dark:border-gray-600">
                                            <span class="mr-2 text-sm text-gray-700 dark:text-gray-300">الطباعة التلقائية للفواتير</span>
                                        </label>
                                        <p class="text-xs text-gray-500 dark:text-gray-400 mt-1 mr-6">طباعة الفاتورة تلقائيًا بعد إتمام عملية البيع</p>
                                    </div>
                                    
                                    <!-- الطباعة التلقائية للإيصالات -->
                                    <div class="mb-4">
                                        <label class="flex items-center">
                                            <input type="checkbox" id="auto_print_payment" name="auto_print_payment" {% if settings.printing.auto_print_payment %}checked{% endif %} class="w-4 h-4 text-primary-600 border-gray-300 rounded focus:ring-primary-500 dark:bg-dark-200 dark:border-gray-600">
                                            <span class="mr-2 text-sm text-gray-700 dark:text-gray-300">الطباعة التلقائية لإيصالات الدفع</span>
                                        </label>
                                        <p class="text-xs text-gray-500 dark:text-gray-400 mt-1 mr-6">طباعة إيصال الدفع تلقائيًا بعد إتمام عملية الدفع</p>
                                    </div>
                                    
                                    <!-- الطباعة التلقائية للباركود -->
                                    <div class="mb-4">
                                        <label class="flex items-center">
                                            <input type="checkbox" id="auto_print_barcode" name="auto_print_barcode" {% if settings.printing.auto_print_barcode %}checked{% endif %} class="w-4 h-4 text-primary-600 border-gray-300 rounded focus:ring-primary-500 dark:bg-dark-200 dark:border-gray-600">
                                            <span class="mr-2 text-sm text-gray-700 dark:text-gray-300">الطباعة التلقائية للباركود</span>
                                        </label>
                                        <p class="text-xs text-gray-500 dark:text-gray-400 mt-1 mr-6">طباعة الباركود تلقائيًا بعد إنشاء منتج جديد</p>
                                    </div>
                                    
                                    <!-- عرض معاينة الطباعة -->
                                    <div class="mb-4">
                                        <label class="flex items-center">
                                            <input type="checkbox" id="show_print_preview" name="show_print_preview" {% if settings.printing.show_print_preview %}checked{% endif %} class="w-4 h-4 text-primary-600 border-gray-300 rounded focus:ring-primary-500 dark:bg-dark-200 dark:border-gray-600">
                                            <span class="mr-2 text-sm text-gray-700 dark:text-gray-300">عرض معاينة الطباعة</span>
                                        </label>
                                        <p class="text-xs text-gray-500 dark:text-gray-400 mt-1 mr-6">عرض معاينة الطباعة قبل إرسالها للطابعة</p>
                                    </div>
                                    
                                    <!-- طباعة شعار المتجر -->
                                    <div class="mb-4">
                                        <label class="flex items-center">
                                            <input type="checkbox" id="print_logo" name="print_logo" {% if settings.printing.print_logo %}checked{% endif %} class="w-4 h-4 text-primary-600 border-gray-300 rounded focus:ring-primary-500 dark:bg-dark-200 dark:border-gray-600">
                                            <span class="mr-2 text-sm text-gray-700 dark:text-gray-300">طباعة شعار المتجر</span>
                                        </label>
                                        <p class="text-xs text-gray-500 dark:text-gray-400 mt-1 mr-6">طباعة شعار المتجر في الفواتير والإيصالات</p>
                                    </div>
                                </div>
                                
                                <div class="md:col-span-2">
                                    <h3 class="text-lg font-medium text-gray-800 dark:text-white mb-4 flex items-center">
                                        <i class="ri-test-tube-line ml-2 text-primary-500"></i>
                                        اختبار الطباعة
                                    </h3>
                                    
                                    <div class="flex flex-wrap gap-4">
                                        <button type="button" id="test_receipt_print" class="bg-primary-500 hover:bg-primary-600 text-white py-2 px-4 rounded-lg inline-flex items-center transition-colors">
                                            <i class="ri-bill-line ml-1"></i>
                                            <span>اختبار طباعة الفاتورة</span>
                                        </button>
                                        
                                        <button type="button" id="test_barcode_print" class="bg-primary-500 hover:bg-primary-600 text-white py-2 px-4 rounded-lg inline-flex items-center transition-colors">
                                            <i class="ri-barcode-line ml-1"></i>
                                            <span>اختبار طباعة الباركود</span>
                                        </button>
                                        
                                        <button type="button" id="refresh_printers" class="bg-gray-500 hover:bg-gray-600 text-white py-2 px-4 rounded-lg inline-flex items-center transition-colors">
                                            <i class="ri-refresh-line ml-1"></i>
                                            <span>تحديث قائمة الطابعات</span>
                                        </button>
                                    </div>
                                </div>
                                
                                <div class="md:col-span-2 flex justify-end mt-4">
                                    <button type="submit" class="bg-primary-500 hover:bg-primary-600 text-white py-2 px-6 rounded-lg inline-flex items-center transition-colors">
                                        <i class="ri-save-line ml-1"></i>
                                        <span>حفظ الإعدادات</span>
                                    </button>
                                </div>
                            </div>
                            
                            <input type="hidden" name="section" value="printing">
                        </form>
                    </div>
                </div>
                
                <!-- معلومات الطباعة -->
                <div class="bg-white dark:bg-dark-100 rounded-xl shadow-sm overflow-hidden">
                    <div class="p-6">
                        <h3 class="text-lg font-medium text-gray-800 dark:text-white mb-4 flex items-center">
                            <i class="ri-information-line ml-2 text-primary-500"></i>
                            معلومات الطباعة
                        </h3>
                        
                        <div class="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4 border border-blue-100 dark:border-blue-900/30">
                            <p class="text-gray-700 dark:text-gray-300 mb-2">
                                <strong>الطباعة الحرارية:</strong> تستخدم الطابعات الحرارية لطباعة الفواتير والإيصالات بسرعة وبدون حبر.
                            </p>
                            <p class="text-gray-700 dark:text-gray-300 mb-2">
                                <strong>أحجام الورق:</strong>
                                <ul class="list-disc list-inside mr-4 mt-1">
                                    <li>58mm: مناسب للإيصالات الصغيرة والطابعات المحمولة.</li>
                                    <li>80mm: الحجم القياسي لمعظم طابعات نقاط البيع.</li>
                                    <li>A4: مناسب للفواتير الرسمية والتقارير.</li>
                                </ul>
                            </p>
                            <p class="text-gray-700 dark:text-gray-300">
                                <strong>ملاحظة:</strong> تأكد من تثبيت تعريفات الطابعة بشكل صحيح على جهاز الكمبيوتر لضمان عمل الطباعة بشكل سليم.
                            </p>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>
    
    <!-- Powered By -->
    <div class="powered-by">
        <div class="w-8 h-8 rounded-full bg-primary-50 dark:bg-primary-900/50 flex items-center justify-center ml-2 text-primary-600 dark:text-primary-400">
            <i class="ri-code-s-slash-line"></i>
        </div>
        <div>
            <div class="text-xs text-gray-600 dark:text-gray-400">
                Powered By <span class="font-bold text-primary-600 dark:text-primary-400">ENG/ Fouad Saber</span>
            </div>
            <div class="text-xs text-gray-500 dark:text-gray-500">Tel: 01020073527</div>
        </div>
    </div>
    
    <script>
        // تفعيل وضع الظلام إذا كان مخزنًا
        if (localStorage.getItem('darkMode') === 'enabled') {
            document.documentElement.classList.add('dark');
        }
        
        document.addEventListener('DOMContentLoaded', function() {
            // إرسال النموذج
            document.getElementById('printSettingsForm').addEventListener('submit', function(e) {
                e.preventDefault();
                
                fetch(this.action, {
                    method: 'POST',
                    body: new FormData(this),
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        alert("تم حفظ إعدادات الطباعة بنجاح");
                    } else {
                        alert("حدث خطأ أثناء حفظ الإعدادات: " + data.error);
                    }
                })
                .catch(error => {
                    alert("حدث خطأ أثناء الاتصال بالخادم");
                    console.error(error);
                });
            });
            
            // اختبار طباعة الفاتورة
            document.getElementById('test_receipt_print').addEventListener('click', function() {
                fetch("{{ url_for('settings.test_receipt_print') }}")
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        alert("تم إرسال اختبار الطباعة بنجاح");
                    } else {
                        alert("حدث خطأ أثناء اختبار الطباعة: " + data.error);
                    }
                })
                .catch(error => {
                    alert("حدث خطأ أثناء الاتصال بالخادم");
                    console.error(error);
                });
            });
            
            // اختبار طباعة الباركود
            document.getElementById('test_barcode_print').addEventListener('click', function() {
                fetch("{{ url_for('settings.test_barcode_print') }}")
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        alert("تم إرسال اختبار طباعة الباركود بنجاح");
                    } else {
                        alert("حدث خطأ أثناء اختبار طباعة الباركود: " + data.error);
                    }
                })
                .catch(error => {
                    alert("حدث خطأ أثناء الاتصال بالخادم");
                    console.error(error);
                });
            });
            
            // تحديث قائمة الطابعات
            document.getElementById('refresh_printers').addEventListener('click', function() {
                fetch("{{ url_for('settings.refresh_printers') }}")
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        alert("تم تحديث قائمة الطابعات بنجاح");
                        location.reload();
                    } else {
                        alert("حدث خطأ أثناء تحديث قائمة الطابعات: " + data.error);
                    }
                })
                .catch(error => {
                    alert("حدث خطأ أثناء الاتصال بالخادم");
                    console.error(error);
                });
            });
        });
    </script>
</body>
</html>
