
{% extends "layout.html" %}

{% block page_content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h1 class="text-2xl font-bold">إدارة المخازن</h1>
        <div class="flex space-x-2 space-x-reverse">
            <a href="{{ url_for('warehouses.dashboard') }}" class="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600 transition-all ml-2">
                <i class="ri-dashboard-line ml-1"></i>لوحة التحكم
            </a>
            <a href="{{ url_for('warehouses.reports') }}" class="bg-indigo-500 text-white px-4 py-2 rounded hover:bg-indigo-600 transition-all ml-2">
                <i class="ri-file-chart-line ml-1"></i>تقارير المخزون
            </a>
            <a href="{{ url_for('warehouses.movements') }}" class="bg-purple-500 text-white px-4 py-2 rounded hover:bg-purple-600 transition-all ml-2">
                <i class="ri-history-line ml-1"></i>حركة المخزون
            </a>
            <a href="{{ url_for('warehouses.alerts') }}" class="bg-yellow-500 text-white px-4 py-2 rounded hover:bg-yellow-600 transition-all ml-2">
                <i class="ri-notification-3-line ml-1"></i>تنبيهات المخزون
            </a>
            <a href="{{ url_for('warehouses.inventory_counts') }}" class="bg-green-500 text-white px-4 py-2 rounded hover:bg-green-600 transition-all ml-2">
                <i class="ri-list-check-2 ml-1"></i>الجرد الدوري
            </a>
            <a href="{{ url_for('warehouses.create') }}" class="bg-teal-500 text-white px-4 py-2 rounded hover:bg-teal-600 transition-all">
                <i class="ri-add-line ml-1"></i>إضافة مخزن جديد
            </a>
        </div>
    </div>

    <!-- إحصائيات المخازن -->
    {% if stats %}
    <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
        <div class="bg-white rounded-lg shadow p-4 border-r-4 border-blue-500">
            <div class="flex justify-between items-center">
                <div>
                    <p class="text-gray-500 text-sm">إجمالي المخازن</p>
                    <p class="text-2xl font-bold">{{ stats.total }}</p>
                </div>
                <div class="text-blue-500 text-3xl">
                    <i class="ri-store-2-line"></i>
                </div>
            </div>
        </div>
        <div class="bg-white rounded-lg shadow p-4 border-r-4 border-green-500">
            <div class="flex justify-between items-center">
                <div>
                    <p class="text-gray-500 text-sm">المخازن النشطة</p>
                    <p class="text-2xl font-bold">{{ stats.active }}</p>
                </div>
                <div class="text-green-500 text-3xl">
                    <i class="ri-checkbox-circle-line"></i>
                </div>
            </div>
        </div>
        <div class="bg-white rounded-lg shadow p-4 border-r-4 border-red-500">
            <div class="flex justify-between items-center">
                <div>
                    <p class="text-gray-500 text-sm">المخازن غير النشطة</p>
                    <p class="text-2xl font-bold">{{ stats.inactive }}</p>
                </div>
                <div class="text-red-500 text-3xl">
                    <i class="ri-close-circle-line"></i>
                </div>
            </div>
        </div>
    </div>
    {% endif %}

    <!-- حقل البحث والفلترة -->
    <div class="bg-white rounded-lg shadow p-4 mb-6">
        <form method="GET" action="{{ url_for('warehouses.index') }}" class="flex flex-wrap items-center gap-4">
            <div class="relative flex-grow">
                <div class="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                    <i class="ri-search-line text-gray-400"></i>
                </div>
                <input type="text" name="search" value="{{ search }}" placeholder="البحث عن مخزن..."
                       class="w-full pr-10 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
            </div>
            <div>
                <select name="status" class="border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500">
                    <option value="all" {% if status == 'all' %}selected{% endif %}>جميع المخازن</option>
                    <option value="active" {% if status == 'active' %}selected{% endif %}>المخازن النشطة</option>
                    <option value="inactive" {% if status == 'inactive' %}selected{% endif %}>المخازن غير النشطة</option>
                </select>
            </div>
            <button type="submit" class="bg-blue-500 text-white px-4 py-2 rounded-md hover:bg-blue-600 transition-all">
                بحث
            </button>
        </form>
    </div>

    <div class="bg-white rounded-lg shadow overflow-hidden">
        <table class="min-w-full">
            <thead>
                <tr class="bg-gray-100">
                    <th class="px-6 py-3 border-b text-right">اسم المخزن</th>
                    <th class="px-6 py-3 border-b text-right">الموقع</th>
                    <th class="px-6 py-3 border-b text-right">عدد المنتجات</th>
                    <th class="px-6 py-3 border-b text-right">المخزون المنخفض</th>
                    <th class="px-6 py-3 border-b text-right">نفذ من المخزون</th>
                    <th class="px-6 py-3 border-b text-right">الحالة</th>
                    <th class="px-6 py-3 border-b text-right">الإجراءات</th>
                </tr>
            </thead>
            <tbody>
                {% for warehouse in warehouses %}
                <tr class="hover:bg-gray-50">
                    <td class="px-6 py-4 border-b font-medium">{{ warehouse.name }}</td>
                    <td class="px-6 py-4 border-b">{{ warehouse.location }}</td>
                    <td class="px-6 py-4 border-b">{{ warehouse.get_product_count() }}</td>
                    <td class="px-6 py-4 border-b">
                        {% if warehouse.get_low_stock_count() > 0 %}
                        <span class="text-yellow-600 font-medium">{{ warehouse.get_low_stock_count() }}</span>
                        {% else %}
                        <span class="text-gray-500">0</span>
                        {% endif %}
                    </td>
                    <td class="px-6 py-4 border-b">
                        {% if warehouse.get_out_of_stock_count() > 0 %}
                        <span class="text-red-600 font-medium">{{ warehouse.get_out_of_stock_count() }}</span>
                        {% else %}
                        <span class="text-gray-500">0</span>
                        {% endif %}
                    </td>
                    <td class="px-6 py-4 border-b">
                        <div class="flex items-center space-x-2 space-x-reverse">
                            {% if warehouse.is_active %}
                            <span class="bg-green-100 text-green-800 text-xs font-medium px-2.5 py-0.5 rounded">نشط</span>
                            {% else %}
                            <span class="bg-red-100 text-red-800 text-xs font-medium px-2.5 py-0.5 rounded">غير نشط</span>
                            {% endif %}

                            {% if warehouse.is_default %}
                            <span class="bg-blue-100 text-blue-800 text-xs font-medium px-2.5 py-0.5 rounded">افتراضي</span>
                            {% endif %}
                        </div>
                    </td>
                    <td class="px-6 py-4 border-b">
                        <div class="flex space-x-2 space-x-reverse">
                            <a href="{{ url_for('warehouses.inventory', id=warehouse.id) }}"
                               class="bg-blue-100 text-blue-700 hover:bg-blue-200 px-3 py-1 rounded-md text-sm transition-all">
                                <i class="ri-stack-line ml-1"></i>المخزون
                            </a>
                            <a href="#" onclick="editWarehouse({{ warehouse.id }})"
                               class="bg-gray-100 text-gray-700 hover:bg-gray-200 px-3 py-1 rounded-md text-sm transition-all">
                                <i class="ri-edit-line ml-1"></i>تعديل
                            </a>

                            <div class="relative" x-data="{ open: false }">
                                <button @click="open = !open" class="bg-gray-100 text-gray-700 hover:bg-gray-200 px-3 py-1 rounded-md text-sm transition-all">
                                    <i class="ri-more-2-fill"></i>
                                </button>
                                <div x-show="open" @click.away="open = false" class="absolute left-0 mt-2 w-48 bg-white rounded-md shadow-lg z-10">
                                    <div class="py-1">
                                        {% if not warehouse.is_default %}
                                        <form action="{{ url_for('warehouses.set_default', id=warehouse.id) }}" method="POST" class="block">
                                            <button type="submit" class="w-full text-right px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                                <i class="ri-star-line ml-1"></i>تعيين كمخزن افتراضي
                                            </button>
                                        </form>
                                        {% endif %}

                                        <form action="{{ url_for('warehouses.toggle_status', id=warehouse.id) }}" method="POST" class="block">
                                            <button type="submit" class="w-full text-right px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                                {% if warehouse.is_active %}
                                                <i class="ri-close-circle-line ml-1"></i>تعطيل المخزن
                                                {% else %}
                                                <i class="ri-checkbox-circle-line ml-1"></i>تفعيل المخزن
                                                {% endif %}
                                            </button>
                                        </form>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </td>
                </tr>
                {% endfor %}

                {% if warehouses|length == 0 %}
                <tr>
                    <td colspan="7" class="px-6 py-8 text-center text-gray-500">
                        <div class="flex flex-col items-center">
                            <i class="ri-inbox-line text-4xl mb-2"></i>
                            <p>لا توجد مخازن متاحة</p>
                            {% if search %}
                            <p class="text-sm mt-1">جرب البحث بكلمات مختلفة</p>
                            {% else %}
                            <a href="{{ url_for('warehouses.create') }}" class="mt-2 text-blue-600 hover:underline">إضافة مخزن جديد</a>
                            {% endif %}
                        </div>
                    </td>
                </tr>
                {% endif %}
            </tbody>
        </table>
    </div>

    <script>
        function editWarehouse(id) {
            // سيتم تنفيذ هذه الوظيفة لاحقاً
            alert('سيتم تنفيذ وظيفة تعديل المخزن قريباً');
        }
    </script>
</div>
{% endblock page_content %}
