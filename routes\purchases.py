from flask import Blueprint, render_template, request, jsonify, flash, redirect, url_for
from flask_login import login_required, current_user
from models import Supplier, Purchase, PurchaseItem, Product, Inventory, Warehouse
from app import db
from sqlalchemy import func, desc
from datetime import datetime
import random
import string

purchases_blueprint = Blueprint('purchases', __name__)

@purchases_blueprint.route('/purchases')
@login_required
def index():
    # Get filter parameters
    search = request.args.get('search', '')
    supplier_id = request.args.get('supplier', '')
    status = request.args.get('status', '')
    date_from = request.args.get('date_from', '')
    date_to = request.args.get('date_to', '')

    # Prepare query
    query = Purchase.query

    # Apply filters
    if search:
        query = query.filter(Purchase.reference_number.ilike(f'%{search}%'))

    if supplier_id and supplier_id.isdigit():
        query = query.filter(Purchase.supplier_id == int(supplier_id))

    if status:
        query = query.filter(Purchase.status == status)

    if date_from:
        try:
            date_from_obj = datetime.strptime(date_from, '%Y-%m-%d')
            query = query.filter(Purchase.created_at >= date_from_obj)
        except ValueError:
            pass

    if date_to:
        try:
            date_to_obj = datetime.strptime(date_to, '%Y-%m-%d')
            # Add a day to include all orders on the end date
            date_to_obj = date_to_obj.replace(hour=23, minute=59, second=59)
            query = query.filter(Purchase.created_at <= date_to_obj)
        except ValueError:
            pass

    # Execute query with pagination
    page = request.args.get('page', 1, type=int)
    per_page = 10
    purchases_paginated = query.order_by(Purchase.created_at.desc()).paginate(page=page, per_page=per_page)

    # Get suppliers for filter dropdown
    suppliers = Supplier.query.order_by(Supplier.name).all()

    # Get purchase statistics
    total_purchases = Purchase.query.count()
    total_amount = db.session.query(func.sum(Purchase.total)).scalar() or 0

    # Recent purchases
    recent_purchases = Purchase.query.order_by(Purchase.created_at.desc()).limit(5).all()

    # Stats to pass to template
    stats = {
        'total_purchases': total_purchases,
        'total_amount': total_amount,
        'recent_purchases': recent_purchases,
        'pending_count': Purchase.query.filter_by(status='pending').count(),
        'received_count': Purchase.query.filter_by(status='received').count()
    }

    return render_template(
        'purchases/purchases.html',
        purchases=purchases_paginated,
        suppliers=suppliers,
        stats=stats,
        search=search,
        supplier_id=supplier_id,
        status=status,
        date_from=date_from,
        date_to=date_to,
        current_user=current_user
    )

@purchases_blueprint.route('/purchases/create', methods=['GET', 'POST'])
@login_required
def create():
    if request.method == 'POST':
        try:
            # Generate reference number
            ref_number = 'PO-' + datetime.now().strftime('%Y%m%d') + '-' + ''.join(random.choices(string.ascii_uppercase + string.digits, k=4))

            # Get form data
            supplier_id = request.form.get('supplier_id')
            status = request.form.get('status', 'pending')
            expected_delivery = request.form.get('expected_delivery')

            if expected_delivery:
                expected_delivery = datetime.strptime(expected_delivery, '%Y-%m-%d')

            # الحصول على بيانات المنتجات
            product_ids = request.form.getlist('product_id[]')
            quantities = request.form.getlist('quantity[]')
            cost_prices = request.form.getlist('cost_price[]')

            # حساب الإجمالي
            total = 0
            for i in range(len(product_ids)):
                if product_ids[i] and quantities[i] and cost_prices[i]:
                    item_total = float(quantities[i]) * float(cost_prices[i])
                    total += item_total

            # إنشاء طلب الشراء
            purchase = Purchase(
                reference_number=ref_number,
                supplier_id=supplier_id,
                user_id=current_user.id,
                status=status,
                expected_delivery=expected_delivery,
                total=total
            )

            db.session.add(purchase)
            db.session.commit()

            # إضافة المنتجات إلى طلب الشراء
            for i in range(len(product_ids)):
                if product_ids[i] and quantities[i] and cost_prices[i]:
                    product_id = product_ids[i]
                    quantity = int(quantities[i])
                    cost_price = float(cost_prices[i])
                    item_total = quantity * cost_price

                    item = PurchaseItem(
                        purchase_id=purchase.id,
                        product_id=product_id,
                        quantity=quantity,
                        cost_price=cost_price,
                        total=item_total
                    )

                    db.session.add(item)

                    # تحديث مخزون المنتج إذا كانت الحالة "تم الاستلام"
                    if status == 'received':
                        product = Product.query.get(product_id)
                        if product:
                            product.cost_price = cost_price
                            product.stock_quantity += quantity

            db.session.commit()

            flash('تم إنشاء طلب الشراء بنجاح', 'success')
            return redirect(url_for('purchases.index'))

        except Exception as e:
            db.session.rollback()
            flash(f'حدث خطأ أثناء إنشاء طلب الشراء: {str(e)}', 'danger')

    # Get all suppliers for dropdown
    suppliers = Supplier.query.order_by(Supplier.name).all()

    return render_template('purchases/purchase_form.html', purchase=None, suppliers=suppliers, action='create')

@purchases_blueprint.route('/purchases/<int:id>/edit', methods=['GET', 'POST'])
@login_required
def edit(id):
    purchase = Purchase.query.get_or_404(id)

    if request.method == 'POST':
        try:
            # Get form data
            purchase.supplier_id = request.form.get('supplier_id')
            purchase.status = request.form.get('status', 'pending')
            expected_delivery = request.form.get('expected_delivery')

            if expected_delivery:
                purchase.expected_delivery = datetime.strptime(expected_delivery, '%Y-%m-%d')
            else:
                purchase.expected_delivery = None

            # الحصول على بيانات المنتجات
            product_ids = request.form.getlist('product_id[]')
            quantities = request.form.getlist('quantity[]')
            cost_prices = request.form.getlist('cost_price[]')

            # حذف جميع العناصر الحالية
            old_status = purchase.status
            PurchaseItem.query.filter_by(purchase_id=id).delete()

            # حساب الإجمالي الجديد
            total = 0
            for i in range(len(product_ids)):
                if product_ids[i] and quantities[i] and cost_prices[i]:
                    item_total = float(quantities[i]) * float(cost_prices[i])
                    total += item_total

            purchase.total = total

            # إضافة المنتجات الجديدة
            for i in range(len(product_ids)):
                if product_ids[i] and quantities[i] and cost_prices[i]:
                    product_id = product_ids[i]
                    quantity = int(quantities[i])
                    cost_price = float(cost_prices[i])
                    item_total = quantity * cost_price

                    item = PurchaseItem(
                        purchase_id=purchase.id,
                        product_id=product_id,
                        quantity=quantity,
                        cost_price=cost_price,
                        total=item_total
                    )

                    db.session.add(item)

                    # تحديث مخزون المنتج إذا كانت الحالة "تم الاستلام"
                    if purchase.status == 'received' and old_status != 'received':
                        product = Product.query.get(product_id)
                        if product:
                            product.cost_price = cost_price
                            product.stock_quantity += quantity

            db.session.commit()

            flash('تم تحديث طلب الشراء بنجاح', 'success')
            return redirect(url_for('purchases.index'))

        except Exception as e:
            db.session.rollback()
            flash(f'حدث خطأ أثناء تحديث طلب الشراء: {str(e)}', 'danger')

    # Get all suppliers for dropdown
    suppliers = Supplier.query.order_by(Supplier.name).all()

    # Get all products for adding to purchase
    products = Product.query.order_by(Product.name).all()

    return render_template(
        'purchases/purchase_form.html',
        purchase=purchase,
        suppliers=suppliers,
        products=products,
        action='edit'
    )

@purchases_blueprint.route('/purchases/<int:id>/delete', methods=['POST'])
@login_required
def delete(id):
    purchase = Purchase.query.get_or_404(id)

    try:
        # Delete all purchase items first
        PurchaseItem.query.filter_by(purchase_id=id).delete()

        # Then delete the purchase
        db.session.delete(purchase)
        db.session.commit()
        flash('تم حذف طلب الشراء بنجاح', 'success')
    except Exception as e:
        db.session.rollback()
        flash(f'حدث خطأ أثناء حذف طلب الشراء: {str(e)}', 'danger')

    return redirect(url_for('purchases.index'))

@purchases_blueprint.route('/purchases/<int:id>/add_item', methods=['POST'])
@login_required
def add_item(id):
    purchase = Purchase.query.get_or_404(id)

    try:
        # Get form data
        product_id = request.form.get('product_id')
        quantity = int(request.form.get('quantity', 1))
        cost_price = float(request.form.get('cost_price', 0))

        # Calculate total
        total = quantity * cost_price

        # Create purchase item
        item = PurchaseItem(
            purchase_id=id,
            product_id=product_id,
            quantity=quantity,
            cost_price=cost_price,
            total=total
        )

        db.session.add(item)

        # Update product cost price if this is a completed purchase
        if purchase.status == 'received':
            product = Product.query.get(product_id)
            product.cost_price = cost_price
            product.stock_quantity += quantity

        # Update purchase total
        purchase.total += total

        db.session.commit()

        flash('تم إضافة المنتج إلى طلب الشراء بنجاح', 'success')
    except Exception as e:
        db.session.rollback()
        flash(f'حدث خطأ أثناء إضافة المنتج: {str(e)}', 'danger')

    return redirect(url_for('purchases.edit', id=id))

@purchases_blueprint.route('/purchases/<int:id>/remove_item/<int:item_id>', methods=['POST'])
@login_required
def remove_item(id, item_id):
    purchase = Purchase.query.get_or_404(id)
    item = PurchaseItem.query.get_or_404(item_id)

    try:
        # Update purchase total
        purchase.total -= item.total

        # If purchase was received, update stock
        if purchase.status == 'received':
            product = Product.query.get(item.product_id)
            product.stock_quantity -= item.quantity

        # Delete item
        db.session.delete(item)
        db.session.commit()

        flash('تم حذف المنتج من طلب الشراء بنجاح', 'success')
    except Exception as e:
        db.session.rollback()
        flash(f'حدث خطأ أثناء حذف المنتج: {str(e)}', 'danger')

    return redirect(url_for('purchases.edit', id=id))

@purchases_blueprint.route('/purchases/<int:id>/receive', methods=['POST'])
@login_required
def receive(id):
    purchase = Purchase.query.get_or_404(id)

    if purchase.status == 'received':
        flash('طلب الشراء مستلم بالفعل', 'warning')
        return redirect(url_for('purchases.edit', id=id))

    try:
        # Update purchase status
        purchase.status = 'received'

        # Update product stock quantities
        for item in purchase.items:
            product = Product.query.get(item.product_id)
            product.stock_quantity += item.quantity
            product.cost_price = item.cost_price

        db.session.commit()

        flash('تم استلام طلب الشراء بنجاح وتحديث المخزون', 'success')
    except Exception as e:
        db.session.rollback()
        flash(f'حدث خطأ أثناء استلام طلب الشراء: {str(e)}', 'danger')

    return redirect(url_for('purchases.edit', id=id))

@purchases_blueprint.route('/purchases/<int:id>/details')
@login_required
def details(id):
    purchase = Purchase.query.get_or_404(id)
    return render_template('purchases/purchase_details.html', purchase=purchase, current_user=current_user)


@purchases_blueprint.route('/purchases/auto-order')
@login_required
def auto_order():
    """صفحة طلب الشراء التلقائي للمنتجات المنخفضة أو المنتهية من المخزون"""
    # التحقق من صلاحيات المستخدم
    if not current_user.has_permission('purchases', 'add'):
        flash('ليس لديك صلاحية لإنشاء طلب شراء', 'error')
        return redirect(url_for('purchases.index'))

    # الحصول على المخازن
    warehouses = Warehouse.query.filter_by(is_active=True).all()

    # الحصول على الموردين
    suppliers = Supplier.query.order_by(Supplier.name).all()

    # الحصول على المنتجات المنخفضة أو المنتهية من المخزون
    warehouse_id = request.args.get('warehouse_id')

    low_stock_products = []
    out_of_stock_products = []

    if warehouse_id:
        # الحصول على المنتجات المنخفضة في مخزن محدد
        low_stock_items = Inventory.get_low_stock(warehouse_id=warehouse_id)
        for item in low_stock_items:
            product = Product.query.get(item.product_id)
            if product and product.is_active:
                low_stock_products.append({
                    'product': product,
                    'inventory': item,
                    'warehouse': item.warehouse
                })

        # الحصول على المنتجات المنتهية في مخزن محدد
        out_of_stock_items = Inventory.get_out_of_stock(warehouse_id=warehouse_id)
        for item in out_of_stock_items:
            product = Product.query.get(item.product_id)
            if product and product.is_active:
                out_of_stock_products.append({
                    'product': product,
                    'inventory': item,
                    'warehouse': item.warehouse
                })
    else:
        # الحصول على المنتجات المنخفضة في جميع المخازن
        low_stock_items = Inventory.get_low_stock()
        for item in low_stock_items:
            product = Product.query.get(item.product_id)
            if product and product.is_active:
                low_stock_products.append({
                    'product': product,
                    'inventory': item,
                    'warehouse': item.warehouse
                })

        # الحصول على المنتجات المنتهية في جميع المخازن
        out_of_stock_items = Inventory.get_out_of_stock()
        for item in out_of_stock_items:
            product = Product.query.get(item.product_id)
            if product and product.is_active:
                out_of_stock_products.append({
                    'product': product,
                    'inventory': item,
                    'warehouse': item.warehouse
                })

    return render_template(
        'purchases/auto_order.html',
        warehouses=warehouses,
        suppliers=suppliers,
        low_stock_products=low_stock_products,
        out_of_stock_products=out_of_stock_products,
        selected_warehouse=warehouse_id
    )


@purchases_blueprint.route('/purchases/auto-order/create', methods=['POST'])
@login_required
def create_auto_order():
    """إنشاء طلب شراء تلقائي"""
    # التحقق من صلاحيات المستخدم
    if not current_user.has_permission('purchases', 'add'):
        flash('ليس لديك صلاحية لإنشاء طلب شراء', 'error')
        return redirect(url_for('purchases.index'))

    try:
        # الحصول على بيانات النموذج
        supplier_id = request.form.get('supplier_id')
        status = request.form.get('status', 'pending')
        expected_delivery = request.form.get('expected_delivery')

        if not supplier_id:
            flash('يرجى اختيار المورد', 'error')
            return redirect(url_for('purchases.auto_order'))

        if expected_delivery:
            expected_delivery = datetime.strptime(expected_delivery, '%Y-%m-%d')

        # الحصول على المنتجات المحددة
        product_ids = request.form.getlist('product_id[]')
        quantities = request.form.getlist('quantity[]')

        if not product_ids or len(product_ids) == 0:
            flash('يرجى اختيار منتج واحد على الأقل', 'error')
            return redirect(url_for('purchases.auto_order'))

        # إنشاء رقم مرجعي
        ref_number = 'PO-AUTO-' + datetime.now().strftime('%Y%m%d') + '-' + ''.join(random.choices(string.ascii_uppercase + string.digits, k=4))

        # حساب الإجمالي
        total = 0
        purchase_items = []

        for i in range(len(product_ids)):
            if product_ids[i] and quantities[i]:
                product_id = int(product_ids[i])
                quantity = int(quantities[i])

                # الحصول على المنتج وسعر التكلفة
                product = Product.query.get(product_id)
                if not product:
                    continue

                cost_price = product.cost_price or 0
                item_total = quantity * cost_price

                purchase_items.append({
                    'product_id': product_id,
                    'quantity': quantity,
                    'cost_price': cost_price,
                    'total': item_total
                })

                total += item_total

        # إنشاء طلب الشراء
        purchase = Purchase(
            reference_number=ref_number,
            supplier_id=supplier_id,
            user_id=current_user.id,
            status=status,
            expected_delivery=expected_delivery,
            total=total
        )

        db.session.add(purchase)
        db.session.commit()

        # إضافة المنتجات إلى طلب الشراء
        for item in purchase_items:
            purchase_item = PurchaseItem(
                purchase_id=purchase.id,
                product_id=item['product_id'],
                quantity=item['quantity'],
                cost_price=item['cost_price'],
                total=item['total']
            )

            db.session.add(purchase_item)

            # تحديث مخزون المنتج إذا كانت الحالة "تم الاستلام"
            if status == 'received':
                product = Product.query.get(item['product_id'])
                if product:
                    product.cost_price = item['cost_price']
                    product.stock_quantity += item['quantity']

        db.session.commit()

        flash('تم إنشاء طلب الشراء التلقائي بنجاح', 'success')
        return redirect(url_for('purchases.details', id=purchase.id))

    except Exception as e:
        db.session.rollback()
        flash(f'حدث خطأ أثناء إنشاء طلب الشراء: {str(e)}', 'error')
        return redirect(url_for('purchases.auto_order'))


@purchases_blueprint.route('/purchases/supplier/<int:supplier_id>/order')
@login_required
def supplier_order(supplier_id):
    """صفحة طلب الشراء حسب المورد"""
    # التحقق من صلاحيات المستخدم
    if not current_user.has_permission('purchases', 'add'):
        flash('ليس لديك صلاحية لإنشاء طلب شراء', 'error')
        return redirect(url_for('purchases.index'))

    # الحصول على المورد
    supplier = Supplier.query.get_or_404(supplier_id)

    # الحصول على المنتجات المرتبطة بالمورد
    products = Product.query.filter_by(supplier_id=supplier_id, is_active=True).all()

    # الحصول على معلومات المخزون لكل منتج
    product_inventory = []

    for product in products:
        # الحصول على مخزون المنتج في جميع المخازن
        inventories = Inventory.query.filter_by(product_id=product.id).all()

        # حساب إجمالي المخزون
        total_quantity = sum(inv.quantity for inv in inventories)

        # تحديد حالة المخزون
        status = 'in_stock'
        if total_quantity <= 0:
            status = 'out_of_stock'
        elif total_quantity <= product.minimum_stock:
            status = 'low_stock'

        product_inventory.append({
            'product': product,
            'total_quantity': total_quantity,
            'status': status,
            'inventories': inventories
        })

    return render_template(
        'purchases/supplier_order.html',
        supplier=supplier,
        product_inventory=product_inventory
    )