"""
نماذج نظام التقارير المتقدم
"""

from datetime import datetime
from app import db
from sqlalchemy.orm import relationship


class CustomReport(db.Model):
    """نموذج التقارير المخصصة (Custom Reports)"""
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(128), nullable=False)
    description = db.Column(db.Text)
    report_type = db.Column(db.String(64), nullable=False, index=True)  # sales, inventory, financial, customer, supplier
    query = db.Column(db.Text, nullable=False)
    parameters = db.Column(db.Text)  # JSON string
    columns = db.Column(db.Text)  # JSON string
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), index=True)
    is_public = db.Column(db.<PERSON>, default=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # العلاقات
    user = relationship('User', backref='custom_reports')
    
    def __repr__(self):
        return f"<CustomReport {self.name}>"


class ScheduledSalesReport(db.Model):
    """نموذج تقارير المبيعات المجدولة (Scheduled Sales Reports)"""
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(128), nullable=False)
    description = db.Column(db.Text)
    frequency = db.Column(db.String(20), nullable=False, index=True)  # daily, weekly, monthly
    recipients = db.Column(db.Text, nullable=False)  # JSON string of email addresses
    report_format = db.Column(db.String(20), nullable=False)  # pdf, excel, csv
    parameters = db.Column(db.Text)  # JSON string
    last_run = db.Column(db.DateTime)
    next_run = db.Column(db.DateTime)
    is_active = db.Column(db.Boolean, default=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    def __repr__(self):
        return f"<ScheduledSalesReport {self.name}>"


class ScheduledInventoryReport(db.Model):
    """نموذج تقارير المخزون المجدولة (Scheduled Inventory Reports)"""
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(128), nullable=False)
    description = db.Column(db.Text)
    frequency = db.Column(db.String(20), nullable=False, index=True)  # daily, weekly, monthly
    recipients = db.Column(db.Text, nullable=False)  # JSON string of email addresses
    report_format = db.Column(db.String(20), nullable=False)  # pdf, excel, csv
    parameters = db.Column(db.Text)  # JSON string
    last_run = db.Column(db.DateTime)
    next_run = db.Column(db.DateTime)
    is_active = db.Column(db.Boolean, default=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    def __repr__(self):
        return f"<ScheduledInventoryReport {self.name}>"


class ScheduledFinancialReport(db.Model):
    """نموذج تقارير المالية المجدولة (Scheduled Financial Reports)"""
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(128), nullable=False)
    description = db.Column(db.Text)
    frequency = db.Column(db.String(20), nullable=False, index=True)  # daily, weekly, monthly
    recipients = db.Column(db.Text, nullable=False)  # JSON string of email addresses
    report_format = db.Column(db.String(20), nullable=False)  # pdf, excel, csv
    report_type = db.Column(db.String(64), nullable=False, index=True)  # balance_sheet, income_statement, cash_flow
    parameters = db.Column(db.Text)  # JSON string
    last_run = db.Column(db.DateTime)
    next_run = db.Column(db.DateTime)
    is_active = db.Column(db.Boolean, default=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    def __repr__(self):
        return f"<ScheduledFinancialReport {self.name}>"


class ScheduledCustomerReport(db.Model):
    """نموذج تقارير العملاء المجدولة (Scheduled Customer Reports)"""
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(128), nullable=False)
    description = db.Column(db.Text)
    frequency = db.Column(db.String(20), nullable=False, index=True)  # daily, weekly, monthly
    recipients = db.Column(db.Text, nullable=False)  # JSON string of email addresses
    report_format = db.Column(db.String(20), nullable=False)  # pdf, excel, csv
    parameters = db.Column(db.Text)  # JSON string
    last_run = db.Column(db.DateTime)
    next_run = db.Column(db.DateTime)
    is_active = db.Column(db.Boolean, default=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    def __repr__(self):
        return f"<ScheduledCustomerReport {self.name}>"


class ScheduledSupplierReport(db.Model):
    """نموذج تقارير الموردين المجدولة (Scheduled Supplier Reports)"""
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(128), nullable=False)
    description = db.Column(db.Text)
    frequency = db.Column(db.String(20), nullable=False, index=True)  # daily, weekly, monthly
    recipients = db.Column(db.Text, nullable=False)  # JSON string of email addresses
    report_format = db.Column(db.String(20), nullable=False)  # pdf, excel, csv
    parameters = db.Column(db.Text)  # JSON string
    last_run = db.Column(db.DateTime)
    next_run = db.Column(db.DateTime)
    is_active = db.Column(db.Boolean, default=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    def __repr__(self):
        return f"<ScheduledSupplierReport {self.name}>"


class ReportExecutionLog(db.Model):
    """نموذج سجل تنفيذ التقارير (Report Execution Log)"""
    id = db.Column(db.Integer, primary_key=True)
    report_type = db.Column(db.String(64), nullable=False, index=True)  # custom, sales, inventory, financial, customer, supplier
    report_id = db.Column(db.Integer)
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), index=True)
    status = db.Column(db.String(20), nullable=False, index=True)  # success, failed
    error_message = db.Column(db.Text)
    execution_time = db.Column(db.Float)  # بالثواني
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    # العلاقات
    user = relationship('User', backref='report_executions')
    
    def __repr__(self):
        return f"<ReportExecutionLog {self.id}: {self.report_type} - {self.status}>"
