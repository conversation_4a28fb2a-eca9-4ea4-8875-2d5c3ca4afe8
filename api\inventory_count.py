from flask import Blueprint, jsonify, request
from flask_login import login_required, current_user
from models import InventoryCount, InventoryCountItem, Inventory, Product, Warehouse, db
from sqlalchemy import or_, and_
from datetime import datetime

inventory_count_api = Blueprint('inventory_count_api', __name__)

@inventory_count_api.route('/api/inventory-counts', methods=['GET'])
@login_required
def get_inventory_counts():
    """
    الحصول على قائمة عمليات الجرد الدوري
    
    معلمات الاستعلام:
    - warehouse_id: معرف المستودع (اختياري)
    - status: حالة الجرد (draft, in_progress, completed, cancelled) (اختياري)
    - date_from: تاريخ البداية (اختياري)
    - date_to: تاريخ النهاية (اختياري)
    - page: رقم الصفحة (افتراضي: 1)
    - per_page: عدد العناصر في الصفحة (افتراضي: 20)
    """
    try:
        # الحصول على معلمات الاستعلام
        warehouse_id = request.args.get('warehouse_id', type=int)
        status = request.args.get('status', '')
        date_from = request.args.get('date_from', '')
        date_to = request.args.get('date_to', '')
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 20, type=int)
        
        # إعداد الاستعلام الأساسي
        query = InventoryCount.query
        
        # تطبيق الفلاتر
        if warehouse_id:
            query = query.filter_by(warehouse_id=warehouse_id)
        
        if status:
            query = query.filter_by(status=status)
        
        if date_from:
            try:
                date_from = datetime.strptime(date_from, '%Y-%m-%d')
                query = query.filter(InventoryCount.created_at >= date_from)
            except:
                pass
        
        if date_to:
            try:
                from datetime import timedelta
                date_to = datetime.strptime(date_to, '%Y-%m-%d')
                date_to = date_to + timedelta(days=1)  # لتضمين اليوم المحدد كاملاً
                query = query.filter(InventoryCount.created_at < date_to)
            except:
                pass
        
        # ترتيب النتائج
        query = query.order_by(InventoryCount.created_at.desc())
        
        # تطبيق الترقيم
        pagination = query.paginate(page=page, per_page=per_page, error_out=False)
        
        # إعداد البيانات للاستجابة
        counts = []
        for count in pagination.items:
            counts.append({
                'id': count.id,
                'name': count.name,
                'warehouse_id': count.warehouse_id,
                'warehouse_name': count.warehouse.name if count.warehouse else None,
                'status': count.status,
                'created_by': count.user.username if count.user else None,
                'created_at': count.created_at.strftime('%Y-%m-%d %H:%M:%S'),
                'completed_at': count.completed_at.strftime('%Y-%m-%d %H:%M:%S') if count.completed_at else None,
                'items_count': len(count.items)
            })
        
        # إعداد معلومات الترقيم
        pagination_info = {
            'page': pagination.page,
            'per_page': pagination.per_page,
            'total_pages': pagination.pages,
            'total_items': pagination.total
        }
        
        return jsonify({
            'success': True,
            'counts': counts,
            'pagination': pagination_info
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'حدث خطأ أثناء جلب قائمة عمليات الجرد: {str(e)}'
        }), 500

@inventory_count_api.route('/api/inventory-counts/<int:id>', methods=['GET'])
@login_required
def get_inventory_count(id):
    """الحصول على تفاصيل عملية جرد محددة"""
    try:
        count = InventoryCount.query.get_or_404(id)
        
        # إعداد بيانات عملية الجرد
        count_data = {
            'id': count.id,
            'name': count.name,
            'warehouse_id': count.warehouse_id,
            'warehouse_name': count.warehouse.name if count.warehouse else None,
            'status': count.status,
            'notes': count.notes,
            'created_by': count.user.username if count.user else None,
            'created_at': count.created_at.strftime('%Y-%m-%d %H:%M:%S'),
            'completed_at': count.completed_at.strftime('%Y-%m-%d %H:%M:%S') if count.completed_at else None
        }
        
        # إعداد بيانات عناصر الجرد
        items = []
        for item in count.items:
            items.append({
                'id': item.id,
                'product_id': item.product_id,
                'product_name': item.product.name if item.product else None,
                'expected_quantity': item.expected_quantity,
                'actual_quantity': item.actual_quantity,
                'difference': item.difference,
                'notes': item.notes
            })
        
        return jsonify({
            'success': True,
            'count': count_data,
            'items': items
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'حدث خطأ أثناء جلب تفاصيل عملية الجرد: {str(e)}'
        }), 500

@inventory_count_api.route('/api/inventory-counts', methods=['POST'])
@login_required
def create_inventory_count():
    """إنشاء عملية جرد جديدة"""
    try:
        data = request.json
        
        # التحقق من البيانات المطلوبة
        if not data or 'name' not in data or 'warehouse_id' not in data:
            return jsonify({
                'success': False,
                'message': 'يجب توفير اسم عملية الجرد ومعرف المستودع'
            }), 400
        
        # التحقق من وجود المستودع
        warehouse = Warehouse.query.get(data['warehouse_id'])
        if not warehouse:
            return jsonify({
                'success': False,
                'message': 'المستودع غير موجود'
            }), 404
        
        # إنشاء عملية الجرد
        count = InventoryCount(
            name=data['name'],
            warehouse_id=data['warehouse_id'],
            status='draft',
            notes=data.get('notes', ''),
            created_by=current_user.id
        )
        
        db.session.add(count)
        db.session.flush()  # للحصول على معرف عملية الجرد
        
        # إضافة عناصر الجرد
        # الحصول على جميع المنتجات في المستودع
        inventories = Inventory.query.filter_by(warehouse_id=data['warehouse_id']).all()
        
        for inventory in inventories:
            count_item = InventoryCountItem(
                count_id=count.id,
                product_id=inventory.product_id,
                expected_quantity=inventory.quantity
            )
            db.session.add(count_item)
        
        db.session.commit()
        
        return jsonify({
            'success': True,
            'message': 'تم إنشاء عملية الجرد بنجاح',
            'count_id': count.id
        })
    except Exception as e:
        db.session.rollback()
        return jsonify({
            'success': False,
            'message': f'حدث خطأ أثناء إنشاء عملية الجرد: {str(e)}'
        }), 500

@inventory_count_api.route('/api/inventory-counts/<int:id>/update-status', methods=['POST'])
@login_required
def update_inventory_count_status(id):
    """تحديث حالة عملية جرد"""
    try:
        count = InventoryCount.query.get_or_404(id)
        data = request.json
        
        # التحقق من البيانات المطلوبة
        if not data or 'status' not in data:
            return jsonify({
                'success': False,
                'message': 'يجب توفير الحالة الجديدة'
            }), 400
        
        new_status = data['status']
        
        # التحقق من صحة الحالة
        valid_statuses = ['draft', 'in_progress', 'completed', 'cancelled']
        if new_status not in valid_statuses:
            return jsonify({
                'success': False,
                'message': f'الحالة غير صالحة. الحالات الصالحة هي: {", ".join(valid_statuses)}'
            }), 400
        
        # تحديث الحالة
        count.status = new_status
        
        # إذا تم تغيير الحالة إلى مكتمل، قم بتحديث تاريخ الاكتمال
        if new_status == 'completed':
            count.completed_at = datetime.now()
        
        # تحديث الملاحظات إذا تم توفيرها
        if 'notes' in data:
            count.notes = data['notes']
        
        db.session.commit()
        
        return jsonify({
            'success': True,
            'message': 'تم تحديث حالة عملية الجرد بنجاح'
        })
    except Exception as e:
        db.session.rollback()
        return jsonify({
            'success': False,
            'message': f'حدث خطأ أثناء تحديث حالة عملية الجرد: {str(e)}'
        }), 500

@inventory_count_api.route('/api/inventory-counts/<int:id>/items/<int:item_id>', methods=['PUT'])
@login_required
def update_inventory_count_item(id, item_id):
    """تحديث عنصر في عملية جرد"""
    try:
        count = InventoryCount.query.get_or_404(id)
        item = InventoryCountItem.query.get_or_404(item_id)
        
        # التحقق من أن العنصر ينتمي إلى عملية الجرد
        if item.count_id != count.id:
            return jsonify({
                'success': False,
                'message': 'العنصر لا ينتمي إلى عملية الجرد المحددة'
            }), 400
        
        # التحقق من أن عملية الجرد في حالة تسمح بالتعديل
        if count.status not in ['draft', 'in_progress']:
            return jsonify({
                'success': False,
                'message': 'لا يمكن تعديل عناصر عملية الجرد في الحالة الحالية'
            }), 400
        
        data = request.json
        
        # التحقق من البيانات المطلوبة
        if not data or 'actual_quantity' not in data:
            return jsonify({
                'success': False,
                'message': 'يجب توفير الكمية الفعلية'
            }), 400
        
        # تحديث الكمية الفعلية
        actual_quantity = int(data['actual_quantity'])
        item.actual_quantity = actual_quantity
        
        # حساب الفرق
        item.difference = actual_quantity - item.expected_quantity
        
        # تحديث الملاحظات إذا تم توفيرها
        if 'notes' in data:
            item.notes = data['notes']
        
        db.session.commit()
        
        return jsonify({
            'success': True,
            'message': 'تم تحديث عنصر الجرد بنجاح',
            'item': {
                'id': item.id,
                'product_id': item.product_id,
                'product_name': item.product.name if item.product else None,
                'expected_quantity': item.expected_quantity,
                'actual_quantity': item.actual_quantity,
                'difference': item.difference,
                'notes': item.notes
            }
        })
    except Exception as e:
        db.session.rollback()
        return jsonify({
            'success': False,
            'message': f'حدث خطأ أثناء تحديث عنصر الجرد: {str(e)}'
        }), 500

def register_inventory_count_api(app):
    """تسجيل واجهة برمجة التطبيقات للجرد الدوري"""
    app.register_blueprint(inventory_count_api)
