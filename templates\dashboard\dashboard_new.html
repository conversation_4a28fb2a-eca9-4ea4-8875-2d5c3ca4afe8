{% extends 'core/base.html' %}

{% block extra_head %}
    <script src="https://cdn.jsdelivr.net/npm/echarts@5.4.2/dist/echarts.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/apexcharts"></script>
    <style>
        /* تصميم عام */
        .dashboard-container {
            max-width: 1920px;
            margin: 0 auto;
        }

        /* تصميم الشريط العلوي */
        .dashboard-header {
            position: relative;
            border-radius: 16px;
            overflow: hidden;
            background: linear-gradient(to left, #3b82f6, #2563eb);
            box-shadow: 0 10px 25px -5px rgba(59, 130, 246, 0.5);
        }

        .dashboard-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-image: url("data:image/svg+xml,%3Csvg width='100' height='100' viewBox='0 0 100 100' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M11 18c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm48 25c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm-43-7c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm63 31c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM34 90c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm56-76c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM12 86c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm28-65c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm23-11c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-6 60c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm29 22c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zM32 63c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm57-13c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-9-21c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM60 91c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM35 41c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM12 60c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2z' fill='%23ffffff' fill-opacity='0.05' fill-rule='evenodd'/%3E%3C/svg%3E");
            opacity: 0.8;
        }

        /* تصميم البطاقات */
        .stat-card {
            position: relative;
            border-radius: 16px;
            overflow: hidden;
            transition: all 0.3s ease;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
        }

        .stat-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
        }

        .stat-card::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            width: 100%;
            height: 4px;
            background: linear-gradient(to right, var(--card-accent-color), transparent);
        }

        .stat-card.sales {
            --card-accent-color: #3b82f6;
        }

        .stat-card.purchases {
            --card-accent-color: #8b5cf6;
        }

        .stat-card.profit {
            --card-accent-color: #10b981;
        }

        .stat-card.returns {
            --card-accent-color: #f59e0b;
        }

        .stat-card.inventory {
            --card-accent-color: #ef4444;
        }

        .stat-card.customers {
            --card-accent-color: #ec4899;
        }

        .stat-icon {
            transition: all 0.3s ease;
        }

        .stat-card:hover .stat-icon {
            transform: scale(1.1) rotate(10deg);
        }

        /* تصميم الرسوم البيانية */
        .chart-container {
            border-radius: 16px;
            overflow: hidden;
            transition: all 0.3s ease;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
        }

        .chart-container:hover {
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
        }

        /* تصميم الجداول */
        .data-table {
            border-radius: 12px;
            overflow: hidden;
        }

        .data-table th {
            background-color: #f9fafb;
            font-weight: 600;
        }

        .data-table tr {
            transition: all 0.2s ease;
        }

        .data-table tr:hover {
            background-color: #f3f4f6;
        }

        /* تصميم القائمة الجانبية */
        .dashboard-sidebar {
            position: sticky;
            top: 1.5rem;
            height: calc(100vh - 3rem);
        }

        .sidebar-menu {
            border-radius: 12px;
            overflow: hidden;
        }

        .sidebar-menu-item {
            transition: all 0.2s ease;
            border-right: 3px solid transparent;
        }

        .sidebar-menu-item:hover, .sidebar-menu-item.active {
            background-color: rgba(59, 130, 246, 0.1);
            border-right-color: #3b82f6;
        }

        /* تأثيرات حركية */
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .fade-in-up {
            animation: fadeInUp 0.5s ease-out forwards;
        }

        .fade-in-up-1 { animation-delay: 0.1s; }
        .fade-in-up-2 { animation-delay: 0.2s; }
        .fade-in-up-3 { animation-delay: 0.3s; }
        .fade-in-up-4 { animation-delay: 0.4s; }

        /* توافق وضع الدارك مود */
        .dark-mode .dashboard-header {
            background: linear-gradient(to left, #1e40af, #1e3a8a);
        }

        .dark-mode .stat-card {
            background-color: #1e293b;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.2), 0 2px 4px -1px rgba(0, 0, 0, 0.1);
        }

        .dark-mode .chart-container {
            background-color: #1e293b;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.2), 0 2px 4px -1px rgba(0, 0, 0, 0.1);
        }

        .dark-mode .data-table th {
            background-color: #334155;
        }

        .dark-mode .data-table tr:hover {
            background-color: #334155;
        }

        .dark-mode .sidebar-menu {
            background-color: #1e293b;
        }

        .dark-mode .sidebar-menu-item:hover, .dark-mode .sidebar-menu-item.active {
            background-color: rgba(59, 130, 246, 0.2);
        }
    </style>
{% endblock %}

{% block content %}
    <div class="dashboard-container">
        <!-- شريط التنقل العلوي -->
        <div class="dashboard-header p-6 mb-6 fade-in-up">
            <div class="flex flex-wrap justify-between items-center relative z-10">
                <div>
                    <h1 class="text-3xl font-bold text-white mb-2">لوحة التحكم</h1>
                    <p class="text-blue-100 flex items-center">
                        <i class="ri-calendar-line mr-1"></i>
                        مرحباً بك في نظام Nobara، اليوم هو {{ current_date }}
                    </p>
                </div>
                <div class="flex items-center space-x-2 space-x-reverse">
                    <button id="refresh-dashboard" class="px-4 py-2 bg-white bg-opacity-10 backdrop-blur-sm border border-white border-opacity-20 rounded-lg shadow-sm hover:bg-opacity-20 text-sm font-medium text-white flex items-center space-x-1 space-x-reverse mr-2 transition-all duration-300">
                        <i class="ri-refresh-line"></i>
                        <span class="mr-1">تحديث</span>
                    </button>
                    <button id="dark-mode-toggle-dashboard" class="px-4 py-2 bg-white bg-opacity-10 backdrop-blur-sm border border-white border-opacity-20 rounded-lg shadow-sm hover:bg-opacity-20 text-sm font-medium text-white flex items-center space-x-1 space-x-reverse mr-2 transition-all duration-300">
                        <i class="ri-moon-line"></i>
                        <span class="mr-1">الوضع الداكن</span>
                    </button>
                    <button id="customize-dashboard" class="px-4 py-2 bg-white bg-opacity-10 backdrop-blur-sm border border-white border-opacity-20 rounded-lg shadow-sm hover:bg-opacity-20 text-sm font-medium text-white flex items-center space-x-1 space-x-reverse transition-all duration-300">
                        <i class="ri-settings-4-line"></i>
                        <span class="mr-1">تخصيص لوحة التحكم</span>
                    </button>
                </div>
            </div>
        </div>

        <div class="flex flex-wrap lg:flex-nowrap gap-6">
            <!-- القائمة الجانبية -->
            <div class="w-full lg:w-64 dashboard-sidebar">
                <div class="bg-white rounded-xl shadow-md p-4 mb-6 sidebar-menu">
                    <h3 class="font-bold text-gray-800 mb-4 px-2">أقسام لوحة التحكم</h3>
                    <ul>
                        <li class="sidebar-menu-item active">
                            <a href="#overview" class="flex items-center px-4 py-3 text-primary">
                                <i class="ri-dashboard-line mr-2"></i>
                                <span>نظرة عامة</span>
                            </a>
                        </li>
                        <li class="sidebar-menu-item">
                            <a href="#sales" class="flex items-center px-4 py-3 text-gray-700 hover:text-primary">
                                <i class="ri-shopping-cart-2-line mr-2"></i>
                                <span>المبيعات</span>
                            </a>
                        </li>
                        <li class="sidebar-menu-item">
                            <a href="#purchases" class="flex items-center px-4 py-3 text-gray-700 hover:text-primary">
                                <i class="ri-shopping-bag-3-line mr-2"></i>
                                <span>المشتريات</span>
                            </a>
                        </li>
                        <li class="sidebar-menu-item">
                            <a href="#inventory" class="flex items-center px-4 py-3 text-gray-700 hover:text-primary">
                                <i class="ri-store-2-line mr-2"></i>
                                <span>المخزون</span>
                            </a>
                        </li>
                        <li class="sidebar-menu-item">
                            <a href="#customers" class="flex items-center px-4 py-3 text-gray-700 hover:text-primary">
                                <i class="ri-user-3-line mr-2"></i>
                                <span>العملاء</span>
                            </a>
                        </li>
                        <li class="sidebar-menu-item">
                            <a href="#reports" class="flex items-center px-4 py-3 text-gray-700 hover:text-primary">
                                <i class="ri-file-chart-line mr-2"></i>
                                <span>التقارير</span>
                            </a>
                        </li>
                    </ul>
                </div>

                <div class="bg-white rounded-xl shadow-md p-4 sidebar-menu">
                    <h3 class="font-bold text-gray-800 mb-4 px-2">الوصول السريع</h3>
                    <ul>
                        <li class="sidebar-menu-item">
                            <a href="{{ url_for('pos.index') }}" class="flex items-center px-4 py-3 text-gray-700 hover:text-primary">
                                <i class="ri-shopping-basket-line mr-2"></i>
                                <span>نقطة البيع</span>
                            </a>
                        </li>
                        <li class="sidebar-menu-item">
                            <a href="{{ url_for('products.create') }}" class="flex items-center px-4 py-3 text-gray-700 hover:text-primary">
                                <i class="ri-add-circle-line mr-2"></i>
                                <span>إضافة منتج</span>
                            </a>
                        </li>
                        <li class="sidebar-menu-item">
                            <a href="{{ url_for('customers.create') }}" class="flex items-center px-4 py-3 text-gray-700 hover:text-primary">
                                <i class="ri-user-add-line mr-2"></i>
                                <span>إضافة عميل</span>
                            </a>
                        </li>
                        <li class="sidebar-menu-item">
                            <a href="{{ url_for('reports.index') }}" class="flex items-center px-4 py-3 text-gray-700 hover:text-primary">
                                <i class="ri-printer-line mr-2"></i>
                                <span>طباعة تقرير</span>
                            </a>
                        </li>
                    </ul>
                </div>
            </div>

            <!-- المحتوى الرئيسي -->
            <div class="flex-1">
                <!-- قسم نظرة عامة -->
                <section id="overview" class="mb-8">
                    <h2 class="text-xl font-bold text-gray-800 mb-6">نظرة عامة</h2>

                    <!-- بطاقات الإحصائيات - الصف الأول -->
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-6">
                        <!-- بطاقة المبيعات اليومية -->
                        <div id="daily-sales-card" class="stat-card sales bg-white p-5 fade-in-up fade-in-up-1">
                            <div class="flex justify-between">
                                <div>
                                    <h3 class="text-gray-500 text-sm flex items-center">
                                        المبيعات اليومية
                                        <span class="tooltip-trigger ml-1 cursor-help text-gray-400 hover:text-gray-600" data-tooltip="إجمالي المبيعات التي تمت اليوم">
                                            <i class="ri-information-line"></i>
                                        </span>
                                    </h3>
                                    <p class="text-2xl font-bold mt-2">{{ stats.today_sales|default(0)|round(2) }} ج.م</p>
                                </div>
                                <div class="w-12 h-12 rounded-full bg-blue-100 flex items-center justify-center text-primary stat-icon">
                                    <i class="ri-shopping-cart-2-line text-xl"></i>
                                </div>
                            </div>
                            <div class="mt-4 text-sm">
                                <div class="flex items-center">
                                    <span class="inline-block w-2 h-2 rounded-full bg-blue-500 mr-1"></span>
                                    <span class="text-blue-500 font-medium">{{ stats.today_orders }} طلب</span>
                                    <span class="text-gray-500 mr-1">اليوم</span>
                                </div>
                                <div class="w-full bg-gray-100 rounded-full h-1 mt-2">
                                    <div class="bg-blue-500 h-1 rounded-full" style="width: {{ stats.today_sales_percentage|default(0) }}%"></div>
                                </div>
                            </div>
                            <a href="{{ url_for('sales.index') }}" class="mt-3 text-xs text-primary hover:text-indigo-700 flex items-center justify-end transition-colors duration-300">
                                عرض التفاصيل <i class="ri-arrow-left-line mr-1"></i>
                            </a>
                        </div>

                        <!-- بطاقة إجمالي المبيعات -->
                        <div id="total-sales-card" class="stat-card sales bg-white p-5 fade-in-up fade-in-up-2">
                            <div class="flex justify-between">
                                <div>
                                    <h3 class="text-gray-500 text-sm flex items-center">
                                        إجمالي المبيعات
                                        <span class="tooltip-trigger ml-1 cursor-help text-gray-400 hover:text-gray-600" data-tooltip="إجمالي المبيعات منذ بداية النشاط">
                                            <i class="ri-information-line"></i>
                                        </span>
                                    </h3>
                                    <p class="text-2xl font-bold mt-2">{{ stats.total_sales|default(0)|round(2) }} ج.م</p>
                                </div>
                                <div class="w-12 h-12 rounded-full bg-green-100 flex items-center justify-center text-green-600 stat-icon">
                                    <i class="ri-line-chart-line text-xl"></i>
                                </div>
                            </div>
                            <div class="mt-4 text-sm">
                                <div class="flex items-center">
                                    <span class="inline-block w-2 h-2 rounded-full bg-green-500 mr-1"></span>
                                    <span class="text-green-500 font-medium">{{ stats.monthly_sales|default(0)|round(2) }} ج.م</span>
                                    <span class="text-gray-500 mr-1">هذا الشهر</span>
                                </div>
                                <div class="w-full bg-gray-100 rounded-full h-1 mt-2">
                                    <div class="bg-green-500 h-1 rounded-full" style="width: {{ stats.monthly_sales_percentage|default(0) }}%"></div>
                                </div>
                            </div>
                            <a href="{{ url_for('reports.index') }}" class="mt-3 text-xs text-primary hover:text-indigo-700 flex items-center justify-end transition-colors duration-300">
                                عرض التقرير <i class="ri-arrow-left-line mr-1"></i>
                            </a>
                        </div>

                        <!-- بطاقة الأرباح -->
                        <div id="profit-card" class="stat-card profit bg-white p-5 fade-in-up fade-in-up-3">
                            <div class="flex justify-between">
                                <div>
                                    <h3 class="text-gray-500 text-sm flex items-center">
                                        الأرباح
                                        <span class="tooltip-trigger ml-1 cursor-help text-gray-400 hover:text-gray-600" data-tooltip="إجمالي الأرباح المحققة من المبيعات">
                                            <i class="ri-information-line"></i>
                                        </span>
                                    </h3>
                                    <p class="text-2xl font-bold mt-2">{{ stats.profit|default(0)|round(2) }} ج.م</p>
                                </div>
                                <div class="w-12 h-12 rounded-full bg-emerald-100 flex items-center justify-center text-emerald-600 stat-icon">
                                    <i class="ri-funds-line text-xl"></i>
                                </div>
                            </div>
                            <div class="mt-4 text-sm">
                                <div class="flex items-center">
                                    <span class="inline-block w-2 h-2 rounded-full bg-emerald-500 mr-1"></span>
                                    <span class="text-emerald-500 font-medium">{{ stats.profit_percentage|default(0) }}%</span>
                                    <span class="text-gray-500 mr-1">هامش الربح</span>
                                </div>
                                <div class="w-full bg-gray-100 rounded-full h-1 mt-2">
                                    <div class="bg-emerald-500 h-1 rounded-full" style="width: {{ stats.profit_percentage|default(0) }}%"></div>
                                </div>
                            </div>
                            <a href="{{ url_for('reports.index') }}" class="mt-3 text-xs text-primary hover:text-indigo-700 flex items-center justify-end transition-colors duration-300">
                                عرض التقرير <i class="ri-arrow-left-line mr-1"></i>
                            </a>
                        </div>
                    </div>

                    <!-- بطاقات الإحصائيات - الصف الثاني -->
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-6">
                        <!-- بطاقة مشتريات اليوم -->
                        <div id="daily-purchases-card" class="stat-card purchases bg-white p-5 fade-in-up fade-in-up-1">
                            <div class="flex justify-between">
                                <div>
                                    <h3 class="text-gray-500 text-sm flex items-center">
                                        مشتريات اليوم
                                        <span class="tooltip-trigger ml-1 cursor-help text-gray-400 hover:text-gray-600" data-tooltip="إجمالي المشتريات التي تمت اليوم">
                                            <i class="ri-information-line"></i>
                                        </span>
                                    </h3>
                                    <p class="text-2xl font-bold mt-2">{{ stats.today_purchases|default(0)|round(2) }} ج.م</p>
                                </div>
                                <div class="w-12 h-12 rounded-full bg-purple-100 flex items-center justify-center text-purple-600 stat-icon">
                                    <i class="ri-shopping-bag-3-line text-xl"></i>
                                </div>
                            </div>
                            <div class="mt-4 text-sm">
                                <div class="flex items-center">
                                    <span class="inline-block w-2 h-2 rounded-full bg-purple-500 mr-1"></span>
                                    <span class="text-purple-500 font-medium">{{ stats.today_purchases|default(0)|round(2) }} ج.م</span>
                                    <span class="text-gray-500 mr-1">اليوم</span>
                                </div>
                                <div class="w-full bg-gray-100 rounded-full h-1 mt-2">
                                    <div class="bg-purple-500 h-1 rounded-full" style="width: {{ stats.today_purchases_percentage|default(0) }}%"></div>
                                </div>
                            </div>
                            <a href="{{ url_for('purchases.index') }}" class="mt-3 text-xs text-primary hover:text-indigo-700 flex items-center justify-end transition-colors duration-300">
                                عرض التفاصيل <i class="ri-arrow-left-line mr-1"></i>
                            </a>
                        </div>

                        <!-- بطاقة المنتجات منخفضة المخزون -->
                        <div id="low-stock-card" class="stat-card inventory bg-white p-5 fade-in-up fade-in-up-2">
                            <div class="flex justify-between">
                                <div>
                                    <h3 class="text-gray-500 text-sm flex items-center">
                                        المنتجات منخفضة المخزون
                                        <span class="tooltip-trigger ml-1 cursor-help text-gray-400 hover:text-gray-600" data-tooltip="عدد المنتجات التي وصلت إلى الحد الأدنى للمخزون">
                                            <i class="ri-information-line"></i>
                                        </span>
                                    </h3>
                                    <p class="text-2xl font-bold mt-2">{{ stats.low_stock_products }}</p>
                                </div>
                                <div class="w-12 h-12 rounded-full bg-amber-100 flex items-center justify-center text-amber-600 stat-icon">
                                    <i class="ri-alert-line text-xl"></i>
                                </div>
                            </div>
                            <div class="mt-4 text-sm">
                                <div class="flex items-center">
                                    <span class="inline-block w-2 h-2 rounded-full bg-amber-500 mr-1"></span>
                                    <span class="text-amber-500 font-medium">{{ stats.low_stock_percentage|default(0) }}%</span>
                                    <span class="text-gray-500 mr-1">من إجمالي المنتجات</span>
                                </div>
                                <div class="w-full bg-gray-100 rounded-full h-1 mt-2">
                                    <div class="bg-amber-500 h-1 rounded-full" style="width: {{ stats.low_stock_percentage|default(0) }}%"></div>
                                </div>
                            </div>
                            <a href="{{ url_for('products.index') }}" class="mt-3 text-xs text-primary hover:text-indigo-700 flex items-center justify-end transition-colors duration-300">
                                عرض القائمة <i class="ri-arrow-left-line mr-1"></i>
                            </a>
                        </div>

                        <!-- بطاقة العملاء -->
                        <div id="customers-card" class="stat-card customers bg-white p-5 fade-in-up fade-in-up-3">
                            <div class="flex justify-between">
                                <div>
                                    <h3 class="text-gray-500 text-sm flex items-center">
                                        العملاء
                                        <span class="tooltip-trigger ml-1 cursor-help text-gray-400 hover:text-gray-600" data-tooltip="إجمالي عدد العملاء المسجلين">
                                            <i class="ri-information-line"></i>
                                        </span>
                                    </h3>
                                    <p class="text-2xl font-bold mt-2">{{ stats.customers_count|default(0) }}</p>
                                </div>
                                <div class="w-12 h-12 rounded-full bg-pink-100 flex items-center justify-center text-pink-600 stat-icon">
                                    <i class="ri-user-3-line text-xl"></i>
                                </div>
                            </div>
                            <div class="mt-4 text-sm">
                                <div class="flex items-center">
                                    <span class="inline-block w-2 h-2 rounded-full bg-pink-500 mr-1"></span>
                                    <span class="text-pink-500 font-medium">{{ stats.customers_count|default(0) }}</span>
                                    <span class="text-gray-500 mr-1">عميل مسجل</span>
                                </div>
                                <div class="w-full bg-gray-100 rounded-full h-1 mt-2">
                                    <div class="bg-pink-500 h-1 rounded-full" style="width: 100%"></div>
                                </div>
                            </div>
                            <a href="{{ url_for('customers.index') }}" class="mt-3 text-xs text-primary hover:text-indigo-700 flex items-center justify-end transition-colors duration-300">
                                عرض العملاء <i class="ri-arrow-left-line mr-1"></i>
                            </a>
                        </div>
                    </div>
                </section>

                <!-- قسم المبيعات -->
                <section id="sales" class="mb-8">
                    <h2 class="text-xl font-bold text-gray-800 mb-6">المبيعات</h2>

                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
                        <!-- بطاقة مبيعات اليوم -->
                        <div id="today-sales-card" class="stat-card sales bg-white p-5 fade-in-up fade-in-up-1">
                            <div class="flex justify-between">
                                <div>
                                    <h3 class="text-gray-500 text-sm flex items-center">
                                        مبيعات اليوم
                                        <span class="tooltip-trigger ml-1 cursor-help text-gray-400 hover:text-gray-600" data-tooltip="إجمالي المبيعات التي تمت اليوم">
                                            <i class="ri-information-line"></i>
                                        </span>
                                    </h3>
                                    <p class="text-2xl font-bold mt-2">{{ stats.today_sales|default(0)|round(2) }} ج.م</p>
                                </div>
                                <div class="w-12 h-12 rounded-full bg-blue-100 flex items-center justify-center text-blue-600 stat-icon">
                                    <i class="ri-calendar-check-line text-xl"></i>
                                </div>
                            </div>
                            <div class="mt-4 text-sm">
                                <div class="flex items-center">
                                    <span class="inline-block w-2 h-2 rounded-full bg-blue-500 mr-1"></span>
                                    <span class="text-blue-500 font-medium">{{ stats.today_orders|default(0) }}</span>
                                    <span class="text-gray-500 mr-1">فاتورة</span>
                                </div>
                                <div class="w-full bg-gray-100 rounded-full h-1 mt-2">
                                    <div class="bg-blue-500 h-1 rounded-full" style="width: {{ stats.today_sales_percentage|default(0) }}%"></div>
                                </div>
                            </div>
                            <a href="{{ url_for('sales.index') }}?filter=today" class="mt-3 text-xs text-primary hover:text-indigo-700 flex items-center justify-end transition-colors duration-300">
                                عرض التفاصيل <i class="ri-arrow-left-line mr-1"></i>
                            </a>
                        </div>

                        <!-- بطاقة مبيعات الأسبوع -->
                        <div id="weekly-sales-card" class="stat-card sales bg-white p-5 fade-in-up fade-in-up-2">
                            <div class="flex justify-between">
                                <div>
                                    <h3 class="text-gray-500 text-sm flex items-center">
                                        مبيعات الأسبوع
                                        <span class="tooltip-trigger ml-1 cursor-help text-gray-400 hover:text-gray-600" data-tooltip="إجمالي المبيعات خلال الأسبوع الحالي">
                                            <i class="ri-information-line"></i>
                                        </span>
                                    </h3>
                                    <p class="text-2xl font-bold mt-2">{{ stats.weekly_sales|default(0)|round(2) }} ج.م</p>
                                </div>
                                <div class="w-12 h-12 rounded-full bg-indigo-100 flex items-center justify-center text-indigo-600 stat-icon">
                                    <i class="ri-calendar-2-line text-xl"></i>
                                </div>
                            </div>
                            <div class="mt-4 text-sm">
                                <div class="flex items-center">
                                    <span class="inline-block w-2 h-2 rounded-full bg-indigo-500 mr-1"></span>
                                    <span class="text-indigo-500 font-medium">{{ stats.weekly_orders|default(0) }}</span>
                                    <span class="text-gray-500 mr-1">فاتورة</span>
                                </div>
                                <div class="w-full bg-gray-100 rounded-full h-1 mt-2">
                                    <div class="bg-indigo-500 h-1 rounded-full" style="width: {{ stats.weekly_sales_percentage|default(0) }}%"></div>
                                </div>
                            </div>
                            <a href="{{ url_for('sales.index') }}?filter=week" class="mt-3 text-xs text-primary hover:text-indigo-700 flex items-center justify-end transition-colors duration-300">
                                عرض التفاصيل <i class="ri-arrow-left-line mr-1"></i>
                            </a>
                        </div>

                        <!-- بطاقة مبيعات الشهر -->
                        <div id="monthly-sales-card" class="stat-card sales bg-white p-5 fade-in-up fade-in-up-3">
                            <div class="flex justify-between">
                                <div>
                                    <h3 class="text-gray-500 text-sm flex items-center">
                                        مبيعات الشهر
                                        <span class="tooltip-trigger ml-1 cursor-help text-gray-400 hover:text-gray-600" data-tooltip="إجمالي المبيعات خلال الشهر الحالي">
                                            <i class="ri-information-line"></i>
                                        </span>
                                    </h3>
                                    <p class="text-2xl font-bold mt-2">{{ stats.monthly_sales|default(0)|round(2) }} ج.م</p>
                                </div>
                                <div class="w-12 h-12 rounded-full bg-purple-100 flex items-center justify-center text-purple-600 stat-icon">
                                    <i class="ri-calendar-line text-xl"></i>
                                </div>
                            </div>
                            <div class="mt-4 text-sm">
                                <div class="flex items-center">
                                    <span class="inline-block w-2 h-2 rounded-full bg-purple-500 mr-1"></span>
                                    <span class="text-purple-500 font-medium">{{ stats.monthly_orders|default(0) }}</span>
                                    <span class="text-gray-500 mr-1">فاتورة</span>
                                </div>
                                <div class="w-full bg-gray-100 rounded-full h-1 mt-2">
                                    <div class="bg-purple-500 h-1 rounded-full" style="width: {{ stats.monthly_sales_percentage|default(0) }}%"></div>
                                </div>
                            </div>
                            <a href="{{ url_for('sales.index') }}?filter=month" class="mt-3 text-xs text-primary hover:text-indigo-700 flex items-center justify-end transition-colors duration-300">
                                عرض التفاصيل <i class="ri-arrow-left-line mr-1"></i>
                            </a>
                        </div>

                        <!-- بطاقة إجمالي المبيعات -->
                        <div id="total-sales-card-2" class="stat-card sales bg-white p-5 fade-in-up fade-in-up-4">
                            <div class="flex justify-between">
                                <div>
                                    <h3 class="text-gray-500 text-sm flex items-center">
                                        إجمالي المبيعات
                                        <span class="tooltip-trigger ml-1 cursor-help text-gray-400 hover:text-gray-600" data-tooltip="إجمالي المبيعات منذ بداية النشاط">
                                            <i class="ri-information-line"></i>
                                        </span>
                                    </h3>
                                    <p class="text-2xl font-bold mt-2">{{ stats.total_sales|default(0)|round(2) }} ج.م</p>
                                </div>
                                <div class="w-12 h-12 rounded-full bg-green-100 flex items-center justify-center text-green-600 stat-icon">
                                    <i class="ri-line-chart-line text-xl"></i>
                                </div>
                            </div>
                            <div class="mt-4 text-sm">
                                <div class="flex items-center">
                                    <span class="inline-block w-2 h-2 rounded-full bg-green-500 mr-1"></span>
                                    <span class="text-green-500 font-medium">{{ stats.total_orders|default(0) }}</span>
                                    <span class="text-gray-500 mr-1">فاتورة</span>
                                </div>
                                <div class="w-full bg-gray-100 rounded-full h-1 mt-2">
                                    <div class="bg-green-500 h-1 rounded-full" style="width: 100%"></div>
                                </div>
                            </div>
                            <a href="{{ url_for('sales.index') }}" class="mt-3 text-xs text-primary hover:text-indigo-700 flex items-center justify-end transition-colors duration-300">
                                عرض التفاصيل <i class="ri-arrow-left-line mr-1"></i>
                            </a>
                        </div>
                    </div>

                    <!-- جدول آخر المبيعات -->
                    <div class="bg-white rounded-xl shadow-md p-5 fade-in-up fade-in-up-1">
                        <div class="flex justify-between items-center mb-5">
                            <h3 class="font-bold text-gray-800 chart-title">آخر المبيعات</h3>
                            <div class="flex items-center">
                                <a href="{{ url_for('pos.index') }}" class="px-4 py-2 bg-primary text-white rounded-lg shadow-sm hover:bg-primary-dark text-sm font-medium flex items-center space-x-1 space-x-reverse mr-2 transition-all duration-300">
                                    <i class="ri-add-line"></i>
                                    <span class="mr-1">فاتورة جديدة</span>
                                </a>
                                <a href="{{ url_for('sales.index') }}" class="text-xs text-primary hover:text-indigo-700 flex items-center transition-colors duration-300">
                                    عرض الكل <i class="ri-arrow-left-line mr-1"></i>
                                </a>
                            </div>
                        </div>
                        <div class="overflow-x-auto">
                            <table class="w-full data-table">
                                <thead>
                                    <tr class="bg-gray-50 text-gray-600 text-xs">
                                        <th class="py-3 px-4 text-right">رقم الفاتورة</th>
                                        <th class="py-3 px-4 text-right">العميل</th>
                                        <th class="py-3 px-4 text-right">المبلغ</th>
                                        <th class="py-3 px-4 text-right">طريقة الدفع</th>
                                        <th class="py-3 px-4 text-right">الحالة</th>
                                        <th class="py-3 px-4 text-right">التاريخ</th>
                                        <th class="py-3 px-4 text-right">الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% if stats.recent_orders is defined and stats.recent_orders %}
                                        {% for order in stats.recent_orders[:10] %}
                                        <tr class="border-b border-gray-100 text-sm text-gray-700 hover:bg-gray-50 transition-colors duration-200">
                                            <td class="py-3 px-4">{{ order.invoice_number }}</td>
                                            <td class="py-3 px-4">{{ order.customer.name }}</td>
                                            <td class="py-3 px-4">{{ order.total|round(2) }} ج.م</td>
                                            <td class="py-3 px-4">{{ order.payment_method }}</td>
                                            <td class="py-3 px-4">
                                                {% if order.status == 'completed' %}
                                                <span class="px-2 py-1 bg-green-100 text-green-800 rounded-full text-xs">مكتمل</span>
                                                {% elif order.status == 'pending' %}
                                                <span class="px-2 py-1 bg-yellow-100 text-yellow-800 rounded-full text-xs">معلق</span>
                                                {% elif order.status == 'cancelled' %}
                                                <span class="px-2 py-1 bg-red-100 text-red-800 rounded-full text-xs">ملغي</span>
                                                {% endif %}
                                            </td>
                                            <td class="py-3 px-4">{{ order.created_at.strftime('%Y-%m-%d') }}</td>
                                            <td class="py-3 px-4">
                                                <div class="flex items-center space-x-2 space-x-reverse">
                                                    <a href="{{ url_for('sales.index') }}" class="text-primary hover:text-indigo-700 transition-colors duration-300">
                                                        <i class="ri-eye-line"></i>
                                                    </a>
                                                    <a href="{{ url_for('sales.index') }}" class="text-gray-600 hover:text-gray-800 transition-colors duration-300">
                                                        <i class="ri-printer-line"></i>
                                                    </a>
                                                </div>
                                            </td>
                                        </tr>
                                        {% endfor %}
                                    {% else %}
                                        <tr class="border-b border-gray-100 text-sm text-gray-700">
                                            <td class="py-4 px-4 text-center" colspan="7">لا توجد مبيعات حديثة</td>
                                        </tr>
                                    {% endif %}
                                </tbody>
                            </table>
                        </div>
                    </div>
                </section>

                <!-- قسم الرسوم البيانية -->
                <section id="charts" class="mb-8">
                    <h2 class="text-xl font-bold text-gray-800 mb-6">الرسوم البيانية</h2>

                    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-6">
                        <!-- رسم بياني المبيعات -->
                        <div id="sales-chart-container" class="lg:col-span-2 bg-white rounded-xl shadow-md p-5 chart-container fade-in-up fade-in-up-1">
                            <div class="flex justify-between items-center mb-5">
                                <h3 class="font-bold text-gray-800 chart-title">المبيعات والطلبات</h3>
                                <div class="flex items-center">
                                    <select id="chart-period" class="text-sm border border-gray-300 rounded-lg p-2 focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary transition-all duration-300">
                                        <option value="7">7 أيام</option>
                                        <option value="30" selected>30 يوم</option>
                                        <option value="90">90 يوم</option>
                                        <option value="180">6 أشهر</option>
                                        <option value="365">سنة</option>
                                    </select>
                                    <button id="chart-type-toggle" class="ml-2 p-2 text-gray-500 hover:text-gray-700 transition-colors duration-300">
                                        <i class="ri-bar-chart-2-line"></i>
                                    </button>
                                </div>
                            </div>
                            <div id="sales-chart" class="w-full h-72"></div>
                            <div class="flex justify-between mt-4 text-sm">
                                <div class="flex items-center">
                                    <span class="inline-block w-3 h-3 rounded-full bg-primary mr-1"></span>
                                    <span class="text-gray-600">المبيعات</span>
                                </div>
                                <div class="flex items-center">
                                    <span class="inline-block w-3 h-3 rounded-full bg-indigo-400 mr-1"></span>
                                    <span class="text-gray-600">الطلبات</span>
                                </div>
                            </div>
                        </div>

                        <!-- رسم بياني توزيع المنتجات -->
                        <div id="categories-chart-container" class="bg-white rounded-xl shadow-md p-5 chart-container fade-in-up fade-in-up-2">
                            <div class="flex justify-between items-center mb-5">
                                <h3 class="font-bold text-gray-800 chart-title">توزيع المنتجات</h3>
                                <div class="flex items-center">
                                    <button id="categories-chart-type-toggle" class="p-2 text-gray-500 hover:text-gray-700 transition-colors duration-300">
                                        <i class="ri-pie-chart-line"></i>
                                    </button>
                                </div>
                            </div>
                            <div id="categories-chart" class="w-full h-72"></div>
                        </div>
                    </div>
                </section>

                <!-- قسم المخزون -->
                <section id="inventory" class="mb-8">
                    <h2 class="text-xl font-bold text-gray-800 mb-6">المخزون</h2>

                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
                        <!-- بطاقة إجمالي المنتجات -->
                        <div id="total-products-card" class="stat-card inventory bg-white p-5 fade-in-up fade-in-up-1">
                            <div class="flex justify-between">
                                <div>
                                    <h3 class="text-gray-500 text-sm flex items-center">
                                        إجمالي المنتجات
                                        <span class="tooltip-trigger ml-1 cursor-help text-gray-400 hover:text-gray-600" data-tooltip="إجمالي عدد المنتجات المسجلة في النظام">
                                            <i class="ri-information-line"></i>
                                        </span>
                                    </h3>
                                    <p class="text-2xl font-bold mt-2">{{ stats.total_products|default(0) }}</p>
                                </div>
                                <div class="w-12 h-12 rounded-full bg-indigo-100 flex items-center justify-center text-indigo-600 stat-icon">
                                    <i class="ri-archive-line text-xl"></i>
                                </div>
                            </div>
                            <div class="mt-4 text-sm">
                                <div class="flex items-center">
                                    <span class="inline-block w-2 h-2 rounded-full bg-indigo-500 mr-1"></span>
                                    <span class="text-indigo-500 font-medium">{{ stats.active_products|default(0) }}</span>
                                    <span class="text-gray-500 mr-1">منتج نشط</span>
                                </div>
                                <div class="w-full bg-gray-100 rounded-full h-1 mt-2">
                                    <div class="bg-indigo-500 h-1 rounded-full" style="width: {{ stats.active_products_percentage|default(0) }}%"></div>
                                </div>
                            </div>
                            <a href="{{ url_for('products.index') }}" class="mt-3 text-xs text-primary hover:text-indigo-700 flex items-center justify-end transition-colors duration-300">
                                عرض المنتجات <i class="ri-arrow-left-line mr-1"></i>
                            </a>
                        </div>

                        <!-- بطاقة المنتجات منخفضة المخزون -->
                        <div id="low-stock-card-2" class="stat-card inventory bg-white p-5 fade-in-up fade-in-up-2">
                            <div class="flex justify-between">
                                <div>
                                    <h3 class="text-gray-500 text-sm flex items-center">
                                        منخفضة المخزون
                                        <span class="tooltip-trigger ml-1 cursor-help text-gray-400 hover:text-gray-600" data-tooltip="المنتجات التي وصلت إلى الحد الأدنى للمخزون">
                                            <i class="ri-information-line"></i>
                                        </span>
                                    </h3>
                                    <p class="text-2xl font-bold mt-2">{{ stats.low_stock_products|default(0) }}</p>
                                </div>
                                <div class="w-12 h-12 rounded-full bg-amber-100 flex items-center justify-center text-amber-600 stat-icon">
                                    <i class="ri-alert-line text-xl"></i>
                                </div>
                            </div>
                            <div class="mt-4 text-sm">
                                <div class="flex items-center">
                                    <span class="inline-block w-2 h-2 rounded-full bg-amber-500 mr-1"></span>
                                    <span class="text-amber-500 font-medium">{{ stats.low_stock_percentage|default(0) }}%</span>
                                    <span class="text-gray-500 mr-1">من إجمالي المنتجات</span>
                                </div>
                                <div class="w-full bg-gray-100 rounded-full h-1 mt-2">
                                    <div class="bg-amber-500 h-1 rounded-full" style="width: {{ stats.low_stock_percentage|default(0) }}%"></div>
                                </div>
                            </div>
                            <a href="{{ url_for('products.index') }}?filter=low_stock" class="mt-3 text-xs text-primary hover:text-indigo-700 flex items-center justify-end transition-colors duration-300">
                                عرض القائمة <i class="ri-arrow-left-line mr-1"></i>
                            </a>
                        </div>

                        <!-- بطاقة المنتجات نفذت من المخزون -->
                        <div id="out-of-stock-card" class="stat-card inventory bg-white p-5 fade-in-up fade-in-up-3">
                            <div class="flex justify-between">
                                <div>
                                    <h3 class="text-gray-500 text-sm flex items-center">
                                        نفذت من المخزون
                                        <span class="tooltip-trigger ml-1 cursor-help text-gray-400 hover:text-gray-600" data-tooltip="المنتجات التي نفذت من المخزون">
                                            <i class="ri-information-line"></i>
                                        </span>
                                    </h3>
                                    <p class="text-2xl font-bold mt-2">{{ stats.out_of_stock_products|default(0) }}</p>
                                </div>
                                <div class="w-12 h-12 rounded-full bg-red-100 flex items-center justify-center text-red-600 stat-icon">
                                    <i class="ri-error-warning-line text-xl"></i>
                                </div>
                            </div>
                            <div class="mt-4 text-sm">
                                <div class="flex items-center">
                                    <span class="inline-block w-2 h-2 rounded-full bg-red-500 mr-1"></span>
                                    <span class="text-red-500 font-medium">{{ stats.out_of_stock_percentage|default(0) }}%</span>
                                    <span class="text-gray-500 mr-1">من إجمالي المنتجات</span>
                                </div>
                                <div class="w-full bg-gray-100 rounded-full h-1 mt-2">
                                    <div class="bg-red-500 h-1 rounded-full" style="width: {{ stats.out_of_stock_percentage|default(0) }}%"></div>
                                </div>
                            </div>
                            <a href="{{ url_for('products.index') }}?filter=out_of_stock" class="mt-3 text-xs text-primary hover:text-indigo-700 flex items-center justify-end transition-colors duration-300">
                                عرض القائمة <i class="ri-arrow-left-line mr-1"></i>
                            </a>
                        </div>

                        <!-- بطاقة المخازن -->
                        <div id="warehouses-card" class="stat-card inventory bg-white p-5 fade-in-up fade-in-up-4">
                            <div class="flex justify-between">
                                <div>
                                    <h3 class="text-gray-500 text-sm flex items-center">
                                        المخازن
                                        <span class="tooltip-trigger ml-1 cursor-help text-gray-400 hover:text-gray-600" data-tooltip="عدد المخازن المسجلة في النظام">
                                            <i class="ri-information-line"></i>
                                        </span>
                                    </h3>
                                    <p class="text-2xl font-bold mt-2">{{ stats.warehouses_count|default(0) }}</p>
                                </div>
                                <div class="w-12 h-12 rounded-full bg-blue-100 flex items-center justify-center text-blue-600 stat-icon">
                                    <i class="ri-store-2-line text-xl"></i>
                                </div>
                            </div>
                            <div class="mt-4 text-sm">
                                <div class="flex items-center">
                                    <span class="inline-block w-2 h-2 rounded-full bg-blue-500 mr-1"></span>
                                    <span class="text-blue-500 font-medium">{{ stats.warehouses_count|default(0) }}</span>
                                    <span class="text-gray-500 mr-1">مخزن نشط</span>
                                </div>
                                <div class="w-full bg-gray-100 rounded-full h-1 mt-2">
                                    <div class="bg-blue-500 h-1 rounded-full" style="width: 100%"></div>
                                </div>
                            </div>
                            <a href="{{ url_for('warehouses.index') }}" class="mt-3 text-xs text-primary hover:text-indigo-700 flex items-center justify-end transition-colors duration-300">
                                عرض المخازن <i class="ri-arrow-left-line mr-1"></i>
                            </a>
                        </div>
                    </div>

                    <!-- جدول المنتجات منخفضة المخزون -->
                    <div class="bg-white rounded-xl shadow-md p-5 fade-in-up fade-in-up-1">
                        <div class="flex justify-between items-center mb-5">
                            <h3 class="font-bold text-gray-800 chart-title">المنتجات منخفضة المخزون</h3>
                            <a href="{{ url_for('products.index') }}?filter=low_stock" class="text-xs text-primary hover:text-indigo-700 flex items-center transition-colors duration-300">
                                عرض الكل <i class="ri-arrow-left-line mr-1"></i>
                            </a>
                        </div>
                        <div class="overflow-x-auto">
                            <table class="w-full data-table">
                                <thead>
                                    <tr class="bg-gray-50 text-gray-600 text-xs">
                                        <th class="py-3 px-4 text-right">المنتج</th>
                                        <th class="py-3 px-4 text-right">الباركود</th>
                                        <th class="py-3 px-4 text-right">المخزن</th>
                                        <th class="py-3 px-4 text-right">الكمية المتاحة</th>
                                        <th class="py-3 px-4 text-right">الحد الأدنى</th>
                                        <th class="py-3 px-4 text-right">الحالة</th>
                                        <th class="py-3 px-4 text-right">الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% if stats.low_stock_products_list is defined and stats.low_stock_products_list %}
                                        {% for product in stats.low_stock_products_list[:5] %}
                                        <tr class="border-b border-gray-100 text-sm text-gray-700 hover:bg-gray-50 transition-colors duration-200">
                                            <td class="py-3 px-4">{{ product.name }}</td>
                                            <td class="py-3 px-4">{{ product.barcode }}</td>
                                            <td class="py-3 px-4">{{ product.warehouse }}</td>
                                            <td class="py-3 px-4">{{ product.quantity }}</td>
                                            <td class="py-3 px-4">{{ product.min_quantity }}</td>
                                            <td class="py-3 px-4">
                                                {% if product.quantity <= 0 %}
                                                <span class="px-2 py-1 bg-red-100 text-red-800 rounded-full text-xs">نفذت</span>
                                                {% else %}
                                                <span class="px-2 py-1 bg-yellow-100 text-yellow-800 rounded-full text-xs">منخفضة</span>
                                                {% endif %}
                                            </td>
                                            <td class="py-3 px-4">
                                                <a href="{{ url_for('purchases.create') }}" class="text-primary hover:text-indigo-700 transition-colors duration-300">
                                                    <i class="ri-shopping-cart-2-line"></i> طلب شراء
                                                </a>
                                            </td>
                                        </tr>
                                        {% endfor %}
                                    {% else %}
                                        <tr class="border-b border-gray-100 text-sm text-gray-700">
                                            <td class="py-4 px-4 text-center" colspan="7">لا توجد منتجات منخفضة المخزون</td>
                                        </tr>
                                    {% endif %}
                                </tbody>
                            </table>
                        </div>
                    </div>
                </section>

                <!-- قسم آخر الطلبات والمنتجات الأكثر مبيعاً -->
                <section id="tables" class="mb-8">
                    <h2 class="text-xl font-bold text-gray-800 mb-6">آخر الطلبات والمنتجات الأكثر مبيعاً</h2>

                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                        <!-- جدول آخر الطلبات -->
                        <div id="orders-table-container" class="bg-white rounded-xl shadow-md p-5 fade-in-up fade-in-up-1">
                            <div class="flex justify-between items-center mb-5">
                                <h3 class="font-bold text-gray-800 chart-title">آخر الطلبات</h3>
                                <a href="{{ url_for('sales.index') }}" class="text-xs text-primary hover:text-indigo-700 flex items-center transition-colors duration-300">
                                    عرض الكل <i class="ri-arrow-left-line mr-1"></i>
                                </a>
                            </div>
                            <div class="overflow-x-auto">
                                <table class="w-full data-table">
                                    <thead>
                                        <tr class="bg-gray-50 text-gray-600 text-xs">
                                            <th class="py-3 px-4 text-right">رقم الفاتورة</th>
                                            <th class="py-3 px-4 text-right">العميل</th>
                                            <th class="py-3 px-4 text-right">المبلغ</th>
                                            <th class="py-3 px-4 text-right">الحالة</th>
                                            <th class="py-3 px-4 text-right">التاريخ</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% if stats.recent_orders is defined and stats.recent_orders %}
                                            {% for order in stats.recent_orders[:5] %}
                                            <tr class="border-b border-gray-100 text-sm text-gray-700 hover:bg-gray-50 transition-colors duration-200">
                                                <td class="py-3 px-4">{{ order.invoice_number }}</td>
                                                <td class="py-3 px-4">{{ order.customer.name }}</td>
                                                <td class="py-3 px-4">{{ order.total|round(2) }} ج.م</td>
                                                <td class="py-3 px-4">
                                                    {% if order.status == 'completed' %}
                                                    <span class="px-2 py-1 bg-green-100 text-green-800 rounded-full text-xs">مكتمل</span>
                                                    {% elif order.status == 'pending' %}
                                                    <span class="px-2 py-1 bg-yellow-100 text-yellow-800 rounded-full text-xs">معلق</span>
                                                    {% elif order.status == 'cancelled' %}
                                                    <span class="px-2 py-1 bg-red-100 text-red-800 rounded-full text-xs">ملغي</span>
                                                    {% endif %}
                                                </td>
                                                <td class="py-3 px-4">{{ order.created_at.strftime('%Y-%m-%d') }}</td>
                                            </tr>
                                            {% endfor %}
                                        {% else %}
                                            <tr class="border-b border-gray-100 text-sm text-gray-700">
                                                <td class="py-4 px-4 text-center" colspan="5">لا توجد طلبات حديثة</td>
                                            </tr>
                                        {% endif %}
                                    </tbody>
                                </table>
                            </div>
                        </div>

                        <!-- جدول المنتجات الأكثر مبيعاً -->
                        <div id="products-list-container" class="bg-white rounded-xl shadow-md p-5 fade-in-up fade-in-up-2">
                            <div class="flex justify-between items-center mb-5">
                                <h3 class="font-bold text-gray-800 chart-title">المنتجات الأكثر مبيعاً</h3>
                                <a href="{{ url_for('products.index') }}" class="text-xs text-primary hover:text-indigo-700 flex items-center transition-colors duration-300">
                                    عرض الكل <i class="ri-arrow-left-line mr-1"></i>
                                </a>
                            </div>
                            <div class="overflow-x-auto">
                                <table class="w-full data-table">
                                    <thead>
                                        <tr class="bg-gray-50 text-gray-600 text-xs">
                                            <th class="py-3 px-4 text-right">المنتج</th>
                                            <th class="py-3 px-4 text-right">التصنيف</th>
                                            <th class="py-3 px-4 text-right">السعر</th>
                                            <th class="py-3 px-4 text-right">الكمية المباعة</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% if stats.top_products is defined and stats.top_products %}
                                            {% for product in stats.top_products %}
                                            <tr class="border-b border-gray-100 text-sm text-gray-700 hover:bg-gray-50 transition-colors duration-200">
                                                <td class="py-3 px-4">{{ product.name }}</td>
                                                <td class="py-3 px-4">{{ product.category }}</td>
                                                <td class="py-3 px-4">{{ product.price|round(2) }} ج.م</td>
                                                <td class="py-3 px-4">{{ product.total_sold }}</td>
                                            </tr>
                                            {% endfor %}
                                        {% else %}
                                            <tr class="border-b border-gray-100 text-sm text-gray-700">
                                                <td class="py-4 px-4 text-center" colspan="4">لا توجد منتجات مباعة</td>
                                            </tr>
                                        {% endif %}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </section>
            </div>
        </div>
    </div>

    <!-- قائمة تخصيص لوحة التحكم -->
    <div id="dashboard-customizer" class="fixed inset-0 bg-black bg-opacity-30 backdrop-blur-sm customizer-overlay z-50 hidden flex items-center justify-center">
        <div class="bg-white rounded-xl shadow-xl p-6 w-full max-w-md transform scale-95 opacity-0 transition-all duration-300 customizer-panel">
            <div class="flex justify-between items-center mb-6">
                <h3 class="text-lg font-bold text-gray-800">تخصيص لوحة التحكم</h3>
                <button id="close-customizer" class="text-gray-500 hover:text-gray-700 transition-colors duration-300">
                    <i class="ri-close-line text-xl"></i>
                </button>
            </div>

            <!-- محتوى قائمة التخصيص -->
        </div>
    </div>
{% endblock %}

{% block scripts %}
<script src="{{ url_for('static', filename='js/dashboard-manager.js') }}?v=1.0.0"></script>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // تهيئة مدير لوحة التحكم
        if (typeof DashboardManager !== 'undefined') {
            window.dashboardManager = new DashboardManager({
                darkMode: document.documentElement.classList.contains('dark-mode')
            });
        }

        // تهيئة التنقل في القائمة الجانبية
        const sidebarItems = document.querySelectorAll('.sidebar-menu-item');
        sidebarItems.forEach(item => {
            item.addEventListener('click', function() {
                sidebarItems.forEach(i => i.classList.remove('active'));
                this.classList.add('active');
            });
        });

        // تهيئة زر تبديل وضع الدارك مود
        const darkModeToggle = document.getElementById('dark-mode-toggle-dashboard');
        if (darkModeToggle) {
            darkModeToggle.addEventListener('click', function() {
                if (window.darkModeManager) {
                    window.darkModeManager.toggle();

                    // تحديث أيقونة الزر
                    const isDarkMode = document.documentElement.classList.contains('dark-mode');
                    this.querySelector('i').className = isDarkMode ? 'ri-sun-line' : 'ri-moon-line';
                    this.querySelector('span').textContent = isDarkMode ? 'الوضع الفاتح' : 'الوضع الداكن';
                }
            });

            // تعيين الحالة الأولية للزر
            const isDarkMode = document.documentElement.classList.contains('dark-mode');
            darkModeToggle.querySelector('i').className = isDarkMode ? 'ri-sun-line' : 'ri-moon-line';
            darkModeToggle.querySelector('span').textContent = isDarkMode ? 'الوضع الفاتح' : 'الوضع الداكن';
        }

        // تهيئة زر تحديث لوحة التحكم
        const refreshButton = document.getElementById('refresh-dashboard');
        if (refreshButton) {
            refreshButton.addEventListener('click', function() {
                // إظهار حالة التحميل
                this.disabled = true;
                this.innerHTML = '<i class="ri-loader-4-line animate-spin"></i><span class="mr-1">جاري التحديث...</span>';

                // إعادة تحميل الصفحة
                setTimeout(() => {
                    window.location.reload();
                }, 500);
            });
        }
    });
</script>
{% endblock %}
