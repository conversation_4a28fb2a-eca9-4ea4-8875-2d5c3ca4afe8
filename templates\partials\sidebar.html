<div class="w-64 bg-white shadow-lg z-10 transition-all duration-300" id="sidebar">
    <div class="p-4 border-b border-gray-100">
        <div class="flex items-center justify-center">
            {% if settings and settings.business and settings.business.logo %}
            <img src="{{ settings.business.logo }}" alt="{{ settings.business.name or 'Nobara' }}" class="h-10">
            {% else %}
            <img src="{{ url_for('static', filename='img/nobara-logo.svg') }}" alt="Nobara" class="h-10">
            {% endif %}
        </div>
    </div>

    <div class="overflow-y-auto h-full scrollbar-hide pb-20">
        <nav class="mt-4">
            <div class="px-4 py-2">
                <h2 class="text-xs font-semibold text-gray-500">القائمة الرئيسية</h2>
            </div>

            <a href="{{ url_for('dashboard.home') }}" class="flex items-center px-4 py-3 {{ 'text-primary bg-blue-50 border-r-4 border-primary' if request.endpoint == 'dashboard.home' else 'text-gray-700 hover:bg-gray-50' }}">
                <div class="w-6 h-6 flex items-center justify-center">
                    <i class="ri-dashboard-line"></i>
                </div>
                <span class="mx-3 {{ 'font-medium' if request.endpoint == 'dashboard.home' else '' }}">لوحة التحكم</span>
            </a>

            <a href="{{ url_for('pos.index') }}" class="flex items-center px-4 py-3 {{ 'text-primary bg-blue-50 border-r-4 border-primary' if request.endpoint == 'pos.index' else 'text-gray-700 hover:bg-gray-50' }}">
                <div class="w-6 h-6 flex items-center justify-center">
                    <i class="ri-shopping-cart-line"></i>
                </div>
                <span class="mx-3 {{ 'font-medium' if request.endpoint == 'pos.index' else '' }}">نقطة البيع</span>
            </a>

            <a href="{{ url_for('products.index') }}" class="flex items-center px-4 py-3 {{ 'text-primary bg-blue-50 border-r-4 border-primary' if 'products.' in request.endpoint and request.endpoint != 'products.categories' else 'text-gray-700 hover:bg-gray-50' }}">
                <div class="w-6 h-6 flex items-center justify-center">
                    <i class="ri-store-line"></i>
                </div>
                <span class="mx-3 {{ 'font-medium' if 'products.' in request.endpoint and request.endpoint != 'products.categories' else '' }}">المنتجات</span>
            </a>

            <div class="relative" x-data="{ salesOpen: {{ 'true' if 'sales.' in request.endpoint or 'returns.' in request.endpoint else 'false' }} }">
                <button @click="salesOpen = !salesOpen" class="w-full flex items-center px-4 py-3 {{ 'text-primary' if 'sales.' in request.endpoint or 'returns.' in request.endpoint else 'text-gray-700 hover:bg-gray-50' }}">
                    <div class="w-6 h-6 flex items-center justify-center">
                        <i class="ri-file-list-3-line"></i>
                    </div>
                    <span class="mx-3 {{ 'font-medium' if 'sales.' in request.endpoint or 'returns.' in request.endpoint else '' }}">المبيعات</span>
                    <i class="ri-arrow-down-s-line mr-auto transition-transform" :class="{ 'rotate-180': salesOpen }"></i>
                </button>

                <div x-show="salesOpen" x-transition:enter="transition ease-out duration-200" x-transition:enter-start="opacity-0 transform scale-95" x-transition:enter-end="opacity-100 transform scale-100" x-transition:leave="transition ease-in duration-150" x-transition:leave-start="opacity-100 transform scale-100" x-transition:leave-end="opacity-0 transform scale-95" class="pr-4 mt-1">
                    <a href="{{ url_for('sales.index') }}" class="flex items-center px-4 py-2 rounded-lg {{ 'text-primary bg-blue-50' if request.endpoint == 'sales.index' else 'text-gray-700 hover:bg-gray-50' }}">
                        <div class="w-6 h-6 flex items-center justify-center">
                            <i class="ri-file-list-3-line"></i>
                        </div>
                        <span class="mx-3 {{ 'font-medium' if request.endpoint == 'sales.index' else '' }}">قائمة المبيعات</span>
                    </a>

                    <a href="{{ url_for('returns.index') }}" class="flex items-center px-4 py-2 rounded-lg {{ 'text-primary bg-blue-50' if 'returns.' in request.endpoint else 'text-gray-700 hover:bg-gray-50' }}">
                        <div class="w-6 h-6 flex items-center justify-center">
                            <i class="ri-arrow-go-back-line"></i>
                        </div>
                        <span class="mx-3 {{ 'font-medium' if 'returns.' in request.endpoint else '' }}">المرتجعات</span>
                    </a>
                </div>
            </div>

            <a href="{{ url_for('purchases.index') }}" class="flex items-center px-4 py-3 {{ 'text-primary bg-blue-50 border-r-4 border-primary' if 'purchases.' in request.endpoint else 'text-gray-700 hover:bg-gray-50' }}">
                <div class="w-6 h-6 flex items-center justify-center">
                    <i class="ri-truck-line"></i>
                </div>
                <span class="mx-3 {{ 'font-medium' if 'purchases.' in request.endpoint else '' }}">المشتريات</span>
            </a>

            <a href="{{ url_for('customers.index') }}" class="flex items-center px-4 py-3 {{ 'text-primary bg-blue-50 border-r-4 border-primary' if 'customers.' in request.endpoint else 'text-gray-700 hover:bg-gray-50' }}">
                <div class="w-6 h-6 flex items-center justify-center">
                    <i class="ri-user-line"></i>
                </div>
                <span class="mx-3 {{ 'font-medium' if 'customers.' in request.endpoint else '' }}">العملاء</span>
            </a>

            <a href="{{ url_for('suppliers.index') }}" class="flex items-center px-4 py-3 {{ 'text-primary bg-blue-50 border-r-4 border-primary' if 'suppliers.' in request.endpoint else 'text-gray-700 hover:bg-gray-50' }}">
                <div class="w-6 h-6 flex items-center justify-center">
                    <i class="ri-building-line"></i>
                </div>
                <span class="mx-3 {{ 'font-medium' if 'suppliers.' in request.endpoint else '' }}">الموردين</span>
            </a>

            <a href="{{ url_for('reports.index') }}" class="flex items-center px-4 py-3 {{ 'text-primary bg-blue-50 border-r-4 border-primary' if 'reports.' in request.endpoint else 'text-gray-700 hover:bg-gray-50' }}">
                <div class="w-6 h-6 flex items-center justify-center">
                    <i class="ri-bar-chart-line"></i>
                </div>
                <span class="mx-3 {{ 'font-medium' if 'reports.' in request.endpoint else '' }}">التقارير</span>
            </a>

            <a href="{{ url_for('warehouses.index') }}" class="flex items-center px-4 py-3 {{ 'text-primary bg-blue-50 border-r-4 border-primary' if 'warehouses.' in request.endpoint else 'text-gray-700 hover:bg-gray-50' }}">
                <div class="w-6 h-6 flex items-center justify-center">
                    <i class="ri-store-2-line"></i>
                </div>
                <span class="mx-3 {{ 'font-medium' if 'warehouses.' in request.endpoint else '' }}">المخازن</span>
            </a>

            <div class="px-4 py-2 mt-4">
                <h2 class="text-xs font-semibold text-gray-500">الإدارة</h2>
            </div>

            <a href="{{ url_for('users.index') }}" class="flex items-center px-4 py-3 {{ 'text-primary bg-blue-50 border-r-4 border-primary' if 'users.' in request.endpoint else 'text-gray-700 hover:bg-gray-50' }}">
                <div class="w-6 h-6 flex items-center justify-center">
                    <i class="ri-user-settings-line"></i>
                </div>
                <span class="mx-3 {{ 'font-medium' if 'users.' in request.endpoint else '' }}">المستخدمين</span>
            </a>

            <a href="{{ url_for('settings.index') }}" class="flex items-center px-4 py-3 {{ 'text-primary bg-blue-50 border-r-4 border-primary' if 'settings.' in request.endpoint else 'text-gray-700 hover:bg-gray-50' }}">
                <div class="w-6 h-6 flex items-center justify-center">
                    <i class="ri-settings-line"></i>
                </div>
                <span class="mx-3 {{ 'font-medium' if 'settings.' in request.endpoint else '' }}">الإعدادات</span>
            </a>
        </nav>
    </div>
</div>