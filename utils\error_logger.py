"""
نظام تسجيل الأخطاء المتقدم
يوفر آليات متقدمة لتسجيل وتتبع وتحليل الأخطاء في النظام
"""

import os
import sys
import time
import logging
import traceback
import json
import inspect
import platform
import psutil
import socket
from datetime import datetime, timedelta
from flask import request, session, g, current_app
from logging.handlers import RotatingFileHandler, SMTPHandler
from sqlalchemy.exc import SQLAlchemyError
from werkzeug.exceptions import HTTPException


class ErrorLogger:
    """فئة لتسجيل الأخطاء"""

    def __init__(self, app=None):
        """تهيئة نظام تسجيل الأخطاء"""
        self.app = app
        self.logger = None

        if app is not None:
            self.init_app(app)

    def init_app(self, app):
        """تهيئة نظام تسجيل الأخطاء المتقدم مع تطبيق Flask"""
        self.app = app

        # إنشاء مجلد السجلات إذا لم يكن موجودًا
        logs_dir = os.path.join(app.root_path, 'logs')
        os.makedirs(logs_dir, exist_ok=True)

        # إنشاء مجلدات فرعية للسجلات
        error_logs_dir = os.path.join(logs_dir, 'errors')
        os.makedirs(error_logs_dir, exist_ok=True)

        warning_logs_dir = os.path.join(logs_dir, 'warnings')
        os.makedirs(warning_logs_dir, exist_ok=True)

        info_logs_dir = os.path.join(logs_dir, 'info')
        os.makedirs(info_logs_dir, exist_ok=True)

        # إعداد المسجل
        self.logger = logging.getLogger('nobara')
        self.logger.setLevel(logging.INFO)

        # التأكد من عدم تكرار المسجلات
        if self.logger.handlers:
            self.logger.handlers.clear()

        # إعداد تنسيق السجل المتقدم
        detailed_formatter = logging.Formatter(
            '%(asctime)s - %(levelname)s - %(name)s - %(module)s:%(lineno)d - %(message)s',
            '%Y-%m-%d %H:%M:%S'
        )

        # إعداد مسجل الملف للمعلومات
        info_file_handler = RotatingFileHandler(
            os.path.join(info_logs_dir, 'app.log'),
            maxBytes=20 * 1024 * 1024,  # 20 ميجابايت
            backupCount=20,
            encoding='utf-8'
        )
        info_file_handler.setLevel(logging.INFO)
        info_file_handler.setFormatter(detailed_formatter)

        # إعداد مسجل الملف للتحذيرات
        warning_file_handler = RotatingFileHandler(
            os.path.join(warning_logs_dir, 'warning.log'),
            maxBytes=20 * 1024 * 1024,  # 20 ميجابايت
            backupCount=20,
            encoding='utf-8'
        )
        warning_file_handler.setLevel(logging.WARNING)
        warning_file_handler.setFormatter(detailed_formatter)

        # إعداد مسجل الملف للأخطاء
        error_file_handler = RotatingFileHandler(
            os.path.join(error_logs_dir, 'error.log'),
            maxBytes=20 * 1024 * 1024,  # 20 ميجابايت
            backupCount=20,
            encoding='utf-8'
        )
        error_file_handler.setLevel(logging.ERROR)
        error_file_handler.setFormatter(detailed_formatter)

        # إعداد مسجل الملف للأخطاء الحرجة
        critical_file_handler = RotatingFileHandler(
            os.path.join(error_logs_dir, 'critical.log'),
            maxBytes=20 * 1024 * 1024,  # 20 ميجابايت
            backupCount=20,
            encoding='utf-8'
        )
        critical_file_handler.setLevel(logging.CRITICAL)
        critical_file_handler.setFormatter(detailed_formatter)

        # إضافة المسجلات إلى المسجل
        self.logger.addHandler(info_file_handler)
        self.logger.addHandler(warning_file_handler)
        self.logger.addHandler(error_file_handler)
        self.logger.addHandler(critical_file_handler)

        # إضافة مسجل البريد الإلكتروني للأخطاء الحرجة إذا تم تكوين البريد الإلكتروني
        if app.config.get('MAIL_SERVER') and app.config.get('MAIL_USERNAME') and app.config.get('MAIL_PASSWORD'):
            mail_handler = SMTPHandler(
                mailhost=(app.config['MAIL_SERVER'], app.config['MAIL_PORT']),
                fromaddr=app.config.get('MAIL_DEFAULT_SENDER', '<EMAIL>'),
                toaddrs=['<EMAIL>'],  # إرسال الأخطاء الحرجة إلى المسؤول
                subject='خطأ حرج في نظام نوبارا',
                credentials=(app.config['MAIL_USERNAME'], app.config['MAIL_PASSWORD']),
                secure=() if app.config.get('MAIL_USE_TLS', True) else None
            )
            mail_handler.setLevel(logging.CRITICAL)
            mail_handler.setFormatter(detailed_formatter)
            self.logger.addHandler(mail_handler)

        # تسجيل الأخطاء غير المعالجة
        @app.errorhandler(Exception)
        def handle_exception(e):
            # تجاهل أخطاء HTTP المعروفة مثل 404
            if isinstance(e, HTTPException):
                return e

            # تسجيل الاستثناء مع معلومات مفصلة
            self.log_exception(e)

            # إرجاع صفحة خطأ مناسبة
            return 'حدث خطأ في الخادم. تم تسجيل الخطأ وسيتم معالجته قريبًا.', 500

        # تسجيل معلومات النظام عند بدء التشغيل
        self.log_system_info()

    def log_info(self, message, extra=None):
        """تسجيل معلومات"""
        if self.logger is None:
            return

        # إضافة معلومات إضافية
        if extra is None:
            extra = {}

        # تسجيل المعلومات
        self.logger.info(message, extra=extra)

    def log_warning(self, message, extra=None):
        """تسجيل تحذير"""
        if self.logger is None:
            return

        # إضافة معلومات إضافية
        if extra is None:
            extra = {}

        # تسجيل التحذير
        self.logger.warning(message, extra=extra)

    def log_error(self, message, extra=None):
        """تسجيل خطأ"""
        if self.logger is None:
            return

        # إضافة معلومات إضافية
        if extra is None:
            extra = {}

        # الحصول على معلومات الطلب إذا لم تكن موجودة في extra
        if 'request_info' not in extra:
            request_info = self._get_request_info()
            if request_info:
                extra['request_info'] = request_info

        # تنسيق الرسالة مع المعلومات الإضافية
        formatted_message = message
        if extra:
            try:
                formatted_message = f"{message}\nمعلومات إضافية: {json.dumps(extra, ensure_ascii=False, indent=2)}"
            except:
                formatted_message = f"{message}\nمعلومات إضافية: {str(extra)}"

        # تسجيل الخطأ
        self.logger.error(formatted_message)

    def log_system_info(self):
        """تسجيل معلومات النظام"""
        if self.logger is None:
            return

        try:
            # جمع معلومات النظام
            system_info = {
                'platform': platform.platform(),
                'python_version': platform.python_version(),
                'hostname': socket.gethostname(),
                'ip_address': socket.gethostbyname(socket.gethostname()),
                'cpu_count': psutil.cpu_count(),
                'memory_total': f"{psutil.virtual_memory().total / (1024 * 1024 * 1024):.2f} GB",
                'memory_available': f"{psutil.virtual_memory().available / (1024 * 1024 * 1024):.2f} GB",
                'disk_usage': f"{psutil.disk_usage('/').percent}%",
                'app_version': self.app.config.get('APP_VERSION', 'غير معروف'),
                'start_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            }

            # تسجيل معلومات النظام
            self.logger.info(
                f"معلومات النظام:\n"
                f"النظام: {system_info['platform']}\n"
                f"إصدار بايثون: {system_info['python_version']}\n"
                f"اسم المضيف: {system_info['hostname']}\n"
                f"عنوان IP: {system_info['ip_address']}\n"
                f"عدد وحدات المعالجة: {system_info['cpu_count']}\n"
                f"إجمالي الذاكرة: {system_info['memory_total']}\n"
                f"الذاكرة المتاحة: {system_info['memory_available']}\n"
                f"استخدام القرص: {system_info['disk_usage']}\n"
                f"إصدار التطبيق: {system_info['app_version']}\n"
                f"وقت البدء: {system_info['start_time']}"
            )
        except Exception as e:
            self.logger.error(f"خطأ أثناء تسجيل معلومات النظام: {str(e)}")

    def log_exception(self, exception):
        """تسجيل استثناء مع معلومات مفصلة"""
        if self.logger is None:
            return

        try:
            # الحصول على معلومات الاستثناء
            exc_type, exc_value, exc_traceback = sys.exc_info()
            exc_info = traceback.format_exc()

            # الحصول على معلومات الطلب
            request_info = self._get_request_info()

            # الحصول على معلومات الإطار الحالي
            frame_info = self._get_frame_info()

            # الحصول على معلومات النظام
            system_info = self._get_system_info()

            # تحديد مستوى الخطأ
            log_level = logging.ERROR
            if isinstance(exception, SQLAlchemyError):
                log_level = logging.CRITICAL  # أخطاء قاعدة البيانات تعتبر حرجة

            # تنسيق رسالة الخطأ
            error_message = (
                f"استثناء: {exc_type.__name__}: {str(exception)}\n"
                f"الوقت: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n"
                f"نوع الاستثناء: {exc_type.__name__}\n"
                f"الملف: {frame_info.get('filename', 'غير معروف')}\n"
                f"السطر: {frame_info.get('lineno', 'غير معروف')}\n"
                f"الدالة: {frame_info.get('function', 'غير معروف')}\n"
                f"معلومات الطلب: {json.dumps(request_info, ensure_ascii=False, indent=2)}\n"
                f"معلومات النظام: {json.dumps(system_info, ensure_ascii=False, indent=2)}\n"
                f"تتبع الاستثناء:\n{exc_info}"
            )

            # تسجيل الاستثناء بالمستوى المناسب
            if log_level == logging.CRITICAL:
                self.logger.critical(error_message)
            else:
                self.logger.error(error_message)

            # إرسال إشعار للمسؤول في حالة الأخطاء الحرجة
            if log_level == logging.CRITICAL and hasattr(self.app, 'notification_manager'):
                self.app.notification_manager.send_admin_notification(
                    title="خطأ حرج في النظام",
                    message=f"حدث خطأ حرج: {exc_type.__name__}: {str(exception)}",
                    category="error",
                    link="/settings/error-logs"
                )
        except Exception as e:
            # في حالة حدوث خطأ أثناء تسجيل الاستثناء، نسجل خطأ بسيط
            self.logger.error(f"خطأ أثناء تسجيل الاستثناء: {str(e)}\nالاستثناء الأصلي: {str(exception)}")

    def _get_frame_info(self):
        """الحصول على معلومات الإطار الحالي"""
        frame_info = {}

        try:
            # الحصول على الإطار الحالي
            frame = inspect.currentframe()

            # البحث عن إطار المستدعي (تجاوز إطارات نظام تسجيل الأخطاء)
            while frame:
                frame_info = inspect.getframeinfo(frame)
                module_name = frame_info.filename

                # تجاوز إطارات نظام تسجيل الأخطاء
                if 'error_logger.py' not in module_name and 'logging' not in module_name:
                    break

                frame = frame.f_back

            # استخراج معلومات الإطار
            result = {
                'filename': os.path.basename(frame_info.filename),
                'full_path': frame_info.filename,
                'lineno': frame_info.lineno,
                'function': frame_info.function,
                'code_context': frame_info.code_context[0].strip() if frame_info.code_context else None
            }

            return result
        except Exception as e:
            return {'error': f'فشل في الحصول على معلومات الإطار: {str(e)}'}
        finally:
            # تنظيف الإطارات
            del frame

    def _get_system_info(self):
        """الحصول على معلومات النظام الحالية"""
        try:
            return {
                'memory_usage': f"{psutil.Process().memory_info().rss / (1024 * 1024):.2f} MB",
                'cpu_percent': f"{psutil.cpu_percent()}%",
                'memory_percent': f"{psutil.virtual_memory().percent}%",
                'disk_usage': f"{psutil.disk_usage('/').percent}%",
                'open_files': len(psutil.Process().open_files()),
                'threads': psutil.Process().num_threads(),
                'connections': len(psutil.Process().connections()),
                'uptime': str(timedelta(seconds=int(time.time() - psutil.Process().create_time())))
            }
        except Exception as e:
            return {'error': f'فشل في الحصول على معلومات النظام: {str(e)}'}

    def _get_request_info(self):
        """الحصول على معلومات الطلب الحالي"""
        if not request:
            return {'error': 'لا يوجد طلب حالي'}

        try:
            # معلومات الطلب الأساسية
            request_info = {
                'url': request.url,
                'base_url': request.base_url,
                'path': request.path,
                'method': request.method,
                'endpoint': request.endpoint,
                'remote_addr': request.remote_addr,
                'user_agent': request.user_agent.string if request.user_agent else None,
                'referrer': request.referrer,
                'content_type': request.content_type,
                'content_length': request.content_length,
                'is_xhr': request.is_xhr,
                'is_secure': request.is_secure,
                'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            }

            # معلومات الطلب المتقدمة
            try:
                request_info['headers'] = dict(request.headers)
            except:
                request_info['headers'] = 'غير متاح'

            try:
                request_info['args'] = dict(request.args)
            except:
                request_info['args'] = 'غير متاح'

            try:
                request_info['form'] = dict(request.form)
            except:
                request_info['form'] = 'غير متاح'

            try:
                request_info['cookies'] = dict(request.cookies)
            except:
                request_info['cookies'] = 'غير متاح'

            # معلومات المستخدم
            if 'user_id' in session:
                request_info['user_id'] = session['user_id']

            # معلومات إضافية
            if hasattr(g, 'user') and g.user:
                request_info['username'] = g.user.username
                request_info['user_role'] = g.user.role if hasattr(g.user, 'role') else 'غير معروف'

            return request_info
        except Exception as e:
            return {'error': f'فشل في الحصول على معلومات الطلب: {str(e)}'}

    def get_error_logs(self, limit=100, offset=0, search=None, start_date=None, end_date=None, log_type=None,
                     source=None, user_id=None, ip_address=None, module=None, sort_by='timestamp', sort_order='desc'):
        """
        الحصول على سجلات الأخطاء مع دعم التصفية والبحث المتقدم

        Args:
            limit (int): عدد السجلات المطلوبة
            offset (int): إزاحة البداية
            search (str): نص البحث في الرسائل والتفاصيل
            start_date (str): تاريخ البداية (YYYY-MM-DD)
            end_date (str): تاريخ النهاية (YYYY-MM-DD)
            log_type (str): نوع السجل (error, warning, info, critical)
            source (str): مصدر الخطأ (server, client)
            user_id (int): معرف المستخدم
            ip_address (str): عنوان IP
            module (str): اسم الوحدة
            sort_by (str): حقل الترتيب (timestamp, level, message)
            sort_order (str): ترتيب تصاعدي أو تنازلي (asc, desc)

        Returns:
            list: قائمة بسجلات الأخطاء
        """
        logs = []

        # تحويل تاريخ البداية والنهاية إلى كائنات datetime
        if start_date:
            try:
                start_date = datetime.strptime(start_date, '%Y-%m-%d')
            except ValueError:
                start_date = None

        if end_date:
            try:
                end_date = datetime.strptime(end_date, '%Y-%m-%d')
                # إضافة يوم كامل للحصول على نهاية اليوم
                end_date = end_date + timedelta(days=1)
            except ValueError:
                end_date = None

        # تحديد مسارات ملفات السجلات
        log_files = []
        logs_dir = os.path.join(self.app.root_path, 'logs')

        # تحديد ملفات السجلات المطلوبة بناءً على نوع السجل
        if log_type == 'error' or not log_type:
            error_log_path = os.path.join(logs_dir, 'errors', 'error.log')
            if os.path.exists(error_log_path):
                log_files.append(error_log_path)

        if log_type == 'critical' or not log_type:
            critical_log_path = os.path.join(logs_dir, 'errors', 'critical.log')
            if os.path.exists(critical_log_path):
                log_files.append(critical_log_path)

        if log_type == 'warning' or not log_type:
            warning_log_path = os.path.join(logs_dir, 'warnings', 'warning.log')
            if os.path.exists(warning_log_path):
                log_files.append(warning_log_path)

        if log_type == 'info' or not log_type:
            info_log_path = os.path.join(logs_dir, 'info', 'app.log')
            if os.path.exists(info_log_path):
                log_files.append(info_log_path)

        # إذا لم يتم العثور على ملفات السجلات في المجلدات الفرعية، نحاول في المجلد الرئيسي
        if not log_files:
            error_log_path = os.path.join(logs_dir, 'error.log')
            if os.path.exists(error_log_path):
                log_files.append(error_log_path)

        # قراءة وتحليل ملفات السجلات
        for log_file in log_files:
            # تحديد نوع السجل من اسم الملف
            if 'critical.log' in log_file:
                current_log_type = 'critical'
            elif 'error.log' in log_file:
                current_log_type = 'error'
            elif 'warning.log' in log_file:
                current_log_type = 'warning'
            else:
                current_log_type = 'info'

            # قراءة ملف السجل
            try:
                with open(log_file, 'r', encoding='utf-8') as f:
                    lines = f.readlines()
            except Exception as e:
                if self.logger:
                    self.logger.error(f"خطأ في قراءة ملف السجل {log_file}: {str(e)}")
                continue

            # تحليل السجلات
            current_log = None
            for line in lines:
                # التحقق من بداية سجل جديد
                if line.startswith('20') and (' - ERROR - ' in line or ' - WARNING - ' in line or
                                             ' - INFO - ' in line or ' - CRITICAL - ' in line):
                    # إضافة السجل السابق إلى القائمة
                    if current_log:
                        logs.append(current_log)

                    # تحديد نوع السجل من الرسالة
                    if ' - ERROR - ' in line:
                        level = 'error'
                        parts = line.split(' - ERROR - ', 1)
                    elif ' - WARNING - ' in line:
                        level = 'warning'
                        parts = line.split(' - WARNING - ', 1)
                    elif ' - CRITICAL - ' in line:
                        level = 'critical'
                        parts = line.split(' - CRITICAL - ', 1)
                    else:
                        level = 'info'
                        parts = line.split(' - INFO - ', 1)

                    timestamp = parts[0]
                    message = parts[1].strip()

                    # تحويل الطابع الزمني إلى كائن datetime
                    try:
                        log_date = datetime.strptime(timestamp, '%Y-%m-%d %H:%M:%S')
                    except ValueError:
                        log_date = None

                    # إنشاء سجل جديد
                    current_log = {
                        'timestamp': timestamp,
                        'datetime': log_date,
                        'message': message,
                        'details': [],
                        'level': level,
                        'source': 'server',  # الافتراضي هو الخادم
                        'user_id': None,
                        'username': None,
                        'ip_address': None,
                        'module': None,
                        'file': None,
                        'line': None,
                        'function': None
                    }
                elif current_log:
                    # إضافة التفاصيل إلى السجل الحالي
                    detail_line = line.strip()
                    current_log['details'].append(detail_line)

                    # استخراج معلومات إضافية من التفاصيل
                    if 'user_id' in detail_line and ':' in detail_line:
                        try:
                            current_log['user_id'] = detail_line.split(':', 1)[1].strip()
                        except:
                            pass

                    if 'username' in detail_line and ':' in detail_line:
                        try:
                            current_log['username'] = detail_line.split(':', 1)[1].strip()
                        except:
                            pass

                    if 'remote_addr' in detail_line and ':' in detail_line:
                        try:
                            current_log['ip_address'] = detail_line.split(':', 1)[1].strip()
                        except:
                            pass

                    if 'module' in detail_line and ':' in detail_line:
                        try:
                            current_log['module'] = detail_line.split(':', 1)[1].strip()
                        except:
                            pass

                    if 'الملف' in detail_line and ':' in detail_line:
                        try:
                            current_log['file'] = detail_line.split(':', 1)[1].strip()
                        except:
                            pass

                    if 'السطر' in detail_line and ':' in detail_line:
                        try:
                            current_log['line'] = detail_line.split(':', 1)[1].strip()
                        except:
                            pass

                    if 'الدالة' in detail_line and ':' in detail_line:
                        try:
                            current_log['function'] = detail_line.split(':', 1)[1].strip()
                        except:
                            pass

            # إضافة السجل الأخير إلى القائمة
            if current_log:
                logs.append(current_log)

        # تطبيق التصفية
        filtered_logs = []
        for log in logs:
            # تصفية حسب التاريخ
            if log.get('datetime'):
                if start_date and log['datetime'] < start_date:
                    continue
                if end_date and log['datetime'] > end_date:
                    continue

            # تصفية حسب معرف المستخدم
            if user_id and str(log.get('user_id')) != str(user_id):
                continue

            # تصفية حسب المصدر
            if source and log.get('source', 'server') != source:
                continue

            # تصفية حسب عنوان IP
            if ip_address and log.get('ip_address') != ip_address:
                continue

            # تصفية حسب الوحدة
            if module and log.get('module') != module:
                continue

            # تصفية حسب نص البحث
            if search:
                search_lower = search.lower()
                message_lower = log['message'].lower()
                details_text = ' '.join(log['details']).lower()

                if search_lower not in message_lower and search_lower not in details_text:
                    continue

            filtered_logs.append(log)

        # ترتيب السجلات
        if sort_by == 'level':
            # ترتيب حسب مستوى الخطورة
            level_order = {'critical': 0, 'error': 1, 'warning': 2, 'info': 3}
            filtered_logs.sort(key=lambda x: level_order.get(x.get('level', 'info'), 4))
        elif sort_by == 'message':
            # ترتيب حسب الرسالة
            filtered_logs.sort(key=lambda x: x.get('message', ''))
        else:
            # ترتيب حسب التاريخ (الافتراضي)
            filtered_logs.sort(key=lambda x: x.get('timestamp', ''))

        # عكس الترتيب إذا كان تنازليًا
        if sort_order.lower() == 'desc':
            filtered_logs.reverse()

        # تطبيق الحد والإزاحة
        paginated_logs = filtered_logs[offset:offset + limit] if offset < len(filtered_logs) else []

        return paginated_logs

    def clear_error_logs(self, log_type=None):
        """
        مسح سجلات الأخطاء

        Args:
            log_type (str): نوع السجل المراد مسحه (error, warning, info, critical)
                            إذا كانت القيمة None، يتم مسح جميع السجلات

        Returns:
            bool: True إذا تم المسح بنجاح، False خلاف ذلك
        """
        try:
            # تحديد مسارات ملفات السجلات
            logs_dir = os.path.join(self.app.root_path, 'logs')
            log_files = []

            # تحديد ملفات السجلات المطلوب مسحها
            if log_type == 'error' or log_type is None:
                log_files.append(os.path.join(logs_dir, 'errors', 'error.log'))
                log_files.append(os.path.join(logs_dir, 'error.log'))  # الملف القديم

            if log_type == 'critical' or log_type is None:
                log_files.append(os.path.join(logs_dir, 'errors', 'critical.log'))

            if log_type == 'warning' or log_type is None:
                log_files.append(os.path.join(logs_dir, 'warnings', 'warning.log'))

            if log_type == 'info' or log_type is None:
                log_files.append(os.path.join(logs_dir, 'info', 'app.log'))
                log_files.append(os.path.join(logs_dir, 'app.log'))  # الملف القديم

            # مسح ملفات السجلات
            success = False
            for log_file in log_files:
                if os.path.exists(log_file):
                    with open(log_file, 'w', encoding='utf-8') as f:
                        f.write('')
                    success = True

                    if self.logger:
                        self.logger.info(f"تم مسح ملف السجل: {log_file}")

            # تسجيل عملية المسح
            if success and self.logger:
                self.logger.info(f"تم مسح سجلات الأخطاء بنجاح. النوع: {log_type or 'الكل'}")

            return success
        except Exception as e:
            if self.logger:
                self.logger.error(f"خطأ أثناء مسح سجلات الأخطاء: {str(e)}")
            return False


# إنشاء كائن المسجل
error_logger = ErrorLogger()
