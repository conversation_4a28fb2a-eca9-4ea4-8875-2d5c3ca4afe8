<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ 'إضافة مستخدم جديد' if action == 'create' else 'تعديل المستخدم' }} - نظام إدارة المبيعات</title>
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com/3.4.16"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#6366F1',
                        secondary: '#10b981',
                        success: '#22c55e',
                        warning: '#f59e0b',
                        danger: '#ef4444',
                        info: '#3b82f6'
                    },
                    container: {
                        center: true,
                        padding: {
                            DEFAULT: '1rem',
                            sm: '2rem',
                            lg: '4rem',
                            xl: '5rem',
                            '2xl': '6rem',
                        },
                    },
                    borderRadius: {
                        'none': '0px',
                        'sm': '4px',
                        DEFAULT: '8px',
                        'md': '12px',
                        'lg': '16px',
                        'xl': '20px',
                        '2xl': '24px',
                        '3xl': '32px',
                        'full': '9999px'
                    }
                }
            }
        }
    </script>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@400;500;700&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Pacifico&display=swap" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/remixicon@3.5.0/fonts/remixicon.css" rel="stylesheet">
    <style>
        :where([class^="ri-"])::before { content: "\f3c2"; }
        body {
            font-family: 'Tajawal', sans-serif;
            background-color: #f9fafb;
        }
        .scrollbar-hide::-webkit-scrollbar {
            display: none;
        }
        .scrollbar-hide {
            -ms-overflow-style: none;
            scrollbar-width: none;
        }
        .bg-pattern {
            background-image: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%236366F1' fill-opacity='0.05'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
        }
        .glass-effect {
            backdrop-filter: blur(8px);
            background: rgba(255, 255, 255, 0.8);
            border: 1px solid rgba(255, 255, 255, 0.3);
            box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.07);
        }
        .pulse-animation {
            animation: pulse 2s infinite;
        }
        @keyframes pulse {
            0% {
                box-shadow: 0 0 0 0 rgba(99, 102, 241, 0.4);
            }
            70% {
                box-shadow: 0 0 0 10px rgba(99, 102, 241, 0);
            }
            100% {
                box-shadow: 0 0 0 0 rgba(99, 102, 241, 0);
            }
        }
        .card-hover {
            transition: all 0.3s ease;
        }
        .card-hover:hover {
            transform: translateY(-5px);
        }
    </style>
</head>
<body class="bg-pattern">
    <div class="flex h-screen overflow-hidden">
        <!-- Sidebar -->
        {% include 'partials/sidebar.html' %}
        
        <!-- Main Content -->
        <div class="flex-1 flex flex-col overflow-hidden">
            <!-- Top Navigation -->
            {% include 'partials/topnav.html' %}
            
            <!-- Content Area -->
            <main class="flex-1 overflow-y-auto p-6">
                <!-- Page Header -->
                <div class="mb-6 flex flex-wrap items-center justify-between">
                    <div>
                        <h1 class="text-2xl font-bold text-gray-800">{{ 'إضافة مستخدم جديد' if action == 'create' else 'تعديل المستخدم' }}</h1>
                        <p class="text-gray-600">{{ 'إضافة مستخدم جديد إلى النظام' if action == 'create' else 'تعديل بيانات المستخدم' }}</p>
                    </div>
                    <a href="{{ url_for('users.index') }}" class="px-4 py-2 bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 transition-all whitespace-nowrap">
                        <i class="ri-arrow-right-line"></i>
                        العودة للمستخدمين
                    </a>
                </div>
                
                <!-- User Form -->
                <div class="bg-white rounded-xl shadow-sm overflow-hidden border border-gray-100">
                    <div class="p-6 border-b border-gray-100">
                        <div class="flex items-center">
                            <div class="w-8 h-8 rounded-full bg-indigo-100 text-indigo-600 flex items-center justify-center mr-3">
                                <i class="ri-user-{{ 'add' if action == 'create' else 'settings' }}-line"></i>
                            </div>
                            <h3 class="text-lg font-bold text-gray-800">{{ 'معلومات المستخدم الجديد' if action == 'create' else 'تعديل معلومات المستخدم' }}</h3>
                        </div>
                    </div>
                    
                    <form method="POST" class="p-6">
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <!-- اسم المستخدم -->
                            <div>
                                <label for="username" class="block text-sm font-medium text-gray-700 mb-1">اسم المستخدم <span class="text-red-500">*</span></label>
                                <input type="text" id="username" name="username" value="{{ user.username if user else '' }}" required class="block w-full bg-gray-50 border border-gray-300 rounded-lg py-2.5 px-4 text-sm placeholder-gray-400 focus:outline-none focus:bg-white focus:border-primary focus:ring-1 focus:ring-primary transition-all" placeholder="أدخل اسم المستخدم">
                            </div>
                            
                            <!-- البريد الإلكتروني -->
                            <div>
                                <label for="email" class="block text-sm font-medium text-gray-700 mb-1">البريد الإلكتروني <span class="text-red-500">*</span></label>
                                <input type="email" id="email" name="email" value="{{ user.email if user else '' }}" required class="block w-full bg-gray-50 border border-gray-300 rounded-lg py-2.5 px-4 text-sm placeholder-gray-400 focus:outline-none focus:bg-white focus:border-primary focus:ring-1 focus:ring-primary transition-all" placeholder="أدخل البريد الإلكتروني">
                            </div>
                            
                            <!-- الاسم الكامل -->
                            <div>
                                <label for="full_name" class="block text-sm font-medium text-gray-700 mb-1">الاسم الكامل</label>
                                <input type="text" id="full_name" name="full_name" value="{{ user.full_name if user and user.full_name else '' }}" class="block w-full bg-gray-50 border border-gray-300 rounded-lg py-2.5 px-4 text-sm placeholder-gray-400 focus:outline-none focus:bg-white focus:border-primary focus:ring-1 focus:ring-primary transition-all" placeholder="أدخل الاسم الكامل">
                            </div>
                            
                            <!-- الصلاحية -->
                            <div>
                                <label for="role" class="block text-sm font-medium text-gray-700 mb-1">الصلاحية <span class="text-red-500">*</span></label>
                                <div class="relative">
                                    <select id="role" name="role" required class="block w-full bg-gray-50 border border-gray-300 rounded-lg py-2.5 pr-3 text-sm focus:outline-none focus:bg-white focus:border-primary focus:ring-1 focus:ring-primary appearance-none transition-all">
                                        <option value="admin" {% if user and user.role == 'admin' %}selected{% endif %}>مدير</option>
                                        <option value="staff" {% if user and user.role == 'staff' %}selected{% endif %}>موظف</option>
                                    </select>
                                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                        <i class="ri-arrow-down-s-line text-gray-400"></i>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- كلمة المرور -->
                            <div>
                                <label for="password" class="block text-sm font-medium text-gray-700 mb-1">كلمة المرور {{ '<span class="text-red-500">*</span>' if action == 'create' else '<span class="text-gray-500">(اتركها فارغة للاحتفاظ بكلمة المرور الحالية)</span>' | safe }}</label>
                                <div class="relative">
                                    <input type="password" id="password" name="password" {{ 'required' if action == 'create' else '' }} class="block w-full bg-gray-50 border border-gray-300 rounded-lg py-2.5 px-4 text-sm placeholder-gray-400 focus:outline-none focus:bg-white focus:border-primary focus:ring-1 focus:ring-primary transition-all" placeholder="أدخل كلمة المرور">
                                    <button type="button" onclick="togglePasswordVisibility('password')" class="absolute inset-y-0 left-0 pl-3 flex items-center">
                                        <i class="ri-eye-line text-gray-400 hover:text-gray-600"></i>
                                    </button>
                                </div>
                            </div>
                            
                            <!-- تأكيد كلمة المرور -->
                            <div>
                                <label for="confirm_password" class="block text-sm font-medium text-gray-700 mb-1">تأكيد كلمة المرور {{ '<span class="text-red-500">*</span>' if action == 'create' else '' | safe }}</label>
                                <div class="relative">
                                    <input type="password" id="confirm_password" name="confirm_password" {{ 'required' if action == 'create' else '' }} class="block w-full bg-gray-50 border border-gray-300 rounded-lg py-2.5 px-4 text-sm placeholder-gray-400 focus:outline-none focus:bg-white focus:border-primary focus:ring-1 focus:ring-primary transition-all" placeholder="أعد إدخال كلمة المرور">
                                    <button type="button" onclick="togglePasswordVisibility('confirm_password')" class="absolute inset-y-0 left-0 pl-3 flex items-center">
                                        <i class="ri-eye-line text-gray-400 hover:text-gray-600"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Submit Buttons -->
                        <div class="flex justify-end space-x-2 space-x-reverse mt-8">
                            <button type="submit" class="px-6 py-2.5 bg-gradient-to-r from-indigo-600 to-indigo-700 text-white rounded-lg shadow-md hover:shadow-lg focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-all whitespace-nowrap flex items-center gap-2">
                                <i class="ri-save-line"></i>
                                {{ 'إضافة المستخدم' if action == 'create' else 'حفظ التغييرات' }}
                            </button>
                            <a href="{{ url_for('users.index') }}" class="px-6 py-2.5 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 transition-all whitespace-nowrap flex items-center gap-2">
                                <i class="ri-close-line"></i>
                                إلغاء
                            </a>
                        </div>
                    </form>
                </div>
            </main>
        </div>
    </div>
    
    <script>
        function togglePasswordVisibility(inputId) {
            const input = document.getElementById(inputId);
            const icon = input.nextElementSibling.querySelector('i');
            
            if (input.type === 'password') {
                input.type = 'text';
                icon.classList.remove('ri-eye-line');
                icon.classList.add('ri-eye-off-line');
            } else {
                input.type = 'password';
                icon.classList.remove('ri-eye-off-line');
                icon.classList.add('ri-eye-line');
            }
        }
    </script>
</body>
</html>
