2025-05-20 02:18:39 - WARNING - nobara - error_logger:164 - خطأ في جانب العميل: Console Error: خطأ في تحديث قائمة الإشعارات: {}
2025-05-20 02:18:40 - WARNING - nobara - error_logger:164 - خطأ في جانب العميل: Console Error: خطأ في تحديث عدد الإشعارات: {}
2025-05-20 02:18:48 - ERROR - nobara - error_logger:190 - خطأ في جانب العميل: Uncaught SyntaxError: Identifier 'NotificationsSystem' has already been declared
معلومات إضافية: {
  "source": "client",
  "client_stack": "SyntaxError: Identifier 'NotificationsSystem' has already been declared",
  "url": "http://127.0.0.1:5000/home",
  "timestamp": "2025-05-19T23:18:45.852Z",
  "browser": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********",
  "os": "Win32",
  "component": "home",
  "user_id": 1,
  "username": "admin",
  "client_ip": "127.0.0.1",
  "request_info": {
    "error": "فشل في الحصول على معلومات الطلب: 'Request' object has no attribute 'is_xhr'"
  }
}
2025-05-20 02:18:48 - ERROR - nobara - error_logger:190 - خطأ في جانب العميل: Uncaught SyntaxError: Identifier 'SystemManager' has already been declared
معلومات إضافية: {
  "source": "client",
  "client_stack": "SyntaxError: Identifier 'SystemManager' has already been declared",
  "url": "http://127.0.0.1:5000/home",
  "timestamp": "2025-05-19T23:18:45.856Z",
  "browser": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********",
  "os": "Win32",
  "component": "home",
  "user_id": 1,
  "username": "admin",
  "client_ip": "127.0.0.1",
  "request_info": {
    "error": "فشل في الحصول على معلومات الطلب: 'Request' object has no attribute 'is_xhr'"
  }
}
2025-05-20 02:18:49 - ERROR - nobara - error_logger:190 - خطأ في جانب العميل: Uncaught SyntaxError: Identifier 'DashboardManager' has already been declared
معلومات إضافية: {
  "source": "client",
  "client_stack": "SyntaxError: Identifier 'DashboardManager' has already been declared",
  "url": "http://127.0.0.1:5000/home",
  "timestamp": "2025-05-19T23:18:45.876Z",
  "browser": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********",
  "os": "Win32",
  "component": "home",
  "user_id": 1,
  "username": "admin",
  "client_ip": "127.0.0.1",
  "request_info": {
    "error": "فشل في الحصول على معلومات الطلب: 'Request' object has no attribute 'is_xhr'"
  }
}
2025-05-20 02:18:54 - ERROR - nobara - error_logger:190 - خطأ في جانب العميل: Uncaught SyntaxError: Identifier 'NotificationsSystem' has already been declared
معلومات إضافية: {
  "source": "client",
  "client_stack": "SyntaxError: Identifier 'NotificationsSystem' has already been declared",
  "url": "http://127.0.0.1:5000/settings/error-logs",
  "timestamp": "2025-05-19T23:18:53.595Z",
  "browser": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********",
  "os": "Win32",
  "component": "settings",
  "user_id": 1,
  "username": "admin",
  "client_ip": "127.0.0.1",
  "request_info": {
    "error": "فشل في الحصول على معلومات الطلب: 'Request' object has no attribute 'is_xhr'"
  }
}
2025-05-20 02:18:55 - ERROR - nobara - error_logger:190 - خطأ في جانب العميل: Uncaught SyntaxError: Identifier 'SystemManager' has already been declared
معلومات إضافية: {
  "source": "client",
  "client_stack": "SyntaxError: Identifier 'SystemManager' has already been declared",
  "url": "http://127.0.0.1:5000/settings/error-logs",
  "timestamp": "2025-05-19T23:18:53.598Z",
  "browser": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********",
  "os": "Win32",
  "component": "settings",
  "user_id": 1,
  "username": "admin",
  "client_ip": "127.0.0.1",
  "request_info": {
    "error": "فشل في الحصول على معلومات الطلب: 'Request' object has no attribute 'is_xhr'"
  }
}
2025-05-20 02:21:45 - ERROR - nobara - error_logger:190 - خطأ في جانب العميل: Uncaught SyntaxError: Identifier 'NotificationsSystem' has already been declared
معلومات إضافية: {
  "source": "client",
  "client_stack": "SyntaxError: Identifier 'NotificationsSystem' has already been declared",
  "url": "http://127.0.0.1:5000/settings/error-logs",
  "timestamp": "2025-05-19T23:21:44.425Z",
  "browser": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********",
  "os": "Win32",
  "component": "settings",
  "user_id": 1,
  "username": "admin",
  "client_ip": "127.0.0.1",
  "request_info": {
    "error": "فشل في الحصول على معلومات الطلب: 'Request' object has no attribute 'is_xhr'"
  }
}
2025-05-20 02:21:46 - ERROR - nobara - error_logger:190 - خطأ في جانب العميل: Uncaught SyntaxError: Identifier 'SystemManager' has already been declared
معلومات إضافية: {
  "source": "client",
  "client_stack": "SyntaxError: Identifier 'SystemManager' has already been declared",
  "url": "http://127.0.0.1:5000/settings/error-logs",
  "timestamp": "2025-05-19T23:21:44.427Z",
  "browser": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********",
  "os": "Win32",
  "component": "settings",
  "user_id": 1,
  "username": "admin",
  "client_ip": "127.0.0.1",
  "request_info": {
    "error": "فشل في الحصول على معلومات الطلب: 'Request' object has no attribute 'is_xhr'"
  }
}
2025-05-20 02:22:03 - ERROR - nobara - error_logger:190 - خطأ في جانب العميل: Uncaught SyntaxError: Identifier 'NotificationsSystem' has already been declared
معلومات إضافية: {
  "source": "client",
  "client_stack": "SyntaxError: Identifier 'NotificationsSystem' has already been declared",
  "url": "http://127.0.0.1:5000/settings/error-logs",
  "timestamp": "2025-05-19T23:22:02.395Z",
  "browser": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********",
  "os": "Win32",
  "component": "settings",
  "user_id": 1,
  "username": "admin",
  "client_ip": "127.0.0.1",
  "request_info": {
    "error": "فشل في الحصول على معلومات الطلب: 'Request' object has no attribute 'is_xhr'"
  }
}
2025-05-20 02:22:08 - ERROR - nobara - error_logger:190 - خطأ في جانب العميل: Uncaught SyntaxError: Identifier 'NotificationsSystem' has already been declared
معلومات إضافية: {
  "source": "client",
  "client_stack": "SyntaxError: Identifier 'NotificationsSystem' has already been declared",
  "url": "http://127.0.0.1:5000/settings/error-logs",
  "timestamp": "2025-05-19T23:22:07.071Z",
  "browser": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********",
  "os": "Win32",
  "component": "settings",
  "user_id": 1,
  "username": "admin",
  "client_ip": "127.0.0.1",
  "request_info": {
    "error": "فشل في الحصول على معلومات الطلب: 'Request' object has no attribute 'is_xhr'"
  }
}
2025-05-20 02:22:09 - ERROR - nobara - error_logger:190 - خطأ في جانب العميل: Uncaught SyntaxError: Identifier 'SystemManager' has already been declared
معلومات إضافية: {
  "source": "client",
  "client_stack": "SyntaxError: Identifier 'SystemManager' has already been declared",
  "url": "http://127.0.0.1:5000/settings/error-logs",
  "timestamp": "2025-05-19T23:22:07.075Z",
  "browser": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********",
  "os": "Win32",
  "component": "settings",
  "user_id": 1,
  "username": "admin",
  "client_ip": "127.0.0.1",
  "request_info": {
    "error": "فشل في الحصول على معلومات الطلب: 'Request' object has no attribute 'is_xhr'"
  }
}
2025-05-20 02:22:23 - ERROR - nobara - error_logger:190 - خطأ في جانب العميل: Uncaught SyntaxError: Identifier 'NotificationsSystem' has already been declared
معلومات إضافية: {
  "source": "client",
  "client_stack": "SyntaxError: Identifier 'NotificationsSystem' has already been declared",
  "url": "http://127.0.0.1:5000/settings/error-logs",
  "timestamp": "2025-05-19T23:22:22.464Z",
  "browser": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********",
  "os": "Win32",
  "component": "settings",
  "user_id": 1,
  "username": "admin",
  "client_ip": "127.0.0.1",
  "request_info": {
    "error": "فشل في الحصول على معلومات الطلب: 'Request' object has no attribute 'is_xhr'"
  }
}
2025-05-20 02:22:35 - ERROR - nobara - error_logger:190 - خطأ في جانب العميل: Uncaught SyntaxError: Identifier 'NotificationsSystem' has already been declared
معلومات إضافية: {
  "source": "client",
  "client_stack": "SyntaxError: Identifier 'NotificationsSystem' has already been declared",
  "url": "http://127.0.0.1:5000/settings/error-logs",
  "timestamp": "2025-05-19T23:22:34.268Z",
  "browser": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********",
  "os": "Win32",
  "component": "settings",
  "user_id": 1,
  "username": "admin",
  "client_ip": "127.0.0.1",
  "request_info": {
    "error": "فشل في الحصول على معلومات الطلب: 'Request' object has no attribute 'is_xhr'"
  }
}
2025-05-20 02:22:36 - ERROR - nobara - error_logger:190 - خطأ في جانب العميل: Uncaught SyntaxError: Identifier 'SystemManager' has already been declared
معلومات إضافية: {
  "source": "client",
  "client_stack": "SyntaxError: Identifier 'SystemManager' has already been declared",
  "url": "http://127.0.0.1:5000/settings/error-logs",
  "timestamp": "2025-05-19T23:22:34.273Z",
  "browser": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********",
  "os": "Win32",
  "component": "settings",
  "user_id": 1,
  "username": "admin",
  "client_ip": "127.0.0.1",
  "request_info": {
    "error": "فشل في الحصول على معلومات الطلب: 'Request' object has no attribute 'is_xhr'"
  }
}
2025-05-20 02:22:41 - ERROR - nobara - error_logger:190 - خطأ في جانب العميل: Uncaught SyntaxError: Identifier 'NotificationsSystem' has already been declared
معلومات إضافية: {
  "source": "client",
  "client_stack": "SyntaxError: Identifier 'NotificationsSystem' has already been declared",
  "url": "http://************:5000/home",
  "timestamp": "2025-05-19T23:22:39.704Z",
  "browser": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********",
  "os": "Win32",
  "component": "home",
  "user_id": 1,
  "username": "admin",
  "client_ip": "************",
  "request_info": {
    "error": "فشل في الحصول على معلومات الطلب: 'Request' object has no attribute 'is_xhr'"
  }
}
2025-05-20 02:22:42 - ERROR - nobara - error_logger:190 - خطأ في جانب العميل: Uncaught SyntaxError: Identifier 'SystemManager' has already been declared
معلومات إضافية: {
  "source": "client",
  "client_stack": "SyntaxError: Identifier 'SystemManager' has already been declared",
  "url": "http://************:5000/home",
  "timestamp": "2025-05-19T23:22:39.707Z",
  "browser": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********",
  "os": "Win32",
  "component": "home",
  "user_id": 1,
  "username": "admin",
  "client_ip": "************",
  "request_info": {
    "error": "فشل في الحصول على معلومات الطلب: 'Request' object has no attribute 'is_xhr'"
  }
}
2025-05-20 02:22:43 - ERROR - nobara - error_logger:190 - خطأ في جانب العميل: Uncaught SyntaxError: Identifier 'DashboardManager' has already been declared
معلومات إضافية: {
  "source": "client",
  "client_stack": "SyntaxError: Identifier 'DashboardManager' has already been declared",
  "url": "http://************:5000/home",
  "timestamp": "2025-05-19T23:22:39.744Z",
  "browser": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********",
  "os": "Win32",
  "component": "home",
  "user_id": 1,
  "username": "admin",
  "client_ip": "************",
  "request_info": {
    "error": "فشل في الحصول على معلومات الطلب: 'Request' object has no attribute 'is_xhr'"
  }
}
2025-05-20 02:22:45 - ERROR - nobara - error_logger:190 - خطأ في جانب العميل: Uncaught SyntaxError: Identifier 'NotificationsSystem' has already been declared
معلومات إضافية: {
  "source": "client",
  "client_stack": "SyntaxError: Identifier 'NotificationsSystem' has already been declared",
  "url": "http://************:5000/home",
  "timestamp": "2025-05-19T23:22:44.455Z",
  "browser": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********",
  "os": "Win32",
  "component": "home",
  "user_id": 1,
  "username": "admin",
  "client_ip": "************",
  "request_info": {
    "error": "فشل في الحصول على معلومات الطلب: 'Request' object has no attribute 'is_xhr'"
  }
}
2025-05-20 02:23:14 - ERROR - nobara - error_logger:190 - خطأ في جانب العميل: Uncaught SyntaxError: Identifier 'NotificationsSystem' has already been declared
معلومات إضافية: {
  "source": "client",
  "client_stack": "SyntaxError: Identifier 'NotificationsSystem' has already been declared",
  "url": "http://127.0.0.1:5000/settings/error-logs",
  "timestamp": "2025-05-19T23:23:13.362Z",
  "browser": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********",
  "os": "Win32",
  "component": "settings",
  "user_id": 1,
  "username": "admin",
  "client_ip": "127.0.0.1",
  "request_info": {
    "error": "فشل في الحصول على معلومات الطلب: 'Request' object has no attribute 'is_xhr'"
  }
}
2025-05-20 02:23:15 - ERROR - nobara - error_logger:190 - خطأ في جانب العميل: Uncaught SyntaxError: Identifier 'SystemManager' has already been declared
معلومات إضافية: {
  "source": "client",
  "client_stack": "SyntaxError: Identifier 'SystemManager' has already been declared",
  "url": "http://127.0.0.1:5000/settings/error-logs",
  "timestamp": "2025-05-19T23:23:13.369Z",
  "browser": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********",
  "os": "Win32",
  "component": "settings",
  "user_id": 1,
  "username": "admin",
  "client_ip": "127.0.0.1",
  "request_info": {
    "error": "فشل في الحصول على معلومات الطلب: 'Request' object has no attribute 'is_xhr'"
  }
}
2025-05-20 12:57:14 - ERROR - nobara - error_logger:190 - خطأ في جانب العميل: Uncaught SyntaxError: Identifier 'DashboardManager' has already been declared
معلومات إضافية: {
  "source": "client",
  "client_stack": "SyntaxError: Identifier 'DashboardManager' has already been declared",
  "url": "http://************:5000/home",
  "timestamp": "2025-05-20T09:57:13.193Z",
  "browser": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********",
  "os": "Win32",
  "component": "home",
  "user_id": 1,
  "username": "admin",
  "client_ip": "************",
  "request_info": {
    "error": "فشل في الحصول على معلومات الطلب: 'Request' object has no attribute 'is_xhr'"
  }
}
2025-05-20 12:57:20 - ERROR - nobara - error_logger:270 - استثناء: UndefinedError: 'jinja2.runtime.TemplateReference object' has no attribute 'title'
الوقت: 2025-05-20 12:57:20
نوع الاستثناء: UndefinedError
الملف: app.py
السطر: 809
الدالة: handle_user_exception
معلومات الطلب: {
  "error": "فشل في الحصول على معلومات الطلب: 'Request' object has no attribute 'is_xhr'"
}
معلومات النظام: {
  "memory_usage": "99.92 MB",
  "cpu_percent": "0.0%",
  "memory_percent": "82.2%",
  "disk_usage": "49.5%",
  "open_files": 11,
  "threads": 13,
  "connections": 0,
  "uptime": "0:00:23"
}
تتبع الاستثناء:
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask\app.py", line 917, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask\app.py", line 902, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)  # type: ignore[no-any-return]
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask_login\utils.py", line 290, in decorated_view
    return current_app.ensure_sync(func)(*args, **kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\routes\products.py", line 23, in import_export
    return render_template('products/import_export.html',
                          warehouses=warehouses,
                          categories=categories)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask\templating.py", line 150, in render_template
    return _render(app, template, context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask\templating.py", line 131, in _render
    rv = template.render(context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\jinja2\environment.py", line 1295, in render
    self.environment.handle_exception()
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\jinja2\environment.py", line 942, in handle_exception
    raise rewrite_traceback_stack(source=source)
  File "c:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\templates\products\import_export.html", line 1, in top-level template code
    {% extends 'base.html' %}
  File "c:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\templates\base.html", line 15, in top-level template code
    {% include 'core/partials/topnav.html' %}
    ^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\templates\core\partials\topnav.html", line 11, in top-level template code
    <h1 class="text-lg font-semibold text-gray-800">{{ self.title() }}</h1>
    ^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\jinja2\utils.py", line 92, in from_obj
    if hasattr(obj, "jinja_pass_arg"):
       ~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
jinja2.exceptions.UndefinedError: 'jinja2.runtime.TemplateReference object' has no attribute 'title'

2025-05-20 12:58:57 - ERROR - nobara - error_logger:270 - استثناء: UndefinedError: 'jinja2.runtime.TemplateReference object' has no attribute 'title'
الوقت: 2025-05-20 12:58:57
نوع الاستثناء: UndefinedError
الملف: app.py
السطر: 809
الدالة: handle_user_exception
معلومات الطلب: {
  "error": "فشل في الحصول على معلومات الطلب: 'Request' object has no attribute 'is_xhr'"
}
معلومات النظام: {
  "memory_usage": "95.02 MB",
  "cpu_percent": "0.0%",
  "memory_percent": "77.2%",
  "disk_usage": "49.3%",
  "open_files": 11,
  "threads": 9,
  "connections": 0,
  "uptime": "0:01:59"
}
تتبع الاستثناء:
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask\app.py", line 917, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask\app.py", line 902, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)  # type: ignore[no-any-return]
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask_login\utils.py", line 290, in decorated_view
    return current_app.ensure_sync(func)(*args, **kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\routes\products.py", line 23, in import_export
    return render_template('products/import_export.html',
                          warehouses=warehouses,
                          categories=categories)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask\templating.py", line 150, in render_template
    return _render(app, template, context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask\templating.py", line 131, in _render
    rv = template.render(context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\jinja2\environment.py", line 1295, in render
    self.environment.handle_exception()
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\jinja2\environment.py", line 942, in handle_exception
    raise rewrite_traceback_stack(source=source)
  File "c:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\templates\products\import_export.html", line 1, in top-level template code
    {% extends 'base.html' %}
  File "c:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\templates\base.html", line 15, in top-level template code
    {% include 'core/partials/topnav.html' %}
    ^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\templates\core\partials\topnav.html", line 11, in top-level template code
    <h1 class="text-lg font-semibold text-gray-800">{{ self.title() }}</h1>
    ^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\jinja2\utils.py", line 92, in from_obj
    if hasattr(obj, "jinja_pass_arg"):
       ~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
jinja2.exceptions.UndefinedError: 'jinja2.runtime.TemplateReference object' has no attribute 'title'

2025-05-20 13:01:17 - ERROR - nobara - error_logger:270 - استثناء: UndefinedError: 'jinja2.runtime.TemplateReference object' has no attribute 'title'
الوقت: 2025-05-20 13:01:17
نوع الاستثناء: UndefinedError
الملف: app.py
السطر: 809
الدالة: handle_user_exception
معلومات الطلب: {
  "error": "فشل في الحصول على معلومات الطلب: 'Request' object has no attribute 'is_xhr'"
}
معلومات النظام: {
  "memory_usage": "99.40 MB",
  "cpu_percent": "0.0%",
  "memory_percent": "79.3%",
  "disk_usage": "49.6%",
  "open_files": 12,
  "threads": 12,
  "connections": 0,
  "uptime": "0:00:21"
}
تتبع الاستثناء:
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask\app.py", line 917, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask\app.py", line 902, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)  # type: ignore[no-any-return]
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask_login\utils.py", line 290, in decorated_view
    return current_app.ensure_sync(func)(*args, **kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\routes\products.py", line 23, in import_export
    return render_template('products/import_export.html',
                          warehouses=warehouses,
                          categories=categories)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask\templating.py", line 150, in render_template
    return _render(app, template, context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask\templating.py", line 131, in _render
    rv = template.render(context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\jinja2\environment.py", line 1295, in render
    self.environment.handle_exception()
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\jinja2\environment.py", line 942, in handle_exception
    raise rewrite_traceback_stack(source=source)
  File "c:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\templates\products\import_export.html", line 1, in top-level template code
    {% extends 'base.html' %}
  File "c:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\templates\base.html", line 15, in top-level template code
    {% include 'core/partials/topnav.html' %}
    ^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\templates\core\partials\topnav.html", line 11, in top-level template code
    <h1 class="text-lg font-semibold text-gray-800">{{ self.title() }}</h1>
    ^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\jinja2\utils.py", line 92, in from_obj
    if hasattr(obj, "jinja_pass_arg"):
       ~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
jinja2.exceptions.UndefinedError: 'jinja2.runtime.TemplateReference object' has no attribute 'title'

2025-05-20 13:01:20 - ERROR - nobara - error_logger:270 - استثناء: UndefinedError: 'jinja2.runtime.TemplateReference object' has no attribute 'title'
الوقت: 2025-05-20 13:01:20
نوع الاستثناء: UndefinedError
الملف: app.py
السطر: 809
الدالة: handle_user_exception
معلومات الطلب: {
  "error": "فشل في الحصول على معلومات الطلب: 'Request' object has no attribute 'is_xhr'"
}
معلومات النظام: {
  "memory_usage": "100.26 MB",
  "cpu_percent": "0.0%",
  "memory_percent": "80.4%",
  "disk_usage": "49.6%",
  "open_files": 12,
  "threads": 13,
  "connections": 0,
  "uptime": "0:00:25"
}
تتبع الاستثناء:
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask\app.py", line 917, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask\app.py", line 902, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)  # type: ignore[no-any-return]
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask_login\utils.py", line 290, in decorated_view
    return current_app.ensure_sync(func)(*args, **kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\routes\products.py", line 23, in import_export
    return render_template('products/import_export.html',
                          warehouses=warehouses,
                          categories=categories)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask\templating.py", line 150, in render_template
    return _render(app, template, context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask\templating.py", line 131, in _render
    rv = template.render(context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\jinja2\environment.py", line 1295, in render
    self.environment.handle_exception()
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\jinja2\environment.py", line 942, in handle_exception
    raise rewrite_traceback_stack(source=source)
  File "c:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\templates\products\import_export.html", line 1, in top-level template code
    {% extends 'base.html' %}
  File "c:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\templates\base.html", line 15, in top-level template code
    {% include 'core/partials/topnav.html' %}
    ^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\templates\core\partials\topnav.html", line 11, in top-level template code
    <h1 class="text-lg font-semibold text-gray-800">{{ self.title() }}</h1>
    ^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\jinja2\utils.py", line 92, in from_obj
    if hasattr(obj, "jinja_pass_arg"):
       ~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
jinja2.exceptions.UndefinedError: 'jinja2.runtime.TemplateReference object' has no attribute 'title'

2025-05-20 13:01:21 - ERROR - nobara - error_logger:190 - خطأ في جانب العميل: Uncaught SyntaxError: Identifier 'DashboardManager' has already been declared
معلومات إضافية: {
  "source": "client",
  "client_stack": "SyntaxError: Identifier 'DashboardManager' has already been declared",
  "url": "http://************:5000/home",
  "timestamp": "2025-05-20T10:01:20.489Z",
  "browser": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********",
  "os": "Win32",
  "component": "home",
  "user_id": 1,
  "username": "admin",
  "client_ip": "************",
  "request_info": {
    "error": "فشل في الحصول على معلومات الطلب: 'Request' object has no attribute 'is_xhr'"
  }
}
2025-05-20 13:01:25 - ERROR - nobara - error_logger:270 - استثناء: UndefinedError: 'jinja2.runtime.TemplateReference object' has no attribute 'title'
الوقت: 2025-05-20 13:01:25
نوع الاستثناء: UndefinedError
الملف: app.py
السطر: 809
الدالة: handle_user_exception
معلومات الطلب: {
  "error": "فشل في الحصول على معلومات الطلب: 'Request' object has no attribute 'is_xhr'"
}
معلومات النظام: {
  "memory_usage": "99.61 MB",
  "cpu_percent": "0.0%",
  "memory_percent": "81.0%",
  "disk_usage": "49.6%",
  "open_files": 12,
  "threads": 13,
  "connections": 0,
  "uptime": "0:00:30"
}
تتبع الاستثناء:
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask\app.py", line 917, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask\app.py", line 902, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)  # type: ignore[no-any-return]
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask_login\utils.py", line 290, in decorated_view
    return current_app.ensure_sync(func)(*args, **kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\routes\products.py", line 23, in import_export
    return render_template('products/import_export.html',
                          warehouses=warehouses,
                          categories=categories)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask\templating.py", line 150, in render_template
    return _render(app, template, context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask\templating.py", line 131, in _render
    rv = template.render(context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\jinja2\environment.py", line 1295, in render
    self.environment.handle_exception()
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\jinja2\environment.py", line 942, in handle_exception
    raise rewrite_traceback_stack(source=source)
  File "c:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\templates\products\import_export.html", line 1, in top-level template code
    {% extends 'base.html' %}
  File "c:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\templates\base.html", line 15, in top-level template code
    {% include 'core/partials/topnav.html' %}
    ^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\templates\core\partials\topnav.html", line 11, in top-level template code
    <h1 class="text-lg font-semibold text-gray-800">{{ self.title() }}</h1>
    ^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\jinja2\utils.py", line 92, in from_obj
    if hasattr(obj, "jinja_pass_arg"):
       ~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
jinja2.exceptions.UndefinedError: 'jinja2.runtime.TemplateReference object' has no attribute 'title'

2025-05-20 13:02:21 - ERROR - nobara - error_logger:270 - استثناء: UndefinedError: 'jinja2.runtime.TemplateReference object' has no attribute 'title'
الوقت: 2025-05-20 13:02:21
نوع الاستثناء: UndefinedError
الملف: app.py
السطر: 809
الدالة: handle_user_exception
معلومات الطلب: {
  "error": "فشل في الحصول على معلومات الطلب: 'Request' object has no attribute 'is_xhr'"
}
معلومات النظام: {
  "memory_usage": "99.56 MB",
  "cpu_percent": "0.0%",
  "memory_percent": "81.1%",
  "disk_usage": "49.6%",
  "open_files": 11,
  "threads": 16,
  "connections": 0,
  "uptime": "0:00:06"
}
تتبع الاستثناء:
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask\app.py", line 917, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask\app.py", line 902, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)  # type: ignore[no-any-return]
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask_login\utils.py", line 290, in decorated_view
    return current_app.ensure_sync(func)(*args, **kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\routes\products.py", line 23, in import_export
    return render_template('products/import_export.html',
                          warehouses=warehouses,
                          categories=categories)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask\templating.py", line 150, in render_template
    return _render(app, template, context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask\templating.py", line 131, in _render
    rv = template.render(context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\jinja2\environment.py", line 1295, in render
    self.environment.handle_exception()
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\jinja2\environment.py", line 942, in handle_exception
    raise rewrite_traceback_stack(source=source)
  File "c:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\templates\products\import_export.html", line 1, in top-level template code
    {% extends 'base.html' %}
  File "c:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\templates\base.html", line 15, in top-level template code
    {% include 'core/partials/topnav.html' %}
    ^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\templates\core\partials\topnav.html", line 11, in top-level template code
    <h1 class="text-lg font-semibold text-gray-800">{{ self.title() }}</h1>
    ^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\jinja2\utils.py", line 92, in from_obj
    if hasattr(obj, "jinja_pass_arg"):
       ~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
jinja2.exceptions.UndefinedError: 'jinja2.runtime.TemplateReference object' has no attribute 'title'

2025-05-20 13:02:23 - ERROR - nobara - error_logger:190 - خطأ في جانب العميل: Uncaught SyntaxError: Identifier 'DashboardManager' has already been declared
معلومات إضافية: {
  "source": "client",
  "client_stack": "SyntaxError: Identifier 'DashboardManager' has already been declared",
  "url": "http://************:5000/home",
  "timestamp": "2025-05-20T10:02:21.573Z",
  "browser": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********",
  "os": "Win32",
  "component": "home",
  "user_id": 1,
  "username": "admin",
  "client_ip": "************",
  "request_info": {
    "error": "فشل في الحصول على معلومات الطلب: 'Request' object has no attribute 'is_xhr'"
  }
}
2025-05-20 13:02:23 - ERROR - nobara - error_logger:270 - استثناء: UndefinedError: 'jinja2.runtime.TemplateReference object' has no attribute 'title'
الوقت: 2025-05-20 13:02:23
نوع الاستثناء: UndefinedError
الملف: app.py
السطر: 809
الدالة: handle_user_exception
معلومات الطلب: {
  "error": "فشل في الحصول على معلومات الطلب: 'Request' object has no attribute 'is_xhr'"
}
معلومات النظام: {
  "memory_usage": "100.70 MB",
  "cpu_percent": "0.0%",
  "memory_percent": "81.4%",
  "disk_usage": "49.6%",
  "open_files": 11,
  "threads": 14,
  "connections": 0,
  "uptime": "0:00:08"
}
تتبع الاستثناء:
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask\app.py", line 917, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask\app.py", line 902, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)  # type: ignore[no-any-return]
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask_login\utils.py", line 290, in decorated_view
    return current_app.ensure_sync(func)(*args, **kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\routes\products.py", line 23, in import_export
    return render_template('products/import_export.html',
                          warehouses=warehouses,
                          categories=categories)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask\templating.py", line 150, in render_template
    return _render(app, template, context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask\templating.py", line 131, in _render
    rv = template.render(context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\jinja2\environment.py", line 1295, in render
    self.environment.handle_exception()
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\jinja2\environment.py", line 942, in handle_exception
    raise rewrite_traceback_stack(source=source)
  File "c:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\templates\products\import_export.html", line 1, in top-level template code
    {% extends 'base.html' %}
  File "c:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\templates\base.html", line 15, in top-level template code
    {% include 'core/partials/topnav.html' %}
    ^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\templates\core\partials\topnav.html", line 11, in top-level template code
    <h1 class="text-lg font-semibold text-gray-800">{{ self.title() }}</h1>
    ^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\jinja2\utils.py", line 92, in from_obj
    if hasattr(obj, "jinja_pass_arg"):
       ~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
jinja2.exceptions.UndefinedError: 'jinja2.runtime.TemplateReference object' has no attribute 'title'

2025-05-20 13:07:47 - ERROR - nobara - error_logger:190 - خطأ في جانب العميل: Uncaught SyntaxError: Identifier 'DashboardManager' has already been declared
معلومات إضافية: {
  "source": "client",
  "client_stack": "SyntaxError: Identifier 'DashboardManager' has already been declared",
  "url": "http://************:5000/home",
  "timestamp": "2025-05-20T10:07:46.038Z",
  "browser": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********",
  "os": "Win32",
  "component": "home",
  "user_id": 1,
  "username": "admin",
  "client_ip": "************",
  "request_info": {
    "error": "فشل في الحصول على معلومات الطلب: 'Request' object has no attribute 'is_xhr'"
  }
}
2025-05-20 13:07:55 - ERROR - nobara - error_logger:270 - استثناء: UndefinedError: 'jinja2.runtime.TemplateReference object' has no attribute 'title'
الوقت: 2025-05-20 13:07:55
نوع الاستثناء: UndefinedError
الملف: app.py
السطر: 809
الدالة: handle_user_exception
معلومات الطلب: {
  "error": "فشل في الحصول على معلومات الطلب: 'Request' object has no attribute 'is_xhr'"
}
معلومات النظام: {
  "memory_usage": "111.13 MB",
  "cpu_percent": "0.0%",
  "memory_percent": "76.8%",
  "disk_usage": "49.6%",
  "open_files": 11,
  "threads": 14,
  "connections": 0,
  "uptime": "0:00:18"
}
تتبع الاستثناء:
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask\app.py", line 917, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask\app.py", line 902, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)  # type: ignore[no-any-return]
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask_login\utils.py", line 290, in decorated_view
    return current_app.ensure_sync(func)(*args, **kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\routes\products.py", line 23, in import_export
    return render_template('products/import_export.html',
                          warehouses=warehouses,
                          categories=categories)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask\templating.py", line 150, in render_template
    return _render(app, template, context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask\templating.py", line 131, in _render
    rv = template.render(context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\jinja2\environment.py", line 1295, in render
    self.environment.handle_exception()
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\jinja2\environment.py", line 942, in handle_exception
    raise rewrite_traceback_stack(source=source)
  File "c:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\templates\products\import_export.html", line 1, in top-level template code
    {% extends 'base.html' %}
  File "c:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\templates\base.html", line 15, in top-level template code
    {% include 'core/partials/topnav.html' %}
    ^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\templates\core\partials\topnav.html", line 11, in top-level template code
    <h1 class="text-lg font-semibold text-gray-800">{{ self.title() }}</h1>
    ^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\jinja2\utils.py", line 92, in from_obj
    if hasattr(obj, "jinja_pass_arg"):
       ~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
jinja2.exceptions.UndefinedError: 'jinja2.runtime.TemplateReference object' has no attribute 'title'

2025-05-20 13:09:57 - ERROR - nobara - error_logger:270 - استثناء: UndefinedError: 'jinja2.runtime.TemplateReference object' has no attribute 'title'
الوقت: 2025-05-20 13:09:57
نوع الاستثناء: UndefinedError
الملف: app.py
السطر: 809
الدالة: handle_user_exception
معلومات الطلب: {
  "error": "فشل في الحصول على معلومات الطلب: 'Request' object has no attribute 'is_xhr'"
}
معلومات النظام: {
  "memory_usage": "105.47 MB",
  "cpu_percent": "0.0%",
  "memory_percent": "79.4%",
  "disk_usage": "49.6%",
  "open_files": 11,
  "threads": 6,
  "connections": 0,
  "uptime": "0:02:20"
}
تتبع الاستثناء:
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask\app.py", line 917, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask\app.py", line 902, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)  # type: ignore[no-any-return]
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask_login\utils.py", line 290, in decorated_view
    return current_app.ensure_sync(func)(*args, **kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\routes\products.py", line 23, in import_export
    return render_template('products/import_export.html',
                          warehouses=warehouses,
                          categories=categories)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask\templating.py", line 150, in render_template
    return _render(app, template, context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask\templating.py", line 131, in _render
    rv = template.render(context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\jinja2\environment.py", line 1295, in render
    self.environment.handle_exception()
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\jinja2\environment.py", line 942, in handle_exception
    raise rewrite_traceback_stack(source=source)
  File "c:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\templates\products\import_export.html", line 1, in top-level template code
    {% extends 'base.html' %}
  File "c:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\templates\base.html", line 15, in top-level template code
    {% include 'core/partials/topnav.html' %}
    ^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\templates\core\partials\topnav.html", line 11, in top-level template code
    <h1 class="text-lg font-semibold text-gray-800">{{ self.title() }}</h1>
    ^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\jinja2\utils.py", line 92, in from_obj
    if hasattr(obj, "jinja_pass_arg"):
       ~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
jinja2.exceptions.UndefinedError: 'jinja2.runtime.TemplateReference object' has no attribute 'title'

2025-05-20 13:13:39 - ERROR - nobara - error_logger:270 - استثناء: UndefinedError: 'jinja2.runtime.TemplateReference object' has no attribute 'title'
الوقت: 2025-05-20 13:13:39
نوع الاستثناء: UndefinedError
الملف: app.py
السطر: 809
الدالة: handle_user_exception
معلومات الطلب: {
  "error": "فشل في الحصول على معلومات الطلب: 'Request' object has no attribute 'is_xhr'"
}
معلومات النظام: {
  "memory_usage": "110.41 MB",
  "cpu_percent": "0.0%",
  "memory_percent": "72.1%",
  "disk_usage": "49.6%",
  "open_files": 11,
  "threads": 17,
  "connections": 0,
  "uptime": "0:00:12"
}
تتبع الاستثناء:
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask\app.py", line 917, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask\app.py", line 902, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)  # type: ignore[no-any-return]
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask_login\utils.py", line 290, in decorated_view
    return current_app.ensure_sync(func)(*args, **kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\routes\products.py", line 23, in import_export
    return render_template('products/import_export.html',
                          warehouses=warehouses,
                          categories=categories)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask\templating.py", line 150, in render_template
    return _render(app, template, context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask\templating.py", line 131, in _render
    rv = template.render(context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\jinja2\environment.py", line 1295, in render
    self.environment.handle_exception()
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\jinja2\environment.py", line 942, in handle_exception
    raise rewrite_traceback_stack(source=source)
  File "c:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\templates\products\import_export.html", line 1, in top-level template code
    {% extends 'base.html' %}
  File "c:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\templates\base.html", line 15, in top-level template code
    {% include 'core/partials/topnav.html' %}
    ^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\templates\core\partials\topnav.html", line 11, in top-level template code
    <h1 class="text-lg font-semibold text-gray-800">{{ self.title() }}</h1>
    ^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\jinja2\utils.py", line 92, in from_obj
    if hasattr(obj, "jinja_pass_arg"):
       ~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
jinja2.exceptions.UndefinedError: 'jinja2.runtime.TemplateReference object' has no attribute 'title'

2025-05-20 13:13:41 - ERROR - nobara - error_logger:190 - خطأ في جانب العميل: Uncaught SyntaxError: Identifier 'DashboardManager' has already been declared
معلومات إضافية: {
  "source": "client",
  "client_stack": "SyntaxError: Identifier 'DashboardManager' has already been declared",
  "url": "http://************:5000/home",
  "timestamp": "2025-05-20T10:13:40.248Z",
  "browser": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********",
  "os": "Win32",
  "component": "home",
  "user_id": 1,
  "username": "admin",
  "client_ip": "************",
  "request_info": {
    "error": "فشل في الحصول على معلومات الطلب: 'Request' object has no attribute 'is_xhr'"
  }
}
2025-05-20 13:15:42 - ERROR - nobara - error_logger:270 - استثناء: UndefinedError: 'jinja2.runtime.TemplateReference object' has no attribute 'title'
الوقت: 2025-05-20 13:15:42
نوع الاستثناء: UndefinedError
الملف: app.py
السطر: 809
الدالة: handle_user_exception
معلومات الطلب: {
  "error": "فشل في الحصول على معلومات الطلب: 'Request' object has no attribute 'is_xhr'"
}
معلومات النظام: {
  "memory_usage": "105.54 MB",
  "cpu_percent": "0.0%",
  "memory_percent": "69.4%",
  "disk_usage": "49.4%",
  "open_files": 11,
  "threads": 6,
  "connections": 0,
  "uptime": "0:02:15"
}
تتبع الاستثناء:
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask\app.py", line 917, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask\app.py", line 902, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)  # type: ignore[no-any-return]
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask_login\utils.py", line 290, in decorated_view
    return current_app.ensure_sync(func)(*args, **kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\routes\products.py", line 23, in import_export
    return render_template('products/import_export.html',
                          warehouses=warehouses,
                          categories=categories)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask\templating.py", line 150, in render_template
    return _render(app, template, context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask\templating.py", line 131, in _render
    rv = template.render(context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\jinja2\environment.py", line 1295, in render
    self.environment.handle_exception()
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\jinja2\environment.py", line 942, in handle_exception
    raise rewrite_traceback_stack(source=source)
  File "c:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\templates\products\import_export.html", line 1, in top-level template code
    {% extends 'base.html' %}
  File "c:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\templates\base.html", line 15, in top-level template code
    {% include 'core/partials/topnav.html' %}
    ^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\templates\core\partials\topnav.html", line 11, in top-level template code
    <h1 class="text-lg font-semibold text-gray-800">{{ self.title() }}</h1>
    ^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\jinja2\utils.py", line 92, in from_obj
    if hasattr(obj, "jinja_pass_arg"):
       ~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
jinja2.exceptions.UndefinedError: 'jinja2.runtime.TemplateReference object' has no attribute 'title'

2025-05-20 13:15:43 - ERROR - nobara - error_logger:270 - استثناء: UndefinedError: 'jinja2.runtime.TemplateReference object' has no attribute 'title'
الوقت: 2025-05-20 13:15:43
نوع الاستثناء: UndefinedError
الملف: app.py
السطر: 809
الدالة: handle_user_exception
معلومات الطلب: {
  "error": "فشل في الحصول على معلومات الطلب: 'Request' object has no attribute 'is_xhr'"
}
معلومات النظام: {
  "memory_usage": "105.57 MB",
  "cpu_percent": "0.0%",
  "memory_percent": "69.4%",
  "disk_usage": "49.4%",
  "open_files": 11,
  "threads": 6,
  "connections": 0,
  "uptime": "0:02:16"
}
تتبع الاستثناء:
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask\app.py", line 917, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask\app.py", line 902, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)  # type: ignore[no-any-return]
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask_login\utils.py", line 290, in decorated_view
    return current_app.ensure_sync(func)(*args, **kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\routes\products.py", line 23, in import_export
    return render_template('products/import_export.html',
                          warehouses=warehouses,
                          categories=categories)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask\templating.py", line 150, in render_template
    return _render(app, template, context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask\templating.py", line 131, in _render
    rv = template.render(context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\jinja2\environment.py", line 1295, in render
    self.environment.handle_exception()
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\jinja2\environment.py", line 942, in handle_exception
    raise rewrite_traceback_stack(source=source)
  File "c:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\templates\products\import_export.html", line 1, in top-level template code
    {% extends 'base.html' %}
  File "c:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\templates\base.html", line 15, in top-level template code
    {% include 'core/partials/topnav.html' %}
    ^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\templates\core\partials\topnav.html", line 11, in top-level template code
    <h1 class="text-lg font-semibold text-gray-800">{{ self.title() }}</h1>
    ^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\jinja2\utils.py", line 92, in from_obj
    if hasattr(obj, "jinja_pass_arg"):
       ~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
jinja2.exceptions.UndefinedError: 'jinja2.runtime.TemplateReference object' has no attribute 'title'

2025-05-20 13:15:45 - ERROR - nobara - error_logger:270 - استثناء: UndefinedError: 'jinja2.runtime.TemplateReference object' has no attribute 'title'
الوقت: 2025-05-20 13:15:45
نوع الاستثناء: UndefinedError
الملف: app.py
السطر: 809
الدالة: handle_user_exception
معلومات الطلب: {
  "error": "فشل في الحصول على معلومات الطلب: 'Request' object has no attribute 'is_xhr'"
}
معلومات النظام: {
  "memory_usage": "105.60 MB",
  "cpu_percent": "0.0%",
  "memory_percent": "69.4%",
  "disk_usage": "49.4%",
  "open_files": 11,
  "threads": 6,
  "connections": 0,
  "uptime": "0:02:18"
}
تتبع الاستثناء:
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask\app.py", line 917, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask\app.py", line 902, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)  # type: ignore[no-any-return]
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask_login\utils.py", line 290, in decorated_view
    return current_app.ensure_sync(func)(*args, **kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\routes\products.py", line 23, in import_export
    return render_template('products/import_export.html',
                          warehouses=warehouses,
                          categories=categories)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask\templating.py", line 150, in render_template
    return _render(app, template, context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask\templating.py", line 131, in _render
    rv = template.render(context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\jinja2\environment.py", line 1295, in render
    self.environment.handle_exception()
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\jinja2\environment.py", line 942, in handle_exception
    raise rewrite_traceback_stack(source=source)
  File "c:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\templates\products\import_export.html", line 1, in top-level template code
    {% extends 'base.html' %}
  File "c:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\templates\base.html", line 15, in top-level template code
    {% include 'core/partials/topnav.html' %}
    ^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\templates\core\partials\topnav.html", line 11, in top-level template code
    <h1 class="text-lg font-semibold text-gray-800">{{ self.title() }}</h1>
    ^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\jinja2\utils.py", line 92, in from_obj
    if hasattr(obj, "jinja_pass_arg"):
       ~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
jinja2.exceptions.UndefinedError: 'jinja2.runtime.TemplateReference object' has no attribute 'title'

2025-05-20 13:15:45 - ERROR - nobara - error_logger:270 - استثناء: UndefinedError: 'jinja2.runtime.TemplateReference object' has no attribute 'title'
الوقت: 2025-05-20 13:15:45
نوع الاستثناء: UndefinedError
الملف: app.py
السطر: 809
الدالة: handle_user_exception
معلومات الطلب: {
  "error": "فشل في الحصول على معلومات الطلب: 'Request' object has no attribute 'is_xhr'"
}
معلومات النظام: {
  "memory_usage": "105.63 MB",
  "cpu_percent": "0.0%",
  "memory_percent": "69.5%",
  "disk_usage": "49.4%",
  "open_files": 11,
  "threads": 6,
  "connections": 0,
  "uptime": "0:02:18"
}
تتبع الاستثناء:
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask\app.py", line 917, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask\app.py", line 902, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)  # type: ignore[no-any-return]
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask_login\utils.py", line 290, in decorated_view
    return current_app.ensure_sync(func)(*args, **kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\routes\products.py", line 23, in import_export
    return render_template('products/import_export.html',
                          warehouses=warehouses,
                          categories=categories)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask\templating.py", line 150, in render_template
    return _render(app, template, context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask\templating.py", line 131, in _render
    rv = template.render(context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\jinja2\environment.py", line 1295, in render
    self.environment.handle_exception()
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\jinja2\environment.py", line 942, in handle_exception
    raise rewrite_traceback_stack(source=source)
  File "c:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\templates\products\import_export.html", line 1, in top-level template code
    {% extends 'base.html' %}
  File "c:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\templates\base.html", line 15, in top-level template code
    {% include 'core/partials/topnav.html' %}
    ^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\templates\core\partials\topnav.html", line 11, in top-level template code
    <h1 class="text-lg font-semibold text-gray-800">{{ self.title() }}</h1>
    ^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\jinja2\utils.py", line 92, in from_obj
    if hasattr(obj, "jinja_pass_arg"):
       ~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
jinja2.exceptions.UndefinedError: 'jinja2.runtime.TemplateReference object' has no attribute 'title'

2025-05-20 13:15:46 - ERROR - nobara - error_logger:270 - استثناء: UndefinedError: 'jinja2.runtime.TemplateReference object' has no attribute 'title'
الوقت: 2025-05-20 13:15:46
نوع الاستثناء: UndefinedError
الملف: app.py
السطر: 809
الدالة: handle_user_exception
معلومات الطلب: {
  "error": "فشل في الحصول على معلومات الطلب: 'Request' object has no attribute 'is_xhr'"
}
معلومات النظام: {
  "memory_usage": "105.64 MB",
  "cpu_percent": "0.0%",
  "memory_percent": "69.5%",
  "disk_usage": "49.4%",
  "open_files": 11,
  "threads": 6,
  "connections": 0,
  "uptime": "0:02:19"
}
تتبع الاستثناء:
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask\app.py", line 917, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask\app.py", line 902, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)  # type: ignore[no-any-return]
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask_login\utils.py", line 290, in decorated_view
    return current_app.ensure_sync(func)(*args, **kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\routes\products.py", line 23, in import_export
    return render_template('products/import_export.html',
                          warehouses=warehouses,
                          categories=categories)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask\templating.py", line 150, in render_template
    return _render(app, template, context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask\templating.py", line 131, in _render
    rv = template.render(context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\jinja2\environment.py", line 1295, in render
    self.environment.handle_exception()
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\jinja2\environment.py", line 942, in handle_exception
    raise rewrite_traceback_stack(source=source)
  File "c:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\templates\products\import_export.html", line 1, in top-level template code
    {% extends 'base.html' %}
  File "c:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\templates\base.html", line 15, in top-level template code
    {% include 'core/partials/topnav.html' %}
    ^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\templates\core\partials\topnav.html", line 11, in top-level template code
    <h1 class="text-lg font-semibold text-gray-800">{{ self.title() }}</h1>
    ^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\jinja2\utils.py", line 92, in from_obj
    if hasattr(obj, "jinja_pass_arg"):
       ~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
jinja2.exceptions.UndefinedError: 'jinja2.runtime.TemplateReference object' has no attribute 'title'

2025-05-20 13:17:50 - ERROR - nobara - error_logger:190 - خطأ في جانب العميل: Uncaught SyntaxError: Identifier 'DashboardManager' has already been declared
معلومات إضافية: {
  "source": "client",
  "client_stack": "SyntaxError: Identifier 'DashboardManager' has already been declared",
  "url": "http://************:5000/home",
  "timestamp": "2025-05-20T10:17:49.159Z",
  "browser": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********",
  "os": "Win32",
  "component": "home",
  "user_id": 1,
  "username": "admin",
  "client_ip": "************",
  "request_info": {
    "error": "فشل في الحصول على معلومات الطلب: 'Request' object has no attribute 'is_xhr'"
  }
}
