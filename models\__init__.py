"""
استيراد جميع نماذج قاعدة البيانات
"""

# استيراد النماذج من ملف models.py الرئيسي
import sys
import os
import importlib.util

# استيراد db من app
from app import db

# تحميل ملف models.py
models_path = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', 'models.py'))
spec = importlib.util.spec_from_file_location('models_main', models_path)
models_main = importlib.util.module_from_spec(spec)
spec.loader.exec_module(models_main)

# استيراد النماذج من ملف models.py
User = models_main.User
PaymentMethod = models_main.PaymentMethod
Permission = models_main.Permission
UserPermission = models_main.UserPermission
Product = models_main.Product
Category = models_main.Category
Warehouse = models_main.Warehouse
Customer = models_main.Customer
Supplier = models_main.Supplier
Order = models_main.Order
OrderItem = models_main.OrderItem
Payment = models_main.Payment
Settings = models_main.Settings
Inventory = models_main.Inventory
InventoryMovement = models_main.InventoryMovement
InventoryAlert = models_main.InventoryAlert
InventoryCount = models_main.InventoryCount
InventoryCountItem = models_main.InventoryCountItem
Notification = models_main.Notification
Purchase = models_main.Purchase
PurchaseItem = models_main.PurchaseItem
ReturnOrder = models_main.ReturnOrder
ReturnItem = models_main.ReturnItem
CashRegister = models_main.CashRegister
CashTransaction = models_main.CashTransaction
Shift = models_main.Shift
DailyCashClosure = models_main.DailyCashClosure
DeferredPaymentPlan = models_main.DeferredPaymentPlan
DeferredPaymentInstallment = models_main.DeferredPaymentInstallment
Expense = models_main.Expense

# استيراد النماذج المحاسبية
from models.accounting import (
    Account, JournalEntry, JournalEntryItem, FiscalPeriod,
    Currency, TaxType, AccountingPaymentMethod, AccountingSettings,
    CostCenter, Project, PaymentMethodAccount, AccountingConfiguration
)

# استيراد نماذج الأمان
from models.security import (
    Role, SecurityPermission, RolePermission, ActivityLog,
    UserSession, LoginAttempt, PasswordResetToken
)

# استيراد نماذج التقارير
from models.reporting import (
    CustomReport, ScheduledSalesReport, ScheduledInventoryReport,
    ScheduledFinancialReport, ScheduledCustomerReport,
    ScheduledSupplierReport, ReportExecutionLog
)

# استيراد نماذج الاستيراد والتصدير
from models.import_export import ExportLog
