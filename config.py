"""
Nobara POS System Configuration
نظام نوبارا لنقاط البيع - ملف التكوين الرئيسي
"""

import os
from datetime import timedelta

class Config:
    """Base configuration class"""
    
    # Application Settings
    APP_NAME = "Nobara POS"
    APP_NAME_AR = "نوبارا"
    APP_VERSION = "2.0.0"
    APP_DESCRIPTION = "Professional Point of Sale System"
    APP_DESCRIPTION_AR = "نظام نقاط البيع الاحترافي"
    
    # Developer Information
    DEVELOPER_NAME = "ENG/ Fouad Saber"
    DEVELOPER_PHONE = "01020073527"
    DEVELOPER_EMAIL = "<EMAIL>"
    POWERED_BY = f"Powered By {DEVELOPER_NAME} Tel: {DEVELOPER_PHONE}"
    
    # Security Settings
    SECRET_KEY = os.environ.get('SECRET_KEY') or 'nobara-pos-secret-key-2024'
    JWT_SECRET_KEY = os.environ.get('JWT_SECRET_KEY') or 'nobara-jwt-secret-2024'
    JWT_ACCESS_TOKEN_EXPIRES = timedelta(hours=24)
    
    # Database Configuration
    SQLALCHEMY_DATABASE_URI = os.environ.get('DATABASE_URL') or 'sqlite:///instance/nobara_pos.db'
    SQLALCHEMY_TRACK_MODIFICATIONS = False
    SQLALCHEMY_ENGINE_OPTIONS = {
        'pool_pre_ping': True,
        'pool_recycle': 300,
    }
    
    # File Upload Settings
    UPLOAD_FOLDER = 'static/uploads'
    MAX_CONTENT_LENGTH = 16 * 1024 * 1024  # 16MB max file size
    ALLOWED_EXTENSIONS = {'png', 'jpg', 'jpeg', 'gif', 'pdf', 'csv', 'xlsx'}
    
    # Internationalization
    LANGUAGES = {
        'ar': 'العربية',
        'en': 'English'
    }
    DEFAULT_LANGUAGE = 'ar'
    
    # Theme Settings
    THEMES = {
        'light': 'الوضع النهاري',
        'dark': 'الوضع الليلي'
    }
    DEFAULT_THEME = 'light'
    
    # Color Scheme
    PRIMARY_COLORS = {
        'red': '#dc2626',
        'blue': '#2563eb',
        'red_light': '#ef4444',
        'blue_light': '#3b82f6'
    }
    
    # Pagination
    ITEMS_PER_PAGE = 20
    MAX_ITEMS_PER_PAGE = 100
    
    # Cache Settings
    CACHE_TYPE = 'simple'
    CACHE_DEFAULT_TIMEOUT = 300
    
    # Session Settings
    PERMANENT_SESSION_LIFETIME = timedelta(hours=24)
    SESSION_COOKIE_SECURE = False  # Set to True in production with HTTPS
    SESSION_COOKIE_HTTPONLY = True
    SESSION_COOKIE_SAMESITE = 'Lax'
    
    # Email Settings (for notifications)
    MAIL_SERVER = os.environ.get('MAIL_SERVER')
    MAIL_PORT = int(os.environ.get('MAIL_PORT') or 587)
    MAIL_USE_TLS = os.environ.get('MAIL_USE_TLS', 'true').lower() in ['true', 'on', '1']
    MAIL_USERNAME = os.environ.get('MAIL_USERNAME')
    MAIL_PASSWORD = os.environ.get('MAIL_PASSWORD')
    
    # Logging Configuration
    LOG_LEVEL = os.environ.get('LOG_LEVEL', 'INFO')
    LOG_FILE = 'logs/nobara.log'
    LOG_MAX_BYTES = 10485760  # 10MB
    LOG_BACKUP_COUNT = 5
    
    # Business Settings
    DEFAULT_CURRENCY = 'EGP'
    CURRENCY_SYMBOL = 'ج.م'
    TAX_RATE = 0.14  # 14% VAT
    
    # POS Settings
    RECEIPT_PRINTER_NAME = os.environ.get('RECEIPT_PRINTER_NAME', 'Default')
    CASH_DRAWER_ENABLED = True
    BARCODE_SCANNER_ENABLED = True
    
    # Backup Settings
    BACKUP_FOLDER = 'backups'
    AUTO_BACKUP_ENABLED = True
    BACKUP_RETENTION_DAYS = 30
    
    @staticmethod
    def init_app(app):
        """Initialize application with configuration"""
        pass

class DevelopmentConfig(Config):
    """Development configuration"""
    DEBUG = True
    TESTING = False
    
class ProductionConfig(Config):
    """Production configuration"""
    DEBUG = False
    TESTING = False
    SESSION_COOKIE_SECURE = True
    
class TestingConfig(Config):
    """Testing configuration"""
    DEBUG = True
    TESTING = True
    SQLALCHEMY_DATABASE_URI = 'sqlite:///:memory:'
    WTF_CSRF_ENABLED = False

# Configuration dictionary
config = {
    'development': DevelopmentConfig,
    'production': ProductionConfig,
    'testing': TestingConfig,
    'default': DevelopmentConfig
}
