from flask import Blueprint, jsonify, request
from flask_login import login_required, current_user
from models import Warehouse, Inventory, Product, InventoryMovement
from app import db
from sqlalchemy import or_, and_

warehouses_api = Blueprint('warehouses_api', __name__)

@warehouses_api.route('/api/warehouses', methods=['GET'])
@login_required
def get_warehouses():
    """
    الحصول على قائمة المستودعات مع دعم البحث والترتيب والترقيم
    
    معلمات الاستعلام:
    - search: نص البحث (اختياري)
    - is_active: حالة المستودع (true, false, all)
    - sort_by: حقل الترتيب (name, location, created_at)
    - sort_order: ترتيب تصاعدي أو تنازلي (asc, desc)
    - page: رقم الصفحة (افتراضي: 1)
    - per_page: عدد العناصر في الصفحة (افتراضي: 20)
    """
    try:
        # الحصول على معلمات الاستعلام
        search = request.args.get('search', '')
        is_active = request.args.get('is_active', 'true')
        sort_by = request.args.get('sort_by', 'name')
        sort_order = request.args.get('sort_order', 'asc')
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 20, type=int)
        
        # إعداد الاستعلام الأساسي
        query = Warehouse.query
        
        # تطبيق فلتر البحث
        if search:
            query = query.filter(
                or_(
                    Warehouse.name.ilike(f'%{search}%'),
                    Warehouse.location.ilike(f'%{search}%'),
                    Warehouse.description.ilike(f'%{search}%')
                )
            )
        
        # تطبيق فلتر الحالة
        if is_active == 'true':
            query = query.filter(Warehouse.is_active == True)
        elif is_active == 'false':
            query = query.filter(Warehouse.is_active == False)
        
        # تطبيق الترتيب
        if sort_by in ['name', 'location', 'created_at']:
            if sort_order == 'desc':
                query = query.order_by(getattr(Warehouse, sort_by).desc())
            else:
                query = query.order_by(getattr(Warehouse, sort_by).asc())
        
        # تطبيق الترقيم
        pagination = query.paginate(page=page, per_page=per_page, error_out=False)
        
        # إعداد البيانات للاستجابة
        warehouses = []
        for warehouse in pagination.items:
            warehouses.append(warehouse.to_dict())
        
        # إعداد معلومات الترقيم
        pagination_info = {
            'page': pagination.page,
            'per_page': pagination.per_page,
            'total_pages': pagination.pages,
            'total_items': pagination.total
        }
        
        return jsonify({
            'success': True,
            'warehouses': warehouses,
            'pagination': pagination_info
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'حدث خطأ أثناء جلب قائمة المستودعات: {str(e)}'
        }), 500

@warehouses_api.route('/api/warehouses/<int:id>', methods=['GET'])
@login_required
def get_warehouse(id):
    """الحصول على تفاصيل مستودع محدد"""
    try:
        warehouse = Warehouse.query.get_or_404(id)
        
        # إعداد البيانات للاستجابة
        warehouse_data = warehouse.to_dict()
        
        # إضافة معلومات إضافية
        warehouse_data['product_count'] = warehouse.get_product_count()
        warehouse_data['low_stock_count'] = warehouse.get_low_stock_count()
        warehouse_data['out_of_stock_count'] = warehouse.get_out_of_stock_count()
        
        return jsonify({
            'success': True,
            'warehouse': warehouse_data
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'حدث خطأ أثناء جلب تفاصيل المستودع: {str(e)}'
        }), 500

@warehouses_api.route('/api/warehouses', methods=['POST'])
@login_required
def create_warehouse():
    """إنشاء مستودع جديد"""
    try:
        data = request.json
        
        # التحقق من البيانات المطلوبة
        if not data or 'name' not in data:
            return jsonify({
                'success': False,
                'message': 'يجب توفير اسم المستودع'
            }), 400
        
        # إنشاء المستودع الجديد
        warehouse = Warehouse(
            name=data['name'],
            location=data.get('location', ''),
            description=data.get('description', ''),
            is_active=data.get('is_active', True),
            is_default=data.get('is_default', False)
        )
        
        db.session.add(warehouse)
        db.session.commit()
        
        return jsonify({
            'success': True,
            'message': 'تم إنشاء المستودع بنجاح',
            'warehouse': warehouse.to_dict()
        })
    except Exception as e:
        db.session.rollback()
        return jsonify({
            'success': False,
            'message': f'حدث خطأ أثناء إنشاء المستودع: {str(e)}'
        }), 500

@warehouses_api.route('/api/warehouses/<int:id>', methods=['PUT'])
@login_required
def update_warehouse(id):
    """تحديث مستودع موجود"""
    try:
        warehouse = Warehouse.query.get_or_404(id)
        data = request.json
        
        # التحقق من البيانات المطلوبة
        if not data:
            return jsonify({
                'success': False,
                'message': 'لم يتم توفير بيانات للتحديث'
            }), 400
        
        # تحديث بيانات المستودع
        if 'name' in data:
            warehouse.name = data['name']
        if 'location' in data:
            warehouse.location = data['location']
        if 'description' in data:
            warehouse.description = data['description']
        if 'is_active' in data:
            warehouse.is_active = data['is_active']
        if 'is_default' in data:
            warehouse.is_default = data['is_default']
        
        db.session.commit()
        
        return jsonify({
            'success': True,
            'message': 'تم تحديث المستودع بنجاح',
            'warehouse': warehouse.to_dict()
        })
    except Exception as e:
        db.session.rollback()
        return jsonify({
            'success': False,
            'message': f'حدث خطأ أثناء تحديث المستودع: {str(e)}'
        }), 500

@warehouses_api.route('/api/warehouses/<int:id>', methods=['DELETE'])
@login_required
def delete_warehouse(id):
    """حذف مستودع موجود"""
    try:
        warehouse = Warehouse.query.get_or_404(id)
        
        # التحقق من وجود مخزون في المستودع
        if warehouse.inventories:
            return jsonify({
                'success': False,
                'message': 'لا يمكن حذف المستودع لأنه يحتوي على مخزون. قم بنقل المخزون أولاً أو تعطيل المستودع بدلاً من حذفه.'
            }), 400
        
        db.session.delete(warehouse)
        db.session.commit()
        
        return jsonify({
            'success': True,
            'message': 'تم حذف المستودع بنجاح'
        })
    except Exception as e:
        db.session.rollback()
        return jsonify({
            'success': False,
            'message': f'حدث خطأ أثناء حذف المستودع: {str(e)}'
        }), 500

def register_warehouses_api(app):
    """تسجيل واجهة برمجة التطبيقات للمستودعات"""
    app.register_blueprint(warehouses_api)
