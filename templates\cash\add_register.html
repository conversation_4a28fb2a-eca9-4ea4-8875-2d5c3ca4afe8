{% extends 'base.html' %}

{% block title %}إضافة خزينة جديدة{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0 text-gray-800">إضافة خزينة جديدة</h1>
        <a href="{{ url_for('cash.cash_registers') }}" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i> العودة
        </a>
    </div>

    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">بيانات الخزينة</h6>
        </div>
        <div class="card-body">
            <form method="post" action="{{ url_for('cash.add_cash_register') }}">
                <div class="form-group">
                    <label for="name">اسم الخزينة <span class="text-danger">*</span></label>
                    <input type="text" class="form-control" id="name" name="name" required>
                </div>
                <div class="form-group">
                    <label for="description">الوصف</label>
                    <textarea class="form-control" id="description" name="description" rows="3"></textarea>
                </div>
                <div class="form-group">
                    <label for="current_balance">الرصيد الافتتاحي</label>
                    <input type="number" class="form-control" id="current_balance" name="current_balance" value="0" step="0.01">
                </div>
                <div class="form-check mb-3">
                    <input type="checkbox" class="form-check-input" id="is_default" name="is_default">
                    <label class="form-check-label" for="is_default">خزينة افتراضية</label>
                </div>
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-save"></i> حفظ
                </button>
            </form>
        </div>
    </div>
</div>
{% endblock %}
