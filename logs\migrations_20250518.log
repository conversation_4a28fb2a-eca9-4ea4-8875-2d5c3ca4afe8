2025-05-18 05:27:14,522 - __main__ - INFO - تم إنشاء نسخة احتياطية من قاعدة البيانات في: backups\pre_migration_backup_20250518_052714.db
2025-05-18 05:27:14,523 - __main__ - INFO - بدء تنفيذ عمليات الهجرة...
2025-05-18 05:27:14,523 - __main__ - INFO - إضافة الأعمدة المفقودة في جدول payment_method...
2025-05-18 05:27:14,527 - __main__ - WARNING - خطأ في تنفيذ العبارة SQL: ALTER TABLE payment_method ADD COLUMN affects_cash... الخطأ: duplicate column name: affects_cash_register
2025-05-18 05:27:14,536 - __main__ - WARNING - خطأ في تنفيذ العبارة SQL: ALTER TABLE payment_method ADD COLUMN is_credit BO... الخطأ: duplicate column name: is_credit
2025-05-18 05:27:14,536 - __main__ - WARNING - خطأ في تنفيذ العبارة SQL: ALTER TABLE payment_method ADD COLUMN allow_partia... الخطأ: duplicate column name: allow_partial_payment
2025-05-18 05:27:14,537 - __main__ - WARNING - خطأ في تنفيذ العبارة SQL: ALTER TABLE payment_method ADD COLUMN requires_app... الخطأ: duplicate column name: requires_approval
2025-05-18 05:27:14,548 - __main__ - WARNING - خطأ في تنفيذ العبارة SQL: ALTER TABLE payment_method ADD COLUMN payment_cate... الخطأ: duplicate column name: payment_category
2025-05-18 05:27:14,553 - __main__ - WARNING - خطأ في تنفيذ العبارة SQL: ALTER TABLE payment_method ADD COLUMN updated_at D... الخطأ: duplicate column name: updated_at
2025-05-18 05:27:14,556 - __main__ - INFO - تم تنفيذ عبارات SQL على قاعدة البيانات instance\fouad_pos.db بنجاح
2025-05-18 05:27:14,560 - __main__ - INFO - إضافة الأعمدة المفقودة في جدول user...
2025-05-18 05:27:14,906 - __main__ - INFO - تم تنفيذ العبارة SQL: ALTER TABLE user ADD COLUMN phone VARCHAR(20)...
2025-05-18 05:27:15,118 - __main__ - INFO - تم تنفيذ العبارة SQL: ALTER TABLE user ADD COLUMN position VARCHAR(64)...
2025-05-18 05:27:15,366 - __main__ - INFO - تم تنفيذ العبارة SQL: ALTER TABLE user ADD COLUMN profile_image VARCHAR(...
2025-05-18 05:27:15,574 - __main__ - INFO - تم تنفيذ العبارة SQL: ALTER TABLE user ADD COLUMN is_active BOOLEAN DEFA...
2025-05-18 05:27:16,036 - __main__ - INFO - تم تنفيذ العبارة SQL: ALTER TABLE user ADD COLUMN last_login DATETIME...
2025-05-18 05:27:16,037 - __main__ - INFO - تم تنفيذ عبارات SQL على قاعدة البيانات instance\fouad_pos.db بنجاح
2025-05-18 05:27:16,039 - __main__ - INFO - إضافة الأعمدة المفقودة في جدول product...
2025-05-18 05:27:16,442 - __main__ - INFO - تم تنفيذ العبارة SQL: ALTER TABLE product ADD COLUMN barcode VARCHAR(64)...
2025-05-18 05:27:16,587 - __main__ - INFO - تم تنفيذ العبارة SQL: ALTER TABLE product ADD COLUMN tax_rate FLOAT DEFA...
2025-05-18 05:27:16,662 - __main__ - INFO - تم تنفيذ العبارة SQL: ALTER TABLE product ADD COLUMN is_service BOOLEAN ...
2025-05-18 05:27:16,663 - __main__ - WARNING - خطأ في تنفيذ العبارة SQL: ALTER TABLE product ADD COLUMN updated_at DATETIME... الخطأ: duplicate column name: updated_at
2025-05-18 05:27:16,664 - __main__ - INFO - تم تنفيذ عبارات SQL على قاعدة البيانات instance\fouad_pos.db بنجاح
2025-05-18 05:27:16,664 - __main__ - INFO - إضافة الفهارس لتحسين الأداء...
2025-05-18 05:27:16,775 - __main__ - INFO - تم تنفيذ العبارة SQL: CREATE INDEX IF NOT EXISTS ix_product_name ON prod...
2025-05-18 05:27:16,858 - __main__ - INFO - تم تنفيذ العبارة SQL: CREATE INDEX IF NOT EXISTS ix_order_created_at ON ...
2025-05-18 05:27:16,953 - __main__ - INFO - تم تنفيذ العبارة SQL: CREATE INDEX IF NOT EXISTS ix_customer_name ON cus...
2025-05-18 05:27:17,072 - __main__ - INFO - تم تنفيذ العبارة SQL: CREATE INDEX IF NOT EXISTS ix_customer_phone ON cu...
2025-05-18 05:27:17,073 - __main__ - INFO - تم تنفيذ عبارات SQL على قاعدة البيانات instance\fouad_pos.db بنجاح
2025-05-18 05:27:17,073 - __main__ - INFO - تم تنفيذ جميع عمليات الهجرة بنجاح
2025-05-18 05:28:39,307 - __main__ - INFO - تم إنشاء نسخة احتياطية من قاعدة البيانات في: backups\pre_migration_backup_20250518_052839.db
2025-05-18 05:28:39,309 - __main__ - INFO - بدء تنفيذ عمليات الهجرة...
2025-05-18 05:28:39,310 - __main__ - INFO - إضافة الأعمدة المفقودة في جدول payment_method...
2025-05-18 05:28:39,317 - __main__ - WARNING - خطأ في تنفيذ العبارة SQL: ALTER TABLE payment_method ADD COLUMN affects_cash... الخطأ: duplicate column name: affects_cash_register
2025-05-18 05:28:39,320 - __main__ - WARNING - خطأ في تنفيذ العبارة SQL: ALTER TABLE payment_method ADD COLUMN is_credit BO... الخطأ: duplicate column name: is_credit
2025-05-18 05:28:39,322 - __main__ - WARNING - خطأ في تنفيذ العبارة SQL: ALTER TABLE payment_method ADD COLUMN allow_partia... الخطأ: duplicate column name: allow_partial_payment
2025-05-18 05:28:39,323 - __main__ - WARNING - خطأ في تنفيذ العبارة SQL: ALTER TABLE payment_method ADD COLUMN requires_app... الخطأ: duplicate column name: requires_approval
2025-05-18 05:28:39,323 - __main__ - WARNING - خطأ في تنفيذ العبارة SQL: ALTER TABLE payment_method ADD COLUMN payment_cate... الخطأ: duplicate column name: payment_category
2025-05-18 05:28:39,324 - __main__ - WARNING - خطأ في تنفيذ العبارة SQL: ALTER TABLE payment_method ADD COLUMN updated_at D... الخطأ: duplicate column name: updated_at
2025-05-18 05:28:39,325 - __main__ - INFO - تم تنفيذ عبارات SQL على قاعدة البيانات instance\fouad_pos.db بنجاح
2025-05-18 05:28:39,326 - __main__ - INFO - إضافة الأعمدة المفقودة في جدول user...
2025-05-18 05:28:39,328 - __main__ - WARNING - خطأ في تنفيذ العبارة SQL: ALTER TABLE user ADD COLUMN phone VARCHAR(20)... الخطأ: duplicate column name: phone
2025-05-18 05:28:39,329 - __main__ - WARNING - خطأ في تنفيذ العبارة SQL: ALTER TABLE user ADD COLUMN position VARCHAR(64)... الخطأ: duplicate column name: position
2025-05-18 05:28:39,330 - __main__ - WARNING - خطأ في تنفيذ العبارة SQL: ALTER TABLE user ADD COLUMN profile_image VARCHAR(... الخطأ: duplicate column name: profile_image
2025-05-18 05:28:39,334 - __main__ - WARNING - خطأ في تنفيذ العبارة SQL: ALTER TABLE user ADD COLUMN is_active BOOLEAN DEFA... الخطأ: duplicate column name: is_active
2025-05-18 05:28:39,337 - __main__ - WARNING - خطأ في تنفيذ العبارة SQL: ALTER TABLE user ADD COLUMN last_login DATETIME... الخطأ: duplicate column name: last_login
2025-05-18 05:28:39,339 - __main__ - INFO - تم تنفيذ عبارات SQL على قاعدة البيانات instance\fouad_pos.db بنجاح
2025-05-18 05:28:39,340 - __main__ - INFO - إضافة الأعمدة المفقودة في جدول product...
2025-05-18 05:28:39,343 - __main__ - WARNING - خطأ في تنفيذ العبارة SQL: ALTER TABLE product ADD COLUMN barcode VARCHAR(64)... الخطأ: duplicate column name: barcode
2025-05-18 05:28:39,344 - __main__ - WARNING - خطأ في تنفيذ العبارة SQL: ALTER TABLE product ADD COLUMN tax_rate FLOAT DEFA... الخطأ: duplicate column name: tax_rate
2025-05-18 05:28:39,345 - __main__ - WARNING - خطأ في تنفيذ العبارة SQL: ALTER TABLE product ADD COLUMN is_service BOOLEAN ... الخطأ: duplicate column name: is_service
2025-05-18 05:28:39,346 - __main__ - WARNING - خطأ في تنفيذ العبارة SQL: ALTER TABLE product ADD COLUMN updated_at DATETIME... الخطأ: duplicate column name: updated_at
2025-05-18 05:28:39,347 - __main__ - INFO - تم تنفيذ عبارات SQL على قاعدة البيانات instance\fouad_pos.db بنجاح
2025-05-18 05:28:39,349 - __main__ - INFO - إضافة الفهارس لتحسين الأداء...
2025-05-18 05:28:39,356 - __main__ - INFO - تم تنفيذ العبارة SQL: CREATE INDEX IF NOT EXISTS ix_product_name ON prod...
2025-05-18 05:28:39,357 - __main__ - INFO - تم تنفيذ العبارة SQL: CREATE INDEX IF NOT EXISTS ix_order_created_at ON ...
2025-05-18 05:28:39,359 - __main__ - INFO - تم تنفيذ العبارة SQL: CREATE INDEX IF NOT EXISTS ix_customer_name ON cus...
2025-05-18 05:28:39,363 - __main__ - INFO - تم تنفيذ العبارة SQL: CREATE INDEX IF NOT EXISTS ix_customer_phone ON cu...
2025-05-18 05:28:39,375 - __main__ - INFO - تم تنفيذ عبارات SQL على قاعدة البيانات instance\fouad_pos.db بنجاح
2025-05-18 05:28:39,380 - __main__ - INFO - إنشاء جدول دليل الحسابات...
2025-05-18 05:28:39,552 - __main__ - INFO - تم تنفيذ العبارة SQL: 
            CREATE TABLE IF NOT EXISTS account (
...
2025-05-18 05:28:39,677 - __main__ - INFO - تم تنفيذ العبارة SQL: CREATE INDEX IF NOT EXISTS ix_account_account_type...
2025-05-18 05:28:39,777 - __main__ - INFO - تم تنفيذ العبارة SQL: CREATE INDEX IF NOT EXISTS ix_account_parent_id ON...
2025-05-18 05:28:39,779 - __main__ - INFO - تم تنفيذ عبارات SQL على قاعدة البيانات instance\fouad_pos.db بنجاح
2025-05-18 05:28:39,780 - __main__ - INFO - إنشاء جدول القيود المحاسبية...
2025-05-18 05:28:39,935 - __main__ - INFO - تم تنفيذ العبارة SQL: 
            CREATE TABLE IF NOT EXISTS journal_en...
2025-05-18 05:28:40,026 - __main__ - INFO - تم تنفيذ العبارة SQL: CREATE INDEX IF NOT EXISTS ix_journal_entry_date O...
2025-05-18 05:28:40,135 - __main__ - INFO - تم تنفيذ العبارة SQL: CREATE INDEX IF NOT EXISTS ix_journal_entry_status...
2025-05-18 05:28:40,136 - __main__ - INFO - تم تنفيذ عبارات SQL على قاعدة البيانات instance\fouad_pos.db بنجاح
2025-05-18 05:28:40,136 - __main__ - INFO - إنشاء جدول تفاصيل القيود المحاسبية...
2025-05-18 05:28:40,252 - __main__ - INFO - تم تنفيذ العبارة SQL: 
            CREATE TABLE IF NOT EXISTS journal_en...
2025-05-18 05:28:40,419 - __main__ - INFO - تم تنفيذ العبارة SQL: CREATE INDEX IF NOT EXISTS ix_journal_entry_item_j...
2025-05-18 05:28:40,559 - __main__ - INFO - تم تنفيذ العبارة SQL: CREATE INDEX IF NOT EXISTS ix_journal_entry_item_a...
2025-05-18 05:28:40,561 - __main__ - INFO - تم تنفيذ عبارات SQL على قاعدة البيانات instance\fouad_pos.db بنجاح
2025-05-18 05:28:40,561 - __main__ - INFO - إنشاء جدول أنواع الضرائب...
2025-05-18 05:28:40,669 - __main__ - INFO - تم تنفيذ العبارة SQL: 
            CREATE TABLE IF NOT EXISTS tax_type (...
2025-05-18 05:28:40,670 - __main__ - INFO - تم تنفيذ عبارات SQL على قاعدة البيانات instance\fouad_pos.db بنجاح
2025-05-18 05:28:40,671 - __main__ - INFO - تم تنفيذ جميع عمليات الهجرة بنجاح
