from flask import Blueprint, render_template, request, jsonify, make_response
from flask_login import login_required, current_user
from models import (
    Order, OrderItem, Product, Purchase, Customer, Supplier, User, Category,
    Shift, CashRegister, CashTransaction, DailyCashClosure, Inventory, InventoryMovement
)
from app import db
from sqlalchemy import func, desc, extract, case, and_, or_, text
from datetime import datetime, timedelta, date
import calendar
import io
import csv
import json
from collections import defaultdict
import sys
import math
from routes.settings import load_settings

# توفير بديل لوحدة numpy
class NumpyReplacement:
    def __init__(self):
        pass

    def mean(self, data):
        """حساب المتوسط الحسابي"""
        if not data:
            return 0
        return sum(data) / len(data)

    def median(self, data):
        """حساب الوسيط"""
        if not data:
            return 0
        sorted_data = sorted(data)
        n = len(sorted_data)
        if n % 2 == 0:
            return (sorted_data[n//2 - 1] + sorted_data[n//2]) / 2
        else:
            return sorted_data[n//2]

    def std(self, data):
        """حساب الانحراف المعياري"""
        if not data:
            return 0
        mean = self.mean(data)
        variance = sum((x - mean) ** 2 for x in data) / len(data)
        return math.sqrt(variance)

    def percentile(self, data, q):
        """حساب النسبة المئوية"""
        if not data:
            return 0
        sorted_data = sorted(data)
        n = len(sorted_data)
        index = (n - 1) * q / 100
        if index.is_integer():
            return sorted_data[int(index)]
        else:
            i = int(index)
            fraction = index - i
            return sorted_data[i] * (1 - fraction) + sorted_data[i + 1] * fraction

# إنشاء كائن بديل لـ numpy
np = NumpyReplacement()

# تعريف دالة relativedelta بديلة
class RelativeDelta:
    def __init__(self, months=0, days=0, years=0):
        self.months = months
        self.days = days
        self.years = years

    def __add__(self, other):
        if isinstance(other, datetime):
            # حساب التاريخ الجديد
            year = other.year + self.years
            month = other.month + self.months

            # تعديل الشهر والسنة إذا تجاوز الشهر 12
            while month > 12:
                month -= 12
                year += 1

            # تعديل الشهر والسنة إذا كان الشهر أقل من 1
            while month < 1:
                month += 12
                year -= 1

            # حساب اليوم مع مراعاة عدد أيام الشهر
            day = min(other.day, calendar.monthrange(year, month)[1])

            # إنشاء تاريخ جديد
            new_date = datetime(year, month, day,
                               other.hour, other.minute, other.second,
                               other.microsecond)

            # إضافة الأيام
            if self.days != 0:
                new_date = new_date + timedelta(days=self.days)

            return new_date
        return NotImplemented

# تعريف relativedelta كمتغير عام
relativedelta = RelativeDelta

reports_blueprint = Blueprint('reports', __name__)

@reports_blueprint.route('/reports')
@login_required
def index():
    # تحميل إعدادات المتجر
    settings = load_settings()
    return render_template('reports/index_new.html', current_user=current_user, settings=settings)

@reports_blueprint.route('/reports/sales-dashboard')
@login_required
def sales_index():
    # تحميل إعدادات المتجر
    settings = load_settings()
    return render_template('reports/sales_index.html', current_user=current_user, settings=settings)

@reports_blueprint.route('/reports/sales')
@login_required
def sales_report():
    # Get date range from request
    date_from = request.args.get('date_from',
                               (datetime.now() - timedelta(days=30)).strftime('%Y-%m-%d'))
    date_to = request.args.get('date_to', datetime.now().strftime('%Y-%m-%d'))

    # Get filter parameters
    payment_method = request.args.get('payment_method', '')
    status = request.args.get('status', '')
    view_type = request.args.get('view_type', 'daily')  # daily, weekly, monthly, yearly

    # Convert string dates to datetime objects
    date_from_obj = datetime.strptime(date_from, '%Y-%m-%d')
    date_to_obj = datetime.strptime(date_to, '%Y-%m-%d')

    # Base query filter
    base_filter = Order.created_at.between(date_from, date_to)

    # Add additional filters if provided
    if payment_method:
        base_filter = base_filter & (Order.payment_method == payment_method)
    if status:
        base_filter = base_filter & (Order.status == status)

    # Query sales data based on view type
    if view_type == 'daily':
        sales_data = db.session.query(
            func.date(Order.created_at).label('date'),
            func.count(Order.id).label('orders_count'),
            func.sum(Order.total).label('total_amount')
        ).filter(base_filter).group_by(
            func.date(Order.created_at)
        ).order_by(
            func.date(Order.created_at)
        ).all()

        # تحويل النتائج إلى قاموس لسهولة التعامل معها في القالب
        formatted_sales_data = []
        for sale in sales_data:
            formatted_sales_data.append({
                'date': str(sale.date),
                'orders_count': sale.orders_count,
                'total_amount': sale.total_amount
            })

    elif view_type == 'weekly':
        # Group by week number
        sales_data = db.session.query(
            extract('year', Order.created_at).label('year'),
            extract('week', Order.created_at).label('week'),
            func.count(Order.id).label('orders_count'),
            func.sum(Order.total).label('total_amount')
        ).filter(base_filter).group_by(
            extract('year', Order.created_at),
            extract('week', Order.created_at)
        ).order_by(
            extract('year', Order.created_at),
            extract('week', Order.created_at)
        ).all()

        formatted_sales_data = []
        for sale in sales_data:
            # Format as "Year-Week"
            formatted_sales_data.append({
                'date': f"{int(sale.year)}-W{int(sale.week)}",
                'orders_count': sale.orders_count,
                'total_amount': sale.total_amount
            })

    elif view_type == 'monthly':
        # Group by month
        sales_data = db.session.query(
            extract('year', Order.created_at).label('year'),
            extract('month', Order.created_at).label('month'),
            func.count(Order.id).label('orders_count'),
            func.sum(Order.total).label('total_amount')
        ).filter(base_filter).group_by(
            extract('year', Order.created_at),
            extract('month', Order.created_at)
        ).order_by(
            extract('year', Order.created_at),
            extract('month', Order.created_at)
        ).all()

        # أسماء الشهور بالعربية
        arabic_months = [
            'يناير', 'فبراير', 'مارس', 'إبريل', 'مايو', 'يونيو',
            'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'
        ]

        formatted_sales_data = []
        for sale in sales_data:
            month_name = arabic_months[int(sale.month) - 1]
            formatted_sales_data.append({
                'date': f"{month_name} {int(sale.year)}",
                'orders_count': sale.orders_count,
                'total_amount': sale.total_amount
            })

    elif view_type == 'yearly':
        # Group by year
        sales_data = db.session.query(
            extract('year', Order.created_at).label('year'),
            func.count(Order.id).label('orders_count'),
            func.sum(Order.total).label('total_amount')
        ).filter(base_filter).group_by(
            extract('year', Order.created_at)
        ).order_by(
            extract('year', Order.created_at)
        ).all()

        formatted_sales_data = []
        for sale in sales_data:
            formatted_sales_data.append({
                'date': str(int(sale.year)),
                'orders_count': sale.orders_count,
                'total_amount': sale.total_amount
            })

    # Get sales by payment method
    sales_by_payment = db.session.query(
        Order.payment_method,
        func.count(Order.id).label('orders_count'),
        func.sum(Order.total).label('total_amount')
    ).filter(base_filter).group_by(
        Order.payment_method
    ).all()

    payment_methods_data = []
    for payment in sales_by_payment:
        payment_name = 'نقدي' if payment.payment_method == 'cash' else 'بطاقة' if payment.payment_method == 'card' else payment.payment_method
        payment_methods_data.append({
            'method': payment_name,
            'orders_count': payment.orders_count,
            'total_amount': payment.total_amount
        })

    # Get sales by status
    sales_by_status = db.session.query(
        Order.status,
        func.count(Order.id).label('orders_count'),
        func.sum(Order.total).label('total_amount')
    ).filter(base_filter).group_by(
        Order.status
    ).all()

    status_data = []
    for status_item in sales_by_status:
        status_name = 'مكتمل' if status_item.status == 'completed' else 'معلق' if status_item.status == 'pending' else 'ملغي' if status_item.status == 'cancelled' else status_item.status
        status_data.append({
            'status': status_name,
            'orders_count': status_item.orders_count,
            'total_amount': status_item.total_amount
        })

    # Get top selling products
    top_products = db.session.query(
        Product.id,
        Product.name,
        func.sum(OrderItem.quantity).label('quantity_sold'),
        func.sum(OrderItem.total).label('total_amount')
    ).join(
        OrderItem, Product.id == OrderItem.product_id
    ).join(
        Order, OrderItem.order_id == Order.id
    ).filter(base_filter).group_by(
        Product.id
    ).order_by(
        func.sum(OrderItem.quantity).desc()
    ).limit(10).all()

    top_products_data = []
    for product in top_products:
        top_products_data.append({
            'id': product.id,
            'name': product.name,
            'quantity_sold': product.quantity_sold,
            'total_amount': product.total_amount
        })

    # Get top customers
    top_customers = db.session.query(
        Customer.id,
        Customer.name,
        func.count(Order.id).label('orders_count'),
        func.sum(Order.total).label('total_amount')
    ).join(
        Order, Customer.id == Order.customer_id
    ).filter(base_filter).group_by(
        Customer.id
    ).order_by(
        func.sum(Order.total).desc()
    ).limit(10).all()

    top_customers_data = []
    for customer in top_customers:
        top_customers_data.append({
            'id': customer.id,
            'name': customer.name,
            'orders_count': customer.orders_count,
            'total_amount': customer.total_amount
        })

    # Get sales by employee (user)
    sales_by_employee = db.session.query(
        User.id,
        User.full_name,
        func.count(Order.id).label('orders_count'),
        func.sum(Order.total).label('total_amount')
    ).join(
        Order, User.id == Order.user_id
    ).filter(base_filter).group_by(
        User.id
    ).order_by(
        func.sum(Order.total).desc()
    ).all()

    employees_data = []
    for employee in sales_by_employee:
        employees_data.append({
            'id': employee.id,
            'name': employee.full_name,
            'orders_count': employee.orders_count,
            'total_amount': employee.total_amount
        })

    # Calculate summary statistics
    total_sales = sum(sale['total_amount'] for sale in formatted_sales_data) if formatted_sales_data else 0
    total_orders = sum(sale['orders_count'] for sale in formatted_sales_data) if formatted_sales_data else 0
    avg_order_value = total_sales / total_orders if total_orders > 0 else 0

    # Get available payment methods and statuses for filters
    available_payment_methods = db.session.query(Order.payment_method).distinct().all()
    available_statuses = db.session.query(Order.status).distinct().all()

    # تحميل إعدادات المتجر
    settings = load_settings()

    return render_template(
        'reports/sales.html',
        sales_data=formatted_sales_data,
        payment_methods_data=payment_methods_data,
        status_data=status_data,
        top_products_data=top_products_data,
        top_customers_data=top_customers_data,
        employees_data=employees_data,
        total_sales=total_sales,
        total_orders=total_orders,
        avg_order_value=avg_order_value,
        date_from=date_from,
        date_to=date_to,
        payment_method=payment_method,
        status=status,
        view_type=view_type,
        available_payment_methods=[pm[0] for pm in available_payment_methods],
        available_statuses=[s[0] for s in available_statuses],
        current_user=current_user,
        settings=settings
    )

@reports_blueprint.route('/reports/inventory')
@login_required
def inventory_report():
    products = Product.query.all()

    inventory_value = sum(p.stock_quantity * p.cost_price for p in products)
    low_stock_count = sum(1 for p in products if p.stock_quantity <= p.minimum_stock)

    # تحميل إعدادات المتجر
    settings = load_settings()

    return render_template(
        'reports/inventory.html',
        products=products,
        inventory_value=inventory_value,
        low_stock_count=low_stock_count,
        current_user=current_user,
        settings=settings
    )

@reports_blueprint.route('/reports/purchases')
@login_required
def purchases_report():
    date_from = request.args.get('date_from',
                               (datetime.now() - timedelta(days=30)).strftime('%Y-%m-%d'))
    date_to = request.args.get('date_to', datetime.now().strftime('%Y-%m-%d'))

    purchases = Purchase.query.filter(
        Purchase.created_at.between(date_from, date_to)
    ).order_by(Purchase.created_at.desc()).all()

    total_amount = sum(p.total for p in purchases)

    # تحميل إعدادات المتجر
    settings = load_settings()

    return render_template(
        'reports/purchases.html',
        purchases=purchases,
        total_amount=total_amount,
        date_from=date_from,
        date_to=date_to,
        current_user=current_user,
        settings=settings
    )

@reports_blueprint.route('/reports/sales/detailed')
@login_required
def sales_detailed_report():
    # Get date range from request
    date_from = request.args.get('date_from',
                               (datetime.now() - timedelta(days=30)).strftime('%Y-%m-%d'))
    date_to = request.args.get('date_to', datetime.now().strftime('%Y-%m-%d'))

    # Get filter parameters
    payment_method = request.args.get('payment_method', '')
    status = request.args.get('status', '')
    customer_id = request.args.get('customer_id', '')
    product_id = request.args.get('product_id', '')

    # Base query
    query = db.session.query(Order).filter(
        Order.created_at.between(date_from, date_to)
    )

    # Apply additional filters
    if payment_method:
        query = query.filter(Order.payment_method == payment_method)
    if status:
        query = query.filter(Order.status == status)
    if customer_id and customer_id.isdigit():
        query = query.filter(Order.customer_id == int(customer_id))

    # Product filter requires joining with OrderItem
    if product_id and product_id.isdigit():
        query = query.join(OrderItem).filter(OrderItem.product_id == int(product_id))

    # Execute query with pagination
    page = request.args.get('page', 1, type=int)
    per_page = 20
    orders_paginated = query.order_by(Order.created_at.desc()).paginate(page=page, per_page=per_page)

    # Get customers and products for filter dropdowns
    customers = Customer.query.order_by(Customer.name).all()
    products = Product.query.filter_by(is_active=True).order_by(Product.name).all()

    # Get available payment methods and statuses for filters
    available_payment_methods = db.session.query(Order.payment_method).distinct().all()
    available_statuses = db.session.query(Order.status).distinct().all()

    # تحميل إعدادات المتجر
    settings = load_settings()

    return render_template(
        'reports/sales_detailed.html',
        orders=orders_paginated,
        customers=customers,
        products=products,
        date_from=date_from,
        date_to=date_to,
        payment_method=payment_method,
        status=status,
        customer_id=customer_id,
        product_id=product_id,
        available_payment_methods=[pm[0] for pm in available_payment_methods],
        available_statuses=[s[0] for s in available_statuses],
        current_user=current_user,
        settings=settings
    )

@reports_blueprint.route('/reports/sales/export')
@login_required
def export_sales_report():
    # Get date range from request
    date_from = request.args.get('date_from',
                               (datetime.now() - timedelta(days=30)).strftime('%Y-%m-%d'))
    date_to = request.args.get('date_to', datetime.now().strftime('%Y-%m-%d'))

    # Get filter parameters
    payment_method = request.args.get('payment_method', '')
    status = request.args.get('status', '')
    export_type = request.args.get('export_type', 'summary')  # summary or detailed

    # Base query filter
    base_filter = Order.created_at.between(date_from, date_to)

    # Add additional filters if provided
    if payment_method:
        base_filter = base_filter & (Order.payment_method == payment_method)
    if status:
        base_filter = base_filter & (Order.status == status)

    # Create CSV file in memory
    output = io.StringIO()
    writer = csv.writer(output)

    if export_type == 'summary':
        # Summary export - daily sales data
        writer.writerow(['التاريخ', 'عدد الطلبات', 'إجمالي المبيعات'])

        sales_data = db.session.query(
            func.date(Order.created_at).label('date'),
            func.count(Order.id).label('orders_count'),
            func.sum(Order.total).label('total_amount')
        ).filter(base_filter).group_by(
            func.date(Order.created_at)
        ).order_by(
            func.date(Order.created_at)
        ).all()

        for sale in sales_data:
            writer.writerow([
                str(sale.date),
                sale.orders_count,
                "%.2f" % sale.total_amount
            ])
    else:
        # Detailed export - all orders with their details
        writer.writerow([
            'رقم الفاتورة', 'التاريخ', 'العميل', 'طريقة الدفع',
            'الحالة', 'المجموع الفرعي', 'الخصم', 'الضريبة', 'الإجمالي'
        ])

        orders = Order.query.filter(base_filter).order_by(Order.created_at.desc()).all()

        for order in orders:
            customer_name = order.customer.name if order.customer else 'عميل عادي'
            payment_method_name = 'نقدي' if order.payment_method == 'cash' else 'بطاقة' if order.payment_method == 'card' else order.payment_method
            status_name = 'مكتمل' if order.status == 'completed' else 'معلق' if order.status == 'pending' else 'ملغي' if order.status == 'cancelled' else order.status

            writer.writerow([
                order.invoice_number,
                order.created_at.strftime('%Y-%m-%d %H:%M'),
                customer_name,
                payment_method_name,
                status_name,
                "%.2f" % order.subtotal,
                "%.2f" % order.discount,
                "%.2f" % order.tax,
                "%.2f" % order.total
            ])

    # Create response
    output.seek(0)
    filename = f"sales_report_{date_from}_to_{date_to}.csv"

    response = make_response(output.getvalue())
    response.headers["Content-Disposition"] = f"attachment; filename={filename}"
    response.headers["Content-type"] = "text/csv"

    return response

@reports_blueprint.route('/reports/profitability')
@login_required
def profitability_report():
    # Get date range from request
    date_from = request.args.get('date_from',
                               (datetime.now() - timedelta(days=30)).strftime('%Y-%m-%d'))
    date_to = request.args.get('date_to', datetime.now().strftime('%Y-%m-%d'))

    # Get filter parameters
    category_id = request.args.get('category_id', '')
    view_type = request.args.get('view_type', 'product')  # product, category, daily

    # Base query filter
    base_filter = Order.created_at.between(date_from, date_to)
    base_filter = base_filter & (Order.status == 'completed')  # Only include completed orders

    if view_type == 'product':
        # Get profitability by product
        profitability_data = db.session.query(
            Product.id,
            Product.name,
            Product.cost_price,
            Product.price,
            func.sum(OrderItem.quantity).label('quantity_sold'),
            func.sum(OrderItem.total).label('revenue'),
            (func.sum(OrderItem.total) - func.sum(OrderItem.quantity * Product.cost_price)).label('profit'),
            (((func.sum(OrderItem.total) - func.sum(OrderItem.quantity * Product.cost_price)) /
              func.sum(OrderItem.total)) * 100).label('profit_margin')
        ).join(
            OrderItem, Product.id == OrderItem.product_id
        ).join(
            Order, OrderItem.order_id == Order.id
        ).filter(
            base_filter
        )

        if category_id and category_id.isdigit():
            profitability_data = profitability_data.filter(Product.category_id == int(category_id))

        profitability_data = profitability_data.group_by(
            Product.id
        ).order_by(
            desc('profit')
        ).all()

        # Format data for template
        formatted_data = []
        for item in profitability_data:
            formatted_data.append({
                'id': item.id,
                'name': item.name,
                'cost_price': item.cost_price,
                'price': item.price,
                'quantity_sold': item.quantity_sold,
                'revenue': item.revenue,
                'profit': item.profit,
                'profit_margin': item.profit_margin
            })

    elif view_type == 'category':
        # Get profitability by category
        profitability_data = db.session.query(
            Category.id,
            Category.name,
            func.sum(OrderItem.quantity).label('quantity_sold'),
            func.sum(OrderItem.total).label('revenue'),
            (func.sum(OrderItem.total) - func.sum(OrderItem.quantity * Product.cost_price)).label('profit'),
            (((func.sum(OrderItem.total) - func.sum(OrderItem.quantity * Product.cost_price)) /
              func.sum(OrderItem.total)) * 100).label('profit_margin')
        ).join(
            Product, Category.id == Product.category_id
        ).join(
            OrderItem, Product.id == OrderItem.product_id
        ).join(
            Order, OrderItem.order_id == Order.id
        ).filter(
            base_filter
        ).group_by(
            Category.id
        ).order_by(
            desc('profit')
        ).all()

        # Format data for template
        formatted_data = []
        for item in profitability_data:
            formatted_data.append({
                'id': item.id,
                'name': item.name,
                'quantity_sold': item.quantity_sold,
                'revenue': item.revenue,
                'profit': item.profit,
                'profit_margin': item.profit_margin
            })

    elif view_type == 'daily':
        # Get profitability by day
        profitability_data = db.session.query(
            func.date(Order.created_at).label('date'),
            func.sum(OrderItem.quantity).label('quantity_sold'),
            func.sum(Order.total).label('revenue'),
            (func.sum(Order.total) - func.sum(OrderItem.quantity * Product.cost_price)).label('profit'),
            (((func.sum(Order.total) - func.sum(OrderItem.quantity * Product.cost_price)) /
              func.sum(Order.total)) * 100).label('profit_margin')
        ).join(
            OrderItem, Order.id == OrderItem.order_id
        ).join(
            Product, OrderItem.product_id == Product.id
        ).filter(
            base_filter
        ).group_by(
            func.date(Order.created_at)
        ).order_by(
            func.date(Order.created_at)
        ).all()

        # Format data for template
        formatted_data = []
        for item in profitability_data:
            formatted_data.append({
                'date': str(item.date),
                'quantity_sold': item.quantity_sold,
                'revenue': item.revenue,
                'profit': item.profit,
                'profit_margin': item.profit_margin
            })

    # Get categories for filter dropdown
    categories = Category.query.order_by(Category.name).all()

    # Calculate summary statistics
    total_revenue = sum(item['revenue'] for item in formatted_data) if formatted_data else 0
    total_profit = sum(item['profit'] for item in formatted_data) if formatted_data else 0
    avg_profit_margin = (total_profit / total_revenue * 100) if total_revenue > 0 else 0

    return render_template(
        'reports/profitability.html',
        profitability_data=formatted_data,
        categories=categories,
        date_from=date_from,
        date_to=date_to,
        category_id=category_id,
        view_type=view_type,
        total_revenue=total_revenue,
        total_profit=total_profit,
        avg_profit_margin=avg_profit_margin,
        current_user=current_user
    )

@reports_blueprint.route('/reports/sales-comparison')
@login_required
def sales_comparison_report():
    # Get date ranges from request
    current_from = request.args.get('current_from',
                                  (datetime.now() - timedelta(days=30)).strftime('%Y-%m-%d'))
    current_to = request.args.get('current_to', datetime.now().strftime('%Y-%m-%d'))

    # Calculate previous period (same length as current period)
    current_from_date = datetime.strptime(current_from, '%Y-%m-%d')
    current_to_date = datetime.strptime(current_to, '%Y-%m-%d')
    period_length = (current_to_date - current_from_date).days

    previous_to_date = current_from_date - timedelta(days=1)
    previous_from_date = previous_to_date - timedelta(days=period_length)

    previous_from = previous_from_date.strftime('%Y-%m-%d')
    previous_to = previous_to_date.strftime('%Y-%m-%d')

    # Get comparison type
    comparison_type = request.args.get('comparison_type', 'daily')  # daily, product, category, payment

    # Get current period data
    current_filter = Order.created_at.between(current_from, current_to)
    current_filter = current_filter & (Order.status == 'completed')

    # Get previous period data
    previous_filter = Order.created_at.between(previous_from, previous_to)
    previous_filter = previous_filter & (Order.status == 'completed')

    if comparison_type == 'daily':
        # Current period daily sales
        current_data = db.session.query(
            func.date(Order.created_at).label('date'),
            func.count(Order.id).label('orders_count'),
            func.sum(Order.total).label('total_amount')
        ).filter(current_filter).group_by(
            func.date(Order.created_at)
        ).order_by(
            func.date(Order.created_at)
        ).all()

        # Previous period daily sales
        previous_data = db.session.query(
            func.date(Order.created_at).label('date'),
            func.count(Order.id).label('orders_count'),
            func.sum(Order.total).label('total_amount')
        ).filter(previous_filter).group_by(
            func.date(Order.created_at)
        ).order_by(
            func.date(Order.created_at)
        ).all()

        # Format data for comparison
        current_formatted = []
        for item in current_data:
            current_formatted.append({
                'date': str(item.date),
                'orders_count': item.orders_count,
                'total_amount': item.total_amount
            })

        previous_formatted = []
        for item in previous_data:
            previous_formatted.append({
                'date': str(item.date),
                'orders_count': item.orders_count,
                'total_amount': item.total_amount
            })

    elif comparison_type == 'product':
        # Current period product sales
        current_data = db.session.query(
            Product.id,
            Product.name,
            func.sum(OrderItem.quantity).label('quantity_sold'),
            func.sum(OrderItem.total).label('total_amount')
        ).join(
            OrderItem, Product.id == OrderItem.product_id
        ).join(
            Order, OrderItem.order_id == Order.id
        ).filter(current_filter).group_by(
            Product.id
        ).order_by(
            func.sum(OrderItem.total).desc()
        ).limit(10).all()

        # Previous period product sales
        previous_data = db.session.query(
            Product.id,
            Product.name,
            func.sum(OrderItem.quantity).label('quantity_sold'),
            func.sum(OrderItem.total).label('total_amount')
        ).join(
            OrderItem, Product.id == OrderItem.product_id
        ).join(
            Order, OrderItem.order_id == Order.id
        ).filter(previous_filter).group_by(
            Product.id
        ).order_by(
            func.sum(OrderItem.total).desc()
        ).limit(10).all()

        # Format data for comparison
        current_formatted = []
        for item in current_data:
            current_formatted.append({
                'id': item.id,
                'name': item.name,
                'quantity_sold': item.quantity_sold,
                'total_amount': item.total_amount
            })

        previous_formatted = []
        for item in previous_data:
            previous_formatted.append({
                'id': item.id,
                'name': item.name,
                'quantity_sold': item.quantity_sold,
                'total_amount': item.total_amount
            })

    elif comparison_type == 'category':
        # Current period category sales
        current_data = db.session.query(
            Category.id,
            Category.name,
            func.sum(OrderItem.quantity).label('quantity_sold'),
            func.sum(OrderItem.total).label('total_amount')
        ).join(
            Product, Category.id == Product.category_id
        ).join(
            OrderItem, Product.id == OrderItem.product_id
        ).join(
            Order, OrderItem.order_id == Order.id
        ).filter(current_filter).group_by(
            Category.id
        ).order_by(
            func.sum(OrderItem.total).desc()
        ).all()

        # Previous period category sales
        previous_data = db.session.query(
            Category.id,
            Category.name,
            func.sum(OrderItem.quantity).label('quantity_sold'),
            func.sum(OrderItem.total).label('total_amount')
        ).join(
            Product, Category.id == Product.category_id
        ).join(
            OrderItem, Product.id == OrderItem.product_id
        ).join(
            Order, OrderItem.order_id == Order.id
        ).filter(previous_filter).group_by(
            Category.id
        ).order_by(
            func.sum(OrderItem.total).desc()
        ).all()

        # Format data for comparison
        current_formatted = []
        for item in current_data:
            current_formatted.append({
                'id': item.id,
                'name': item.name,
                'quantity_sold': item.quantity_sold,
                'total_amount': item.total_amount
            })

        previous_formatted = []
        for item in previous_data:
            previous_formatted.append({
                'id': item.id,
                'name': item.name,
                'quantity_sold': item.quantity_sold,
                'total_amount': item.total_amount
            })

    elif comparison_type == 'payment':
        # Current period payment method sales
        current_data = db.session.query(
            Order.payment_method,
            func.count(Order.id).label('orders_count'),
            func.sum(Order.total).label('total_amount')
        ).filter(current_filter).group_by(
            Order.payment_method
        ).all()

        # Previous period payment method sales
        previous_data = db.session.query(
            Order.payment_method,
            func.count(Order.id).label('orders_count'),
            func.sum(Order.total).label('total_amount')
        ).filter(previous_filter).group_by(
            Order.payment_method
        ).all()

        # Format data for comparison
        current_formatted = []
        for item in current_data:
            payment_name = 'نقدي' if item.payment_method == 'cash' else 'بطاقة' if item.payment_method == 'card' else item.payment_method
            current_formatted.append({
                'payment_method': payment_name,
                'orders_count': item.orders_count,
                'total_amount': item.total_amount
            })

        previous_formatted = []
        for item in previous_data:
            payment_name = 'نقدي' if item.payment_method == 'cash' else 'بطاقة' if item.payment_method == 'card' else item.payment_method
            previous_formatted.append({
                'payment_method': payment_name,
                'orders_count': item.orders_count,
                'total_amount': item.total_amount
            })

    # Calculate summary statistics
    current_total_sales = sum(item['total_amount'] for item in current_formatted) if current_formatted else 0
    previous_total_sales = sum(item['total_amount'] for item in previous_formatted) if previous_formatted else 0

    sales_change = current_total_sales - previous_total_sales
    sales_change_percentage = (sales_change / previous_total_sales * 100) if previous_total_sales > 0 else 0

    current_orders_count = sum(item.get('orders_count', 0) for item in current_formatted) if current_formatted else 0
    previous_orders_count = sum(item.get('orders_count', 0) for item in previous_formatted) if previous_formatted else 0

    orders_change = current_orders_count - previous_orders_count
    orders_change_percentage = (orders_change / previous_orders_count * 100) if previous_orders_count > 0 else 0

    return render_template(
        'reports/sales_comparison.html',
        current_data=current_formatted,
        previous_data=previous_formatted,
        current_from=current_from,
        current_to=current_to,
        previous_from=previous_from,
        previous_to=previous_to,
        comparison_type=comparison_type,
        current_total_sales=current_total_sales,
        previous_total_sales=previous_total_sales,
        sales_change=sales_change,
        sales_change_percentage=sales_change_percentage,
        current_orders_count=current_orders_count,
        previous_orders_count=previous_orders_count,
        orders_change=orders_change,
        orders_change_percentage=orders_change_percentage,
        current_user=current_user
    )

@reports_blueprint.route('/reports/trends-forecast')
@login_required
def trends_forecast_report():
    # Get parameters
    period = request.args.get('period', 'monthly')  # daily, weekly, monthly
    forecast_months = int(request.args.get('forecast_months', '3'))

    today = datetime.now().date()

    # Get historical data based on period
    if period == 'daily':
        # Get last 90 days of data
        start_date = today - timedelta(days=90)

        sales_data = db.session.query(
            func.date(Order.created_at).label('date'),
            func.sum(Order.total).label('total_amount')
        ).filter(
            Order.created_at >= start_date,
            Order.status == 'completed'
        ).group_by(
            func.date(Order.created_at)
        ).order_by(
            func.date(Order.created_at)
        ).all()

        # Format data
        formatted_data = []
        for item in sales_data:
            formatted_data.append({
                'date': str(item.date),
                'total_amount': float(item.total_amount)
            })

        # Fill in missing dates with zero values
        date_dict = {item['date']: item['total_amount'] for item in formatted_data}

        all_dates = []
        current_date = start_date
        while current_date <= today:
            date_str = current_date.strftime('%Y-%m-%d')
            if date_str not in date_dict:
                formatted_data.append({
                    'date': date_str,
                    'total_amount': 0.0
                })
            all_dates.append(date_str)
            current_date += timedelta(days=1)

        # Sort by date
        formatted_data.sort(key=lambda x: x['date'])

    elif period == 'weekly':
        # Get last 52 weeks of data (1 year)
        start_date = today - timedelta(weeks=52)

        # Custom query to group by week
        sales_data = db.session.query(
            extract('year', Order.created_at).label('year'),
            extract('week', Order.created_at).label('week'),
            func.sum(Order.total).label('total_amount')
        ).filter(
            Order.created_at >= start_date,
            Order.status == 'completed'
        ).group_by(
            extract('year', Order.created_at),
            extract('week', Order.created_at)
        ).order_by(
            extract('year', Order.created_at),
            extract('week', Order.created_at)
        ).all()

        # Format data
        formatted_data = []
        for item in sales_data:
            # Create a date string for the week (using the first day of the week)
            year = int(item.year)
            week = int(item.week)
            first_day = datetime.strptime(f'{year}-{week}-1', '%Y-%W-%w')
            date_str = first_day.strftime('%Y-%m-%d')

            formatted_data.append({
                'date': date_str,
                'total_amount': float(item.total_amount)
            })

    elif period == 'monthly':
        # Get last 24 months of data (2 years)
        start_date = today.replace(day=1) - relativedelta(months=24)

        sales_data = db.session.query(
            extract('year', Order.created_at).label('year'),
            extract('month', Order.created_at).label('month'),
            func.sum(Order.total).label('total_amount')
        ).filter(
            Order.created_at >= start_date,
            Order.status == 'completed'
        ).group_by(
            extract('year', Order.created_at),
            extract('month', Order.created_at)
        ).order_by(
            extract('year', Order.created_at),
            extract('month', Order.created_at)
        ).all()

        # Format data
        formatted_data = []
        for item in sales_data:
            year = int(item.year)
            month = int(item.month)
            date_str = f"{year}-{month:02d}-01"

            formatted_data.append({
                'date': date_str,
                'total_amount': float(item.total_amount)
            })

        # Fill in missing months with zero values
        date_dict = {item['date']: item['total_amount'] for item in formatted_data}

        all_dates = []
        current_date = start_date
        while current_date <= today:
            date_str = current_date.strftime('%Y-%m-01')
            if date_str not in date_dict:
                formatted_data.append({
                    'date': date_str,
                    'total_amount': 0.0
                })
            all_dates.append(date_str)
            current_date = current_date + relativedelta(months=1)

        # Sort by date
        formatted_data.sort(key=lambda x: x['date'])

    # Generate forecast data
    # Simple moving average forecast
    if len(formatted_data) > 0:
        # Get the last 6 data points for the forecast
        last_points = formatted_data[-6:]
        avg_sales = sum(item['total_amount'] for item in last_points) / len(last_points)

        # Generate forecast dates
        forecast_data = []
        last_date = datetime.strptime(formatted_data[-1]['date'], '%Y-%m-%d')

        for i in range(1, forecast_months + 1):
            if period == 'daily':
                next_date = last_date + timedelta(days=i)
            elif period == 'weekly':
                next_date = last_date + timedelta(weeks=i)
            else:  # monthly
                next_date = last_date + relativedelta(months=i)

            # Add some randomness to the forecast (±10%)
            random_factor = 0.9 + (np.random.random() * 0.2)  # Between 0.9 and 1.1
            forecast_amount = avg_sales * random_factor

            forecast_data.append({
                'date': next_date.strftime('%Y-%m-%d'),
                'total_amount': forecast_amount
            })
    else:
        forecast_data = []

    # Calculate trend statistics
    if len(formatted_data) >= 2:
        first_value = formatted_data[0]['total_amount']
        last_value = formatted_data[-1]['total_amount']

        absolute_change = last_value - first_value
        percentage_change = (absolute_change / first_value * 100) if first_value > 0 else 0

        # Calculate average monthly growth rate
        monthly_growth_rates = []
        for i in range(1, len(formatted_data)):
            prev_value = formatted_data[i-1]['total_amount']
            curr_value = formatted_data[i]['total_amount']
            if prev_value > 0:
                growth_rate = (curr_value - prev_value) / prev_value
                monthly_growth_rates.append(growth_rate)

        avg_growth_rate = (sum(monthly_growth_rates) / len(monthly_growth_rates)) if monthly_growth_rates else 0
    else:
        absolute_change = 0
        percentage_change = 0
        avg_growth_rate = 0

    # Get top growing products
    top_growing_products = db.session.query(
        Product.id,
        Product.name,
        func.sum(OrderItem.quantity).label('current_quantity'),
        func.sum(OrderItem.total).label('current_amount')
    ).join(
        OrderItem, Product.id == OrderItem.product_id
    ).join(
        Order, OrderItem.order_id == Order.id
    ).filter(
        Order.created_at >= (today - timedelta(days=30)),
        Order.status == 'completed'
    ).group_by(
        Product.id
    ).order_by(
        func.sum(OrderItem.quantity).desc()
    ).limit(5).all()

    top_products_data = []
    for product in top_growing_products:
        # Get previous period data for this product
        previous_data = db.session.query(
            func.sum(OrderItem.quantity).label('prev_quantity'),
            func.sum(OrderItem.total).label('prev_amount')
        ).join(
            Order, OrderItem.order_id == Order.id
        ).filter(
            OrderItem.product_id == product.id,
            Order.created_at.between(
                today - timedelta(days=60),
                today - timedelta(days=31)
            ),
            Order.status == 'completed'
        ).first()

        prev_quantity = previous_data.prev_quantity or 0
        prev_amount = previous_data.prev_amount or 0

        quantity_change = product.current_quantity - prev_quantity
        amount_change = product.current_amount - prev_amount

        quantity_change_pct = (quantity_change / prev_quantity * 100) if prev_quantity > 0 else 0
        amount_change_pct = (amount_change / prev_amount * 100) if prev_amount > 0 else 0

        top_products_data.append({
            'id': product.id,
            'name': product.name,
            'current_quantity': product.current_quantity,
            'current_amount': product.current_amount,
            'prev_quantity': prev_quantity,
            'prev_amount': prev_amount,
            'quantity_change': quantity_change,
            'amount_change': amount_change,
            'quantity_change_pct': quantity_change_pct,
            'amount_change_pct': amount_change_pct
        })

    return render_template(
        'reports/trends_forecast.html',
        historical_data=formatted_data,
        forecast_data=forecast_data,
        period=period,
        forecast_months=forecast_months,
        absolute_change=absolute_change,
        percentage_change=percentage_change,
        avg_growth_rate=avg_growth_rate,
        top_products_data=top_products_data,
        current_user=current_user
    )

@reports_blueprint.route('/reports/customer-analysis')
@login_required
def customer_analysis_report():
    # Get date range from request
    date_from = request.args.get('date_from',
                               (datetime.now() - timedelta(days=365)).strftime('%Y-%m-%d'))
    date_to = request.args.get('date_to', datetime.now().strftime('%Y-%m-%d'))

    # Base filter
    base_filter = Order.created_at.between(date_from, date_to)
    base_filter = base_filter & (Order.status == 'completed')

    # Get top customers by revenue
    top_customers = db.session.query(
        Customer.id,
        Customer.name,
        Customer.phone,
        Customer.email,
        func.count(Order.id).label('orders_count'),
        func.sum(Order.total).label('total_spent'),
        func.avg(Order.total).label('avg_order_value'),
        func.min(Order.created_at).label('first_purchase'),
        func.max(Order.created_at).label('last_purchase')
    ).join(
        Order, Customer.id == Order.customer_id
    ).filter(
        base_filter
    ).group_by(
        Customer.id
    ).order_by(
        func.sum(Order.total).desc()
    ).limit(20).all()

    # Format customer data
    customers_data = []
    for customer in top_customers:
        # Calculate days since last purchase
        days_since_last = (datetime.now() - customer.last_purchase).days

        # Calculate customer lifetime (days between first and last purchase)
        customer_lifetime = (customer.last_purchase - customer.first_purchase).days

        # Calculate purchase frequency (average days between orders)
        if customer.orders_count > 1 and customer_lifetime > 0:
            purchase_frequency = customer_lifetime / (customer.orders_count - 1)
        else:
            purchase_frequency = 0

        # Determine customer status based on recency
        if days_since_last <= 30:
            status = 'نشط'
        elif days_since_last <= 90:
            status = 'في خطر'
        else:
            status = 'غير نشط'

        customers_data.append({
            'id': customer.id,
            'name': customer.name,
            'phone': customer.phone,
            'email': customer.email,
            'orders_count': customer.orders_count,
            'total_spent': customer.total_spent,
            'avg_order_value': customer.avg_order_value,
            'first_purchase': customer.first_purchase,
            'last_purchase': customer.last_purchase,
            'days_since_last': days_since_last,
            'customer_lifetime': customer_lifetime,
            'purchase_frequency': purchase_frequency,
            'status': status
        })

    # Calculate customer segments based on RFM (Recency, Frequency, Monetary)
    if customers_data:
        # Calculate quartiles for RFM
        recency_values = [c['days_since_last'] for c in customers_data]
        frequency_values = [c['orders_count'] for c in customers_data]
        monetary_values = [c['total_spent'] for c in customers_data]

        # For recency, lower is better (more recent)
        recency_quartiles = np.percentile(recency_values, [25, 50, 75])
        # For frequency and monetary, higher is better
        frequency_quartiles = np.percentile(frequency_values, [25, 50, 75])
        monetary_quartiles = np.percentile(monetary_values, [25, 50, 75])

        # Assign RFM scores
        for customer in customers_data:
            # Recency score (1-4, 4 is best = most recent)
            if customer['days_since_last'] <= recency_quartiles[0]:
                r_score = 4
            elif customer['days_since_last'] <= recency_quartiles[1]:
                r_score = 3
            elif customer['days_since_last'] <= recency_quartiles[2]:
                r_score = 2
            else:
                r_score = 1

            # Frequency score (1-4, 4 is best = most frequent)
            if customer['orders_count'] >= frequency_quartiles[2]:
                f_score = 4
            elif customer['orders_count'] >= frequency_quartiles[1]:
                f_score = 3
            elif customer['orders_count'] >= frequency_quartiles[0]:
                f_score = 2
            else:
                f_score = 1

            # Monetary score (1-4, 4 is best = highest spender)
            if customer['total_spent'] >= monetary_quartiles[2]:
                m_score = 4
            elif customer['total_spent'] >= monetary_quartiles[1]:
                m_score = 3
            elif customer['total_spent'] >= monetary_quartiles[0]:
                m_score = 2
            else:
                m_score = 1

            # Combined RFM score
            rfm_score = r_score * 100 + f_score * 10 + m_score

            # Assign segment based on RFM score
            if rfm_score >= 444:  # All high scores
                segment = 'عميل مميز'
            elif r_score >= 3 and (f_score + m_score >= 5):
                segment = 'عميل قيم'
            elif r_score >= 3:
                segment = 'عميل جديد واعد'
            elif r_score <= 2 and f_score <= 2 and m_score >= 3:
                segment = 'عميل سابق ذو قيمة'
            elif r_score <= 2 and f_score >= 3:
                segment = 'عميل متكرر غير نشط'
            elif r_score <= 2:
                segment = 'عميل في خطر الفقدان'
            else:
                segment = 'عميل عادي'

            customer['r_score'] = r_score
            customer['f_score'] = f_score
            customer['m_score'] = m_score
            customer['rfm_score'] = rfm_score
            customer['segment'] = segment

    # Calculate segment distribution
    segment_distribution = defaultdict(int)
    for customer in customers_data:
        segment_distribution[customer['segment']] += 1

    segment_data = [
        {'name': segment, 'count': count}
        for segment, count in segment_distribution.items()
    ]

    # Calculate total customers and total revenue
    total_customers = len(customers_data)
    total_revenue = sum(customer['total_spent'] for customer in customers_data)

    return render_template(
        'reports/customer_analysis.html',
        customers_data=customers_data,
        segment_data=segment_data,
        total_customers=total_customers,
        total_revenue=total_revenue,
        date_from=date_from,
        date_to=date_to,
        current_user=current_user
    )



@reports_blueprint.route('/api/reports/dashboard-stats')
@login_required
def dashboard_stats():
    today = datetime.now().date()

    # Daily sales
    daily_sales = db.session.query(
        func.sum(Order.total)
    ).filter(
        func.date(Order.created_at) == today
    ).scalar() or 0

    # Monthly sales
    monthly_sales = db.session.query(
        func.sum(Order.total)
    ).filter(
        func.extract('month', Order.created_at) == today.month,
        func.extract('year', Order.created_at) == today.year
    ).scalar() or 0

    return jsonify({
        'daily_sales': daily_sales,
        'monthly_sales': monthly_sales,
        'total_customers': Customer.query.count(),
        'total_suppliers': Supplier.query.count()
    })

# تقارير الشيفت والخزينة

@reports_blueprint.route('/reports/shift-closure')
@login_required
def shift_closure_report():
    """تقرير إغلاق الشيفت"""
    # الحصول على معلمات الفلتر
    date_from = request.args.get('date_from', (datetime.now() - timedelta(days=30)).strftime('%Y-%m-%d'))
    date_to = request.args.get('date_to', datetime.now().strftime('%Y-%m-%d'))
    user_id = request.args.get('user_id', '')
    register_id = request.args.get('cash_register_id', '')

    # تحويل التواريخ إلى كائنات datetime
    try:
        date_from_obj = datetime.strptime(date_from, '%Y-%m-%d')
        date_to_obj = datetime.strptime(date_to, '%Y-%m-%d')
        # إضافة 23:59:59 إلى تاريخ النهاية للحصول على اليوم كاملاً
        date_to_obj = date_to_obj.replace(hour=23, minute=59, second=59)
    except ValueError:
        date_from_obj = datetime.now() - timedelta(days=30)
        date_to_obj = datetime.now()

    # استعلام الشيفتات
    shifts_query = Shift.query.filter(
        Shift.status == 'closed',
        Shift.end_time.between(date_from_obj, date_to_obj)
    )

    # تطبيق الفلاتر
    if user_id:
        shifts_query = shifts_query.filter(Shift.user_id == user_id)

    if register_id:
        shifts_query = shifts_query.filter(Shift.cash_register_id == register_id)

    # الحصول على الشيفتات
    shifts = shifts_query.order_by(desc(Shift.end_time)).all()

    # حساب الإحصائيات
    total_sales = 0
    total_deposits = 0
    total_withdrawals = 0
    payment_methods_data = [0, 0, 0]  # نقدي، بطاقة، آجل
    payment_methods_labels = ['نقدي', 'بطاقة', 'آجل']

    for shift in shifts:
        # حساب المبيعات
        sales = Order.query.filter(
            Order.created_at.between(shift.start_time, shift.end_time),
            Order.user_id == shift.user_id,
            Order.status == 'completed'
        ).all()

        for sale in sales:
            total_sales += sale.total
            if sale.payment_method == 'cash':
                payment_methods_data[0] += sale.total
            elif sale.payment_method == 'card':
                payment_methods_data[1] += sale.total
            elif sale.payment_method == 'credit':
                payment_methods_data[2] += sale.total

        # حساب الإيداعات والسحوبات
        transactions = CashTransaction.query.filter_by(shift_id=shift.id).all()
        for transaction in transactions:
            if transaction.transaction_type == 'deposit':
                total_deposits += transaction.amount
            elif transaction.transaction_type == 'withdraw':
                total_withdrawals += transaction.amount

    # الحصول على الخزائن والمستخدمين للفلترة
    cash_registers = CashRegister.query.filter_by(is_active=True).all()
    users = User.query.all()

    # تحميل إعدادات المتجر
    settings = load_settings()

    return render_template(
        'reports/shift_closure.html',
        shifts=shifts,
        cash_registers=cash_registers,
        users=users,
        date_from=date_from,
        date_to=date_to,
        selected_user=user_id,
        selected_register=register_id,
        total_sales=total_sales,
        total_deposits=total_deposits,
        total_withdrawals=total_withdrawals,
        payment_methods_data=payment_methods_data,
        payment_methods_labels=payment_methods_labels,
        settings=settings
    )

@reports_blueprint.route('/reports/cash-transactions')
@login_required
def cash_transactions_report():
    """تقرير معاملات الخزينة"""
    # الحصول على معلمات الفلتر
    date_from = request.args.get('date_from', (datetime.now() - timedelta(days=30)).strftime('%Y-%m-%d'))
    date_to = request.args.get('date_to', datetime.now().strftime('%Y-%m-%d'))
    register_id = request.args.get('cash_register_id', '')
    transaction_type = request.args.get('transaction_type', '')
    user_id = request.args.get('user_id', '')

    # تحويل التواريخ إلى كائنات datetime
    try:
        date_from_obj = datetime.strptime(date_from, '%Y-%m-%d')
        date_to_obj = datetime.strptime(date_to, '%Y-%m-%d')
        # إضافة 23:59:59 إلى تاريخ النهاية للحصول على اليوم كاملاً
        date_to_obj = date_to_obj.replace(hour=23, minute=59, second=59)
    except ValueError:
        date_from_obj = datetime.now() - timedelta(days=30)
        date_to_obj = datetime.now()

    # استعلام المعاملات
    transactions_query = CashTransaction.query.filter(
        CashTransaction.created_at.between(date_from_obj, date_to_obj)
    )

    # تطبيق الفلاتر
    if register_id:
        transactions_query = transactions_query.filter(CashTransaction.cash_register_id == register_id)

    if transaction_type:
        transactions_query = transactions_query.filter(CashTransaction.transaction_type == transaction_type)

    if user_id:
        transactions_query = transactions_query.filter(CashTransaction.created_by == user_id)

    # الحصول على المعاملات
    transactions = transactions_query.order_by(desc(CashTransaction.created_at)).all()

    # حساب الإحصائيات
    total_deposits = 0
    total_withdrawals = 0
    deposits_count = 0
    withdrawals_count = 0
    transfers_count = 0

    for transaction in transactions:
        if transaction.transaction_type == 'deposit':
            total_deposits += transaction.amount
            deposits_count += 1
        elif transaction.transaction_type == 'withdraw':
            total_withdrawals += transaction.amount
            withdrawals_count += 1
        elif transaction.transaction_type == 'transfer':
            transfers_count += 1

    # الحصول على الخزائن والمستخدمين للفلترة
    cash_registers = CashRegister.query.filter_by(is_active=True).all()
    users = User.query.all()

    return render_template(
        'reports/cash_transactions.html',
        transactions=transactions,
        cash_registers=cash_registers,
        users=users,
        date_from=date_from,
        date_to=date_to,
        selected_register=register_id,
        selected_type=transaction_type,
        selected_user=user_id,
        total_deposits=total_deposits,
        total_withdrawals=total_withdrawals,
        deposits_count=deposits_count,
        withdrawals_count=withdrawals_count,
        transfers_count=transfers_count
    )

@reports_blueprint.route('/reports/daily-closure')
@login_required
def daily_closure_report():
    """تقرير الإغلاق اليومي"""
    # الحصول على معلمات الفلتر
    date_from = request.args.get('date_from', (datetime.now() - timedelta(days=30)).strftime('%Y-%m-%d'))
    date_to = request.args.get('date_to', datetime.now().strftime('%Y-%m-%d'))
    register_id = request.args.get('cash_register_id', '')
    user_id = request.args.get('user_id', '')

    # تحويل التواريخ إلى كائنات datetime
    try:
        date_from_obj = datetime.strptime(date_from, '%Y-%m-%d').date()
        date_to_obj = datetime.strptime(date_to, '%Y-%m-%d').date()
    except ValueError:
        date_from_obj = (datetime.now() - timedelta(days=30)).date()
        date_to_obj = datetime.now().date()

    # استعلام الإغلاقات اليومية
    closures_query = DailyCashClosure.query.filter(
        DailyCashClosure.closure_date.between(date_from_obj, date_to_obj)
    )

    # تطبيق الفلاتر
    if register_id:
        closures_query = closures_query.filter(DailyCashClosure.cash_register_id == register_id)

    if user_id:
        closures_query = closures_query.filter(DailyCashClosure.user_id == user_id)

    # الحصول على الإغلاقات اليومية
    closures = closures_query.order_by(desc(DailyCashClosure.closure_date)).all()

    # حساب الإحصائيات
    total_sales = sum(closure.total_sales for closure in closures)
    total_purchases = sum(closure.total_purchases for closure in closures)
    avg_difference = sum(closure.difference for closure in closures) / len(closures) if closures else 0

    # إعداد بيانات الرسم البياني
    dates = []
    sales_data = []

    # إنشاء قاموس لتخزين المبيعات حسب التاريخ
    sales_by_date = {}

    for closure in closures:
        date_str = closure.closure_date.strftime('%Y-%m-%d')
        if date_str in sales_by_date:
            sales_by_date[date_str] += closure.total_sales
        else:
            sales_by_date[date_str] = closure.total_sales

    # ترتيب التواريخ تصاعدياً
    sorted_dates = sorted(sales_by_date.keys())

    for date_str in sorted_dates:
        dates.append(date_str)
        sales_data.append(sales_by_date[date_str])

    # الحصول على الخزائن والمستخدمين للفلترة
    cash_registers = CashRegister.query.filter_by(is_active=True).all()
    users = User.query.all()

    return render_template(
        'reports/daily_closure.html',
        closures=closures,
        cash_registers=cash_registers,
        users=users,
        date_from=date_from,
        date_to=date_to,
        selected_register=register_id,
        selected_user=user_id,
        total_sales=total_sales,
        total_purchases=total_purchases,
        avg_difference=avg_difference,
        dates=dates,
        sales_data=sales_data
    )

@reports_blueprint.route('/reports/print-shift/<int:shift_id>')
@login_required
def print_shift_report(shift_id):
    """طباعة تقرير الشيفت"""
    # الحصول على الشيفت
    shift = Shift.query.get_or_404(shift_id)

    # الحصول على المعاملات المرتبطة بالشيفت
    transactions = CashTransaction.query.filter_by(shift_id=shift.id).order_by(desc(CashTransaction.created_at)).all()

    # الحصول على المبيعات المرتبطة بالشيفت
    sales = Order.query.filter(
        Order.created_at.between(shift.start_time, shift.end_time or datetime.utcnow()),
        Order.user_id == shift.user_id,
        Order.status == 'completed'
    ).order_by(desc(Order.created_at)).all()

    # حساب الإحصائيات
    total_sales = sum(sale.total for sale in sales)
    cash_sales = sum(sale.total for sale in sales if sale.payment_method == 'cash')
    card_sales = sum(sale.total for sale in sales if sale.payment_method == 'card')
    deposits = sum(transaction.amount for transaction in transactions if transaction.transaction_type == 'deposit')
    withdrawals = sum(transaction.amount for transaction in transactions if transaction.transaction_type == 'withdraw')

    # تحميل إعدادات المتجر
    settings = load_settings()

    return render_template(
        'reports/print_shift.html',
        shift=shift,
        transactions=transactions,
        sales=sales,
        total_sales=total_sales,
        cash_sales=cash_sales,
        card_sales=card_sales,
        deposits=deposits,
        withdrawals=withdrawals,
        now=datetime.now(),
        settings=settings
    )

@reports_blueprint.route('/reports/print-daily-closure/<int:closure_id>')
@login_required
def print_daily_closure(closure_id):
    """طباعة تقرير الإغلاق اليومي"""
    # الحصول على الإغلاق اليومي
    closure = DailyCashClosure.query.get_or_404(closure_id)

    # الحصول على المعاملات المرتبطة بالإغلاق اليومي
    transactions = CashTransaction.query.filter_by(daily_closure_id=closure.id).order_by(desc(CashTransaction.created_at)).all()

    # حساب الإحصائيات
    deposits = sum(transaction.amount for transaction in transactions if transaction.transaction_type == 'deposit')
    withdrawals = sum(transaction.amount for transaction in transactions if transaction.transaction_type == 'withdraw')

    # تحميل إعدادات المتجر
    settings = load_settings()

    return render_template(
        'reports/print_daily_closure.html',
        closure=closure,
        transactions=transactions,
        deposits=deposits,
        withdrawals=withdrawals,
        now=datetime.now(),
        settings=settings
    )

@reports_blueprint.route('/reports/inventory-movement')
@login_required
def inventory_movement_report():
    """تقرير حركة المخزون"""
    # الحصول على معلمات الفلتر
    date_from = request.args.get('date_from', (datetime.now() - timedelta(days=30)).strftime('%Y-%m-%d'))
    date_to = request.args.get('date_to', datetime.now().strftime('%Y-%m-%d'))
    warehouse_id = request.args.get('warehouse_id', '')
    product_id = request.args.get('product_id', '')
    movement_type = request.args.get('movement_type', '')

    # تحويل التواريخ إلى كائنات datetime
    try:
        date_from_obj = datetime.strptime(date_from, '%Y-%m-%d')
        date_to_obj = datetime.strptime(date_to, '%Y-%m-%d')
        # إضافة 23:59:59 إلى تاريخ النهاية للحصول على اليوم كاملاً
        date_to_obj = date_to_obj.replace(hour=23, minute=59, second=59)
    except ValueError:
        date_from_obj = datetime.now() - timedelta(days=30)
        date_to_obj = datetime.now()

    # استعلام حركات المخزون
    movements_query = InventoryMovement.query.filter(
        InventoryMovement.created_at.between(date_from_obj, date_to_obj)
    )

    # تطبيق الفلاتر
    if warehouse_id:
        movements_query = movements_query.filter(InventoryMovement.warehouse_id == warehouse_id)

    if product_id:
        movements_query = movements_query.filter(InventoryMovement.product_id == product_id)

    if movement_type:
        movements_query = movements_query.filter(InventoryMovement.movement_type == movement_type)

    # الحصول على حركات المخزون
    movements = movements_query.order_by(desc(InventoryMovement.created_at)).all()

    # الحصول على المستودعات والمنتجات للفلترة
    warehouses = Warehouse.query.filter_by(is_active=True).all()
    products = Product.query.all()

    return render_template(
        'reports/inventory_movement.html',
        movements=movements,
        warehouses=warehouses,
        products=products,
        date_from=date_from,
        date_to=date_to,
        selected_warehouse=warehouse_id,
        selected_product=product_id,
        selected_type=movement_type
    )

@reports_blueprint.route('/reports/inventory-valuation')
@login_required
def inventory_valuation_report():
    """تقرير تقييم المخزون"""
    # الحصول على معلمات الفلتر
    warehouse_id = request.args.get('warehouse_id', '')
    category_id = request.args.get('category_id', '')

    # استعلام المخزون
    inventory_query = Inventory.query

    # تطبيق الفلاتر
    if warehouse_id:
        inventory_query = inventory_query.filter(Inventory.warehouse_id == warehouse_id)

    # الحصول على المخزون
    inventory_items = inventory_query.all()

    # تصفية المنتجات حسب الفئة إذا تم تحديدها
    if category_id:
        inventory_items = [item for item in inventory_items if item.product.category_id == int(category_id)]

    # حساب الإحصائيات
    total_value = sum(item.quantity * item.product.cost_price for item in inventory_items)
    total_retail_value = sum(item.quantity * item.product.price for item in inventory_items)
    potential_profit = total_retail_value - total_value

    # الحصول على المستودعات والفئات للفلترة
    warehouses = Warehouse.query.filter_by(is_active=True).all()
    categories = Category.query.all()

    return render_template(
        'reports/inventory_valuation.html',
        inventory_items=inventory_items,
        warehouses=warehouses,
        categories=categories,
        selected_warehouse=warehouse_id,
        selected_category=category_id,
        total_value=total_value,
        total_retail_value=total_retail_value,
        potential_profit=potential_profit
    )




