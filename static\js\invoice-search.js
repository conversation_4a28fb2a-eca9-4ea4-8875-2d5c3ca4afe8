/**
 * Invoice Search Functionality
 * Handles searching for invoices by invoice number
 */

// Initialize invoice search functionality
document.addEventListener('DOMContentLoaded', function() {
    console.log('Invoice search script loaded');

    // Check if we're in the return invoice modal
    if (document.getElementById('returnInvoiceModal')) {
        console.log('Return invoice modal found');
        setupInvoiceSearchListeners();
    } else {
        console.log('Return invoice modal not found');
    }
});

/**
 * Setup event listeners for invoice search
 */
function setupInvoiceSearchListeners() {
    console.log('Setting up invoice search listeners');

    // Get elements
    const searchInvoiceBtn = document.getElementById('search-invoice-btn');
    const invoiceNumberSearch = document.getElementById('invoice-number-search');

    console.log('Search button:', searchInvoiceBtn);
    console.log('Search input:', invoiceNumberSearch);

    if (searchInvoiceBtn && invoiceNumberSearch) {
        // Search for invoice when button is clicked
        searchInvoiceBtn.addEventListener('click', function() {
            console.log('Search button clicked');
            const invoiceNumber = invoiceNumberSearch.value.trim();
            if (invoiceNumber) {
                searchInvoice(invoiceNumber);
            } else {
                showNotification('يرجى إدخال رقم الفاتورة', 'warning');
            }
        });

        // Search for invoice when Enter key is pressed
        invoiceNumberSearch.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                console.log('Enter key pressed in search input');
                const invoiceNumber = this.value.trim();
                if (invoiceNumber) {
                    searchInvoice(invoiceNumber);
                } else {
                    showNotification('يرجى إدخال رقم الفاتورة', 'warning');
                }
            }
        });
    } else {
        console.error('Could not find search button or input field');
    }

    // Close return modal
    const closeReturnModal = document.getElementById('close-return-modal');
    const cancelReturn = document.getElementById('cancel-return');

    if (closeReturnModal) {
        closeReturnModal.addEventListener('click', function() {
            hideModal('returnInvoiceModal', 'return-modal-content');
        });
    }

    if (cancelReturn) {
        cancelReturn.addEventListener('click', function() {
            hideModal('returnInvoiceModal', 'return-modal-content');
        });
    }
}

/**
 * Search for invoice by invoice number
 * @param {string} invoiceNumber - The invoice number to search for
 */
function searchInvoice(invoiceNumber) {
    console.log('Searching for invoice:', invoiceNumber);
    showNotification('جاري البحث عن الفاتورة...', 'info');

    // Show loading state
    const invoiceDetails = document.getElementById('invoice-details');
    if (!invoiceDetails) {
        console.error('Could not find invoice-details element');
        return;
    }

    invoiceDetails.innerHTML = `
        <div class="flex items-center justify-center p-4">
            <div class="w-8 h-8 rounded-full bg-blue-100 flex items-center justify-center text-blue-500 animate-spin mr-2">
                <i class="ri-loader-4-line"></i>
            </div>
            <span class="text-gray-600">جاري البحث...</span>
        </div>
    `;
    invoiceDetails.classList.remove('hidden');

    // Fetch invoice data
    console.log('Fetching invoice data from:', `/api/sales/invoice/${invoiceNumber}`);
    fetch(`/api/sales/invoice/${invoiceNumber}`)
        .then(response => {
            console.log('Response status:', response.status);
            if (!response.ok) {
                throw new Error(`Invoice not found (${response.status})`);
            }
            return response.json();
        })
        .then(data => {
            console.log('Response data:', data);
            if (data.success) {
                displayInvoiceDetails(data.invoice, data.items);
                showNotification('تم العثور على الفاتورة', 'success');
            } else {
                showNotification(data.message || 'حدث خطأ أثناء البحث عن الفاتورة', 'error');
                invoiceDetails.innerHTML = `
                    <div class="text-center p-4 text-red-500">
                        <i class="ri-error-warning-line text-2xl mb-2"></i>
                        <p>${data.message || 'حدث خطأ أثناء البحث عن الفاتورة'}</p>
                    </div>
                `;
            }
        })
        .catch(error => {
            console.error('Error searching for invoice:', error);
            showNotification('لم يتم العثور على الفاتورة', 'error');
            invoiceDetails.innerHTML = `
                <div class="text-center p-4 text-red-500">
                    <i class="ri-error-warning-line text-2xl mb-2"></i>
                    <p>لم يتم العثور على الفاتورة</p>
                </div>
            `;
        });
}

/**
 * Display invoice details
 * @param {Object} invoice - The invoice object
 * @param {Array} items - The invoice items
 */
function displayInvoiceDetails(invoice, items) {
    const invoiceDetails = document.getElementById('invoice-details');

    let html = `
        <div class="bg-blue-50 p-3 rounded-lg mb-3">
            <div class="flex justify-between items-center mb-2">
                <span class="text-gray-700 text-sm">رقم الفاتورة:</span>
                <span class="text-primary font-bold">${invoice.invoice_number}</span>
            </div>
            <div class="flex justify-between items-center mb-2">
                <span class="text-gray-700 text-sm">تاريخ الفاتورة:</span>
                <span class="text-gray-800 text-sm">${new Date(invoice.created_at).toLocaleString('ar-EG')}</span>
            </div>
            <div class="flex justify-between items-center mb-2">
                <span class="text-gray-700 text-sm">العميل:</span>
                <span class="text-gray-800 text-sm">${invoice.customer_name || 'عميل نقدي'}</span>
            </div>
            <div class="flex justify-between items-center">
                <span class="text-gray-700 text-sm">إجمالي الفاتورة:</span>
                <span class="text-primary font-bold">${invoice.total.toFixed(2)} ج.م</span>
            </div>
        </div>

        <h4 class="font-medium text-gray-700 mb-2 text-sm">منتجات الفاتورة:</h4>
    `;

    // Add items
    html += '<div class="space-y-2 max-h-60 overflow-y-auto">';

    items.forEach(item => {
        html += `
            <div class="bg-white p-2 rounded border border-gray-200">
                <div class="flex justify-between items-center">
                    <span class="text-sm font-medium">${item.product_name}</span>
                    <span class="text-sm text-primary">${item.price.toFixed(2)} ج.م</span>
                </div>
                <div class="flex justify-between items-center text-xs text-gray-500 mt-1">
                    <span>الكمية: ${item.quantity}</span>
                    <span>الإجمالي: ${(item.price * item.quantity).toFixed(2)} ج.م</span>
                </div>
            </div>
        `;
    });

    html += '</div>';

    // Add buttons
    html += `
        <div class="mt-3 flex space-x-2 space-x-reverse">
            <button id="print-found-invoice" class="px-3 py-1.5 bg-blue-500 text-white rounded text-sm hover:bg-blue-600 transition-colors flex items-center">
                <i class="ri-printer-line mr-1"></i>
                طباعة الفاتورة
            </button>
            <button id="return-found-invoice" class="px-3 py-1.5 bg-red-500 text-white rounded text-sm hover:bg-red-600 transition-colors flex items-center">
                <i class="ri-arrow-go-back-line mr-1"></i>
                إرجاع الفاتورة
            </button>
        </div>
    `;

    invoiceDetails.innerHTML = html;
    invoiceDetails.classList.remove('hidden');

    // Add event listeners to buttons
    document.getElementById('print-found-invoice').addEventListener('click', function() {
        printFoundInvoice(invoice.id);
    });

    document.getElementById('return-found-invoice').addEventListener('click', function() {
        returnFoundInvoice(invoice, items);
    });
}

/**
 * Print found invoice
 * @param {number} invoiceId - The invoice ID
 */
function printFoundInvoice(invoiceId) {
    // Create a hidden iframe for printing
    const iframe = document.createElement('iframe');
    iframe.style.display = 'none';
    iframe.src = `/pos/invoice/${invoiceId}`;
    document.body.appendChild(iframe);

    // Wait for iframe to load then print
    iframe.onload = function() {
        try {
            iframe.contentWindow.print();

            // Remove iframe after printing (with delay)
            setTimeout(function() {
                document.body.removeChild(iframe);
            }, 1000);
        } catch (e) {
            console.error('Error printing invoice:', e);
            showNotification('حدث خطأ أثناء الطباعة', 'error');
            document.body.removeChild(iframe);
        }
    };
}

/**
 * Return found invoice
 * @param {Object} invoice - The invoice object
 * @param {Array} items - The invoice items
 */
function returnFoundInvoice(invoice, items) {
    // إغلاق النافذة المنبثقة
    hideModal('returnInvoiceModal', 'return-modal-content');

    // فتح نافذة استرجاع المنتجات
    if (typeof openReturnProductsModal === 'function') {
        // استخدام نظام استرجاع المنتجات الجديد
        openReturnProductsModal(invoice, items);
    } else {
        // الطريقة القديمة: توجيه المستخدم إلى نقطة البيع مع الفاتورة المسترجعة
        const posUrl = `/pos?invoice=${invoice.invoice_number}`;

        // إظهار رسالة تأكيد
        if (confirm('هل تريد استرجاع هذه الفاتورة في نقطة البيع؟')) {
            // فتح نقطة البيع في نفس النافذة
            window.location.href = posUrl;
        }
    }
}

/**
 * Show notification if not already defined
 * @param {string} message - The message to show
 * @param {string} type - The type of notification (info, success, error, warning)
 */
if (typeof showNotification !== 'function') {
    function showNotification(message, type = 'info') {
        console.log(`Notification (${type}): ${message}`);

        const container = document.getElementById('notification-container');
        if (!container) {
            console.error('Notification container not found');
            alert(message);
            return;
        }

        const id = 'notification-' + Date.now();

        const notification = document.createElement('div');
        notification.id = id;
        notification.className = `notification notification-${type}`;

        let icon;
        switch (type) {
            case 'success':
                icon = 'ri-check-line';
                break;
            case 'error':
                icon = 'ri-error-warning-line';
                break;
            case 'warning':
                icon = 'ri-alert-line';
                break;
            default:
                icon = 'ri-information-line';
        }

        notification.innerHTML = `
            <div class="mr-3 text-xl">
                <i class="${icon}"></i>
            </div>
            <div class="flex-1">
                <p class="text-sm">${message}</p>
            </div>
            <button class="ml-2 text-gray-500 hover:text-gray-700" onclick="document.getElementById('${id}').remove()">
                <i class="ri-close-line"></i>
            </button>
        `;

        container.appendChild(notification);

        // Remove notification after 5 seconds
        setTimeout(() => {
            if (document.getElementById(id)) {
                document.getElementById(id).remove();
            }
        }, 5000);
    }
}
