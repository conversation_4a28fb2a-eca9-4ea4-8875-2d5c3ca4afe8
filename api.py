
from flask import Blueprint, request, jsonify, g
from flask_login import current_user
from functools import wraps
from datetime import datetime, timedelta
import jwt
import uuid
import logging
from app import app, db
from models import User, Product, Category, Order, OrderItem, Customer, Supplier, Purchase, PurchaseItem
from security import Security

# إعداد التسجيل
logger = logging.getLogger("api")
logger.setLevel(logging.INFO)

api_blueprint = Blueprint('api', __name__)

# مفتاح JWT السري
JWT_SECRET = app.config.get('JWT_SECRET', 'jwt_super_secret_key')

def token_required(f):
    """مصادقة المستخدم باستخدام JWT"""
    @wraps(f)
    def decorated(*args, **kwargs):
        token = None
        
        # البحث عن التوكن في الهيدر
        if 'Authorization' in request.headers:
            auth_header = request.headers['Authorization']
            if auth_header.startswith('Bearer '):
                token = auth_header.split(' ')[1]
        
        if not token:
            return jsonify({'error': 'مطلوب توكن المصادقة!'}), 401
        
        try:
            # فك تشفير التوكن
            payload = jwt.decode(token, JWT_SECRET, algorithms=['HS256'])
            
            # التحقق من صلاحية التوكن
            if 'exp' in payload and datetime.fromtimestamp(payload['exp']) < datetime.utcnow():
                return jsonify({'error': 'انتهت صلاحية التوكن!'}), 401
            
            # البحث عن المستخدم
            user = User.query.get(payload['user_id'])
            
            if not user:
                return jsonify({'error': 'مستخدم غير موجود!'}), 401
            
            # حفظ المستخدم في g لاستخدامه في الدوال الأخرى
            g.user = user
            
            return f(*args, **kwargs)
        except jwt.ExpiredSignatureError:
            return jsonify({'error': 'انتهت صلاحية التوكن!'}), 401
        except jwt.InvalidTokenError:
            return jsonify({'error': 'توكن غير صالح!'}), 401
        except Exception as e:
            logger.error(f"خطأ في المصادقة: {str(e)}")
            return jsonify({'error': 'فشل في المصادقة!'}), 401
    
    return decorated

@api_blueprint.route('/auth/login', methods=['POST'])
def login():
    """تسجيل الدخول وإصدار JWT"""
    data = request.get_json()
    
    if not data or not data.get('username') or not data.get('password'):
        return jsonify({'error': 'بيانات غير كاملة!'}), 400
    
    username = data.get('username')
    password = data.get('password')
    
    # البحث عن المستخدم
    user = User.query.filter_by(username=username).first()
    
    if not user or not user.check_password(password):
        return jsonify({'error': 'اسم المستخدم أو كلمة المرور غير صحيحة!'}), 401
    
    # إنشاء توكن JWT
    token = Security.generate_token(user.id, user.role)
    
    # إرجاع التوكن وبيانات المستخدم
    return jsonify({
        'token': token,
        'user': {
            'id': user.id,
            'username': user.username,
            'email': user.email,
            'full_name': user.full_name,
            'role': user.role
        }
    })

@api_blueprint.route('/products', methods=['GET'])
@token_required
def get_products():
    """الحصول على قائمة المنتجات"""
    try:
        # تطبيق الفلترة
        category_id = request.args.get('category_id')
        search = request.args.get('search', '')
        
        query = Product.query
        
        if category_id and category_id.isdigit() and int(category_id) > 0:
            query = query.filter(Product.category_id == int(category_id))
        
        if search:
            query = query.filter(
                Product.name.ilike(f'%{search}%') | 
                Product.code.ilike(f'%{search}%') |
                Product.brand.ilike(f'%{search}%')
            )
        
        # التصنيف والترقيم
        sort_by = request.args.get('sort_by', 'id')
        sort_dir = request.args.get('sort_dir', 'asc')
        
        if sort_dir == 'desc':
            query = query.order_by(getattr(Product, sort_by).desc())
        else:
            query = query.order_by(getattr(Product, sort_by).asc())
        
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 20, type=int)
        
        # الحصول على نتائج الاستعلام
        products_paginated = query.paginate(page=page, per_page=per_page)
        
        # تحويل النتائج إلى قاموس
        products = [{
            'id': product.id,
            'name': product.name,
            'code': product.code,
            'brand': product.brand,
            'description': product.description,
            'price': product.price,
            'cost_price': product.cost_price,
            'stock_quantity': product.stock_quantity,
            'minimum_stock': product.minimum_stock,
            'category_id': product.category_id,
            'category_name': product.category.name if product.category else None,
            'image_path': product.image_path,
            'is_active': product.is_active,
            'stock_status': product.get_stock_status()
        } for product in products_paginated.items]
        
        # إرجاع النتائج
        return jsonify({
            'products': products,
            'pagination': {
                'page': products_paginated.page,
                'per_page': products_paginated.per_page,
                'total_pages': products_paginated.pages,
                'total_items': products_paginated.total
            }
        })
    except Exception as e:
        logger.error(f"خطأ في الحصول على المنتجات: {str(e)}")
        return jsonify({'error': f'فشل في الحصول على المنتجات: {str(e)}'}), 500

@api_blueprint.route('/categories', methods=['GET'])
@token_required
def get_categories():
    """الحصول على قائمة الفئات"""
    try:
        categories = Category.query.order_by(Category.name).all()
        
        return jsonify({
            'categories': [category.to_dict() for category in categories]
        })
    except Exception as e:
        logger.error(f"خطأ في الحصول على الفئات: {str(e)}")
        return jsonify({'error': f'فشل في الحصول على الفئات: {str(e)}'}), 500

@api_blueprint.route('/orders', methods=['GET'])
@token_required
def get_orders():
    """الحصول على قائمة الطلبات"""
    try:
        # تطبيق الفلترة
        status = request.args.get('status')
        customer_id = request.args.get('customer_id')
        date_from = request.args.get('date_from')
        date_to = request.args.get('date_to')
        
        query = Order.query
        
        if status:
            query = query.filter(Order.status == status)
        
        if customer_id and customer_id.isdigit() and int(customer_id) > 0:
            query = query.filter(Order.customer_id == int(customer_id))
        
        if date_from:
            try:
                date_from = datetime.strptime(date_from, '%Y-%m-%d')
                query = query.filter(Order.created_at >= date_from)
            except Exception:
                pass
        
        if date_to:
            try:
                date_to = datetime.strptime(date_to, '%Y-%m-%d')
                date_to = date_to.replace(hour=23, minute=59, second=59)
                query = query.filter(Order.created_at <= date_to)
            except Exception:
                pass
        
        # التصنيف والترقيم
        sort_by = request.args.get('sort_by', 'created_at')
        sort_dir = request.args.get('sort_dir', 'desc')
        
        if sort_dir == 'desc':
            query = query.order_by(getattr(Order, sort_by).desc())
        else:
            query = query.order_by(getattr(Order, sort_by).asc())
        
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 20, type=int)
        
        # الحصول على نتائج الاستعلام
        orders_paginated = query.paginate(page=page, per_page=per_page)
        
        # تحويل النتائج إلى قاموس
        orders = [order.to_dict() for order in orders_paginated.items]
        
        # إرجاع النتائج
        return jsonify({
            'orders': orders,
            'pagination': {
                'page': orders_paginated.page,
                'per_page': orders_paginated.per_page,
                'total_pages': orders_paginated.pages,
                'total_items': orders_paginated.total
            }
        })
    except Exception as e:
        logger.error(f"خطأ في الحصول على الطلبات: {str(e)}")
        return jsonify({'error': f'فشل في الحصول على الطلبات: {str(e)}'}), 500

@api_blueprint.route('/orders/<int:order_id>', methods=['GET'])
@token_required
def get_order(order_id):
    """الحصول على تفاصيل طلب معين"""
    try:
        order = Order.query.get_or_404(order_id)
        
        return jsonify({
            'order': order.to_dict()
        })
    except Exception as e:
        logger.error(f"خطأ في الحصول على تفاصيل الطلب: {str(e)}")
        return jsonify({'error': f'فشل في الحصول على تفاصيل الطلب: {str(e)}'}), 500

@api_blueprint.route('/orders', methods=['POST'])
@token_required
def create_order():
    """إنشاء طلب جديد"""
    try:
        data = request.get_json()
        
        if not data or 'items' not in data or not data['items']:
            return jsonify({'error': 'بيانات غير كاملة!'}), 400
        
        # إنشاء رقم الفاتورة
        invoice_number = f"INV-{datetime.utcnow().strftime('%Y%m%d')}-{uuid.uuid4().hex[:6].upper()}"
        
        # حساب المجموع الفرعي
        subtotal = sum(item['price'] * item['quantity'] for item in data['items'])
        
        # حساب الخصم
        discount = data.get('discount', 0)
        discount_type = data.get('discount_type', 'fixed')
        
        if discount_type == 'percentage':
            discount_amount = subtotal * (discount / 100)
        else:
            discount_amount = discount
        
        # حساب الضريبة
        tax_percentage = data.get('tax_percentage', 0)
        tax = (subtotal - discount_amount) * (tax_percentage / 100)
        
        # حساب الإجمالي
        total = subtotal - discount_amount + tax
        
        # إنشاء الطلب
        order = Order(
            invoice_number=invoice_number,
            customer_id=data.get('customer_id'),
            user_id=g.user.id,
            subtotal=subtotal,
            discount=discount,
            discount_type=discount_type,
            tax=tax,
            tax_percentage=tax_percentage,
            total=total,
            payment_method=data.get('payment_method', 'cash'),
            status=data.get('status', 'completed')
        )
        
        # إضافة عناصر الطلب
        for item_data in data['items']:
            product = Product.query.get(item_data['id'])
            
            if not product:
                continue
            
            # إنشاء عنصر الطلب
            order_item = OrderItem(
                product_id=product.id,
                quantity=item_data['quantity'],
                price=item_data['price'],
                total=item_data['price'] * item_data['quantity']
            )
            
            # تحديث مخزون المنتج
            if order.status == 'completed':
                product.stock_quantity -= item_data['quantity']
            
            # إضافة العنصر إلى الطلب
            order.items.append(order_item)
        
        # حفظ الطلب في قاعدة البيانات
        db.session.add(order)
        db.session.commit()
        
        return jsonify({
            'success': True,
            'message': 'تم إنشاء الطلب بنجاح!',
            'order': order.to_dict()
        })
    except Exception as e:
        db.session.rollback()
        logger.error(f"خطأ في إنشاء الطلب: {str(e)}")
        return jsonify({'error': f'فشل في إنشاء الطلب: {str(e)}'}), 500

@api_blueprint.route('/customers', methods=['GET'])
@token_required
def get_customers():
    """الحصول على قائمة العملاء"""
    try:
        # تطبيق الفلترة
        search = request.args.get('search', '')
        
        query = Customer.query
        
        if search:
            query = query.filter(
                Customer.name.ilike(f'%{search}%') | 
                Customer.phone.ilike(f'%{search}%') |
                Customer.email.ilike(f'%{search}%')
            )
        
        # التصنيف والترقيم
        sort_by = request.args.get('sort_by', 'name')
        sort_dir = request.args.get('sort_dir', 'asc')
        
        if sort_dir == 'desc':
            query = query.order_by(getattr(Customer, sort_by).desc())
        else:
            query = query.order_by(getattr(Customer, sort_by).asc())
        
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 20, type=int)
        
        # الحصول على نتائج الاستعلام
        customers_paginated = query.paginate(page=page, per_page=per_page)
        
        # تحويل النتائج إلى قاموس
        customers = [customer.to_dict() for customer in customers_paginated.items]
        
        # إرجاع النتائج
        return jsonify({
            'customers': customers,
            'pagination': {
                'page': customers_paginated.page,
                'per_page': customers_paginated.per_page,
                'total_pages': customers_paginated.pages,
                'total_items': customers_paginated.total
            }
        })
    except Exception as e:
        logger.error(f"خطأ في الحصول على العملاء: {str(e)}")
        return jsonify({'error': f'فشل في الحصول على العملاء: {str(e)}'}), 500

@api_blueprint.route('/suppliers', methods=['GET'])
@token_required
def get_suppliers():
    """الحصول على قائمة الموردين"""
    try:
        # تطبيق الفلترة
        search = request.args.get('search', '')
        
        query = Supplier.query
        
        if search:
            query = query.filter(
                Supplier.name.ilike(f'%{search}%') | 
                Supplier.phone.ilike(f'%{search}%') |
                Supplier.email.ilike(f'%{search}%')
            )
        
        # التصنيف والترقيم
        sort_by = request.args.get('sort_by', 'name')
        sort_dir = request.args.get('sort_dir', 'asc')
        
        if sort_dir == 'desc':
            query = query.order_by(getattr(Supplier, sort_by).desc())
        else:
            query = query.order_by(getattr(Supplier, sort_by).asc())
        
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 20, type=int)
        
        # الحصول على نتائج الاستعلام
        suppliers_paginated = query.paginate(page=page, per_page=per_page)
        
        # تحويل النتائج إلى قاموس
        suppliers = [supplier.to_dict() for supplier in suppliers_paginated.items]
        
        # إرجاع النتائج
        return jsonify({
            'suppliers': suppliers,
            'pagination': {
                'page': suppliers_paginated.page,
                'per_page': suppliers_paginated.per_page,
                'total_pages': suppliers_paginated.pages,
                'total_items': suppliers_paginated.total
            }
        })
    except Exception as e:
        logger.error(f"خطأ في الحصول على الموردين: {str(e)}")
        return jsonify({'error': f'فشل في الحصول على الموردين: {str(e)}'}), 500

@api_blueprint.route('/dashboard/stats', methods=['GET'])
@token_required
def get_dashboard_stats():
    """الحصول على إحصائيات لوحة التحكم"""
    try:
        # الحصول على التاريخ الحالي
        today = datetime.utcnow().date()
        month_start = datetime(today.year, today.month, 1)
        
        # إحصائيات المبيعات اليومية
        daily_sales = db.session.query(db.func.sum(Order.total)).filter(
            db.func.date(Order.created_at) == today,
            Order.status == 'completed'
        ).scalar() or 0
        
        # عدد الطلبات اليومية
        daily_orders_count = Order.query.filter(
            db.func.date(Order.created_at) == today
        ).count()
        
        # إحصائيات المبيعات الشهرية
        monthly_sales = db.session.query(db.func.sum(Order.total)).filter(
            Order.created_at >= month_start,
            Order.status == 'completed'
        ).scalar() or 0
        
        # احصائيات المخزون
        products = Product.query.filter_by(is_active=True).all()
        low_stock_products = 0
        out_of_stock_products = 0
        
        for product in products:
            if product.stock_quantity <= 0:
                out_of_stock_products += 1
            elif product.stock_quantity <= product.minimum_stock:
                low_stock_products += 1
        
        # إحصائيات أخرى
        total_products = Product.query.count()
        active_products = Product.query.filter_by(is_active=True).count()
        total_customers = Customer.query.count()
        total_suppliers = Supplier.query.count()
        
        # إرجاع الإحصائيات
        return jsonify({
            'sales': {
                'daily': daily_sales,
                'monthly': monthly_sales,
                'orders_today': daily_orders_count
            },
            'inventory': {
                'low_stock': low_stock_products,
                'out_of_stock': out_of_stock_products,
                'total_products': total_products,
                'active_products': active_products
            },
            'customers': {
                'total': total_customers
            },
            'suppliers': {
                'total': total_suppliers
            }
        })
    except Exception as e:
        logger.error(f"خطأ في الحصول على إحصائيات لوحة التحكم: {str(e)}")
        return jsonify({'error': f'فشل في الحصول على إحصائيات لوحة التحكم: {str(e)}'}), 500

# تسجيل البلوبرنت في التطبيق الرئيسي
def register_api(app):
    app.register_blueprint(api_blueprint, url_prefix='/api')
