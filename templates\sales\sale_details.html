<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تفاصيل الطلب #{{ order.invoice_number }} - نظام إدارة المبيعات</title>
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <!-- Remixicon -->
    <link href="https://cdn.jsdelivr.net/npm/remixicon@2.5.0/fonts/remixicon.css" rel="stylesheet">
    <style>
        .bg-pattern {
            background-color: #f9fafb;
            background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='40' height='40' viewBox='0 0 40 40'%3E%3Cg fill-rule='evenodd'%3E%3Cg fill='%23e5e7eb' fill-opacity='0.4'%3E%3Cpath d='M0 38.59l2.83-2.83 1.41 1.41L1.41 40H0v-1.41zM0 1.4l2.83 2.83 1.41-1.41L1.41 0H0v1.41zM38.59 40l-2.83-2.83 1.41-1.41L40 38.59V40h-1.41zM40 1.41l-2.83 2.83-1.41-1.41L38.59 0H40v1.41zM20 18.6l2.83-2.83 1.41 1.41L21.41 20l2.83 2.83-1.41 1.41L20 21.41l-2.83 2.83-1.41-1.41L18.59 20l-2.83-2.83 1.41-1.41L20 18.59z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
        }
        .rtl {
            direction: rtl;
        }
        .glass-effect {
            background-color: rgba(255, 255, 255, 0.8);
            backdrop-filter: blur(10px);
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
        }
    </style>
</head>
<body class="bg-pattern">
    <div class="flex h-screen overflow-hidden">
        <!-- Sidebar -->
        {% include 'partials/sidebar.html' %}
        
        <!-- Main Content -->
        <div class="flex-1 flex flex-col overflow-hidden">
            <!-- Top Navigation -->
            {% include 'partials/topnav.html' %}
            
            <!-- Content Area -->
            <main class="flex-1 overflow-y-auto p-6">
                <div class="mb-6 flex items-center justify-between">
                    <div>
                        <h1 class="text-2xl font-bold text-gray-800">تفاصيل الطلب #{{ order.invoice_number }}</h1>
                        <p class="text-gray-600">{{ order.created_at.strftime('%Y-%m-%d %H:%M') }}</p>
                    </div>
                    <div class="flex space-x-2 space-x-reverse">
                        <a href="{{ url_for('sales.index') }}" class="px-4 py-2 bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 transition-all !rounded-button whitespace-nowrap">
                            <i class="ri-arrow-right-line"></i>
                            العودة للمبيعات
                        </a>
                        
                        <button onclick="printReceipt()" class="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 transition-all !rounded-button whitespace-nowrap">
                            <i class="ri-printer-line"></i>
                            طباعة الفاتورة
                        </button>
                        
                        {% if order.status != 'cancelled' %}
                        <button onclick="confirmCancel()" class="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 transition-all !rounded-button whitespace-nowrap">
                            <i class="ri-close-circle-line"></i>
                            إلغاء الطلب
                        </button>
                        {% endif %}
                    </div>
                </div>
                
                <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
                    <!-- Order Details -->
                    <div class="lg:col-span-2">
                        <div class="glass-effect rounded-lg overflow-hidden mb-6">
                            <div class="p-5 border-b border-gray-200">
                                <h2 class="text-lg font-medium text-gray-800">تفاصيل الطلب</h2>
                            </div>
                            <div class="p-5">
                                <div class="grid grid-cols-2 gap-4">
                                    <div>
                                        <p class="text-sm text-gray-500 mb-1">رقم الفاتورة</p>
                                        <p class="font-medium">{{ order.invoice_number }}</p>
                                    </div>
                                    <div>
                                        <p class="text-sm text-gray-500 mb-1">التاريخ</p>
                                        <p class="font-medium">{{ order.created_at.strftime('%Y-%m-%d %H:%M') }}</p>
                                    </div>
                                    <div>
                                        <p class="text-sm text-gray-500 mb-1">العميل</p>
                                        <p class="font-medium">{{ order.customer.name if order.customer else 'عميل عام' }}</p>
                                    </div>
                                    <div>
                                        <p class="text-sm text-gray-500 mb-1">البائع</p>
                                        <p class="font-medium">{{ order.user.full_name if order.user else 'غير معروف' }}</p>
                                    </div>
                                    <div>
                                        <p class="text-sm text-gray-500 mb-1">طريقة الدفع</p>
                                        <p class="font-medium">
                                            {% if order.payment_method == 'cash' %}
                                            <span class="px-2 py-1 text-xs rounded-full bg-green-100 text-green-800">نقدي</span>
                                            {% elif order.payment_method == 'card' %}
                                            <span class="px-2 py-1 text-xs rounded-full bg-blue-100 text-blue-800">بطاقة</span>
                                            {% else %}
                                            <span class="px-2 py-1 text-xs rounded-full bg-gray-100 text-gray-800">{{ order.payment_method }}</span>
                                            {% endif %}
                                        </p>
                                    </div>
                                    <div>
                                        <p class="text-sm text-gray-500 mb-1">الحالة</p>
                                        <p class="font-medium">
                                            {% if order.status == 'completed' %}
                                            <span class="px-2 py-1 text-xs rounded-full bg-green-100 text-green-800">مكتمل</span>
                                            {% elif order.status == 'pending' %}
                                            <span class="px-2 py-1 text-xs rounded-full bg-yellow-100 text-yellow-800">معلق</span>
                                            {% elif order.status == 'cancelled' %}
                                            <span class="px-2 py-1 text-xs rounded-full bg-red-100 text-red-800">ملغي</span>
                                            {% else %}
                                            <span class="px-2 py-1 text-xs rounded-full bg-gray-100 text-gray-800">{{ order.status }}</span>
                                            {% endif %}
                                        </p>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="glass-effect rounded-lg overflow-hidden">
                            <div class="p-5 border-b border-gray-200">
                                <h2 class="text-lg font-medium text-gray-800">المنتجات</h2>
                            </div>
                            <div class="overflow-x-auto">
                                <table class="min-w-full divide-y divide-gray-200">
                                    <thead class="bg-gray-50">
                                        <tr>
                                            <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">المنتج</th>
                                            <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">السعر</th>
                                            <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الكمية</th>
                                            <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الإجمالي</th>
                                        </tr>
                                    </thead>
                                    <tbody class="bg-white divide-y divide-gray-200">
                                        {% for item in order.items %}
                                        <tr>
                                            <td class="px-6 py-4 whitespace-nowrap">
                                                <div class="flex items-center">
                                                    <div class="flex-shrink-0 h-10 w-10 rounded-md bg-gray-100 flex items-center justify-center text-gray-500">
                                                        {% if item.product.image_path %}
                                                        <img src="{{ item.product.image_path }}" alt="{{ item.product.name }}" class="h-10 w-10 rounded-md object-cover">
                                                        {% else %}
                                                        <i class="ri-shopping-bag-line ri-lg"></i>
                                                        {% endif %}
                                                    </div>
                                                    <div class="mr-4">
                                                        <div class="text-sm font-medium text-gray-900">{{ item.product.name }}</div>
                                                        <div class="text-sm text-gray-500">{{ item.product.code or '-' }}</div>
                                                    </div>
                                                </div>
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ "%.2f"|format(item.price) }} ج.م</td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ item.quantity }}</td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">{{ "%.2f"|format(item.total) }} ج.م</td>
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Order Summary -->
                    <div class="lg:col-span-1">
                        <div class="glass-effect rounded-lg overflow-hidden sticky top-6">
                            <div class="p-5 border-b border-gray-200">
                                <h2 class="text-lg font-medium text-gray-800">ملخص الطلب</h2>
                            </div>
                            <div class="p-5 space-y-4">
                                <div class="flex justify-between">
                                    <span class="text-gray-600">المجموع الفرعي</span>
                                    <span class="font-medium">{{ "%.2f"|format(order.subtotal) }} ج.م</span>
                                </div>
                                
                                {% if order.discount > 0 %}
                                <div class="flex justify-between">
                                    <span class="text-gray-600">الخصم</span>
                                    <span class="font-medium text-green-600">
                                        {% if order.discount_type == 'percentage' %}
                                        {{ "%.2f"|format(order.discount) }}٪ ({{ "%.2f"|format(order.subtotal * order.discount / 100) }} ج.م)
                                        {% else %}
                                        {{ "%.2f"|format(order.discount) }} ج.م
                                        {% endif %}
                                    </span>
                                </div>
                                {% endif %}
                                
                                {% if order.tax > 0 %}
                                <div class="flex justify-between">
                                    <span class="text-gray-600">الضريبة ({{ "%.2f"|format(order.tax_percentage) }}٪)</span>
                                    <span class="font-medium">{{ "%.2f"|format(order.tax) }} ج.م</span>
                                </div>
                                {% endif %}
                                
                                <div class="border-t border-gray-200 pt-4 mt-4">
                                    <div class="flex justify-between">
                                        <span class="text-lg font-bold">الإجمالي</span>
                                        <span class="text-lg font-bold">{{ "%.2f"|format(order.total) }} ج.م</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>
    
    <!-- Cancel Confirmation Modal -->
    <div id="cancelModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden">
        <div class="bg-white rounded-lg w-80 sm:w-96 overflow-hidden">
            <div class="p-5">
                <div class="flex items-center justify-center w-12 h-12 rounded-full bg-red-100 text-red-600 mx-auto mb-4">
                    <i class="ri-close-circle-line ri-lg"></i>
                </div>
                <h3 class="text-lg font-medium text-gray-900 text-center mb-2">تأكيد إلغاء الطلب</h3>
                <p class="text-sm text-gray-500 text-center mb-5">
                    هل أنت متأكد من إلغاء الطلب رقم:<br>
                    <span class="font-medium">{{ order.invoice_number }}</span>؟<br>
                    <span class="text-xs text-red-500">سيتم إعادة المنتجات للمخزون.</span>
                </p>
                <div class="flex justify-center gap-2">
                    <form id="cancelForm" method="POST" action="{{ url_for('sales.cancel', id=order.id) }}">
                        <button type="submit" class="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 transition-all !rounded-button whitespace-nowrap text-sm">
                            نعم، إلغاء الطلب
                        </button>
                    </form>
                    <button id="cancelCancel" class="px-4 py-2 bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 transition-all !rounded-button whitespace-nowrap text-sm">
                        تراجع
                    </button>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        function printReceipt() {
            // هنا يمكن إضافة كود لطباعة الفاتورة
            alert('جاري طباعة الفاتورة رقم: {{ order.invoice_number }}');
        }
        
        function confirmCancel() {
            document.getElementById('cancelModal').classList.remove('hidden');
        }
        
        document.getElementById('cancelCancel').addEventListener('click', function() {
            document.getElementById('cancelModal').classList.add('hidden');
        });
        
        document.addEventListener('click', function(event) {
            if (event.target === document.getElementById('cancelModal')) {
                document.getElementById('cancelModal').classList.add('hidden');
            }
        });
    </script>
</body>
</html>
