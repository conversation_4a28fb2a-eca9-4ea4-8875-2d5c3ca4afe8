<!-- Flash Messages -->
{% with messages = get_flashed_messages(with_categories=true) %}
    {% if messages %}
        <div class="flash-messages fixed top-20 left-4 right-4 z-50 space-y-2">
            {% for category, message in messages %}
                <div class="alert alert-{{ 'danger' if category == 'error' else category }} flex items-center justify-between p-4 rounded-lg shadow-lg animate-slide-down">
                    <div class="flex items-center">
                        {% if category == 'error' %}
                            <i class="ri-error-warning-line text-red-500 text-xl ml-3"></i>
                        {% elif category == 'success' %}
                            <i class="ri-check-line text-green-500 text-xl ml-3"></i>
                        {% elif category == 'warning' %}
                            <i class="ri-alert-line text-yellow-500 text-xl ml-3"></i>
                        {% else %}
                            <i class="ri-information-line text-blue-500 text-xl ml-3"></i>
                        {% endif %}
                        <span class="font-medium">{{ message }}</span>
                    </div>
                    <button onclick="this.parentElement.remove()" class="text-muted hover:text-primary">
                        <i class="ri-close-line"></i>
                    </button>
                </div>
            {% endfor %}
        </div>
        
        <script>
            // Auto-hide flash messages after 5 seconds
            setTimeout(() => {
                document.querySelectorAll('.flash-messages .alert').forEach(alert => {
                    alert.style.opacity = '0';
                    alert.style.transform = 'translateY(-20px)';
                    setTimeout(() => alert.remove(), 300);
                });
            }, 5000);
        </script>
    {% endif %}
{% endwith %}

<style>
.alert {
    background-color: var(--bg-card);
    border: 1px solid var(--border-primary);
    color: var(--text-primary);
}

.alert-success {
    background-color: #f0fdf4;
    border-color: #22c55e;
    color: #15803d;
}

.alert-danger {
    background-color: #fef2f2;
    border-color: #ef4444;
    color: #dc2626;
}

.alert-warning {
    background-color: #fffbeb;
    border-color: #f59e0b;
    color: #d97706;
}

.alert-info {
    background-color: #eff6ff;
    border-color: #3b82f6;
    color: #2563eb;
}

@keyframes slide-down {
    from {
        opacity: 0;
        transform: translateY(-20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.animate-slide-down {
    animation: slide-down 0.3s ease-out;
}
</style>
