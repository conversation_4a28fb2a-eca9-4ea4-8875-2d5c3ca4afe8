from flask import Blueprint, render_template, request, jsonify, redirect, url_for
from flask_login import login_required, current_user
from models import Notification
from app import db

notifications_blueprint = Blueprint('notifications', __name__)

@notifications_blueprint.route('/notifications')
@login_required
def index():
    """عرض قائمة الإشعارات"""
    # الحصول على معلمات الفلتر
    is_read = request.args.get('is_read', '')
    type = request.args.get('type', '')
    page = request.args.get('page', 1, type=int)
    per_page = 20  # عدد العناصر في الصفحة الواحدة
    
    # إعداد الاستعلام
    query = Notification.query.filter_by(user_id=current_user.id)
    
    # تطبيق الفلاتر
    if is_read:
        if is_read == 'yes':
            query = query.filter_by(is_read=True)
        elif is_read == 'no':
            query = query.filter_by(is_read=False)
    
    if type:
        query = query.filter_by(type=type)
    
    # تنفيذ الاستعلام مع الترقيم
    notifications_paginated = query.order_by(Notification.created_at.desc()).paginate(page=page, per_page=per_page)
    
    # إحصائيات الإشعارات
    unread_count = Notification.query.filter_by(user_id=current_user.id, is_read=False).count()
    info_count = Notification.query.filter_by(user_id=current_user.id, type='info').count()
    warning_count = Notification.query.filter_by(user_id=current_user.id, type='warning').count()
    danger_count = Notification.query.filter_by(user_id=current_user.id, type='danger').count()
    success_count = Notification.query.filter_by(user_id=current_user.id, type='success').count()
    
    stats = {
        'unread': unread_count,
        'info': info_count,
        'warning': warning_count,
        'danger': danger_count,
        'success': success_count,
        'total': notifications_paginated.total
    }
    
    return render_template('notifications.html',
                          notifications=notifications_paginated.items,
                          stats=stats,
                          is_read=is_read,
                          type=type,
                          page=page,
                          total_pages=notifications_paginated.pages)

@notifications_blueprint.route('/notifications/<int:id>/mark-read', methods=['POST'])
@login_required
def mark_read(id):
    """تحديد الإشعار كمقروء"""
    notification = Notification.query.get_or_404(id)
    
    # التحقق من أن الإشعار ينتمي للمستخدم الحالي
    if notification.user_id != current_user.id:
        return jsonify({'success': False, 'message': 'غير مصرح لك بتحديث هذا الإشعار'}), 403
    
    notification.is_read = True
    db.session.commit()
    
    return jsonify({'success': True})

@notifications_blueprint.route('/notifications/mark-all-read', methods=['POST'])
@login_required
def mark_all_read():
    """تحديد جميع الإشعارات كمقروءة"""
    Notification.query.filter_by(user_id=current_user.id, is_read=False).update({'is_read': True})
    db.session.commit()
    
    return jsonify({'success': True})

@notifications_blueprint.route('/api/notifications/count')
@login_required
def api_count():
    """الحصول على عدد الإشعارات غير المقروءة"""
    unread_count = Notification.query.filter_by(user_id=current_user.id, is_read=False).count()
    return jsonify({'count': unread_count})

@notifications_blueprint.route('/api/notifications/recent')
@login_required
def api_recent():
    """الحصول على أحدث الإشعارات"""
    notifications = Notification.query.filter_by(user_id=current_user.id).order_by(Notification.created_at.desc()).limit(5).all()
    return jsonify({
        'notifications': [notification.to_dict() for notification in notifications],
        'unread_count': Notification.query.filter_by(user_id=current_user.id, is_read=False).count()
    })
