{% extends 'layout.html' %}

{% block title %}الجرد الدوري{% endblock %}

{% block page_content %}
<div class="container mx-auto px-4 py-8">
    <div class="mb-6 flex justify-between items-center">
        <div>
            <h1 class="text-2xl font-bold">الجرد الدوري</h1>
            <p class="text-gray-600">إدارة عمليات الجرد الدوري للمخازن</p>
        </div>
        <div class="flex space-x-2 space-x-reverse">
            <a href="{{ url_for('warehouses.create_inventory_count') }}" class="bg-green-500 text-white px-4 py-2 rounded-lg hover:bg-green-600 transition-all ml-2">
                <i class="ri-add-line ml-1"></i>إنشاء عملية جرد جديدة
            </a>
            <a href="{{ url_for('warehouses.index') }}" class="bg-gray-100 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-200 transition-all">
                <i class="ri-arrow-right-line ml-1"></i>العودة للمخازن
            </a>
        </div>
    </div>

    <!-- إحصائيات الجرد -->
    {% if stats %}
    <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
        <div class="bg-white rounded-lg shadow p-4 border-r-4 border-blue-500">
            <div class="flex justify-between items-center">
                <div>
                    <p class="text-gray-500 text-sm">مسودات</p>
                    <p class="text-2xl font-bold">{{ stats.draft }}</p>
                </div>
                <div class="text-blue-500 text-3xl">
                    <i class="ri-draft-line"></i>
                </div>
            </div>
        </div>
        <div class="bg-white rounded-lg shadow p-4 border-r-4 border-yellow-500">
            <div class="flex justify-between items-center">
                <div>
                    <p class="text-gray-500 text-sm">قيد التنفيذ</p>
                    <p class="text-2xl font-bold">{{ stats.in_progress }}</p>
                </div>
                <div class="text-yellow-500 text-3xl">
                    <i class="ri-loader-4-line"></i>
                </div>
            </div>
        </div>
        <div class="bg-white rounded-lg shadow p-4 border-r-4 border-green-500">
            <div class="flex justify-between items-center">
                <div>
                    <p class="text-gray-500 text-sm">مكتملة</p>
                    <p class="text-2xl font-bold">{{ stats.completed }}</p>
                </div>
                <div class="text-green-500 text-3xl">
                    <i class="ri-checkbox-circle-line"></i>
                </div>
            </div>
        </div>
        <div class="bg-white rounded-lg shadow p-4 border-r-4 border-indigo-500">
            <div class="flex justify-between items-center">
                <div>
                    <p class="text-gray-500 text-sm">إجمالي عمليات الجرد</p>
                    <p class="text-2xl font-bold">{{ stats.total }}</p>
                </div>
                <div class="text-indigo-500 text-3xl">
                    <i class="ri-list-check-2"></i>
                </div>
            </div>
        </div>
    </div>
    {% endif %}

    <!-- فلاتر البحث -->
    <div class="bg-white rounded-lg shadow p-4 mb-6">
        <form method="GET" action="{{ url_for('warehouses.inventory_counts') }}" class="flex flex-wrap items-center gap-4">
            <div>
                <label class="block text-gray-700 mb-2">المخزن</label>
                <select name="warehouse_id" class="border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500">
                    <option value="">جميع المخازن</option>
                    {% for warehouse in warehouses %}
                    <option value="{{ warehouse.id }}" {% if warehouse_id == warehouse.id|string %}selected{% endif %}>{{ warehouse.name }}</option>
                    {% endfor %}
                </select>
            </div>
            <div>
                <label class="block text-gray-700 mb-2">الحالة</label>
                <select name="status" class="border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500">
                    <option value="">جميع الحالات</option>
                    <option value="draft" {% if status == 'draft' %}selected{% endif %}>مسودة</option>
                    <option value="in_progress" {% if status == 'in_progress' %}selected{% endif %}>قيد التنفيذ</option>
                    <option value="completed" {% if status == 'completed' %}selected{% endif %}>مكتملة</option>
                    <option value="cancelled" {% if status == 'cancelled' %}selected{% endif %}>ملغاة</option>
                </select>
            </div>
            <div class="self-end">
                <button type="submit" class="bg-blue-500 text-white px-4 py-2 rounded-md hover:bg-blue-600 transition-all">
                    تطبيق الفلتر
                </button>
            </div>
        </form>
    </div>

    <!-- جدول عمليات الجرد -->
    <div class="bg-white rounded-lg shadow overflow-hidden">
        <div class="p-4 bg-gray-50 border-b">
            <h2 class="text-xl font-semibold">قائمة عمليات الجرد</h2>
        </div>

        <table class="min-w-full">
            <thead>
                <tr class="bg-gray-100">
                    <th class="px-6 py-3 border-b text-right">اسم عملية الجرد</th>
                    <th class="px-6 py-3 border-b text-right">المخزن</th>
                    <th class="px-6 py-3 border-b text-right">التاريخ</th>
                    <th class="px-6 py-3 border-b text-right">المنشئ</th>
                    <th class="px-6 py-3 border-b text-right">الحالة</th>
                    <th class="px-6 py-3 border-b text-right">عدد العناصر</th>
                    <th class="px-6 py-3 border-b text-right">الإجراءات</th>
                </tr>
            </thead>
            <tbody>
                {% for count in counts %}
                <tr class="hover:bg-gray-50 {% if count.status == 'draft' %}bg-blue-50{% elif count.status == 'in_progress' %}bg-yellow-50{% elif count.status == 'completed' %}bg-green-50{% elif count.status == 'cancelled' %}bg-gray-50 opacity-70{% endif %}">
                    <td class="px-6 py-4 border-b font-medium">{{ count.name }}</td>
                    <td class="px-6 py-4 border-b">{{ count.warehouse.name }}</td>
                    <td class="px-6 py-4 border-b">{{ count.created_at.strftime('%Y-%m-%d %H:%M') }}</td>
                    <td class="px-6 py-4 border-b">{{ count.user.username if count.user else '-' }}</td>
                    <td class="px-6 py-4 border-b">
                        {% if count.status == 'draft' %}
                        <span class="bg-blue-100 text-blue-800 text-xs font-medium px-2.5 py-0.5 rounded">مسودة</span>
                        {% elif count.status == 'in_progress' %}
                        <span class="bg-yellow-100 text-yellow-800 text-xs font-medium px-2.5 py-0.5 rounded">قيد التنفيذ</span>
                        {% elif count.status == 'completed' %}
                        <span class="bg-green-100 text-green-800 text-xs font-medium px-2.5 py-0.5 rounded">مكتملة</span>
                        {% elif count.status == 'cancelled' %}
                        <span class="bg-gray-100 text-gray-800 text-xs font-medium px-2.5 py-0.5 rounded">ملغاة</span>
                        {% endif %}
                    </td>
                    <td class="px-6 py-4 border-b">{{ count.items|length }}</td>
                    <td class="px-6 py-4 border-b">
                        <div class="flex space-x-2 space-x-reverse">
                            <a href="{{ url_for('warehouses.view_inventory_count', id=count.id) }}" class="bg-blue-100 text-blue-700 hover:bg-blue-200 px-2 py-1 rounded-md text-xs transition-all">
                                <i class="ri-eye-line ml-1"></i>عرض
                            </a>
                            
                            {% if count.status == 'draft' or count.status == 'in_progress' %}
                            <form action="{{ url_for('warehouses.cancel_inventory_count', id=count.id) }}" method="POST" onsubmit="return confirm('هل أنت متأكد من إلغاء عملية الجرد؟')">
                                <button type="submit" class="bg-red-100 text-red-700 hover:bg-red-200 px-2 py-1 rounded-md text-xs transition-all">
                                    <i class="ri-close-line ml-1"></i>إلغاء
                                </button>
                            </form>
                            {% endif %}
                        </div>
                    </td>
                </tr>
                {% endfor %}
                
                {% if counts|length == 0 %}
                <tr>
                    <td colspan="7" class="px-6 py-8 text-center text-gray-500">
                        <div class="flex flex-col items-center">
                            <i class="ri-list-check-2 text-4xl mb-2"></i>
                            <p>لا توجد عمليات جرد</p>
                            <a href="{{ url_for('warehouses.create_inventory_count') }}" class="mt-2 text-blue-600 hover:underline">إنشاء عملية جرد جديدة</a>
                        </div>
                    </td>
                </tr>
                {% endif %}
            </tbody>
        </table>
        
        <!-- ترقيم الصفحات -->
        {% if total_pages > 1 %}
        <div class="px-6 py-4 border-t flex justify-center">
            <nav class="flex items-center space-x-2 space-x-reverse">
                {% if page > 1 %}
                <a href="{{ url_for('warehouses.inventory_counts', page=page-1, warehouse_id=warehouse_id, status=status) }}" class="px-3 py-1 rounded border hover:bg-gray-100">
                    السابق
                </a>
                {% endif %}
                
                {% for p in range(1, total_pages + 1) %}
                <a href="{{ url_for('warehouses.inventory_counts', page=p, warehouse_id=warehouse_id, status=status) }}" 
                   class="px-3 py-1 rounded border {% if p == page %}bg-blue-500 text-white{% else %}hover:bg-gray-100{% endif %}">
                    {{ p }}
                </a>
                {% endfor %}
                
                {% if page < total_pages %}
                <a href="{{ url_for('warehouses.inventory_counts', page=page+1, warehouse_id=warehouse_id, status=status) }}" class="px-3 py-1 rounded border hover:bg-gray-100">
                    التالي
                </a>
                {% endif %}
            </nav>
        </div>
        {% endif %}
    </div>
</div>
{% endblock page_content %}
