// Suspend current invoice
function suspendInvoice() {
    if (cartItems.length === 0) {
        showNotification('لا يمكن تعليق فاتورة فارغة', 'warning');
        return;
    }

    const invoiceNumber = document.getElementById('cart-invoice').textContent;
    const customerId = document.getElementById('customer-select').value;
    const customerName = customerId ? document.getElementById('customer-select').options[document.getElementById('customer-select').selectedIndex].text : 'عميل نقدي';

    // Create suspended invoice object
    const suspendedInvoice = {
        id: Date.now().toString(), // استخدام الطابع الزمني كمعرف فريد
        invoice_number: invoiceNumber,
        customer_id: customerId,
        customer_name: customerName,
        items: JSON.parse(JSON.stringify(cartItems)), // نسخة عميقة من العناصر
        created_at: new Date().toLocaleString('ar-EG'),
        subtotal: cartItems.reduce((sum, item) => sum + item.total, 0)
    };

    // Add to suspended invoices
    suspendedInvoices.push(suspendedInvoice);
    localStorage.setItem('suspendedInvoices', JSON.stringify(suspendedInvoices));

    // Clear cart
    cartItems = [];
    updateCartDisplay();

    // Generate new invoice number
    generateNewInvoiceNumber();

    // Update suspended count
    updateSuspendedCount();

    // Show notification
    showNotification('تم تعليق الفاتورة بنجاح', 'success');
}

// Load suspended invoices from localStorage
function loadSuspendedInvoices() {
    const saved = localStorage.getItem('suspendedInvoices');
    if (saved) {
        suspendedInvoices = JSON.parse(saved);
        updateSuspendedCount();
    }
}

// Update suspended invoices count badge
function updateSuspendedCount() {
    const badge = document.getElementById('suspended-count');
    if (suspendedInvoices.length > 0) {
        badge.textContent = suspendedInvoices.length;
        badge.classList.remove('hidden');
    } else {
        badge.classList.add('hidden');
    }
}

// Show suspended invoices in modal
function showSuspendedInvoices() {
    const container = document.getElementById('suspended-invoices-list');
    const template = document.getElementById('suspended-invoice-template');

    // Clear container
    container.innerHTML = '';

    if (suspendedInvoices.length === 0) {
        // Show empty message
        container.innerHTML = `
            <div class="flex flex-col items-center justify-center py-8 text-gray-400">
                <div class="w-20 h-20 rounded-full bg-yellow-50 flex items-center justify-center mb-3 shadow-sm">
                    <i class="ri-time-line ri-2x text-yellow-400"></i>
                </div>
                <p class="text-sm font-medium">لا توجد فواتير معلقة</p>
                <p class="text-xs mt-1 text-gray-500">يمكنك تعليق الفواتير والعودة إليها لاحقاً</p>
            </div>
        `;
        return;
    }

    // Render each suspended invoice
    suspendedInvoices.forEach((invoice, index) => {
        const clone = template.content.cloneNode(true);
        const card = clone.querySelector('.suspended-invoice-item');
        const number = clone.querySelector('.suspended-invoice-number');
        const customer = clone.querySelector('.suspended-invoice-customer');
        const total = clone.querySelector('.suspended-invoice-total');
        const date = clone.querySelector('.suspended-invoice-date');
        const count = clone.querySelector('.suspended-invoice-items-count');
        const loadBtn = clone.querySelector('.suspended-invoice-load');

        // Set invoice data
        number.textContent = invoice.invoice_number;
        customer.textContent = invoice.customer_name;
        total.textContent = `${invoice.subtotal.toFixed(2)} ج.م`;
        date.textContent = invoice.created_at;
        count.textContent = `${invoice.items.length} منتج`;

        // Add event listener
        loadBtn.addEventListener('click', () => {
            loadSuspendedInvoice(index);
        });

        container.appendChild(clone);
    });
}

// Load suspended invoice into cart
function loadSuspendedInvoice(index) {
    // Check if current cart has items
    if (cartItems.length > 0) {
        if (!confirm('سيتم مسح السلة الحالية. هل تريد المتابعة؟')) {
            return;
        }
    }

    const invoice = suspendedInvoices[index];

    // Set invoice number
    document.getElementById('cart-invoice').textContent = invoice.invoice_number;

    // Set customer
    document.getElementById('customer-select').value = invoice.customer_id || '';

    // Set cart items
    cartItems = JSON.parse(JSON.stringify(invoice.items));

    // Remove from suspended invoices
    suspendedInvoices.splice(index, 1);
    localStorage.setItem('suspendedInvoices', JSON.stringify(suspendedInvoices));

    // Update UI
    updateCartDisplay();
    hideModal('suspendedInvoicesModal', 'suspended-modal-content');
    updateSuspendedCount();

    // Show notification
    showNotification('تم تحميل الفاتورة المعلقة بنجاح', 'success');
}

// Generate new invoice number
function generateNewInvoiceNumber() {
    const date = new Date();
    const dateStr = date.getFullYear().toString() +
                   (date.getMonth() + 1).toString().padStart(2, '0') +
                   date.getDate().toString().padStart(2, '0');
    const random = Math.floor(Math.random() * 10000).toString().padStart(4, '0');

    document.getElementById('cart-invoice').textContent = `INV-${dateStr}-${random}`;
}

// Open checkout modal
function openCheckoutModal() {
    if (cartItems.length === 0) {
        showNotification('لا يمكن إتمام البيع بدون منتجات', 'warning');
        return;
    }

    // Set modal data
    const subtotal = calculateSubtotal();
    const discount = calculateDiscount();
    const tax = calculateTax();
    const total = calculateTotal();

    document.getElementById('confirm-subtotal').textContent = `${subtotal.toFixed(2)} ج.م`;
    document.getElementById('confirm-discount').textContent = `- ${discount.toFixed(2)} ج.م`;
    document.getElementById('confirm-tax').textContent = `+ ${tax.toFixed(2)} ج.م`;
    document.getElementById('confirm-total').textContent = `${total.toFixed(2)} ج.م`;

    // Set received amount field to empty for manual input
    document.getElementById('received-amount').value = '';
    document.getElementById('received-amount').placeholder = `${total.toFixed(2)} ج.م`;
    document.getElementById('change-amount').textContent = '0.00 ج.م';

    // Show/hide customer info
    const customerId = document.getElementById('customer-select').value;
    if (customerId) {
        const customerName = document.getElementById('customer-select').options[document.getElementById('customer-select').selectedIndex].text;
        document.getElementById('confirm-customer').textContent = customerName;
        document.getElementById('confirm-customer-container').classList.remove('hidden');
    } else {
        document.getElementById('confirm-customer-container').classList.add('hidden');
    }

    // Show modal with animation
    showModal('checkoutModal', 'checkout-modal-content');
}

// Process sale
function processSale() {
    const invoiceNumber = document.getElementById('cart-invoice').textContent;
    const customerId = document.getElementById('customer-select').value;
    const paymentMethod = selectedPaymentMethod;
    const total = calculateTotal();
    const receivedAmount = parseFloat(document.getElementById('received-amount').value) || 0;

    // Get the selected payment method card
    const selectedCard = document.querySelector(`.payment-method-card[data-method="${paymentMethod}"]`);
    const isCredit = selectedCard && selectedCard.dataset.isCredit === 'true';
    const requiresReference = selectedCard && selectedCard.dataset.requiresReference === 'true';

    // Validate payment method and customer selection
    if (isCredit) {
        // For credit sales, customer selection is mandatory
        if (!customerId || customerId === 'null' || customerId === '') {
            showNotification('يجب اختيار عميل للبيع الآجل', 'error');
            return;
        }
    }

    // Validate reference details for methods that require it
    if (requiresReference) {
        const cardNumber = document.getElementById('card-number').value;
        const cardName = document.getElementById('card-name').value;
        const cardExpiry = document.getElementById('card-expiry').value;
        const cardCvv = document.getElementById('card-cvv').value;

        if (!cardNumber || !cardName || !cardExpiry || !cardCvv) {
            showNotification('يرجى إدخال جميع بيانات المرجع المطلوبة', 'error');
            return;
        }

        // Basic validation for card number (16 digits)
        if (cardNumber.replace(/\s/g, '').length !== 16 || !/^\d+$/.test(cardNumber.replace(/\s/g, ''))) {
            showNotification('رقم المرجع غير صحيح، يجب أن يتكون من 16 رقم', 'error');
            return;
        }

        // Basic validation for expiry date (MM/YY format)
        if (!/^\d{2}\/\d{2}$/.test(cardExpiry)) {
            showNotification('تاريخ المرجع غير صحيح، يجب أن يكون بصيغة MM/YY', 'error');
            return;
        }

        // Basic validation for CVV (3-4 digits)
        if (!/^\d{3,4}$/.test(cardCvv)) {
            showNotification('رمز التحقق غير صحيح، يجب أن يتكون من 3 أو 4 أرقام', 'error');
            return;
        }
    }

    // Validate received amount for cash payments
    if (paymentMethod === 'cash' && receivedAmount > 0 && receivedAmount < total) {
        // Only show warning if amount is entered but less than total
        if (!confirm('المبلغ المستلم أقل من إجمالي الفاتورة. هل تريد المتابعة؟')) {
            return;
        }
    }

    // Validate cart items
    if (cartItems.length === 0) {
        showNotification('لا يمكن إتمام البيع، السلة فارغة', 'error');
        return;
    }

    // Check for invalid product IDs
    console.log('Checking cart items for invalid product IDs:', cartItems);

    // Check each item individually and log details
    cartItems.forEach((item, index) => {
        console.log(`Item ${index + 1}:`, item);
        if (!item.id) {
            console.error(`Item ${index + 1} has invalid product_id:`, item);
        }
    });

    const invalidItems = cartItems.filter(item => !item.id);
    if (invalidItems.length > 0) {
        console.error('Invalid items found:', invalidItems);
        showNotification(`هناك منتجات غير صالحة في السلة (${invalidItems.length} منتج)`, 'error');
        return;
    }

    // Prepare sale data
    const sale = {
        invoice_number: invoiceNumber,
        customer_id: customerId || null,
        warehouse_id: selectedWarehouse || null,  // إضافة معرف المخزن المحدد
        payment_method: paymentMethod,
        items: cartItems.map(item => {
            // Ensure product_id is valid
            if (!item.id) {
                console.error('Invalid product_id in item:', item);
                throw new Error(`معرف المنتج غير صالح: ${item.name || 'منتج غير معروف'}`);
            }

            return {
                id: item.id, // Use 'id' instead of 'product_id' to match the server expectation
                product_id: item.id, // Also include product_id for backward compatibility
                quantity: item.quantity,
                price: item.price,
                discount: item.discount,
                discount_type: item.discount_type,
                total: item.total
            };
        }),
        subtotal: calculateSubtotal(),
        discount: calculateDiscount(),
        tax: calculateTax(),
        total: total,
        received_amount: receivedAmount,
        change_amount: Math.max(0, receivedAmount - total)
    };

    // Add reference details if the payment method requires it
    const selectedCard = document.querySelector(`.payment-method-card[data-method="${paymentMethod}"]`);
    const requiresReference = selectedCard && selectedCard.dataset.requiresReference === 'true';

    if (requiresReference) {
        sale.payment_details = {
            card_number: document.getElementById('card-number').value.replace(/\s/g, '').slice(-4), // Only store last 4 digits for security
            card_holder: document.getElementById('card-name').value,
            card_expiry: document.getElementById('card-expiry').value,
            payment_type: paymentMethod
        };
    }

    // Show loading notification
    showNotification('جاري معالجة البيع...', 'info');

    // Disable checkout button to prevent double submission
    document.getElementById('confirm-checkout').disabled = true;
    document.getElementById('confirm-checkout').innerHTML = '<i class="ri-loader-4-line animate-spin mr-1"></i> جاري المعالجة...';

    console.log('Sending sale data:', sale);

    // Process the sale via API
    fetch('/api/pos/process-sale', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(sale)
    })
    .then(response => {
        console.log('Response status:', response.status);
        // Check if response is ok (status in the range 200-299)
        if (!response.ok) {
            console.error('Server returned error status:', response.status);
        }
        return response.json();
    })
    .then(data => {
        console.log('Response data:', data);

        // Re-enable checkout button
        document.getElementById('confirm-checkout').disabled = false;
        document.getElementById('confirm-checkout').innerHTML = '<i class="ri-check-line mr-1"></i> إتمام البيع';

        if (data.success) {
            console.log('Sale completed successfully');

            // Hide checkout modal
            hideModal('checkoutModal', 'checkout-modal-content');

            // Store order ID for printing
            document.getElementById('print-receipt').dataset.orderId = data.order_id;
            document.getElementById('print-invoice').dataset.orderId = data.order_id;
            document.getElementById('print-invoice-a4').dataset.orderId = data.order_id;
            document.getElementById('print-invoice-a5').dataset.orderId = data.order_id;

            // Reset invoice options dropdown
            const invoiceOptions = document.getElementById('invoice-options');
            if (invoiceOptions) {
                invoiceOptions.classList.add('hidden', 'opacity-0', 'scale-95');
                invoiceOptions.classList.remove('opacity-100', 'scale-100');
            }

            // Show success modal
            showModal('successModal', 'success-modal-content');

            // Clear cart
            cartItems = [];
            updateCartDisplay();

            // Generate new invoice number
            generateNewInvoiceNumber();
        } else {
            console.error('Sale failed:', data.message);
            showNotification(`فشل في إتمام البيع: ${data.message}`, 'error');
        }
    })
    .catch(error => {
        // Re-enable checkout button
        document.getElementById('confirm-checkout').disabled = false;
        document.getElementById('confirm-checkout').innerHTML = '<i class="ri-check-line mr-1"></i> إتمام البيع';

        console.error('Error processing sale:', error);
        showNotification('حدث خطأ أثناء معالجة البيع. يرجى المحاولة مرة أخرى.', 'error');
    });
}
