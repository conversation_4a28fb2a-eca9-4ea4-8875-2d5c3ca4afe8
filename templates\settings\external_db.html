<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Nobara - إعدادات قاعدة البيانات الخارجية</title>
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <!-- Remixicon -->
    <link href="https://cdn.jsdelivr.net/npm/remixicon@2.5.0/fonts/remixicon.css" rel="stylesheet">
    <!-- Alpine.js -->
    <script defer src="https://cdn.jsdelivr.net/npm/alpinejs@3.x.x/dist/cdn.min.js"></script>
    <script>
        tailwind.config = {
            darkMode: 'class',
            theme: {
                extend: {
                    colors: {
                        primary: {
                            50: '#eff6ff',
                            100: '#dbeafe',
                            200: '#bfdbfe',
                            300: '#93c5fd',
                            400: '#60a5fa',
                            500: '#3b82f6',
                            600: '#2563eb',
                            700: '#1d4ed8',
                            800: '#1e40af',
                            900: '#1e3a8a',
                            950: '#172554',
                        },
                        dark: {
                            100: '#1E293B',
                            200: '#0F172A',
                            300: '#0B1120',
                            400: '#060A15'
                        }
                    }
                }
            }
        }
    </script>
    <style>
        body {
            font-family: 'Cairo', 'Tajawal', sans-serif;
            transition: background-color 0.3s ease, color 0.3s ease;
        }
        
        .bg-pattern {
            background-color: #f9fafb;
            background-image: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%233B82F6' fill-opacity='0.05'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
        }
        
        .dark .bg-pattern {
            background-color: #0F172A;
            background-image: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%233B82F6' fill-opacity='0.1'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
        }
        
        .powered-by {
            position: fixed;
            bottom: 1rem;
            left: 1rem;
            background-color: #ffffff;
            border-radius: 1rem;
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
            padding: 0.75rem 1rem;
            display: flex;
            align-items: center;
            z-index: 40;
            transition: all 0.3s ease;
            border: 1px solid #e5e7eb;
        }
        
        .dark .powered-by {
            background-color: #1E293B;
            border-color: #2D3748;
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.3), 0 4px 6px -2px rgba(0, 0, 0, 0.2);
        }
        
        .powered-by:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
        }
        
        .dark .powered-by:hover {
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.3), 0 10px 10px -5px rgba(0, 0, 0, 0.2);
        }
    </style>
</head>
<body class="bg-pattern dark:bg-dark-200 dark:text-gray-100" x-data="{ darkMode: localStorage.getItem('darkMode') === 'enabled' }">
    <div class="flex h-screen overflow-hidden">
        <!-- Sidebar -->
        {% include 'partials/sidebar.html' %}

        <!-- Main Content -->
        <div class="flex-1 flex flex-col overflow-hidden">
            <!-- Top Navigation -->
            {% include 'partials/topnav.html' %}

            <!-- Content Area -->
            <main class="flex-1 overflow-y-auto p-6">
                <div class="mb-6 flex justify-between items-center">
                    <div>
                        <h1 class="text-2xl font-bold text-gray-800 dark:text-white">إعدادات قاعدة البيانات الخارجية</h1>
                        <p class="text-gray-600 dark:text-gray-400">إعداد الاتصال بقاعدة بيانات خارجية</p>
                    </div>
                    <div class="flex items-center gap-2">
                        <a href="{{ url_for('settings.index') }}" class="flex items-center gap-1 bg-gray-100 dark:bg-dark-100 hover:bg-gray-200 dark:hover:bg-dark-300 text-gray-700 dark:text-gray-300 py-2 px-4 rounded-lg transition-colors">
                            <i class="ri-arrow-right-line"></i>
                            <span>العودة للإعدادات</span>
                        </a>
                        <button @click="darkMode = !darkMode; localStorage.setItem('darkMode', darkMode ? 'enabled' : 'disabled'); document.documentElement.classList.toggle('dark')" class="p-2 rounded-full bg-gray-100 dark:bg-dark-100 hover:bg-gray-200 dark:hover:bg-dark-300 transition-colors">
                            <i class="ri-moon-line dark:hidden"></i>
                            <i class="ri-sun-line hidden dark:block"></i>
                        </button>
                    </div>
                </div>

                <div class="bg-white dark:bg-dark-100 rounded-xl shadow-sm overflow-hidden mb-6">
                    <div class="p-6">
                        <form id="externalDbForm" action="{{ url_for('settings.update_external_db') }}" method="POST">
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div>
                                    <h3 class="text-lg font-medium text-gray-800 dark:text-white mb-4 flex items-center">
                                        <i class="ri-database-2-line ml-2 text-primary-500"></i>
                                        إعدادات الاتصال
                                    </h3>
                                    
                                    <!-- تفعيل قاعدة البيانات الخارجية -->
                                    <div class="mb-4">
                                        <label class="flex items-center">
                                            <input type="checkbox" id="enable_external_db" name="enable_external_db" {% if settings.database.enable_external_db %}checked{% endif %} class="w-4 h-4 text-primary-600 border-gray-300 rounded focus:ring-primary-500 dark:bg-dark-200 dark:border-gray-600">
                                            <span class="mr-2 text-sm text-gray-700 dark:text-gray-300">تفعيل قاعدة البيانات الخارجية</span>
                                        </label>
                                        <p class="text-xs text-gray-500 dark:text-gray-400 mt-1 mr-6">تفعيل الاتصال بقاعدة بيانات خارجية بدلاً من قاعدة البيانات المحلية</p>
                                    </div>
                                    
                                    <!-- نوع قاعدة البيانات -->
                                    <div class="mb-4">
                                        <label for="db_type" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">نوع قاعدة البيانات</label>
                                        <select id="db_type" name="db_type" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 dark:bg-dark-200 dark:text-gray-100">
                                            <option value="mysql" {% if settings.database.db_type == 'mysql' %}selected{% endif %}>MySQL</option>
                                            <option value="postgresql" {% if settings.database.db_type == 'postgresql' %}selected{% endif %}>PostgreSQL</option>
                                            <option value="mssql" {% if settings.database.db_type == 'mssql' %}selected{% endif %}>Microsoft SQL Server</option>
                                        </select>
                                    </div>
                                    
                                    <!-- اسم المضيف -->
                                    <div class="mb-4">
                                        <label for="db_host" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">اسم المضيف</label>
                                        <input type="text" id="db_host" name="db_host" value="{{ settings.database.db_host }}" placeholder="localhost" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 dark:bg-dark-200 dark:text-gray-100">
                                    </div>
                                    
                                    <!-- رقم المنفذ -->
                                    <div class="mb-4">
                                        <label for="db_port" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">رقم المنفذ</label>
                                        <input type="text" id="db_port" name="db_port" value="{{ settings.database.db_port }}" placeholder="3306" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 dark:bg-dark-200 dark:text-gray-100">
                                    </div>
                                    
                                    <!-- اسم قاعدة البيانات -->
                                    <div class="mb-4">
                                        <label for="db_name" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">اسم قاعدة البيانات</label>
                                        <input type="text" id="db_name" name="db_name" value="{{ settings.database.db_name }}" placeholder="nobara" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 dark:bg-dark-200 dark:text-gray-100">
                                    </div>
                                    
                                    <!-- اسم المستخدم -->
                                    <div class="mb-4">
                                        <label for="db_user" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">اسم المستخدم</label>
                                        <input type="text" id="db_user" name="db_user" value="{{ settings.database.db_user }}" placeholder="root" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 dark:bg-dark-200 dark:text-gray-100">
                                    </div>
                                    
                                    <!-- كلمة المرور -->
                                    <div class="mb-4">
                                        <label for="db_password" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">كلمة المرور</label>
                                        <input type="password" id="db_password" name="db_password" value="{{ settings.database.db_password }}" placeholder="••••••••" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 dark:bg-dark-200 dark:text-gray-100">
                                    </div>
                                </div>
                                
                                <div>
                                    <h3 class="text-lg font-medium text-gray-800 dark:text-white mb-4 flex items-center">
                                        <i class="ri-settings-3-line ml-2 text-primary-500"></i>
                                        إعدادات متقدمة
                                    </h3>
                                    
                                    <!-- بادئة الجداول -->
                                    <div class="mb-4">
                                        <label for="table_prefix" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">بادئة الجداول</label>
                                        <input type="text" id="table_prefix" name="table_prefix" value="{{ settings.database.table_prefix }}" placeholder="nb_" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 dark:bg-dark-200 dark:text-gray-100">
                                        <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">بادئة لأسماء الجداول في قاعدة البيانات (اختياري)</p>
                                    </div>
                                    
                                    <!-- الاتصال الآمن (SSL) -->
                                    <div class="mb-4">
                                        <label class="flex items-center">
                                            <input type="checkbox" id="use_ssl" name="use_ssl" {% if settings.database.use_ssl %}checked{% endif %} class="w-4 h-4 text-primary-600 border-gray-300 rounded focus:ring-primary-500 dark:bg-dark-200 dark:border-gray-600">
                                            <span class="mr-2 text-sm text-gray-700 dark:text-gray-300">استخدام اتصال آمن (SSL)</span>
                                        </label>
                                    </div>
                                    
                                    <!-- مزامنة البيانات -->
                                    <div class="mb-4">
                                        <label class="flex items-center">
                                            <input type="checkbox" id="sync_data" name="sync_data" {% if settings.database.sync_data %}checked{% endif %} class="w-4 h-4 text-primary-600 border-gray-300 rounded focus:ring-primary-500 dark:bg-dark-200 dark:border-gray-600">
                                            <span class="mr-2 text-sm text-gray-700 dark:text-gray-300">مزامنة البيانات مع قاعدة البيانات المحلية</span>
                                        </label>
                                        <p class="text-xs text-gray-500 dark:text-gray-400 mt-1 mr-6">الاحتفاظ بنسخة محلية من البيانات للعمل في وضع عدم الاتصال</p>
                                    </div>
                                    
                                    <!-- فترة المزامنة -->
                                    <div class="mb-4">
                                        <label for="sync_interval" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">فترة المزامنة (دقائق)</label>
                                        <input type="number" id="sync_interval" name="sync_interval" value="{{ settings.database.sync_interval }}" min="5" max="1440" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 dark:bg-dark-200 dark:text-gray-100">
                                    </div>
                                    
                                    <!-- اختبار الاتصال -->
                                    <div class="mt-6">
                                        <button type="button" id="test_connection" class="bg-green-500 hover:bg-green-600 text-white py-2 px-4 rounded-lg inline-flex items-center transition-colors">
                                            <i class="ri-link-m ml-1"></i>
                                            <span>اختبار الاتصال</span>
                                        </button>
                                        
                                        <div id="connection_status" class="mt-2 hidden">
                                            <div id="connection_success" class="text-green-500 dark:text-green-400 hidden">
                                                <i class="ri-checkbox-circle-line"></i>
                                                <span>تم الاتصال بنجاح</span>
                                            </div>
                                            <div id="connection_error" class="text-red-500 dark:text-red-400 hidden">
                                                <i class="ri-close-circle-line"></i>
                                                <span id="error_message">فشل الاتصال</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="md:col-span-2 flex justify-end mt-4">
                                    <button type="submit" class="bg-primary-500 hover:bg-primary-600 text-white py-2 px-6 rounded-lg inline-flex items-center transition-colors">
                                        <i class="ri-save-line ml-1"></i>
                                        <span>حفظ الإعدادات</span>
                                    </button>
                                </div>
                            </div>
                            
                            <input type="hidden" name="section" value="database">
                        </form>
                    </div>
                </div>
                
                <!-- معلومات قاعدة البيانات -->
                <div class="bg-white dark:bg-dark-100 rounded-xl shadow-sm overflow-hidden">
                    <div class="p-6">
                        <h3 class="text-lg font-medium text-gray-800 dark:text-white mb-4 flex items-center">
                            <i class="ri-information-line ml-2 text-primary-500"></i>
                            معلومات قاعدة البيانات
                        </h3>
                        
                        <div class="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4 border border-blue-100 dark:border-blue-900/30">
                            <p class="text-gray-700 dark:text-gray-300 mb-2">
                                <strong>قاعدة البيانات الخارجية:</strong> تتيح لك الاتصال بقاعدة بيانات مركزية يمكن الوصول إليها من عدة أجهزة، مما يسمح بمزامنة البيانات بين نقاط البيع المختلفة.
                            </p>
                            <p class="text-gray-700 dark:text-gray-300 mb-2">
                                <strong>المزامنة:</strong> عند تفعيل خيار المزامنة، سيحتفظ النظام بنسخة محلية من البيانات للعمل في حالة انقطاع الاتصال بقاعدة البيانات الخارجية.
                            </p>
                            <p class="text-gray-700 dark:text-gray-300">
                                <strong>ملاحظة:</strong> يجب التأكد من إعداد قاعدة البيانات الخارجية بشكل صحيح وإنشاء المستخدم بالصلاحيات المناسبة قبل تفعيل هذه الميزة.
                            </p>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>
    
    <!-- Powered By -->
    <div class="powered-by">
        <div class="w-8 h-8 rounded-full bg-primary-50 dark:bg-primary-900/50 flex items-center justify-center ml-2 text-primary-600 dark:text-primary-400">
            <i class="ri-code-s-slash-line"></i>
        </div>
        <div>
            <div class="text-xs text-gray-600 dark:text-gray-400">
                Powered By <span class="font-bold text-primary-600 dark:text-primary-400">ENG/ Fouad Saber</span>
            </div>
            <div class="text-xs text-gray-500 dark:text-gray-500">Tel: 01020073527</div>
        </div>
    </div>
    
    <script>
        // تفعيل وضع الظلام إذا كان مخزنًا
        if (localStorage.getItem('darkMode') === 'enabled') {
            document.documentElement.classList.add('dark');
        }
        
        document.addEventListener('DOMContentLoaded', function() {
            // اختبار الاتصال
            document.getElementById('test_connection').addEventListener('click', function() {
                // إظهار حالة الاتصال
                document.getElementById('connection_status').classList.remove('hidden');
                document.getElementById('connection_success').classList.add('hidden');
                document.getElementById('connection_error').classList.add('hidden');
                
                // الحصول على بيانات الاتصال
                const dbType = document.getElementById('db_type').value;
                const dbHost = document.getElementById('db_host').value;
                const dbPort = document.getElementById('db_port').value;
                const dbName = document.getElementById('db_name').value;
                const dbUser = document.getElementById('db_user').value;
                const dbPassword = document.getElementById('db_password').value;
                
                // التحقق من البيانات المطلوبة
                if (!dbHost || !dbName || !dbUser) {
                    document.getElementById('connection_error').classList.remove('hidden');
                    document.getElementById('error_message').textContent = 'يرجى إدخال جميع البيانات المطلوبة';
                    return;
                }
                
                // إرسال طلب اختبار الاتصال
                fetch("{{ url_for('settings.test_db_connection') }}", {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        db_type: dbType,
                        db_host: dbHost,
                        db_port: dbPort,
                        db_name: dbName,
                        db_user: dbUser,
                        db_password: dbPassword
                    }),
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        document.getElementById('connection_success').classList.remove('hidden');
                    } else {
                        document.getElementById('connection_error').classList.remove('hidden');
                        document.getElementById('error_message').textContent = data.error || 'فشل الاتصال';
                    }
                })
                .catch(error => {
                    document.getElementById('connection_error').classList.remove('hidden');
                    document.getElementById('error_message').textContent = 'حدث خطأ أثناء الاتصال بالخادم';
                    console.error(error);
                });
            });
            
            // إرسال النموذج
            document.getElementById('externalDbForm').addEventListener('submit', function(e) {
                e.preventDefault();
                
                fetch(this.action, {
                    method: 'POST',
                    body: new FormData(this),
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        alert("تم حفظ إعدادات قاعدة البيانات بنجاح");
                        if (data.restart_required) {
                            if (confirm("يجب إعادة تشغيل التطبيق لتطبيق التغييرات. هل تريد إعادة التشغيل الآن؟")) {
                                window.location.href = "{{ url_for('auth.logout') }}";
                            }
                        }
                    } else {
                        alert("حدث خطأ أثناء حفظ الإعدادات: " + data.error);
                    }
                })
                .catch(error => {
                    alert("حدث خطأ أثناء الاتصال بالخادم");
                    console.error(error);
                });
            });
        });
    </script>
</body>
</html>
