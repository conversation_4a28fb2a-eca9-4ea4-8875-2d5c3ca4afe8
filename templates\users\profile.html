<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>الملف الشخصي - نظام إدارة المبيعات</title>
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <!-- Remixicon -->
    <link href="https://cdn.jsdelivr.net/npm/remixicon@2.5.0/fonts/remixicon.css" rel="stylesheet">
    <!-- Tippy.js for tooltips -->
    <script src="https://unpkg.com/@popperjs/core@2"></script>
    <script src="https://unpkg.com/tippy.js@6"></script>
    <link rel="stylesheet" href="https://unpkg.com/tippy.js@6/animations/scale.css" />
    <style>
        .bg-pattern {
            background-color: #f9fafb;
            background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='40' height='40' viewBox='0 0 40 40'%3E%3Cg fill-rule='evenodd'%3E%3Cg fill='%23e5e7eb' fill-opacity='0.4'%3E%3Cpath d='M0 38.59l2.83-2.83 1.41 1.41L1.41 40H0v-1.41zM0 1.4l2.83 2.83 1.41-1.41L1.41 0H0v1.41zM38.59 40l-2.83-2.83 1.41-1.41L40 38.59V40h-1.41zM40 1.41l-2.83 2.83-1.41-1.41L38.59 0H40v1.41zM20 18.6l2.83-2.83 1.41 1.41L21.41 20l2.83 2.83-1.41 1.41L20 21.41l-2.83 2.83-1.41-1.41L18.59 20l-2.83-2.83 1.41-1.41L20 18.59z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
        }
        .rtl {
            direction: rtl;
        }
        .glass-effect {
            background-color: rgba(255, 255, 255, 0.8);
            backdrop-filter: blur(10px);
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
        }

        /* تحسينات المظهر العام */
        .hover-card {
            transition: all 0.3s ease;
        }
        .hover-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 15px rgba(0, 0, 0, 0.1);
        }

        /* تنسيق الشروحات التوضيحية */
        .tippy-box {
            background-color: #334155;
            color: white;
            border-radius: 8px;
            font-size: 14px;
            padding: 5px 10px;
            text-align: right;
            direction: rtl;
        }
        .tippy-arrow {
            color: #334155;
        }

        /* تأثيرات إضافية */
        .pulse-animation {
            animation: pulse 2s infinite;
        }
        @keyframes pulse {
            0% {
                box-shadow: 0 0 0 0 rgba(59, 130, 246, 0.4);
            }
            70% {
                box-shadow: 0 0 0 10px rgba(59, 130, 246, 0);
            }
            100% {
                box-shadow: 0 0 0 0 rgba(59, 130, 246, 0);
            }
        }
    </style>
</head>
<body class="bg-pattern">
    <div class="flex h-screen overflow-hidden">
        <!-- Sidebar -->
        {% include 'partials/sidebar.html' %}

        <!-- Main Content -->
        <div class="flex-1 flex flex-col overflow-hidden">
            <!-- Top Navigation -->
            {% include 'partials/topnav.html' %}

            <!-- Content Area -->
            <main class="flex-1 overflow-y-auto p-6">
                <div class="mb-6">
                    <h1 class="text-2xl font-bold text-gray-800">الملف الشخصي</h1>
                    <p class="text-gray-600">عرض وتعديل بيانات الملف الشخصي</p>
                </div>

                <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
                    <!-- Profile Info -->
                    <div class="lg:col-span-1">
                        <div class="glass-effect rounded-lg overflow-hidden mb-6">
                            <div class="p-6 flex flex-col items-center">
                                <div class="relative group" data-tippy-content="يمكنك تغيير صورتك الشخصية بالضغط عليها واختيار صورة جديدة. يفضل استخدام صورة مربعة بدقة عالية.">
                                    <div class="w-32 h-32 rounded-full overflow-hidden mb-4 bg-primary hover-card">
                                        <img id="profile-image" src="https://ui-avatars.com/api/?name={{ current_user.full_name }}&background=3b82f6&color=fff&size=128" alt="صورة المستخدم" class="w-full h-full object-cover">
                                    </div>
                                    <div class="absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                                        <label for="profile-image-upload" class="w-full h-full flex items-center justify-center cursor-pointer bg-black bg-opacity-50 rounded-full">
                                            <span class="text-white text-sm font-medium">تغيير الصورة</span>
                                        </label>
                                        <input type="file" id="profile-image-upload" class="hidden" accept="image/*">
                                    </div>
                                </div>
                                <h2 class="text-xl font-bold text-gray-800">{{ current_user.full_name }}</h2>
                                <p class="text-gray-500 mb-2">{{ current_user.email }}</p>
                                <div class="px-3 py-1 bg-blue-100 text-blue-800 rounded-full text-xs font-medium">
                                    {% if current_user.role == 'admin' %}
                                        مدير النظام
                                    {% else %}
                                        موظف
                                    {% endif %}
                                </div>
                                <div class="mt-2 flex space-x-2 space-x-reverse">
                                    <span class="px-2 py-1 bg-green-100 text-green-800 rounded-full text-xs font-medium">نشط</span>
                                    <span class="px-2 py-1 bg-purple-100 text-purple-800 rounded-full text-xs font-medium">متصل</span>
                                </div>

                                <div class="w-full mt-6 space-y-4">
                                    <div class="flex justify-between items-center">
                                        <span class="text-gray-600">اسم المستخدم:</span>
                                        <span class="font-medium">{{ current_user.username }}</span>
                                    </div>
                                    <div class="flex justify-between items-center">
                                        <span class="text-gray-600">تاريخ الانضمام:</span>
                                        <span class="font-medium">{{ current_user.created_at.strftime('%Y-%m-%d') }}</span>
                                    </div>
                                    <div class="flex justify-between items-center">
                                        <span class="text-gray-600">آخر تسجيل دخول:</span>
                                        <span class="font-medium">{{ current_user.last_login.strftime('%Y-%m-%d %H:%M') if current_user.last_login else 'غير متوفر' }}</span>
                                    </div>
                                    <div class="flex justify-between items-center">
                                        <span class="text-gray-600">عدد المبيعات:</span>
                                        <span class="font-medium">65 طلب</span>
                                    </div>
                                    <div class="flex justify-between items-center">
                                        <span class="text-gray-600">عدد المشتريات:</span>
                                        <span class="font-medium">23 طلب</span>
                                    </div>
                                    <div class="flex justify-between items-center">
                                        <span class="text-gray-600">الحالة:</span>
                                        <span class="font-medium text-green-600">نشط</span>
                                    </div>
                                </div>

                                <div class="w-full mt-4 pt-4 border-t border-gray-200">
                                    <div class="flex justify-between items-center mb-2">
                                        <span class="text-gray-600 text-sm">مستوى الصلاحيات</span>
                                        <span class="text-xs px-2 py-1 bg-blue-100 text-blue-800 rounded-full">متقدم</span>
                                    </div>
                                    <div class="w-full bg-gray-200 rounded-full h-2">
                                        <div class="bg-blue-600 h-2 rounded-full" style="width: 85%"></div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Activity Stats -->
                        <div class="glass-effect rounded-lg overflow-hidden">
                            <div class="p-4 border-b border-gray-200 flex justify-between items-center">
                                <h3 class="font-medium text-gray-800">إحصائيات النشاط</h3>
                                <div class="text-xs px-2 py-1 bg-green-100 text-green-800 rounded-full">آخر 30 يوم</div>
                            </div>
                            <div class="p-4 space-y-4">
                                <div>
                                    <div class="flex justify-between mb-1">
                                        <span class="text-sm text-gray-600">المبيعات</span>
                                        <span class="text-sm font-medium">65 طلب</span>
                                    </div>
                                    <div class="w-full bg-gray-200 rounded-full h-2">
                                        <div class="bg-blue-600 h-2 rounded-full" style="width: 65%"></div>
                                    </div>
                                    <div class="mt-1 flex justify-between text-xs text-gray-500">
                                        <span>الشهر الماضي: 58</span>
                                        <span class="text-green-600">+12%</span>
                                    </div>
                                </div>

                                <div>
                                    <div class="flex justify-between mb-1">
                                        <span class="text-sm text-gray-600">المشتريات</span>
                                        <span class="text-sm font-medium">23 طلب</span>
                                    </div>
                                    <div class="w-full bg-gray-200 rounded-full h-2">
                                        <div class="bg-green-600 h-2 rounded-full" style="width: 23%"></div>
                                    </div>
                                    <div class="mt-1 flex justify-between text-xs text-gray-500">
                                        <span>الشهر الماضي: 20</span>
                                        <span class="text-green-600">+15%</span>
                                    </div>
                                </div>

                                <div>
                                    <div class="flex justify-between mb-1">
                                        <span class="text-sm text-gray-600">تعديلات المنتجات</span>
                                        <span class="text-sm font-medium">42 تعديل</span>
                                    </div>
                                    <div class="w-full bg-gray-200 rounded-full h-2">
                                        <div class="bg-purple-600 h-2 rounded-full" style="width: 42%"></div>
                                    </div>
                                    <div class="mt-1 flex justify-between text-xs text-gray-500">
                                        <span>الشهر الماضي: 38</span>
                                        <span class="text-green-600">+10%</span>
                                    </div>
                                </div>

                                <div>
                                    <div class="flex justify-between mb-1">
                                        <span class="text-sm text-gray-600">معدل النشاط اليومي</span>
                                        <span class="text-sm font-medium">4.3 ساعة</span>
                                    </div>
                                    <div class="w-full bg-gray-200 rounded-full h-2">
                                        <div class="bg-yellow-500 h-2 rounded-full" style="width: 72%"></div>
                                    </div>
                                    <div class="mt-1 flex justify-between text-xs text-gray-500">
                                        <span>الشهر الماضي: 3.8 ساعة</span>
                                        <span class="text-green-600">+13%</span>
                                    </div>
                                </div>
                            </div>
                            <div class="p-3 bg-gray-50 border-t border-gray-200 text-center">
                                <a href="#" class="text-sm text-primary font-medium hover:underline">عرض تقرير مفصل</a>
                            </div>
                        </div>
                    </div>

                    <!-- Edit Profile Form -->
                    <div class="lg:col-span-2">
                        <div class="glass-effect rounded-lg overflow-hidden">
                            <div class="p-4 border-b border-gray-200">
                                <h3 class="font-medium text-gray-800">تعديل الملف الشخصي</h3>
                            </div>
                            <div class="p-6">
                                <form method="POST" action="{{ url_for('users.profile') }}">
                                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                                        <div>
                                            <label for="full_name" class="block text-sm font-medium text-gray-700 mb-1">الاسم الكامل</label>
                                            <input type="text" id="full_name" name="full_name" value="{{ current_user.full_name }}" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent">
                                        </div>

                                        <div>
                                            <label for="email" class="block text-sm font-medium text-gray-700 mb-1">البريد الإلكتروني</label>
                                            <input type="email" id="email" name="email" value="{{ current_user.email }}" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent">
                                        </div>
                                    </div>

                                    <div class="border-t border-gray-200 pt-6 mb-6">
                                        <h4 class="text-lg font-medium text-gray-800 mb-4">تغيير كلمة المرور</h4>

                                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                            <div>
                                                <label for="current_password" class="block text-sm font-medium text-gray-700 mb-1">كلمة المرور الحالية</label>
                                                <input type="password" id="current_password" name="current_password" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent">
                                            </div>

                                            <div class="md:col-span-2 grid grid-cols-1 md:grid-cols-2 gap-6">
                                                <div>
                                                    <label for="new_password" class="block text-sm font-medium text-gray-700 mb-1">كلمة المرور الجديدة</label>
                                                    <input type="password" id="new_password" name="new_password" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent">
                                                </div>

                                                <div>
                                                    <label for="confirm_password" class="block text-sm font-medium text-gray-700 mb-1">تأكيد كلمة المرور</label>
                                                    <input type="password" id="confirm_password" name="confirm_password" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent">
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="border-t border-gray-200 pt-6">
                                        <h4 class="text-lg font-medium text-gray-800 mb-4">تفضيلات النظام</h4>

                                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                            <div class="space-y-4">
                                                <div class="flex items-center justify-between" data-tippy-content="يمكنك تفعيل الوضع الليلي لتقليل الإجهاد على العين أثناء العمل ليلاً. يغير هذا الخيار ألوان النظام إلى ألوان داكنة.">
                                                    <div class="flex items-center">
                                                        <input type="checkbox" id="dark_mode" name="dark_mode" class="h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded">
                                                        <label for="dark_mode" class="mr-2 block text-sm text-gray-700">تفعيل الوضع الليلي</label>
                                                    </div>
                                                    <span class="text-xs px-2 py-1 bg-blue-100 text-blue-800 rounded-full">جديد</span>
                                                </div>

                                                <div class="flex items-center" data-tippy-content="ستظهر لك إشعارات داخل النظام عند حدوث تغييرات مهمة مثل طلبات جديدة أو منتجات منخفضة المخزون.">
                                                    <input type="checkbox" id="notifications" name="notifications" checked class="h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded">
                                                    <label for="notifications" class="mr-2 block text-sm text-gray-700">تفعيل الإشعارات</label>
                                                </div>

                                                <div class="flex items-center">
                                                    <input type="checkbox" id="email_notifications" name="email_notifications" checked class="h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded">
                                                    <label for="email_notifications" class="mr-2 block text-sm text-gray-700">إشعارات البريد الإلكتروني</label>
                                                </div>

                                                <div class="flex items-center">
                                                    <input type="checkbox" id="sound_alerts" name="sound_alerts" checked class="h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded">
                                                    <label for="sound_alerts" class="mr-2 block text-sm text-gray-700">تنبيهات صوتية</label>
                                                </div>
                                            </div>

                                            <div class="space-y-4">
                                                <div class="flex items-center">
                                                    <input type="checkbox" id="auto_logout" name="auto_logout" class="h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded">
                                                    <label for="auto_logout" class="mr-2 block text-sm text-gray-700">تسجيل خروج تلقائي بعد فترة خمول</label>
                                                </div>

                                                <div class="flex items-center">
                                                    <input type="checkbox" id="two_factor" name="two_factor" class="h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded">
                                                    <label for="two_factor" class="mr-2 block text-sm text-gray-700">تفعيل المصادقة الثنائية</label>
                                                </div>

                                                <div class="flex items-center">
                                                    <input type="checkbox" id="activity_log" name="activity_log" checked class="h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded">
                                                    <label for="activity_log" class="mr-2 block text-sm text-gray-700">تسجيل نشاط الحساب</label>
                                                </div>

                                                <div class="flex items-center">
                                                    <input type="checkbox" id="compact_view" name="compact_view" class="h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded">
                                                    <label for="compact_view" class="mr-2 block text-sm text-gray-700">عرض مضغوط للقوائم</label>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="mt-6">
                                            <label for="language" class="block text-sm font-medium text-gray-700 mb-1">لغة النظام</label>
                                            <select id="language" name="language" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent">
                                                <option value="ar" selected>العربية</option>
                                                <option value="en">الإنجليزية</option>
                                                <option value="fr">الفرنسية</option>
                                            </select>
                                        </div>

                                        <div class="mt-4">
                                            <label for="timezone" class="block text-sm font-medium text-gray-700 mb-1">المنطقة الزمنية</label>
                                            <select id="timezone" name="timezone" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent">
                                                <option value="Africa/Cairo" selected>توقيت القاهرة (GMT+2)</option>
                                                <option value="Asia/Riyadh">توقيت الرياض (GMT+3)</option>
                                                <option value="Europe/London">توقيت غرينتش (GMT+0)</option>
                                            </select>
                                        </div>
                                    </div>

                                    <div class="mt-6 border-t border-gray-200 pt-6">
                                        <h4 class="text-lg font-medium text-gray-800 mb-4">خيارات متقدمة</h4>

                                        <div class="space-y-4">
                                            <div class="flex items-center justify-between" data-tippy-content="يمكنك تصدير جميع بيانات حسابك بما في ذلك سجل النشاط والإحصائيات والتفضيلات بتنسيق CSV أو JSON للاستخدام الشخصي أو النسخ الاحتياطي.">
                                                <div class="flex items-center">
                                                    <input type="checkbox" id="data_export" name="data_export" class="h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded">
                                                    <label for="data_export" class="mr-2 block text-sm text-gray-700">تصدير بيانات الحساب</label>
                                                </div>
                                                <a href="#" class="text-xs text-primary hover:underline">تصدير الآن</a>
                                            </div>

                                            <div class="flex items-center justify-between text-red-600" data-tippy-content="تحذير: هذا الإجراء سيؤدي إلى حذف حسابك نهائياً من النظام بما في ذلك جميع بياناتك وسجل نشاطك. لا يمكن التراجع عن هذه العملية بعد تنفيذها.">
                                                <div class="flex items-center">
                                                    <input type="checkbox" id="account_delete" name="account_delete" class="h-4 w-4 text-red-600 focus:ring-red-500 border-gray-300 rounded">
                                                    <label for="account_delete" class="mr-2 block text-sm">حذف الحساب نهائياً</label>
                                                </div>
                                                <span class="text-xs bg-red-100 text-red-800 px-2 py-1 rounded-full pulse-animation">تحذير</span>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="mt-6 flex justify-between">
                                        <button type="reset" class="px-6 py-2.5 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 transition-all" data-tippy-content="إعادة تعيين جميع التغييرات التي قمت بها في هذه الصفحة إلى القيم الأصلية.">
                                            <i class="ri-refresh-line ml-1"></i>
                                            إعادة تعيين
                                        </button>

                                        <button type="submit" class="px-6 py-2.5 bg-gradient-to-r from-blue-500 to-indigo-600 text-white rounded-md hover:from-blue-600 hover:to-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-all shadow-md hover:shadow-lg hover-card" data-tippy-content="حفظ جميع التغييرات التي قمت بها في ملفك الشخصي. سيتم تطبيق التغييرات فوراً.">
                                            <i class="ri-save-line ml-1"></i>
                                            حفظ التغييرات
                                        </button>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <script>
        // تفعيل الشروحات التوضيحية (tooltips)
        document.addEventListener('DOMContentLoaded', function() {
            tippy('[data-tippy-content]', {
                arrow: true,
                animation: 'scale',
                placement: 'top',
                theme: 'custom',
                maxWidth: 300,
                delay: [300, 100],
                touch: ['hold', 500],
                interactive: true
            });
        });

        // Form validation
        document.querySelector('form').addEventListener('submit', function(e) {
            const newPassword = document.getElementById('new_password').value;
            const confirmPassword = document.getElementById('confirm_password').value;
            const accountDelete = document.getElementById('account_delete');

            if (newPassword && newPassword !== confirmPassword) {
                e.preventDefault();
                alert('كلمات المرور غير متطابقة');
                return;
            }

            // تأكيد حذف الحساب
            if (accountDelete && accountDelete.checked) {
                if (!confirm('هل أنت متأكد من حذف حسابك نهائياً؟ لا يمكن التراجع عن هذه العملية.')) {
                    e.preventDefault();
                    accountDelete.checked = false;
                }
            }
        });

        // معاينة الصورة الشخصية
        const profileImageUpload = document.getElementById('profile-image-upload');
        const profileImage = document.getElementById('profile-image');

        if (profileImageUpload && profileImage) {
            profileImageUpload.addEventListener('change', function() {
                if (this.files && this.files[0]) {
                    const reader = new FileReader();

                    reader.onload = function(e) {
                        profileImage.src = e.target.result;
                        // عرض رسالة نجاح
                        const successMessage = document.createElement('div');
                        successMessage.className = 'fixed top-4 left-1/2 transform -translate-x-1/2 bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded z-50';
                        successMessage.innerHTML = 'تم تحديث الصورة الشخصية بنجاح. اضغط على حفظ التغييرات لتأكيد التغيير.';
                        document.body.appendChild(successMessage);

                        setTimeout(() => {
                            successMessage.remove();
                        }, 3000);
                    }

                    reader.readAsDataURL(this.files[0]);
                }
            });
        }

        // تفعيل الوضع الليلي
        const darkModeToggle = document.getElementById('dark_mode');

        if (darkModeToggle) {
            darkModeToggle.addEventListener('change', function() {
                if (this.checked) {
                    document.body.classList.add('dark-mode');
                    // يمكن إضافة المزيد من التغييرات لتفعيل الوضع الليلي
                } else {
                    document.body.classList.remove('dark-mode');
                }
            });
        }
    </script>
</body>
</html>
