
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة المشتريات</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdn.jsdelivr.net/npm/remixicon@3.5.0/fonts/remixicon.css" rel="stylesheet">
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Tajawal:wght@400;500;700&display=swap');
        body {
            font-family: 'Tajawal', sans-serif;
        }
        .bg-pattern {
            background-color: #f0f4f8;
            background-image: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23e1ebf5' fill-opacity='0.6'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
        }
        .text-primary {
            color: #3490dc;
        }
        .bg-primary {
            background-color: #3490dc;
        }
        .border-primary {
            border-color: #3490dc;
        }
    </style>
</head>
<body class="bg-pattern">
    <!-- Main Container -->
    <div class="flex h-screen overflow-hidden">

        <!-- Sidebar -->
        {% include 'partials/sidebar.html' %}

        <!-- Main Content -->
        <div class="flex-1 overflow-x-hidden overflow-y-auto">
            <!-- Top Navigation -->
            {% include 'partials/topnav.html' %}

            <!-- Content -->
            <main class="p-6">
                <div class="flex justify-between items-center mb-6">
                    <h1 class="text-2xl font-bold text-gray-800">إدارة المشتريات</h1>
                    <div class="flex gap-3">
                        <a href="{{ url_for('deferred_purchases.index') }}" class="bg-gradient-to-r from-yellow-500 to-yellow-700 text-white px-6 py-3 rounded-lg shadow-md hover:shadow-lg transition-all duration-300 flex items-center gap-2 whitespace-nowrap text-lg font-bold">
                            <div class="w-6 h-6 flex items-center justify-center">
                                <i class="ri-time-line"></i>
                            </div>
                            <span>المشتريات الآجلة</span>
                        </a>
                        <a href="{{ url_for('purchases.create') }}" class="bg-gradient-to-r from-green-500 to-green-700 text-white px-6 py-3 rounded-lg shadow-md hover:shadow-lg transition-all duration-300 flex items-center gap-2 pulse-animation whitespace-nowrap text-lg font-bold">
                            <div class="w-6 h-6 flex items-center justify-center">
                                <i class="ri-add-line"></i>
                            </div>
                            <span>إضافة مشتريات</span>
                        </a>
                    </div>
                </div>

                <!-- Search and Filters -->
                <div class="bg-white rounded-lg shadow-sm p-4 mb-6">
                    <h2 class="text-lg font-medium mb-4">فلترة وبحث</h2>

                    <form action="{{ url_for('purchases.index') }}" method="GET" class="space-y-4">
                        <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                            <div>
                                <label for="search" class="block text-sm font-medium text-gray-700 mb-1">بحث</label>
                                <input type="text" id="search" name="search" value="{{ search }}" placeholder="رقم الفاتورة..." class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent">
                            </div>

                            <div>
                                <label for="supplier" class="block text-sm font-medium text-gray-700 mb-1">المورد</label>
                                <select id="supplier" name="supplier" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent">
                                    <option value="">جميع الموردين</option>
                                    {% for supplier in suppliers %}
                                        <option value="{{ supplier.id }}" {% if supplier_id and supplier_id|int == supplier.id %}selected{% endif %}>{{ supplier.name }}</option>
                                    {% endfor %}
                                </select>
                            </div>

                            <div>
                                <label for="status" class="block text-sm font-medium text-gray-700 mb-1">الحالة</label>
                                <select id="status" name="status" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent">
                                    <option value="">جميع الحالات</option>
                                    <option value="pending" {% if status == 'pending' %}selected{% endif %}>قيد المعالجة</option>
                                    <option value="received" {% if status == 'received' %}selected{% endif %}>تم الاستلام</option>
                                </select>
                            </div>

                            <div class="flex space-x-2 mt-6">
                                <button type="submit" class="bg-primary text-white px-4 py-2 rounded-md hover:bg-blue-600">
                                    <i class="ri-filter-3-line ml-1"></i>
                                    <span>فلترة</span>
                                </button>
                                <a href="{{ url_for('purchases.index') }}" class="bg-gray-200 text-gray-700 px-4 py-2 rounded-md hover:bg-gray-300">
                                    <i class="ri-refresh-line ml-1"></i>
                                    <span>إعادة تعيين</span>
                                </a>
                            </div>
                        </div>
                    </form>
                </div>

                <!-- Statistics Cards -->
                <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
                    <div class="bg-white rounded-lg shadow-sm p-4">
                        <div class="flex items-center">
                            <div class="flex-shrink-0 bg-blue-100 rounded-full p-3">
                                <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01"></path>
                                </svg>
                            </div>
                            <div class="ml-4">
                                <h2 class="text-sm font-medium text-gray-500">إجمالي طلبات الشراء</h2>
                                <p class="text-lg font-semibold text-gray-800">{{ stats.total_purchases }}</p>
                            </div>
                        </div>
                    </div>

                    <div class="bg-white rounded-lg shadow-sm p-4">
                        <div class="flex items-center">
                            <div class="flex-shrink-0 bg-green-100 rounded-full p-3">
                                <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                            </div>
                            <div class="ml-4">
                                <h2 class="text-sm font-medium text-gray-500">طلبات تم استلامها</h2>
                                <p class="text-lg font-semibold text-gray-800">{{ stats.received_count }}</p>
                            </div>
                        </div>
                    </div>

                    <div class="bg-white rounded-lg shadow-sm p-4">
                        <div class="flex items-center">
                            <div class="flex-shrink-0 bg-yellow-100 rounded-full p-3">
                                <svg class="w-6 h-6 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                            </div>
                            <div class="ml-4">
                                <h2 class="text-sm font-medium text-gray-500">طلبات قيد المعالجة</h2>
                                <p class="text-lg font-semibold text-gray-800">{{ stats.pending_count }}</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Purchases Table -->
                <div class="bg-white rounded-lg shadow-sm overflow-hidden">
                    <div class="px-4 py-5 sm:px-6 border-b border-gray-200">
                        <h3 class="text-lg font-medium leading-6 text-gray-900">طلبات الشراء</h3>
                    </div>
                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">رقم الطلب</th>
                                    <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">المورد</th>
                                    <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">التاريخ</th>
                                    <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الإجمالي</th>
                                    <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الحالة</th>
                                    <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">إجراءات</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                {% for purchase in purchases.items %}
                                <tr class="hover:bg-gray-50">
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">{{ purchase.reference_number }}</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ purchase.supplier.name if purchase.supplier else 'غير محدد' }}</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ purchase.created_at.strftime('%Y-%m-%d') }}</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ purchase.total }} ج.م</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm">
                                        <span class="px-2 py-1 text-xs rounded-full {{ 'bg-green-100 text-green-800' if purchase.status == 'received' else 'bg-yellow-100 text-yellow-800' }}">
                                            {{ 'تم الاستلام' if purchase.status == 'received' else 'قيد المعالجة' }}
                                        </span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                        <div class="flex space-x-2 space-x-reverse">
                                            <a href="{{ url_for('purchases.details', id=purchase.id) }}" class="text-indigo-600 hover:text-indigo-900" title="عرض التفاصيل">
                                                <i class="ri-eye-line"></i>
                                            </a>
                                            <a href="{{ url_for('purchases.edit', id=purchase.id) }}" class="text-blue-600 hover:text-blue-900" title="تعديل">
                                                <i class="ri-edit-line"></i>
                                            </a>
                                            <button onclick="confirmDelete('{{ purchase.id }}')" class="text-red-600 hover:text-red-900" title="حذف">
                                                <i class="ri-delete-bin-line"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                {% else %}
                                <tr>
                                    <td colspan="6" class="px-6 py-4 text-center text-sm text-gray-500">لا توجد طلبات شراء</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    {% if purchases.pages > 1 %}
                    <div class="px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6">
                        <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                            <div>
                                <p class="text-sm text-gray-700">
                                    عرض
                                    <span class="font-medium">{{ purchases.items|length }}</span>
                                    من إجمالي
                                    <span class="font-medium">{{ purchases.total }}</span>
                                    عنصر
                                </p>
                            </div>
                            <div>
                                <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                                    {% if purchases.has_prev %}
                                    <a href="{{ url_for('purchases.index', page=purchases.prev_num, search=search, supplier=supplier_id, status=status, date_from=date_from, date_to=date_to) }}" class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                                        <i class="ri-arrow-right-s-line"></i>
                                        <span class="sr-only">Previous</span>
                                    </a>
                                    {% endif %}

                                    {% for page_num in purchases.iter_pages(left_edge=1, right_edge=1, left_current=2, right_current=2) %}
                                        {% if page_num %}
                                            {% if page_num != purchases.page %}
                                                <a href="{{ url_for('purchases.index', page=page_num, search=search, supplier=supplier_id, status=status, date_from=date_from, date_to=date_to) }}" class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700 hover:bg-gray-50">{{ page_num }}</a>
                                            {% else %}
                                                <span class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-primary text-sm font-medium text-white">{{ page_num }}</span>
                                            {% endif %}
                                        {% else %}
                                            <span class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700">...</span>
                                        {% endif %}
                                    {% endfor %}

                                    {% if purchases.has_next %}
                                    <a href="{{ url_for('purchases.index', page=purchases.next_num, search=search, supplier=supplier_id, status=status, date_from=date_from, date_to=date_to) }}" class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                                        <i class="ri-arrow-left-s-line"></i>
                                        <span class="sr-only">Next</span>
                                    </a>
                                    {% endif %}
                                </nav>
                            </div>
                        </div>
                    </div>
                    {% endif %}
                </div>
            </main>
        </div>
    </div>

    <!-- Delete Confirmation Modal -->
    <div id="deleteModal" class="fixed inset-0 flex items-center justify-center z-50 hidden">
        <div class="absolute inset-0 bg-black opacity-50"></div>
        <div class="bg-white rounded-lg p-6 z-10 max-w-md mx-auto">
            <h3 class="text-lg font-medium text-gray-900 mb-4">تأكيد الحذف</h3>
            <p class="text-sm text-gray-500 mb-4">هل أنت متأكد من حذف طلب الشراء هذا؟ لا يمكن التراجع عن هذا الإجراء.</p>
            <div class="flex justify-end space-x-2 space-x-reverse">
                <button id="cancelDelete" class="px-4 py-2 bg-gray-200 text-gray-800 rounded-md hover:bg-gray-300">إلغاء</button>
                <form id="deleteForm" method="POST" action="">
                    <button type="submit" class="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700">حذف</button>
                </form>
            </div>
        </div>
    </div>

    <script>
        function confirmDelete(id) {
            const modal = document.getElementById('deleteModal');
            const deleteForm = document.getElementById('deleteForm');
            const cancelBtn = document.getElementById('cancelDelete');

            modal.classList.remove('hidden');
            deleteForm.action = `/purchases/${id}/delete`;

            cancelBtn.addEventListener('click', function() {
                modal.classList.add('hidden');
            });
        }
    </script>
</body>
</html>
