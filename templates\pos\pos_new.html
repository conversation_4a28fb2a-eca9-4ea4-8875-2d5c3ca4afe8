<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    {% include 'partials/head.html' %}
    <!-- POS Theme CSS -->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/pos-theme.css') }}?v=1.0.0">
    <style>
        :where([class^="ri-"])::before { content: "\f3c2"; }

        /* تصميم عام محسن */
        :root {
            --primary: #2563eb;
            --primary-light: #3b82f6;
            --primary-dark: #1d4ed8;
            --secondary: #059669;
            --secondary-light: #10b981;
            --secondary-dark: #047857;
            --success: #16a34a;
            --success-light: #22c55e;
            --success-dark: #15803d;
            --danger: #dc2626;
            --danger-light: #ef4444;
            --danger-dark: #b91c1c;
            --warning: #d97706;
            --warning-light: #f59e0b;
            --warning-dark: #b45309;
            --info: #0284c7;
            --info-light: #0ea5e9;
            --info-dark: #0369a1;

            --primary-gradient: linear-gradient(135deg, var(--primary-light), var(--primary));
            --secondary-gradient: linear-gradient(135deg, var(--secondary-light), var(--secondary));
            --success-gradient: linear-gradient(135deg, var(--success-light), var(--success));
            --danger-gradient: linear-gradient(135deg, var(--danger-light), var(--danger));
            --warning-gradient: linear-gradient(135deg, var(--warning-light), var(--warning));
            --info-gradient: linear-gradient(135deg, var(--info-light), var(--info));

            --card-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
            --hover-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
            --button-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
            --button-hover-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);

            --border-radius-sm: 0.375rem;
            --border-radius: 0.5rem;
            --border-radius-md: 0.75rem;
            --border-radius-lg: 1rem;
            --border-radius-xl: 1.25rem;
            --border-radius-2xl: 1.5rem;
            --border-radius-full: 9999px;
        }

        /* إعدادات عامة */
        body {
            font-family: 'Cairo', 'Tajawal', sans-serif;
            background-color: #f8fafc;
            color: #1e293b;
        }

        .scrollbar-hide::-webkit-scrollbar {
            display: none;
        }

        .scrollbar-hide {
            -ms-overflow-style: none;
            scrollbar-width: none;
        }

        input[type="number"]::-webkit-inner-spin-button,
        input[type="number"]::-webkit-outer-spin-button {
            -webkit-appearance: none;
            margin: 0;
        }

        input[type="number"] {
            -moz-appearance: textfield;
        }

        /* تصميم الشبكة */
        .product-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(140px, 1fr));
            gap: 0.75rem;
            padding: 1rem;
        }

        /* تصميم التصنيفات */
        .category-btn {
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            border-radius: var(--border-radius-full);
            padding: 0.5rem 1rem;
            font-weight: 600;
            font-size: 0.875rem;
            box-shadow: var(--button-shadow);
            background-color: rgba(255, 255, 255, 0.15);
            backdrop-filter: blur(4px);
            color: white;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .category-btn:hover {
            background-color: rgba(255, 255, 255, 0.25);
            transform: translateY(-2px);
            box-shadow: var(--button-hover-shadow);
        }

        .category-active {
            background: var(--primary-gradient);
            color: white;
            box-shadow: var(--button-shadow);
            position: relative;
            overflow: hidden;
        }

        .category-active::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(to right, transparent, rgba(255, 255, 255, 0.2), transparent);
            transform: translateX(-100%);
            animation: shine 2s infinite;
        }

        @keyframes shine {
            100% {
                transform: translateX(100%);
            }
        }

        /* تصميم طرق الدفع */
        .payment-method-card {
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            border-radius: var(--border-radius-lg);
            overflow: hidden;
            position: relative;
            cursor: pointer;
            background: linear-gradient(to bottom, #ffffff, #f9fafb);
            border: 1px solid #e5e7eb;
        }

        .payment-method-card:hover {
            transform: translateY(-3px);
            box-shadow: var(--hover-shadow);
            border-color: var(--primary-light);
        }

        .payment-method-active {
            border-color: var(--primary);
            background-color: rgba(59, 130, 246, 0.05);
            box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.2);
        }

        /* تصميم الهيكل الرئيسي */
        .pos-container {
            display: flex;
            height: 100vh;
            overflow: hidden;
            background-color: #f8fafc;
        }

        .pos-sidebar {
            width: 4.5rem;
            background: var(--primary-gradient);
            height: 100%;
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 1rem 0;
            box-shadow: 4px 0 15px rgba(0, 0, 0, 0.05);
            z-index: 40;
            transition: width 0.3s ease;
        }

        .pos-main {
            flex: 1;
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }

        .pos-header {
            height: 4rem;
            background-color: white;
            border-bottom: 1px solid #e5e7eb;
            display: flex;
            align-items: center;
            padding: 0 1.5rem;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.05);
            z-index: 30;
        }

        .pos-content {
            flex: 1;
            display: flex;
            overflow: hidden;
        }
    </style>
</head>
<body>
    <div class="flex h-screen overflow-hidden bg-gray-50">
        <!-- Sidebar -->
        {% include 'partials/sidebar.html' %}

        <!-- Main Content -->
        <div class="flex-1 flex flex-col overflow-hidden">
            <!-- Top Navigation -->
            {% include 'partials/topnav.html' %}

            <!-- Notification Container -->
            <div class="notification-container" id="notification-container"></div>

            <!-- Content Area -->
            <main class="flex-1 overflow-hidden flex">
                <!-- Left Panel - Products -->
                <div class="w-2/3 flex flex-col bg-white border-l border-gray-200 shadow-md rounded-tr-2xl rounded-br-2xl overflow-hidden">
                    <!-- Search and Categories - تصميم محسن -->
                    <div class="p-4 border-b border-gray-200 bg-gradient-to-r from-blue-600 to-blue-800 text-white relative overflow-hidden">
                        <!-- نمط الخلفية -->
                        <div class="absolute inset-0 opacity-10" style="background-image: url('data:image/svg+xml,%3Csvg width=\'100\' height=\'100\' viewBox=\'0 0 100 100\' xmlns=\'http://www.w3.org/2000/svg\'%3E%3Cpath d=\'M11 18c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm48 25c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm-43-7c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm63 31c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM34 90c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm56-76c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM12 86c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm28-65c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm23-11c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-6 60c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm29 22c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zM32 63c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm57-13c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-9-21c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM60 91c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM35 41c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM12 60c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2z\' fill=\'%23ffffff\' fill-opacity=\'0.1\' fill-rule=\'evenodd\'/%3E%3C/svg%3E')"></div>

                        <div class="flex gap-3 mb-4">
                            <div class="relative flex-1 group">
                                <input type="text" id="product-search" placeholder="البحث عن منتجات بالاسم أو الباركود..." class="w-full px-4 py-2.5 pl-10 bg-white/10 rounded-xl border border-white/20 focus:ring-2 focus:ring-white/30 focus:border-transparent text-white text-sm shadow-md transition-all duration-300 hover:shadow-lg placeholder-white/70 backdrop-blur-sm">
                                <div class="absolute left-3 top-2.5 text-white/70 group-hover:text-white transition-colors duration-300">
                                    <div class="w-5 h-5 flex items-center justify-center">
                                        <i class="ri-search-line"></i>
                                    </div>
                                </div>
                            </div>
                            <div class="relative">
                                <select id="warehouse-select" class="px-4 py-2.5 bg-white/10 rounded-xl border border-white/20 focus:ring-2 focus:ring-white/30 focus:border-transparent text-white text-sm shadow-md transition-all duration-300 hover:shadow-lg appearance-none pl-9 pr-3 backdrop-blur-sm">
                                    {% for warehouse in warehouses %}
                                    <option value="{{ warehouse.id }}" {% if default_warehouse and default_warehouse.id == warehouse.id %}selected{% endif %}>{{ warehouse.name }}</option>
                                    {% endfor %}
                                </select>
                                <div class="absolute left-3 top-2.5 text-white/70 pointer-events-none">
                                    <div class="w-5 h-5 flex items-center justify-center">
                                        <i class="ri-store-2-line"></i>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="flex overflow-x-auto scrollbar-hide pb-1 -mx-1">
                            <button data-category="all" class="category-btn flex-shrink-0 px-4 py-2 mx-1 rounded-full bg-white/15 text-sm font-medium text-white hover:bg-white/25 transition-all duration-300 category-active backdrop-blur-sm shadow-md">
                                جميع المنتجات
                            </button>
                            {% for category in categories %}
                            <button data-category="{{ category.id }}" class="category-btn flex-shrink-0 px-4 py-2 mx-1 rounded-full bg-white/15 text-sm font-medium text-white hover:bg-white/25 transition-all duration-300 backdrop-blur-sm shadow-md">
                                {{ category.name }}
                            </button>
                            {% endfor %}
                        </div>
                    </div>

                    <!-- Products Grid -->
                    <div class="flex-1 overflow-y-auto p-3 bg-gradient-to-b from-gray-50 to-white">
                        <div id="products-container" class="product-grid">
                            <!-- Products will be loaded here via JS -->
                            <div class="flex items-center justify-center h-full col-span-full text-gray-400">
                                <div class="text-center">
                                    <div class="w-16 h-16 mx-auto rounded-full bg-blue-50 flex items-center justify-center mb-3">
                                        <i class="ri-shopping-bag-line ri-2x text-blue-400"></i>
                                    </div>
                                    <p class="text-sm">جاري تحميل المنتجات...</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Right Panel - Cart -->
                <div class="w-1/3 flex flex-col bg-white border-r border-gray-200 shadow-sm">
                    <!-- Cart Header - تصميم محسن -->
                    <div class="p-4 border-b border-gray-200 bg-gradient-to-r from-blue-600 to-blue-800 text-white relative overflow-hidden">
                        <!-- نمط الخلفية -->
                        <div class="absolute inset-0 opacity-10" style="background-image: url('data:image/svg+xml,%3Csvg width=\'100\' height=\'100\' viewBox=\'0 0 100 100\' xmlns=\'http://www.w3.org/2000/svg\'%3E%3Cpath d=\'M11 18c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm48 25c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm-43-7c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm63 31c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM34 90c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm56-76c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM12 86c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm28-65c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm23-11c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-6 60c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm29 22c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zM32 63c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm57-13c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-9-21c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM60 91c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM35 41c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM12 60c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2z\' fill=\'%23ffffff\' fill-opacity=\'0.1\' fill-rule=\'evenodd\'/%3E%3C/svg%3E')"></div>

                        <div class="flex justify-between items-center mb-4 relative z-10">
                            <div>
                                <h2 class="text-xl font-bold text-white flex items-center">
                                    <i class="ri-shopping-cart-2-line mr-2 text-white/80"></i>
                                    سلة المشتريات
                                </h2>
                                <p class="text-sm text-white/80 mt-1">رقم الفاتورة: <span id="cart-invoice" class="font-medium text-white bg-white/10 px-2 py-0.5 rounded-lg">{{ invoice_number }}</span></p>
                            </div>
                            <div class="flex space-x-2 space-x-reverse">
                                <button id="clear-cart-btn" class="p-2 text-white/80 hover:text-white hover:bg-white/10 rounded-full transition-all duration-300 hover:shadow-lg backdrop-blur-sm" title="مسح السلة">
                                    <div class="w-6 h-6 flex items-center justify-center">
                                        <i class="ri-delete-bin-line"></i>
                                    </div>
                                </button>
                                <button id="suspend-cart-btn" class="p-2 text-white/80 hover:text-white hover:bg-white/10 rounded-full transition-all duration-300 hover:shadow-lg backdrop-blur-sm" title="تعليق الفاتورة">
                                    <div class="w-6 h-6 flex items-center justify-center">
                                        <i class="ri-time-line"></i>
                                    </div>
                                </button>
                                <button id="suspended-invoices-btn" class="p-2 text-white/80 hover:text-white hover:bg-white/10 rounded-full transition-all duration-300 hover:shadow-lg backdrop-blur-sm relative" title="الفواتير المعلقة">
                                    <div class="w-6 h-6 flex items-center justify-center">
                                        <i class="ri-bill-line"></i>
                                    </div>
                                    <span id="suspended-count" class="absolute -top-1 -right-1 w-5 h-5 bg-red-500 text-white text-xs rounded-full flex items-center justify-center hidden shadow-lg">0</span>
                                </button>
                                <button id="return-invoice-btn" class="p-2 text-white/80 hover:text-white hover:bg-white/10 rounded-full transition-all duration-300 hover:shadow-lg backdrop-blur-sm" title="استرجاع فاتورة">
                                    <div class="w-6 h-6 flex items-center justify-center">
                                        <i class="ri-arrow-go-back-line"></i>
                                    </div>
                                </button>
                            </div>
                        </div>

                        <!-- Customer Selection - تصميم جديد -->
                        <div class="flex items-center justify-between mb-2">
                            <!-- معلومات العميل الحالي -->
                            <div id="current-customer-info" class="flex items-center bg-white/10 rounded-md px-2 py-1 backdrop-blur-sm shadow-sm">
                                <div class="w-5 h-5 rounded-full bg-white/20 flex items-center justify-center mr-1.5">
                                    <i class="ri-user-line text-white/90 text-[10px]"></i>
                                </div>
                                <div class="text-white text-xs font-medium" id="current-customer-name">عميل نقدي</div>
                            </div>

                            <!-- زر البحث عن عميل -->
                            <button id="open-customer-modal" class="bg-white/20 text-white rounded-md px-2 py-1 hover:bg-white/30 transition-all duration-300 shadow-sm hover:shadow-md flex items-center">
                                <i class="ri-user-search-line text-xs mr-1"></i>
                                <span class="text-xs">العملاء</span>
                            </button>

                            <!-- قائمة العملاء المخفية -->
                            <select id="customer-select" class="hidden">
                                <option value="">عميل نقدي</option>
                                {% for customer in customers %}
                                <option value="{{ customer.id }}">{{ customer.name }}</option>
                                {% endfor %}
                            </select>
                        </div>
                    </div>

                    <!-- Cart Items -->
                    <div id="cart-items" class="flex-1 overflow-y-auto p-4">
                        <!-- Cart items will be loaded here via JS -->
                        <div class="flex flex-col items-center justify-center h-full text-gray-400">
                            <div class="w-16 h-16 rounded-full bg-gray-100 flex items-center justify-center mb-2">
                                <i class="ri-shopping-cart-line ri-2x"></i>
                            </div>
                            <p class="text-sm">سلة المشتريات فارغة</p>
                            <p class="text-xs mt-1">قم بإضافة منتجات لبدء البيع</p>
                        </div>
                    </div>

                    <!-- Cart Footer - تصميم محسن -->
                    <div class="p-5 border-t border-gray-200 bg-gradient-to-b from-gray-50 to-blue-50 relative">
                        <!-- نمط الخلفية -->
                        <div class="absolute inset-0 opacity-30" style="background-image: url('data:image/svg+xml,%3Csvg width=\'100\' height=\'100\' viewBox=\'0 0 100 100\' xmlns=\'http://www.w3.org/2000/svg\'%3E%3Cpath d=\'M11 18c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm48 25c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm-43-7c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm63 31c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM34 90c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm56-76c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM12 86c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm28-65c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm23-11c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-6 60c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm29 22c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zM32 63c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm57-13c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-9-21c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM60 91c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM35 41c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM12 60c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2z\' fill=\'%233b82f6\' fill-opacity=\'0.05\' fill-rule=\'evenodd\'/%3E%3C/svg%3E')"></div>

                        <!-- Cart Summary - تصميم مصغر -->
                        <div class="mb-3 space-y-2 relative z-10">
                            <div class="flex justify-between items-center bg-white p-2 rounded-lg shadow-sm border border-gray-100 hover:border-blue-100 transition-all duration-300">
                                <span class="text-xs font-medium text-gray-600 flex items-center">
                                    <i class="ri-money-dollar-circle-line mr-1 text-blue-500 text-xs"></i>
                                    المجموع:
                                </span>
                                <span id="cart-subtotal" class="font-bold text-gray-800 text-xs">0.00 ج.م</span>
                            </div>
                            <div class="flex justify-between items-center bg-white p-2 rounded-lg shadow-sm border border-gray-100 hover:border-red-100 transition-all duration-300">
                                <div class="flex items-center">
                                    <i class="ri-coupon-2-line mr-1 text-red-500 text-xs"></i>
                                    <span class="text-xs font-medium text-gray-600 ml-1">الخصم:</span>
                                    <div class="relative">
                                        <input type="number" id="discount-value" class="w-14 px-2 py-1 border border-gray-300 rounded-lg text-xs shadow-inner focus:ring-1 focus:ring-blue-300 focus:border-blue-300 transition-all duration-300" value="0" min="0">
                                        <select id="discount-type" class="absolute left-0 top-0 h-full border-l border-gray-300 bg-gray-100 rounded-r-lg text-[10px] px-1 shadow-inner focus:outline-none">
                                            <option value="fixed">ج.م</option>
                                            <option value="percentage">%</option>
                                        </select>
                                    </div>
                                </div>
                                <span id="cart-discount" class="font-medium text-red-500 text-xs">- 0.00 ج.م</span>
                            </div>
                            <div class="flex justify-between items-center bg-white p-2 rounded-lg shadow-sm border border-gray-100 hover:border-green-100 transition-all duration-300">
                                <div class="flex items-center">
                                    <i class="ri-percent-line mr-1 text-green-500 text-xs"></i>
                                    <span class="text-xs font-medium text-gray-600 ml-1">الضريبة:</span>
                                    <input type="number" id="tax-percentage" class="w-10 px-2 py-1 border border-gray-300 rounded-lg text-xs shadow-inner focus:ring-1 focus:ring-blue-300 focus:border-blue-300 transition-all duration-300" value="14" min="0" max="100">
                                    <span class="text-xs text-gray-600 mr-0.5">%</span>
                                </div>
                                <span id="cart-tax" class="font-medium text-green-600 text-xs">+ 0.00 ج.م</span>
                            </div>
                            <div class="p-2 bg-gradient-to-r from-blue-600 to-blue-700 text-white rounded-lg flex justify-between items-center shadow-md relative overflow-hidden">
                                <span class="absolute inset-0 w-full h-full bg-blue-600 opacity-0 hover:opacity-100 transition-opacity duration-300"></span>
                                <span class="absolute inset-0 w-0 bg-white opacity-10 hover:w-full transition-all duration-700 ease-out"></span>
                                <span class="text-xs font-bold relative z-10 flex items-center">
                                    <i class="ri-money-dollar-circle-line mr-1 text-white/80 text-xs"></i>
                                    الإجمالي:
                                </span>
                                <span id="cart-total" class="text-sm font-bold relative z-10">0.00 ج.م</span>
                            </div>
                        </div>

                        <!-- Checkout Button - تصميم مصغر -->
                        <button id="checkout-btn" class="w-full py-2 bg-gradient-to-r from-green-500 to-green-600 text-white rounded-lg font-bold hover:from-green-600 hover:to-green-700 transition-all duration-300 flex items-center justify-center disabled:opacity-50 disabled:cursor-not-allowed shadow-md relative overflow-hidden group disabled:shadow-none transform hover:scale-[1.02] active:scale-[0.98]" disabled>
                            <span class="absolute inset-0 w-full h-full bg-gradient-to-r from-green-600 to-green-700 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></span>
                            <span class="absolute inset-0 w-0 bg-white opacity-10 group-hover:w-full transition-all duration-700 ease-out"></span>
                            <span class="relative flex items-center">
                                <i class="ri-shopping-cart-fill mr-1.5 text-base"></i>
                                <span class="text-sm">إتمام البيع</span>
                            </span>
                        </button>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <!-- Modals -->

    <!-- Checkout Modal - تصميم محسن -->
    <div id="checkoutModal" class="fixed inset-0 bg-black bg-opacity-70 z-50 flex items-center justify-center hidden backdrop-blur-sm">
        <div id="checkout-modal-content" class="bg-white rounded-2xl shadow-2xl w-full max-w-md transform scale-95 opacity-0 transition-all duration-300 mx-2 max-h-[90vh] overflow-y-auto border border-gray-200">
            <div class="p-4 border-b border-gray-200 flex justify-between items-center bg-gradient-to-r from-blue-50 to-white sticky top-0 z-10 rounded-t-2xl">
                <h3 class="text-lg font-bold text-gray-800 flex items-center">
                    <i class="ri-shopping-cart-2-line mr-2 text-primary text-xl"></i>
                    إتمام البيع
                </h3>
                <button id="close-checkout-modal" class="text-gray-500 hover:text-white hover:bg-red-500 transition-all duration-300 w-8 h-8 rounded-full flex items-center justify-center shadow-sm">
                    <i class="ri-close-line text-lg"></i>
                </button>
            </div>
            <div class="p-3 sm:p-4">
                <div id="confirm-customer-container" class="mb-3 p-2 sm:p-3 bg-blue-50 rounded-lg border border-blue-100 shadow-sm">
                    <div class="flex items-center">
                        <div class="w-8 h-8 sm:w-10 sm:h-10 rounded-full bg-blue-100 flex items-center justify-center text-blue-500 mr-2 shadow-sm">
                            <i class="ri-user-line text-base"></i>
                        </div>
                        <div class="flex-grow">
                            <p class="text-xs text-gray-500">العميل</p>
                            <p id="confirm-customer" class="font-bold text-gray-800 text-sm">عميل نقدي</p>
                        </div>
                        <button id="change-customer-btn" class="text-xs bg-blue-500 text-white px-2 py-1 rounded hover:bg-blue-600 transition-colors">
                            <i class="ri-user-search-line"></i>
                            تغيير
                        </button>
                    </div>
                </div>

                <!-- Customer Credit Info - Hidden by default -->
                <div id="customer-credit-info" class="mb-3 p-2 sm:p-3 bg-yellow-50 rounded-lg border border-yellow-100 shadow-sm hidden">
                    <div class="flex justify-between items-center mb-1">
                        <p class="text-xs font-bold text-gray-700">المستحقات السابقة</p>
                        <span id="customer-total-debt" class="text-sm font-bold text-red-500">0.00 ج.م</span>
                    </div>
                    <div class="text-xs text-gray-500 mb-2" id="customer-debt-details">
                        <!-- Will be populated dynamically -->
                    </div>
                    <div class="flex justify-between items-center" id="payment-options-container">
                        <div class="flex space-x-2 space-x-reverse">
                            <button id="pay-full-debt" class="text-xs bg-green-500 text-white px-2 py-1 rounded hover:bg-green-600 transition-colors">
                                <i class="ri-money-dollar-circle-line"></i>
                                سداد كامل
                            </button>
                            <button id="pay-partial-debt" class="text-xs bg-blue-500 text-white px-2 py-1 rounded hover:bg-blue-600 transition-colors">
                                <i class="ri-money-dollar-circle-line"></i>
                                سداد جزئي
                            </button>
                        </div>
                        <span class="text-xs text-gray-500">إجمالي المديونية مع الفاتورة الحالية: <span id="total-with-current" class="font-bold text-red-500">0.00 ج.م</span></span>
                    </div>
                </div>

                <div class="mb-3 p-2 sm:p-3 bg-gray-50 rounded-lg shadow-sm border border-gray-100">
                    <div class="flex justify-between mb-1 text-sm">
                        <span class="text-gray-600">المجموع:</span>
                        <span id="confirm-subtotal" class="font-medium">0.00 ج.م</span>
                    </div>
                    <div class="flex justify-between mb-1 text-sm">
                        <span class="text-gray-600">الخصم:</span>
                        <span id="confirm-discount" class="font-medium text-red-500">- 0.00 ج.م</span>
                    </div>
                    <div class="flex justify-between mb-1 text-sm">
                        <span class="text-gray-600">الضريبة:</span>
                        <span id="confirm-tax" class="font-medium text-green-600">+ 0.00 ج.م</span>
                    </div>
                    <div class="pt-2 border-t border-gray-200 flex justify-between">
                        <span class="font-bold">الإجمالي:</span>
                        <span id="confirm-total" class="font-bold text-primary">0.00 ج.م</span>
                    </div>
                </div>

                <div class="mb-4">
                    <label class="block text-sm font-bold text-gray-700 mb-3 flex items-center">
                        <i class="ri-bank-card-line mr-1.5 text-primary"></i>
                        طريقة الدفع
                    </label>
                    <div class="grid grid-cols-4 gap-3">
                        {% for method in payment_methods %}
                        <div class="payment-method-card border border-gray-200 rounded-xl p-3 cursor-pointer hover:border-{{ method.color }}-500 transition-all duration-300 {% if method.is_default %}payment-method-active{% endif %} shadow-md hover:shadow-lg transform hover:-translate-y-1" data-method="{{ method.code }}" data-is-credit="{{ method.is_credit|lower }}" data-requires-reference="{{ method.requires_reference|lower }}">
                            <div class="flex flex-col items-center">
                                <div class="w-10 h-10 rounded-full bg-gradient-to-br from-{{ method.color }}-400 to-{{ method.color }}-500 flex items-center justify-center text-white mb-2 shadow-md">
                                    <i class="{{ method.icon }} text-lg"></i>
                                </div>
                                <span class="text-xs font-bold">{{ method.name }}</span>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                </div>

                <!-- Visa Payment Details (Hidden by default) -->
                <div id="visa-payment-details" class="mb-3 p-3 bg-indigo-50 rounded-lg border border-indigo-100 hidden">
                    <div class="text-xs font-bold text-gray-700 mb-2">تفاصيل بطاقة الفيزا</div>
                    <div class="grid grid-cols-2 gap-2 mb-2">
                        <div>
                            <label for="card-number" class="block text-xs text-gray-600 mb-1">رقم البطاقة</label>
                            <input type="text" id="card-number" class="w-full text-sm border border-gray-300 rounded-lg p-2" placeholder="**** **** **** ****">
                        </div>
                        <div>
                            <label for="card-name" class="block text-xs text-gray-600 mb-1">اسم حامل البطاقة</label>
                            <input type="text" id="card-name" class="w-full text-sm border border-gray-300 rounded-lg p-2">
                        </div>
                    </div>
                    <div class="grid grid-cols-2 gap-2">
                        <div>
                            <label for="card-expiry" class="block text-xs text-gray-600 mb-1">تاريخ الانتهاء</label>
                            <input type="text" id="card-expiry" class="w-full text-sm border border-gray-300 rounded-lg p-2" placeholder="MM/YY">
                        </div>
                        <div>
                            <label for="card-cvv" class="block text-xs text-gray-600 mb-1">رمز الأمان</label>
                            <input type="text" id="card-cvv" class="w-full text-sm border border-gray-300 rounded-lg p-2" placeholder="CVV">
                        </div>
                    </div>
                </div>

                <div class="mb-3">
                    <div class="flex justify-between mb-2">
                        <label for="received-amount" class="block text-xs font-bold text-gray-700">المبلغ المستلم</label>
                        <div class="flex items-center">
                            <span class="text-xs text-gray-500 ml-1">المتبقي:</span>
                            <span id="change-amount" class="font-bold text-primary text-sm">0.00 ج.م</span>
                        </div>
                    </div>
                    <div class="flex mb-2">
                        <input type="number" id="received-amount" class="block w-full rounded-lg border-gray-300 focus:ring-primary focus:border-primary text-base font-bold text-center py-2 shadow-sm" placeholder="أدخل المبلغ المستلم">
                    </div>

                    <!-- Numpad - تصميم محسن -->
                    <div class="grid grid-cols-3 gap-3">
                        <button class="numpad-btn bg-white border border-gray-200 rounded-xl p-3 hover:bg-gray-50 hover:border-primary hover:shadow-md transition-all duration-300 text-lg font-bold shadow-sm transform hover:scale-105 active:scale-95" data-value="1">1</button>
                        <button class="numpad-btn bg-white border border-gray-200 rounded-xl p-3 hover:bg-gray-50 hover:border-primary hover:shadow-md transition-all duration-300 text-lg font-bold shadow-sm transform hover:scale-105 active:scale-95" data-value="2">2</button>
                        <button class="numpad-btn bg-white border border-gray-200 rounded-xl p-3 hover:bg-gray-50 hover:border-primary hover:shadow-md transition-all duration-300 text-lg font-bold shadow-sm transform hover:scale-105 active:scale-95" data-value="3">3</button>
                        <button class="numpad-btn bg-white border border-gray-200 rounded-xl p-3 hover:bg-gray-50 hover:border-primary hover:shadow-md transition-all duration-300 text-lg font-bold shadow-sm transform hover:scale-105 active:scale-95" data-value="4">4</button>
                        <button class="numpad-btn bg-white border border-gray-200 rounded-xl p-3 hover:bg-gray-50 hover:border-primary hover:shadow-md transition-all duration-300 text-lg font-bold shadow-sm transform hover:scale-105 active:scale-95" data-value="5">5</button>
                        <button class="numpad-btn bg-white border border-gray-200 rounded-xl p-3 hover:bg-gray-50 hover:border-primary hover:shadow-md transition-all duration-300 text-lg font-bold shadow-sm transform hover:scale-105 active:scale-95" data-value="6">6</button>
                        <button class="numpad-btn bg-white border border-gray-200 rounded-xl p-3 hover:bg-gray-50 hover:border-primary hover:shadow-md transition-all duration-300 text-lg font-bold shadow-sm transform hover:scale-105 active:scale-95" data-value="7">7</button>
                        <button class="numpad-btn bg-white border border-gray-200 rounded-xl p-3 hover:bg-gray-50 hover:border-primary hover:shadow-md transition-all duration-300 text-lg font-bold shadow-sm transform hover:scale-105 active:scale-95" data-value="8">8</button>
                        <button class="numpad-btn bg-white border border-gray-200 rounded-xl p-3 hover:bg-gray-50 hover:border-primary hover:shadow-md transition-all duration-300 text-lg font-bold shadow-sm transform hover:scale-105 active:scale-95" data-value="9">9</button>
                        <button class="numpad-btn bg-white border border-gray-200 rounded-xl p-3 hover:bg-gray-50 hover:border-primary hover:shadow-md transition-all duration-300 text-lg font-bold shadow-sm transform hover:scale-105 active:scale-95" data-value="00">00</button>
                        <button class="numpad-btn bg-white border border-gray-200 rounded-xl p-3 hover:bg-gray-50 hover:border-primary hover:shadow-md transition-all duration-300 text-lg font-bold shadow-sm transform hover:scale-105 active:scale-95" data-value="0">0</button>
                        <button class="numpad-btn bg-white border border-gray-200 rounded-xl p-3 hover:bg-red-50 hover:border-red-500 hover:text-white hover:bg-red-500 transition-all duration-300 text-lg font-bold shadow-sm transform hover:scale-105 active:scale-95 text-red-500" data-value="clear">
                            <i class="ri-delete-back-2-line"></i>
                        </button>
                    </div>
                </div>
            </div>
            <div class="p-4 border-t border-gray-200 flex justify-between bg-gradient-to-b from-gray-50 to-white rounded-b-xl sticky bottom-0 z-10 shadow-inner">
                <button id="cancel-checkout" class="px-4 py-2.5 border border-gray-300 rounded-xl text-gray-700 hover:bg-gray-100 transition-all duration-300 font-medium shadow-sm text-sm hover:border-gray-400 hover:shadow transform hover:scale-[1.02] active:scale-[0.98]">إلغاء</button>
                <button id="confirm-checkout" class="px-5 py-2.5 bg-gradient-to-r from-green-500 to-green-600 text-white rounded-xl hover:from-green-600 hover:to-green-700 transition-all duration-300 font-medium shadow-lg flex items-center text-sm relative overflow-hidden group transform hover:scale-[1.02] active:scale-[0.98]">
                    <span class="absolute inset-0 w-full h-full bg-gradient-to-r from-green-600 to-green-700 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></span>
                    <span class="absolute inset-0 w-0 bg-white opacity-10 group-hover:w-full transition-all duration-700 ease-out"></span>
                    <span class="relative flex items-center">
                        <i class="ri-check-line mr-1.5 text-lg"></i>
                        <span>إتمام البيع</span>
                    </span>
                </button>
            </div>
        </div>
    </div>

    <!-- Suspended Invoices Modal - تصميم محسن -->
    <div id="suspendedInvoicesModal" class="fixed inset-0 bg-black bg-opacity-70 z-50 flex items-center justify-center hidden backdrop-blur-sm">
        <div id="suspended-modal-content" class="bg-white rounded-2xl shadow-2xl w-full max-w-md transform scale-95 opacity-0 transition-all duration-300 mx-2 max-h-[90vh] overflow-hidden border border-gray-200">
            <div class="p-4 border-b border-gray-200 flex justify-between items-center bg-gradient-to-r from-yellow-50 to-white sticky top-0 z-10 rounded-t-2xl">
                <h3 class="text-lg font-bold text-gray-800 flex items-center">
                    <i class="ri-time-line mr-2 text-yellow-500 text-xl"></i>
                    الفواتير المعلقة
                </h3>
                <button id="close-suspended-modal" class="text-gray-500 hover:text-white hover:bg-red-500 transition-all duration-300 w-8 h-8 rounded-full flex items-center justify-center shadow-sm">
                    <i class="ri-close-line text-lg"></i>
                </button>
            </div>
            <div id="suspended-invoices-list" class="max-h-[60vh] overflow-y-auto p-4">
                <!-- الفواتير المعلقة ستظهر هنا -->
                <div class="flex flex-col items-center justify-center py-8 text-gray-400">
                    <div class="w-20 h-20 rounded-full bg-gradient-to-br from-yellow-100 to-yellow-50 flex items-center justify-center mb-4 shadow-md">
                        <i class="ri-time-line text-3xl text-yellow-400"></i>
                    </div>
                    <p class="text-base font-medium text-gray-500">لا توجد فواتير معلقة</p>
                    <p class="text-sm mt-2 text-gray-400 text-center max-w-xs">يمكنك تعليق الفواتير والعودة إليها لاحقاً عن طريق الضغط على زر تعليق الفاتورة</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Return Invoice Modal - تصميم محسن -->
    <div id="returnInvoiceModal" class="fixed inset-0 bg-black bg-opacity-70 z-50 flex items-center justify-center hidden backdrop-blur-sm">
        <div id="return-modal-content" class="bg-white rounded-2xl shadow-2xl w-full max-w-2xl transform scale-95 opacity-0 transition-all duration-300 mx-2 max-h-[90vh] overflow-hidden border border-gray-200">
            <div class="p-4 border-b border-gray-200 flex justify-between items-center bg-gradient-to-r from-blue-50 to-white sticky top-0 z-10 rounded-t-2xl">
                <h3 class="text-lg font-bold text-gray-800 flex items-center">
                    <i class="ri-arrow-go-back-line mr-2 text-blue-500 text-xl"></i>
                    مرتجعات المبيعات
                </h3>
                <button id="close-return-modal" class="text-gray-500 hover:text-white hover:bg-red-500 transition-all duration-300 w-8 h-8 rounded-full flex items-center justify-center shadow-sm">
                    <i class="ri-close-line text-lg"></i>
                </button>
            </div>

            <!-- Tabs - تصميم محسن -->
            <div class="flex border-b border-gray-200 px-4 bg-gray-50">
                <button id="return-tab-invoice" class="py-3 px-6 text-blue-600 border-b-2 border-blue-600 font-bold text-sm return-tab-active relative overflow-hidden transition-all duration-300 hover:bg-blue-50">
                    <span class="relative z-10 flex items-center">
                        <i class="ri-file-list-3-line mr-1.5"></i>
                        استرجاع فاتورة
                    </span>
                </button>
                <button id="return-tab-cart" class="py-3 px-6 text-gray-500 font-bold text-sm border-b-2 border-transparent hover:text-blue-600 hover:border-blue-200 transition-all duration-300 hover:bg-blue-50 relative overflow-hidden">
                    <span class="relative z-10 flex items-center">
                        <i class="ri-shopping-cart-2-line mr-1.5"></i>
                        استرجاع من السلة
                    </span>
                </button>
            </div>

            <div class="overflow-y-auto max-h-[60vh]">
                <!-- استرجاع فاتورة - تصميم محسن -->
                <div id="return-invoice-content" class="p-5">
                    <div class="mb-5">
                        <label for="invoice-search-input" class="block text-sm font-bold text-gray-700 mb-2 flex items-center">
                            <i class="ri-file-search-line mr-1.5 text-blue-500"></i>
                            رقم الفاتورة
                        </label>
                        <div class="flex">
                            <input type="text" id="invoice-search-input" class="block w-full rounded-r-xl border-gray-300 focus:ring-blue-500 focus:border-blue-500 text-base py-2.5 shadow-md" placeholder="أدخل رقم الفاتورة...">
                            <button id="invoice-search-btn" class="px-4 py-2.5 bg-gradient-to-r from-blue-500 to-blue-600 text-white rounded-l-xl hover:from-blue-600 hover:to-blue-700 transition-all duration-300 shadow-md flex items-center justify-center">
                                <i class="ri-search-line text-lg"></i>
                            </button>
                        </div>
                        <p class="text-xs text-gray-500 mt-2 flex items-center">
                            <i class="ri-information-line mr-1 text-blue-400"></i>
                            أدخل رقم الفاتورة للبحث عنها واسترجاع منتجاتها
                        </p>
                    </div>

                    <!-- تفاصيل الفاتورة -->
                    <div id="invoice-details-container" class="mb-4">
                        <!-- سيتم ملؤه ديناميكياً -->
                    </div>

                    <!-- قائمة المنتجات -->
                    <div id="invoice-items-container" class="mb-4">
                        <!-- سيتم ملؤه ديناميكياً -->
                    </div>
                </div>

                <!-- استرجاع من السلة -->
                <div id="return-cart-content" class="p-4 hidden">
                    <div class="mb-4">
                        <p class="text-sm text-gray-700 dark:text-gray-300 mb-2">استرجاع المنتجات من السلة الحالية</p>
                        <div class="bg-blue-50 dark:bg-blue-900/30 p-3 rounded-lg text-xs text-blue-800 dark:text-blue-300">
                            <p>يمكنك استرجاع المنتجات من السلة الحالية دون الحاجة لفاتورة سابقة.</p>
                            <p class="mt-1">سيتم إعادة المنتجات للمخزون وتسجيل عملية الاسترجاع في التقارير.</p>
                        </div>
                    </div>

                    <!-- اختيار العميل -->
                    <div class="mb-4">
                        <label for="return-customer-select" class="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">العميل</label>
                        <select id="return-customer-select" class="block w-full rounded-lg border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white focus:ring-blue-500 focus:border-blue-500 text-sm py-2">
                            <option value="">عميل نقدي</option>
                            {% for customer in customers %}
                            <option value="{{ customer.id }}">{{ customer.name }}</option>
                            {% endfor %}
                        </select>
                    </div>

                    <!-- قائمة المنتجات -->
                    <div id="cart-items-for-return" class="mb-4">
                        <!-- سيتم ملؤه ديناميكياً -->
                    </div>
                </div>
            </div>

            <!-- معلومات الاسترجاع -->
            <div id="return-info-section" class="p-4 border-t border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-700 hidden">
                <!-- طريقة استرداد المبلغ -->
                <div class="mb-3">
                    <label class="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-2">طريقة استرداد المبلغ</label>
                    <div class="grid grid-cols-3 gap-2">
                        <label class="flex items-center p-2 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-600 rounded-lg cursor-pointer hover:border-blue-500 dark:hover:border-blue-400 transition-all duration-300">
                            <input type="radio" name="return_payment_method" value="cash" class="hidden" checked>
                            <div class="w-full flex flex-col items-center">
                                <div class="w-8 h-8 rounded-full bg-green-100 dark:bg-green-900/50 flex items-center justify-center text-green-500 dark:text-green-400 mb-1">
                                    <i class="ri-money-dollar-circle-line"></i>
                                </div>
                                <span class="text-xs font-medium text-gray-700 dark:text-gray-300">نقدي</span>
                            </div>
                        </label>
                        <label class="flex items-center p-2 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-600 rounded-lg cursor-pointer hover:border-blue-500 dark:hover:border-blue-400 transition-all duration-300">
                            <input type="radio" name="return_payment_method" value="card" class="hidden">
                            <div class="w-full flex flex-col items-center">
                                <div class="w-8 h-8 rounded-full bg-blue-100 dark:bg-blue-900/50 flex items-center justify-center text-blue-500 dark:text-blue-400 mb-1">
                                    <i class="ri-bank-card-line"></i>
                                </div>
                                <span class="text-xs font-medium text-gray-700 dark:text-gray-300">بطاقة</span>
                            </div>
                        </label>
                        <label class="flex items-center p-2 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-600 rounded-lg cursor-pointer hover:border-blue-500 dark:hover:border-blue-400 transition-all duration-300">
                            <input type="radio" name="return_payment_method" value="store_credit" class="hidden">
                            <div class="w-full flex flex-col items-center">
                                <div class="w-8 h-8 rounded-full bg-purple-100 dark:bg-purple-900/50 flex items-center justify-center text-purple-500 dark:text-purple-400 mb-1">
                                    <i class="ri-store-2-line"></i>
                                </div>
                                <span class="text-xs font-medium text-gray-700 dark:text-gray-300">رصيد متجر</span>
                            </div>
                        </label>
                    </div>
                </div>

                <!-- ملاحظات المرتجع -->
                <div class="mb-3">
                    <label for="return-notes" class="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">ملاحظات</label>
                    <textarea id="return-notes" class="w-full rounded-lg border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white focus:ring-blue-500 focus:border-blue-500 text-sm" rows="2" placeholder="أدخل أي ملاحظات إضافية حول المرتجع..."></textarea>
                </div>

                <!-- خيارات الطباعة -->
                <div class="mb-3 bg-white dark:bg-gray-800 p-3 rounded-lg border border-gray-200 dark:border-gray-600">
                    <div class="text-xs font-medium text-gray-700 dark:text-gray-300 mb-2">خيارات الطباعة</div>
                    <div class="flex items-center mb-2">
                        <input type="checkbox" id="print-return-receipt" class="w-4 h-4 text-blue-600 dark:text-blue-500 border-gray-300 dark:border-gray-600 rounded focus:ring-blue-500" checked>
                        <label for="print-return-receipt" class="ml-2 text-xs text-gray-700 dark:text-gray-300">طباعة إيصال المرتجع</label>
                    </div>
                    <div class="flex items-center">
                        <input type="checkbox" id="print-return-invoice" class="w-4 h-4 text-blue-600 dark:text-blue-500 border-gray-300 dark:border-gray-600 rounded focus:ring-blue-500">
                        <label for="print-return-invoice" class="ml-2 text-xs text-gray-700 dark:text-gray-300">طباعة فاتورة المرتجع</label>
                    </div>
                </div>

                <!-- إجمالي المرتجع -->
                <div class="bg-blue-50 dark:bg-blue-900/30 p-3 rounded-lg border border-blue-100 dark:border-blue-800">
                    <div class="flex justify-between items-center">
                        <span class="text-sm font-bold text-gray-700 dark:text-gray-300">إجمالي المرتجع:</span>
                        <span id="return-total" class="font-bold text-lg text-red-500 dark:text-red-400">0.00 ج.م</span>
                    </div>
                </div>
            </div>

            <div class="p-3 border-t border-gray-200 dark:border-gray-700 flex justify-between bg-gray-50 dark:bg-gray-700 rounded-b-xl sticky bottom-0 z-10">
                <button id="cancel-return" class="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-600 transition-all duration-300 text-sm font-medium">إلغاء</button>
                <button id="return-invoice-btn" class="px-3 py-2 bg-red-500 text-white rounded-lg hover:bg-red-600 transition-all duration-300 hidden text-sm font-medium">
                    <i class="ri-arrow-go-back-line ml-1"></i>
                    تأكيد الاسترجاع
                </button>
            </div>
        </div>
    </div>



    <!-- Customer Search Modal - تصميم جديد -->
    <div id="customerModal" class="fixed inset-0 bg-black bg-opacity-70 z-50 flex items-center justify-center hidden backdrop-blur-sm">
        <div id="customer-modal-content" class="bg-white rounded-2xl shadow-2xl w-full max-w-md transform scale-95 opacity-0 transition-all duration-300 mx-2 max-h-[90vh] overflow-hidden border border-gray-200">
            <div class="p-4 border-b border-gray-200 flex justify-between items-center bg-gradient-to-r from-blue-50 to-white sticky top-0 z-10 rounded-t-2xl">
                <h3 class="text-lg font-bold text-gray-800 flex items-center">
                    <i class="ri-user-search-line mr-2 text-blue-500 text-xl"></i>
                    البحث عن عميل
                </h3>
                <button id="close-customer-modal" class="text-gray-500 hover:text-white hover:bg-red-500 transition-all duration-300 w-8 h-8 rounded-full flex items-center justify-center shadow-sm">
                    <i class="ri-close-line text-lg"></i>
                </button>
            </div>

            <!-- Tabs - تصميم محسن -->
            <div class="flex border-b border-gray-200 px-4 bg-gray-50">
                <button id="search-customer-tab" class="py-3 px-6 text-blue-600 border-b-2 border-blue-600 font-bold text-sm customer-tab-active relative overflow-hidden transition-all duration-300 hover:bg-blue-50">
                    <span class="relative z-10 flex items-center">
                        <i class="ri-search-line mr-1.5"></i>
                        بحث عن عميل
                    </span>
                </button>
                <button id="add-customer-tab" class="py-3 px-6 text-gray-500 font-bold text-sm border-b-2 border-transparent hover:text-blue-600 hover:border-blue-200 transition-all duration-300 hover:bg-blue-50 relative overflow-hidden">
                    <span class="relative z-10 flex items-center">
                        <i class="ri-user-add-line mr-1.5"></i>
                        إضافة عميل جديد
                    </span>
                </button>
            </div>

            <div class="overflow-y-auto max-h-[60vh]">
                <!-- بحث عن عميل -->
                <div id="search-customer-content" class="p-4">
                    <div class="mb-4">
                        <div class="relative">
                            <div class="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                                <i class="ri-search-line text-gray-400"></i>
                            </div>
                            <input type="text" id="modal-customer-search" class="block w-full pr-10 py-2.5 border border-gray-300 bg-white text-gray-800 rounded-lg focus:ring-blue-500 focus:border-blue-500 text-sm placeholder-gray-400 shadow-sm" placeholder="ابحث باسم العميل أو رقم الهاتف...">
                        </div>
                    </div>

                    <div id="modal-customer-results" class="space-y-2 mt-3">
                        <!-- سيتم ملء نتائج البحث هنا -->
                        <div class="text-center text-gray-500 py-4">
                            <div class="w-16 h-16 mx-auto rounded-full bg-gray-100 flex items-center justify-center mb-2">
                                <i class="ri-user-search-line text-2xl text-gray-400"></i>
                            </div>
                            <p class="text-sm">ابحث عن عميل</p>
                            <p class="text-xs mt-1">أدخل اسم العميل أو رقم الهاتف للبحث</p>
                        </div>
                    </div>
                </div>

                <!-- إضافة عميل جديد -->
                <div id="add-customer-content" class="p-4 hidden">
                    <form id="add-customer-form">
                        <div class="mb-3">
                            <label for="customer-name" class="block text-xs font-medium text-gray-700 mb-1">اسم العميل</label>
                            <input type="text" id="customer-name" name="name" class="block w-full rounded-lg border-gray-300 focus:ring-blue-500 focus:border-blue-500 text-sm py-2 shadow-sm" required>
                        </div>
                        <div class="mb-3">
                            <label for="customer-phone" class="block text-xs font-medium text-gray-700 mb-1">رقم الهاتف</label>
                            <input type="text" id="customer-phone" name="phone" class="block w-full rounded-lg border-gray-300 focus:ring-blue-500 focus:border-blue-500 text-sm py-2 shadow-sm">
                        </div>
                        <div class="mb-3">
                            <label for="customer-email" class="block text-xs font-medium text-gray-700 mb-1">البريد الإلكتروني</label>
                            <input type="email" id="customer-email" name="email" class="block w-full rounded-lg border-gray-300 focus:ring-blue-500 focus:border-blue-500 text-sm py-2 shadow-sm">
                        </div>
                        <div class="mb-3">
                            <label for="customer-address" class="block text-xs font-medium text-gray-700 mb-1">العنوان</label>
                            <textarea id="customer-address" name="address" class="block w-full rounded-lg border-gray-300 focus:ring-blue-500 focus:border-blue-500 text-sm shadow-sm" rows="2"></textarea>
                        </div>
                    </form>
                </div>
            </div>

            <div class="p-4 border-t border-gray-200 flex justify-between bg-gradient-to-b from-gray-50 to-white rounded-b-2xl sticky bottom-0 z-10 shadow-inner">
                <button id="cancel-customer-modal" class="px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-100 transition-all duration-300 text-sm font-medium shadow-sm">إلغاء</button>
                <button id="save-customer" class="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-all duration-300 text-sm font-medium shadow-md hidden">
                    <i class="ri-save-line mr-1"></i>
                    حفظ العميل
                </button>
                <button id="select-customer" class="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-all duration-300 text-sm font-medium shadow-md">
                    <i class="ri-check-line mr-1"></i>
                    اختيار العميل
                </button>
            </div>
        </div>
    </div>

    <!-- Success Modal -->
    <div id="successModal" class="fixed inset-0 bg-black bg-opacity-60 z-50 flex items-center justify-center hidden">
        <div id="success-modal-content" class="bg-white rounded-xl shadow-xl w-full max-w-sm transform scale-95 opacity-0 transition-all duration-300 mx-2">
            <div class="p-4 text-center">
                <div class="w-16 h-16 rounded-full bg-green-100 flex items-center justify-center mx-auto mb-3 shadow-sm">
                    <i class="ri-check-line text-2xl text-green-500"></i>
                </div>
                <h3 class="text-lg font-bold text-gray-800 mb-1">تم إتمام البيع بنجاح</h3>
                <p class="text-sm text-gray-600 mb-4">تم إتمام البيع وتسجيل الفاتورة بنجاح</p>

                <div class="grid grid-cols-3 gap-2 mb-3">
                    <button id="print-receipt" class="px-2 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors duration-300 flex flex-col items-center text-xs shadow-sm">
                        <i class="ri-printer-line mb-1 text-lg"></i>
                        <span>طباعة الإيصال</span>
                    </button>
                    <div class="relative group">
                        <button id="print-invoice-dropdown" class="px-2 py-2 bg-primary text-white rounded-lg hover:bg-primary-dark transition-colors duration-300 flex flex-col items-center text-xs shadow-sm w-full">
                            <i class="ri-file-list-3-line mb-1 text-lg"></i>
                            <span>طباعة الفاتورة</span>
                        </button>
                        <div id="invoice-options" class="absolute left-0 right-0 mt-1 bg-white rounded-lg shadow-lg z-10 hidden transform transition-all duration-300 opacity-0 scale-95 origin-top" style="display: none;">
                            <button id="print-invoice" class="w-full px-3 py-2 text-sm text-gray-700 hover:bg-gray-100 flex items-center rounded-t-lg">
                                <i class="ri-printer-line mr-2 text-primary"></i>
                                <span>طباعة حراري</span>
                            </button>
                            <button id="print-invoice-a4" class="w-full px-3 py-2 text-sm text-gray-700 hover:bg-gray-100 flex items-center">
                                <i class="ri-file-paper-2-line mr-2 text-indigo-500"></i>
                                <span>طباعة A4</span>
                            </button>
                            <button id="print-invoice-a5" class="w-full px-3 py-2 text-sm text-gray-700 hover:bg-gray-100 flex items-center rounded-b-lg">
                                <i class="ri-file-paper-line mr-2 text-purple-500"></i>
                                <span>طباعة A5</span>
                            </button>
                        </div>
                    </div>
                    <button id="open-drawer" class="px-2 py-2 bg-gray-500 text-white rounded-lg hover:bg-gray-600 transition-colors duration-300 flex flex-col items-center text-xs shadow-sm">
                        <i class="ri-archive-drawer-line mb-1 text-lg"></i>
                        <span>فتح الدرج</span>
                    </button>
                </div>

                <button id="new-sale" class="px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors duration-300 w-full text-sm font-medium shadow-sm">بيع جديد</button>
            </div>
        </div>
    </div>

    <!-- Templates -->

    <!-- Product Template - تصميم محسن -->
    <template id="product-template">
        <div class="product-card bg-white border border-gray-200 cursor-pointer transition-all duration-300 group hover:border-primary hover:shadow-lg rounded-xl overflow-hidden">
            <div class="product-image relative flex items-center justify-center h-36 overflow-hidden">
                <!-- صورة المنتج -->
                <div class="absolute inset-0 bg-cover bg-center bg-no-repeat transition-transform duration-500 group-hover:scale-110"></div>

                <!-- طبقة التأثير -->
                <div class="absolute inset-0 bg-gradient-to-t from-gray-900/30 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>

                <!-- زر إضافة سريعة -->
                <button class="absolute bottom-2 right-2 w-8 h-8 bg-primary text-white rounded-full flex items-center justify-center shadow-lg transform scale-0 opacity-0 group-hover:scale-100 group-hover:opacity-100 transition-all duration-300 ease-out z-10 hover:bg-primary-dark">
                    <i class="ri-add-line"></i>
                </button>
            </div>
            <div class="product-details p-3 border-t border-gray-100">
                <div class="product-name text-gray-800 font-medium text-sm mb-1.5 line-clamp-2 h-10 group-hover:text-primary transition-colors duration-300"></div>
                <div class="flex justify-between items-center">
                    <div class="product-price text-primary font-bold text-base"></div>
                    <div class="product-stock text-xs py-0.5 px-1.5 rounded-md font-medium"></div>
                </div>
            </div>
        </div>
    </template>

    <!-- Cart Item Template - تصميم مصغر -->
    <template id="cart-item-template">
        <div class="cart-item bg-white border border-gray-200 rounded-lg p-2 mb-2 shadow-sm hover:shadow transition-all duration-300 group hover:border-primary">
            <div class="flex">
                <!-- صورة المنتج -->
                <div class="cart-item-image flex-shrink-0 mr-2 w-12 h-12 rounded-md overflow-hidden shadow-sm relative">
                    <!-- طبقة التأثير عند التحويم -->
                    <div class="absolute inset-0 bg-gradient-to-t from-gray-900/40 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                </div>

                <!-- تفاصيل المنتج -->
                <div class="cart-item-details flex-1">
                    <div class="flex justify-between mb-1">
                        <div class="font-medium text-xs text-gray-800 cart-item-name group-hover:text-primary transition-colors duration-300"></div>
                        <div class="flex">
                            <button class="text-red-500 hover:text-white transition-all duration-300 cart-item-remove w-5 h-5 rounded-full hover:bg-red-500 flex items-center justify-center shadow-sm text-xs">
                                <i class="ri-close-line"></i>
                            </button>
                        </div>
                    </div>

                    <!-- السعر وأدوات التحكم بالكمية -->
                    <div class="flex justify-between items-center">
                        <div class="text-xs text-gray-500 cart-item-price"></div>
                        <div class="flex items-center bg-gradient-to-r from-blue-50 to-white rounded-lg border border-blue-100 p-0.5 shadow-sm">
                            <button class="cart-item-decrease w-5 h-5 rounded-full bg-white flex items-center justify-center hover:bg-red-500 hover:text-white transition-all duration-300 shadow-sm text-gray-600 text-xs">
                                <i class="ri-subtract-line"></i>
                            </button>
                            <span class="cart-item-quantity mx-1 font-bold text-xs text-gray-800 min-w-[16px] text-center"></span>
                            <button class="cart-item-increase w-5 h-5 rounded-full bg-white flex items-center justify-center hover:bg-green-500 hover:text-white transition-all duration-300 shadow-sm text-gray-600 text-xs">
                                <i class="ri-add-line"></i>
                            </button>
                        </div>
                    </div>

                    <!-- الخصم والإجمالي -->
                    <div class="flex justify-between items-center mt-1 pt-1 border-t border-gray-100">
                        <button class="cart-item-discount text-[10px] text-blue-600 hover:text-white transition-all duration-300 flex items-center bg-blue-50 hover:bg-blue-600 px-1.5 py-0.5 rounded-full shadow-sm">
                            <i class="ri-coupon-2-line mr-0.5"></i>
                            <span>خصم</span>
                        </button>
                        <div class="font-bold text-xs text-primary cart-item-total bg-blue-50 px-1.5 py-0.5 rounded-full"></div>
                    </div>
                </div>
            </div>
        </div>
    </template>

    <!-- Suspended Invoice Template -->
    <template id="suspended-invoice-template">
        <div class="suspended-invoice-item bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-4 mb-3 hover:border-blue-500 dark:hover:border-blue-400 hover:shadow-md transition-all duration-300">
            <div class="flex justify-between items-start mb-2">
                <div>
                    <h4 class="font-medium text-gray-800 dark:text-white suspended-invoice-number"></h4>
                    <p class="text-sm text-gray-500 dark:text-gray-400 suspended-invoice-customer"></p>
                </div>
                <div class="text-right">
                    <p class="text-sm font-medium text-blue-600 dark:text-blue-400 suspended-invoice-total"></p>
                    <p class="text-xs text-gray-500 dark:text-gray-400 suspended-invoice-date"></p>
                </div>
            </div>
            <div class="flex justify-between items-center">
                <span class="text-xs bg-yellow-100 dark:bg-yellow-900/30 text-yellow-800 dark:text-yellow-300 px-2 py-1 rounded-full suspended-invoice-items-count"></span>
                <button class="text-sm bg-blue-500 text-white px-3 py-1 rounded-lg hover:bg-blue-600 transition-colors duration-300 suspended-invoice-load">
                    تحميل الفاتورة
                </button>
            </div>
        </div>
    </template>

    <!-- Customer Result Template -->
    <template id="customer-result-template">
        <div class="customer-result p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg cursor-pointer transition-colors duration-300 border border-transparent hover:border-gray-200 dark:hover:border-gray-600">
            <div class="font-medium text-sm text-gray-800 dark:text-white customer-name"></div>
            <div class="text-xs text-gray-500 dark:text-gray-400 customer-phone flex items-center">
                <i class="ri-phone-line mr-1 text-blue-500 dark:text-blue-400"></i>
                <span></span>
            </div>
        </div>
    </template>



    <!-- JavaScript -->
    <script src="{{ url_for('static', filename='js/pos.js') }}"></script>
    <script src="{{ url_for('static', filename='js/pos_part2.js') }}"></script>
    <script src="{{ url_for('static', filename='js/pos_part3.js') }}"></script>
    <script src="{{ url_for('static', filename='js/pos_part4.js') }}"></script>
    <script src="{{ url_for('static', filename='js/pos_credit.js') }}"></script>
    <script src="{{ url_for('static', filename='js/pos_returns.js') }}"></script>
    <script src="{{ url_for('static', filename='js/invoice-search.js') }}"></script>
    <script src="{{ url_for('static', filename='js/customer_search.js') }}"></script>

    <!-- تحميل الفاتورة المسترجعة -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // تحميل الفاتورة المسترجعة إذا كانت موجودة
            {% if retrieved_invoice %}
            // تعيين بيانات الفاتورة
            document.getElementById('cart-invoice').textContent = '{{ retrieved_invoice.invoice_number }}';

            // تعيين العميل
            if ('{{ retrieved_invoice.customer_id }}') {
                selectedCustomerId = '{{ retrieved_invoice.customer_id }}';
                document.getElementById('customer-search').value = '{{ retrieved_invoice.customer_name }}' || 'عميل نقدي';
            }

            // تعيين قيم الخصم والضريبة
            document.getElementById('discount-value').value = {{ retrieved_invoice.discount or 0 }};
            document.getElementById('discount-type').value = '{{ retrieved_invoice.discount_type or "fixed" }}';
            document.getElementById('tax-percentage').value = {{ retrieved_invoice.tax_percentage or 14 }};

            // إضافة المنتجات إلى السلة
            {% for item in retrieved_items %}
            cartItems.push({
                id: {{ item.product_id }},
                name: '{{ item.product_name }}',
                price: {{ item.price }},
                original_price: {{ item.price }},
                quantity: {{ item.quantity }},
                total: {{ item.total }},
                max_quantity: {{ item.quantity }},
                discount: 0,
                discount_type: 'fixed',
                order_item_id: {{ item.id }},
                is_returned_item: true
            });
            {% endfor %}

            // تحديث عرض السلة
            setTimeout(() => {
                updateCartDisplay();
                showNotification('تم استرجاع الفاتورة بنجاح', 'success');
            }, 500);
            {% endif %}
        });
    </script>