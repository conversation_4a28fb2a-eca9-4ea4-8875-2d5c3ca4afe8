from flask import Blueprint, render_template, request, jsonify, flash, redirect, url_for
from flask_login import login_required, current_user
from models import Order, OrderItem, Customer, Product, Inventory, InventoryMovement, Notification, User
from app import db
from sqlalchemy import func, desc, extract
from datetime import datetime, timedelta
import calendar
import random

sales_blueprint = Blueprint('sales', __name__)

@sales_blueprint.route('/sales')
@login_required
def index():
    # Get filter parameters
    search = request.args.get('search', '')
    customer_id = request.args.get('customer_id', '')
    status = request.args.get('status', '')
    payment_method = request.args.get('payment_method', '')
    date_from = request.args.get('date_from', '')
    date_to = request.args.get('date_to', '')
    user_id = request.args.get('user_id', '')

    # Prepare query
    query = Order.query

    # Apply filters
    if search:
        # البحث في رقم الفاتورة أو اسم العميل
        query = query.outerjoin(Customer).filter(
            (Order.invoice_number.ilike(f'%{search}%')) |
            (Customer.name.ilike(f'%{search}%'))
        )

    if customer_id and customer_id.isdigit():
        query = query.filter(Order.customer_id == int(customer_id))

    if status:
        query = query.filter(Order.status == status)

    if payment_method:
        query = query.filter(Order.payment_method == payment_method)

    if user_id and user_id.isdigit():
        query = query.filter(Order.user_id == int(user_id))

    if date_from:
        try:
            date_from_obj = datetime.strptime(date_from, '%Y-%m-%d')
            query = query.filter(Order.created_at >= date_from_obj)
        except ValueError:
            pass

    if date_to:
        try:
            date_to_obj = datetime.strptime(date_to, '%Y-%m-%d')
            # Add a day to include all orders on the end date
            date_to_obj = date_to_obj.replace(hour=23, minute=59, second=59)
            query = query.filter(Order.created_at <= date_to_obj)
        except ValueError:
            pass

    # Execute query with pagination
    page = request.args.get('page', 1, type=int)
    per_page = 10

    # استخدام استعلام آمن بدون استخدام أعمدة غير موجودة
    orders_paginated = query.order_by(Order.created_at.desc()).paginate(page=page, per_page=per_page)

    # Get customers for filter dropdown
    customers = Customer.query.order_by(Customer.name).all()

    # Get users for filter dropdown
    users = User.query.order_by(User.username).all()

    # Get sales statistics safely
    try:
        total_orders = db.session.query(func.count(Order.id)).scalar() or 0
        total_sales = db.session.query(func.sum(Order.total)).scalar() or 0
        avg_order_value = total_sales / total_orders if total_orders > 0 else 0
    except Exception as e:
        print(f"Error fetching sales statistics: {str(e)}")
        total_orders = 0
        total_sales = 0
        avg_order_value = 0

    # Sales by payment method
    try:
        payment_data = db.session.query(
            Order.payment_method,
            func.sum(Order.total).label('total')
        ).group_by(Order.payment_method).all()

        # تحويل البيانات إلى قاموس
        sales_by_payment = {}
        for payment in payment_data:
            sales_by_payment[payment[0]] = float(payment[1])
    except Exception as e:
        print(f"Error fetching payment data: {str(e)}")
        sales_by_payment = {'cash': 0, 'card': 0}

    # Today's sales
    today = datetime.now().date()
    today_start = datetime.combine(today, datetime.min.time())
    today_end = datetime.combine(today, datetime.max.time())

    today_sales = db.session.query(func.sum(Order.total)).filter(
        Order.created_at.between(today_start, today_end)
    ).scalar() or 0

    today_order_count = Order.query.filter(
        Order.created_at.between(today_start, today_end)
    ).count()

    # Sales for this month
    first_day_of_month = datetime(today.year, today.month, 1)
    last_day_of_month = datetime(today.year, today.month, calendar.monthrange(today.year, today.month)[1], 23, 59, 59)

    month_sales = db.session.query(func.sum(Order.total)).filter(
        Order.created_at.between(first_day_of_month, last_day_of_month)
    ).scalar() or 0

    month_order_count = Order.query.filter(
        Order.created_at.between(first_day_of_month, last_day_of_month)
    ).count()

    # Top selling products this month (excluding returned items)
    top_products = db.session.query(
        Product,
        func.sum(OrderItem.quantity - func.coalesce(OrderItem.returned_quantity, 0)).label('quantity'),
        func.sum((OrderItem.quantity - func.coalesce(OrderItem.returned_quantity, 0)) * OrderItem.price).label('total')
    ).join(OrderItem).join(Order).filter(
        Order.created_at.between(first_day_of_month, last_day_of_month),
        Order.status != 'cancelled',  # استبعاد الطلبات الملغاة
        (OrderItem.quantity - func.coalesce(OrderItem.returned_quantity, 0)) > 0  # استبعاد المنتجات المرتجعة بالكامل
    ).group_by(Product.id).order_by(func.sum(OrderItem.quantity - func.coalesce(OrderItem.returned_quantity, 0)).desc()).limit(5).all()

    # Recent orders
    recent_orders = Order.query.order_by(Order.created_at.desc()).limit(5).all()

    stats = {
        'total_orders': total_orders,
        'total_sales': total_sales,
        'avg_order_value': avg_order_value,
        'sales_by_payment': sales_by_payment,
        'today_sales': today_sales,
        'today_order_count': today_order_count,
        'month_sales': month_sales,
        'month_order_count': month_order_count,
        'top_products': top_products,
        'recent_orders': recent_orders
    }

    return render_template(
        'sales/sales.html',
        orders=orders_paginated,
        customers=customers,
        users=users,
        stats=stats,
        search=search,
        customer_id=customer_id,
        user_id=user_id,
        status=status,
        payment_method=payment_method,
        date_from=date_from,
        date_to=date_to,
        current_user=current_user
    )

@sales_blueprint.route('/sales/<int:id>/details')
@login_required
def details(id):
    order = Order.query.get_or_404(id)
    return render_template('sales/sale_details.html', order=order, current_user=current_user)

@sales_blueprint.route('/sales/<int:id>/cancel', methods=['POST'])
@login_required
def cancel(id):
    order = Order.query.get_or_404(id)

    if order.status == 'cancelled':
        flash('الطلب ملغي بالفعل', 'warning')
        return redirect(url_for('sales.details', id=id))

    try:
        # Save the previous status
        previous_status = order.status

        # Update order status
        order.status = 'cancelled'

        # If order was completed, return products to stock
        if previous_status == 'completed':
            for item in order.items:
                product = Product.query.get(item.product_id)
                product.stock_quantity += item.quantity

        db.session.commit()

        flash('تم إلغاء الطلب بنجاح وإعادة المنتجات للمخزون', 'success')
    except Exception as e:
        db.session.rollback()
        flash(f'حدث خطأ أثناء إلغاء الطلب: {str(e)}', 'danger')

    return redirect(url_for('sales.details', id=id))

@sales_blueprint.route('/api/sales/invoice/<invoice_number>')
@login_required
def get_invoice_by_number(invoice_number):
    """استرجاع معلومات الفاتورة بواسطة رقم الفاتورة"""
    try:
        order = Order.query.filter_by(invoice_number=invoice_number).first()

        if not order:
            return jsonify({
                'success': False,
                'message': 'لم يتم العثور على الفاتورة'
            }), 404

        return jsonify({
            'success': True,
            'message': 'تم استرجاع الفاتورة بنجاح',
            'invoice': order.to_dict()
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'حدث خطأ أثناء استرجاع الفاتورة: {str(e)}'
        }), 500



@sales_blueprint.route('/sales/daily-chart')
@login_required
def daily_chart():
    # Get date range (default: last 7 days)
    days = int(request.args.get('days', 7))
    end_date = datetime.now().date()
    start_date = end_date - timedelta(days=days-1)

    # Generate a list of all dates in range
    date_list = []
    current_date = start_date
    while current_date <= end_date:
        date_list.append(current_date)
        current_date += timedelta(days=1)

    # Get sales data for each date
    sales_data = []
    order_counts = []

    for date in date_list:
        date_start = datetime.combine(date, datetime.min.time())
        date_end = datetime.combine(date, datetime.max.time())

        # Get total sales for this date
        daily_sales = db.session.query(func.sum(Order.total)).filter(
            Order.created_at.between(date_start, date_end)
        ).scalar() or 0

        # Get order count for this date
        daily_order_count = Order.query.filter(
            Order.created_at.between(date_start, date_end)
        ).count()

        sales_data.append(daily_sales)
        order_counts.append(daily_order_count)

    # Format dates for chart
    formatted_dates = [date.strftime('%Y-%m-%d') for date in date_list]

    return jsonify({
        'dates': formatted_dates,
        'sales': sales_data,
        'orders': order_counts
    })

@sales_blueprint.route('/sales/payment-method-chart')
@login_required
def payment_method_chart():
    # Get date range (default: all time)
    period = request.args.get('period', 'all')

    if period == 'month':
        today = datetime.now().date()
        start_date = datetime(today.year, today.month, 1)
        last_day = calendar.monthrange(today.year, today.month)[1]
        end_date = datetime(today.year, today.month, last_day, 23, 59, 59)
    elif period == 'year':
        today = datetime.now().date()
        start_date = datetime(today.year, 1, 1)
        end_date = datetime(today.year, 12, 31, 23, 59, 59)
    else:  # all time
        start_date = datetime(1900, 1, 1)  # A long time ago
        end_date = datetime.now()

    # Get sales by payment method
    payment_data = db.session.query(
        Order.payment_method,
        func.sum(Order.total).label('total')
    ).filter(
        Order.created_at.between(start_date, end_date)
    ).group_by(Order.payment_method).all()

    # Format data for chart
    labels = [payment[0] for payment in payment_data]
    data = [float(payment[1]) for payment in payment_data]

    return jsonify({
        'labels': labels,
        'data': data
    })

@sales_blueprint.route('/sales/monthly-chart')
@login_required
def monthly_chart():
    # Get sales data for each month of the current year
    current_year = datetime.now().year

    months_data = []
    for month in range(1, 13):
        # Get total sales for this month
        monthly_sales = db.session.query(func.sum(Order.total)).filter(
            extract('year', Order.created_at) == current_year,
            extract('month', Order.created_at) == month
        ).scalar() or 0

        months_data.append(float(monthly_sales))

    # Month names in Arabic
    month_names = ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو', 'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر']

    return jsonify({
        'months': month_names,
        'sales': months_data
    })

@sales_blueprint.route('/api/sales/invoice/<invoice_number>')
@login_required
def get_sales_invoice_by_number(invoice_number):
    """الحصول على تفاصيل الفاتورة برقم الفاتورة"""
    try:
        # البحث عن الفاتورة
        invoice = Order.query.filter_by(invoice_number=invoice_number).first()

        if not invoice:
            return jsonify({
                'success': False,
                'message': 'لم يتم العثور على الفاتورة'
            }), 404

        # التحقق من أن الفاتورة ليست ملغاة
        if invoice.status == 'cancelled':
            return jsonify({
                'success': False,
                'message': 'هذه الفاتورة ملغاة بالفعل'
            }), 400

        # الحصول على عناصر الفاتورة
        items = []
        for item in invoice.items:
            # حساب الكمية المتاحة للإرجاع
            available_quantity = item.quantity - item.returned_quantity

            # إضافة العنصر فقط إذا كانت هناك كمية متاحة للإرجاع
            if available_quantity > 0:
                items.append({
                    'id': item.id,
                    'product_id': item.product_id,
                    'product_name': item.product.name if item.product else 'منتج غير معروف',
                    'quantity': item.quantity,
                    'price': item.price,
                    'total': item.total,
                    'returned_quantity': item.returned_quantity,
                    'available_quantity': available_quantity
                })

        # إعداد بيانات الفاتورة
        invoice_data = {
            'id': invoice.id,
            'invoice_number': invoice.invoice_number,
            'customer_id': invoice.customer_id,
            'customer_name': invoice.customer.name if invoice.customer else None,
            'subtotal': invoice.subtotal,
            'discount': invoice.discount,
            'tax': invoice.tax,
            'total': invoice.total,
            'payment_method': invoice.payment_method,
            'status': invoice.status,
            'created_at': invoice.created_at.isoformat()
        }

        return jsonify({
            'success': True,
            'invoice': invoice_data,
            'items': items
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'حدث خطأ أثناء البحث عن الفاتورة: {str(e)}'
        }), 500

@sales_blueprint.route('/api/sales/invoice_search')
@login_required
def get_invoice_by_number_search():
    """الحصول على تفاصيل الفاتورة برقم الفاتورة (للمرتجعات)"""
    try:
        # الحصول على رقم الفاتورة من الاستعلام
        invoice_number = request.args.get('invoice_number')

        if not invoice_number:
            return jsonify({
                'success': False,
                'message': 'يرجى تحديد رقم الفاتورة'
            }), 400

        # البحث عن الفاتورة
        invoice = Order.query.filter_by(invoice_number=invoice_number).first()

        if not invoice:
            return jsonify({
                'success': False,
                'message': 'لم يتم العثور على الفاتورة'
            }), 404

        # التحقق من أن الفاتورة ليست ملغاة
        if invoice.status == 'cancelled':
            return jsonify({
                'success': False,
                'message': 'هذه الفاتورة ملغاة بالفعل'
            }), 400

        # التحقق من أن الفاتورة ليست مرتجعة بالكامل
        if invoice.status == 'returned':
            return jsonify({
                'success': False,
                'message': 'هذه الفاتورة مرتجعة بالكامل بالفعل'
            }), 400

        # الحصول على عناصر الفاتورة
        items = []
        for item in invoice.items:
            # حساب الكمية المتاحة للإرجاع
            available_quantity = item.quantity - item.returned_quantity

            # إضافة العنصر فقط إذا كانت هناك كمية متاحة للإرجاع
            if available_quantity > 0:
                items.append({
                    'id': item.id,
                    'product_id': item.product_id,
                    'product_name': item.product.name if item.product else 'منتج غير معروف',
                    'quantity': item.quantity,
                    'price': item.price,
                    'total': item.total,
                    'returned_quantity': item.returned_quantity,
                    'available_quantity': available_quantity
                })

        # التحقق من وجود منتجات متاحة للإرجاع
        if not items:
            return jsonify({
                'success': False,
                'message': 'لا توجد منتجات متاحة للإرجاع في هذه الفاتورة'
            }), 400

        # إعداد بيانات الفاتورة
        invoice_data = {
            'id': invoice.id,
            'invoice_number': invoice.invoice_number,
            'customer_id': invoice.customer_id,
            'customer_name': invoice.customer.name if invoice.customer else 'عميل نقدي',
            'subtotal': invoice.subtotal,
            'discount': invoice.discount,
            'tax': invoice.tax,
            'total': invoice.total,
            'payment_method': invoice.payment_method,
            'status': invoice.status,
            'created_at': invoice.created_at.isoformat()
        }

        return jsonify({
            'success': True,
            'invoice': invoice_data,
            'items': items
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'حدث خطأ أثناء البحث عن الفاتورة: {str(e)}'
        }), 500

@sales_blueprint.route('/api/sales/<int:id>/update-status', methods=['POST'])
@login_required
def api_update_order_status(id):
    """تحديث حالة الطلب"""
    try:
        order = Order.query.get_or_404(id)

        data = request.json
        if not data:
            return jsonify({
                'success': False,
                'message': 'لم يتم توفير بيانات التحديث'
            }), 400

        status = data.get('status')
        notes = data.get('notes')

        if status:
            # التحقق من صحة الحالة
            valid_statuses = ['completed', 'pending', 'cancelled', 'suspended', 'deferred']
            if status not in valid_statuses:
                return jsonify({
                    'success': False,
                    'message': f'الحالة غير صالحة. الحالات الصالحة هي: {", ".join(valid_statuses)}'
                }), 400

            # حفظ الحالة السابقة
            previous_status = order.status

            # تحديث الحالة
            order.status = status

            # إذا تم تغيير الحالة من ملغي إلى مكتمل، نحتاج لخصم المنتجات من المخزون مرة أخرى
            if previous_status == 'cancelled' and status == 'completed':
                for item in order.items:
                    product = Product.query.get(item.product_id)
                    if product:
                        product.stock_quantity -= item.quantity

            # إذا تم تغيير الحالة من مكتمل إلى ملغي، نحتاج لإعادة المنتجات للمخزون
            elif previous_status == 'completed' and status == 'cancelled':
                for item in order.items:
                    product = Product.query.get(item.product_id)
                    if product:
                        product.stock_quantity += item.quantity

        # حفظ التغييرات
        db.session.commit()

        return jsonify({
            'success': True,
            'message': 'تم تحديث حالة الطلب بنجاح',
            'order': {
                'id': order.id,
                'invoice_number': order.invoice_number,
                'status': order.status
            }
        })
    except Exception as e:
        db.session.rollback()
        return jsonify({
            'success': False,
            'message': f'حدث خطأ أثناء تحديث حالة الطلب: {str(e)}'
        }), 500

@sales_blueprint.route('/api/sales/<int:id>/add-payment', methods=['POST'])
@login_required
def api_add_payment(id):
    """إضافة دفعة للطلب"""
    try:
        order = Order.query.get_or_404(id)

        data = request.json
        if not data:
            return jsonify({
                'success': False,
                'message': 'لم يتم توفير بيانات الدفعة'
            }), 400

        # الحصول على بيانات الدفعة
        amount = float(data.get('amount', 0))
        payment_method = data.get('payment_method', 'cash')
        notes = data.get('notes', '')

        # التحقق من صحة المبلغ
        if amount <= 0:
            return jsonify({
                'success': False,
                'message': 'يجب أن يكون المبلغ أكبر من صفر'
            }), 400

        # حساب المبلغ المتبقي
        from models import Payment
        payments = Payment.query.filter_by(order_id=id).all()
        total_paid = sum(payment.amount for payment in payments)
        remaining_amount = order.total - total_paid

        # التحقق من أن المبلغ لا يتجاوز المبلغ المتبقي
        if amount > remaining_amount:
            return jsonify({
                'success': False,
                'message': f'المبلغ يتجاوز المبلغ المتبقي ({remaining_amount})'
            }), 400

        # إنشاء رقم مرجعي للدفعة
        reference_number = f"PAY-{datetime.now().strftime('%Y%m%d')}-{random.randint(1000, 9999)}"

        # إنشاء الدفعة
        payment = Payment(
            reference_number=reference_number,
            amount=amount,
            payment_method=payment_method,
            payment_date=datetime.now(),
            notes=notes,
            created_by=current_user.id,
            order_id=id
        )

        db.session.add(payment)

        # تحديث حالة الطلب بناءً على المدفوعات
        new_total_paid = total_paid + amount

        # إذا تم دفع كامل المبلغ، نغير الحالة إلى مكتمل
        if new_total_paid >= order.total:
            order.status = 'completed'

        # حفظ التغييرات
        db.session.commit()

        return jsonify({
            'success': True,
            'message': 'تم إضافة الدفعة بنجاح',
            'payment': {
                'id': payment.id,
                'reference_number': payment.reference_number,
                'amount': payment.amount,
                'payment_method': payment.payment_method,
                'payment_date': payment.payment_date.strftime('%Y-%m-%d %H:%M:%S')
            },
            'order': {
                'id': order.id,
                'invoice_number': order.invoice_number,
                'total': order.total,
                'total_paid': new_total_paid,
                'remaining_amount': order.total - new_total_paid,
                'status': order.status
            }
        })
    except Exception as e:
        db.session.rollback()
        return jsonify({
            'success': False,
            'message': f'حدث خطأ أثناء إضافة الدفعة: {str(e)}'
        }), 500

@sales_blueprint.route('/api/sales/return', methods=['POST'])
@login_required
def return_invoice():
    """استرجاع الفاتورة أو المنتجات من السلة وإعادتها للمخزون"""
    try:
        data = request.json

        # التحقق من نوع الإرجاع (من فاتورة أو من السلة)
        from_cart = data.get('from_cart', False)

        if from_cart:
            # استرجاع من السلة
            customer_id = data.get('customer_id')
            items_to_return = data.get('items', [])
            payment_method = data.get('payment_method', 'cash')
            notes = data.get('notes', '')

            if not items_to_return:
                return jsonify({
                    'success': False,
                    'message': 'لا توجد منتجات للإرجاع'
                }), 400

            # إنشاء فاتورة مرتجعات مباشرة
            # إنشاء فاتورة جديدة للمرتجعات
            invoice = Order(
                invoice_number=f"RET-{datetime.now().strftime('%Y%m%d%H%M%S')}",
                customer_id=customer_id if customer_id else None,
                user_id=current_user.id,
                subtotal=0,
                discount=0,
                tax=0,
                total=0,
                payment_method=payment_method,
                status='returned',
                created_at=datetime.now()
            )
            db.session.add(invoice)
            db.session.flush()  # للحصول على معرف الفاتورة

            # إنشاء سجل للاسترجاع
            return_order = ReturnOrder(
                reference_number=ReturnOrder.generate_reference_number(),
                order_id=invoice.id,
                user_id=current_user.id,
                payment_method=payment_method,
                notes=notes
            )
            db.session.add(return_order)

            # إعادة المنتجات للمخزون
            total_returned = 0

            for item_data in items_to_return:
                product_id = item_data.get('product_id')
                return_quantity = item_data.get('quantity')
                price = item_data.get('price')
                item_reason = item_data.get('reason', '')

                if not product_id or not return_quantity or not price:
                    continue

                # إنشاء عنصر في الفاتورة
                order_item = OrderItem(
                    order_id=invoice.id,
                    product_id=product_id,
                    quantity=return_quantity,
                    price=price,
                    total=price * return_quantity,
                    returned_quantity=return_quantity  # مرتجع بالكامل
                )
                db.session.add(order_item)
                db.session.flush()  # للحصول على معرف العنصر

                # إعادة المنتج للمخزون
                product = Product.query.get(product_id)

                if product:
                    # البحث عن المخزن الافتراضي
                    from models import Warehouse
                    default_warehouse = Warehouse.query.filter_by(is_default=True).first()

                    if default_warehouse:
                        # البحث عن المنتج في المخزن
                        inventory = Inventory.query.filter_by(
                            warehouse_id=default_warehouse.id,
                            product_id=product_id
                        ).first()

                        if inventory:
                            # تحديث كمية المخزون وتسجيل الحركة
                            new_quantity = inventory.quantity + return_quantity
                            inventory.update_quantity(
                                new_quantity=new_quantity,
                                movement_type='add',
                                user_id=current_user.id,
                                reference=f'return_{return_order.reference_number}',
                                notes=f'تم إرجاع المنتج من السلة'
                            )
                        else:
                            # إنشاء سجل جديد في المخزون
                            inventory = Inventory(
                                warehouse_id=default_warehouse.id,
                                product_id=product_id,
                                quantity=return_quantity
                            )
                            db.session.add(inventory)

                            # تسجيل حركة المخزون
                            movement = InventoryMovement(
                                product_id=product_id,
                                warehouse_id=default_warehouse.id,
                                movement_type='add',
                                quantity=return_quantity,
                                previous_quantity=0,
                                new_quantity=return_quantity,
                                reference=f'return_{return_order.reference_number}',
                                notes=f'تم إرجاع المنتج من السلة',
                                created_by=current_user.id
                            )
                            db.session.add(movement)

                    # حساب المبلغ المرتجع
                    returned_amount = return_quantity * price
                    total_returned += returned_amount

                    # إنشاء عنصر إرجاع
                    return_item = ReturnItem(
                        return_order_id=return_order.id,
                        order_item_id=order_item.id,
                        product_id=product_id,
                        quantity=return_quantity,
                        price=price,
                        total=returned_amount,
                        reason=item_reason
                    )
                    db.session.add(return_item)

            # تحديث إجمالي الفاتورة
            invoice.subtotal = total_returned
            invoice.total = total_returned

            # تحديث إجمالي المبلغ المرتجع
            return_order.total_amount = total_returned

            # إنشاء إشعار بالاسترجاع
            notification = Notification(
                user_id=current_user.id,
                title='استرجاع منتجات',
                message=f'تم استرجاع منتجات من السلة بقيمة {total_returned:.2f} ج.م',
                type='info',
                link=f'/returns/{return_order.id}/details'
            )
            db.session.add(notification)

            # إنشاء معاملة في الخزينة إذا كان الدفع نقدي أو بطاقة
            if payment_method in ['cash', 'card']:
                # البحث عن الخزينة الافتراضية
                from models import CashRegister, CashTransaction
                default_register = CashRegister.query.filter_by(is_default=True).first()

                if default_register:
                    # إنشاء معاملة سحب من الخزينة
                    transaction = CashTransaction(
                        cash_register_id=default_register.id,
                        transaction_type='withdraw',
                        amount=total_returned,
                        previous_balance=default_register.current_balance,
                        new_balance=default_register.current_balance - total_returned,
                        reference=f'return_{return_order.reference_number}',
                        notes=f'استرداد مبلغ مرتجع من السلة بواسطة {payment_method}',
                        created_by=current_user.id
                    )
                    db.session.add(transaction)

                    # تحديث رصيد الخزينة
                    default_register.current_balance -= total_returned

            db.session.commit()

            return jsonify({
                'success': True,
                'message': 'تم استرجاع المنتجات بنجاح وإعادتها للمخزون',
                'returned_amount': total_returned,
                'return_reference': return_order.reference_number,
                'return_id': return_order.id
            })

        else:
            # استرجاع من فاتورة موجودة
            invoice_id = data.get('invoice_id')
            invoice_number = data.get('invoice_number')
            items_to_return = data.get('items', [])
            payment_method = data.get('payment_method', 'cash')
            notes = data.get('notes', '')

            if not invoice_id or not items_to_return:
                return jsonify({
                    'success': False,
                    'message': 'بيانات غير كاملة'
                }), 400

            # البحث عن الفاتورة
            invoice = Order.query.get(invoice_id)

            if not invoice:
                return jsonify({
                    'success': False,
                    'message': 'لم يتم العثور على الفاتورة'
                }), 404

            # التحقق من أن الفاتورة ليست ملغاة
            if invoice.status == 'cancelled':
                return jsonify({
                    'success': False,
                    'message': 'هذه الفاتورة ملغاة بالفعل'
                }), 400

            # إنشاء سجل للاسترجاع
            return_order = ReturnOrder(
                reference_number=ReturnOrder.generate_reference_number(),
                order_id=invoice.id,
                user_id=current_user.id,
                payment_method=payment_method,
                notes=notes
            )
            db.session.add(return_order)

            # إعادة المنتجات للمخزون
            total_returned = 0

            for item_data in items_to_return:
                order_item_id = item_data.get('order_item_id')
                product_id = item_data.get('product_id')
                return_quantity = item_data.get('quantity')
                item_reason = item_data.get('reason', '')

                if not order_item_id or not product_id or not return_quantity:
                    continue

                # البحث عن عنصر الفاتورة
                order_item = OrderItem.query.get(order_item_id)

                if not order_item or order_item.order_id != invoice.id:
                    continue

                # التحقق من أن الكمية المرتجعة لا تتجاوز الكمية المتاحة للإرجاع
                available_quantity = order_item.quantity - order_item.returned_quantity
                if return_quantity > available_quantity:
                    return_quantity = available_quantity

                if return_quantity <= 0:
                    continue

                # إعادة المنتج للمخزون
                product = Product.query.get(product_id)

                if product:
                    # البحث عن المخزن الافتراضي
                    from models import Warehouse
                    default_warehouse = Warehouse.query.filter_by(is_default=True).first()

                    if default_warehouse:
                        # البحث عن المنتج في المخزن
                        inventory = Inventory.query.filter_by(
                            warehouse_id=default_warehouse.id,
                            product_id=product_id
                        ).first()

                        if inventory:
                            # تحديث كمية المخزون وتسجيل الحركة
                            new_quantity = inventory.quantity + return_quantity
                            inventory.update_quantity(
                                new_quantity=new_quantity,
                                movement_type='add',
                                user_id=current_user.id,
                                reference=f'return_{return_order.reference_number}',
                                notes=f'تم إرجاع المنتج من الفاتورة {invoice_number}'
                            )
                        else:
                            # إنشاء سجل جديد في المخزون
                            inventory = Inventory(
                                warehouse_id=default_warehouse.id,
                                product_id=product_id,
                                quantity=return_quantity
                            )
                            db.session.add(inventory)

                            # تسجيل حركة المخزون
                            movement = InventoryMovement(
                                product_id=product_id,
                                warehouse_id=default_warehouse.id,
                                movement_type='add',
                                quantity=return_quantity,
                                previous_quantity=0,
                                new_quantity=return_quantity,
                                reference=f'return_{return_order.reference_number}',
                                notes=f'تم إرجاع المنتج من الفاتورة {invoice_number}',
                                created_by=current_user.id
                            )
                            db.session.add(movement)

                    # تحديث كمية المنتج المرتجعة في عنصر الفاتورة
                    order_item.returned_quantity += return_quantity

                    # حساب المبلغ المرتجع
                    returned_amount = return_quantity * order_item.price
                    total_returned += returned_amount

                    # إنشاء عنصر إرجاع
                    return_item = ReturnItem(
                        return_order_id=return_order.id,
                        order_item_id=order_item.id,
                        product_id=product_id,
                        quantity=return_quantity,
                        price=order_item.price,
                        total=returned_amount,
                        reason=item_reason
                    )
                    db.session.add(return_item)

            # تحديث إجمالي المبلغ المرتجع
            return_order.total_amount = total_returned

            # تحديث حالة الفاتورة إذا تم إرجاع جميع المنتجات
            all_items_returned = all(
                item.returned_quantity == item.quantity
                for item in invoice.items
            )

            if all_items_returned:
                invoice.status = 'returned'
            else:
                invoice.status = 'partially_returned'

            # إنشاء إشعار بالاسترجاع
            notification = Notification(
                user_id=current_user.id,
                title='استرجاع فاتورة',
                message=f'تم استرجاع منتجات من الفاتورة {invoice_number} بقيمة {total_returned:.2f} ج.م',
                type='info',
                link=f'/returns/{return_order.id}/details'
            )
            db.session.add(notification)

            # إنشاء معاملة في الخزينة إذا كان الدفع نقدي أو بطاقة
            if payment_method in ['cash', 'card']:
                # البحث عن الخزينة الافتراضية
                from models import CashRegister, CashTransaction
                default_register = CashRegister.query.filter_by(is_default=True).first()

                if default_register:
                    # إنشاء معاملة سحب من الخزينة
                    transaction = CashTransaction(
                        cash_register_id=default_register.id,
                        transaction_type='withdraw',
                        amount=total_returned,
                        previous_balance=default_register.current_balance,
                        new_balance=default_register.current_balance - total_returned,
                        reference=f'return_{return_order.reference_number}',
                        notes=f'استرداد مبلغ مرتجع للفاتورة {invoice_number} بواسطة {payment_method}',
                        created_by=current_user.id
                    )
                    db.session.add(transaction)

                    # تحديث رصيد الخزينة
                    default_register.current_balance -= total_returned

            db.session.commit()

            return jsonify({
                'success': True,
                'message': 'تم استرجاع المنتجات بنجاح وإعادتها للمخزون',
                'returned_amount': total_returned,
                'return_reference': return_order.reference_number,
                'return_id': return_order.id
            })

    except Exception as e:
        db.session.rollback()
        return jsonify({
            'success': False,
            'message': f'حدث خطأ أثناء استرجاع المنتجات: {str(e)}'
        }), 500