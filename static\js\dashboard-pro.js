/**
 * Dashboard Pro - وظائف متقدمة للوحة التحكم
 */

class DashboardPro {
    constructor(options = {}) {
        // الإعدادات الافتراضية
        this.options = {
            apiEndpoint: options.apiEndpoint || '/api/dashboard',
            refreshInterval: options.refreshInterval || 300000, // 5 دقائق
            darkMode: options.darkMode || false,
            animateCharts: options.animateCharts !== undefined ? options.animateCharts : true,
            saveSettings: options.saveSettings !== undefined ? options.saveSettings : true,
            settingsKey: options.settingsKey || 'dashboard-pro-settings',
            sidebarId: options.sidebarId || 'dashboard-sidebar',
            contentId: options.contentId || 'dashboard-content',
            notificationsEnabled: options.notificationsEnabled !== undefined ? options.notificationsEnabled : true
        };

        // حالة لوحة التحكم
        this.state = {
            loading: false,
            lastUpdate: null,
            charts: {},
            data: {},
            visibleSections: [],
            activeSection: 'overview'
        };

        // تهيئة لوحة التحكم
        this.init();
    }

    /**
     * تهيئة لوحة التحكم
     */
    init() {
        console.log('تهيئة لوحة التحكم المتقدمة...');

        // تحميل الإعدادات المحفوظة
        this.loadSettings();

        // إعداد مستمعي الأحداث
        this.setupEventListeners();

        // تحميل بيانات لوحة التحكم
        this.loadDashboardData();

        // بدء التحديث التلقائي
        this.startAutoRefresh();

        // تهيئة الرسوم البيانية
        this.initCharts();

        // تطبيق وضع الدارك مود إذا كان مفعلاً
        if (this.options.darkMode) {
            this.applyDarkMode();
        }

        console.log('تم تهيئة لوحة التحكم المتقدمة بنجاح');
    }

    /**
     * إعداد مستمعي الأحداث
     */
    setupEventListeners() {
        // زر تحديث لوحة التحكم
        const refreshBtn = document.getElementById('refresh-dashboard');
        if (refreshBtn) {
            refreshBtn.addEventListener('click', () => {
                this.refreshDashboard();
            });
        }

        // زر تخصيص لوحة التحكم
        const customizeBtn = document.getElementById('customize-dashboard');
        const customizerPanel = document.getElementById('dashboard-customizer');
        const closeCustomizerBtn = document.getElementById('close-customizer');

        if (customizeBtn && customizerPanel) {
            customizeBtn.addEventListener('click', () => {
                customizerPanel.classList.remove('hidden');
                setTimeout(() => {
                    const panel = customizerPanel.querySelector('.customizer-panel');
                    if (panel) {
                        panel.classList.add('scale-100', 'opacity-100');
                    }
                }, 10);
            });
        }

        if (closeCustomizerBtn && customizerPanel) {
            closeCustomizerBtn.addEventListener('click', () => {
                const panel = customizerPanel.querySelector('.customizer-panel');
                if (panel) {
                    panel.classList.remove('scale-100', 'opacity-100');
                }
                setTimeout(() => {
                    customizerPanel.classList.add('hidden');
                }, 300);
            });
        }

        // مستمعي أحداث القائمة الجانبية
        const sidebarItems = document.querySelectorAll('.sidebar-menu-item');
        sidebarItems.forEach(item => {
            const link = item.querySelector('a');
            if (link) {
                link.addEventListener('click', (e) => {
                    // منع السلوك الافتراضي للرابط إذا كان يشير إلى قسم في نفس الصفحة
                    if (link.getAttribute('href').startsWith('#')) {
                        e.preventDefault();

                        // تحديث العنصر النشط في القائمة
                        sidebarItems.forEach(i => i.classList.remove('active'));
                        item.classList.add('active');

                        // الانتقال إلى القسم المطلوب
                        const sectionId = link.getAttribute('href').substring(1);
                        this.navigateToSection(sectionId);
                    }
                });
            }
        });

        // مستمع تغيير فترة الرسم البياني
        const chartPeriodSelect = document.getElementById('chart-period');
        if (chartPeriodSelect) {
            chartPeriodSelect.addEventListener('change', () => {
                this.updateChartPeriod(chartPeriodSelect.value);
            });
        }

        // مستمع تغيير نوع الرسم البياني
        const chartTypeToggle = document.getElementById('chart-type-toggle');
        if (chartTypeToggle) {
            chartTypeToggle.addEventListener('click', () => {
                this.toggleChartType();
            });
        }
    }

    /**
     * تحميل بيانات لوحة التحكم
     */
    loadDashboardData() {
        this.state.loading = true;
        this.updateLoadingState(true);

        fetch(this.options.apiEndpoint)
            .then(response => response.json())
            .then(data => {
                this.state.data = data;
                this.state.lastUpdate = new Date();
                this.updateDashboard();
                this.state.loading = false;
                this.updateLoadingState(false);
            })
            .catch(error => {
                console.error('خطأ في تحميل بيانات لوحة التحكم:', error);
                this.state.loading = false;
                this.updateLoadingState(false);

                // عرض إشعار
                if (this.options.notificationsEnabled && window.systemManager) {
                    window.systemManager.showNotification(
                        'خطأ في التحميل',
                        'حدث خطأ أثناء تحميل بيانات لوحة التحكم',
                        'error'
                    );
                }
            });
    }

    /**
     * تحديث حالة التحميل
     * @param {boolean} loading - حالة التحميل
     */
    updateLoadingState(loading) {
        const refreshBtn = document.getElementById('refresh-dashboard');
        if (refreshBtn) {
            if (loading) {
                refreshBtn.disabled = true;
                refreshBtn.innerHTML = '<i class="ri-loader-4-line animate-spin"></i><span class="mr-1">جاري التحديث...</span>';
            } else {
                refreshBtn.disabled = false;
                refreshBtn.innerHTML = '<i class="ri-refresh-line"></i><span class="mr-1">تحديث</span>';
            }
        }
    }

    /**
     * تحديث لوحة التحكم
     */
    updateDashboard() {
        // تحديث البطاقات الإحصائية
        this.updateStatsCards();

        // تحديث الرسوم البيانية
        this.updateCharts();

        // تحديث الجداول
        this.updateTables();

        // تحديث وقت آخر تحديث
        this.updateLastUpdateTime();
    }

    /**
     * تحديث البطاقات الإحصائية
     */
    updateStatsCards() {
        // تنفيذ تحديث البطاقات الإحصائية هنا
        console.log('تحديث البطاقات الإحصائية');
    }

    /**
     * تحديث الرسوم البيانية
     */
    updateCharts() {
        // تنفيذ تحديث الرسوم البيانية هنا
        console.log('تحديث الرسوم البيانية');
    }

    /**
     * تحديث الجداول
     */
    updateTables() {
        // تنفيذ تحديث الجداول هنا
        console.log('تحديث الجداول');
    }

    /**
     * تحديث وقت آخر تحديث
     */
    updateLastUpdateTime() {
        // تنفيذ تحديث وقت آخر تحديث هنا
        console.log('تحديث وقت آخر تحديث');
    }

    /**
     * تحديث لوحة التحكم
     */
    refreshDashboard() {
        if (!this.state.loading) {
            this.loadDashboardData();

            // عرض إشعار
            if (this.options.notificationsEnabled && window.systemManager) {
                window.systemManager.showNotification(
                    'جاري التحديث',
                    'جاري تحديث بيانات لوحة التحكم',
                    'info'
                );
            }
        }
    }

    /**
     * بدء التحديث التلقائي
     */
    startAutoRefresh() {
        if (this.refreshInterval) {
            clearInterval(this.refreshInterval);
        }

        this.refreshInterval = setInterval(() => {
            this.refreshDashboard();
        }, this.options.refreshInterval);

        console.log(`تم بدء التحديث التلقائي كل ${this.options.refreshInterval / 60000} دقيقة`);
    }

    /**
     * إيقاف التحديث التلقائي
     */
    stopAutoRefresh() {
        if (this.refreshInterval) {
            clearInterval(this.refreshInterval);
            console.log('تم إيقاف التحديث التلقائي');
        }
    }

    /**
     * الانتقال إلى قسم معين
     * @param {string} sectionId - معرف القسم
     */
    navigateToSection(sectionId) {
        // تحديث القسم النشط
        this.state.activeSection = sectionId;

        // الانتقال إلى القسم
        const section = document.getElementById(sectionId);
        if (section) {
            section.scrollIntoView({ behavior: 'smooth' });
        }

        // حفظ الإعدادات
        this.saveSettings();

        console.log(`تم الانتقال إلى قسم: ${sectionId}`);
    }

    /**
     * تهيئة الرسوم البيانية
     */
    initCharts() {
        console.log('تهيئة الرسوم البيانية');

        // تهيئة رسم بياني المبيعات
        this.initSalesChart();

        // تهيئة رسم بياني توزيع المنتجات
        this.initCategoriesChart();
    }

    /**
     * تهيئة رسم بياني المبيعات
     */
    initSalesChart() {
        const salesChartElement = document.getElementById('sales-chart');
        if (!salesChartElement) return;

        // الحصول على فترة الرسم البياني
        const chartPeriod = document.getElementById('chart-period')?.value || '30';

        // جلب بيانات المبيعات
        fetch(`/api/sales-chart-data?days=${chartPeriod}`)
            .then(response => response.json())
            .then(data => {
                // تهيئة الرسم البياني
                const options = {
                    chart: {
                        type: 'area',
                        height: 350,
                        fontFamily: 'Cairo, sans-serif',
                        toolbar: {
                            show: true,
                            tools: {
                                download: true,
                                selection: false,
                                zoom: false,
                                zoomin: false,
                                zoomout: false,
                                pan: false,
                                reset: false
                            }
                        },
                        animations: {
                            enabled: true,
                            easing: 'easeinout',
                            speed: 800,
                            animateGradually: {
                                enabled: true,
                                delay: 150
                            },
                            dynamicAnimation: {
                                enabled: true,
                                speed: 350
                            }
                        }
                    },
                    colors: ['#3b82f6', '#818cf8'],
                    dataLabels: {
                        enabled: false
                    },
                    stroke: {
                        curve: 'smooth',
                        width: [3, 2]
                    },
                    series: [
                        {
                            name: 'المبيعات',
                            data: data.sales
                        },
                        {
                            name: 'الطلبات',
                            data: data.orders
                        }
                    ],
                    xaxis: {
                        categories: data.dates,
                        labels: {
                            style: {
                                colors: '#64748b',
                                fontFamily: 'Cairo, sans-serif'
                            }
                        }
                    },
                    yaxis: [
                        {
                            title: {
                                text: 'المبيعات (ج.م)',
                                style: {
                                    color: '#3b82f6',
                                    fontFamily: 'Cairo, sans-serif'
                                }
                            },
                            labels: {
                                style: {
                                    colors: '#64748b',
                                    fontFamily: 'Cairo, sans-serif'
                                }
                            }
                        },
                        {
                            opposite: true,
                            title: {
                                text: 'عدد الطلبات',
                                style: {
                                    color: '#818cf8',
                                    fontFamily: 'Cairo, sans-serif'
                                }
                            },
                            labels: {
                                style: {
                                    colors: '#64748b',
                                    fontFamily: 'Cairo, sans-serif'
                                }
                            }
                        }
                    ],
                    tooltip: {
                        y: {
                            formatter: function(value, { series, seriesIndex, dataPointIndex, w }) {
                                if (seriesIndex === 0) {
                                    return value + ' ج.م';
                                } else {
                                    return value + ' طلب';
                                }
                            }
                        }
                    },
                    legend: {
                        position: 'top',
                        horizontalAlign: 'right',
                        fontFamily: 'Cairo, sans-serif'
                    },
                    fill: {
                        type: 'gradient',
                        gradient: {
                            shadeIntensity: 1,
                            opacityFrom: 0.7,
                            opacityTo: 0.2,
                            stops: [0, 90, 100]
                        }
                    },
                    grid: {
                        borderColor: '#e2e8f0',
                        strokeDashArray: 4,
                        xaxis: {
                            lines: {
                                show: true
                            }
                        },
                        yaxis: {
                            lines: {
                                show: true
                            }
                        }
                    }
                };

                // إنشاء الرسم البياني
                this.state.charts.salesChart = new ApexCharts(salesChartElement, options);
                this.state.charts.salesChart.render();

                // تطبيق وضع الدارك مود إذا كان مفعلاً
                if (this.options.darkMode) {
                    this.updateChartTheme(this.state.charts.salesChart, true);
                }
            })
            .catch(error => {
                console.error('خطأ في جلب بيانات المبيعات:', error);

                // عرض إشعار
                if (this.options.notificationsEnabled && window.systemManager) {
                    window.systemManager.showNotification(
                        'خطأ في الرسم البياني',
                        'حدث خطأ أثناء جلب بيانات المبيعات',
                        'error'
                    );
                }
            });
    }

    /**
     * تهيئة رسم بياني توزيع المنتجات
     */
    initCategoriesChart() {
        const categoriesChartElement = document.getElementById('categories-chart');
        if (!categoriesChartElement) return;

        // جلب بيانات توزيع المنتجات
        fetch('/api/category-distribution')
            .then(response => response.json())
            .then(data => {
                // تهيئة الرسم البياني
                const options = {
                    chart: {
                        type: 'donut',
                        height: 350,
                        fontFamily: 'Cairo, sans-serif',
                        animations: {
                            enabled: true,
                            easing: 'easeinout',
                            speed: 800,
                            animateGradually: {
                                enabled: true,
                                delay: 150
                            },
                            dynamicAnimation: {
                                enabled: true,
                                speed: 350
                            }
                        }
                    },
                    colors: ['#3b82f6', '#8b5cf6', '#10b981', '#f59e0b', '#ef4444', '#ec4899', '#06b6d4', '#14b8a6'],
                    series: data.counts,
                    labels: data.categories,
                    legend: {
                        position: 'bottom',
                        fontFamily: 'Cairo, sans-serif'
                    },
                    dataLabels: {
                        enabled: true,
                        formatter: function(val) {
                            return Math.round(val) + '%';
                        },
                        style: {
                            fontFamily: 'Cairo, sans-serif'
                        }
                    },
                    plotOptions: {
                        pie: {
                            donut: {
                                size: '50%',
                                labels: {
                                    show: true,
                                    name: {
                                        show: true,
                                        fontFamily: 'Cairo, sans-serif'
                                    },
                                    value: {
                                        show: true,
                                        fontFamily: 'Cairo, sans-serif',
                                        formatter: function(val) {
                                            return val;
                                        }
                                    },
                                    total: {
                                        show: true,
                                        label: 'الإجمالي',
                                        fontFamily: 'Cairo, sans-serif',
                                        formatter: function(w) {
                                            return w.globals.seriesTotals.reduce((a, b) => a + b, 0);
                                        }
                                    }
                                }
                            }
                        }
                    },
                    responsive: [
                        {
                            breakpoint: 480,
                            options: {
                                chart: {
                                    height: 300
                                },
                                legend: {
                                    position: 'bottom'
                                }
                            }
                        }
                    ]
                };

                // إنشاء الرسم البياني
                this.state.charts.categoriesChart = new ApexCharts(categoriesChartElement, options);
                this.state.charts.categoriesChart.render();

                // تطبيق وضع الدارك مود إذا كان مفعلاً
                if (this.options.darkMode) {
                    this.updateChartTheme(this.state.charts.categoriesChart, true);
                }
            })
            .catch(error => {
                console.error('خطأ في جلب بيانات توزيع المنتجات:', error);

                // عرض إشعار
                if (this.options.notificationsEnabled && window.systemManager) {
                    window.systemManager.showNotification(
                        'خطأ في الرسم البياني',
                        'حدث خطأ أثناء جلب بيانات توزيع المنتجات',
                        'error'
                    );
                }
            });
    }

    /**
     * تحديث سمة الرسم البياني
     * @param {object} chart - كائن الرسم البياني
     * @param {boolean} isDarkMode - حالة وضع الدارك مود
     */
    updateChartTheme(chart, isDarkMode) {
        if (!chart) return;

        const darkTheme = {
            chart: {
                background: '#1e293b'
            },
            xaxis: {
                labels: {
                    style: {
                        colors: '#94a3b8'
                    }
                }
            },
            yaxis: {
                labels: {
                    style: {
                        colors: '#94a3b8'
                    }
                }
            },
            grid: {
                borderColor: '#334155'
            },
            legend: {
                labels: {
                    colors: '#e2e8f0'
                }
            }
        };

        const lightTheme = {
            chart: {
                background: '#ffffff'
            },
            xaxis: {
                labels: {
                    style: {
                        colors: '#64748b'
                    }
                }
            },
            yaxis: {
                labels: {
                    style: {
                        colors: '#64748b'
                    }
                }
            },
            grid: {
                borderColor: '#e2e8f0'
            },
            legend: {
                labels: {
                    colors: '#1e293b'
                }
            }
        };

        chart.updateOptions(isDarkMode ? darkTheme : lightTheme);
    }

    /**
     * تحديث فترة الرسم البياني
     * @param {string} period - الفترة الزمنية
     */
    updateChartPeriod(period) {
        console.log(`تحديث فترة الرسم البياني إلى ${period} يوم`);

        // تحديث الرسم البياني
        if (this.state.charts.salesChart) {
            // إظهار حالة التحميل
            this.state.charts.salesChart.updateOptions({
                chart: {
                    animations: {
                        enabled: false
                    }
                }
            });

            // جلب بيانات المبيعات الجديدة
            fetch(`/api/sales-chart-data?days=${period}`)
                .then(response => response.json())
                .then(data => {
                    // تحديث بيانات الرسم البياني
                    this.state.charts.salesChart.updateOptions({
                        series: [
                            {
                                name: 'المبيعات',
                                data: data.sales
                            },
                            {
                                name: 'الطلبات',
                                data: data.orders
                            }
                        ],
                        xaxis: {
                            categories: data.dates
                        },
                        chart: {
                            animations: {
                                enabled: true
                            }
                        }
                    });

                    // حفظ الإعدادات
                    this.saveSettings();
                })
                .catch(error => {
                    console.error('خطأ في جلب بيانات المبيعات:', error);

                    // عرض إشعار
                    if (this.options.notificationsEnabled && window.systemManager) {
                        window.systemManager.showNotification(
                            'خطأ في الرسم البياني',
                            'حدث خطأ أثناء جلب بيانات المبيعات',
                            'error'
                        );
                    }
                });
        }
    }

    /**
     * تبديل نوع الرسم البياني
     */
    toggleChartType() {
        console.log('تبديل نوع الرسم البياني');

        // تبديل نوع الرسم البياني
        if (this.state.charts.salesChart) {
            // الحصول على نوع الرسم البياني الحالي
            const currentType = this.state.charts.salesChart.w.config.chart.type;

            // تحديد النوع الجديد
            const newType = currentType === 'area' ? 'bar' : 'area';

            // تحديث أيقونة الزر
            const chartTypeToggle = document.getElementById('chart-type-toggle');
            if (chartTypeToggle) {
                chartTypeToggle.innerHTML = newType === 'area' ?
                    '<i class="ri-bar-chart-2-line"></i>' :
                    '<i class="ri-line-chart-line"></i>';
            }

            // تحديث نوع الرسم البياني
            this.state.charts.salesChart.updateOptions({
                chart: {
                    type: newType
                },
                stroke: {
                    curve: newType === 'area' ? 'smooth' : 'straight',
                    width: newType === 'area' ? [3, 2] : [0, 0]
                },
                fill: {
                    type: newType === 'area' ? 'gradient' : 'solid',
                    opacity: newType === 'area' ? [0.7, 0.5] : 1
                }
            });

            // حفظ الإعدادات
            this.state.chartType = newType;
            this.saveSettings();
        }
    }

    /**
     * تطبيق وضع الدارك مود
     * @param {boolean} isDarkMode - حالة وضع الدارك مود
     */
    applyDarkMode(isDarkMode = true) {
        console.log(`تطبيق وضع الدارك مود: ${isDarkMode}`);

        // تحديث حالة الدارك مود
        this.options.darkMode = isDarkMode;

        // تحديث سمة الرسوم البيانية
        Object.values(this.state.charts).forEach(chart => {
            this.updateChartTheme(chart, isDarkMode);
        });

        // حفظ الإعدادات
        this.saveSettings();
    }

    /**
     * تحميل إعدادات لوحة التحكم
     */
    loadSettings() {
        if (this.options.saveSettings) {
            const savedSettings = localStorage.getItem(this.options.settingsKey);
            if (savedSettings) {
                try {
                    const settings = JSON.parse(savedSettings);

                    // تطبيق الإعدادات المحفوظة
                    if (settings.activeSection) {
                        this.state.activeSection = settings.activeSection;

                        // تحديث العنصر النشط في القائمة
                        const sidebarItems = document.querySelectorAll('.sidebar-menu-item');
                        sidebarItems.forEach(item => {
                            const link = item.querySelector('a');
                            if (link && link.getAttribute('href') === `#${settings.activeSection}`) {
                                item.classList.add('active');
                            } else {
                                item.classList.remove('active');
                            }
                        });
                    }

                    if (settings.visibleSections) {
                        this.state.visibleSections = settings.visibleSections;
                    }

                    if (settings.chartPeriod) {
                        const chartPeriodSelect = document.getElementById('chart-period');
                        if (chartPeriodSelect) {
                            chartPeriodSelect.value = settings.chartPeriod;
                        }
                    }

                    console.log('تم تحميل إعدادات لوحة التحكم');
                } catch (error) {
                    console.error('خطأ في تحليل إعدادات لوحة التحكم:', error);
                }
            }
        }
    }

    /**
     * حفظ إعدادات لوحة التحكم
     */
    saveSettings() {
        if (this.options.saveSettings) {
            const settings = {
                activeSection: this.state.activeSection,
                visibleSections: this.state.visibleSections,
                chartPeriod: document.getElementById('chart-period')?.value,
                chartType: this.state.chartType,
                darkMode: this.options.darkMode,
                lastUpdate: this.state.lastUpdate ? this.state.lastUpdate.toISOString() : null
            };

            localStorage.setItem(this.options.settingsKey, JSON.stringify(settings));
            console.log('تم حفظ إعدادات لوحة التحكم');

            // عرض إشعار
            if (this.options.notificationsEnabled && window.systemManager) {
                window.systemManager.showNotification(
                    'تم الحفظ',
                    'تم حفظ إعدادات لوحة التحكم بنجاح',
                    'success',
                    3000
                );
            }
        }
    }
}

// تصدير الكلاس للاستخدام العالمي
window.DashboardPro = DashboardPro;

// تهيئة لوحة التحكم المتقدمة عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', () => {
    if (document.querySelector('.dashboard-container')) {
        window.dashboardPro = new DashboardPro({
            darkMode: document.documentElement.classList.contains('dark-mode')
        });
    }
});
