<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إيصال #{{ order.invoice_number }}</title>
    <style>
        @page {
            {% if settings.receipt.receipt_size == '58mm' %}
            size: 58mm auto;
            {% elif settings.receipt.receipt_size == '80mm' %}
            size: 80mm auto;
            {% elif settings.receipt.receipt_size == 'a5' %}
            size: A5;
            {% elif settings.receipt.receipt_size == 'a4' %}
            size: A4;
            {% elif settings.receipt.receipt_size == 'custom' %}
            size: {{ settings.receipt.custom_width }}mm {{ settings.receipt.custom_height }}mm;
            {% else %}
            size: 80mm auto;
            {% endif %}
            margin: 0;
        }

        @media print {
            body {
                margin: 0;
                padding: 0;
                font-family: 'Cairo', sans-serif;
                {% if settings.receipt.receipt_size == '58mm' %}
                width: 58mm;
                {% elif settings.receipt.receipt_size == '80mm' %}
                width: 80mm;
                {% elif settings.receipt.receipt_size == 'a5' %}
                width: 148mm;
                {% elif settings.receipt.receipt_size == 'a4' %}
                width: 210mm;
                {% elif settings.receipt.receipt_size == 'custom' %}
                width: {{ settings.receipt.custom_width }}mm;
                {% else %}
                width: 80mm;
                {% endif %}
            }

            .no-print {
                display: none !important;
            }

            .receipt-container {
                {% if settings.receipt.receipt_size == '58mm' %}
                width: 52mm;
                padding: 2mm 3mm;
                {% elif settings.receipt.receipt_size == '80mm' %}
                width: 72mm;
                padding: 2mm 4mm;
                {% elif settings.receipt.receipt_size == 'a5' %}
                width: 138mm;
                padding: 5mm;
                {% elif settings.receipt.receipt_size == 'a4' %}
                width: 190mm;
                padding: 10mm;
                {% elif settings.receipt.receipt_size == 'custom' %}
                width: {{ settings.receipt.custom_width - 8 }}mm;
                padding: 4mm;
                {% else %}
                width: 72mm;
                padding: 2mm 4mm;
                {% endif %}
                box-shadow: none;
                border: none;
            }
        }

        body {
            font-family: 'Cairo', sans-serif;
            background-color: #f3f4f6;
            margin: 0;
            padding: 20px;
            color: #1f2937;
            display: flex;
            justify-content: center;
        }

        .receipt-container {
            width: 80mm;
            background-color: white;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            padding: 10mm 5mm;
        }

        .receipt-header {
            text-align: center;
            margin-bottom: 10mm;
        }

        .store-logo {
            max-width: 60mm;
            max-height: 20mm;
            margin-bottom: 3mm;
        }

        .store-name {
            font-size: 6mm;
            font-weight: bold;
            margin: 0 0 2mm 0;
        }

        .store-contact {
            font-size: 3mm;
            margin: 0 0 1mm 0;
        }

        .receipt-title {
            font-size: 4mm;
            font-weight: bold;
            text-align: center;
            margin: 5mm 0;
            border-top: 1px dashed #000;
            border-bottom: 1px dashed #000;
            padding: 2mm 0;
        }

        .receipt-info {
            font-size: 3mm;
            margin-bottom: 5mm;
        }

        .receipt-info p {
            margin: 1mm 0;
        }

        .items-table {
            width: 100%;
            border-collapse: collapse;
            font-size: 3mm;
            margin-bottom: 5mm;
        }

        .items-table th {
            border-bottom: 1px solid #000;
            padding: 1mm 0;
            text-align: right;
        }

        .items-table td {
            padding: 1mm 0;
            text-align: right;
        }

        .items-table .item-total {
            text-align: left;
        }

        .receipt-summary {
            font-size: 3mm;
            margin-bottom: 5mm;
            border-top: 1px dashed #000;
            padding-top: 2mm;
        }

        .summary-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 1mm;
        }

        .summary-row.total {
            font-size: 4mm;
            font-weight: bold;
            border-top: 1px dashed #000;
            padding-top: 2mm;
            margin-top: 2mm;
        }

        .receipt-footer {
            text-align: center;
            font-size: 3mm;
            margin-top: 5mm;
            border-top: 1px dashed #000;
            padding-top: 3mm;
        }

        .barcode {
            text-align: center;
            margin: 3mm 0;
        }

        .barcode img {
            max-width: 50mm;
            max-height: 15mm;
        }

        .print-button {
            display: block;
            margin: 20px auto;
            padding: 10px 20px;
            background-color: #3b82f6;
            color: white;
            border: none;
            border-radius: 4px;
            font-size: 16px;
            cursor: pointer;
            transition: background-color 0.3s;
        }

        .print-button:hover {
            background-color: #2563eb;
        }
    </style>
</head>
<body>
    <div class="receipt-container">
        <div class="receipt-header">
            {% if settings.logo %}
            <img src="{{ url_for('static', filename='uploads/' + settings.logo) }}" alt="شعار المتجر" class="store-logo">
            {% endif %}
            <h1 class="store-name">{{ settings.business.name }}</h1>
            {% if settings.receipt.show_tax_number and settings.business.tax_number %}
            <p class="store-contact">الرقم الضريبي: {{ settings.business.tax_number }}</p>
            {% endif %}
            {% if settings.receipt.show_address and settings.business.address %}
            <p class="store-contact">{{ settings.business.address }}</p>
            {% endif %}
            {% if settings.receipt.show_phone and settings.business.phone %}
            <p class="store-contact">هاتف: {{ settings.business.phone }}</p>
            {% endif %}
        </div>

        <div class="receipt-title">إيصال مبيعات</div>

        <div class="receipt-info">
            <p><strong>رقم الفاتورة:</strong> {{ order.invoice_number }}</p>
            <p><strong>التاريخ:</strong> {{ order.created_at.strftime('%Y-%m-%d %H:%M') }}</p>
            {% if current_user %}
            <p><strong>الكاشير:</strong> {{ current_user.username }}</p>
            {% endif %}
            {% if order.customer %}
            <p><strong>العميل:</strong> {{ order.customer.name }}</p>
            {% else %}
            <p><strong>العميل:</strong> عميل نقدي</p>
            {% endif %}
            {% if order.warehouse %}
            <p><strong>المخزن:</strong> {{ order.warehouse.name }}</p>
            {% endif %}
        </div>

        <table class="items-table">
            <thead>
                <tr>
                    <th>المنتج</th>
                    <th>الكمية</th>
                    <th>السعر</th>
                    <th>الإجمالي</th>
                </tr>
            </thead>
            <tbody>
                {% for item in order.items %}
                <tr>
                    <td>{{ item.product.name }}</td>
                    <td>{{ item.quantity }}</td>
                    <td>{{ item.price }}</td>
                    <td class="item-total">{{ item.total }}</td>
                </tr>
                {% endfor %}
            </tbody>
        </table>

        <div class="receipt-summary">
            <div class="summary-row">
                <span>المجموع:</span>
                <span>{{ order.subtotal }} ج.م</span>
            </div>
            <div class="summary-row">
                <span>الخصم:</span>
                <span>{{ order.discount }} ج.م</span>
            </div>
            <div class="summary-row">
                <span>الضريبة ({{ order.tax_percentage }}%):</span>
                <span>{{ order.tax }} ج.م</span>
            </div>
            <div class="summary-row total">
                <span>الإجمالي:</span>
                <span>{{ order.total }} ج.م</span>
            </div>
            <div class="summary-row">
                <span>طريقة الدفع:</span>
                <span>
                    {% if order.payment_method == 'cash' %}
                    نقدي
                    {% elif order.payment_method == 'card' %}
                    بطاقة ائتمان
                    {% elif order.payment_method == 'credit' %}
                    آجل
                    {% else %}
                    {{ order.payment_method }}
                    {% endif %}
                </span>
            </div>
        </div>

        {% if settings.receipt.show_barcode %}
        <div class="barcode">
            <img src="data:image/png;base64,{{ barcode_image }}" alt="Barcode">
        </div>
        {% endif %}

        {% if settings.receipt.show_customer_signature %}
        <div class="customer-signature">
            <p class="text-center">توقيع العميل</p>
            <div class="signature-box"></div>
        </div>
        <style>
            .customer-signature {
                margin: 10mm 0;
                text-align: center;
            }
            .signature-box {
                border: 1px dashed #000;
                height: 15mm;
                margin: 2mm 0;
            }
        </style>
        {% endif %}

        <div class="receipt-footer">
            <p>{{ settings.receipt.footer or 'شكراً لتعاملكم معنا' }}</p>
            <p>Powered By ENG/ Fouad Saber Tel: 01020073527</p>
        </div>
    </div>

    <button class="print-button no-print" onclick="window.print()">طباعة الإيصال</button>

    <script>
        // Auto print when page loads
        window.onload = function() {
            // Delay printing to ensure everything is loaded
            setTimeout(function() {
                window.print();
            }, 500);
        };
    </script>
</body>
</html>