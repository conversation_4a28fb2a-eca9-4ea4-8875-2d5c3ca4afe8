"""
Nobara POS System - Products Routes
نظام نوبارا لنقاط البيع - مسارات المنتجات
"""

from flask import render_template, request, jsonify, redirect, url_for, flash
from flask_login import login_required, current_user
from app.products import bp
from app.models import Product, Category, InventoryItem, Warehouse, db
from datetime import datetime
import logging
import uuid

logger = logging.getLogger(__name__)

@bp.route('/')
@login_required
def index():
    """صفحة قائمة المنتجات"""
    try:
        page = request.args.get('page', 1, type=int)
        per_page = 20
        
        # فلاتر البحث
        search = request.args.get('search', '').strip()
        category_id = request.args.get('category_id', type=int)
        status = request.args.get('status', 'all')
        
        # بناء الاستعلام
        query = Product.query
        
        if search:
            query = query.filter(
                db.or_(
                    Product.name.contains(search),
                    Product.sku.contains(search),
                    Product.barcode.contains(search)
                )
            )
        
        if category_id:
            query = query.filter_by(category_id=category_id)
        
        if status == 'active':
            query = query.filter_by(is_active=True)
        elif status == 'inactive':
            query = query.filter_by(is_active=False)
        elif status == 'low_stock':
            # المنتجات منخفضة المخزون
            low_stock_products = []
            all_products = query.filter_by(is_active=True, track_inventory=True).all()
            for product in all_products:
                if product.total_stock <= product.min_stock_level:
                    low_stock_products.append(product.id)
            query = query.filter(Product.id.in_(low_stock_products))
        
        # ترتيب النتائج
        sort_by = request.args.get('sort', 'name')
        if sort_by == 'name':
            query = query.order_by(Product.name)
        elif sort_by == 'price':
            query = query.order_by(Product.selling_price.desc())
        elif sort_by == 'created':
            query = query.order_by(Product.created_at.desc())
        
        # تطبيق التصفح
        products = query.paginate(
            page=page, per_page=per_page, error_out=False
        )
        
        # جلب الفئات للفلتر
        categories = Category.query.filter_by(is_active=True).order_by(Category.name).all()
        
        # إحصائيات
        stats = {
            'total_products': Product.query.filter_by(is_active=True).count(),
            'total_categories': Category.query.filter_by(is_active=True).count(),
            'low_stock_count': len([p for p in Product.query.filter_by(is_active=True, track_inventory=True).all() 
                                   if p.total_stock <= p.min_stock_level]),
            'out_of_stock_count': len([p for p in Product.query.filter_by(is_active=True, track_inventory=True).all() 
                                      if p.total_stock <= 0])
        }
        
        return render_template('products/index.html',
                             products=products,
                             categories=categories,
                             stats=stats,
                             search=search,
                             category_id=category_id,
                             status=status,
                             sort_by=sort_by)
        
    except Exception as e:
        logger.error(f'Error loading products: {e}')
        flash('حدث خطأ أثناء تحميل المنتجات', 'error')
        return redirect(url_for('main.dashboard'))

@bp.route('/create', methods=['GET', 'POST'])
@login_required
def create():
    """إنشاء منتج جديد"""
    if request.method == 'POST':
        try:
            # جلب البيانات من النموذج
            name = request.form.get('name', '').strip()
            name_ar = request.form.get('name_ar', '').strip()
            description = request.form.get('description', '').strip()
            sku = request.form.get('sku', '').strip()
            barcode = request.form.get('barcode', '').strip()
            category_id = request.form.get('category_id', type=int)
            
            # الأسعار
            cost_price = float(request.form.get('cost_price', 0))
            selling_price = float(request.form.get('selling_price', 0))
            min_price = request.form.get('min_price')
            min_price = float(min_price) if min_price else None
            
            # المخزون
            track_inventory = bool(request.form.get('track_inventory'))
            min_stock_level = int(request.form.get('min_stock_level', 0))
            max_stock_level = request.form.get('max_stock_level')
            max_stock_level = int(max_stock_level) if max_stock_level else None
            
            # تفاصيل أخرى
            unit = request.form.get('unit', 'piece')
            weight = request.form.get('weight')
            weight = float(weight) if weight else None
            dimensions = request.form.get('dimensions', '').strip()
            tax_rate = float(request.form.get('tax_rate', 0.14))
            
            # التحقق من البيانات المطلوبة
            if not name:
                flash('اسم المنتج مطلوب', 'error')
                return render_template('products/form.html', categories=Category.query.filter_by(is_active=True).all())
            
            if selling_price <= 0:
                flash('سعر البيع يجب أن يكون أكبر من صفر', 'error')
                return render_template('products/form.html', categories=Category.query.filter_by(is_active=True).all())
            
            # التحقق من تفرد SKU و Barcode
            if sku and Product.query.filter_by(sku=sku).first():
                flash('رمز المنتج (SKU) موجود مسبقاً', 'error')
                return render_template('products/form.html', categories=Category.query.filter_by(is_active=True).all())
            
            if barcode and Product.query.filter_by(barcode=barcode).first():
                flash('الباركود موجود مسبقاً', 'error')
                return render_template('products/form.html', categories=Category.query.filter_by(is_active=True).all())
            
            # إنشاء المنتج
            product = Product(
                name=name,
                name_ar=name_ar,
                description=description,
                sku=sku or f"PRD-{str(uuid.uuid4())[:8].upper()}",
                barcode=barcode,
                category_id=category_id,
                cost_price=cost_price,
                selling_price=selling_price,
                min_price=min_price,
                track_inventory=track_inventory,
                min_stock_level=min_stock_level,
                max_stock_level=max_stock_level,
                unit=unit,
                weight=weight,
                dimensions=dimensions,
                tax_rate=tax_rate
            )
            
            db.session.add(product)
            db.session.flush()  # للحصول على ID المنتج
            
            # إنشاء عناصر المخزون للمخازن النشطة
            if track_inventory:
                warehouses = Warehouse.query.filter_by(is_active=True).all()
                for warehouse in warehouses:
                    inventory_item = InventoryItem(
                        product_id=product.id,
                        warehouse_id=warehouse.id,
                        quantity=0
                    )
                    db.session.add(inventory_item)
            
            db.session.commit()
            
            logger.info(f'Product created: {product.name} by user {current_user.username}')
            flash('تم إنشاء المنتج بنجاح', 'success')
            
            return redirect(url_for('products.view', id=product.id))
            
        except Exception as e:
            db.session.rollback()
            logger.error(f'Error creating product: {e}')
            flash('حدث خطأ أثناء إنشاء المنتج', 'error')
    
    # جلب الفئات للنموذج
    categories = Category.query.filter_by(is_active=True).order_by(Category.name).all()
    
    return render_template('products/form.html', 
                         product=None, 
                         categories=categories,
                         action='create')

@bp.route('/<int:id>')
@login_required
def view(id):
    """عرض تفاصيل المنتج"""
    try:
        product = Product.query.get_or_404(id)
        
        # جلب المخزون في جميع المخازن
        inventory_items = InventoryItem.query.filter_by(product_id=id).join(Warehouse).all()
        
        # إحصائيات المنتج
        stats = {
            'total_stock': product.total_stock,
            'profit_margin': product.profit_margin,
            'warehouses_count': len(inventory_items)
        }
        
        return render_template('products/view.html',
                             product=product,
                             inventory_items=inventory_items,
                             stats=stats)
        
    except Exception as e:
        logger.error(f'Error loading product: {e}')
        flash('حدث خطأ أثناء تحميل المنتج', 'error')
        return redirect(url_for('products.index'))

@bp.route('/<int:id>/edit', methods=['GET', 'POST'])
@login_required
def edit(id):
    """تعديل المنتج"""
    product = Product.query.get_or_404(id)
    
    if request.method == 'POST':
        try:
            # تحديث البيانات
            product.name = request.form.get('name', '').strip()
            product.name_ar = request.form.get('name_ar', '').strip()
            product.description = request.form.get('description', '').strip()
            product.category_id = request.form.get('category_id', type=int)
            
            # الأسعار
            product.cost_price = float(request.form.get('cost_price', 0))
            product.selling_price = float(request.form.get('selling_price', 0))
            min_price = request.form.get('min_price')
            product.min_price = float(min_price) if min_price else None
            
            # المخزون
            product.track_inventory = bool(request.form.get('track_inventory'))
            product.min_stock_level = int(request.form.get('min_stock_level', 0))
            max_stock_level = request.form.get('max_stock_level')
            product.max_stock_level = int(max_stock_level) if max_stock_level else None
            
            # تفاصيل أخرى
            product.unit = request.form.get('unit', 'piece')
            weight = request.form.get('weight')
            product.weight = float(weight) if weight else None
            product.dimensions = request.form.get('dimensions', '').strip()
            product.tax_rate = float(request.form.get('tax_rate', 0.14))
            product.is_active = bool(request.form.get('is_active'))
            
            product.updated_at = datetime.utcnow()
            
            db.session.commit()
            
            logger.info(f'Product updated: {product.name} by user {current_user.username}')
            flash('تم تحديث المنتج بنجاح', 'success')
            
            return redirect(url_for('products.view', id=product.id))
            
        except Exception as e:
            db.session.rollback()
            logger.error(f'Error updating product: {e}')
            flash('حدث خطأ أثناء تحديث المنتج', 'error')
    
    # جلب الفئات للنموذج
    categories = Category.query.filter_by(is_active=True).order_by(Category.name).all()
    
    return render_template('products/form.html',
                         product=product,
                         categories=categories,
                         action='edit')

@bp.route('/<int:id>/delete', methods=['POST'])
@login_required
def delete(id):
    """حذف المنتج"""
    try:
        product = Product.query.get_or_404(id)
        
        # التحقق من وجود مبيعات للمنتج
        if product.sale_items.count() > 0:
            flash('لا يمكن حذف المنتج لوجود مبيعات مرتبطة به', 'error')
            return redirect(url_for('products.view', id=id))
        
        # حذف عناصر المخزون
        InventoryItem.query.filter_by(product_id=id).delete()
        
        # حذف المنتج
        db.session.delete(product)
        db.session.commit()
        
        logger.info(f'Product deleted: {product.name} by user {current_user.username}')
        flash('تم حذف المنتج بنجاح', 'success')
        
        return redirect(url_for('products.index'))
        
    except Exception as e:
        db.session.rollback()
        logger.error(f'Error deleting product: {e}')
        flash('حدث خطأ أثناء حذف المنتج', 'error')
        return redirect(url_for('products.view', id=id))

@bp.route('/api/search')
@login_required
def api_search():
    """API البحث في المنتجات"""
    try:
        query = request.args.get('q', '').strip()
        limit = request.args.get('limit', 20, type=int)
        
        if not query:
            return jsonify([])
        
        products = Product.query.filter(
            Product.is_active == True,
            db.or_(
                Product.name.contains(query),
                Product.sku.contains(query),
                Product.barcode.contains(query)
            )
        ).limit(limit).all()
        
        result = []
        for product in products:
            result.append({
                'id': product.id,
                'name': product.name,
                'sku': product.sku,
                'barcode': product.barcode,
                'selling_price': float(product.selling_price),
                'stock': product.total_stock,
                'unit': product.unit
            })
        
        return jsonify(result)
        
    except Exception as e:
        logger.error(f'Error in product search API: {e}')
        return jsonify({'error': str(e)}), 500
