{% extends 'layout.html' %}

{% block title %}تقارير المخزون{% endblock %}

{% block page_content %}
<div class="container mx-auto px-4 py-8">
    <div class="mb-6 flex justify-between items-center">
        <div>
            <h1 class="text-2xl font-bold">تقارير المخزون</h1>
            <p class="text-gray-600">عرض وتحليل بيانات المخزون</p>
        </div>
        <div class="flex space-x-2 space-x-reverse">
            <a href="{{ url_for('warehouses.index') }}" class="bg-gray-100 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-200 transition-all">
                <i class="ri-arrow-right-line ml-1"></i>العودة للمخازن
            </a>
        </div>
    </div>

    <!-- فلاتر التقارير -->
    <div class="bg-white rounded-lg shadow p-4 mb-6">
        <form method="GET" action="{{ url_for('warehouses.reports') }}" class="flex flex-wrap items-center gap-4">
            <div>
                <label class="block text-gray-700 mb-2">نوع التقرير</label>
                <select name="report_type" id="report_type" class="border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500">
                    <option value="inventory_status" {% if report_type == 'inventory_status' %}selected{% endif %}>حالة المخزون</option>
                    <option value="inventory_value" {% if report_type == 'inventory_value' %}selected{% endif %}>قيمة المخزون</option>
                    <option value="inventory_movement" {% if report_type == 'inventory_movement' %}selected{% endif %}>حركة المخزون</option>
                    <option value="low_stock" {% if report_type == 'low_stock' %}selected{% endif %}>المخزون المنخفض</option>
                    <option value="out_of_stock" {% if report_type == 'out_of_stock' %}selected{% endif %}>نفذ من المخزون</option>
                </select>
            </div>
            <div>
                <label class="block text-gray-700 mb-2">المخزن</label>
                <select name="warehouse_id" id="warehouse_id" class="border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500">
                    <option value="">جميع المخازن</option>
                    {% for warehouse in warehouses %}
                    <option value="{{ warehouse.id }}" {% if warehouse_id == warehouse.id|string %}selected{% endif %}>{{ warehouse.name }}</option>
                    {% endfor %}
                </select>
            </div>
            <div id="date_filters" class="{% if report_type != 'inventory_movement' %}hidden{% endif %} flex gap-2">
                <div>
                    <label class="block text-gray-700 mb-2">من تاريخ</label>
                    <input type="date" name="date_from" value="{{ date_from }}" class="border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500">
                </div>
                <div>
                    <label class="block text-gray-700 mb-2">إلى تاريخ</label>
                    <input type="date" name="date_to" value="{{ date_to }}" class="border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500">
                </div>
            </div>
            <div class="self-end">
                <button type="submit" class="bg-blue-500 text-white px-4 py-2 rounded-md hover:bg-blue-600 transition-all">
                    عرض التقرير
                </button>
                <button type="button" id="exportBtn" class="bg-green-500 text-white px-4 py-2 rounded-md hover:bg-green-600 transition-all">
                    <i class="ri-file-excel-line ml-1"></i>تصدير
                </button>
            </div>
        </form>
    </div>

    <!-- محتوى التقرير -->
    <div class="bg-white rounded-lg shadow overflow-hidden">
        <div class="p-4 bg-gray-50 border-b flex justify-between items-center">
            <h2 class="text-xl font-semibold">{{ report_data.title }}</h2>
            <div class="text-sm text-gray-500">
                {% if warehouse_id %}
                    {% for warehouse in warehouses %}
                        {% if warehouse.id|string == warehouse_id %}
                            المخزن: {{ warehouse.name }}
                        {% endif %}
                    {% endfor %}
                {% else %}
                    جميع المخازن
                {% endif %}

                {% if report_type == 'inventory_movement' and (date_from or date_to) %}
                    | الفترة:
                    {% if date_from %}من {{ date_from }}{% endif %}
                    {% if date_to %} إلى {{ date_to }}{% endif %}
                {% endif %}
            </div>
        </div>

        <!-- إحصائيات التقرير -->
        <div class="p-4 border-b">
            {% if report_type == 'inventory_status' %}
                <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                    <div class="bg-blue-50 p-3 rounded-lg">
                        <div class="text-sm text-gray-500">إجمالي المنتجات</div>
                        <div class="text-2xl font-bold">{{ report_data.stats.total_items }}</div>
                    </div>
                    <div class="bg-green-50 p-3 rounded-lg">
                        <div class="text-sm text-gray-500">متوفر في المخزون</div>
                        <div class="text-2xl font-bold text-green-600">{{ report_data.stats.in_stock }}</div>
                    </div>
                    <div class="bg-yellow-50 p-3 rounded-lg">
                        <div class="text-sm text-gray-500">مخزون منخفض</div>
                        <div class="text-2xl font-bold text-yellow-600">{{ report_data.stats.low_stock }}</div>
                    </div>
                    <div class="bg-red-50 p-3 rounded-lg">
                        <div class="text-sm text-gray-500">نفذ من المخزون</div>
                        <div class="text-2xl font-bold text-red-600">{{ report_data.stats.out_of_stock }}</div>
                    </div>
                </div>
            {% elif report_type == 'inventory_value' %}
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div class="bg-green-50 p-3 rounded-lg">
                        <div class="text-sm text-gray-500">إجمالي قيمة المخزون</div>
                        <div class="text-2xl font-bold text-green-600">{{ report_data.stats.total_value|round(2) }} ج.م</div>
                    </div>
                    <div class="bg-blue-50 p-3 rounded-lg">
                        <div class="text-sm text-gray-500">عدد المنتجات</div>
                        <div class="text-2xl font-bold">{{ report_data.items|length }}</div>
                    </div>
                </div>

                {% if report_data.stats.warehouse_values %}
                <div class="mt-4">
                    <h3 class="text-lg font-semibold mb-2">توزيع قيمة المخزون حسب المخازن</h3>
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                        {% for warehouse_name, value in report_data.stats.warehouse_values.items() %}
                        <div class="bg-indigo-50 p-3 rounded-lg">
                            <div class="text-sm text-gray-500">{{ warehouse_name }}</div>
                            <div class="text-xl font-bold text-indigo-600">{{ value|round(2) }} ج.م</div>
                        </div>
                        {% endfor %}
                    </div>
                </div>
                {% endif %}
            {% elif report_type == 'inventory_movement' %}
                <div class="grid grid-cols-1 md:grid-cols-5 gap-4">
                    <div class="bg-blue-50 p-3 rounded-lg">
                        <div class="text-sm text-gray-500">إجمالي الحركات</div>
                        <div class="text-2xl font-bold">{{ report_data.stats.total_movements }}</div>
                    </div>
                    <div class="bg-green-50 p-3 rounded-lg">
                        <div class="text-sm text-gray-500">إضافة</div>
                        <div class="text-2xl font-bold text-green-600">{{ report_data.stats.add_movements }}</div>
                    </div>
                    <div class="bg-red-50 p-3 rounded-lg">
                        <div class="text-sm text-gray-500">سحب</div>
                        <div class="text-2xl font-bold text-red-600">{{ report_data.stats.remove_movements }}</div>
                    </div>
                    <div class="bg-yellow-50 p-3 rounded-lg">
                        <div class="text-sm text-gray-500">تحديث</div>
                        <div class="text-2xl font-bold text-yellow-600">{{ report_data.stats.update_movements }}</div>
                    </div>
                    <div class="bg-purple-50 p-3 rounded-lg">
                        <div class="text-sm text-gray-500">نقل</div>
                        <div class="text-2xl font-bold text-purple-600">{{ report_data.stats.transfer_movements }}</div>
                    </div>
                </div>
            {% elif report_type == 'low_stock' %}
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div class="bg-yellow-50 p-3 rounded-lg">
                        <div class="text-sm text-gray-500">عدد المنتجات منخفضة المخزون</div>
                        <div class="text-2xl font-bold text-yellow-600">{{ report_data.stats.total_items }}</div>
                    </div>
                    <div class="bg-blue-50 p-3 rounded-lg">
                        <div class="text-sm text-gray-500">إجمالي الكمية المطلوبة</div>
                        <div class="text-2xl font-bold">{{ report_data.stats.total_needed_quantity }}</div>
                    </div>
                </div>
            {% elif report_type == 'out_of_stock' %}
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div class="bg-red-50 p-3 rounded-lg">
                        <div class="text-sm text-gray-500">عدد المنتجات النافذة من المخزون</div>
                        <div class="text-2xl font-bold text-red-600">{{ report_data.stats.total_items }}</div>
                    </div>
                </div>

                {% if report_data.stats.category_counts %}
                <div class="mt-4">
                    <h3 class="text-lg font-semibold mb-2">توزيع المنتجات النافذة حسب التصنيفات</h3>
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                        {% for category_name, count in report_data.stats.category_counts.items() %}
                        <div class="bg-indigo-50 p-3 rounded-lg">
                            <div class="text-sm text-gray-500">{{ category_name }}</div>
                            <div class="text-xl font-bold text-indigo-600">{{ count }}</div>
                        </div>
                        {% endfor %}
                    </div>
                </div>
                {% endif %}
            {% endif %}
        </div>

        <!-- جدول التقرير -->
        <div class="overflow-x-auto">
            <table class="min-w-full">
                <thead>
                    <tr class="bg-gray-100">
                        {% if report_type == 'inventory_status' %}
                            <th class="px-6 py-3 border-b text-right">المنتج</th>
                            <th class="px-6 py-3 border-b text-right">الباركود</th>
                            <th class="px-6 py-3 border-b text-right">التصنيف</th>
                            <th class="px-6 py-3 border-b text-right">المخزن</th>
                            <th class="px-6 py-3 border-b text-right">الكمية</th>
                            <th class="px-6 py-3 border-b text-right">الحد الأدنى</th>
                            <th class="px-6 py-3 border-b text-right">الحالة</th>
                        {% elif report_type == 'inventory_value' %}
                            <th class="px-6 py-3 border-b text-right">المنتج</th>
                            <th class="px-6 py-3 border-b text-right">الباركود</th>
                            <th class="px-6 py-3 border-b text-right">التصنيف</th>
                            <th class="px-6 py-3 border-b text-right">المخزن</th>
                            <th class="px-6 py-3 border-b text-right">الكمية</th>
                            <th class="px-6 py-3 border-b text-right">السعر</th>
                            <th class="px-6 py-3 border-b text-right">القيمة</th>
                        {% elif report_type == 'inventory_movement' %}
                            <th class="px-6 py-3 border-b text-right">التاريخ</th>
                            <th class="px-6 py-3 border-b text-right">المنتج</th>
                            <th class="px-6 py-3 border-b text-right">المخزن</th>
                            <th class="px-6 py-3 border-b text-right">نوع الحركة</th>
                            <th class="px-6 py-3 border-b text-right">الكمية قبل</th>
                            <th class="px-6 py-3 border-b text-right">الكمية بعد</th>
                            <th class="px-6 py-3 border-b text-right">التغيير</th>
                            <th class="px-6 py-3 border-b text-right">المستخدم</th>
                            <th class="px-6 py-3 border-b text-right">الملاحظات</th>
                        {% elif report_type == 'low_stock' %}
                            <th class="px-6 py-3 border-b text-right">المنتج</th>
                            <th class="px-6 py-3 border-b text-right">الباركود</th>
                            <th class="px-6 py-3 border-b text-right">التصنيف</th>
                            <th class="px-6 py-3 border-b text-right">المخزن</th>
                            <th class="px-6 py-3 border-b text-right">الكمية</th>
                            <th class="px-6 py-3 border-b text-right">الحد الأدنى</th>
                            <th class="px-6 py-3 border-b text-right">الكمية المطلوبة</th>
                            <th class="px-6 py-3 border-b text-right">السعر</th>
                        {% elif report_type == 'out_of_stock' %}
                            <th class="px-6 py-3 border-b text-right">المنتج</th>
                            <th class="px-6 py-3 border-b text-right">الباركود</th>
                            <th class="px-6 py-3 border-b text-right">التصنيف</th>
                            <th class="px-6 py-3 border-b text-right">المخزن</th>
                            <th class="px-6 py-3 border-b text-right">الحد الأدنى</th>
                            <th class="px-6 py-3 border-b text-right">السعر</th>
                        {% endif %}
                    </tr>
                </thead>
                <tbody>
                    {% if report_data.items %}
                        {% for item in report_data.items %}
                            <tr class="hover:bg-gray-50">
                                {% if report_type == 'inventory_status' %}
                                    <td class="px-6 py-4 border-b font-medium">{{ item.product_name }}</td>
                                    <td class="px-6 py-4 border-b">{{ item.product_code or '-' }}</td>
                                    <td class="px-6 py-4 border-b">{{ item.category_name }}</td>
                                    <td class="px-6 py-4 border-b">{{ item.warehouse_name }}</td>
                                    <td class="px-6 py-4 border-b">{{ item.quantity }}</td>
                                    <td class="px-6 py-4 border-b">{{ item.minimum_stock }}</td>
                                    <td class="px-6 py-4 border-b">
                                        {% if item.status == 'نفذ من المخزون' %}
                                            <span class="bg-red-100 text-red-800 text-xs font-medium px-2.5 py-0.5 rounded">{{ item.status }}</span>
                                        {% elif item.status == 'مخزون منخفض' %}
                                            <span class="bg-yellow-100 text-yellow-800 text-xs font-medium px-2.5 py-0.5 rounded">{{ item.status }}</span>
                                        {% else %}
                                            <span class="bg-green-100 text-green-800 text-xs font-medium px-2.5 py-0.5 rounded">{{ item.status }}</span>
                                        {% endif %}
                                    </td>
                                {% elif report_type == 'inventory_value' %}
                                    <td class="px-6 py-4 border-b font-medium">{{ item.product_name }}</td>
                                    <td class="px-6 py-4 border-b">{{ item.product_code or '-' }}</td>
                                    <td class="px-6 py-4 border-b">{{ item.category_name }}</td>
                                    <td class="px-6 py-4 border-b">{{ item.warehouse_name }}</td>
                                    <td class="px-6 py-4 border-b">{{ item.quantity }}</td>
                                    <td class="px-6 py-4 border-b">{{ item.price }} ج.م</td>
                                    <td class="px-6 py-4 border-b font-medium">{{ item.value|round(2) }} ج.م</td>
                                {% elif report_type == 'inventory_movement' %}
                                    <td class="px-6 py-4 border-b">{{ item.created_at }}</td>
                                    <td class="px-6 py-4 border-b font-medium">{{ item.product_name }}</td>
                                    <td class="px-6 py-4 border-b">{{ item.warehouse_name }}</td>
                                    <td class="px-6 py-4 border-b">
                                        {% if item.movement_type == 'add' %}
                                            <span class="bg-green-100 text-green-800 text-xs font-medium px-2.5 py-0.5 rounded">إضافة</span>
                                        {% elif item.movement_type == 'remove' %}
                                            <span class="bg-red-100 text-red-800 text-xs font-medium px-2.5 py-0.5 rounded">سحب</span>
                                        {% elif item.movement_type == 'update' %}
                                            <span class="bg-yellow-100 text-yellow-800 text-xs font-medium px-2.5 py-0.5 rounded">تحديث</span>
                                        {% elif item.movement_type == 'transfer' %}
                                            <span class="bg-purple-100 text-purple-800 text-xs font-medium px-2.5 py-0.5 rounded">نقل</span>
                                        {% else %}
                                            <span class="bg-gray-100 text-gray-800 text-xs font-medium px-2.5 py-0.5 rounded">{{ item.movement_type }}</span>
                                        {% endif %}
                                    </td>
                                    <td class="px-6 py-4 border-b">{{ item.quantity_before }}</td>
                                    <td class="px-6 py-4 border-b">{{ item.quantity_after }}</td>
                                    <td class="px-6 py-4 border-b font-medium">
                                        {% if item.quantity_change > 0 %}
                                            <span class="text-green-600">+{{ item.quantity_change }}</span>
                                        {% elif item.quantity_change < 0 %}
                                            <span class="text-red-600">{{ item.quantity_change }}</span>
                                        {% else %}
                                            <span>{{ item.quantity_change }}</span>
                                        {% endif %}
                                    </td>
                                    <td class="px-6 py-4 border-b">{{ item.user or '-' }}</td>
                                    <td class="px-6 py-4 border-b">{{ item.notes or '-' }}</td>
                                {% elif report_type == 'low_stock' %}
                                    <td class="px-6 py-4 border-b font-medium">{{ item.product_name }}</td>
                                    <td class="px-6 py-4 border-b">{{ item.product_code or '-' }}</td>
                                    <td class="px-6 py-4 border-b">{{ item.category_name }}</td>
                                    <td class="px-6 py-4 border-b">{{ item.warehouse_name }}</td>
                                    <td class="px-6 py-4 border-b">{{ item.quantity }}</td>
                                    <td class="px-6 py-4 border-b">{{ item.minimum_stock }}</td>
                                    <td class="px-6 py-4 border-b font-medium text-red-600">{{ item.needed_quantity }}</td>
                                    <td class="px-6 py-4 border-b">{{ item.price }} ج.م</td>
                                {% elif report_type == 'out_of_stock' %}
                                    <td class="px-6 py-4 border-b font-medium">{{ item.product_name }}</td>
                                    <td class="px-6 py-4 border-b">{{ item.product_code or '-' }}</td>
                                    <td class="px-6 py-4 border-b">{{ item.category_name }}</td>
                                    <td class="px-6 py-4 border-b">{{ item.warehouse_name }}</td>
                                    <td class="px-6 py-4 border-b">{{ item.minimum_stock }}</td>
                                    <td class="px-6 py-4 border-b">{{ item.price }} ج.م</td>
                                {% endif %}
                            </tr>
                        {% endfor %}
                    {% else %}
                        <tr>
                            <td colspan="10" class="px-6 py-8 text-center text-gray-500">
                                <div class="flex flex-col items-center">
                                    <i class="ri-inbox-line text-4xl mb-2"></i>
                                    <p>لا توجد بيانات للعرض</p>
                                </div>
                            </td>
                        </tr>
                    {% endif %}
                </tbody>
            </table>
        </div>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // إظهار/إخفاء حقول التاريخ حسب نوع التقرير
        const reportTypeSelect = document.getElementById('report_type');
        const dateFilters = document.getElementById('date_filters');

        reportTypeSelect.addEventListener('change', function() {
            if (this.value === 'inventory_movement') {
                dateFilters.classList.remove('hidden');
            } else {
                dateFilters.classList.add('hidden');
            }
        });

        // تصدير التقرير
        document.getElementById('exportBtn').addEventListener('click', function() {
            const reportType = document.getElementById('report_type').value;
            const warehouseId = document.getElementById('warehouse_id').value;
            let url = `/api/warehouses/reports/export-new?report_type=${reportType}`;

            if (warehouseId) {
                url += `&warehouse_id=${warehouseId}`;
            }

            if (reportType === 'inventory_movement') {
                const dateFrom = document.querySelector('input[name="date_from"]').value;
                const dateTo = document.querySelector('input[name="date_to"]').value;

                if (dateFrom) {
                    url += `&date_from=${dateFrom}`;
                }

                if (dateTo) {
                    url += `&date_to=${dateTo}`;
                }
            }

            window.location.href = url;
        });
    });
</script>
{% endblock page_content %}
