from flask import Blueprint, render_template, request, jsonify, flash, redirect, url_for
from flask_login import login_required, current_user
from models import User, Permission, UserPermission
from app import db
from werkzeug.security import generate_password_hash, check_password_hash
from sqlalchemy import func
from datetime import datetime
from permissions_manager import PermissionsManager
from sqlalchemy.exc import IntegrityError

users_blueprint = Blueprint('users', __name__)

@users_blueprint.route('/users')
@login_required
def index():
    # التحقق من صلاحية المستخدم الحالي
    if not current_user.has_permission('users', 'view'):
        flash('ليس لديك صلاحية الوصول إلى هذه الصفحة', 'danger')
        return redirect(url_for('dashboard.home'))

    # الحصول على معلمات البحث والتصفية
    page = request.args.get('page', 1, type=int)
    search = request.args.get('search', '')
    role = request.args.get('role', '')

    # بناء الاستعلام
    query = User.query

    # تطبيق البحث
    if search:
        query = query.filter(
            User.username.ilike(f'%{search}%') |
            User.email.ilike(f'%{search}%') |
            User.full_name.ilike(f'%{search}%')
        )

    # تطبيق التصفية حسب الدور
    if role:
        query = query.filter_by(role=role)

    # الحصول على المستخدمين مع الترقيم
    users = query.order_by(User.created_at.desc()).paginate(page=page, per_page=10)

    # الحصول على إحصائيات المستخدمين
    total_users = User.query.count()
    admin_count = User.query.filter_by(role='admin').count()
    staff_count = User.query.filter_by(role='staff').count()
    recent_users = User.query.order_by(User.created_at.desc()).limit(5).all()

    stats = {
        'total_users': total_users,
        'admin_count': admin_count,
        'staff_count': staff_count,
        'recent_users': recent_users
    }

    return render_template(
        'users/users.html',
        users=users,
        search=search,
        role=role,
        stats=stats,
        current_user=current_user
    )

@users_blueprint.route('/users/create', methods=['GET', 'POST'])
@login_required
def create():
    # التحقق من صلاحية المستخدم الحالي
    if not current_user.has_permission('users', 'add'):
        flash('ليس لديك صلاحية إنشاء مستخدمين جدد', 'danger')
        return redirect(url_for('dashboard.home'))

    # تهيئة الصلاحيات إذا لم تكن موجودة
    PermissionsManager.initialize_permissions()

    if request.method == 'POST':
        try:
            # الحصول على بيانات النموذج
            username = request.form.get('username')
            email = request.form.get('email')
            full_name = request.form.get('full_name')
            password = request.form.get('password')
            confirm_password = request.form.get('confirm_password')
            role = request.form.get('role', 'staff')

            # التحقق من البيانات
            if not username or not email or not password:
                flash('جميع الحقول المطلوبة يجب ملؤها', 'danger')
                return render_template('user_form.html', user=None, action='create')

            if password != confirm_password:
                flash('كلمات المرور غير متطابقة', 'danger')
                return render_template('user_form.html', user=None, action='create')

            # التحقق من وجود اسم المستخدم أو البريد الإلكتروني
            if User.query.filter_by(username=username).first():
                flash('اسم المستخدم موجود بالفعل', 'danger')
                return render_template('user_form.html', user=None, action='create')

            if User.query.filter_by(email=email).first():
                flash('البريد الإلكتروني موجود بالفعل', 'danger')
                return render_template('user_form.html', user=None, action='create')

            # إنشاء المستخدم
            user = User(
                username=username,
                email=email,
                full_name=full_name,
                role=role
            )
            user.set_password(password)

            db.session.add(user)
            db.session.commit()

            # إذا كان المستخدم مدير، قم بمنحه جميع الصلاحيات
            if role == 'admin':
                # الحصول على جميع الصلاحيات
                all_permissions = Permission.query.all()

                # منح جميع الصلاحيات للمستخدم
                for permission in all_permissions:
                    user_permission = UserPermission(
                        user_id=user.id,
                        permission_id=permission.id,
                        granted=True
                    )
                    db.session.add(user_permission)

                db.session.commit()
            # إذا كان المستخدم موظف، قم بمنحه صلاحيات العرض فقط
            else:
                # الحصول على صلاحيات العرض فقط
                view_permissions = Permission.query.filter_by(action='view').all()

                # منح صلاحيات العرض للمستخدم
                for permission in view_permissions:
                    user_permission = UserPermission(
                        user_id=user.id,
                        permission_id=permission.id,
                        granted=True
                    )
                    db.session.add(user_permission)

                db.session.commit()

            flash('تم إنشاء المستخدم بنجاح', 'success')
            return redirect(url_for('users.index'))

        except Exception as e:
            db.session.rollback()
            flash(f'حدث خطأ أثناء إنشاء المستخدم: {str(e)}', 'danger')

    return render_template('user_form.html', user=None, action='create')

@users_blueprint.route('/users/<int:id>/edit', methods=['GET', 'POST'])
@login_required
def edit(id):
    # التحقق من صلاحية المستخدم الحالي
    if not current_user.has_permission('users', 'edit'):
        flash('ليس لديك صلاحية تعديل المستخدمين', 'danger')
        return redirect(url_for('dashboard.home'))

    # الحصول على المستخدم
    user = User.query.get_or_404(id)

    if request.method == 'POST':
        try:
            # الحصول على بيانات النموذج
            username = request.form.get('username')
            email = request.form.get('email')
            full_name = request.form.get('full_name')
            password = request.form.get('password')
            confirm_password = request.form.get('confirm_password')
            role = request.form.get('role', 'staff')


            # التحقق من البيانات
            if not username or not email:
                flash('جميع الحقول المطلوبة يجب ملؤها', 'danger')
                return render_template('user_form.html', user=user, action='edit')

            # التحقق من وجود اسم المستخدم أو البريد الإلكتروني
            username_exists = User.query.filter(User.username == username, User.id != id).first()
            if username_exists:
                flash('اسم المستخدم موجود بالفعل', 'danger')
                return render_template('user_form.html', user=user, action='edit')

            email_exists = User.query.filter(User.email == email, User.id != id).first()
            if email_exists:
                flash('البريد الإلكتروني موجود بالفعل', 'danger')
                return render_template('user_form.html', user=user, action='edit')

            # تحديث بيانات المستخدم
            user.username = username
            user.email = email
            user.full_name = full_name
            user.role = role

            # تحديث كلمة المرور إذا تم تقديمها
            if password:
                if password != confirm_password:
                    flash('كلمات المرور غير متطابقة', 'danger')
                    return render_template('user_form.html', user=user, action='edit')
                user.set_password(password)

            # حفظ التغييرات
            db.session.commit()

            # إذا تم تغيير الدور إلى مدير، قم بمنح جميع الصلاحيات
            if role == 'admin':
                PermissionsManager.grant_all_permissions_to_admins()

            flash('تم تحديث المستخدم بنجاح', 'success')
            return redirect(url_for('users.index'))

        except Exception as e:
            db.session.rollback()
            flash(f'حدث خطأ أثناء تحديث المستخدم: {str(e)}', 'danger')

    return render_template('user_form.html', user=user, action='edit')

@users_blueprint.route('/users/<int:id>/delete', methods=['POST'])
@login_required
def delete(id):
    # التحقق من صلاحية المستخدم الحالي
    if not current_user.has_permission('users', 'delete'):
        flash('ليس لديك صلاحية حذف المستخدمين', 'danger')
        return redirect(url_for('dashboard.home'))

    # لا يمكن للمستخدم حذف نفسه
    if id == current_user.id:
        flash('لا يمكنك حذف حسابك الخاص', 'danger')
        return redirect(url_for('users.index'))

    # الحصول على المستخدم
    user = User.query.get_or_404(id)

    try:
        # حذف المستخدم
        db.session.delete(user)
        db.session.commit()

        flash('تم حذف المستخدم بنجاح', 'success')
    except Exception as e:
        db.session.rollback()
        flash(f'حدث خطأ أثناء حذف المستخدم: {str(e)}', 'danger')

    return redirect(url_for('users.index'))

@users_blueprint.route('/users/<int:id>/permissions', methods=['GET', 'POST'])
@login_required
def user_permissions(id):
    """إدارة صلاحيات المستخدم"""
    # التحقق من صلاحية المستخدم الحالي
    if not current_user.has_permission('users', 'edit'):
        flash('ليس لديك صلاحية إدارة صلاحيات المستخدمين', 'danger')
        return redirect(url_for('dashboard.home'))

    # الحصول على المستخدم
    user = User.query.get_or_404(id)

    # تهيئة الصلاحيات إذا لم تكن موجودة
    PermissionsManager.initialize_permissions()

    if request.method == 'POST':
        try:
            # جمع بيانات الصلاحيات من النموذج
            permissions_data = {}
            for key, value in request.form.items():
                if key.startswith('permission_'):
                    permission_id = key.replace('permission_', '')
                    permissions_data[permission_id] = value == 'on'

            # حذف الصلاحيات الحالية للمستخدم
            UserPermission.query.filter_by(user_id=user.id).delete()

            # إضافة الصلاحيات الجديدة
            for permission_id, granted in permissions_data.items():
                if granted:
                    user_permission = UserPermission(
                        user_id=user.id,
                        permission_id=int(permission_id),
                        granted=True
                    )
                    db.session.add(user_permission)

            db.session.commit()
            flash('تم تحديث صلاحيات المستخدم بنجاح', 'success')

            return redirect(url_for('users.user_permissions', id=user.id))
        except Exception as e:
            db.session.rollback()
            flash(f'حدث خطأ: {str(e)}', 'danger')

    # الحصول على الصلاحيات المنظمة حسب الوحدة
    permissions_by_module = PermissionsManager.get_permissions_by_module()

    # الحصول على صلاحيات المستخدم الحالية
    user_permissions = UserPermission.query.filter_by(user_id=user.id).all()
    user_permissions_dict = {}

    # تحويل الصلاحيات إلى قاموس
    for user_permission in user_permissions:
        permission = Permission.query.get(user_permission.permission_id)
        if permission:
            user_permissions_dict[permission.name] = user_permission.granted

    # إذا كان المستخدم مدير، فلديه جميع الصلاحيات
    if user.role == 'admin':
        all_permissions = Permission.query.all()
        for permission in all_permissions:
            user_permissions_dict[permission.name] = True

    return render_template(
        'user_permissions.html',
        user=user,
        permissions_by_module=permissions_by_module,
        user_permissions=user_permissions_dict,
        current_user=current_user
    )

@users_blueprint.route('/api/users')
@login_required
def api_users():
    # التحقق من صلاحية المستخدم الحالي
    if not current_user.has_permission('users', 'view'):
        return jsonify({'error': 'Unauthorized'}), 403

    search = request.args.get('search', '')

    query = User.query

    if search:
        query = query.filter(
            User.username.ilike(f'%{search}%') |
            User.email.ilike(f'%{search}%') |
            User.full_name.ilike(f'%{search}%')
        )

    users = query.order_by(User.username).all()

    return jsonify({
        'users': [
            {
                'id': user.id,
                'username': user.username,
                'email': user.email,
                'full_name': user.full_name,
                'role': user.role,
                'created_at': user.created_at.strftime('%Y-%m-%d %H:%M:%S') if user.created_at else None
            }
            for user in users
        ]
    })

@users_blueprint.route('/profile')
@login_required
def profile():
    return render_template('profile.html', user=current_user, current_user=current_user)
