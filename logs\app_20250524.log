2025-05-24 18:34:43,055 - app - INFO - استخدام قاعدة البيانات المحلية: sqlite:///C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\instance\fouad_pos.db
2025-05-24 18:34:47,514 - nobara - INFO - معلومات النظام:
النظام: Windows-10-10.0.19045-SP0
إصدار بايثون: 3.13.1
اسم المضيف: DESKTOP-646FJ2N
عنوان IP: 127.0.0.1
عدد وحدات المعالجة: 4
إجمالي الذاكرة: 6.93 GB
الذاكرة المتاحة: 3.74 GB
استخدام القرص: 45.6%
إصدار التطبيق: غير معروف
وقت البدء: 2025-05-24 18:34:47
2025-05-24 18:34:48,673 - app - ERROR - خطأ أثناء تهيئة النظام المحاسبي: Class <class 'models_main.PaymentMethod'> does not have a __table__ or __tablename__ specified and does not inherit from an existing table-mapped class.
2025-05-24 18:35:12,336 - app - INFO - استخدام قاعدة البيانات المحلية: sqlite:///C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\instance\fouad_pos.db
2025-05-24 18:35:12,495 - nobara - INFO - معلومات النظام:
النظام: Windows-10-10.0.19045-SP0
إصدار بايثون: 3.13.1
اسم المضيف: DESKTOP-646FJ2N
عنوان IP: 127.0.0.1
عدد وحدات المعالجة: 4
إجمالي الذاكرة: 6.93 GB
الذاكرة المتاحة: 3.31 GB
استخدام القرص: 45.6%
إصدار التطبيق: غير معروف
وقت البدء: 2025-05-24 18:35:12
2025-05-24 18:35:12,536 - app - ERROR - خطأ أثناء تهيئة النظام المحاسبي: Class <class 'models_main.PaymentMethod'> does not have a __table__ or __tablename__ specified and does not inherit from an existing table-mapped class.
