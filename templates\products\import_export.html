{% extends 'base.html' %}

{% block extra_head %}
<style>
    .import-export-card {
        transition: all 0.3s ease;
    }
    .import-export-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 15px rgba(0, 0, 0, 0.1);
    }
    .step-card {
        border-right: 4px solid transparent;
        transition: all 0.3s ease;
    }
    .step-card.active {
        border-right-color: #3b82f6;
        background-color: #eff6ff;
    }
    .preview-table {
        max-height: 400px;
        overflow-y: auto;
    }
    .field-mapping-container {
        max-height: 400px;
        overflow-y: auto;
    }
    .progress-container {
        height: 8px;
        width: 100%;
        background-color: #e5e7eb;
        border-radius: 4px;
        margin-top: 8px;
    }
    .progress-bar {
        height: 100%;
        background-color: #3b82f6;
        border-radius: 4px;
        transition: width 0.3s ease;
    }
</style>
{% endblock %}

{% block content %}
<div class="mb-6 flex items-center justify-between">
    <div>
        <h1 class="text-2xl font-bold text-gray-800">استيراد وتصدير المنتجات</h1>
        <p class="text-gray-600">استيراد المنتجات من ملفات خارجية أو تصدير المنتجات الحالية</p>
    </div>
    <a href="{{ url_for('products.index') }}" class="flex items-center text-gray-700 hover:text-blue-600">
        <i class="ri-arrow-right-line ml-1"></i>
        <span>العودة للمنتجات</span>
    </a>
</div>

<div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
    <!-- بطاقة الاستيراد -->
    <div class="bg-white rounded-lg shadow-sm p-6 import-export-card">
        <div class="flex items-center mb-4">
            <div class="w-12 h-12 rounded-full bg-blue-100 flex items-center justify-center text-blue-600 ml-4">
                <i class="ri-download-2-line text-xl"></i>
            </div>
            <div>
                <h2 class="text-xl font-bold text-gray-800">استيراد المنتجات</h2>
                <p class="text-gray-600">استيراد المنتجات من ملفات CSV أو Excel</p>
            </div>
        </div>
        <p class="text-gray-700 mb-4">
            يمكنك استيراد المنتجات من ملفات CSV أو Excel. يدعم النظام استيراد المنتجات من أنظمة مختلفة، حتى لو كانت الملفات مشفرة.
        </p>
        <div class="flex justify-between items-center">
            <button id="import-products-btn" class="bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded-lg flex items-center">
                <i class="ri-upload-2-line ml-2"></i>
                <span>استيراد المنتجات</span>
            </button>
            <a href="#" id="download-import-template" class="text-blue-600 hover:text-blue-800 flex items-center">
                <i class="ri-file-download-line ml-1"></i>
                <span>تنزيل قالب</span>
            </a>
        </div>
    </div>

    <!-- بطاقة التصدير -->
    <div class="bg-white rounded-lg shadow-sm p-6 import-export-card">
        <div class="flex items-center mb-4">
            <div class="w-12 h-12 rounded-full bg-green-100 flex items-center justify-center text-green-600 ml-4">
                <i class="ri-upload-2-line text-xl"></i>
            </div>
            <div>
                <h2 class="text-xl font-bold text-gray-800">تصدير المنتجات</h2>
                <p class="text-gray-600">تصدير المنتجات إلى ملفات CSV أو Excel</p>
            </div>
        </div>
        <p class="text-gray-700 mb-4">
            يمكنك تصدير المنتجات إلى ملفات CSV أو Excel. يمكنك تصفية المنتجات حسب التصنيف أو المخزن أو الحالة قبل التصدير.
        </p>
        <div class="flex justify-between items-center">
            <button id="export-products-btn" class="bg-green-600 hover:bg-green-700 text-white py-2 px-4 rounded-lg flex items-center">
                <i class="ri-download-2-line ml-2"></i>
                <span>تصدير المنتجات</span>
            </button>
            <div class="flex">
                <select id="export-format" class="bg-gray-100 border border-gray-300 rounded-lg px-3 py-2 text-gray-700 focus:outline-none focus:ring-2 focus:ring-blue-500">
                    <option value="csv">CSV (فاصلة)</option>
                    <option value="csv_semicolon">CSV (فاصلة منقوطة)</option>
                    <option value="excel">Excel</option>
                </select>
            </div>
        </div>
    </div>
</div>

<!-- قسم الخطوات والإرشادات -->
<div class="bg-white rounded-lg shadow-sm p-6 mb-8">
    <h2 class="text-xl font-bold text-gray-800 mb-4">كيفية استيراد وتصدير المنتجات</h2>

    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
        <!-- خطوة 1 -->
        <div class="step-card p-4 rounded-lg bg-gray-50">
            <div class="flex items-center mb-2">
                <div class="w-8 h-8 rounded-full bg-blue-100 flex items-center justify-center text-blue-600 ml-2">
                    <span class="font-bold">1</span>
                </div>
                <h3 class="font-bold text-gray-800">تحضير الملف</h3>
            </div>
            <p class="text-gray-700">
                قم بتنزيل قالب الاستيراد أو استخدم ملف CSV/Excel الخاص بك. تأكد من أن الملف يحتوي على الأعمدة المطلوبة.
            </p>
        </div>

        <!-- خطوة 2 -->
        <div class="step-card p-4 rounded-lg bg-gray-50">
            <div class="flex items-center mb-2">
                <div class="w-8 h-8 rounded-full bg-blue-100 flex items-center justify-center text-blue-600 ml-2">
                    <span class="font-bold">2</span>
                </div>
                <h3 class="font-bold text-gray-800">تحميل الملف</h3>
            </div>
            <p class="text-gray-700">
                انقر على زر "استيراد المنتجات" واختر الملف من جهازك. يدعم النظام ملفات CSV وExcel.
            </p>
        </div>

        <!-- خطوة 3 -->
        <div class="step-card p-4 rounded-lg bg-gray-50">
            <div class="flex items-center mb-2">
                <div class="w-8 h-8 rounded-full bg-blue-100 flex items-center justify-center text-blue-600 ml-2">
                    <span class="font-bold">3</span>
                </div>
                <h3 class="font-bold text-gray-800">مطابقة الحقول</h3>
            </div>
            <p class="text-gray-700">
                قم بمطابقة حقول الملف مع حقول النظام. يمكنك معاينة البيانات قبل الاستيراد النهائي.
            </p>
        </div>
    </div>
</div>

<!-- نافذة الاستيراد -->
<div id="import-modal" class="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center hidden">
    <div class="bg-white rounded-lg shadow-lg w-full max-w-4xl max-h-screen overflow-hidden">
        <div class="p-6 border-b">
            <div class="flex justify-between items-center">
                <h2 class="text-xl font-bold text-gray-800">استيراد المنتجات</h2>
                <button id="close-import-modal" class="text-gray-500 hover:text-gray-700">
                    <i class="ri-close-line text-xl"></i>
                </button>
            </div>
        </div>

        <div class="p-6 overflow-y-auto" style="max-height: calc(100vh - 200px);">
            <div id="import-steps">
                <!-- خطوة 1: اختيار الملف -->
                <div id="step-1" class="import-step">
                    <h3 class="text-lg font-bold text-gray-800 mb-4">الخطوة 1: اختيار ملف الاستيراد</h3>
                    <div class="mb-4">
                        <label class="block text-gray-700 mb-2">اختر ملف CSV أو Excel:</label>
                        <div class="flex items-center">
                            <input type="file" id="import-file" class="hidden" accept=".csv,.xlsx,.xls">
                            <button id="select-file-btn" class="bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded-lg flex items-center">
                                <i class="ri-file-upload-line ml-2"></i>
                                <span>اختيار ملف</span>
                            </button>
                            <span id="selected-file-name" class="mr-4 text-gray-700"></span>
                        </div>
                    </div>

                    <div class="mb-4">
                        <label class="block text-gray-700 mb-2">خيارات الاستيراد:</label>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <label class="flex items-center text-gray-700">
                                    <input type="checkbox" id="update-existing" class="ml-2">
                                    <span>تحديث المنتجات الموجودة</span>
                                </label>
                                <p class="text-gray-500 text-sm mr-6">إذا كان المنتج موجودًا بالفعل، سيتم تحديث بياناته</p>
                            </div>
                            <div>
                                <label class="flex items-center text-gray-700">
                                    <input type="checkbox" id="skip-errors" class="ml-2">
                                    <span>تخطي الأخطاء</span>
                                </label>
                                <p class="text-gray-500 text-sm mr-6">استمر في الاستيراد حتى مع وجود أخطاء في بعض الصفوف</p>
                            </div>
                        </div>
                    </div>

                    <div class="flex justify-end">
                        <button id="next-step-1" class="bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded-lg flex items-center" disabled>
                            <span>التالي</span>
                            <i class="ri-arrow-left-line mr-2"></i>
                        </button>
                    </div>
                </div>

                <!-- سيتم إضافة الخطوات الأخرى لاحقًا باستخدام JavaScript -->
            </div>
        </div>
    </div>
</div>

<!-- نافذة التصدير -->
<div id="export-modal" class="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center hidden">
    <div class="bg-white rounded-lg shadow-lg w-full max-w-2xl">
        <div class="p-6 border-b">
            <div class="flex justify-between items-center">
                <h2 class="text-xl font-bold text-gray-800">تصدير المنتجات</h2>
                <button id="close-export-modal" class="text-gray-500 hover:text-gray-700">
                    <i class="ri-close-line text-xl"></i>
                </button>
            </div>
        </div>

        <div class="p-6">
            <div class="mb-4">
                <label class="block text-gray-700 mb-2">تصفية المنتجات:</label>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label class="block text-gray-700 mb-1">التصنيف:</label>
                        <select id="export-category" class="w-full bg-gray-100 border border-gray-300 rounded-lg px-3 py-2 text-gray-700 focus:outline-none focus:ring-2 focus:ring-blue-500">
                            <option value="">جميع التصنيفات</option>
                            {% for category in categories %}
                            <option value="{{ category.id }}">{{ category.name }}</option>
                            {% endfor %}
                        </select>
                    </div>
                    <div>
                        <label class="block text-gray-700 mb-1">المخزن:</label>
                        <select id="export-warehouse" class="w-full bg-gray-100 border border-gray-300 rounded-lg px-3 py-2 text-gray-700 focus:outline-none focus:ring-2 focus:ring-blue-500">
                            <option value="">جميع المخازن</option>
                            {% for warehouse in warehouses %}
                            <option value="{{ warehouse.id }}">{{ warehouse.name }}</option>
                            {% endfor %}
                        </select>
                    </div>
                </div>
            </div>

            <div class="mb-4">
                <label class="block text-gray-700 mb-2">خيارات التصدير:</label>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label class="flex items-center text-gray-700">
                            <input type="checkbox" id="export-with-inventory" class="ml-2" checked>
                            <span>تضمين بيانات المخزون</span>
                        </label>
                    </div>
                    <div>
                        <label class="flex items-center text-gray-700">
                            <input type="checkbox" id="export-active-only" class="ml-2" checked>
                            <span>المنتجات النشطة فقط</span>
                        </label>
                    </div>
                    <div>
                        <label class="flex items-center text-gray-700">
                            <input type="checkbox" id="export-arabic-headers" class="ml-2">
                            <span>استخدام عناوين عربية</span>
                        </label>
                    </div>
                </div>
            </div>

            <div class="flex justify-end">
                <button id="start-export" class="bg-green-600 hover:bg-green-700 text-white py-2 px-4 rounded-lg flex items-center">
                    <i class="ri-download-2-line ml-2"></i>
                    <span>بدء التصدير</span>
                </button>
            </div>
        </div>
    </div>
</div>

{% endblock %}

{% block scripts %}
<script src="{{ url_for('static', filename='js/product_import_export.js') }}"></script>
{% endblock %}
