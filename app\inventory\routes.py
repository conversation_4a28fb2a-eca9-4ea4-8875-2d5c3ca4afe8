"""
Nobara POS System - Inventory Routes
نظام نوبارا لنقاط البيع - مسارات المخازن
"""

from flask import render_template, request, jsonify, redirect, url_for, flash
from flask_login import login_required, current_user
from app.inventory import bp
from app.models import Warehouse, InventoryItem, Product, db
import logging

logger = logging.getLogger(__name__)

@bp.route('/')
@login_required
def index():
    """قائمة المخازن"""
    try:
        warehouses = Warehouse.query.filter_by(is_active=True).all()
        
        # إحصائيات
        stats = {
            'total_warehouses': len(warehouses),
            'total_products': Product.query.filter_by(is_active=True).count(),
            'low_stock_items': 0,
            'out_of_stock_items': 0
        }
        
        return render_template('inventory/index.html', 
                             warehouses=warehouses,
                             stats=stats)
        
    except Exception as e:
        logger.error(f'Error loading inventory: {e}')
        flash('حدث خطأ أثناء تحميل المخازن', 'error')
        return redirect(url_for('main.dashboard'))

@bp.route('/warehouse/<int:id>')
@login_required
def warehouse_view(id):
    """عرض تفاصيل المخزن"""
    try:
        warehouse = Warehouse.query.get_or_404(id)
        inventory_items = InventoryItem.query.filter_by(warehouse_id=id).join(Product).all()
        
        return render_template('inventory/warehouse_view.html',
                             warehouse=warehouse,
                             inventory_items=inventory_items)
        
    except Exception as e:
        logger.error(f'Error loading warehouse: {e}')
        flash('حدث خطأ أثناء تحميل المخزن', 'error')
        return redirect(url_for('inventory.index'))

@bp.route('/movements')
@login_required
def movements():
    """حركات المخزون"""
    return render_template('inventory/movements.html')

@bp.route('/adjustments')
@login_required
def adjustments():
    """تسويات المخزون"""
    return render_template('inventory/adjustments.html')
