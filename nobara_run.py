#!/usr/bin/env python3
"""
Nobara POS System - Enhanced Runner
نظام نوبارا لنقاط البيع - ملف التشغيل المحسن

Developer: ENG/ Fouad Saber
Phone: 01020073527
Email: <EMAIL>
"""

import os
import sys
import webbrowser
from datetime import datetime

def create_simple_app():
    """Create a simple Flask app for testing"""
    from flask import Flask, render_template_string
    
    app = Flask(__name__)
    app.config['SECRET_KEY'] = 'nobara-pos-secret-key-2024'
    
    # Main page template
    main_template = '''
    <!DOCTYPE html>
    <html lang="ar" dir="rtl" data-theme="light">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>نوبارا - نظام نقاط البيع الاحترافي</title>
        
        <!-- Fonts -->
        <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@200;300;400;500;600;700;800;900&display=swap" rel="stylesheet">
        <link href="https://cdn.jsdelivr.net/npm/remixicon@3.5.0/fonts/remixicon.css" rel="stylesheet">
        
        <!-- CSS -->
        <link href="{{ url_for('static', filename='css/nobara-design-system.css') }}?v=2.0.0" rel="stylesheet">
        
        <style>
            .hero-section {
                min-height: 100vh;
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                display: flex;
                align-items: center;
                justify-content: center;
                padding: 2rem;
            }
            
            .hero-card {
                background: rgba(255, 255, 255, 0.95);
                backdrop-filter: blur(10px);
                border-radius: 2rem;
                padding: 3rem;
                text-align: center;
                max-width: 600px;
                width: 100%;
                box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            }
            
            .logo {
                font-size: 4rem;
                margin-bottom: 1rem;
            }
            
            .title {
                font-size: 2.5rem;
                font-weight: 900;
                color: #2563eb;
                margin-bottom: 0.5rem;
            }
            
            .subtitle {
                font-size: 1.2rem;
                color: #6b7280;
                margin-bottom: 2rem;
            }
            
            .features {
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
                gap: 1rem;
                margin: 2rem 0;
            }
            
            .feature {
                padding: 1rem;
                background: #f8fafc;
                border-radius: 1rem;
                border: 2px solid #e5e7eb;
            }
            
            .feature-icon {
                font-size: 2rem;
                color: #dc2626;
                margin-bottom: 0.5rem;
            }
            
            .btn-primary {
                background: linear-gradient(135deg, #2563eb 0%, #dc2626 100%);
                color: white;
                padding: 1rem 2rem;
                border-radius: 1rem;
                text-decoration: none;
                font-weight: 600;
                display: inline-flex;
                align-items: center;
                gap: 0.5rem;
                transition: transform 0.3s ease;
                border: none;
                font-size: 1.1rem;
                cursor: pointer;
            }
            
            .btn-primary:hover {
                transform: translateY(-2px);
                color: white;
            }
            
            .developer-info {
                margin-top: 2rem;
                padding: 1.5rem;
                background: linear-gradient(135deg, #2563eb 0%, #dc2626 100%);
                border-radius: 1rem;
                color: white;
            }
            
            .status-indicator {
                display: inline-flex;
                align-items: center;
                gap: 0.5rem;
                background: #10b981;
                color: white;
                padding: 0.5rem 1rem;
                border-radius: 2rem;
                font-size: 0.9rem;
                margin-bottom: 1rem;
            }
            
            .pulse {
                width: 8px;
                height: 8px;
                background: white;
                border-radius: 50%;
                animation: pulse 2s infinite;
            }
            
            @keyframes pulse {
                0% { opacity: 1; }
                50% { opacity: 0.5; }
                100% { opacity: 1; }
            }
        </style>
    </head>
    
    <body>
        <div class="hero-section">
            <div class="hero-card">
                <div class="status-indicator">
                    <div class="pulse"></div>
                    النظام يعمل بنجاح
                </div>
                
                <div class="logo">🏪</div>
                <h1 class="title">نوبارا</h1>
                <p class="subtitle">نظام نقاط البيع الاحترافي</p>
                
                <div class="features">
                    <div class="feature">
                        <div class="feature-icon">
                            <i class="ri-dashboard-line"></i>
                        </div>
                        <h3>لوحة تحكم</h3>
                        <p>إدارة شاملة</p>
                    </div>
                    
                    <div class="feature">
                        <div class="feature-icon">
                            <i class="ri-shopping-cart-line"></i>
                        </div>
                        <h3>نقطة البيع</h3>
                        <p>سريعة وسهلة</p>
                    </div>
                    
                    <div class="feature">
                        <div class="feature-icon">
                            <i class="ri-file-chart-line"></i>
                        </div>
                        <h3>التقارير</h3>
                        <p>تحليلات دقيقة</p>
                    </div>
                </div>
                
                <button onclick="showComingSoon()" class="btn-primary">
                    <i class="ri-login-box-line"></i>
                    دخول النظام
                </button>
                
                <div class="developer-info">
                    <h3 style="margin-bottom: 0.5rem;">المطور</h3>
                    <p><strong>ENG/ Fouad Saber</strong></p>
                    <p>📞 01020073527</p>
                    <p>📧 <EMAIL></p>
                    <p style="margin-top: 1rem; font-size: 0.9rem; opacity: 0.9;">
                        Version 2.0.0 - {{ current_time }}
                    </p>
                </div>
            </div>
        </div>
        
        <script>
            function showComingSoon() {
                alert('🚀 النظام الكامل قيد التطوير!\\n\\nالميزات المتاحة قريباً:\\n• نقطة البيع الاحترافية\\n• إدارة المخزون\\n• التقارير المالية\\n• إدارة العملاء\\n• والمزيد...');
            }
            
            // Theme management
            document.documentElement.setAttribute('data-theme', 'light');
        </script>
    </body>
    </html>
    '''
    
    @app.route('/')
    def index():
        return render_template_string(main_template, current_time=datetime.now().strftime('%Y-%m-%d %H:%M'))
    
    @app.route('/status')
    def status():
        return {
            'status': 'running',
            'app_name': 'Nobara POS System',
            'version': '2.0.0',
            'developer': 'ENG/ Fouad Saber',
            'phone': '01020073527',
            'email': '<EMAIL>',
            'timestamp': datetime.now().isoformat()
        }
    
    return app

def main():
    """Main function"""
    print("""
🏪 ═══════════════════════════════════════════════════════════════
   نوبارا - نظام نقاط البيع الاحترافي
   Nobara Professional POS System
   
   📱 Version: 2.0.0
   👨‍💻 Developer: ENG/ Fouad Saber
   📞 Phone: 01020073527
   📧 Email: <EMAIL>
   
   🌐 Access: http://localhost:5000
   🌐 Status: http://localhost:5000/status
   
   ✅ النظام يعمل بنجاح!
   🚀 جاري تحميل المتصفح...
═══════════════════════════════════════════════════════════════
    """)
    
    # Create the app
    app = create_simple_app()
    
    # Open browser automatically
    try:
        webbrowser.open('http://localhost:5000')
    except Exception as e:
        print(f"تعذر فتح المتصفح تلقائياً: {e}")
        print("يرجى فتح المتصفح يدوياً والذهاب إلى: http://localhost:5000")
    
    # Run the app
    try:
        app.run(host='0.0.0.0', port=5000, debug=True, use_reloader=False)
    except KeyboardInterrupt:
        print("\n\n🛑 تم إيقاف النظام بواسطة المستخدم")
    except Exception as e:
        print(f"\n❌ خطأ في تشغيل النظام: {e}")
    finally:
        print("\n👋 شكراً لاستخدام نظام نوبارا!")

if __name__ == '__main__':
    main()
