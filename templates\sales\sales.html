<!DOCTYPE html>
<html lang="ar" dir="rtl" class="light">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>FoUaD - إدارة المبيعات</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdn.jsdelivr.net/npm/remixicon@2.5.0/fonts/remixicon.css" rel="stylesheet">
    <!-- إضافة مكتبة SweetAlert2 -->
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <script>
        tailwind.config = {
            darkMode: 'class',
            theme: {
                extend: {
                    colors: {
                        primary: '#3B82F6',
                        secondary: '#10b981',
                        dark: {
                            100: '#1E293B',
                            200: '#0F172A',
                            300: '#0B1120',
                            400: '#060A15'
                        },
                        accent: {
                            blue: '#3B82F6',
                            indigo: '#6366F1',
                            purple: '#8B5CF6',
                            green: '#10B981',
                            red: '#EF4444',
                            orange: '#F97316',
                            yellow: '#F59E0B'
                        }
                    },
                    borderRadius: {
                        'none': '0px',
                        'sm': '4px',
                        DEFAULT: '8px',
                        'md': '12px',
                        'lg': '16px',
                        'xl': '20px',
                        '2xl': '24px',
                        '3xl': '32px',
                        'full': '9999px',
                        'button': '8px'
                    }
                }
            }
        }
    </script>
    <style>
        :where([class^="ri-"])::before { content: "\f3c2"; }
        body {
            font-family: 'Tajawal', sans-serif;
            background-color: #f9fafb;
            transition: background-color 0.3s ease, color 0.3s ease;
        }
        body.dark {
            background-color: #0F172A;
            color: #E2E8F0;
        }
        .scrollbar-hide::-webkit-scrollbar {
            display: none;
        }
        .scrollbar-hide {
            -ms-overflow-style: none;
            scrollbar-width: none;
        }
        .bg-pattern {
            background-color: #f9fafb;
            background-image: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%233B82F6' fill-opacity='0.05'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
            transition: background-color 0.3s ease;
        }
        .dark .bg-pattern {
            background-color: #0F172A;
            background-image: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%233B82F6' fill-opacity='0.1'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
        }
        .glass-effect {
            backdrop-filter: blur(8px);
            background: rgba(255, 255, 255, 0.8);
            border: 1px solid rgba(255, 255, 255, 0.3);
            box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.07);
            transition: all 0.3s ease;
        }
        .dark .glass-effect {
            background: rgba(30, 41, 59, 0.8);
            border: 1px solid rgba(30, 41, 59, 0.3);
            box-shadow: 0 8px 32px 0 rgba(0, 0, 0, 0.2);
        }

        /* تحسينات إضافية للوضع الداكن */
        .dark .text-gray-800 { color: #E2E8F0; }
        .dark .text-gray-700 { color: #CBD5E1; }
        .dark .text-gray-600 { color: #94A3B8; }
        .dark .text-gray-500 { color: #64748B; }

        .dark .bg-white { background-color: #1E293B; }
        .dark .bg-gray-50 { background-color: #0F172A; }
        .dark .bg-gray-100 { background-color: #1E293B; }
        .dark .bg-gray-200 { background-color: #334155; }

        .dark .border-gray-200 { border-color: #334155; }
        .dark .border-gray-300 { border-color: #475569; }

        /* تأثيرات إضافية */
        .card-hover {
            transition: all 0.3s ease;
        }
        .card-hover:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px -5px rgba(59, 130, 246, 0.1), 0 10px 10px -5px rgba(59, 130, 246, 0.04);
        }
        .dark .card-hover:hover {
            box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.3), 0 10px 10px -5px rgba(0, 0, 0, 0.2);
        }

        /* زر تبديل الوضع الداكن */
        .theme-toggle {
            position: fixed;
            bottom: 20px;
            right: 20px;
            z-index: 50;
            width: 50px;
            height: 50px;
            border-radius: 50%;
            background-color: #3B82F6;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            box-shadow: 0 4px 15px -1px rgba(0, 0, 0, 0.2), 0 2px 8px -1px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
            border: 2px solid rgba(255, 255, 255, 0.1);
        }
        .dark .theme-toggle {
            background-color: #1E293B;
            border: 2px solid rgba(59, 130, 246, 0.3);
            box-shadow: 0 4px 15px -1px rgba(0, 0, 0, 0.4), 0 2px 8px -1px rgba(0, 0, 0, 0.2);
        }
        .theme-toggle:hover {
            transform: scale(1.1);
            box-shadow: 0 8px 25px -5px rgba(59, 130, 246, 0.4), 0 8px 10px -5px rgba(59, 130, 246, 0.2);
        }
        .dark .theme-toggle:hover {
            box-shadow: 0 8px 25px -5px rgba(0, 0, 0, 0.5), 0 8px 10px -5px rgba(0, 0, 0, 0.3);
        }
        .theme-toggle i {
            font-size: 1.5rem;
        }

        /* تأثيرات التلميح */
        .tooltip {
            position: relative;
        }
        .tooltip:hover::after {
            content: attr(data-tooltip);
            position: absolute;
            bottom: 100%;
            left: 50%;
            transform: translateX(-50%);
            padding: 0.25rem 0.5rem;
            background-color: #1F2937;
            color: white;
            font-size: 0.75rem;
            border-radius: 0.25rem;
            white-space: nowrap;
            z-index: 10;
            margin-bottom: 0.25rem;
        }
    </style>
</head>
<body class="bg-pattern">
    <div class="flex h-screen overflow-hidden">
        <!-- Sidebar -->
        {% include 'partials/sidebar.html' %}

        <!-- Main Content -->
        <div class="flex-1 flex flex-col overflow-hidden">
            <!-- Top Navigation -->
            {% include 'partials/topnav.html' %}

            <!-- Content Area -->
            <main class="flex-1 overflow-y-auto p-6">
                <div class="mb-6 flex flex-col md:flex-row md:items-center justify-between gap-4">
                    <div>
                        <h1 class="text-2xl font-bold text-gray-800 dark:text-white flex items-center">
                            <i class="ri-shopping-cart-2-fill text-primary dark:text-blue-400 ml-3"></i>
                            <span>المبيعات</span>
                        </h1>
                        <p class="text-gray-600 dark:text-gray-400 mt-1">إدارة وعرض سجلات المبيعات</p>
                    </div>

                    <div class="flex flex-wrap gap-3 mt-4 md:mt-0">
                        <a href="{{ url_for('pos.index') }}"
                           class="bg-gradient-to-r from-primary to-blue-600 dark:from-blue-600 dark:to-blue-700 text-white px-4 py-2.5 rounded-lg shadow-md hover:shadow-lg hover:shadow-blue-500/20 dark:hover:shadow-blue-800/20 transition-all duration-300 flex items-center gap-2 pulse-animation">
                            <i class="ri-add-line text-lg"></i>
                            <span class="font-medium">إضافة مبيعات جديدة</span>
                        </a>

                        <button type="button" id="filterToggleBtn"
                           class="bg-gradient-to-r from-purple-500 to-indigo-600 dark:from-purple-600 dark:to-indigo-700 text-white px-4 py-2.5 rounded-lg shadow-md hover:shadow-lg hover:shadow-purple-500/20 dark:hover:shadow-purple-800/20 transition-all duration-300 flex items-center gap-2">
                            <i class="ri-filter-3-line text-lg"></i>
                            <span class="font-medium">فلترة المبيعات</span>
                        </button>

                        <a href="{{ url_for('deferred_sales.index') }}"
                           class="bg-gradient-to-r from-yellow-500 to-yellow-600 dark:from-yellow-600 dark:to-yellow-700 text-white px-4 py-2.5 rounded-lg shadow-md hover:shadow-lg hover:shadow-yellow-500/20 dark:hover:shadow-yellow-800/20 transition-all duration-300 flex items-center gap-2">
                            <i class="ri-time-line text-lg"></i>
                            <span class="font-medium">المبيعات الآجلة</span>
                        </a>
                    </div>
                </div>

                <!-- Stats Cards -->
                <div class="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
                    <!-- إجمالي المبيعات -->
                    <div class="bg-white dark:bg-dark-100 rounded-lg p-4 transition-all duration-300 hover:shadow-md card-hover border border-gray-200 dark:border-gray-700 shadow-sm relative overflow-hidden">
                        <div class="absolute top-0 left-0 w-1 h-full bg-primary dark:bg-blue-600"></div>
                        <div class="flex justify-between items-start">
                            <div>
                                <h3 class="text-gray-500 dark:text-gray-400 text-xs font-medium mb-1 flex items-center">
                                    <i class="ri-funds-line ml-1"></i>
                                    إجمالي المبيعات
                                </h3>
                                <p class="text-xl font-bold text-gray-800 dark:text-white">{{ "%.2f"|format(stats.total_sales) }} <span class="text-sm">ج.م</span></p>
                                <div class="flex items-center mt-1">
                                    <span class="text-xs text-gray-500 dark:text-gray-400">
                                        {{ stats.total_orders }} فاتورة
                                    </span>
                                </div>
                            </div>
                            <div class="w-10 h-10 rounded-lg bg-blue-50 dark:bg-blue-900/20 flex items-center justify-center text-primary dark:text-blue-400 shadow-sm">
                                <i class="ri-money-dollar-circle-line text-xl"></i>
                            </div>
                        </div>
                        <div class="mt-3">
                            <div class="w-full bg-gray-100 dark:bg-gray-800 rounded-full h-1">
                                <div class="bg-primary dark:bg-blue-600 h-1 rounded-full" style="width: 100%"></div>
                            </div>
                            <div class="flex justify-between mt-1 text-xs">
                                <span class="text-gray-500 dark:text-gray-400">متوسط:</span>
                                <span class="text-primary dark:text-blue-400 font-medium">{{ "%.2f"|format(stats.avg_order_value) }} ج.م</span>
                            </div>
                        </div>
                    </div>

                    <!-- مبيعات اليوم -->
                    <div class="bg-white dark:bg-dark-100 rounded-lg p-4 transition-all duration-300 hover:shadow-md card-hover border border-gray-200 dark:border-gray-700 shadow-sm relative overflow-hidden">
                        <div class="absolute top-0 left-0 w-1 h-full bg-green-500 dark:bg-green-600"></div>
                        <div class="flex justify-between items-start">
                            <div>
                                <h3 class="text-gray-500 dark:text-gray-400 text-xs font-medium mb-1 flex items-center">
                                    <i class="ri-calendar-todo-line ml-1"></i>
                                    مبيعات اليوم
                                </h3>
                                <p class="text-xl font-bold text-gray-800 dark:text-white">{{ "%.2f"|format(stats.today_sales) }} <span class="text-sm">ج.م</span></p>
                                <div class="flex items-center mt-1">
                                    <span class="text-xs text-gray-500 dark:text-gray-400">
                                        {{ stats.today_order_count }} فاتورة
                                    </span>
                                </div>
                            </div>
                            <div class="w-10 h-10 rounded-lg bg-green-50 dark:bg-green-900/20 flex items-center justify-center text-green-500 dark:text-green-400 shadow-sm">
                                <i class="ri-calendar-check-line text-xl"></i>
                            </div>
                        </div>
                        <div class="mt-3">
                            {% set today_percentage = (stats.today_sales / stats.month_sales * 100) if stats.month_sales > 0 else 0 %}
                            <div class="w-full bg-gray-100 dark:bg-gray-800 rounded-full h-1">
                                <div class="bg-green-500 dark:bg-green-600 h-1 rounded-full" style="width: {{ today_percentage|round|int }}%"></div>
                            </div>
                            <div class="flex justify-between mt-1 text-xs">
                                <span class="text-gray-500 dark:text-gray-400">من الشهر:</span>
                                <span class="text-green-500 dark:text-green-400 font-medium">{{ today_percentage|round|int }}٪</span>
                            </div>
                        </div>
                    </div>

                    <!-- مبيعات الشهر -->
                    <div class="bg-white dark:bg-dark-100 rounded-lg p-4 transition-all duration-300 hover:shadow-md card-hover border border-gray-200 dark:border-gray-700 shadow-sm relative overflow-hidden">
                        <div class="absolute top-0 left-0 w-1 h-full bg-purple-500 dark:bg-purple-600"></div>
                        <div class="flex justify-between items-start">
                            <div>
                                <h3 class="text-gray-500 dark:text-gray-400 text-xs font-medium mb-1 flex items-center">
                                    <i class="ri-calendar-line ml-1"></i>
                                    مبيعات الشهر
                                </h3>
                                <p class="text-xl font-bold text-gray-800 dark:text-white">{{ "%.2f"|format(stats.month_sales) }} <span class="text-sm">ج.م</span></p>
                                <div class="flex items-center mt-1">
                                    <span class="text-xs text-gray-500 dark:text-gray-400">
                                        {{ stats.month_order_count }} فاتورة
                                    </span>
                                </div>
                            </div>
                            <div class="w-10 h-10 rounded-lg bg-purple-50 dark:bg-purple-900/20 flex items-center justify-center text-purple-500 dark:text-purple-400 shadow-sm">
                                <i class="ri-bar-chart-line text-xl"></i>
                            </div>
                        </div>
                        <div class="mt-3">
                            {% set month_percentage = (stats.month_sales / stats.total_sales * 100) if stats.total_sales > 0 else 0 %}
                            <div class="w-full bg-gray-100 dark:bg-gray-800 rounded-full h-1">
                                <div class="bg-purple-500 dark:bg-purple-600 h-1 rounded-full" style="width: {{ month_percentage|round|int }}%"></div>
                            </div>
                            <div class="flex justify-between mt-1 text-xs">
                                <span class="text-gray-500 dark:text-gray-400">متوسط يومي:</span>
                                <span class="text-purple-500 dark:text-purple-400 font-medium">{{ "%.2f"|format(stats.month_sales / 30) }} ج.م</span>
                            </div>
                        </div>
                    </div>

                    <!-- طرق الدفع -->
                    <div class="bg-white dark:bg-dark-100 rounded-lg p-4 transition-all duration-300 hover:shadow-md card-hover border border-gray-200 dark:border-gray-700 shadow-sm relative overflow-hidden">
                        <div class="absolute top-0 left-0 w-1 h-full bg-orange-500 dark:bg-orange-600"></div>
                        <div class="flex justify-between items-start">
                            <div>
                                <h3 class="text-gray-500 dark:text-gray-400 text-xs font-medium mb-1 flex items-center">
                                    <i class="ri-bank-card-line ml-1"></i>
                                    طرق الدفع
                                </h3>
                                {% set cash_sales = stats.sales_by_payment.get('cash', 0) %}
                                {% set card_sales = stats.sales_by_payment.get('card', 0) %}
                                {% set total = cash_sales + card_sales %}
                                <p class="text-xl font-bold text-gray-800 dark:text-white">
                                    {% if total > 0 %}
                                        {{ "%.0f"|format(cash_sales / total * 100) }}٪ <span class="text-sm">نقدي</span>
                                    {% else %}
                                        0٪ <span class="text-sm">نقدي</span>
                                    {% endif %}
                                </p>
                                <div class="flex items-center mt-1">
                                    <span class="text-xs text-gray-500 dark:text-gray-400">
                                        {{ "%.0f"|format((card_sales / total * 100) if total > 0 else 0) }}٪ بطاقة
                                    </span>
                                </div>
                            </div>
                            <div class="w-10 h-10 rounded-lg bg-orange-50 dark:bg-orange-900/20 flex items-center justify-center text-orange-500 dark:text-orange-400 shadow-sm">
                                <i class="ri-wallet-3-line text-xl"></i>
                            </div>
                        </div>
                        <div class="mt-3">
                            <div class="w-full h-1.5 bg-gray-100 dark:bg-gray-800 rounded-full overflow-hidden">
                                {% if total > 0 %}
                                <div class="flex h-full">
                                    <div class="bg-orange-500 dark:bg-orange-600 h-full" style="width: {{ (cash_sales / total * 100)|round|int }}%"></div>
                                    <div class="bg-blue-500 dark:bg-blue-600 h-full" style="width: {{ (card_sales / total * 100)|round|int }}%"></div>
                                </div>
                                {% else %}
                                <div class="bg-gray-300 dark:bg-gray-700 h-full w-full"></div>
                                {% endif %}
                            </div>
                            <div class="flex justify-between mt-1 text-xs">
                                <span class="text-orange-500 dark:text-orange-400">{{ "%.2f"|format(cash_sales) }} ج.م</span>
                                <span class="text-blue-500 dark:text-blue-400">{{ "%.2f"|format(card_sales) }} ج.م</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Filters & Search -->
                <div id="salesFilterPanel" class="bg-gradient-to-r from-gray-50 to-white dark:from-dark-200 dark:to-dark-100 rounded-xl p-5 mb-6 border border-gray-200 dark:border-gray-700 shadow-lg transition-all duration-300 transform scale-100 origin-top hidden">
                    <div class="flex items-center justify-between mb-4 border-b border-gray-200 dark:border-gray-700 pb-3">
                        <h3 class="text-lg font-bold text-gray-800 dark:text-white flex items-center">
                            <div class="w-10 h-10 rounded-lg bg-purple-100 dark:bg-purple-900/30 flex items-center justify-center text-purple-600 dark:text-purple-400 mr-3 shadow-sm">
                                <i class="ri-filter-3-line text-xl"></i>
                            </div>
                            <div>
                                <span>فلترة المبيعات</span>
                                <p class="text-xs text-gray-500 dark:text-gray-400 mt-0.5 font-normal">استخدم الخيارات أدناه لتصفية المبيعات حسب احتياجاتك</p>
                            </div>
                        </h3>
                        <div class="flex items-center gap-2">
                            <a href="{{ url_for('sales.index') }}" class="text-xs text-gray-500 dark:text-gray-400 hover:text-primary dark:hover:text-blue-400 transition-colors flex items-center bg-white dark:bg-dark-300 px-3 py-1.5 rounded-lg border border-gray-200 dark:border-gray-700 shadow-sm">
                                <i class="ri-refresh-line ml-1"></i>
                                <span>إعادة ضبط الفلاتر</span>
                            </a>
                            <button type="button" id="closeFilterBtn" class="text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 p-1.5 rounded-lg hover:bg-gray-100 dark:hover:bg-dark-300 transition-colors">
                                <i class="ri-close-line text-xl"></i>
                            </button>
                        </div>
                    </div>

                    <form method="GET" action="{{ url_for('sales.index') }}" id="filterForm">
                        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-5">
                            <!-- بحث -->
                            <div class="relative">
                                <label for="search" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1.5 flex items-center">
                                    <i class="ri-search-line ml-1.5 text-primary dark:text-blue-400"></i>
                                    <span>بحث</span>
                                </label>
                                <div class="relative">
                                    <input type="text" id="search" name="search" value="{{ search }}"
                                        placeholder="رقم الفاتورة أو اسم العميل..."
                                        class="w-full pr-10 px-4 py-2.5 bg-white dark:bg-dark-300 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 dark:focus:ring-purple-600 focus:border-transparent transition-all text-sm shadow-sm">
                                    <div class="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none text-gray-400 dark:text-gray-500">
                                        <i class="ri-search-line"></i>
                                    </div>
                                </div>
                            </div>

                            <!-- العميل -->
                            <div class="relative">
                                <label for="customer_id" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1.5 flex items-center">
                                    <i class="ri-user-3-line ml-1.5 text-green-500 dark:text-green-400"></i>
                                    <span>العميل</span>
                                </label>
                                <div class="relative">
                                    <select id="customer_id" name="customer_id"
                                        class="w-full px-4 py-2.5 bg-white dark:bg-dark-300 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 dark:focus:ring-green-600 focus:border-transparent transition-all text-sm appearance-none shadow-sm">
                                        <option value="">كل العملاء</option>
                                        {% for customer in customers %}
                                        <option value="{{ customer.id }}" {% if customer_id|int == customer.id %}selected{% endif %}>{{ customer.name }}</option>
                                        {% endfor %}
                                    </select>
                                    <div class="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none text-gray-400 dark:text-gray-500">
                                        <i class="ri-arrow-down-s-line"></i>
                                    </div>
                                </div>
                            </div>

                            <!-- المستخدم -->
                            <div class="relative">
                                <label for="user_id" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1.5 flex items-center">
                                    <i class="ri-user-settings-line ml-1.5 text-indigo-500 dark:text-indigo-400"></i>
                                    <span>المستخدم</span>
                                </label>
                                <div class="relative">
                                    <select id="user_id" name="user_id"
                                        class="w-full px-4 py-2.5 bg-white dark:bg-dark-300 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500 dark:focus:ring-indigo-600 focus:border-transparent transition-all text-sm appearance-none shadow-sm">
                                        <option value="">كل المستخدمين</option>
                                        {% for user in users %}
                                        <option value="{{ user.id }}" {% if user_id|int == user.id %}selected{% endif %}>{{ user.username }}</option>
                                        {% endfor %}
                                    </select>
                                    <div class="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none text-gray-400 dark:text-gray-500">
                                        <i class="ri-arrow-down-s-line"></i>
                                    </div>
                                </div>
                            </div>

                            <!-- الحالة -->
                            <div class="relative">
                                <label for="status" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1.5 flex items-center">
                                    <i class="ri-checkbox-circle-line ml-1.5 text-blue-500 dark:text-blue-400"></i>
                                    <span>حالة الفاتورة</span>
                                </label>
                                <div class="relative">
                                    <select id="status" name="status"
                                        class="w-full px-4 py-2.5 bg-white dark:bg-dark-300 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-600 focus:border-transparent transition-all text-sm appearance-none shadow-sm">
                                        <option value="">جميع الحالات</option>
                                        <option value="completed" {% if status == 'completed' %}selected{% endif %}>مكتمل</option>
                                        <option value="pending" {% if status == 'pending' %}selected{% endif %}>معلق</option>
                                        <option value="cancelled" {% if status == 'cancelled' %}selected{% endif %}>ملغي</option>
                                        <option value="returned" {% if status == 'returned' %}selected{% endif %}>مرتجع</option>
                                        <option value="partially_returned" {% if status == 'partially_returned' %}selected{% endif %}>مرتجع جزئي</option>
                                    </select>
                                    <div class="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none text-gray-400 dark:text-gray-500">
                                        <i class="ri-arrow-down-s-line"></i>
                                    </div>
                                </div>
                            </div>

                            <!-- طريقة الدفع -->
                            <div class="relative">
                                <label for="payment_method" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1.5 flex items-center">
                                    <i class="ri-bank-card-line ml-1.5 text-orange-500 dark:text-orange-400"></i>
                                    <span>طريقة الدفع</span>
                                </label>
                                <div class="relative">
                                    <select id="payment_method" name="payment_method"
                                        class="w-full px-4 py-2.5 bg-white dark:bg-dark-300 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500 dark:focus:ring-orange-600 focus:border-transparent transition-all text-sm appearance-none shadow-sm">
                                        <option value="">جميع طرق الدفع</option>
                                        <option value="cash" {% if payment_method == 'cash' %}selected{% endif %}>نقدي</option>
                                        <option value="card" {% if payment_method == 'card' %}selected{% endif %}>بطاقة</option>
                                    </select>
                                    <div class="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none text-gray-400 dark:text-gray-500">
                                        <i class="ri-arrow-down-s-line"></i>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- قسم التاريخ - تصميم جديد -->
                        <div class="bg-gradient-to-r from-indigo-50 to-purple-50 dark:from-indigo-900/10 dark:to-purple-900/10 rounded-xl border border-gray-200 dark:border-gray-700 p-5 mb-5 shadow-sm">
                            <div class="flex items-center justify-between mb-3 border-b border-gray-200 dark:border-gray-700 pb-3">
                                <h3 class="text-sm font-bold text-gray-700 dark:text-gray-300 flex items-center">
                                    <div class="w-8 h-8 rounded-lg bg-indigo-100 dark:bg-indigo-900/30 flex items-center justify-center text-indigo-600 dark:text-indigo-400 ml-2 shadow-sm">
                                        <i class="ri-calendar-line"></i>
                                    </div>
                                    <span>تصفية حسب التاريخ</span>
                                </h3>

                                <button type="button" id="clearDateFilter" class="text-xs text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 transition-colors flex items-center bg-white dark:bg-dark-300 px-3 py-1.5 rounded-lg border border-gray-200 dark:border-gray-700 shadow-sm">
                                    <i class="ri-refresh-line ml-1"></i>
                                    <span>مسح التاريخ</span>
                                </button>
                            </div>

                            <!-- أزرار الفترات المحددة مسبقاً -->
                            <div class="mb-4">
                                <div class="text-xs font-medium text-gray-600 dark:text-gray-400 mb-2">فترات زمنية سريعة:</div>
                                <div class="grid grid-cols-2 sm:grid-cols-4 gap-2">
                                    <button type="button" class="date-preset flex items-center justify-center gap-1.5 text-sm py-2 px-3 rounded-lg bg-white dark:bg-dark-300 hover:bg-indigo-50 dark:hover:bg-indigo-900/20 hover:text-indigo-600 dark:hover:text-indigo-400 border border-gray-200 dark:border-gray-700 transition-all shadow-sm" data-range="today">
                                        <i class="ri-calendar-todo-line"></i>
                                        <span>اليوم</span>
                                    </button>
                                    <button type="button" class="date-preset flex items-center justify-center gap-1.5 text-sm py-2 px-3 rounded-lg bg-white dark:bg-dark-300 hover:bg-indigo-50 dark:hover:bg-indigo-900/20 hover:text-indigo-600 dark:hover:text-indigo-400 border border-gray-200 dark:border-gray-700 transition-all shadow-sm" data-range="yesterday">
                                        <i class="ri-calendar-2-line"></i>
                                        <span>أمس</span>
                                    </button>
                                    <button type="button" class="date-preset flex items-center justify-center gap-1.5 text-sm py-2 px-3 rounded-lg bg-white dark:bg-dark-300 hover:bg-indigo-50 dark:hover:bg-indigo-900/20 hover:text-indigo-600 dark:hover:text-indigo-400 border border-gray-200 dark:border-gray-700 transition-all shadow-sm" data-range="this_week">
                                        <i class="ri-calendar-check-line"></i>
                                        <span>هذا الأسبوع</span>
                                    </button>
                                    <button type="button" class="date-preset flex items-center justify-center gap-1.5 text-sm py-2 px-3 rounded-lg bg-white dark:bg-dark-300 hover:bg-indigo-50 dark:hover:bg-indigo-900/20 hover:text-indigo-600 dark:hover:text-indigo-400 border border-gray-200 dark:border-gray-700 transition-all shadow-sm" data-range="last_week">
                                        <i class="ri-calendar-event-line"></i>
                                        <span>الأسبوع الماضي</span>
                                    </button>
                                </div>
                            </div>

                            <div class="mb-4">
                                <div class="grid grid-cols-2 sm:grid-cols-4 gap-2">
                                    <button type="button" class="date-preset flex items-center justify-center gap-1.5 text-sm py-2 px-3 rounded-lg bg-white dark:bg-dark-300 hover:bg-indigo-50 dark:hover:bg-indigo-900/20 hover:text-indigo-600 dark:hover:text-indigo-400 border border-gray-200 dark:border-gray-700 transition-all shadow-sm" data-range="this_month">
                                        <i class="ri-calendar-line"></i>
                                        <span>هذا الشهر</span>
                                    </button>
                                    <button type="button" class="date-preset flex items-center justify-center gap-1.5 text-sm py-2 px-3 rounded-lg bg-white dark:bg-dark-300 hover:bg-indigo-50 dark:hover:bg-indigo-900/20 hover:text-indigo-600 dark:hover:text-indigo-400 border border-gray-200 dark:border-gray-700 transition-all shadow-sm" data-range="last_month">
                                        <i class="ri-calendar-line"></i>
                                        <span>الشهر الماضي</span>
                                    </button>
                                    <button type="button" class="date-preset flex items-center justify-center gap-1.5 text-sm py-2 px-3 rounded-lg bg-white dark:bg-dark-300 hover:bg-indigo-50 dark:hover:bg-indigo-900/20 hover:text-indigo-600 dark:hover:text-indigo-400 border border-gray-200 dark:border-gray-700 transition-all shadow-sm" data-range="this_year">
                                        <i class="ri-calendar-line"></i>
                                        <span>هذه السنة</span>
                                    </button>
                                    <button type="button" class="date-preset flex items-center justify-center gap-1.5 text-sm py-2 px-3 rounded-lg bg-white dark:bg-dark-300 hover:bg-indigo-50 dark:hover:bg-indigo-900/20 hover:text-indigo-600 dark:hover:text-indigo-400 border border-gray-200 dark:border-gray-700 transition-all shadow-sm" data-range="last_year">
                                        <i class="ri-calendar-line"></i>
                                        <span>السنة الماضية</span>
                                    </button>
                                </div>
                            </div>

                            <!-- تحديد فترة مخصصة -->
                            <div class="mb-4">
                                <div class="text-xs font-medium text-gray-600 dark:text-gray-400 mb-2">تحديد فترة مخصصة:</div>
                                <div class="grid grid-cols-1 sm:grid-cols-2 gap-3">
                                    <div>
                                        <label class="text-xs text-gray-600 dark:text-gray-400 block mb-1.5 flex items-center">
                                            <i class="ri-calendar-line ml-1.5 text-indigo-500 dark:text-indigo-400"></i>
                                            <span>من تاريخ</span>
                                        </label>
                                        <div class="relative">
                                            <input type="date" id="date_from" name="date_from" value="{{ date_from }}"
                                                class="w-full pr-10 px-4 py-2.5 bg-white dark:bg-dark-300 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500 dark:focus:ring-indigo-600 focus:border-transparent transition-all text-sm shadow-sm">
                                            <div class="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none text-gray-400 dark:text-gray-500">
                                                <i class="ri-calendar-line"></i>
                                            </div>
                                        </div>
                                    </div>
                                    <div>
                                        <label class="text-xs text-gray-600 dark:text-gray-400 block mb-1.5 flex items-center">
                                            <i class="ri-calendar-line ml-1.5 text-indigo-500 dark:text-indigo-400"></i>
                                            <span>إلى تاريخ</span>
                                        </label>
                                        <div class="relative">
                                            <input type="date" id="date_to" name="date_to" value="{{ date_to }}"
                                                class="w-full pr-10 px-4 py-2.5 bg-white dark:bg-dark-300 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500 dark:focus:ring-indigo-600 focus:border-transparent transition-all text-sm shadow-sm">
                                            <div class="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none text-gray-400 dark:text-gray-500">
                                                <i class="ri-calendar-line"></i>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- عرض الفترة المحددة والزر -->
                            <div class="flex flex-col sm:flex-row items-center justify-between gap-3 pt-3 border-t border-gray-200 dark:border-gray-700">
                                <div class="text-sm text-gray-700 dark:text-gray-300 bg-white dark:bg-dark-300 px-4 py-2 rounded-lg border border-gray-200 dark:border-gray-700 shadow-sm w-full sm:w-auto">
                                    <span class="font-medium">الفترة المحددة:</span>
                                    <span id="selectedDateRange" class="mr-1 text-indigo-600 dark:text-indigo-400 font-bold">
                                        {% if date_from or date_to %}
                                            {% if date_from == date_to %}
                                                {{ date_from }}
                                            {% else %}
                                                {{ date_from }} - {{ date_to }}
                                            {% endif %}
                                        {% else %}
                                            الكل
                                        {% endif %}
                                    </span>
                                </div>

                                <button type="submit" class="px-5 py-2.5 bg-gradient-to-r from-indigo-500 to-purple-600 dark:from-indigo-600 dark:to-purple-700 text-white rounded-lg hover:shadow-lg hover:shadow-indigo-500/20 dark:hover:shadow-indigo-800/20 transition-all text-sm font-medium flex items-center justify-center gap-2 w-full sm:w-auto">
                                    <i class="ri-filter-3-line text-lg"></i>
                                    <span>تطبيق الفلتر</span>
                                </button>
                            </div>
                        </div>
                    </form>
                </div>

                <!-- JavaScript لفلتر المبيعات -->
                <script>
                    document.addEventListener('DOMContentLoaded', function() {
                        // عناصر فلتر التاريخ
                        const datePresets = document.querySelectorAll('.date-preset');
                        const dateFromInput = document.getElementById('date_from');
                        const dateToInput = document.getElementById('date_to');
                        const clearDateFilter = document.getElementById('clearDateFilter');
                        const selectedDateRange = document.getElementById('selectedDateRange');

                        // عناصر لوحة الفلترة
                        const filterToggleBtn = document.getElementById('filterToggleBtn');
                        const closeFilterBtn = document.getElementById('closeFilterBtn');
                        const salesFilterPanel = document.getElementById('salesFilterPanel');

                        // إظهار/إخفاء لوحة الفلترة
                        filterToggleBtn.addEventListener('click', function() {
                            if (salesFilterPanel.classList.contains('hidden')) {
                                // إظهار لوحة الفلترة مع تأثير حركي
                                salesFilterPanel.classList.remove('hidden');
                                salesFilterPanel.classList.add('animate-fade-in-down');

                                // تمرير إلى لوحة الفلترة
                                setTimeout(() => {
                                    salesFilterPanel.scrollIntoView({ behavior: 'smooth', block: 'start' });
                                }, 100);

                                // تغيير لون زر الفلترة
                                filterToggleBtn.classList.remove('from-purple-500', 'to-indigo-600');
                                filterToggleBtn.classList.add('from-indigo-600', 'to-purple-700');
                            } else {
                                // إخفاء لوحة الفلترة مع تأثير حركي
                                salesFilterPanel.classList.add('animate-fade-out-up');

                                setTimeout(() => {
                                    salesFilterPanel.classList.add('hidden');
                                    salesFilterPanel.classList.remove('animate-fade-out-up');
                                }, 300);

                                // إعادة لون زر الفلترة
                                filterToggleBtn.classList.add('from-purple-500', 'to-indigo-600');
                                filterToggleBtn.classList.remove('from-indigo-600', 'to-purple-700');
                            }
                        });

                        // إغلاق لوحة الفلترة
                        closeFilterBtn.addEventListener('click', function() {
                            salesFilterPanel.classList.add('animate-fade-out-up');

                            setTimeout(() => {
                                salesFilterPanel.classList.add('hidden');
                                salesFilterPanel.classList.remove('animate-fade-out-up');
                            }, 300);

                            // إعادة لون زر الفلترة
                            filterToggleBtn.classList.add('from-purple-500', 'to-indigo-600');
                            filterToggleBtn.classList.remove('from-indigo-600', 'to-purple-700');
                        });

                        // إظهار لوحة الفلترة إذا كان هناك فلاتر نشطة
                        if (dateFromInput.value || dateToInput.value || document.getElementById('search').value ||
                            document.getElementById('customer_id').value || document.getElementById('status').value ||
                            document.getElementById('payment_method').value) {
                            salesFilterPanel.classList.remove('hidden');
                            filterToggleBtn.classList.remove('from-purple-500', 'to-indigo-600');
                            filterToggleBtn.classList.add('from-indigo-600', 'to-purple-700');
                        }

                        // تحديد فترة زمنية محددة مسبقاً
                        datePresets.forEach(button => {
                            button.addEventListener('click', function() {
                                // إزالة التحديد من جميع الأزرار
                                datePresets.forEach(btn => {
                                    btn.classList.remove('bg-indigo-50', 'dark:bg-indigo-900/20', 'text-indigo-600', 'dark:text-indigo-400');
                                });

                                // تحديد الزر المختار
                                this.classList.add('bg-indigo-50', 'dark:bg-indigo-900/20', 'text-indigo-600', 'dark:text-indigo-400');

                                const range = this.dataset.range;
                                const today = new Date();
                                let fromDate = new Date();
                                let toDate = new Date();

                                switch(range) {
                                    case 'today':
                                        // اليوم الحالي
                                        break;
                                    case 'yesterday':
                                        // أمس
                                        fromDate.setDate(fromDate.getDate() - 1);
                                        toDate.setDate(toDate.getDate() - 1);
                                        break;
                                    case 'this_week':
                                        // الأسبوع الحالي (الأحد إلى السبت)
                                        const dayOfWeek = today.getDay(); // 0 = الأحد، 6 = السبت
                                        fromDate.setDate(today.getDate() - dayOfWeek);
                                        toDate.setDate(fromDate.getDate() + 6);
                                        break;
                                    case 'last_week':
                                        // الأسبوع الماضي
                                        const lastWeekDay = today.getDay();
                                        fromDate.setDate(today.getDate() - lastWeekDay - 7);
                                        toDate.setDate(fromDate.getDate() + 6);
                                        break;
                                    case 'this_month':
                                        // الشهر الحالي
                                        fromDate = new Date(today.getFullYear(), today.getMonth(), 1);
                                        toDate = new Date(today.getFullYear(), today.getMonth() + 1, 0);
                                        break;
                                    case 'last_month':
                                        // الشهر الماضي
                                        fromDate = new Date(today.getFullYear(), today.getMonth() - 1, 1);
                                        toDate = new Date(today.getFullYear(), today.getMonth(), 0);
                                        break;
                                    case 'this_year':
                                        // السنة الحالية
                                        fromDate = new Date(today.getFullYear(), 0, 1);
                                        toDate = new Date(today.getFullYear(), 11, 31);
                                        break;
                                    case 'last_year':
                                        // السنة الماضية
                                        fromDate = new Date(today.getFullYear() - 1, 0, 1);
                                        toDate = new Date(today.getFullYear() - 1, 11, 31);
                                        break;
                                }

                                // تنسيق التاريخ بصيغة YYYY-MM-DD
                                dateFromInput.value = fromDate.toISOString().split('T')[0];
                                dateToInput.value = toDate.toISOString().split('T')[0];

                                // تحديث النص المعروض
                                updateSelectedDateRangeText();

                                // تقديم النموذج تلقائياً
                                document.getElementById('filterForm').submit();
                            });
                        });

                        // مسح فلتر التاريخ
                        clearDateFilter.addEventListener('click', function() {
                            dateFromInput.value = '';
                            dateToInput.value = '';
                            updateSelectedDateRangeText();

                            // إزالة التحديد من جميع الأزرار
                            datePresets.forEach(btn => {
                                btn.classList.remove('bg-indigo-50', 'dark:bg-indigo-900/20', 'text-indigo-600', 'dark:text-indigo-400');
                            });
                        });

                        // تحديث النص المعروض للتاريخ المحدد
                        function updateSelectedDateRangeText() {
                            if (dateFromInput.value || dateToInput.value) {
                                if (dateFromInput.value === dateToInput.value) {
                                    selectedDateRange.textContent = dateFromInput.value;
                                } else {
                                    selectedDateRange.textContent = `${dateFromInput.value || ''} - ${dateToInput.value || ''}`;
                                }
                            } else {
                                selectedDateRange.textContent = 'الكل';
                            }
                        }

                        // تحديث النص عند تغيير التاريخ يدوياً
                        dateFromInput.addEventListener('change', updateSelectedDateRangeText);
                        dateToInput.addEventListener('change', updateSelectedDateRangeText);

                        // تحديد الزر المناسب عند تحميل الصفحة إذا كان هناك تاريخ محدد
                        if (dateFromInput.value && dateToInput.value) {
                            const today = new Date();
                            const fromDate = new Date(dateFromInput.value);
                            const toDate = new Date(dateToInput.value);

                            // التحقق من الفترات المحددة مسبقاً
                            const todayStart = new Date(today.getFullYear(), today.getMonth(), today.getDate());
                            const yesterdayStart = new Date(today.getFullYear(), today.getMonth(), today.getDate() - 1);

                            // اليوم
                            if (fromDate.getTime() === todayStart.getTime() && toDate.getTime() === todayStart.getTime()) {
                                document.querySelector('.date-preset[data-range="today"]').classList.add('bg-indigo-50', 'dark:bg-indigo-900/20', 'text-indigo-600', 'dark:text-indigo-400');
                            }
                            // أمس
                            else if (fromDate.getTime() === yesterdayStart.getTime() && toDate.getTime() === yesterdayStart.getTime()) {
                                document.querySelector('.date-preset[data-range="yesterday"]').classList.add('bg-indigo-50', 'dark:bg-indigo-900/20', 'text-indigo-600', 'dark:text-indigo-400');
                            }
                            // هذا الشهر
                            else if (fromDate.getDate() === 1 &&
                                    fromDate.getMonth() === today.getMonth() &&
                                    fromDate.getFullYear() === today.getFullYear() &&
                                    toDate.getMonth() === today.getMonth() &&
                                    toDate.getFullYear() === today.getFullYear()) {
                                document.querySelector('.date-preset[data-range="this_month"]').classList.add('bg-indigo-50', 'dark:bg-indigo-900/20', 'text-indigo-600', 'dark:text-indigo-400');
                            }
                            // وهكذا للفترات الأخرى...
                        }
                    });
                </script>

                <!-- تأثيرات الحركة -->
                <style>
                    @keyframes fadeInDown {
                        from {
                            opacity: 0;
                            transform: translateY(-20px);
                        }
                        to {
                            opacity: 1;
                            transform: translateY(0);
                        }
                    }

                    @keyframes fadeOutUp {
                        from {
                            opacity: 1;
                            transform: translateY(0);
                        }
                        to {
                            opacity: 0;
                            transform: translateY(-20px);
                        }
                    }

                    .animate-fade-in-down {
                        animation: fadeInDown 0.3s ease-out forwards;
                    }

                    .animate-fade-out-up {
                        animation: fadeOutUp 0.3s ease-in forwards;
                    }
                </style>

                <!-- Sales Table -->
                <div class="glass-effect rounded-xl overflow-hidden border border-gray-200 dark:border-gray-700">
                    <div class="p-4 bg-gray-50 dark:bg-dark-200 border-b border-gray-200 dark:border-gray-700 flex flex-col sm:flex-row justify-between items-center gap-3">
                        <h3 class="text-lg font-bold text-gray-800 dark:text-white flex items-center">
                            <i class="ri-file-list-3-line ml-2 text-primary dark:text-blue-400"></i>
                            <span>قائمة المبيعات</span>
                        </h3>
                        <div class="flex items-center gap-2">
                            <span class="text-sm text-gray-500 dark:text-gray-400 bg-gray-100 dark:bg-dark-300 px-3 py-1 rounded-full">
                                <i class="ri-information-line ml-1"></i>
                                <span>إجمالي: {{ orders.total }} فاتورة</span>
                            </span>
                        </div>
                    </div>

                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                            <thead class="bg-gray-50 dark:bg-dark-200">
                                <tr>
                                    <th scope="col" class="px-6 py-4 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">رقم الفاتورة</th>
                                    <th scope="col" class="px-6 py-4 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">العميل</th>
                                    <th scope="col" class="px-6 py-4 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">التاريخ</th>
                                    <th scope="col" class="px-6 py-4 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">المبلغ</th>
                                    <th scope="col" class="px-6 py-4 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">طريقة الدفع</th>
                                    <th scope="col" class="px-6 py-4 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">الحالة</th>
                                    <th scope="col" class="px-6 py-4 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">المستخدم</th>
                                    <th scope="col" class="px-6 py-4 text-center text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white dark:bg-dark-100 divide-y divide-gray-200 dark:divide-gray-700">
                                {% for order in orders.items %}
                                <tr class="hover:bg-gray-50 dark:hover:bg-dark-200 transition-colors duration-150">
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="flex items-center">
                                            <div class="w-8 h-8 rounded-lg bg-blue-100 dark:bg-blue-900/30 flex items-center justify-center text-primary dark:text-blue-400 ml-2">
                                                <i class="ri-bill-line"></i>
                                            </div>
                                            <div class="text-sm font-medium text-gray-900 dark:text-white">{{ order.invoice_number }}</div>
                                        </div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="flex items-center">
                                            <div class="flex-shrink-0 h-8 w-8 rounded-lg bg-gray-100 dark:bg-dark-300 flex items-center justify-center text-gray-500 dark:text-gray-400">
                                                <i class="ri-user-line"></i>
                                            </div>
                                            <div class="mr-3">
                                                <div class="text-sm font-medium text-gray-900 dark:text-white">{{ order.customer.name if order.customer else 'عميل عام' }}</div>
                                                {% if order.customer and order.customer.phone %}
                                                <div class="text-xs text-gray-500 dark:text-gray-400">{{ order.customer.phone }}</div>
                                                {% endif %}
                                            </div>
                                        </div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="flex items-center">
                                            <div class="w-8 h-8 rounded-lg bg-purple-100 dark:bg-purple-900/30 flex items-center justify-center text-purple-600 dark:text-purple-400 ml-2">
                                                <i class="ri-calendar-line"></i>
                                            </div>
                                            <div>
                                                <div class="text-sm text-gray-900 dark:text-white">{{ order.created_at.strftime('%Y-%m-%d') }}</div>
                                                <div class="text-xs text-gray-500 dark:text-gray-400">{{ order.created_at.strftime('%H:%M') }}</div>
                                            </div>
                                        </div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="flex items-center">
                                            <div class="w-8 h-8 rounded-lg bg-green-100 dark:bg-green-900/30 flex items-center justify-center text-green-600 dark:text-green-400 ml-2">
                                                <i class="ri-money-dollar-circle-line"></i>
                                            </div>
                                            <div>
                                                <div class="text-sm font-bold text-gray-900 dark:text-white">{{ "%.2f"|format(order.total) }} ج.م</div>
                                                <div class="text-xs text-gray-500 dark:text-gray-400">{{ order.items|length }} منتج</div>
                                            </div>
                                        </div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        {% if order.payment_method == 'cash' %}
                                        <span class="inline-flex items-center px-3 py-1.5 rounded-lg text-xs font-medium bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-300">
                                            <i class="ri-money-dollar-circle-line ml-1"></i>
                                            <span>نقدي</span>
                                        </span>
                                        {% elif order.payment_method == 'card' %}
                                        <span class="inline-flex items-center px-3 py-1.5 rounded-lg text-xs font-medium bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-300">
                                            <i class="ri-bank-card-line ml-1"></i>
                                            <span>بطاقة</span>
                                        </span>
                                        {% else %}
                                        <span class="inline-flex items-center px-3 py-1.5 rounded-lg text-xs font-medium bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-300">
                                            <i class="ri-question-line ml-1"></i>
                                            <span>{{ order.payment_method }}</span>
                                        </span>
                                        {% endif %}
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        {% if order.status == 'completed' %}
                                        <span class="inline-flex items-center px-3 py-1.5 rounded-lg text-xs font-medium bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-300">
                                            <i class="ri-check-double-line ml-1"></i>
                                            <span>مكتمل</span>
                                        </span>
                                        {% elif order.status == 'pending' %}
                                        <span class="inline-flex items-center px-3 py-1.5 rounded-lg text-xs font-medium bg-yellow-100 dark:bg-yellow-900/30 text-yellow-800 dark:text-yellow-300">
                                            <i class="ri-time-line ml-1"></i>
                                            <span>معلق</span>
                                        </span>
                                        {% elif order.status == 'cancelled' %}
                                        <span class="inline-flex items-center px-3 py-1.5 rounded-lg text-xs font-medium bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-300">
                                            <i class="ri-close-circle-line ml-1"></i>
                                            <span>ملغي</span>
                                        </span>
                                        {% else %}
                                        <span class="inline-flex items-center px-3 py-1.5 rounded-lg text-xs font-medium bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-300">
                                            <i class="ri-question-line ml-1"></i>
                                            <span>{{ order.status }}</span>
                                        </span>
                                        {% endif %}
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="flex items-center">
                                            <div class="w-8 h-8 rounded-lg bg-gray-100 dark:bg-gray-700/50 flex items-center justify-center text-gray-500 dark:text-gray-400 ml-2">
                                                <i class="ri-user-line"></i>
                                            </div>
                                            <div>
                                                {% if order.created_by %}
                                                    {% set user = order.get_user() %}
                                                    {% if user %}
                                                        <div class="text-sm font-medium text-gray-900 dark:text-white">
                                                            <a href="{{ url_for('users.edit', id=user.id) }}" class="hover:text-primary dark:hover:text-blue-400 transition-colors">
                                                                {{ user.username }}
                                                            </a>
                                                        </div>
                                                        {% if user.role %}
                                                        <div class="text-xs text-gray-500 dark:text-gray-400">{{ user.role }}</div>
                                                        {% endif %}
                                                    {% else %}
                                                        <div class="text-sm text-gray-900 dark:text-white">غير معروف</div>
                                                    {% endif %}
                                                {% else %}
                                                    <div class="text-sm text-gray-900 dark:text-white">غير محدد</div>
                                                {% endif %}
                                            </div>
                                        </div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-center">
                                        <div class="flex justify-center space-x-2 space-x-reverse">
                                            <a href="{{ url_for('sales.details', id=order.id) }}"
                                               class="bg-blue-50 dark:bg-blue-900/20 text-primary dark:text-blue-400 hover:bg-blue-100 dark:hover:bg-blue-900/30 p-2 rounded-lg transition-all tooltip"
                                               data-tooltip="عرض التفاصيل">
                                                <i class="ri-eye-line text-lg"></i>
                                            </a>

                                            <a href="#" onclick="openReturnModal('{{ order.id }}', '{{ order.invoice_number }}')"
                                               class="bg-purple-50 dark:bg-purple-900/20 text-purple-600 dark:text-purple-400 hover:bg-purple-100 dark:hover:bg-purple-900/30 p-2 rounded-lg transition-all tooltip"
                                               data-tooltip="استرجاع الفاتورة">
                                                <i class="ri-arrow-go-back-line text-lg"></i>
                                            </a>

                                            <a href="#" onclick="printReceipt('{{ order.invoice_number }}')"
                                               class="bg-green-50 dark:bg-green-900/20 text-green-600 dark:text-green-400 hover:bg-green-100 dark:hover:bg-green-900/30 p-2 rounded-lg transition-all tooltip"
                                               data-tooltip="طباعة الفاتورة">
                                                <i class="ri-printer-line text-lg"></i>
                                            </a>

                                            {% if order.status != 'cancelled' and order.status != 'returned' and order.status != 'partially_returned' %}
                                            <a href="{{ url_for('sales.cancel', id=order.id) }}"
                                               onclick="return confirm('هل أنت متأكد من إلغاء هذه الفاتورة؟')"
                                               class="bg-red-50 dark:bg-red-900/20 text-red-600 dark:text-red-400 hover:bg-red-100 dark:hover:bg-red-900/30 p-2 rounded-lg transition-all tooltip"
                                               data-tooltip="إلغاء الفاتورة">
                                                <i class="ri-close-circle-line text-lg"></i>
                                            </a>
                                            {% endif %}
                                        </div>
                                    </td>
                                </tr>
                                {% else %}
                                <tr>
                                    <td colspan="8" class="px-6 py-12 text-center">
                                        <div class="flex flex-col items-center">
                                            <div class="w-20 h-20 rounded-full bg-gray-100 dark:bg-dark-300 flex items-center justify-center text-gray-400 dark:text-gray-500 mb-4">
                                                <i class="ri-search-line text-4xl"></i>
                                            </div>
                                            <p class="text-gray-600 dark:text-gray-300 mb-2 text-lg">لا توجد مبيعات مطابقة</p>
                                            <p class="text-gray-500 dark:text-gray-400 text-sm">حاول استخدام معايير بحث مختلفة أو <a href="{{ url_for('sales.index') }}" class="text-primary dark:text-blue-400 hover:underline">عرض جميع المبيعات</a></p>
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    {% if orders.pages > 1 %}
                    <div class="px-6 py-4 bg-gray-50 dark:bg-dark-200 border-t border-gray-200 dark:border-gray-700 flex flex-col sm:flex-row items-center justify-between gap-4">
                        <div class="text-sm text-gray-500 dark:text-gray-400 flex items-center">
                            <span class="inline-flex items-center">
                                <i class="ri-pages-line ml-1.5"></i>
                                عرض <span class="font-medium text-gray-700 dark:text-gray-300 mx-1">{{ orders.items|length }}</span> من
                                <span class="font-medium text-gray-700 dark:text-gray-300 mx-1">{{ orders.total }}</span> فاتورة
                            </span>
                        </div>

                        <div class="flex space-x-1 space-x-reverse">
                            {% if orders.has_prev %}
                            <a href="{{ url_for('sales.index', page=orders.prev_num, search=search, customer_id=customer_id, status=status, payment_method=payment_method, date_from=date_from, date_to=date_to) }}"
                               class="flex items-center px-3 py-2 text-sm text-gray-700 dark:text-gray-300 bg-white dark:bg-dark-100 rounded-lg border border-gray-300 dark:border-gray-600 hover:bg-gray-50 dark:hover:bg-dark-200 transition-colors">
                                <i class="ri-arrow-right-s-line ml-1"></i>
                                <span>السابق</span>
                            </a>
                            {% else %}
                            <span class="flex items-center px-3 py-2 text-sm text-gray-400 dark:text-gray-600 bg-gray-100 dark:bg-dark-300 rounded-lg border border-gray-200 dark:border-gray-700 opacity-60 cursor-not-allowed">
                                <i class="ri-arrow-right-s-line ml-1"></i>
                                <span>السابق</span>
                            </span>
                            {% endif %}

                            <div class="hidden sm:flex space-x-1 space-x-reverse">
                            {% for page_num in orders.iter_pages(left_edge=1, right_edge=1, left_current=2, right_current=2) %}
                                {% if page_num %}
                                    {% if page_num == orders.page %}
                                    <span class="flex items-center justify-center w-10 h-10 text-white bg-primary dark:bg-blue-600 rounded-lg text-sm font-medium shadow-sm">
                                        {{ page_num }}
                                    </span>
                                    {% else %}
                                    <a href="{{ url_for('sales.index', page=page_num, search=search, customer_id=customer_id, status=status, payment_method=payment_method, date_from=date_from, date_to=date_to) }}"
                                       class="flex items-center justify-center w-10 h-10 text-gray-700 dark:text-gray-300 bg-white dark:bg-dark-100 rounded-lg border border-gray-300 dark:border-gray-600 hover:bg-gray-50 dark:hover:bg-dark-200 text-sm transition-colors">
                                        {{ page_num }}
                                    </a>
                                    {% endif %}
                                {% else %}
                                <span class="flex items-center justify-center w-10 h-10 text-gray-500 dark:text-gray-400">...</span>
                                {% endif %}
                            {% endfor %}
                            </div>

                            {% if orders.has_next %}
                            <a href="{{ url_for('sales.index', page=orders.next_num, search=search, customer_id=customer_id, status=status, payment_method=payment_method, date_from=date_from, date_to=date_to) }}"
                               class="flex items-center px-3 py-2 text-sm text-gray-700 dark:text-gray-300 bg-white dark:bg-dark-100 rounded-lg border border-gray-300 dark:border-gray-600 hover:bg-gray-50 dark:hover:bg-dark-200 transition-colors">
                                <span>التالي</span>
                                <i class="ri-arrow-left-s-line mr-1"></i>
                            </a>
                            {% else %}
                            <span class="flex items-center px-3 py-2 text-sm text-gray-400 dark:text-gray-600 bg-gray-100 dark:bg-dark-300 rounded-lg border border-gray-200 dark:border-gray-700 opacity-60 cursor-not-allowed">
                                <span>التالي</span>
                                <i class="ri-arrow-left-s-line mr-1"></i>
                            </span>
                            {% endif %}
                        </div>
                    </div>
                    {% endif %}
                </div>

                <!-- Top Products - تصميم جديد -->
                <div class="bg-white dark:bg-dark-100 rounded-lg overflow-hidden mt-6 border border-gray-200 dark:border-gray-700 shadow-sm">
                    <div class="p-4 bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/10 dark:to-indigo-900/10 border-b border-gray-200 dark:border-gray-700 flex flex-col sm:flex-row justify-between items-center gap-3">
                        <h3 class="text-base font-bold text-gray-800 dark:text-white flex items-center">
                            <i class="ri-trophy-line ml-2 text-yellow-500 dark:text-yellow-400"></i>
                            <span>المنتجات الأكثر مبيعًا هذا الشهر</span>
                            <span class="mr-2 text-xs bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-400 px-2 py-0.5 rounded-full">
                                تحديث تلقائي
                            </span>
                        </h3>
                        <div class="flex items-center gap-2">
                            <span class="text-xs text-gray-500 dark:text-gray-400 bg-white dark:bg-dark-200 px-2 py-1 rounded-md shadow-sm">
                                <i class="ri-calendar-line ml-1"></i>
                                <span>{{ stats.month_order_count }} طلب هذا الشهر</span>
                            </span>
                            <a href="{{ url_for('products.index') }}" class="text-xs text-primary dark:text-blue-400 hover:underline flex items-center">
                                <i class="ri-external-link-line ml-1"></i>
                                <span>عرض كل المنتجات</span>
                            </a>
                        </div>
                    </div>

                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                            <thead class="bg-gray-50 dark:bg-dark-200">
                                <tr>
                                    <th scope="col" class="px-4 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">المنتج</th>
                                    <th scope="col" class="px-4 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">الكمية المباعة</th>
                                    <th scope="col" class="px-4 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">إجمالي المبيعات</th>
                                    <th scope="col" class="px-4 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">النسبة</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white dark:bg-dark-100 divide-y divide-gray-200 dark:divide-gray-700">
                                {% for product, quantity, total in stats.top_products %}
                                <tr class="hover:bg-gray-50 dark:hover:bg-dark-200 transition-colors duration-150">
                                    <td class="px-4 py-3 whitespace-nowrap">
                                        <div class="flex items-center">
                                            <div class="flex-shrink-0 h-10 w-10 rounded-lg bg-gray-100 dark:bg-dark-300 flex items-center justify-center text-gray-500 dark:text-gray-400 overflow-hidden border border-gray-200 dark:border-gray-600">
                                                {% if product.image_path %}
                                                <img src="{{ product.image_path }}" alt="{{ product.name }}" class="h-10 w-10 rounded-lg object-cover">
                                                {% else %}
                                                <i class="ri-shopping-bag-line text-xl"></i>
                                                {% endif %}
                                            </div>
                                            <div class="mr-3">
                                                <div class="text-sm font-medium text-gray-900 dark:text-white">
                                                    <a href="{{ url_for('products.edit', id=product.id) }}" class="hover:text-primary dark:hover:text-blue-400 transition-colors">
                                                        {{ product.name }}
                                                    </a>
                                                </div>
                                                <div class="text-xs text-gray-500 dark:text-gray-400 font-mono">{{ product.code or '-' }}</div>
                                            </div>
                                        </div>
                                    </td>
                                    <td class="px-4 py-3 whitespace-nowrap">
                                        <div class="flex items-center">
                                            <div class="w-8 h-8 rounded-lg bg-green-50 dark:bg-green-900/20 flex items-center justify-center text-green-500 dark:text-green-400 ml-2">
                                                <i class="ri-shopping-basket-line"></i>
                                            </div>
                                            <div>
                                                <div class="text-sm font-medium text-gray-900 dark:text-white">{{ quantity }} قطعة</div>
                                                {% if product.stock_quantity is defined %}
                                                <div class="text-xs text-gray-500 dark:text-gray-400">
                                                    المخزون:
                                                    <span class="{% if product.stock_quantity < 10 %}text-red-500 dark:text-red-400{% else %}text-green-500 dark:text-green-400{% endif %}">
                                                        {{ product.stock_quantity }}
                                                    </span>
                                                </div>
                                                {% endif %}
                                            </div>
                                        </div>
                                    </td>
                                    <td class="px-4 py-3 whitespace-nowrap">
                                        <div class="flex items-center">
                                            <div class="w-8 h-8 rounded-lg bg-blue-50 dark:bg-blue-900/20 flex items-center justify-center text-primary dark:text-blue-400 ml-2">
                                                <i class="ri-money-dollar-circle-line"></i>
                                            </div>
                                            <div>
                                                <div class="text-sm font-bold text-gray-900 dark:text-white">{{ "%.2f"|format(total) }} ج.م</div>
                                                <div class="text-xs text-gray-500 dark:text-gray-400">متوسط {{ "%.2f"|format(total / quantity) }} ج.م</div>
                                            </div>
                                        </div>
                                    </td>
                                    <td class="px-4 py-3 whitespace-nowrap">
                                        {% set percentage = (total / stats.month_sales * 100) if stats.month_sales > 0 else 0 %}
                                        <div class="flex flex-col">
                                            <div class="flex items-center justify-between mb-1">
                                                <span class="text-xs text-gray-500 dark:text-gray-400">نسبة المبيعات</span>
                                                <span class="text-xs font-medium text-primary dark:text-blue-400">{{ percentage|round|int }}٪</span>
                                            </div>
                                            <div class="w-full h-1.5 bg-gray-100 dark:bg-gray-800 rounded-full overflow-hidden">
                                                <div class="h-full bg-gradient-to-r from-blue-500 to-indigo-500 dark:from-blue-600 dark:to-indigo-600 rounded-full" style="width: {{ percentage|round|int }}%"></div>
                                            </div>
                                        </div>
                                    </td>
                                </tr>
                                {% else %}
                                <tr>
                                    <td colspan="4" class="px-6 py-8 text-center">
                                        <div class="flex flex-col items-center">
                                            <div class="w-16 h-16 rounded-full bg-gray-100 dark:bg-dark-300 flex items-center justify-center text-gray-400 dark:text-gray-500 mb-4">
                                                <i class="ri-shopping-cart-line text-3xl"></i>
                                            </div>
                                            <p class="text-gray-600 dark:text-gray-300 mb-2">لا توجد مبيعات هذا الشهر</p>
                                            <p class="text-gray-500 dark:text-gray-400 text-sm">ستظهر هنا المنتجات الأكثر مبيعاً بمجرد تسجيل مبيعات جديدة</p>
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    <div class="p-3 bg-gray-50 dark:bg-dark-200 border-t border-gray-200 dark:border-gray-700 text-xs text-gray-500 dark:text-gray-400 text-center">
                        <i class="ri-information-line ml-1"></i>
                        <span>يتم تحديث هذه البيانات تلقائياً وتستثني المنتجات المرتجعة من الحسابات</span>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <!-- زر تبديل الوضع الداكن -->
    <div class="theme-toggle" id="themeToggle">
        <i class="ri-moon-line dark:hidden"></i>
        <i class="ri-sun-line hidden dark:block"></i>
    </div>

    <!-- نموذج استرجاع المنتجات -->
    <div id="returnProductsModal" class="fixed inset-0 bg-black bg-opacity-60 z-50 flex items-center justify-center hidden backdrop-blur-sm">
        <div id="return-products-modal-content" class="bg-white dark:bg-dark-100 rounded-xl shadow-2xl w-full max-w-lg mx-4 transform scale-95 opacity-0 transition-all duration-300 max-h-[90vh] overflow-y-auto">
            <div class="p-4 border-b border-gray-200 dark:border-gray-700 flex justify-between items-center bg-gradient-to-r from-red-50 to-white dark:from-red-900/10 dark:to-dark-100">
                <h3 class="text-xl font-bold text-gray-800 dark:text-white flex items-center">
                    <i class="ri-arrow-go-back-line ml-2 text-red-500 dark:text-red-400"></i>
                    <span>مرتجع المبيعات</span>
                </h3>
                <button id="close-return-modal" class="text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 p-1 rounded-full hover:bg-gray-100 dark:hover:bg-dark-300 transition-colors">
                    <i class="ri-close-line text-2xl"></i>
                </button>
            </div>
            <div class="p-6">
                <!-- معلومات الفاتورة -->
                <div class="bg-gray-50 dark:bg-dark-200 p-4 rounded-lg mb-4 border border-gray-200 dark:border-gray-700">
                    <div class="flex justify-between items-center mb-2">
                        <span class="text-sm text-gray-600 dark:text-gray-400">رقم الفاتورة:</span>
                        <span id="return-invoice-number" class="font-bold text-primary dark:text-blue-400"></span>
                    </div>
                    <div class="flex justify-between items-center mb-2">
                        <span class="text-sm text-gray-600 dark:text-gray-400">العميل:</span>
                        <span id="return-customer-name" class="text-gray-800 dark:text-gray-300"></span>
                    </div>
                    <div class="flex justify-between items-center">
                        <span class="text-sm text-gray-600 dark:text-gray-400">تاريخ الفاتورة:</span>
                        <span id="return-invoice-date" class="text-gray-800 dark:text-gray-300 text-sm"></span>
                    </div>
                </div>

                <!-- قائمة المنتجات -->
                <div class="mb-4">
                    <h4 class="text-sm font-bold text-gray-700 dark:text-gray-300 mb-2 flex items-center">
                        <i class="ri-shopping-basket-line ml-1 text-green-500 dark:text-green-400"></i>
                        المنتجات المراد استرجاعها
                    </h4>
                    <div id="return-products-list" class="space-y-2 max-h-60 overflow-y-auto">
                        <!-- سيتم إضافة المنتجات هنا ديناميكياً -->
                    </div>
                </div>

                <!-- إجمالي المرتجع -->
                <div class="bg-gray-50 dark:bg-dark-200 p-4 rounded-lg mb-4 border border-gray-200 dark:border-gray-700">
                    <div class="flex justify-between items-center mb-2">
                        <span class="text-sm font-bold text-gray-700 dark:text-gray-300">إجمالي المرتجع:</span>
                        <span id="return-total-amount" class="font-bold text-red-500 dark:text-red-400">0.00 ج.م</span>
                    </div>
                </div>

                <!-- طريقة استرداد المبلغ -->
                <div class="mb-4">
                    <label class="block text-sm font-bold text-gray-700 dark:text-gray-300 mb-2 flex items-center">
                        <i class="ri-bank-card-line ml-1 text-orange-500 dark:text-orange-400"></i>
                        طريقة استرداد المبلغ
                    </label>
                    <div class="grid grid-cols-3 gap-2">
                        <div class="return-payment-method border border-gray-200 dark:border-gray-700 rounded-lg p-2 cursor-pointer hover:border-red-500 dark:hover:border-red-500 transition-all duration-300 payment-method-active shadow-sm" data-method="cash">
                            <div class="flex flex-col items-center">
                                <div class="w-8 h-8 rounded-full bg-green-100 dark:bg-green-900/30 flex items-center justify-center text-green-500 dark:text-green-400 mb-1">
                                    <i class="ri-money-dollar-circle-line"></i>
                                </div>
                                <span class="text-xs font-medium text-gray-700 dark:text-gray-300">نقدي</span>
                            </div>
                        </div>
                        <div class="return-payment-method border border-gray-200 dark:border-gray-700 rounded-lg p-2 cursor-pointer hover:border-red-500 dark:hover:border-red-500 transition-all duration-300 shadow-sm" data-method="card">
                            <div class="flex flex-col items-center">
                                <div class="w-8 h-8 rounded-full bg-blue-100 dark:bg-blue-900/30 flex items-center justify-center text-blue-500 dark:text-blue-400 mb-1">
                                    <i class="ri-bank-card-line"></i>
                                </div>
                                <span class="text-xs font-medium text-gray-700 dark:text-gray-300">بطاقة</span>
                            </div>
                        </div>
                        <div class="return-payment-method border border-gray-200 dark:border-gray-700 rounded-lg p-2 cursor-pointer hover:border-red-500 dark:hover:border-red-500 transition-all duration-300 shadow-sm" data-method="store_credit">
                            <div class="flex flex-col items-center">
                                <div class="w-8 h-8 rounded-full bg-purple-100 dark:bg-purple-900/30 flex items-center justify-center text-purple-500 dark:text-purple-400 mb-1">
                                    <i class="ri-store-2-line"></i>
                                </div>
                                <span class="text-xs font-medium text-gray-700 dark:text-gray-300">رصيد متجر</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- ملاحظات المرتجع -->
                <div class="mb-4">
                    <label for="return-notes" class="block text-sm font-bold text-gray-700 dark:text-gray-300 mb-2 flex items-center">
                        <i class="ri-file-text-line ml-1 text-gray-500 dark:text-gray-400"></i>
                        ملاحظات
                    </label>
                    <textarea id="return-notes" class="w-full rounded-lg border-gray-300 dark:border-gray-600 dark:bg-dark-200 dark:text-gray-300 focus:ring-red-500 focus:border-red-500 text-sm" rows="2" placeholder="أدخل أي ملاحظات إضافية حول المرتجع..."></textarea>
                </div>

                <div class="flex flex-col sm:flex-row gap-3">
                    <button type="button" id="confirm-return-products"
                            class="bg-gradient-to-r from-red-500 to-red-600 dark:from-red-600 dark:to-red-700 text-white px-5 py-3 rounded-xl shadow-md hover:shadow-lg hover:shadow-red-500/20 dark:hover:shadow-red-800/20 transition-all duration-300 flex-1 flex items-center justify-center gap-2">
                        <i class="ri-arrow-go-back-line text-lg"></i>
                        <span class="font-medium">تأكيد الاسترجاع</span>
                    </button>

                    <button type="button" id="cancel-return-products"
                            class="bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 px-5 py-3 rounded-xl shadow-md hover:bg-gray-200 dark:hover:bg-gray-600 transition-all duration-300 flex items-center justify-center gap-2">
                        <i class="ri-close-line text-lg"></i>
                        <span class="font-medium">إلغاء</span>
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- نموذج طباعة الفاتورة -->
    <div id="printReceiptModal" class="fixed inset-0 bg-black bg-opacity-50 dark:bg-black dark:bg-opacity-70 flex items-center justify-center z-50 hidden backdrop-blur-sm">
        <div class="bg-white dark:bg-dark-100 rounded-xl w-full max-w-lg mx-4 shadow-2xl transform transition-all duration-300 scale-100">
            <div class="p-4 border-b border-gray-200 dark:border-gray-700 flex justify-between items-center">
                <h3 class="text-xl font-bold text-gray-800 dark:text-white flex items-center">
                    <i class="ri-printer-line ml-2 text-primary dark:text-blue-400"></i>
                    <span>طباعة الفاتورة</span>
                </h3>
                <button onclick="closePrintModal()" class="text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 p-1 rounded-full hover:bg-gray-100 dark:hover:bg-dark-300 transition-colors">
                    <i class="ri-close-line text-2xl"></i>
                </button>
            </div>
            <div class="p-6">
                <div class="mb-6">
                    <div class="flex items-center mb-4">
                        <div class="w-10 h-10 rounded-lg bg-blue-100 dark:bg-blue-900/30 flex items-center justify-center text-primary dark:text-blue-400 mr-3">
                            <i class="ri-file-list-3-line text-xl"></i>
                        </div>
                        <div>
                            <h4 class="text-gray-800 dark:text-white font-medium">معلومات الفاتورة</h4>
                            <p class="text-sm text-gray-500 dark:text-gray-400">رقم الفاتورة: <span id="invoiceNumberDisplay" class="font-medium text-gray-700 dark:text-gray-300"></span></p>
                        </div>
                    </div>

                    <div class="bg-gray-50 dark:bg-dark-200 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
                        <div class="flex items-center justify-between mb-4">
                            <div class="text-sm text-gray-700 dark:text-gray-300">
                                <span class="font-medium">خيارات الطباعة</span>
                            </div>
                        </div>

                        <div class="space-y-3">
                            <label class="flex items-center p-2.5 bg-white dark:bg-dark-300 rounded-lg hover:bg-gray-100 dark:hover:bg-dark-400 transition-colors cursor-pointer">
                                <input type="radio" name="printType" value="receipt" checked
                                       class="form-radio h-5 w-5 text-primary dark:text-blue-500 rounded-full border-gray-300 dark:border-gray-600 focus:ring-primary dark:focus:ring-blue-500 ml-2">
                                <div>
                                    <span class="text-gray-700 dark:text-gray-300 block">إيصال صغير</span>
                                    <span class="text-xs text-gray-500 dark:text-gray-400">طباعة إيصال بحجم صغير (80مم)</span>
                                </div>
                            </label>

                            <label class="flex items-center p-2.5 bg-white dark:bg-dark-300 rounded-lg hover:bg-gray-100 dark:hover:bg-dark-400 transition-colors cursor-pointer">
                                <input type="radio" name="printType" value="invoice"
                                       class="form-radio h-5 w-5 text-primary dark:text-blue-500 rounded-full border-gray-300 dark:border-gray-600 focus:ring-primary dark:focus:ring-blue-500 ml-2">
                                <div>
                                    <span class="text-gray-700 dark:text-gray-300 block">فاتورة كاملة</span>
                                    <span class="text-xs text-gray-500 dark:text-gray-400">طباعة فاتورة بحجم A4</span>
                                </div>
                            </label>

                            <label class="flex items-center p-2.5 bg-white dark:bg-dark-300 rounded-lg hover:bg-gray-100 dark:hover:bg-dark-400 transition-colors cursor-pointer">
                                <input type="checkbox" id="includeLogo" checked
                                       class="form-checkbox h-5 w-5 text-primary dark:text-blue-500 rounded border-gray-300 dark:border-gray-600 focus:ring-primary dark:focus:ring-blue-500 ml-2">
                                <div>
                                    <span class="text-gray-700 dark:text-gray-300 block">إضافة شعار المتجر</span>
                                    <span class="text-xs text-gray-500 dark:text-gray-400">طباعة شعار المتجر في الفاتورة</span>
                                </div>
                            </label>
                        </div>
                    </div>
                </div>

                <div class="flex flex-col sm:flex-row gap-3">
                    <button type="button" onclick="doPrintReceipt()"
                            class="bg-gradient-to-r from-primary to-blue-600 dark:from-blue-600 dark:to-blue-700 text-white px-5 py-3 rounded-xl shadow-md hover:shadow-lg hover:shadow-blue-500/20 dark:hover:shadow-blue-800/20 transition-all duration-300 flex-1 flex items-center justify-center gap-2">
                        <i class="ri-printer-line text-lg"></i>
                        <span class="font-medium">طباعة</span>
                    </button>

                    <button type="button" onclick="closePrintModal()"
                            class="bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 px-5 py-3 rounded-xl shadow-md hover:bg-gray-200 dark:hover:bg-gray-600 transition-all duration-300 flex items-center justify-center gap-2">
                        <i class="ri-close-line text-lg"></i>
                        <span class="font-medium">إلغاء</span>
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- قالب عنصر المنتج في قائمة المرتجعات -->
    <template id="sales-return-product-template">
        <div class="return-product-item bg-white dark:bg-dark-200 p-3 rounded-lg border border-gray-200 dark:border-gray-700 hover:border-red-200 dark:hover:border-red-700 transition-all duration-300">
            <div class="flex justify-between items-center mb-2">
                <div class="font-medium text-sm text-gray-800 dark:text-gray-200 return-product-name"></div>
                <div class="text-primary dark:text-blue-400 font-bold text-sm return-product-price"></div>
            </div>
            <div class="flex justify-between items-center">
                <div class="flex items-center">
                    <span class="text-xs text-gray-500 dark:text-gray-400 ml-2">الكمية:</span>
                    <div class="flex items-center">
                        <button class="return-quantity-decrease w-6 h-6 rounded-full bg-gray-100 dark:bg-gray-700 flex items-center justify-center text-gray-600 dark:text-gray-400 hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors">
                            <i class="ri-subtract-line"></i>
                        </button>
                        <input type="number" class="return-quantity mx-1 w-10 text-center border border-gray-200 dark:border-gray-700 dark:bg-dark-300 dark:text-gray-300 rounded text-sm py-1" min="1" value="1">
                        <button class="return-quantity-increase w-6 h-6 rounded-full bg-gray-100 dark:bg-gray-700 flex items-center justify-center text-gray-600 dark:text-gray-400 hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors">
                            <i class="ri-add-line"></i>
                        </button>
                    </div>
                </div>
                <div class="flex items-center">
                    <span class="text-xs text-gray-500 dark:text-gray-400 ml-2">الإجمالي:</span>
                    <span class="return-product-total font-bold text-red-500 dark:text-red-400"></span>
                </div>
            </div>
        </div>
    </template>

    <!-- JavaScript -->
    <script src="{{ url_for('static', filename='js/sales_returns.js') }}"></script>

    <script>
        // تبديل الوضع الداكن
        document.addEventListener('DOMContentLoaded', function() {
            const themeToggle = document.getElementById('themeToggle');

            // التحقق من الوضع المحفوظ
            function applyTheme() {
                if (localStorage.getItem('theme') === 'dark') {
                    document.documentElement.classList.add('dark');
                    document.documentElement.classList.remove('light');
                } else {
                    document.documentElement.classList.remove('dark');
                    document.documentElement.classList.add('light');
                }
            }

            // تطبيق الوضع المحفوظ عند تحميل الصفحة
            applyTheme();

            // تبديل الوضع عند النقر على الزر
            themeToggle.addEventListener('click', function() {
                if (document.documentElement.classList.contains('dark')) {
                    localStorage.setItem('theme', 'light');
                } else {
                    localStorage.setItem('theme', 'dark');
                }
                applyTheme();
            });
        });



        // طباعة الفاتورة
        const printReceiptModal = document.getElementById('printReceiptModal');
        const invoiceNumberDisplay = document.getElementById('invoiceNumberDisplay');
        let currentInvoiceNumber = '';

        function printReceipt(invoiceNumber) {
            currentInvoiceNumber = invoiceNumber;
            invoiceNumberDisplay.textContent = invoiceNumber;
            printReceiptModal.classList.remove('hidden');
        }

        function closePrintModal() {
            const modalContent = printReceiptModal.querySelector('div');
            modalContent.classList.add('scale-95', 'opacity-0');

            setTimeout(() => {
                printReceiptModal.classList.add('hidden');
                modalContent.classList.remove('scale-95', 'opacity-0');
            }, 200);
        }

        function doPrintReceipt() {
            const printType = document.querySelector('input[name="printType"]:checked').value;
            const includeLogo = document.getElementById('includeLogo').checked;

            // هنا يمكن إضافة كود لطباعة الفاتورة الفعلية
            console.log(`طباعة فاتورة رقم: ${currentInvoiceNumber}`);
            console.log(`نوع الطباعة: ${printType}`);
            console.log(`إضافة الشعار: ${includeLogo}`);

            // إظهار رسالة نجاح
            alert(`جاري طباعة الفاتورة رقم: ${currentInvoiceNumber}`);

            // إغلاق النموذج
            closePrintModal();
        }
    </script>
</body>
</html>
