from flask import Blueprint, render_template, request, jsonify, flash, redirect, url_for, Response
from flask_login import login_required, current_user
from werkzeug.utils import secure_filename
from app import db
import models  # استيراد كامل models للوصول إلى models.OrderItem
from models import Product, Category, OrderItem, Warehouse, Inventory, Supplier
import os
import csv
import io
from datetime import datetime
from sqlalchemy import func

products_blueprint = Blueprint('products', __name__)

@products_blueprint.route('/products/import-export')
@login_required
def import_export():
    """صفحة استيراد وتصدير المنتجات"""
    # الحصول على قائمة المخازن والتصنيفات للفلترة
    warehouses = Warehouse.query.filter_by(is_active=True).all()
    categories = Category.query.all()

    return render_template('products/import_export.html',
                          warehouses=warehouses,
                          categories=categories)

@products_blueprint.route('/products')
@login_required
def index():
    # Get filter parameters
    search = request.args.get('search', '')
    category_id = request.args.get('category', '')
    status = request.args.get('status', '')

    # Prepare query
    query = Product.query

    # Apply filters
    if search:
        query = query.filter(Product.name.ilike(f'%{search}%') | Product.code.ilike(f'%{search}%'))

    if category_id and category_id.isdigit():
        query = query.filter(Product.category_id == int(category_id))

    if status == 'active':
        query = query.filter(Product.is_active == True)
    elif status == 'inactive':
        query = query.filter(Product.is_active == False)

    # Execute query with pagination
    page = request.args.get('page', 1, type=int)
    per_page = 10
    products_paginated = query.order_by(Product.name).paginate(page=page, per_page=per_page)

    # Get all categories for filter dropdown
    categories = Category.query.all()

    # Get product statistics
    total_products = Product.query.count()
    active_products = Product.query.filter_by(is_active=True).count()
    # استخدام استعلام أكثر أمانًا للمنتجات ذات المخزون المنخفض
    products = Product.query.filter_by(is_active=True).all()
    low_stock_products = 0
    for product in products:
        if product.stock_quantity > 0 and product.stock_quantity <= product.minimum_stock:
            low_stock_products += 1


    # Top selling products
    top_selling = db.session.query(
        Product,
        func.sum(models.OrderItem.quantity).label('total_sold')
    ).join(
        models.OrderItem
    ).group_by(
        Product.id
    ).order_by(
        func.sum(models.OrderItem.quantity).desc()
    ).limit(4).all()

    # تعديل استعلامات تنبيهات المخزون للتوافق مع نموذج البيانات
    products = Product.query.filter_by(is_active=True).all()

    # استخراج المنتجات ذات المخزون المنخفض
    low_stock_products_list = []
    out_of_stock_products_list = []

    for product in products:
        if product.stock_quantity <= 0:
            out_of_stock_products_list.append(product)
        elif product.stock_quantity <= product.minimum_stock:
            low_stock_products_list.append(product)

    # ترتيب المنتجات حسب الكمية في المخزون
    low_stock_alerts = sorted(low_stock_products_list, key=lambda p: p.stock_quantity)[:4]
    out_of_stock_alerts = sorted(out_of_stock_products_list, key=lambda p: p.name)[:4]

    stats = {
        'total_products': total_products,
        'active_products': active_products,
        'low_stock_products': low_stock_products,
        'category_count': Category.query.count(),
        'top_selling': top_selling,
        'low_stock_alerts': low_stock_alerts,
        'out_of_stock_alerts': out_of_stock_alerts
    }

    return render_template(
        'products/products.html',
        products=products_paginated,
        categories=categories,
        stats=stats,
        search=search,
        category_id=category_id,
        status=status,
        current_user=current_user
    )

@products_blueprint.route('/products/create', methods=['GET', 'POST'])
@login_required
def create():
    if request.method == 'POST':
        try:
            # Get form data
            name = request.form.get('name')
            code = request.form.get('code')
            brand = request.form.get('brand')
            description = request.form.get('description')
            price = float(request.form.get('price', 0))
            cost_price = float(request.form.get('cost_price', 0))
            stock_quantity = int(request.form.get('stock_quantity', 0))
            minimum_stock = int(request.form.get('minimum_stock', 5))
            category_id = int(request.form.get('category_id')) if request.form.get('category_id') else None
            supplier_id = int(request.form.get('supplier_id')) if request.form.get('supplier_id') else None
            is_active = True if request.form.get('is_active') == 'on' else False

            # الحصول على معلومات المخزن
            warehouse_id = request.form.get('warehouse_id')
            add_to_inventory = request.form.get('add_to_inventory') == 'on'

            # Create product
            product = Product(
                name=name,
                code=code,
                brand=brand,
                description=description,
                price=price,
                cost_price=cost_price,
                stock_quantity=stock_quantity,
                minimum_stock=minimum_stock,
                category_id=category_id,
                supplier_id=supplier_id,
                is_active=is_active
            )

            db.session.add(product)
            db.session.commit()

            # إضافة المنتج للمخزون إذا تم تحديد ذلك
            if add_to_inventory and warehouse_id and warehouse_id.isdigit():
                warehouse_id = int(warehouse_id)
                inventory = Inventory(
                    product_id=product.id,
                    warehouse_id=warehouse_id,
                    quantity=stock_quantity,
                    minimum_stock=minimum_stock
                )
                db.session.add(inventory)
                db.session.commit()

            flash('تم إضافة المنتج بنجاح', 'success')
            return redirect(url_for('products.index'))

        except Exception as e:
            db.session.rollback()
            flash(f'حدث خطأ أثناء إضافة المنتج: {str(e)}', 'danger')

    # Get all categories for dropdown
    categories = Category.query.all()
    # الحصول على قائمة المخازن
    warehouses = Warehouse.query.filter_by(is_active=True).all()
    # الحصول على قائمة الموردين
    suppliers = Supplier.query.all()

    return render_template('products/product_form.html', product=None, categories=categories, warehouses=warehouses, suppliers=suppliers, action='create')

@products_blueprint.route('/products/<int:id>/edit', methods=['GET', 'POST'])
@login_required
def edit(id):
    product = Product.query.get_or_404(id)

    if request.method == 'POST':
        try:
            # Get form data
            product.name = request.form.get('name')
            product.code = request.form.get('code')
            product.brand = request.form.get('brand')
            product.description = request.form.get('description')
            product.price = float(request.form.get('price', 0))
            product.cost_price = float(request.form.get('cost_price', 0))
            product.stock_quantity = int(request.form.get('stock_quantity', 0))
            product.minimum_stock = int(request.form.get('minimum_stock', 5))
            product.category_id = int(request.form.get('category_id')) if request.form.get('category_id') else None
            product.supplier_id = int(request.form.get('supplier_id')) if request.form.get('supplier_id') else None
            product.is_active = True if request.form.get('is_active') == 'on' else False

            # الحصول على معلومات المخزن
            warehouse_id = request.form.get('warehouse_id')
            update_inventory = request.form.get('update_inventory') == 'on'

            db.session.commit()

            # تحديث المخزون إذا تم تحديد ذلك
            if update_inventory and warehouse_id and warehouse_id.isdigit():
                warehouse_id = int(warehouse_id)

                # البحث عن المخزون الحالي
                inventory = Inventory.query.filter_by(
                    product_id=product.id,
                    warehouse_id=warehouse_id
                ).first()

                if inventory:
                    # تحديث المخزون الحالي
                    inventory.quantity = product.stock_quantity
                    inventory.minimum_stock = product.minimum_stock
                else:
                    # إنشاء مخزون جديد
                    inventory = Inventory(
                        product_id=product.id,
                        warehouse_id=warehouse_id,
                        quantity=product.stock_quantity,
                        minimum_stock=product.minimum_stock
                    )
                    db.session.add(inventory)

                db.session.commit()

            flash('تم تحديث المنتج بنجاح', 'success')
            return redirect(url_for('products.index'))

        except Exception as e:
            db.session.rollback()
            flash(f'حدث خطأ أثناء تحديث المنتج: {str(e)}', 'danger')

    # Get all categories for dropdown
    categories = Category.query.all()
    # الحصول على قائمة المخازن
    warehouses = Warehouse.query.filter_by(is_active=True).all()
    # الحصول على قائمة الموردين
    suppliers = Supplier.query.all()
    # الحصول على مخزون المنتج في المخازن المختلفة
    inventories = Inventory.query.filter_by(product_id=product.id).all()

    return render_template('products/product_form.html',
                          product=product,
                          categories=categories,
                          warehouses=warehouses,
                          suppliers=suppliers,
                          inventories=inventories,
                          action='edit')

@products_blueprint.route('/products/<int:id>/delete', methods=['POST'])
@login_required
def delete(id):
    product = Product.query.get_or_404(id)

    try:
        db.session.delete(product)
        db.session.commit()
        flash('تم حذف المنتج بنجاح', 'success')
    except Exception as e:
        db.session.rollback()
        flash(f'حدث خطأ أثناء حذف المنتج: {str(e)}', 'danger')

    return redirect(url_for('products.index'))

@products_blueprint.route('/api/products')
@login_required
def api_products():
    search = request.args.get('search', '')
    category_id = request.args.get('category', '')

    query = Product.query.filter_by(is_active=True)

    if search:
        query = query.filter(Product.name.ilike(f'%{search}%') | Product.code.ilike(f'%{search}%'))

    if category_id and category_id.isdigit():
        query = query.filter(Product.category_id == int(category_id))

    products = query.order_by(Product.name).all()

    return jsonify({
        'products': [product.to_dict() for product in products]
    })

@products_blueprint.route('/api/products/import', methods=['POST'])
@login_required
def import_products():
    """استيراد المنتجات من ملف CSV"""
    try:
        # التحقق من وجود ملف
        if 'file' not in request.files:
            return jsonify({'success': False, 'message': 'لم يتم تحديد ملف'}), 400

        file = request.files['file']

        # التحقق من اسم الملف
        if file.filename == '':
            return jsonify({'success': False, 'message': 'لم يتم تحديد ملف'}), 400

        # التحقق من امتداد الملف
        if not file.filename.endswith('.csv'):
            return jsonify({'success': False, 'message': 'يجب أن يكون الملف بصيغة CSV'}), 400

        # الحصول على خيارات الاستيراد
        update_existing = request.form.get('update_existing', 'false') == 'true'
        skip_errors = request.form.get('skip_errors', 'false') == 'true'

        # الحصول على مطابقة الحقول
        field_mapping = {}
        for key, value in request.form.items():
            if key.startswith('field_'):
                field_name = key.replace('field_', '')
                field_mapping[field_name] = value

        # التحقق من وجود الحقول المطلوبة
        required_fields = ['name', 'price']
        for field in required_fields:
            if field not in field_mapping or not field_mapping[field]:
                return jsonify({
                    'success': False,
                    'message': f'الحقل {field} مطلوب في مطابقة الحقول'
                }), 400

        # قراءة الملف مع محاولة اكتشاف الترميز
        try:
            # محاولة قراءة الملف بترميز UTF-8
            csv_content = file.read().decode('utf-8')
        except UnicodeDecodeError:
            # إذا فشلت القراءة بترميز UTF-8، نحاول بترميز آخر
            file.seek(0)  # إعادة مؤشر القراءة إلى بداية الملف
            try:
                csv_content = file.read().decode('latin-1')
            except UnicodeDecodeError:
                file.seek(0)
                try:
                    csv_content = file.read().decode('cp1256')  # ترميز عربي شائع
                except UnicodeDecodeError:
                    return jsonify({
                        'success': False,
                        'message': 'فشل في قراءة الملف. الترميز غير مدعوم.'
                    }), 400

        # اكتشاف الفاصل المستخدم في الملف
        dialect = 'excel'  # الافتراضي هو فاصلة
        if ';' in csv_content:
            dialect = 'excel-semicolon'
            csv.register_dialect('excel-semicolon', delimiter=';', quoting=csv.QUOTE_MINIMAL)
        elif '\t' in csv_content:
            dialect = 'excel-tab'
            csv.register_dialect('excel-tab', delimiter='\t', quoting=csv.QUOTE_MINIMAL)

        # قراءة الملف باستخدام الفاصل المناسب
        csv_reader = csv.reader(io.StringIO(csv_content), dialect=dialect)

        # قراءة الترويسة
        try:
            headers = next(csv_reader)
            # تنظيف العناوين من أي أحرف غير مرئية
            headers = [h.strip() for h in headers]
        except StopIteration:
            return jsonify({
                'success': False,
                'message': 'الملف فارغ أو لا يحتوي على بيانات صالحة.'
            }), 400

        # إعداد قائمة للمنتجات المستوردة والأخطاء
        imported_products = []
        errors = []

        # معالجة كل صف
        for row_index, row in enumerate(csv_reader, start=2):  # البدء من 2 لأن الصف 1 هو الترويسة
            try:
                # التحقق من أن الصف يحتوي على بيانات كافية
                if len(row) < len(headers):
                    if skip_errors:
                        errors.append(f'الصف {row_index}: عدد الأعمدة غير كافٍ')
                        continue
                    else:
                        return jsonify({
                            'success': False,
                            'message': f'الصف {row_index}: عدد الأعمدة غير كافٍ'
                        }), 400

                # إنشاء قاموس للصف
                row_data = {}
                for i, header in enumerate(headers):
                    if i < len(row):
                        row_data[header] = row[i]

                # استخراج بيانات المنتج باستخدام مطابقة الحقول
                product_data = {}
                for field, header in field_mapping.items():
                    if header in row_data:
                        product_data[field] = row_data[header]

                # التحقق من وجود الحقول المطلوبة
                if not product_data.get('name'):
                    if skip_errors:
                        errors.append(f'الصف {row_index}: اسم المنتج مطلوب')
                        continue
                    else:
                        return jsonify({
                            'success': False,
                            'message': f'الصف {row_index}: اسم المنتج مطلوب'
                        }), 400

                # تحويل الأنواع
                try:
                    if 'price' in product_data:
                        product_data['price'] = float(product_data['price'])

                    if 'cost_price' in product_data and product_data['cost_price']:
                        product_data['cost_price'] = float(product_data['cost_price'])

                    if 'stock_quantity' in product_data and product_data['stock_quantity']:
                        product_data['stock_quantity'] = int(product_data['stock_quantity'])

                    if 'minimum_stock' in product_data and product_data['minimum_stock']:
                        product_data['minimum_stock'] = int(product_data['minimum_stock'])

                    if 'is_active' in product_data:
                        product_data['is_active'] = product_data['is_active'].lower() in ['1', 'true', 'yes', 'y']
                except ValueError as e:
                    if skip_errors:
                        errors.append(f'الصف {row_index}: خطأ في تحويل البيانات - {str(e)}')
                        continue
                    else:
                        return jsonify({
                            'success': False,
                            'message': f'الصف {row_index}: خطأ في تحويل البيانات - {str(e)}'
                        }), 400

                # معالجة التصنيف
                if 'category' in product_data and product_data['category']:
                    category_name = product_data['category']
                    category = Category.query.filter_by(name=category_name).first()

                    if not category:
                        # إنشاء تصنيف جديد
                        category = Category(name=category_name)
                        db.session.add(category)
                        db.session.flush()  # للحصول على معرف التصنيف

                    product_data['category_id'] = category.id
                    del product_data['category']

                # البحث عن المنتج الحالي (إذا كان موجودًا)
                existing_product = None
                if 'code' in product_data and product_data['code']:
                    existing_product = Product.query.filter_by(code=product_data['code']).first()

                if not existing_product:
                    existing_product = Product.query.filter_by(name=product_data['name']).first()

                if existing_product and update_existing:
                    # تحديث المنتج الموجود
                    for key, value in product_data.items():
                        if hasattr(existing_product, key):
                            setattr(existing_product, key, value)

                    imported_products.append({
                        'id': existing_product.id,
                        'name': existing_product.name,
                        'status': 'updated'
                    })
                elif existing_product and not update_existing:
                    # تخطي المنتج الموجود
                    if skip_errors:
                        errors.append(f'الصف {row_index}: المنتج موجود بالفعل - {product_data["name"]}')
                        continue
                    else:
                        return jsonify({
                            'success': False,
                            'message': f'الصف {row_index}: المنتج موجود بالفعل - {product_data["name"]}'
                        }), 400
                else:
                    # إنشاء منتج جديد
                    new_product = Product(**product_data)
                    db.session.add(new_product)
                    db.session.flush()  # للحصول على معرف المنتج

                    imported_products.append({
                        'id': new_product.id,
                        'name': new_product.name,
                        'status': 'created'
                    })
            except Exception as e:
                if skip_errors:
                    errors.append(f'الصف {row_index}: {str(e)}')
                    continue
                else:
                    db.session.rollback()
                    return jsonify({
                        'success': False,
                        'message': f'الصف {row_index}: {str(e)}'
                    }), 400

        # حفظ التغييرات
        db.session.commit()

        return jsonify({
            'success': True,
            'message': f'تم استيراد {len(imported_products)} منتج بنجاح',
            'imported_products': imported_products,
            'errors': errors
        })
    except Exception as e:
        db.session.rollback()
        return jsonify({
            'success': False,
            'message': f'حدث خطأ أثناء استيراد المنتجات: {str(e)}'
        }), 500

@products_blueprint.route('/api/products/export')
@login_required
def export_products():
    """تصدير المنتجات إلى ملف CSV أو Excel"""
    # الحصول على معلمات التصفية
    category_id = request.args.get('category_id', '')
    warehouse_id = request.args.get('warehouse_id', '')
    active_only = request.args.get('active_only', '1')
    with_inventory = request.args.get('with_inventory', '1')
    export_format = request.args.get('format', 'csv')

    # إعداد الاستعلام
    query = Product.query

    # تطبيق الفلاتر
    if category_id and category_id.isdigit():
        query = query.filter_by(category_id=int(category_id))

    if active_only == '1':
        query = query.filter_by(is_active=True)

    # تنفيذ الاستعلام
    products = query.order_by(Product.name).all()

    # إنشاء ملف CSV
    output = io.StringIO()

    # تحديد الفاصل المستخدم (فاصلة أو فاصلة منقوطة)
    delimiter = ','
    if export_format == 'csv_semicolon':
        delimiter = ';'

    writer = csv.writer(output, delimiter=delimiter, quoting=csv.QUOTE_MINIMAL)

    # كتابة الترويسة
    headers = ['Name', 'Code', 'Category', 'Price', 'Cost_Price', 'Description', 'Stock_Quantity', 'Minimum_Stock', 'Brand', 'Is_Active']

    # إضافة أعمدة المخزون إذا تم طلبها
    if with_inventory == '1':
        if warehouse_id and warehouse_id.isdigit():
            # إضافة كمية المخزون لمستودع محدد
            warehouse = Warehouse.query.get(int(warehouse_id))
            if warehouse:
                headers.append(f'Quantity_{warehouse.name}')
        else:
            # إضافة كمية المخزون لجميع المستودعات
            warehouses = Warehouse.query.filter_by(is_active=True).all()
            for warehouse in warehouses:
                headers.append(f'Quantity_{warehouse.name}')

    # كتابة الترويسة بالعربية إذا تم طلب ذلك
    arabic_headers = {
        'Name': 'اسم المنتج',
        'Code': 'الباركود',
        'Category': 'التصنيف',
        'Price': 'سعر البيع',
        'Cost_Price': 'سعر التكلفة',
        'Description': 'الوصف',
        'Stock_Quantity': 'الكمية الإجمالية',
        'Minimum_Stock': 'الحد الأدنى للمخزون',
        'Brand': 'العلامة التجارية',
        'Is_Active': 'نشط'
    }

    # استخدام العناوين العربية إذا تم طلب ذلك
    use_arabic = request.args.get('arabic_headers', '0') == '1'
    final_headers = []

    for header in headers:
        if use_arabic and header in arabic_headers:
            final_headers.append(arabic_headers[header])
        elif use_arabic and header.startswith('Quantity_'):
            warehouse_name = header.replace('Quantity_', '')
            final_headers.append(f'كمية_{warehouse_name}')
        else:
            final_headers.append(header)

    writer.writerow(final_headers)

    # كتابة بيانات المنتجات
    for product in products:
        # إعداد البيانات الأساسية للمنتج
        row = [
            product.name,
            product.code or '',
            product.category.name if product.category else '',
            product.price,
            product.cost_price or 0,
            product.description or '',
            product.stock_quantity or 0,
            product.minimum_stock or 0,
            product.brand or '',
            '1' if product.is_active else '0'
        ]

        # إضافة كمية المخزون إذا تم طلبها
        if with_inventory == '1':
            if warehouse_id and warehouse_id.isdigit():
                # إضافة كمية المخزون لمستودع محدد
                inventory = Inventory.query.filter_by(
                    product_id=product.id,
                    warehouse_id=int(warehouse_id)
                ).first()

                row.append(inventory.quantity if inventory else 0)
            else:
                # إضافة كمية المخزون لجميع المستودعات
                warehouses = Warehouse.query.filter_by(is_active=True).all()
                for warehouse in warehouses:
                    inventory = Inventory.query.filter_by(
                        product_id=product.id,
                        warehouse_id=warehouse.id
                    ).first()

                    row.append(inventory.quantity if inventory else 0)

        # التعامل مع القيم الخاصة (مثل الفواصل في النصوص)
        for i in range(len(row)):
            if isinstance(row[i], str) and (delimiter in row[i] or '\n' in row[i]):
                # إحاطة القيم التي تحتوي على الفاصل أو أسطر جديدة بعلامات اقتباس
                row[i] = f'"{row[i]}"'

        writer.writerow(row)

    # إعداد الاستجابة
    output.seek(0)
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')

    # تحديد نوع الملف
    if export_format == 'excel':
        try:
            # استخدام مكتبة pandas لإنشاء ملف Excel
            import pandas as pd
            from io import BytesIO

            # إعادة تعيين مؤشر القراءة إلى بداية الملف
            output.seek(0)

            # قراءة البيانات من CSV
            df = pd.read_csv(output, encoding='utf-8')

            # إنشاء ملف Excel
            excel_output = BytesIO()
            with pd.ExcelWriter(excel_output, engine='xlsxwriter') as writer:
                df.to_excel(writer, sheet_name='Products', index=False)

                # تنسيق ورقة العمل
                workbook = writer.book
                worksheet = writer.sheets['Products']

                # تنسيق العناوين
                header_format = workbook.add_format({
                    'bold': True,
                    'text_wrap': True,
                    'valign': 'top',
                    'fg_color': '#D7E4BC',
                    'border': 1
                })

                # تطبيق التنسيق على العناوين
                for col_num, value in enumerate(df.columns.values):
                    worksheet.write(0, col_num, value, header_format)
                    worksheet.set_column(col_num, col_num, 15)  # تعيين عرض العمود

            # إعداد الاستجابة
            mimetype = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
            filename = f'products_export_{timestamp}.xlsx'

            return Response(
                excel_output.getvalue(),
                mimetype=mimetype,
                headers={
                    'Content-Disposition': f'attachment; filename={filename}'
                }
            )
        except ImportError:
            # إذا لم تكن مكتبة pandas متاحة، نستخدم CSV كبديل
            mimetype = 'text/csv'
            filename = f'products_export_{timestamp}.csv'
    elif export_format == 'csv_semicolon':
        mimetype = 'text/csv'
        filename = f'products_export_{timestamp}.csv'
    else:
        mimetype = 'text/csv'
        filename = f'products_export_{timestamp}.csv'

    return Response(
        output.getvalue(),
        mimetype=mimetype,
        headers={
            'Content-Disposition': f'attachment; filename={filename}',
            'Content-Type': f'{mimetype}; charset=utf-8'
        }
    )

@products_blueprint.route('/categories')
@login_required
def categories():
    categories = Category.query.order_by(Category.name).all()
    return render_template('products/categories.html', categories=categories, current_user=current_user)

@products_blueprint.route('/categories/create', methods=['GET', 'POST'])
@login_required
def create_category():
    if request.method == 'POST':
        try:
            name = request.form.get('name')
            description = request.form.get('description')
            color = request.form.get('color', 'blue')

            category = Category(
                name=name,
                description=description,
                color=color
            )

            db.session.add(category)
            db.session.commit()

            flash('تم إضافة التصنيف بنجاح', 'success')
            return redirect(url_for('products.categories'))

        except Exception as e:
            db.session.rollback()
            flash(f'حدث خطأ أثناء إضافة التصنيف: {str(e)}', 'danger')

    return render_template('products/category_form.html', category=None, action='create')

@products_blueprint.route('/categories/<int:id>/edit', methods=['GET', 'POST'])
@login_required
def edit_category(id):
    category = Category.query.get_or_404(id)

    if request.method == 'POST':
        try:
            category.name = request.form.get('name')
            category.description = request.form.get('description')
            category.color = request.form.get('color', 'blue')

            db.session.commit()

            flash('تم تحديث التصنيف بنجاح', 'success')
            return redirect(url_for('products.categories'))

        except Exception as e:
            db.session.rollback()
            flash(f'حدث خطأ أثناء تحديث التصنيف: {str(e)}', 'danger')

    return render_template('products/category_form.html', category=category, action='edit')

@products_blueprint.route('/categories/<int:id>/delete', methods=['POST'])
@login_required
def delete_category(id):
    category = Category.query.get_or_404(id)

    try:
        # Check if category has products
        if category.products:
            flash('لا يمكن حذف التصنيف لأنه يحتوي على منتجات', 'warning')
            return redirect(url_for('products.categories'))

        db.session.delete(category)
        db.session.commit()
        flash('تم حذف التصنيف بنجاح', 'success')
    except Exception as e:
        db.session.rollback()
        flash(f'حدث خطأ أثناء حذف التصنيف: {str(e)}', 'danger')

    return redirect(url_for('products.categories'))

@products_blueprint.route('/api/categories')
@login_required
def api_categories():
    categories = Category.query.order_by(Category.name).all()
    return jsonify({
        'categories': [category.to_dict() for category in categories]
    })

@products_blueprint.route('/products/barcode')
@login_required
def barcode():
    """صفحة إنشاء وطباعة الباركود للمنتجات"""
    # الحصول على معلمات الفلتر
    search = request.args.get('search', '')
    category_id = request.args.get('category_id', '')
    page = request.args.get('page', 1, type=int)
    per_page = 20  # عدد العناصر في الصفحة الواحدة

    # إعداد الاستعلام
    query = Product.query.filter_by(is_active=True)

    # تطبيق الفلاتر
    if search:
        query = query.filter(Product.name.ilike(f'%{search}%') | Product.code.ilike(f'%{search}%'))

    if category_id and category_id.isdigit():
        query = query.filter(Product.category_id == int(category_id))

    # تنفيذ الاستعلام مع الترقيم
    products_paginated = query.order_by(Product.name).paginate(page=page, per_page=per_page)

    # الحصول على قائمة التصنيفات
    categories = Category.query.all()

    return render_template('products/product_barcode.html',
                          products=products_paginated.items,
                          categories=categories,
                          search=search,
                          category_id=category_id,
                          page=page,
                          total_pages=products_paginated.pages)

@products_blueprint.route('/products/scale-barcode')
@login_required
def scale_barcode():
    """صفحة ربط المنتجات بأكواد ميزان الباركود"""
    # الحصول على معلمات الفلتر
    search = request.args.get('search', '')
    category_id = request.args.get('category_id', '')
    page = request.args.get('page', 1, type=int)
    per_page = 20  # عدد العناصر في الصفحة الواحدة

    # إعداد الاستعلام للمنتجات التي يمكن وزنها
    query = Product.query.filter_by(is_active=True)

    # تطبيق الفلاتر
    if search:
        query = query.filter(Product.name.ilike(f'%{search}%') | Product.code.ilike(f'%{search}%'))

    if category_id and category_id.isdigit():
        query = query.filter(Product.category_id == int(category_id))

    # تنفيذ الاستعلام مع الترقيم
    products_paginated = query.order_by(Product.name).paginate(page=page, per_page=per_page)

    # الحصول على قائمة التصنيفات
    categories = Category.query.all()

    # الحصول على إعدادات الباركود من ملف الإعدادات
    from routes.settings import load_settings
    settings = load_settings()

    # التأكد من وجود قسم barcode في الإعدادات
    if 'barcode' not in settings:
        settings['barcode'] = {
            'enable_scale': False,
            'scale_prefix': '20',
            'barcode_length': 13,
            'weight_position': 'end',
            'weight_digits': 5,
            'decimal_places': 3,
            'weight_format': 'weight_x_price'
        }

    return render_template('products/scale_barcode.html',
                          products=products_paginated.items,
                          categories=categories,
                          search=search,
                          category_id=category_id,
                          page=page,
                          total_pages=products_paginated.pages,
                          settings=settings)


@products_blueprint.route('/products/scale-barcode/assign', methods=['POST'])
@login_required
def assign_scale_code():
    """تعيين كود ميزان الباركود لمنتج"""
    try:
        product_id = request.form.get('product_id')
        scale_code = request.form.get('scale_code')

        if not product_id or not scale_code:
            return jsonify({'success': False, 'message': 'يجب تحديد المنتج والكود'})

        product = Product.query.get_or_404(product_id)

        # تحديث كود الميزان للمنتج
        product.scale_code = scale_code
        db.session.commit()

        return jsonify({'success': True, 'message': f'تم تعيين كود الميزان {scale_code} للمنتج {product.name} بنجاح'})

    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'message': f'فشل تعيين كود الميزان: {str(e)}'})

@products_blueprint.route('/products/promotions')
@login_required
def promotions():
    """صفحة إدارة العروض والإجراءات"""
    # الحصول على معلمات الفلتر
    search = request.args.get('search', '')
    category_id = request.args.get('category_id', '')
    status = request.args.get('status', '')
    page = request.args.get('page', 1, type=int)
    per_page = 10  # عدد العروض في الصفحة الواحدة

    # إعداد الاستعلام
    # في حالة وجود نموذج للعروض، يمكن استخدامه هنا
    # مثال: query = Promotion.query

    # للتبسيط، نستخدم قائمة وهمية من العروض
    promotions = [
        {
            'id': 1,
            'name': 'خصم 10% على منتجات الألبان',
            'type': 'discount_percentage',
            'value': 10,
            'start_date': '2023-06-01',
            'end_date': '2023-06-30',
            'is_active': True,
            'products_count': 5,
            'barcode': '9900001'
        },
        {
            'id': 2,
            'name': 'اشتري 1 واحصل على 1 مجانًا',
            'type': 'buy_x_get_y',
            'value': {'buy': 1, 'get': 1},
            'start_date': '2023-06-15',
            'end_date': '2023-07-15',
            'is_active': True,
            'products_count': 3,
            'barcode': '9900002'
        },
        {
            'id': 3,
            'name': 'خصم 50 جنيه على المشتريات فوق 500 جنيه',
            'type': 'discount_fixed',
            'value': 50,
            'start_date': '2023-07-01',
            'end_date': '2023-07-31',
            'is_active': False,
            'products_count': 0,
            'barcode': '9900003'
        }
    ]

    # الحصول على قائمة التصنيفات
    categories = Category.query.all()

    # الحصول على قائمة المنتجات النشطة
    products = Product.query.filter_by(is_active=True).all()

    return render_template('promotions.html',
                          promotions=promotions,
                          categories=categories,
                          products=products,
                          search=search,
                          category_id=category_id,
                          status=status,
                          page=page,
                          total_pages=1)  # للتبسيط، نفترض صفحة واحدة

@products_blueprint.route('/products/promotions/create', methods=['GET', 'POST'])
@login_required
def create_promotion():
    """إنشاء عرض جديد"""
    if request.method == 'POST':
        try:
            # الحصول على بيانات النموذج
            name = request.form.get('name')
            type = request.form.get('type')
            value = request.form.get('value')
            start_date = request.form.get('start_date')
            end_date = request.form.get('end_date')
            product_ids = request.form.getlist('product_ids')
            barcode = request.form.get('barcode')

            # هنا يمكن إضافة كود إنشاء العرض
            # مثال:
            # promotion = Promotion(
            #     name=name,
            #     type=type,
            #     value=value,
            #     start_date=start_date,
            #     end_date=end_date,
            #     barcode=barcode
            # )
            # db.session.add(promotion)
            # db.session.commit()

            # إضافة المنتجات للعرض
            # for product_id in product_ids:
            #     promotion_product = PromotionProduct(
            #         promotion_id=promotion.id,
            #         product_id=product_id
            #     )
            #     db.session.add(promotion_product)
            # db.session.commit()

            flash('تم إنشاء العرض بنجاح', 'success')
            return redirect(url_for('products.promotions'))

        except Exception as e:
            flash(f'حدث خطأ أثناء إنشاء العرض: {str(e)}', 'danger')

    # الحصول على قائمة المنتجات النشطة
    products = Product.query.filter_by(is_active=True).all()

    return render_template('promotion_form.html',
                          promotion=None,
                          products=products,
                          action='create')

@products_blueprint.route('/products/promotions/<int:id>/edit', methods=['GET', 'POST'])
@login_required
def edit_promotion(id):
    """تعديل عرض"""
    # للتبسيط، نستخدم عرض وهمي
    promotion = {
        'id': id,
        'name': 'خصم 10% على منتجات الألبان',
        'type': 'discount_percentage',
        'value': 10,
        'start_date': '2023-06-01',
        'end_date': '2023-06-30',
        'is_active': True,
        'barcode': '9900001',
        'product_ids': [1, 2, 3, 4, 5]
    }

    if request.method == 'POST':
        try:
            # الحصول على بيانات النموذج
            name = request.form.get('name')
            type = request.form.get('type')
            value = request.form.get('value')
            start_date = request.form.get('start_date')
            end_date = request.form.get('end_date')
            product_ids = request.form.getlist('product_ids')
            barcode = request.form.get('barcode')

            # هنا يمكن إضافة كود تحديث العرض
            # مثال:
            # promotion = Promotion.query.get_or_404(id)
            # promotion.name = name
            # promotion.type = type
            # promotion.value = value
            # promotion.start_date = start_date
            # promotion.end_date = end_date
            # promotion.barcode = barcode
            # db.session.commit()

            # تحديث المنتجات المرتبطة بالعرض
            # PromotionProduct.query.filter_by(promotion_id=id).delete()
            # for product_id in product_ids:
            #     promotion_product = PromotionProduct(
            #         promotion_id=id,
            #         product_id=product_id
            #     )
            #     db.session.add(promotion_product)
            # db.session.commit()

            flash('تم تحديث العرض بنجاح', 'success')
            return redirect(url_for('products.promotions'))

        except Exception as e:
            flash(f'حدث خطأ أثناء تحديث العرض: {str(e)}', 'danger')

    # الحصول على قائمة المنتجات النشطة
    products = Product.query.filter_by(is_active=True).all()

    return render_template('promotion_form.html',
                          promotion=promotion,
                          products=products,
                          action='edit')

@products_blueprint.route('/products/promotions/<int:id>/delete', methods=['POST'])
@login_required
def delete_promotion(id):
    """حذف عرض"""
    try:
        # هنا يمكن إضافة كود حذف العرض
        # مثال:
        # promotion = Promotion.query.get_or_404(id)
        # db.session.delete(promotion)
        # db.session.commit()

        # للتبسيط، نعيد استجابة JSON للاستخدام في واجهة المستخدم
        return jsonify({'success': True, 'message': 'تم حذف العرض بنجاح'})
    except Exception as e:
        return jsonify({'success': False, 'message': f'حدث خطأ أثناء حذف العرض: {str(e)}'})

@products_blueprint.route('/products/promotions/<int:id>/toggle-status', methods=['POST'])
@login_required
def toggle_promotion_status(id):
    """تغيير حالة العرض (تفعيل/تعطيل)"""
    try:
        status = request.form.get('status')
        if status not in ['0', '1']:
            return jsonify({'success': False, 'message': 'قيمة الحالة غير صالحة'})

        # هنا يمكن إضافة كود تغيير حالة العرض
        # مثال:
        # promotion = Promotion.query.get_or_404(id)
        # promotion.is_active = status == '1'
        # db.session.commit()

        # للتبسيط، نعيد استجابة JSON للاستخدام في واجهة المستخدم
        status_text = 'تفعيل' if status == '1' else 'تعطيل'
        return jsonify({'success': True, 'message': f'تم {status_text} العرض بنجاح'})
    except Exception as e:
        return jsonify({'success': False, 'message': f'حدث خطأ أثناء تغيير حالة العرض: {str(e)}'})

