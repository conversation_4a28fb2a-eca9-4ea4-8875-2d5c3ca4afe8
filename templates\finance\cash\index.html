{% extends 'base.html' %}

{% block title %}الخزينة{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0 text-gray-800">الخزينة</h1>
        <div>
            <a href="{{ url_for('cash.cash_registers') }}" class="btn btn-primary">
                <i class="fas fa-cash-register"></i> إدارة الخزائن
            </a>
            <a href="{{ url_for('cash.transactions') }}" class="btn btn-info">
                <i class="fas fa-exchange-alt"></i> المعاملات
            </a>
            <a href="{{ url_for('cash.add_transaction') }}" class="btn btn-success">
                <i class="fas fa-plus"></i> إضافة معاملة
            </a>
        </div>
    </div>

    <!-- إجمالي الرصيد -->
    <div class="row">
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-primary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                إجمالي الرصيد</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ total_balance|round(2) }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-money-bill-wave fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                عدد الخزائن</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ cash_registers|length }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-cash-register fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-info shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                الشيفتات المفتوحة</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ open_shifts|length }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-user-clock fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-warning shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                آخر المعاملات</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ recent_transactions|length }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-exchange-alt fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- الخزائن -->
        <div class="col-lg-6">
            <div class="card shadow mb-4">
                <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                    <h6 class="m-0 font-weight-bold text-primary">الخزائن</h6>
                    <a href="{{ url_for('cash.add_cash_register') }}" class="btn btn-sm btn-primary">
                        <i class="fas fa-plus"></i> إضافة خزينة
                    </a>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-bordered" width="100%" cellspacing="0">
                            <thead>
                                <tr>
                                    <th>الاسم</th>
                                    <th>الرصيد الحالي</th>
                                    <th>الحالة</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for register in cash_registers %}
                                <tr>
                                    <td>{{ register.name }}</td>
                                    <td>{{ register.current_balance|round(2) }}</td>
                                    <td>
                                        {% if register.is_active %}
                                        <span class="badge badge-success">نشط</span>
                                        {% else %}
                                        <span class="badge badge-danger">غير نشط</span>
                                        {% endif %}
                                        {% if register.is_default %}
                                        <span class="badge badge-info">افتراضي</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <a href="{{ url_for('cash.edit_cash_register', register_id=register.id) }}" class="btn btn-sm btn-info">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- الشيفتات المفتوحة -->
        <div class="col-lg-6">
            <div class="card shadow mb-4">
                <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                    <h6 class="m-0 font-weight-bold text-primary">الشيفتات المفتوحة</h6>
                    <a href="{{ url_for('shifts.open_shift') }}" class="btn btn-sm btn-primary">
                        <i class="fas fa-plus"></i> فتح شيفت
                    </a>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-bordered" width="100%" cellspacing="0">
                            <thead>
                                <tr>
                                    <th>المستخدم</th>
                                    <th>الخزينة</th>
                                    <th>وقت البدء</th>
                                    <th>الرصيد الافتتاحي</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for shift in open_shifts %}
                                <tr>
                                    <td>{{ shift.user.username }}</td>
                                    <td>{{ shift.cash_register.name }}</td>
                                    <td>{{ shift.start_time.strftime('%Y-%m-%d %H:%M') }}</td>
                                    <td>{{ shift.start_balance|round(2) }}</td>
                                    <td>
                                        <a href="{{ url_for('shifts.shift_details', shift_id=shift.id) }}" class="btn btn-sm btn-info">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="{{ url_for('shifts.close_shift', shift_id=shift.id) }}" class="btn btn-sm btn-warning">
                                            <i class="fas fa-door-closed"></i> إغلاق
                                        </a>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- آخر المعاملات -->
    <div class="row">
        <div class="col-12">
            <div class="card shadow mb-4">
                <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                    <h6 class="m-0 font-weight-bold text-primary">آخر المعاملات</h6>
                    <a href="{{ url_for('cash.transactions') }}" class="btn btn-sm btn-primary">
                        عرض الكل
                    </a>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-bordered" width="100%" cellspacing="0">
                            <thead>
                                <tr>
                                    <th>الخزينة</th>
                                    <th>النوع</th>
                                    <th>المبلغ</th>
                                    <th>الرصيد السابق</th>
                                    <th>الرصيد الجديد</th>
                                    <th>المستخدم</th>
                                    <th>التاريخ</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for transaction in recent_transactions %}
                                <tr>
                                    <td>{{ transaction.cash_register.name }}</td>
                                    <td>
                                        {% if transaction.transaction_type == 'deposit' %}
                                        <span class="badge badge-success">إيداع</span>
                                        {% elif transaction.transaction_type == 'withdraw' %}
                                        <span class="badge badge-danger">سحب</span>
                                        {% else %}
                                        <span class="badge badge-info">{{ transaction.transaction_type }}</span>
                                        {% endif %}
                                    </td>
                                    <td>{{ transaction.amount|round(2) }}</td>
                                    <td>{{ transaction.previous_balance|round(2) }}</td>
                                    <td>{{ transaction.new_balance|round(2) }}</td>
                                    <td>{{ transaction.user.username }}</td>
                                    <td>{{ transaction.created_at.strftime('%Y-%m-%d %H:%M') }}</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
