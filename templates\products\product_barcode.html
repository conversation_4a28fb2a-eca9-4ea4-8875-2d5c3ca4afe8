{% extends 'layout.html' %}

{% block title %}طباعة الباركود{% endblock %}

{% block page_content %}
<div class="container mx-auto px-4 py-8">
    <div class="mb-8 flex flex-col md:flex-row md:items-center justify-between gap-4">
        <div>
            <h1 class="text-3xl font-bold text-gray-800 dark:text-white flex items-center">
                <i class="ri-barcode-line text-primary dark:text-blue-400 ml-3"></i>
                <span>طباعة الباركود</span>
            </h1>
            <p class="text-gray-600 dark:text-gray-400 mt-2">إنشاء وطباعة الباركود للمنتجات بتنسيقات مختلفة</p>
        </div>
        <div class="flex space-x-2 space-x-reverse mt-4 md:mt-0">
            <a href="{{ url_for('products.index') }}"
               class="bg-white dark:bg-dark-100 border border-gray-300 dark:border-gray-700 text-gray-700 dark:text-gray-300 px-5 py-3 rounded-xl shadow-sm hover:shadow-md transition-all duration-300 flex items-center gap-2">
                <i class="ri-arrow-right-line text-lg"></i>
                <span class="font-medium">العودة للمنتجات</span>
            </a>
        </div>
    </div>

    <!-- فلاتر البحث -->
    <div class="glass-effect rounded-xl p-6 mb-6 border border-gray-200 dark:border-gray-700">
        <div class="flex items-center justify-between mb-4">
            <h3 class="text-lg font-bold text-gray-800 dark:text-white flex items-center">
                <i class="ri-filter-3-line ml-2 text-primary dark:text-blue-400"></i>
                <span>فلترة المنتجات</span>
            </h3>
        </div>

        <form method="GET" action="{{ url_for('products.barcode') }}" class="grid grid-cols-1 md:grid-cols-3 gap-5">
            <div class="relative">
                <label for="category_id" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">التصنيف</label>
                <div class="relative">
                    <select name="category_id" id="category_id"
                        class="w-full px-4 py-2.5 bg-white dark:bg-dark-100 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary dark:focus:ring-blue-500 focus:border-transparent transition-all text-sm appearance-none">
                        <option value="">جميع التصنيفات</option>
                        {% for category in categories %}
                        <option value="{{ category.id }}" {% if category_id == category.id|string %}selected{% endif %}>{{ category.name }}</option>
                        {% endfor %}
                    </select>
                    <div class="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none text-gray-400 dark:text-gray-500">
                        <i class="ri-arrow-down-s-line"></i>
                    </div>
                </div>
            </div>

            <div class="relative">
                <label for="search" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">البحث</label>
                <div class="relative">
                    <div class="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none text-gray-400 dark:text-gray-500">
                        <i class="ri-search-line"></i>
                    </div>
                    <input type="text" name="search" id="search" value="{{ search }}"
                        placeholder="اسم المنتج أو الكود..."
                        class="w-full pr-10 px-4 py-2.5 bg-white dark:bg-dark-100 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary dark:focus:ring-blue-500 focus:border-transparent transition-all text-sm">
                </div>
            </div>

            <div class="flex items-end">
                <button type="submit" class="w-full px-4 py-2.5 bg-gradient-to-r from-primary to-blue-600 dark:from-blue-600 dark:to-blue-700 text-white rounded-lg hover:shadow-lg focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary dark:focus:ring-blue-500 transition-all text-sm font-medium flex items-center justify-center">
                    <i class="ri-filter-line ml-1"></i>
                    <span>تطبيق الفلاتر</span>
                </button>
            </div>
        </form>
    </div>

    <!-- خيارات الطباعة -->
    <div class="glass-effect rounded-xl p-6 mb-6 border border-gray-200 dark:border-gray-700">
        <div class="flex items-center justify-between mb-4">
            <h3 class="text-lg font-bold text-gray-800 dark:text-white flex items-center">
                <i class="ri-settings-4-line ml-2 text-primary dark:text-blue-400"></i>
                <span>خيارات الطباعة</span>
            </h3>
            <div class="flex items-center text-sm text-gray-500 dark:text-gray-400">
                <i class="ri-information-line ml-1"></i>
                <span>قم بتخصيص إعدادات الباركود حسب احتياجاتك</span>
            </div>
        </div>

        <!-- تبويبات خيارات الطباعة -->
        <div x-data="{ activeTab: 'general' }">
            <div class="flex flex-wrap border-b border-gray-200 dark:border-gray-700 mb-6">
                <nav class="flex space-x-4 space-x-reverse overflow-x-auto scrollbar-hide">
                    <button @click="activeTab = 'general'"
                            :class="{'border-primary text-primary dark:border-blue-400 dark:text-blue-400 bg-blue-50 dark:bg-blue-900/20': activeTab === 'general',
                                    'border-transparent text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 hover:border-gray-300 dark:hover:border-gray-600 hover:bg-gray-50 dark:hover:bg-dark-200': activeTab !== 'general'}"
                            class="py-3 px-4 font-medium text-sm border-b-2 transition-all rounded-t-lg flex items-center">
                        <i class="ri-settings-line ml-1.5 text-lg"></i>
                        <span>إعدادات عامة</span>
                    </button>
                    <button @click="activeTab = 'barcode'"
                            :class="{'border-primary text-primary dark:border-blue-400 dark:text-blue-400 bg-blue-50 dark:bg-blue-900/20': activeTab === 'barcode',
                                    'border-transparent text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 hover:border-gray-300 dark:hover:border-gray-600 hover:bg-gray-50 dark:hover:bg-dark-200': activeTab !== 'barcode'}"
                            class="py-3 px-4 font-medium text-sm border-b-2 transition-all rounded-t-lg flex items-center">
                        <i class="ri-barcode-line ml-1.5 text-lg"></i>
                        <span>خيارات الباركود</span>
                    </button>
                    <button @click="activeTab = 'layout'"
                            :class="{'border-primary text-primary dark:border-blue-400 dark:text-blue-400 bg-blue-50 dark:bg-blue-900/20': activeTab === 'layout',
                                    'border-transparent text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 hover:border-gray-300 dark:hover:border-gray-600 hover:bg-gray-50 dark:hover:bg-dark-200': activeTab !== 'layout'}"
                            class="py-3 px-4 font-medium text-sm border-b-2 transition-all rounded-t-lg flex items-center">
                        <i class="ri-layout-grid-line ml-1.5 text-lg"></i>
                        <span>تخطيط الطباعة</span>
                    </button>
                    <button @click="activeTab = 'advanced'"
                            :class="{'border-primary text-primary dark:border-blue-400 dark:text-blue-400 bg-blue-50 dark:bg-blue-900/20': activeTab === 'advanced',
                                    'border-transparent text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 hover:border-gray-300 dark:hover:border-gray-600 hover:bg-gray-50 dark:hover:bg-dark-200': activeTab !== 'advanced'}"
                            class="py-3 px-4 font-medium text-sm border-b-2 transition-all rounded-t-lg flex items-center">
                        <i class="ri-tools-line ml-1.5 text-lg"></i>
                        <span>خيارات متقدمة</span>
                    </button>
                </nav>
            </div>

            <!-- إعدادات عامة -->
            <div x-show="activeTab === 'general'" class="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div class="bg-white dark:bg-dark-100 rounded-xl p-5 border border-gray-200 dark:border-gray-700 shadow-sm">
                    <div class="flex items-center mb-4">
                        <div class="w-10 h-10 rounded-lg bg-blue-100 dark:bg-blue-900/30 flex items-center justify-center text-primary dark:text-blue-400 mr-3">
                            <i class="ri-file-copy-line text-xl"></i>
                        </div>
                        <h4 class="text-gray-800 dark:text-white font-medium">عدد النسخ لكل منتج</h4>
                    </div>
                    <div class="relative">
                        <input type="number" id="copiesPerProduct" value="1" min="1" max="100"
                               class="w-full px-4 py-2.5 bg-white dark:bg-dark-200 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary dark:focus:ring-blue-500 focus:border-transparent transition-all text-sm">
                        <div class="absolute inset-y-0 left-0 flex items-center">
                            <button type="button" onclick="decrementCopies()" class="h-full px-3 text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300">
                                <i class="ri-subtract-line"></i>
                            </button>
                            <div class="h-full w-px bg-gray-300 dark:bg-gray-600"></div>
                            <button type="button" onclick="incrementCopies()" class="h-full px-3 text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300">
                                <i class="ri-add-line"></i>
                            </button>
                        </div>
                    </div>
                    <p class="text-xs text-gray-500 dark:text-gray-400 mt-2">
                        <i class="ri-information-line ml-1"></i>
                        <span>عدد النسخ التي سيتم طباعتها لكل منتج</span>
                    </p>
                </div>

                <div class="bg-white dark:bg-dark-100 rounded-xl p-5 border border-gray-200 dark:border-gray-700 shadow-sm">
                    <div class="flex items-center mb-4">
                        <div class="w-10 h-10 rounded-lg bg-purple-100 dark:bg-purple-900/30 flex items-center justify-center text-purple-600 dark:text-purple-400 mr-3">
                            <i class="ri-file-list-3-line text-xl"></i>
                        </div>
                        <h4 class="text-gray-800 dark:text-white font-medium">قالب الطباعة</h4>
                    </div>
                    <div class="flex space-x-2 space-x-reverse">
                        <div class="relative flex-1">
                            <select id="printTemplate"
                                    class="w-full px-4 py-2.5 bg-white dark:bg-dark-200 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary dark:focus:ring-blue-500 focus:border-transparent transition-all text-sm appearance-none">
                                <option value="standard">قياسي</option>
                                <option value="compact">مدمج</option>
                                <option value="detailed">مفصل</option>
                                <option value="price_tag">بطاقة سعر</option>
                            </select>
                            <div class="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none text-gray-400 dark:text-gray-500">
                                <i class="ri-arrow-down-s-line"></i>
                            </div>
                        </div>
                        <button type="button" id="loadTemplateBtn"
                                class="bg-purple-100 dark:bg-purple-900/30 text-purple-700 dark:text-purple-400 px-3 py-2 rounded-lg hover:bg-purple-200 dark:hover:bg-purple-900/50 transition-all flex items-center justify-center tooltip"
                                data-tooltip="تحميل قالب محفوظ">
                            <i class="ri-folder-open-line text-lg"></i>
                        </button>
                    </div>
                    <p class="text-xs text-gray-500 dark:text-gray-400 mt-2">
                        <i class="ri-information-line ml-1"></i>
                        <span>اختر قالب الطباعة المناسب لاحتياجاتك</span>
                    </p>
                </div>

                <div class="bg-white dark:bg-dark-100 rounded-xl p-5 border border-gray-200 dark:border-gray-700 shadow-sm">
                    <div class="flex items-center mb-4">
                        <div class="w-10 h-10 rounded-lg bg-green-100 dark:bg-green-900/30 flex items-center justify-center text-green-600 dark:text-green-400 mr-3">
                            <i class="ri-eye-line text-xl"></i>
                        </div>
                        <h4 class="text-gray-800 dark:text-white font-medium">عناصر العرض</h4>
                    </div>
                    <div class="grid grid-cols-2 gap-3">
                        <label class="flex items-center p-2.5 bg-gray-50 dark:bg-dark-200 rounded-lg hover:bg-gray-100 dark:hover:bg-dark-300 transition-colors cursor-pointer">
                            <input type="checkbox" id="showName" checked
                                   class="form-checkbox h-5 w-5 text-primary dark:text-blue-500 rounded border-gray-300 dark:border-gray-600 focus:ring-primary dark:focus:ring-blue-500 ml-2">
                            <span class="text-gray-700 dark:text-gray-300">اسم المنتج</span>
                        </label>
                        <label class="flex items-center p-2.5 bg-gray-50 dark:bg-dark-200 rounded-lg hover:bg-gray-100 dark:hover:bg-dark-300 transition-colors cursor-pointer">
                            <input type="checkbox" id="showPrice" checked
                                   class="form-checkbox h-5 w-5 text-primary dark:text-blue-500 rounded border-gray-300 dark:border-gray-600 focus:ring-primary dark:focus:ring-blue-500 ml-2">
                            <span class="text-gray-700 dark:text-gray-300">السعر</span>
                        </label>
                        <label class="flex items-center p-2.5 bg-gray-50 dark:bg-dark-200 rounded-lg hover:bg-gray-100 dark:hover:bg-dark-300 transition-colors cursor-pointer">
                            <input type="checkbox" id="showCode" checked
                                   class="form-checkbox h-5 w-5 text-primary dark:text-blue-500 rounded border-gray-300 dark:border-gray-600 focus:ring-primary dark:focus:ring-blue-500 ml-2">
                            <span class="text-gray-700 dark:text-gray-300">الكود</span>
                        </label>
                        <label class="flex items-center p-2.5 bg-gray-50 dark:bg-dark-200 rounded-lg hover:bg-gray-100 dark:hover:bg-dark-300 transition-colors cursor-pointer">
                            <input type="checkbox" id="showCategory"
                                   class="form-checkbox h-5 w-5 text-primary dark:text-blue-500 rounded border-gray-300 dark:border-gray-600 focus:ring-primary dark:focus:ring-blue-500 ml-2">
                            <span class="text-gray-700 dark:text-gray-300">التصنيف</span>
                        </label>
                    </div>
                </div>
            </div>

            <!-- خيارات الباركود -->
            <div x-show="activeTab === 'barcode'" class="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div class="bg-white dark:bg-dark-100 rounded-xl p-5 border border-gray-200 dark:border-gray-700 shadow-sm">
                    <div class="flex items-center mb-4">
                        <div class="w-10 h-10 rounded-lg bg-blue-100 dark:bg-blue-900/30 flex items-center justify-center text-primary dark:text-blue-400 mr-3">
                            <i class="ri-barcode-box-line text-xl"></i>
                        </div>
                        <h4 class="text-gray-800 dark:text-white font-medium">نوع الباركود</h4>
                    </div>
                    <div class="relative">
                        <select id="barcodeType"
                                class="w-full px-4 py-2.5 bg-white dark:bg-dark-200 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary dark:focus:ring-blue-500 focus:border-transparent transition-all text-sm appearance-none">
                            <option value="CODE128" selected>CODE128 (الأكثر استخداماً)</option>
                            <option value="EAN13">EAN-13 (منتجات التجزئة)</option>
                            <option value="EAN8">EAN-8 (منتجات صغيرة)</option>
                            <option value="UPC">UPC (منتجات أمريكية)</option>
                            <option value="CODE39">CODE39 (صناعي)</option>
                            <option value="ITF14">ITF-14 (شحن)</option>
                            <option value="MSI">MSI (مخازن)</option>
                            <option value="pharmacode">Pharmacode (صيدليات)</option>
                        </select>
                        <div class="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none text-gray-400 dark:text-gray-500">
                            <i class="ri-arrow-down-s-line"></i>
                        </div>
                    </div>
                    <p class="text-xs text-gray-500 dark:text-gray-400 mt-2">
                        <i class="ri-information-line ml-1"></i>
                        <span>اختر نوع الباركود المناسب للمنتجات</span>
                    </p>
                </div>

                <div class="bg-white dark:bg-dark-100 rounded-xl p-5 border border-gray-200 dark:border-gray-700 shadow-sm">
                    <div class="flex items-center mb-4">
                        <div class="w-10 h-10 rounded-lg bg-green-100 dark:bg-green-900/30 flex items-center justify-center text-green-600 dark:text-green-400 mr-3">
                            <i class="ri-aspect-ratio-line text-xl"></i>
                        </div>
                        <h4 class="text-gray-800 dark:text-white font-medium">حجم الباركود</h4>
                    </div>
                    <div class="relative mb-3">
                        <select id="barcodeSize"
                                class="w-full px-4 py-2.5 bg-white dark:bg-dark-200 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary dark:focus:ring-blue-500 focus:border-transparent transition-all text-sm appearance-none">
                            <option value="small">صغير</option>
                            <option value="medium" selected>متوسط</option>
                            <option value="large">كبير</option>
                            <option value="custom">مخصص</option>
                        </select>
                        <div class="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none text-gray-400 dark:text-gray-500">
                            <i class="ri-arrow-down-s-line"></i>
                        </div>
                    </div>

                    <div id="customSizeContainer" class="hidden mt-3 p-3 bg-gray-50 dark:bg-dark-200 rounded-lg border border-gray-200 dark:border-gray-700">
                        <div class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">الحجم المخصص (مم)</div>
                        <div class="grid grid-cols-2 gap-3">
                            <div>
                                <label class="text-xs text-gray-500 dark:text-gray-400 block mb-1">العرض</label>
                                <div class="relative">
                                    <input type="number" id="customWidth" value="50" min="20" max="200"
                                           class="w-full px-4 py-2 bg-white dark:bg-dark-300 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary dark:focus:ring-blue-500 focus:border-transparent transition-all text-sm">
                                    <div class="absolute inset-y-0 left-0 flex items-center pl-2 pointer-events-none text-gray-400 dark:text-gray-500">
                                        <span class="text-xs">مم</span>
                                    </div>
                                </div>
                            </div>
                            <div>
                                <label class="text-xs text-gray-500 dark:text-gray-400 block mb-1">الارتفاع</label>
                                <div class="relative">
                                    <input type="number" id="customHeight" value="30" min="10" max="100"
                                           class="w-full px-4 py-2 bg-white dark:bg-dark-300 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary dark:focus:ring-blue-500 focus:border-transparent transition-all text-sm">
                                    <div class="absolute inset-y-0 left-0 flex items-center pl-2 pointer-events-none text-gray-400 dark:text-gray-500">
                                        <span class="text-xs">مم</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="bg-white dark:bg-dark-100 rounded-xl p-5 border border-gray-200 dark:border-gray-700 shadow-sm">
                    <div class="flex items-center mb-4">
                        <div class="w-10 h-10 rounded-lg bg-purple-100 dark:bg-purple-900/30 flex items-center justify-center text-purple-600 dark:text-purple-400 mr-3">
                            <i class="ri-palette-line text-xl"></i>
                        </div>
                        <h4 class="text-gray-800 dark:text-white font-medium">لون الباركود</h4>
                    </div>
                    <div class="grid grid-cols-2 gap-3">
                        <div>
                            <label class="text-sm text-gray-700 dark:text-gray-300 block mb-2">الخطوط</label>
                            <div class="flex items-center">
                                <input type="color" id="barcodeColor" value="#000000"
                                       class="w-10 h-10 border-0 bg-transparent p-0 cursor-pointer">
                                <div class="flex-1 ml-2 px-3 py-2 bg-gray-50 dark:bg-dark-200 rounded-lg border border-gray-200 dark:border-gray-700 text-sm text-gray-700 dark:text-gray-300">
                                    <span id="barcodeColorText">#000000</span>
                                </div>
                            </div>
                        </div>
                        <div>
                            <label class="text-sm text-gray-700 dark:text-gray-300 block mb-2">الخلفية</label>
                            <div class="flex items-center">
                                <input type="color" id="barcodeBackground" value="#ffffff"
                                       class="w-10 h-10 border-0 bg-transparent p-0 cursor-pointer">
                                <div class="flex-1 ml-2 px-3 py-2 bg-gray-50 dark:bg-dark-200 rounded-lg border border-gray-200 dark:border-gray-700 text-sm text-gray-700 dark:text-gray-300">
                                    <span id="barcodeBackgroundText">#ffffff</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="bg-white dark:bg-dark-100 rounded-xl p-5 border border-gray-200 dark:border-gray-700 shadow-sm">
                    <div class="flex items-center mb-4">
                        <div class="w-10 h-10 rounded-lg bg-orange-100 dark:bg-orange-900/30 flex items-center justify-center text-orange-600 dark:text-orange-400 mr-3">
                            <i class="ri-text-spacing line text-xl"></i>
                        </div>
                        <h4 class="text-gray-800 dark:text-white font-medium">خيارات النص</h4>
                    </div>
                    <div class="mb-3">
                        <label class="flex items-center p-2.5 bg-gray-50 dark:bg-dark-200 rounded-lg hover:bg-gray-100 dark:hover:bg-dark-300 transition-colors cursor-pointer">
                            <input type="checkbox" id="showBarcodeText" checked
                                   class="form-checkbox h-5 w-5 text-primary dark:text-blue-500 rounded border-gray-300 dark:border-gray-600 focus:ring-primary dark:focus:ring-blue-500 ml-2">
                            <span class="text-gray-700 dark:text-gray-300">إظهار نص الباركود</span>
                        </label>
                    </div>
                    <div class="relative">
                        <select id="barcodeTextPosition"
                                class="w-full px-4 py-2.5 bg-white dark:bg-dark-200 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary dark:focus:ring-blue-500 focus:border-transparent transition-all text-sm appearance-none">
                            <option value="bottom" selected>أسفل الباركود</option>
                            <option value="top">أعلى الباركود</option>
                        </select>
                        <div class="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none text-gray-400 dark:text-gray-500">
                            <i class="ri-arrow-down-s-line"></i>
                        </div>
                    </div>
                </div>

                <div class="bg-white dark:bg-dark-100 rounded-xl p-5 border border-gray-200 dark:border-gray-700 shadow-sm">
                    <div class="flex items-center mb-4">
                        <div class="w-10 h-10 rounded-lg bg-red-100 dark:bg-red-900/30 flex items-center justify-center text-red-600 dark:text-red-400 mr-3">
                            <i class="ri-line-height text-xl"></i>
                        </div>
                        <h4 class="text-gray-800 dark:text-white font-medium">سماكة الخطوط</h4>
                    </div>
                    <div class="px-2">
                        <input type="range" id="barcodeWidth" min="1" max="4" value="2"
                               class="w-full h-2 bg-gray-200 dark:bg-gray-700 rounded-lg appearance-none cursor-pointer accent-primary dark:accent-blue-500">
                        <div class="flex justify-between text-xs text-gray-500 dark:text-gray-400 mt-2">
                            <span>رفيع</span>
                            <span id="barcodeWidthValue">متوسط</span>
                            <span>سميك</span>
                        </div>
                    </div>
                    <div class="mt-4 flex justify-center">
                        <div class="flex space-x-2 space-x-reverse">
                            <div class="w-8 h-8 bg-gray-200 dark:bg-gray-700 rounded-lg flex items-center justify-center cursor-pointer hover:bg-gray-300 dark:hover:bg-gray-600 transition-colors" onclick="decrementBarcodeWidth()">
                                <i class="ri-subtract-line text-gray-500 dark:text-gray-400"></i>
                            </div>
                            <div class="w-12 h-8 bg-gray-100 dark:bg-gray-800 rounded-lg flex items-center justify-center text-gray-700 dark:text-gray-300 text-sm font-medium">
                                <span id="barcodeWidthNumber">2</span>
                            </div>
                            <div class="w-8 h-8 bg-gray-200 dark:bg-gray-700 rounded-lg flex items-center justify-center cursor-pointer hover:bg-gray-300 dark:hover:bg-gray-600 transition-colors" onclick="incrementBarcodeWidth()">
                                <i class="ri-add-line text-gray-500 dark:text-gray-400"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- تخطيط الطباعة -->
            <div x-show="activeTab === 'layout'" class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                    <label class="block text-gray-700 mb-2">حجم الورق</label>
                    <select id="paperSize" class="border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 w-full">
                        <option value="A4" selected>A4 (210×297 مم)</option>
                        <option value="A5">A5 (148×210 مم)</option>
                        <option value="Letter">Letter (8.5×11 بوصة)</option>
                        <option value="custom">مخصص</option>
                    </select>
                </div>
                <div>
                    <label class="block text-gray-700 mb-2">عدد الأعمدة</label>
                    <select id="columnsCount" class="border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 w-full">
                        <option value="2">2 أعمدة</option>
                        <option value="3" selected>3 أعمدة</option>
                        <option value="4">4 أعمدة</option>
                        <option value="5">5 أعمدة</option>
                    </select>
                </div>
                <div>
                    <label class="block text-gray-700 mb-2">المسافة بين الباركود (مم)</label>
                    <input type="number" id="barcodeSpacing" value="5" min="0" max="20" class="border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 w-full">
                </div>
                <div>
                    <label class="block text-gray-700 mb-2">هوامش الصفحة (مم)</label>
                    <div class="grid grid-cols-2 gap-2">
                        <div>
                            <label class="text-xs text-gray-500">أفقي</label>
                            <input type="number" id="marginHorizontal" value="10" min="0" max="50" class="border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 w-full">
                        </div>
                        <div>
                            <label class="text-xs text-gray-500">رأسي</label>
                            <input type="number" id="marginVertical" value="10" min="0" max="50" class="border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 w-full">
                        </div>
                    </div>
                </div>
                <div>
                    <label class="block text-gray-700 mb-2">ترتيب الطباعة</label>
                    <select id="printOrder" class="border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 w-full">
                        <option value="horizontal" selected>أفقي (صف بصف)</option>
                        <option value="vertical">رأسي (عمود بعمود)</option>
                    </select>
                </div>
            </div>

            <!-- خيارات متقدمة -->
            <div x-show="activeTab === 'advanced'" class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                    <label class="block text-gray-700 mb-2">إضافة شعار</label>
                    <div class="flex items-center space-x-2 space-x-reverse">
                        <label class="flex items-center">
                            <input type="checkbox" id="showLogo" class="ml-2">
                            <span>إظهار الشعار</span>
                        </label>
                    </div>
                    <div id="logoOptions" class="mt-2 hidden">
                        <input type="file" id="logoFile" accept="image/*" class="w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-md file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100">
                        <div class="mt-2">
                            <label class="text-xs text-gray-500 block">حجم الشعار (%)</label>
                            <input type="range" id="logoSize" min="10" max="100" value="30" class="w-full">
                        </div>
                    </div>
                </div>
                <div>
                    <label class="block text-gray-700 mb-2">معلومات إضافية</label>
                    <div class="space-y-2">
                        <label class="flex items-center">
                            <input type="checkbox" id="showCustomText" class="ml-2">
                            <span>إضافة نص مخصص</span>
                        </label>
                        <div id="customTextOptions" class="hidden">
                            <input type="text" id="customText" placeholder="مثال: صنع في مصر" class="border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 w-full">
                        </div>
                    </div>
                </div>
                <div>
                    <label class="block text-gray-700 mb-2">نوع الملصق</label>
                    <select id="labelType" class="border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 w-full">
                        <option value="custom" selected>مخصص (حسب الإعدادات)</option>
                        <option value="30x20">ملصق 30×20 مم</option>
                        <option value="40x30">ملصق 40×30 مم</option>
                        <option value="50x30">ملصق 50×30 مم</option>
                        <option value="60x40">ملصق 60×40 مم</option>
                    </select>
                </div>
                <div>
                    <label class="block text-gray-700 mb-2">باركود QR</label>
                    <div class="space-y-2">
                        <label class="flex items-center">
                            <input type="checkbox" id="generateQR" class="ml-2">
                            <span>إنشاء باركود QR</span>
                        </label>
                        <div id="qrOptions" class="hidden">
                            <select id="qrContent" class="border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 w-full">
                                <option value="code">كود المنتج</option>
                                <option value="name">اسم المنتج</option>
                                <option value="both" selected>الكود والاسم</option>
                                <option value="url">رابط المنتج</option>
                            </select>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="mt-8 flex flex-col sm:flex-row justify-between gap-4 border-t border-gray-200 dark:border-gray-700 pt-6">
            <button id="saveTemplateBtn"
                    class="bg-gradient-to-r from-indigo-500 to-indigo-600 dark:from-indigo-600 dark:to-indigo-700 text-white px-5 py-3 rounded-xl shadow-md hover:shadow-lg hover:shadow-indigo-500/20 dark:hover:shadow-indigo-800/20 transition-all duration-300 flex items-center justify-center gap-2">
                <i class="ri-save-line text-lg"></i>
                <span class="font-medium">حفظ القالب</span>
            </button>
            <div class="flex flex-col sm:flex-row gap-3">
                <button id="printSelectedBtn"
                        class="bg-gradient-to-r from-green-500 to-green-600 dark:from-green-600 dark:to-green-700 text-white px-5 py-3 rounded-xl shadow-md hover:shadow-lg hover:shadow-green-500/20 dark:hover:shadow-green-800/20 transition-all duration-300 flex items-center justify-center gap-2 disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:shadow-none">
                    <i class="ri-printer-line text-lg"></i>
                    <span class="font-medium">طباعة المحدد</span>
                    <span class="inline-flex items-center justify-center px-2 py-1 ml-2 text-xs font-bold leading-none text-green-100 bg-green-600/50 dark:bg-green-800/50 rounded-full" id="selectedCountBadge">0</span>
                </button>
                <button id="printAllBtn"
                        class="bg-gradient-to-r from-primary to-blue-600 dark:from-blue-600 dark:to-blue-700 text-white px-5 py-3 rounded-xl shadow-md hover:shadow-lg hover:shadow-blue-500/20 dark:hover:shadow-blue-800/20 transition-all duration-300 flex items-center justify-center gap-2">
                    <i class="ri-printer-line text-lg"></i>
                    <span class="font-medium">طباعة الكل</span>
                </button>
            </div>
        </div>
    </div>

    <!-- قائمة المنتجات -->
    <div class="glass-effect rounded-xl overflow-hidden border border-gray-200 dark:border-gray-700">
        <div class="p-4 bg-gray-50 dark:bg-dark-200 border-b border-gray-200 dark:border-gray-700 flex flex-col sm:flex-row justify-between items-center gap-3">
            <h3 class="text-lg font-bold text-gray-800 dark:text-white flex items-center">
                <i class="ri-shopping-bag-3-line ml-2 text-primary dark:text-blue-400"></i>
                <span>قائمة المنتجات</span>
            </h3>
            <div class="flex items-center gap-4">
                <label class="flex items-center p-2 bg-gray-100 dark:bg-dark-300 rounded-lg hover:bg-gray-200 dark:hover:bg-dark-400 transition-colors cursor-pointer">
                    <input type="checkbox" id="selectAll"
                           class="form-checkbox h-5 w-5 text-primary dark:text-blue-500 rounded border-gray-300 dark:border-gray-600 focus:ring-primary dark:focus:ring-blue-500 ml-2">
                    <span class="text-gray-700 dark:text-gray-300">تحديد الكل</span>
                </label>
                <div class="flex items-center gap-2">
                    <span class="text-gray-500 dark:text-gray-400 text-sm">المحدد:</span>
                    <span id="selectedCount" class="bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-300 text-xs font-medium px-2.5 py-1 rounded-full inline-flex items-center">
                        <i class="ri-checkbox-multiple-line ml-1"></i>
                        <span>0 منتج</span>
                    </span>
                </div>
            </div>
        </div>

        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                <thead class="bg-gray-50 dark:bg-dark-200">
                    <tr>
                        <th scope="col" class="px-6 py-4 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">تحديد</th>
                        <th scope="col" class="px-6 py-4 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">المنتج</th>
                        <th scope="col" class="px-6 py-4 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">الباركود</th>
                        <th scope="col" class="px-6 py-4 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">السعر</th>
                        <th scope="col" class="px-6 py-4 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">معاينة</th>
                    </tr>
                </thead>
                <tbody class="bg-white dark:bg-dark-100 divide-y divide-gray-200 dark:divide-gray-700">
                    {% for product in products %}
                    <tr class="hover:bg-gray-50 dark:hover:bg-dark-200 transition-colors duration-150 product-row"
                        data-id="{{ product.id }}" data-name="{{ product.name }}" data-code="{{ product.code }}" data-price="{{ product.price }}"
                        {% if product.category %}data-category="{{ product.category.name }}"{% endif %}>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="flex items-center justify-center">
                                <input type="checkbox" class="product-checkbox form-checkbox h-5 w-5 text-primary dark:text-blue-500 rounded border-gray-300 dark:border-gray-600 focus:ring-primary dark:focus:ring-blue-500">
                            </div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="flex items-center">
                                <div class="flex-shrink-0 h-10 w-10 rounded-lg bg-gray-100 dark:bg-dark-300 flex items-center justify-center text-gray-500 dark:text-gray-400 overflow-hidden border border-gray-200 dark:border-gray-600">
                                    {% if product.image_path %}
                                    <img src="{{ product.image_path }}" alt="{{ product.name }}" class="h-10 w-10 rounded-lg object-cover">
                                    {% else %}
                                    <i class="ri-shopping-bag-line text-xl"></i>
                                    {% endif %}
                                </div>
                                <div class="mr-4">
                                    <div class="text-sm font-medium text-gray-900 dark:text-white">{{ product.name }}</div>
                                    {% if product.category %}
                                    <div class="text-xs text-gray-500 dark:text-gray-400 mt-0.5 product-category">{{ product.category.name }}</div>
                                    {% endif %}
                                </div>
                            </div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="text-sm text-gray-900 dark:text-white font-mono">{{ product.code or '-' }}</div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="font-medium text-gray-900 dark:text-white">{{ product.price }} ج.م</div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <button class="preview-btn text-primary dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300 p-1.5 rounded-lg hover:bg-blue-50 dark:hover:bg-blue-900/30 transition-all tooltip flex items-center"
                                    data-tooltip="معاينة الباركود">
                                <i class="ri-eye-line text-lg ml-1"></i>
                                <span class="text-sm">معاينة</span>
                            </button>
                        </td>
                    </tr>
                    {% endfor %}

                    {% if products|length == 0 %}
                    <tr>
                        <td colspan="5" class="px-6 py-12 text-center">
                            <div class="flex flex-col items-center">
                                <div class="w-20 h-20 rounded-full bg-gray-100 dark:bg-dark-300 flex items-center justify-center text-gray-400 dark:text-gray-500 mb-4">
                                    <i class="ri-inbox-line text-4xl"></i>
                                </div>
                                <p class="text-gray-600 dark:text-gray-300 mb-2 text-lg">لا توجد منتجات مطابقة</p>
                                {% if category_id or search %}
                                <p class="text-gray-500 dark:text-gray-400 text-sm">جرب تغيير معايير البحث أو <a href="{{ url_for('products.barcode') }}" class="text-primary dark:text-blue-400 hover:underline">عرض جميع المنتجات</a></p>
                                {% endif %}
                            </div>
                        </td>
                    </tr>
                    {% endif %}
                </tbody>
            </table>
        </div>

        <!-- ترقيم الصفحات -->
        {% if total_pages > 1 %}
        <div class="px-6 py-4 bg-gray-50 dark:bg-dark-200 border-t border-gray-200 dark:border-gray-700 flex flex-col sm:flex-row items-center justify-between gap-4">
            <div class="text-sm text-gray-500 dark:text-gray-400 flex items-center">
                <span class="inline-flex items-center">
                    <i class="ri-pages-line ml-1.5"></i>
                    صفحة <span class="font-medium text-gray-700 dark:text-gray-300 mx-1">{{ page }}</span> من
                    <span class="font-medium text-gray-700 dark:text-gray-300 mx-1">{{ total_pages }}</span>
                </span>
            </div>

            <div class="flex space-x-1 space-x-reverse">
                {% if page > 1 %}
                <a href="{{ url_for('products.barcode', page=page-1, category_id=category_id, search=search) }}"
                   class="flex items-center px-3 py-2 text-sm text-gray-700 dark:text-gray-300 bg-white dark:bg-dark-100 rounded-lg border border-gray-300 dark:border-gray-600 hover:bg-gray-50 dark:hover:bg-dark-200 transition-colors">
                    <i class="ri-arrow-right-s-line ml-1"></i>
                    <span>السابق</span>
                </a>
                {% else %}
                <span class="flex items-center px-3 py-2 text-sm text-gray-400 dark:text-gray-600 bg-gray-100 dark:bg-dark-300 rounded-lg border border-gray-200 dark:border-gray-700 opacity-60 cursor-not-allowed">
                    <i class="ri-arrow-right-s-line ml-1"></i>
                    <span>السابق</span>
                </span>
                {% endif %}

                <div class="hidden sm:flex space-x-1 space-x-reverse">
                {% for p in range(1, total_pages + 1) %}
                    {% if p == page %}
                    <span class="flex items-center justify-center w-10 h-10 text-white bg-primary dark:bg-blue-600 rounded-lg text-sm font-medium shadow-sm">
                        {{ p }}
                    </span>
                    {% else %}
                    <a href="{{ url_for('products.barcode', page=p, category_id=category_id, search=search) }}"
                       class="flex items-center justify-center w-10 h-10 text-gray-700 dark:text-gray-300 bg-white dark:bg-dark-100 rounded-lg border border-gray-300 dark:border-gray-600 hover:bg-gray-50 dark:hover:bg-dark-200 text-sm transition-colors">
                        {{ p }}
                    </a>
                    {% endif %}
                {% endfor %}
                </div>

                {% if page < total_pages %}
                <a href="{{ url_for('products.barcode', page=page+1, category_id=category_id, search=search) }}"
                   class="flex items-center px-3 py-2 text-sm text-gray-700 dark:text-gray-300 bg-white dark:bg-dark-100 rounded-lg border border-gray-300 dark:border-gray-600 hover:bg-gray-50 dark:hover:bg-dark-200 transition-colors">
                    <span>التالي</span>
                    <i class="ri-arrow-left-s-line mr-1"></i>
                </a>
                {% else %}
                <span class="flex items-center px-3 py-2 text-sm text-gray-400 dark:text-gray-600 bg-gray-100 dark:bg-dark-300 rounded-lg border border-gray-200 dark:border-gray-700 opacity-60 cursor-not-allowed">
                    <span>التالي</span>
                    <i class="ri-arrow-left-s-line mr-1"></i>
                </span>
                {% endif %}
            </div>
        </div>
        {% endif %}
    </div>
</div>

<!-- نموذج معاينة الباركود -->
<div id="previewModal" class="fixed inset-0 bg-black bg-opacity-50 dark:bg-black dark:bg-opacity-70 flex items-center justify-center z-50 hidden backdrop-blur-sm">
    <div class="bg-white dark:bg-dark-100 rounded-xl w-full max-w-3xl mx-4 shadow-2xl transform transition-all duration-300 scale-100">
        <div class="p-4 border-b border-gray-200 dark:border-gray-700 flex justify-between items-center">
            <h3 class="text-xl font-bold text-gray-800 dark:text-white flex items-center" id="previewTitle">
                <i class="ri-barcode-line ml-2 text-primary dark:text-blue-400"></i>
                <span>معاينة الباركود</span>
            </h3>
            <button onclick="closePreviewModal()" class="text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 p-1 rounded-full hover:bg-gray-100 dark:hover:bg-dark-300 transition-colors">
                <i class="ri-close-line text-2xl"></i>
            </button>
        </div>
        <div class="p-6">
            <div class="flex flex-col md:flex-row gap-6">
                <!-- معاينة الباركود -->
                <div class="flex-1 flex flex-col items-center justify-center border border-gray-200 dark:border-gray-700 rounded-xl p-6 bg-white dark:bg-dark-200 shadow-sm">
                    <div id="barcodePreview" class="flex flex-col items-center"></div>
                </div>

                <!-- معاينة باركود QR (إذا تم تفعيله) -->
                <div id="qrPreviewContainer" class="flex-1 flex flex-col items-center justify-center border border-gray-200 dark:border-gray-700 rounded-xl p-6 bg-white dark:bg-dark-200 shadow-sm hidden">
                    <div class="flex items-center mb-3">
                        <div class="w-8 h-8 rounded-lg bg-purple-100 dark:bg-purple-900/30 flex items-center justify-center text-purple-600 dark:text-purple-400 mr-2">
                            <i class="ri-qr-code-line text-lg"></i>
                        </div>
                        <h4 class="text-sm font-medium text-gray-800 dark:text-white">باركود QR</h4>
                    </div>
                    <div id="qrPreview" class="flex flex-col items-center"></div>
                </div>
            </div>

            <!-- معاينة ورقة الطباعة -->
            <div class="mt-6 border border-gray-200 dark:border-gray-700 rounded-xl p-5 bg-gray-50 dark:bg-dark-300 shadow-sm">
                <div class="flex items-center mb-3">
                    <div class="w-8 h-8 rounded-lg bg-blue-100 dark:bg-blue-900/30 flex items-center justify-center text-primary dark:text-blue-400 mr-2">
                        <i class="ri-file-paper-2-line text-lg"></i>
                    </div>
                    <h4 class="text-sm font-medium text-gray-800 dark:text-white">معاينة ورقة الطباعة</h4>
                </div>
                <div class="bg-white dark:bg-dark-200 border border-gray-300 dark:border-gray-600 rounded-lg p-3 relative" style="height: 180px; overflow: hidden;">
                    <div id="printSheetPreview" class="grid grid-cols-3 gap-2 scale-[0.3] origin-top-right absolute top-2 right-2"></div>
                </div>
                <p class="text-xs text-gray-500 dark:text-gray-400 mt-2 text-center">
                    <i class="ri-information-line ml-1"></i>
                    <span>هذه معاينة مصغرة لورقة الطباعة. سيتم طباعة الباركود بالحجم الكامل.</span>
                </p>
            </div>
        </div>
        <div class="p-5 bg-gray-50 dark:bg-dark-200 flex flex-col sm:flex-row justify-between gap-4 rounded-b-xl border-t border-gray-200 dark:border-gray-700">
            <button type="button" id="downloadBarcodeBtn"
                    class="bg-gradient-to-r from-indigo-500 to-indigo-600 dark:from-indigo-600 dark:to-indigo-700 text-white px-5 py-3 rounded-xl shadow-md hover:shadow-lg hover:shadow-indigo-500/20 dark:hover:shadow-indigo-800/20 transition-all duration-300 flex items-center justify-center gap-2">
                <i class="ri-download-line text-lg"></i>
                <span class="font-medium">تحميل الباركود</span>
            </button>
            <div class="flex flex-col sm:flex-row gap-3">
                <button type="button" onclick="closePreviewModal()"
                        class="bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 px-5 py-3 rounded-xl shadow-md hover:bg-gray-200 dark:hover:bg-gray-600 transition-all duration-300 flex items-center justify-center gap-2">
                    <i class="ri-close-line text-lg"></i>
                    <span class="font-medium">إغلاق</span>
                </button>
                <button type="button" id="printPreviewBtn"
                        class="bg-gradient-to-r from-green-500 to-green-600 dark:from-green-600 dark:to-green-700 text-white px-5 py-3 rounded-xl shadow-md hover:shadow-lg hover:shadow-green-500/20 dark:hover:shadow-green-800/20 transition-all duration-300 flex items-center justify-center gap-2">
                    <i class="ri-printer-line text-lg"></i>
                    <span class="font-medium">طباعة</span>
                </button>
            </div>
        </div>
    </div>
</div>

<!-- صفحة الطباعة (مخفية) -->
<div id="printContainer" class="hidden"></div>

<!-- زر تبديل الوضع الداكن -->
<button id="themeToggle" class="theme-toggle">
    <i class="ri-moon-line text-xl" id="darkIcon"></i>
    <i class="ri-sun-line text-xl hidden" id="lightIcon"></i>
</button>

<!-- مكتبة JsBarcode لإنشاء الباركود -->
<script src="https://cdn.jsdelivr.net/npm/jsbarcode@3.11.5/dist/JsBarcode.all.min.js"></script>
<!-- مكتبة QRCode.js لإنشاء باركود QR -->
<script src="https://cdn.jsdelivr.net/npm/qrcode@1.5.1/build/qrcode.min.js"></script>
<!-- مكتبة html2canvas لتحميل الباركود كصورة -->
<script src="https://cdn.jsdelivr.net/npm/html2canvas@1.4.1/dist/html2canvas.min.js"></script>

<script>
    // تحديد العناصر
    const selectAllCheckbox = document.getElementById('selectAll');
    const productCheckboxes = document.querySelectorAll('.product-checkbox');
    const selectedCountElement = document.getElementById('selectedCount');
    const printSelectedBtn = document.getElementById('printSelectedBtn');
    const printAllBtn = document.getElementById('printAllBtn');
    const previewBtns = document.querySelectorAll('.preview-btn');
    const previewModal = document.getElementById('previewModal');
    const previewTitle = document.getElementById('previewTitle');
    const barcodePreview = document.getElementById('barcodePreview');
    const printPreviewBtn = document.getElementById('printPreviewBtn');
    const printContainer = document.getElementById('printContainer');

    // خيارات الطباعة
    const copiesPerProductInput = document.getElementById('copiesPerProduct');
    const barcodeSizeSelect = document.getElementById('barcodeSize');
    const showPriceCheckbox = document.getElementById('showPrice');
    const showNameCheckbox = document.getElementById('showName');

    // وظائف إضافية لتحسين تجربة المستخدم
    function incrementCopies() {
        const currentValue = parseInt(copiesPerProductInput.value) || 1;
        copiesPerProductInput.value = Math.min(currentValue + 1, 100);
    }

    function decrementCopies() {
        const currentValue = parseInt(copiesPerProductInput.value) || 1;
        copiesPerProductInput.value = Math.max(currentValue - 1, 1);
    }

    function incrementBarcodeWidth() {
        const barcodeWidth = document.getElementById('barcodeWidth');
        const barcodeWidthNumber = document.getElementById('barcodeWidthNumber');
        const barcodeWidthValue = document.getElementById('barcodeWidthValue');

        const currentValue = parseInt(barcodeWidth.value) || 2;
        const newValue = Math.min(currentValue + 1, 4);

        barcodeWidth.value = newValue;
        barcodeWidthNumber.textContent = newValue;

        // تحديث النص الوصفي
        if (newValue === 1) barcodeWidthValue.textContent = 'رفيع';
        else if (newValue === 2) barcodeWidthValue.textContent = 'متوسط';
        else if (newValue === 3) barcodeWidthValue.textContent = 'سميك';
        else barcodeWidthValue.textContent = 'سميك جداً';
    }

    function decrementBarcodeWidth() {
        const barcodeWidth = document.getElementById('barcodeWidth');
        const barcodeWidthNumber = document.getElementById('barcodeWidthNumber');
        const barcodeWidthValue = document.getElementById('barcodeWidthValue');

        const currentValue = parseInt(barcodeWidth.value) || 2;
        const newValue = Math.max(currentValue - 1, 1);

        barcodeWidth.value = newValue;
        barcodeWidthNumber.textContent = newValue;

        // تحديث النص الوصفي
        if (newValue === 1) barcodeWidthValue.textContent = 'رفيع';
        else if (newValue === 2) barcodeWidthValue.textContent = 'متوسط';
        else if (newValue === 3) barcodeWidthValue.textContent = 'سميك';
        else barcodeWidthValue.textContent = 'سميك جداً';
    }

    // تحديث قيم الألوان النصية
    document.getElementById('barcodeColor').addEventListener('input', function() {
        document.getElementById('barcodeColorText').textContent = this.value.toUpperCase();
    });

    document.getElementById('barcodeBackground').addEventListener('input', function() {
        document.getElementById('barcodeBackgroundText').textContent = this.value.toUpperCase();
    });

    // تحديث قيمة سماكة الخطوط
    document.getElementById('barcodeWidth').addEventListener('input', function() {
        const value = parseInt(this.value);
        document.getElementById('barcodeWidthNumber').textContent = value;

        // تحديث النص الوصفي
        const barcodeWidthValue = document.getElementById('barcodeWidthValue');
        if (value === 1) barcodeWidthValue.textContent = 'رفيع';
        else if (value === 2) barcodeWidthValue.textContent = 'متوسط';
        else if (value === 3) barcodeWidthValue.textContent = 'سميك';
        else barcodeWidthValue.textContent = 'سميك جداً';
    });

    // تحديد الكل
    document.getElementById('selectAll').addEventListener('change', function() {
        const isChecked = this.checked;
        productCheckboxes.forEach(checkbox => {
            checkbox.checked = isChecked;
        });
        updateSelectedCount();
    });

    // تحديث عدد العناصر المحددة
    function updateSelectedCount() {
        const selectedCount = document.querySelectorAll('.product-checkbox:checked').length;
        selectedCountElement.innerHTML = `<i class="ri-checkbox-multiple-line ml-1"></i><span>${selectedCount} منتج</span>`;
        document.getElementById('selectedCountBadge').textContent = selectedCount;
        printSelectedBtn.disabled = selectedCount === 0;

        // تحديث حالة "تحديد الكل"
        const allCheckboxes = document.querySelectorAll('.product-checkbox');
        const selectAllCheckbox = document.getElementById('selectAll');
        if (allCheckboxes.length > 0 && selectedCount === allCheckboxes.length) {
            selectAllCheckbox.checked = true;
            selectAllCheckbox.indeterminate = false;
        } else if (selectedCount === 0) {
            selectAllCheckbox.checked = false;
            selectAllCheckbox.indeterminate = false;
        } else {
            selectAllCheckbox.indeterminate = true;
        }
    }

    // إضافة مستمع لكل مربع اختيار
    productCheckboxes.forEach(checkbox => {
        checkbox.addEventListener('change', updateSelectedCount);
    });

    // تحديث العدد الأولي
    updateSelectedCount();

    // إضافة مستمعي الأحداث للخيارات المتقدمة
    document.addEventListener('DOMContentLoaded', function() {
        // إظهار/إخفاء خيارات الحجم المخصص
        const barcodeSizeSelect = document.getElementById('barcodeSize');
        const customSizeContainer = document.getElementById('customSizeContainer');

        barcodeSizeSelect.addEventListener('change', function() {
            if (this.value === 'custom') {
                customSizeContainer.classList.remove('hidden');
            } else {
                customSizeContainer.classList.add('hidden');
            }
        });

        // إظهار/إخفاء خيارات الشعار
        const showLogoCheckbox = document.getElementById('showLogo');
        const logoOptions = document.getElementById('logoOptions');

        if (showLogoCheckbox && logoOptions) {
            showLogoCheckbox.addEventListener('change', function() {
                if (this.checked) {
                    logoOptions.classList.remove('hidden');
                } else {
                    logoOptions.classList.add('hidden');
                }
            });
        }

        // إظهار/إخفاء خيارات النص المخصص
        const showCustomTextCheckbox = document.getElementById('showCustomText');
        const customTextOptions = document.getElementById('customTextOptions');

        if (showCustomTextCheckbox && customTextOptions) {
            showCustomTextCheckbox.addEventListener('change', function() {
                if (this.checked) {
                    customTextOptions.classList.remove('hidden');
                } else {
                    customTextOptions.classList.add('hidden');
                }
            });
        }

        // إظهار/إخفاء خيارات باركود QR
        const generateQRCheckbox = document.getElementById('generateQR');
        const qrOptions = document.getElementById('qrOptions');

        if (generateQRCheckbox && qrOptions) {
            generateQRCheckbox.addEventListener('change', function() {
                if (this.checked) {
                    qrOptions.classList.remove('hidden');
                } else {
                    qrOptions.classList.add('hidden');
                }
            });
        }

        // تطبيق إعدادات نوع الملصق
        const labelTypeSelect = document.getElementById('labelType');

        if (labelTypeSelect) {
            labelTypeSelect.addEventListener('change', function() {
                if (this.value !== 'custom') {
                    // تطبيق الإعدادات المناسبة حسب نوع الملصق
                    const [width, height] = this.value.split('x').map(Number);

                    // تحديث حقول الحجم المخصص
                    document.getElementById('customWidth').value = width;
                    document.getElementById('customHeight').value = height;

                    // تحديث حجم الباركود
                    document.getElementById('barcodeSize').value = 'custom';
                    customSizeContainer.classList.remove('hidden');

                    // تحديث عدد الأعمدة بناءً على حجم الملصق
                    if (width <= 30) {
                        document.getElementById('columnsCount').value = 5;
                    } else if (width <= 40) {
                        document.getElementById('columnsCount').value = 4;
                    } else {
                        document.getElementById('columnsCount').value = 3;
                    }
                }
            });
        }

        // جمع إعدادات القالب
        function collectTemplateSettings(templateName) {
            return {
                name: templateName,
                showName: document.getElementById('showName').checked,
                showPrice: document.getElementById('showPrice').checked,
                showCode: document.getElementById('showCode').checked,
                showCategory: document.getElementById('showCategory').checked,
                barcodeType: document.getElementById('barcodeType').value,
                barcodeSize: document.getElementById('barcodeSize').value,
                customWidth: document.getElementById('customWidth').value,
                customHeight: document.getElementById('customHeight').value,
                barcodeColor: document.getElementById('barcodeColor').value,
                barcodeBackground: document.getElementById('barcodeBackground').value,
                showBarcodeText: document.getElementById('showBarcodeText').checked,
                barcodeTextPosition: document.getElementById('barcodeTextPosition').value,
                barcodeWidth: document.getElementById('barcodeWidth').value,
                printTemplate: document.getElementById('printTemplate').value,
                paperSize: document.getElementById('paperSize').value,
                columnsCount: document.getElementById('columnsCount').value,
                barcodeSpacing: document.getElementById('barcodeSpacing').value,
                marginHorizontal: document.getElementById('marginHorizontal').value,
                marginVertical: document.getElementById('marginVertical').value,
                printOrder: document.getElementById('printOrder').value,
                showLogo: document.getElementById('showLogo')?.checked || false,
                logoSize: document.getElementById('logoSize')?.value || 30,
                showCustomText: document.getElementById('showCustomText')?.checked || false,
                customText: document.getElementById('customText')?.value || '',
                generateQR: document.getElementById('generateQR')?.checked || false,
                qrContent: document.getElementById('qrContent')?.value || 'code'
            };
        }

        // تطبيق إعدادات القالب
        function applyTemplateSettings(settings) {
            // تطبيق الإعدادات العامة
            document.getElementById('showName').checked = settings.showName;
            document.getElementById('showPrice').checked = settings.showPrice;
            document.getElementById('showCode').checked = settings.showCode;
            document.getElementById('showCategory').checked = settings.showCategory;
            document.getElementById('printTemplate').value = settings.printTemplate;

            // تطبيق إعدادات الباركود
            document.getElementById('barcodeType').value = settings.barcodeType;
            document.getElementById('barcodeSize').value = settings.barcodeSize;
            document.getElementById('customWidth').value = settings.customWidth;
            document.getElementById('customHeight').value = settings.customHeight;
            document.getElementById('barcodeColor').value = settings.barcodeColor;
            document.getElementById('barcodeBackground').value = settings.barcodeBackground;
            document.getElementById('showBarcodeText').checked = settings.showBarcodeText;
            document.getElementById('barcodeTextPosition').value = settings.barcodeTextPosition;
            document.getElementById('barcodeWidth').value = settings.barcodeWidth;

            // تطبيق إعدادات التخطيط
            document.getElementById('paperSize').value = settings.paperSize;
            document.getElementById('columnsCount').value = settings.columnsCount;
            document.getElementById('barcodeSpacing').value = settings.barcodeSpacing;
            document.getElementById('marginHorizontal').value = settings.marginHorizontal;
            document.getElementById('marginVertical').value = settings.marginVertical;
            document.getElementById('printOrder').value = settings.printOrder;

            // تطبيق الإعدادات المتقدمة
            if (document.getElementById('showLogo')) {
                document.getElementById('showLogo').checked = settings.showLogo;
                document.getElementById('logoSize').value = settings.logoSize;

                // إظهار/إخفاء خيارات الشعار
                const logoOptions = document.getElementById('logoOptions');
                if (logoOptions) {
                    if (settings.showLogo) {
                        logoOptions.classList.remove('hidden');
                    } else {
                        logoOptions.classList.add('hidden');
                    }
                }
            }

            if (document.getElementById('showCustomText')) {
                document.getElementById('showCustomText').checked = settings.showCustomText;
                document.getElementById('customText').value = settings.customText;

                // إظهار/إخفاء خيارات النص المخصص
                const customTextOptions = document.getElementById('customTextOptions');
                if (customTextOptions) {
                    if (settings.showCustomText) {
                        customTextOptions.classList.remove('hidden');
                    } else {
                        customTextOptions.classList.add('hidden');
                    }
                }
            }

            if (document.getElementById('generateQR')) {
                document.getElementById('generateQR').checked = settings.generateQR;
                document.getElementById('qrContent').value = settings.qrContent;

                // إظهار/إخفاء خيارات باركود QR
                const qrOptions = document.getElementById('qrOptions');
                if (qrOptions) {
                    if (settings.generateQR) {
                        qrOptions.classList.remove('hidden');
                    } else {
                        qrOptions.classList.add('hidden');
                    }
                }
            }

            // إظهار/إخفاء خيارات الحجم المخصص
            const customSizeContainer = document.getElementById('customSizeContainer');
            if (customSizeContainer) {
                if (settings.barcodeSize === 'custom') {
                    customSizeContainer.classList.remove('hidden');
                } else {
                    customSizeContainer.classList.add('hidden');
                }
            }
        }

        // حفظ القالب
        const saveTemplateBtn = document.getElementById('saveTemplateBtn');

        if (saveTemplateBtn) {
            saveTemplateBtn.addEventListener('click', function() {
                const templateName = prompt('أدخل اسم القالب:');

                if (templateName) {
                    // جمع إعدادات القالب
                    const templateSettings = collectTemplateSettings(templateName);

                    // حفظ القالب في localStorage
                    const savedTemplates = JSON.parse(localStorage.getItem('barcodeTemplates') || '{}');
                    savedTemplates[templateName] = templateSettings;
                    localStorage.setItem('barcodeTemplates', JSON.stringify(savedTemplates));

                    alert(`تم حفظ القالب "${templateName}" بنجاح!`);
                }
            });
        }

        // تحميل القالب
        const loadTemplateBtn = document.getElementById('loadTemplateBtn');

        if (loadTemplateBtn) {
            loadTemplateBtn.addEventListener('click', function() {
                // الحصول على القوالب المحفوظة
                const savedTemplates = JSON.parse(localStorage.getItem('barcodeTemplates') || '{}');
                const templateNames = Object.keys(savedTemplates);

                if (templateNames.length === 0) {
                    alert('لا توجد قوالب محفوظة!');
                    return;
                }

                // إنشاء قائمة بالقوالب المحفوظة
                const templatesList = templateNames.map((name, index) => `${index + 1}. ${name}`).join('\n');
                const selectedTemplate = prompt(`اختر القالب المراد تحميله:\n${templatesList}`);

                if (selectedTemplate) {
                    // البحث عن القالب المحدد
                    const templateName = templateNames.find(name =>
                        selectedTemplate === name ||
                        selectedTemplate === `${templateNames.indexOf(name) + 1}` ||
                        selectedTemplate === `${templateNames.indexOf(name) + 1}. ${name}`
                    );

                    if (templateName && savedTemplates[templateName]) {
                        // تطبيق إعدادات القالب
                        applyTemplateSettings(savedTemplates[templateName]);
                        alert(`تم تحميل القالب "${templateName}" بنجاح!`);
                    } else {
                        alert('القالب المحدد غير موجود!');
                    }
                }
            });
        }
    });

    // معاينة الباركود
    previewBtns.forEach(btn => {
        btn.addEventListener('click', function() {
            const row = this.closest('.product-row');
            const productId = row.dataset.id;
            const productName = row.dataset.name;
            const productCode = row.dataset.code;
            const productPrice = row.dataset.price;
            const category = row.dataset.category || '';

            // تحديث عنوان المعاينة
            const titleSpan = previewTitle.querySelector('span');
            titleSpan.textContent = `معاينة الباركود: ${productName}`;

            // إضافة تأثير تحميل
            barcodePreview.innerHTML = `
                <div class="flex flex-col items-center justify-center py-8">
                    <div class="w-16 h-16 border-4 border-primary dark:border-blue-500 border-t-transparent dark:border-t-transparent rounded-full animate-spin mb-4"></div>
                    <p class="text-gray-600 dark:text-gray-300">جاري إنشاء الباركود...</p>
                </div>
            `;

            // عرض النموذج مع تأثير حركي
            previewModal.classList.remove('hidden');
            setTimeout(() => {
                const modalContent = previewModal.querySelector('div');
                modalContent.classList.add('scale-100');
            }, 10);

            // إنشاء الباركود للمعاينة بعد تأخير قصير
            setTimeout(() => {
                // إنشاء الباركود للمعاينة
                barcodePreview.innerHTML = createBarcodeHTML(productCode, productName, productPrice, category);

                try {
                    // إنشاء الباركود
                    JsBarcode(".barcode-preview").init();
                } catch (error) {
                    console.error("خطأ في إنشاء الباركود:", error);
                    barcodePreview.innerHTML = `
                        <div class="flex flex-col items-center justify-center py-8 text-center">
                            <div class="w-16 h-16 rounded-full bg-red-100 dark:bg-red-900/30 flex items-center justify-center text-red-600 dark:text-red-400 mb-4">
                                <i class="ri-error-warning-line text-3xl"></i>
                            </div>
                            <p class="text-red-600 dark:text-red-400 font-medium mb-2">حدث خطأ أثناء إنشاء الباركود</p>
                            <p class="text-gray-600 dark:text-gray-400 text-sm">تأكد من صحة كود المنتج: ${productCode || 'غير محدد'}</p>
                        </div>
                    `;
                }

                // إنشاء باركود QR إذا كان مفعلاً
                const generateQR = document.getElementById('generateQR')?.checked || false;
                const qrContent = document.getElementById('qrContent')?.value || 'code';
                const qrPreviewContainer = document.getElementById('qrPreviewContainer');

                if (generateQR && qrPreviewContainer) {
                    qrPreviewContainer.classList.remove('hidden');

                    const qrPreview = document.getElementById('qrPreview');
                    qrPreview.innerHTML = '';

                    const qrCanvas = document.createElement('canvas');
                    qrPreview.appendChild(qrCanvas);

                    // تحديد محتوى الباركود QR
                    let qrData = '';
                    if (qrContent === 'code') {
                        qrData = productCode;
                    } else if (qrContent === 'name') {
                        qrData = productName;
                    } else if (qrContent === 'both') {
                        qrData = `${productCode}: ${productName}`;
                    } else if (qrContent === 'url') {
                        qrData = `https://example.com/products/${productCode}`;
                    }

                    // إنشاء باركود QR
                    createQRCode(qrCanvas, qrData);
                } else if (qrPreviewContainer) {
                    qrPreviewContainer.classList.add('hidden');
                }

                // معاينة ورقة الطباعة
                const printSheetPreview = document.getElementById('printSheetPreview');
                if (printSheetPreview) {
                    const columnsCount = document.getElementById('columnsCount').value || 3;
                    const barcodeSpacing = document.getElementById('barcodeSpacing').value || 5;

                    printSheetPreview.style.gridTemplateColumns = `repeat(${columnsCount}, 1fr)`;
                    printSheetPreview.style.gap = `${barcodeSpacing}mm`;

                    // إنشاء 9 نسخ للمعاينة
                    printSheetPreview.innerHTML = '';
                    for (let i = 0; i < 9; i++) {
                        printSheetPreview.innerHTML += createBarcodeHTML(productCode, productName, productPrice, category);
                    }

                    try {
                        // إنشاء الباركود
                        JsBarcode(printSheetPreview.querySelectorAll(".barcode-preview")).init();
                    } catch (error) {
                        console.error("خطأ في إنشاء الباركود في معاينة الورقة:", error);
                    }
                }
            }, 500);
        });
    });

    // إغلاق نموذج المعاينة
    function closePreviewModal() {
        // إضافة تأثير حركي للإغلاق
        const modalContent = previewModal.querySelector('div');
        modalContent.classList.add('scale-95', 'opacity-0');

        setTimeout(() => {
            previewModal.classList.add('hidden');
            modalContent.classList.remove('scale-95', 'opacity-0');
        }, 200);
    }

    // طباعة الباركود المعروض في المعاينة
    printPreviewBtn.addEventListener('click', function() {
        const barcodeHTML = barcodePreview.innerHTML;
        printBarcodes([barcodeHTML]);
    });

    // طباعة الباركود للمنتجات المحددة
    printSelectedBtn.addEventListener('click', function() {
        const selectedProducts = [];
        document.querySelectorAll('.product-checkbox:checked').forEach(checkbox => {
            const row = checkbox.closest('.product-row');
            selectedProducts.push({
                code: row.dataset.code,
                name: row.dataset.name,
                price: row.dataset.price
            });
        });

        if (selectedProducts.length > 0) {
            printProductBarcodes(selectedProducts);
        }
    });

    // طباعة الباركود لجميع المنتجات
    printAllBtn.addEventListener('click', function() {
        const allProducts = [];
        document.querySelectorAll('.product-row').forEach(row => {
            allProducts.push({
                code: row.dataset.code,
                name: row.dataset.name,
                price: row.dataset.price
            });
        });

        if (allProducts.length > 0) {
            printProductBarcodes(allProducts);
        }
    });

    // إنشاء HTML للباركود
    function createBarcodeHTML(code, name, price, category = '') {
        // الحصول على الإعدادات
        const showName = document.getElementById('showName').checked;
        const showPrice = document.getElementById('showPrice').checked;
        const showCode = document.getElementById('showCode').checked;
        const showCategory = document.getElementById('showCategory').checked;
        const barcodeType = document.getElementById('barcodeType').value;
        const size = document.getElementById('barcodeSize').value;
        const barcodeColor = document.getElementById('barcodeColor').value;
        const barcodeBackground = document.getElementById('barcodeBackground').value;
        const showBarcodeText = document.getElementById('showBarcodeText').checked;
        const barcodeTextPosition = document.getElementById('barcodeTextPosition').value;
        const barcodeWidth = document.getElementById('barcodeWidth').value;
        const printTemplate = document.getElementById('printTemplate').value;
        const showLogo = document.getElementById('showLogo')?.checked || false;
        const showCustomText = document.getElementById('showCustomText')?.checked || false;
        const customText = document.getElementById('customText')?.value || '';

        // تحديد حجم الباركود
        let sizeClass = '';
        let width = '';
        if (size === 'small') {
            sizeClass = 'w-32';
            width = '128';
        } else if (size === 'medium') {
            sizeClass = 'w-48';
            width = '192';
        } else if (size === 'large') {
            sizeClass = 'w-64';
            width = '256';
        } else if (size === 'custom') {
            const customWidth = document.getElementById('customWidth').value;
            width = customWidth * 3.78; // تحويل من مم إلى بكسل (تقريبي)
            sizeClass = `w-[${width}px]`;
        }

        // تحديد قالب الطباعة
        let templateClass = '';
        let templateStyle = '';

        if (printTemplate === 'compact') {
            templateClass = 'p-1 border border-gray-200 rounded';
        } else if (printTemplate === 'detailed') {
            templateClass = 'p-2 border-2 border-gray-300 rounded-lg';
        } else if (printTemplate === 'price_tag') {
            templateClass = 'p-3 border-2 border-dashed border-gray-400 rounded-lg bg-gray-50';
        }

        // بناء HTML للباركود
        let html = `<div class="barcode-container text-center ${sizeClass} ${templateClass}" style="${templateStyle}">`;

        // إضافة الشعار
        if (showLogo) {
            const logoSize = document.getElementById('logoSize').value;
            const logoFile = document.getElementById('logoFile').files[0];

            if (logoFile) {
                const logoUrl = URL.createObjectURL(logoFile);
                html += `<div class="mb-2"><img src="${logoUrl}" alt="Logo" style="max-width: ${logoSize}%; height: auto; margin: 0 auto;"></div>`;
            } else {
                // شعار افتراضي
                html += `<div class="mb-2 text-gray-400"><i class="ri-store-2-line text-2xl"></i></div>`;
            }
        }

        // إضافة اسم المنتج
        if (showName) {
            html += `<div class="text-sm font-medium ${printTemplate === 'detailed' ? 'text-base' : ''} mb-1">${name}</div>`;
        }

        // إضافة التصنيف
        if (showCategory && category) {
            html += `<div class="text-xs text-gray-500 mb-1">${category}</div>`;
        }

        // إضافة الباركود
        const barcodeOptions = {
            format: barcodeType,
            width: barcodeWidth,
            height: 80,
            displayValue: showBarcodeText,
            textPosition: barcodeTextPosition,
            textMargin: 2,
            lineColor: barcodeColor,
            background: barcodeBackground
        };

        const barcodeOptionsStr = Object.entries(barcodeOptions)
            .map(([key, value]) => `jsbarcode-${key.toLowerCase()}="${value}"`)
            .join(' ');

        html += `<svg class="barcode-preview mx-auto" ${barcodeOptionsStr} jsbarcode-value="${code}"></svg>`;

        // إضافة كود المنتج
        if (showCode) {
            html += `<div class="text-xs text-gray-500 mt-1">كود: ${code}</div>`;
        }

        // إضافة السعر
        if (showPrice) {
            if (printTemplate === 'price_tag') {
                html += `<div class="text-lg font-bold mt-2 bg-white py-1 px-2 rounded inline-block">${price} ج.م</div>`;
            } else {
                html += `<div class="text-sm font-bold mt-1">${price} ج.م</div>`;
            }
        }

        // إضافة نص مخصص
        if (showCustomText && customText) {
            html += `<div class="text-xs text-gray-600 mt-1">${customText}</div>`;
        }

        html += `</div>`;

        return html;
    }

    // إدارة الوضع الداكن
    const themeToggle = document.getElementById('themeToggle');
    const darkIcon = document.getElementById('darkIcon');
    const lightIcon = document.getElementById('lightIcon');
    const html = document.documentElement;
    const body = document.body;

    // التحقق من الوضع المحفوظ
    const savedTheme = localStorage.getItem('theme');
    if (savedTheme === 'dark') {
        enableDarkMode();
    }

    themeToggle.addEventListener('click', toggleTheme);

    function toggleTheme() {
        if (body.classList.contains('dark')) {
            disableDarkMode();
        } else {
            enableDarkMode();
        }
    }

    function enableDarkMode() {
        body.classList.add('dark');
        darkIcon.classList.add('hidden');
        lightIcon.classList.remove('hidden');
        localStorage.setItem('theme', 'dark');
    }

    function disableDarkMode() {
        body.classList.remove('dark');
        lightIcon.classList.add('hidden');
        darkIcon.classList.remove('hidden');
        localStorage.setItem('theme', 'light');
    }

    // إضافة تأثيرات التلميح للأزرار
    document.addEventListener('DOMContentLoaded', function() {
        const tooltips = document.querySelectorAll('.tooltip');

        tooltips.forEach(tooltip => {
            const tooltipText = tooltip.getAttribute('data-tooltip');
            if (tooltipText) {
                tooltip.addEventListener('mouseenter', function(e) {
                    const tip = document.createElement('div');
                    tip.className = 'absolute z-50 px-2 py-1 text-xs text-white bg-gray-900 dark:bg-gray-800 rounded pointer-events-none opacity-0 transition-opacity duration-300';
                    tip.textContent = tooltipText;
                    tip.style.bottom = '100%';
                    tip.style.left = '50%';
                    tip.style.transform = 'translateX(-50%) translateY(-5px)';
                    tip.style.marginBottom = '5px';
                    tip.style.whiteSpace = 'nowrap';

                    tooltip.style.position = 'relative';
                    tooltip.appendChild(tip);

                    setTimeout(() => {
                        tip.classList.remove('opacity-0');
                        tip.classList.add('opacity-100');
                    }, 10);
                });

                tooltip.addEventListener('mouseleave', function() {
                    const tip = tooltip.querySelector('div');
                    if (tip) {
                        tip.classList.remove('opacity-100');
                        tip.classList.add('opacity-0');

                        setTimeout(() => {
                            tip.remove();
                        }, 300);
                    }
                });
            }
        });
    });

    // إنشاء باركود QR
    function createQRCode(element, content) {
        QRCode.toCanvas(element, content, {
            width: 150,
            margin: 1,
            color: {
                dark: document.getElementById('barcodeColor').value,
                light: document.getElementById('barcodeBackground').value
            }
        });
    }

    // طباعة الباركود للمنتجات
    function printProductBarcodes(products) {
        const copies = parseInt(document.getElementById('copiesPerProduct').value) || 1;
        const barcodeHTMLs = [];
        const qrEnabled = document.getElementById('generateQR')?.checked || false;
        const qrContent = document.getElementById('qrContent')?.value || 'code';
        const columnsCount = document.getElementById('columnsCount').value || 3;

        // الحصول على معلومات التصنيف للمنتجات (إذا كانت متاحة)
        const productCategories = {};
        document.querySelectorAll('.product-row').forEach(row => {
            const productId = row.dataset.id;
            const categoryElement = row.querySelector('.product-category');
            if (categoryElement) {
                productCategories[productId] = categoryElement.textContent;
            }
        });

        products.forEach(product => {
            const category = productCategories[product.id] || '';
            for (let i = 0; i < copies; i++) {
                barcodeHTMLs.push(createBarcodeHTML(product.code, product.name, product.price, category));
            }
        });

        printBarcodes(barcodeHTMLs, qrEnabled, qrContent, products);
    }

    // طباعة الباركود
    function printBarcodes(barcodeHTMLs, qrEnabled = false, qrContent = 'code', products = []) {
        // الحصول على إعدادات الطباعة
        const columnsCount = document.getElementById('columnsCount').value || 3;
        const paperSize = document.getElementById('paperSize').value || 'A4';
        const marginHorizontal = document.getElementById('marginHorizontal').value || 10;
        const marginVertical = document.getElementById('marginVertical').value || 10;
        const barcodeSpacing = document.getElementById('barcodeSpacing').value || 5;
        const printOrder = document.getElementById('printOrder').value || 'horizontal';

        // إنشاء صفحة الطباعة
        printContainer.innerHTML = `
            <div class="print-page">
                <div class="barcode-grid" style="display: grid; grid-template-columns: repeat(${columnsCount}, 1fr); gap: ${barcodeSpacing}mm; padding: ${marginVertical}mm ${marginHorizontal}mm;">
                    ${barcodeHTMLs.join('')}
                </div>
            </div>
        `;

        // إنشاء الباركود
        JsBarcode(".barcode-preview").init();

        // إنشاء باركود QR إذا كان مفعلاً
        if (qrEnabled && products.length > 0) {
            // إنشاء عناصر canvas للباركود QR
            const qrContainers = document.createElement('div');
            qrContainers.className = 'qr-containers hidden';

            products.forEach((product, index) => {
                const qrCanvas = document.createElement('canvas');
                qrCanvas.id = `qr-${index}`;
                qrContainers.appendChild(qrCanvas);

                // تحديد محتوى الباركود QR
                let qrData = '';
                if (qrContent === 'code') {
                    qrData = product.code;
                } else if (qrContent === 'name') {
                    qrData = product.name;
                } else if (qrContent === 'both') {
                    qrData = `${product.code}: ${product.name}`;
                } else if (qrContent === 'url') {
                    qrData = `https://example.com/products/${product.code}`;
                }

                // إنشاء باركود QR
                createQRCode(qrCanvas, qrData);
            });

            printContainer.appendChild(qrContainers);
        }

        // معاينة ورقة الطباعة
        const printSheetPreview = document.getElementById('printSheetPreview');
        if (printSheetPreview) {
            printSheetPreview.innerHTML = printContainer.innerHTML;
            JsBarcode(printSheetPreview.querySelectorAll(".barcode-preview")).init();
        }

        // طباعة الصفحة
        setTimeout(() => {
            const printWindow = window.open('', '_blank');

            // تحديد حجم الورقة
            let pageSize = '';
            if (paperSize === 'A4') {
                pageSize = 'size: A4 portrait;';
            } else if (paperSize === 'A5') {
                pageSize = 'size: A5 portrait;';
            } else if (paperSize === 'Letter') {
                pageSize = 'size: letter portrait;';
            }

            printWindow.document.write(`
                <html dir="rtl">
                <head>
                    <title>طباعة الباركود</title>
                    <style>
                        @page { ${pageSize} margin: 0; }
                        body {
                            font-family: Arial, sans-serif;
                            margin: 0;
                            padding: 0;
                        }
                        .print-page { width: 100%; }
                        .barcode-grid {
                            display: grid;
                            grid-template-columns: repeat(${columnsCount}, 1fr);
                            gap: ${barcodeSpacing}mm;
                            padding: ${marginVertical}mm ${marginHorizontal}mm;
                        }
                        .barcode-container {
                            text-align: center;
                            break-inside: avoid;
                            page-break-inside: avoid;
                        }
                        ${printOrder === 'vertical' ? `
                        .barcode-grid {
                            grid-auto-flow: column;
                        }
                        ` : ''}
                    </style>
                </head>
                <body>
                    ${printContainer.innerHTML}
                    <script src="https://cdn.jsdelivr.net/npm/jsbarcode@3.11.5/dist/JsBarcode.all.min.js"><\/script>
                    <script>
                        JsBarcode(".barcode-preview").init();
                        setTimeout(() => { window.print(); window.close(); }, 500);
                    <\/script>
                </body>
                </html>
            `);
            printWindow.document.close();
        }, 100);
    }

    // تحميل الباركود كصورة
    document.getElementById('downloadBarcodeBtn').addEventListener('click', function() {
        const barcodePreview = document.getElementById('barcodePreview');

        html2canvas(barcodePreview).then(canvas => {
            const link = document.createElement('a');
            link.download = 'barcode.png';
            link.href = canvas.toDataURL('image/png');
            link.click();
        });
    });
</script>
{% endblock page_content %}
