from datetime import datetime
from app import db
from sqlalchemy.orm import relationship

class ExportLog(db.Model):
    __tablename__ = 'export_log'
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.<PERSON><PERSON><PERSON>, db.<PERSON><PERSON>('user.id'), index=True)
    file_name = db.Column(db.String(256))
    file_type = db.Column(db.String(32))
    file_size = db.Column(db.Integer)
    status = db.Column(db.String(32), default='pending')  # pending, success, failed
    total_records = db.Column(db.Integer)
    filter_criteria = db.Column(db.Text)  # JSON string of filters used
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    completed_at = db.Column(db.DateTime)

    # العلاقات
    user = relationship('User', backref='export_logs')

    def __repr__(self):
        return f"<ExportLog {self.id}: {self.file_name} - {self.status}>" 