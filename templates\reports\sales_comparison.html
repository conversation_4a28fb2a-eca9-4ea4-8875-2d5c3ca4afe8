<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تقرير مقارنة المبيعات - نظام إدارة المبيعات</title>
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <!-- Remixicon -->
    <link href="https://cdn.jsdelivr.net/npm/remixicon@2.5.0/fonts/remixicon.css" rel="stylesheet">
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        .report-card {
            transition: all 0.3s ease;
        }
        .report-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 15px rgba(0, 0, 0, 0.1);
        }
    </style>
</head>
<body class="bg-gray-50">
    <div class="flex h-screen overflow-hidden">
        <!-- Sidebar -->
        {% include 'partials/sidebar.html' %}

        <!-- Main Content -->
        <div class="flex-1 flex flex-col overflow-hidden">
            <!-- Top Navigation -->
            {% include 'partials/topnav.html' %}

            <!-- Content Area -->
            <main class="flex-1 overflow-y-auto p-6">
                <div class="mb-6 flex items-center justify-between">
                    <div>
                        <h1 class="text-2xl font-bold text-gray-800">تقرير مقارنة المبيعات</h1>
                        <p class="text-gray-600">مقارنة المبيعات بين فترتين زمنيتين</p>
                    </div>
                    <div class="flex space-x-2 space-x-reverse">
                        <a href="{{ url_for('reports.sales_index') }}" class="px-4 py-2 bg-blue-100 text-blue-700 rounded-md hover:bg-blue-200 transition-all">
                            <i class="ri-arrow-right-line"></i>
                            العودة لتقارير المبيعات
                        </a>
                        <a href="{{ url_for('reports.index') }}" class="px-4 py-2 bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200 transition-all">
                            <i class="ri-home-line"></i>
                            الرئيسية
                        </a>
                    </div>
                </div>

                <!-- Filters -->
                <div class="bg-white p-4 rounded-lg shadow-sm mb-6">
                    <form action="{{ url_for('reports.sales_comparison_report') }}" method="get" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
                        <div>
                            <label for="current_from" class="block text-sm font-medium text-gray-700 mb-1">الفترة الحالية من</label>
                            <input type="date" id="current_from" name="current_from" value="{{ current_from }}"
                                   class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500">
                        </div>
                        <div>
                            <label for="current_to" class="block text-sm font-medium text-gray-700 mb-1">الفترة الحالية إلى</label>
                            <input type="date" id="current_to" name="current_to" value="{{ current_to }}"
                                   class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500">
                        </div>
                        <div>
                            <label for="comparison_type" class="block text-sm font-medium text-gray-700 mb-1">نوع المقارنة</label>
                            <select id="comparison_type" name="comparison_type" class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500">
                                <option value="daily" {% if comparison_type == 'daily' %}selected{% endif %}>يومي</option>
                                <option value="product" {% if comparison_type == 'product' %}selected{% endif %}>المنتجات</option>
                                <option value="category" {% if comparison_type == 'category' %}selected{% endif %}>الفئات</option>
                                <option value="payment" {% if comparison_type == 'payment' %}selected{% endif %}>طرق الدفع</option>
                            </select>
                        </div>
                        <div class="flex items-end">
                            <button type="submit" class="bg-indigo-600 text-white px-4 py-2 rounded-md hover:bg-indigo-700 transition-all">
                                <i class="ri-filter-3-line ml-1"></i>
                                تطبيق الفلتر
                            </button>
                        </div>
                    </form>
                </div>

                <!-- Comparison Summary -->
                <div class="bg-white p-6 rounded-lg shadow-sm mb-6">
                    <h2 class="text-lg font-bold text-gray-800 mb-4">ملخص المقارنة</h2>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div class="p-4 bg-indigo-50 rounded-lg">
                                <h3 class="text-sm font-medium text-gray-500 mb-1">الفترة الحالية</h3>
                                <p class="text-sm text-gray-600">{{ current_from }} إلى {{ current_to }}</p>
                                <p class="text-xl font-bold text-indigo-600 mt-2">
                                    {{ "%.2f"|format(current_total_sales) }} ج.م
                                </p>
                                <p class="text-sm text-gray-600">
                                    {{ current_orders_count }} طلب
                                </p>
                            </div>
                            <div class="p-4 bg-gray-100 rounded-lg">
                                <h3 class="text-sm font-medium text-gray-500 mb-1">الفترة السابقة</h3>
                                <p class="text-sm text-gray-600">{{ previous_from }} إلى {{ previous_to }}</p>
                                <p class="text-xl font-bold text-gray-600 mt-2">
                                    {{ "%.2f"|format(previous_total_sales) }} ج.م
                                </p>
                                <p class="text-sm text-gray-600">
                                    {{ previous_orders_count }} طلب
                                </p>
                            </div>
                        </div>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div class="p-4 {% if sales_change >= 0 %}bg-green-50{% else %}bg-red-50{% endif %} rounded-lg">
                                <h3 class="text-sm font-medium text-gray-500 mb-1">التغير في المبيعات</h3>
                                <p class="text-xl font-bold {% if sales_change >= 0 %}text-green-600{% else %}text-red-600{% endif %} mt-2">
                                    {{ "%.2f"|format(sales_change) }} ج.م
                                </p>
                                <p class="text-sm {% if sales_change_percentage >= 0 %}text-green-600{% else %}text-red-600{% endif %}">
                                    {{ "%.2f"|format(sales_change_percentage) }}%
                                    {% if sales_change_percentage >= 0 %}
                                        <i class="ri-arrow-up-line"></i>
                                    {% else %}
                                        <i class="ri-arrow-down-line"></i>
                                    {% endif %}
                                </p>
                            </div>
                            <div class="p-4 {% if orders_change >= 0 %}bg-green-50{% else %}bg-red-50{% endif %} rounded-lg">
                                <h3 class="text-sm font-medium text-gray-500 mb-1">التغير في عدد الطلبات</h3>
                                <p class="text-xl font-bold {% if orders_change >= 0 %}text-green-600{% else %}text-red-600{% endif %} mt-2">
                                    {{ orders_change }}
                                </p>
                                <p class="text-sm {% if orders_change_percentage >= 0 %}text-green-600{% else %}text-red-600{% endif %}">
                                    {{ "%.2f"|format(orders_change_percentage) }}%
                                    {% if orders_change_percentage >= 0 %}
                                        <i class="ri-arrow-up-line"></i>
                                    {% else %}
                                        <i class="ri-arrow-down-line"></i>
                                    {% endif %}
                                </p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Comparison Chart -->
                <div class="bg-white p-6 rounded-lg shadow-sm mb-6">
                    <h2 class="text-lg font-bold text-gray-800 mb-4">رسم بياني للمقارنة</h2>
                    <div class="h-80">
                        <canvas id="comparisonChart"></canvas>
                    </div>
                </div>

                <!-- Comparison Table -->
                <div class="bg-white rounded-lg shadow-sm overflow-hidden">
                    <h2 class="text-lg font-bold text-gray-800 p-6 border-b">
                        {% if comparison_type == 'daily' %}
                            مقارنة المبيعات اليومية
                        {% elif comparison_type == 'product' %}
                            مقارنة مبيعات المنتجات
                        {% elif comparison_type == 'category' %}
                            مقارنة مبيعات الفئات
                        {% else %}
                            مقارنة مبيعات طرق الدفع
                        {% endif %}
                    </h2>
                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-50">
                                <tr>
                                    {% if comparison_type == 'daily' %}
                                        <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            التاريخ
                                        </th>
                                    {% elif comparison_type == 'product' %}
                                        <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            المنتج
                                        </th>
                                    {% elif comparison_type == 'category' %}
                                        <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            الفئة
                                        </th>
                                    {% else %}
                                        <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            طريقة الدفع
                                        </th>
                                    {% endif %}
                                    <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        الفترة الحالية
                                    </th>
                                    <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        الفترة السابقة
                                    </th>
                                    <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        التغير
                                    </th>
                                    <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        نسبة التغير
                                    </th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                {% if comparison_type == 'daily' %}
                                    {% for current in current_data %}
                                        <tr class="hover:bg-gray-50">
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                                {{ current.date }}
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                                {{ "%.2f"|format(current.total_amount) }} ج.م
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                                {% set prev_amount = 0 %}
                                                {% for prev in previous_data %}
                                                    {% if prev.date == current.date %}
                                                        {% set prev_amount = prev.total_amount %}
                                                    {% endif %}
                                                {% endfor %}
                                                {{ "%.2f"|format(prev_amount) }} ج.م
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                                {% set prev_amount = 0 %}
                                                {% for prev in previous_data %}
                                                    {% if prev.date == current.date %}
                                                        {% set prev_amount = prev.total_amount %}
                                                    {% endif %}
                                                {% endfor %}
                                                {% set change = current.total_amount - prev_amount %}
                                                <span class="{% if change >= 0 %}text-green-600{% else %}text-red-600{% endif %}">
                                                    {{ "%.2f"|format(change) }} ج.م
                                                </span>
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                                {% set prev_amount = 0 %}
                                                {% for prev in previous_data %}
                                                    {% if prev.date == current.date %}
                                                        {% set prev_amount = prev.total_amount %}
                                                    {% endif %}
                                                {% endfor %}
                                                {% set change = current.total_amount - prev_amount %}
                                                {% set change_pct = (change / prev_amount * 100) if prev_amount > 0 else 0 %}
                                                <span class="{% if change >= 0 %}text-green-600{% else %}text-red-600{% endif %}">
                                                    {{ "%.2f"|format(change_pct) }}%
                                                    {% if change >= 0 %}
                                                        <i class="ri-arrow-up-line"></i>
                                                    {% else %}
                                                        <i class="ri-arrow-down-line"></i>
                                                    {% endif %}
                                                </span>
                                            </td>
                                        </tr>
                                    {% else %}
                                        <tr>
                                            <td colspan="5" class="px-6 py-4 text-center text-gray-500">
                                                لا توجد بيانات للفترة المحددة
                                            </td>
                                        </tr>
                                    {% endfor %}
                                {% elif comparison_type == 'product' or comparison_type == 'category' %}
                                    {% for current in current_data %}
                                        <tr class="hover:bg-gray-50">
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                                {{ current.name }}
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                                {{ "%.2f"|format(current.total_amount) }} ج.م
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                                {% set prev_amount = 0 %}
                                                {% for prev in previous_data %}
                                                    {% if prev.id == current.id %}
                                                        {% set prev_amount = prev.total_amount %}
                                                    {% endif %}
                                                {% endfor %}
                                                {{ "%.2f"|format(prev_amount) }} ج.م
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                                {% set prev_amount = 0 %}
                                                {% for prev in previous_data %}
                                                    {% if prev.id == current.id %}
                                                        {% set prev_amount = prev.total_amount %}
                                                    {% endif %}
                                                {% endfor %}
                                                {% set change = current.total_amount - prev_amount %}
                                                <span class="{% if change >= 0 %}text-green-600{% else %}text-red-600{% endif %}">
                                                    {{ "%.2f"|format(change) }} ج.م
                                                </span>
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                                {% set prev_amount = 0 %}
                                                {% for prev in previous_data %}
                                                    {% if prev.id == current.id %}
                                                        {% set prev_amount = prev.total_amount %}
                                                    {% endif %}
                                                {% endfor %}
                                                {% set change = current.total_amount - prev_amount %}
                                                {% set change_pct = (change / prev_amount * 100) if prev_amount > 0 else 0 %}
                                                <span class="{% if change >= 0 %}text-green-600{% else %}text-red-600{% endif %}">
                                                    {{ "%.2f"|format(change_pct) }}%
                                                    {% if change >= 0 %}
                                                        <i class="ri-arrow-up-line"></i>
                                                    {% else %}
                                                        <i class="ri-arrow-down-line"></i>
                                                    {% endif %}
                                                </span>
                                            </td>
                                        </tr>
                                    {% else %}
                                        <tr>
                                            <td colspan="5" class="px-6 py-4 text-center text-gray-500">
                                                لا توجد بيانات للفترة المحددة
                                            </td>
                                        </tr>
                                    {% endfor %}
                                {% else %}
                                    {% for current in current_data %}
                                        <tr class="hover:bg-gray-50">
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                                {{ current.payment_method }}
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                                {{ "%.2f"|format(current.total_amount) }} ج.م
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                                {% set prev_amount = 0 %}
                                                {% for prev in previous_data %}
                                                    {% if prev.payment_method == current.payment_method %}
                                                        {% set prev_amount = prev.total_amount %}
                                                    {% endif %}
                                                {% endfor %}
                                                {{ "%.2f"|format(prev_amount) }} ج.م
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                                {% set prev_amount = 0 %}
                                                {% for prev in previous_data %}
                                                    {% if prev.payment_method == current.payment_method %}
                                                        {% set prev_amount = prev.total_amount %}
                                                    {% endif %}
                                                {% endfor %}
                                                {% set change = current.total_amount - prev_amount %}
                                                <span class="{% if change >= 0 %}text-green-600{% else %}text-red-600{% endif %}">
                                                    {{ "%.2f"|format(change) }} ج.م
                                                </span>
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                                {% set prev_amount = 0 %}
                                                {% for prev in previous_data %}
                                                    {% if prev.payment_method == current.payment_method %}
                                                        {% set prev_amount = prev.total_amount %}
                                                    {% endif %}
                                                {% endfor %}
                                                {% set change = current.total_amount - prev_amount %}
                                                {% set change_pct = (change / prev_amount * 100) if prev_amount > 0 else 0 %}
                                                <span class="{% if change >= 0 %}text-green-600{% else %}text-red-600{% endif %}">
                                                    {{ "%.2f"|format(change_pct) }}%
                                                    {% if change >= 0 %}
                                                        <i class="ri-arrow-up-line"></i>
                                                    {% else %}
                                                        <i class="ri-arrow-down-line"></i>
                                                    {% endif %}
                                                </span>
                                            </td>
                                        </tr>
                                    {% else %}
                                        <tr>
                                            <td colspan="5" class="px-6 py-4 text-center text-gray-500">
                                                لا توجد بيانات للفترة المحددة
                                            </td>
                                        </tr>
                                    {% endfor %}
                                {% endif %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <script>
        // Prepare data for chart
        {% if comparison_type == 'daily' %}
            const labels = [
                {% for current in current_data %}
                    '{{ current.date }}'{% if not loop.last %},{% endif %}
                {% endfor %}
            ];

            const currentData = [
                {% for current in current_data %}
                    {{ current.total_amount }}{% if not loop.last %},{% endif %}
                {% endfor %}
            ];

            const previousData = [
                {% for current in current_data %}
                    {% set prev_amount = 0 %}
                    {% for prev in previous_data %}
                        {% if prev.date == current.date %}
                            {% set prev_amount = prev.total_amount %}
                        {% endif %}
                    {% endfor %}
                    {{ prev_amount }}{% if not loop.last %},{% endif %}
                {% endfor %}
            ];
        {% elif comparison_type == 'product' or comparison_type == 'category' %}
            const labels = [
                {% for current in current_data %}
                    '{{ current.name }}'{% if not loop.last %},{% endif %}
                {% endfor %}
            ];

            const currentData = [
                {% for current in current_data %}
                    {{ current.total_amount }}{% if not loop.last %},{% endif %}
                {% endfor %}
            ];

            const previousData = [
                {% for current in current_data %}
                    {% set prev_amount = 0 %}
                    {% for prev in previous_data %}
                        {% if prev.id == current.id %}
                            {% set prev_amount = prev.total_amount %}
                        {% endif %}
                    {% endfor %}
                    {{ prev_amount }}{% if not loop.last %},{% endif %}
                {% endfor %}
            ];
        {% else %}
            const labels = [
                {% for current in current_data %}
                    '{{ current.payment_method }}'{% if not loop.last %},{% endif %}
                {% endfor %}
            ];

            const currentData = [
                {% for current in current_data %}
                    {{ current.total_amount }}{% if not loop.last %},{% endif %}
                {% endfor %}
            ];

            const previousData = [
                {% for current in current_data %}
                    {% set prev_amount = 0 %}
                    {% for prev in previous_data %}
                        {% if prev.payment_method == current.payment_method %}
                            {% set prev_amount = prev.total_amount %}
                        {% endif %}
                    {% endfor %}
                    {{ prev_amount }}{% if not loop.last %},{% endif %}
                {% endfor %}
            ];
        {% endif %}

        // Create chart
        const ctx = document.getElementById('comparisonChart').getContext('2d');
        const comparisonChart = new Chart(ctx, {
            type: 'bar',
            data: {
                labels: labels,
                datasets: [{
                    label: 'الفترة الحالية',
                    data: currentData,
                    backgroundColor: 'rgba(99, 102, 241, 0.5)',
                    borderColor: 'rgba(99, 102, 241, 1)',
                    borderWidth: 1
                }, {
                    label: 'الفترة السابقة',
                    data: previousData,
                    backgroundColor: 'rgba(156, 163, 175, 0.5)',
                    borderColor: 'rgba(156, 163, 175, 1)',
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true,
                        title: {
                            display: true,
                            text: 'القيمة (ج.م)'
                        }
                    }
                }
            }
        });
    </script>
</body>
</html>
