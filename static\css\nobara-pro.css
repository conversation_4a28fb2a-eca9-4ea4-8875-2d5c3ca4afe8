/**
 * <PERSON><PERSON> Pro - تصميم احترافي لنظام نقاط البيع
 * تم التطوير بواسطة م/ فؤاد صابر
 * الإصدار: 1.0.0
 * التاريخ: 2023
 */

:root {
    /* الألوان الأساسية */
    --primary: #1e40af;
    --primary-light: #3b82f6;
    --primary-dark: #1e3a8a;
    --primary-50: #eff6ff;
    --primary-100: #dbeafe;
    --primary-200: #bfdbfe;
    --primary-300: #93c5fd;
    --primary-400: #60a5fa;
    --primary-500: #3b82f6;
    --primary-600: #2563eb;
    --primary-700: #1d4ed8;
    --primary-800: #1e40af;
    --primary-900: #1e3a8a;
    --primary-950: #172554;
    
    /* الألوان الثانوية */
    --secondary: #6b7280;
    --success: #10b981;
    --danger: #ef4444;
    --warning: #f59e0b;
    --info: #3b82f6;
    
    /* ألوان الخلفية */
    --bg-light: #f9fafb;
    --bg-dark: #1e293b;
    --bg-card: #ffffff;
    --bg-card-dark: #1e293b;
    
    /* ألوان النص */
    --text-primary: #111827;
    --text-secondary: #4b5563;
    --text-muted: #6b7280;
    --text-light: #f9fafb;
    
    /* ألوان الحدود */
    --border-light: #e5e7eb;
    --border-dark: #374151;
    
    /* الظلال */
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    
    /* الانتقالات */
    --transition-fast: 150ms;
    --transition-normal: 300ms;
    --transition-slow: 500ms;
    
    /* نصف قطر الحدود */
    --rounded-sm: 0.125rem;
    --rounded: 0.25rem;
    --rounded-md: 0.375rem;
    --rounded-lg: 0.5rem;
    --rounded-xl: 0.75rem;
    --rounded-2xl: 1rem;
    --rounded-3xl: 1.5rem;
    --rounded-full: 9999px;
}

/* تنسيقات عامة */
body {
    font-family: 'Cairo', sans-serif;
    background-color: var(--bg-light);
    color: var(--text-primary);
    line-height: 1.5;
}

/* تنسيقات البطاقات */
.card {
    background-color: var(--bg-card);
    border-radius: var(--rounded-lg);
    box-shadow: var(--shadow);
    transition: box-shadow var(--transition-normal);
}

.card:hover {
    box-shadow: var(--shadow-md);
}

.card-header {
    padding: 1rem 1.5rem;
    border-bottom: 1px solid var(--border-light);
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.card-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--text-primary);
    margin: 0;
}

.card-body {
    padding: 1.5rem;
}

.card-footer {
    padding: 1rem 1.5rem;
    border-top: 1px solid var(--border-light);
}

/* تنسيقات الأزرار */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 0.5rem 1rem;
    font-weight: 500;
    border-radius: var(--rounded);
    transition: all var(--transition-fast);
    cursor: pointer;
    border: none;
    outline: none;
}

.btn-primary {
    background-color: var(--primary);
    color: white;
}

.btn-primary:hover {
    background-color: var(--primary-dark);
}

.btn-secondary {
    background-color: var(--secondary);
    color: white;
}

.btn-secondary:hover {
    background-color: #4b5563;
}

.btn-success {
    background-color: var(--success);
    color: white;
}

.btn-success:hover {
    background-color: #0d9668;
}

.btn-danger {
    background-color: var(--danger);
    color: white;
}

.btn-danger:hover {
    background-color: #dc2626;
}

.btn-warning {
    background-color: var(--warning);
    color: white;
}

.btn-warning:hover {
    background-color: #d97706;
}

.btn-info {
    background-color: var(--info);
    color: white;
}

.btn-info:hover {
    background-color: #2563eb;
}

.btn-sm {
    padding: 0.25rem 0.5rem;
    font-size: 0.875rem;
}

.btn-lg {
    padding: 0.75rem 1.5rem;
    font-size: 1.125rem;
}

.btn-icon {
    padding: 0.5rem;
    border-radius: var(--rounded-full);
}

/* تنسيقات النماذج */
.form-group {
    margin-bottom: 1rem;
}

.form-label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: var(--text-primary);
}

.form-control {
    width: 100%;
    padding: 0.5rem 0.75rem;
    font-size: 1rem;
    line-height: 1.5;
    color: var(--text-primary);
    background-color: white;
    border: 1px solid var(--border-light);
    border-radius: var(--rounded);
    transition: border-color var(--transition-fast), box-shadow var(--transition-fast);
}

.form-control:focus {
    border-color: var(--primary-light);
    outline: none;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.25);
}

/* تنسيقات الجداول */
.table {
    width: 100%;
    border-collapse: collapse;
}

.table th,
.table td {
    padding: 0.75rem 1rem;
    border-bottom: 1px solid var(--border-light);
}

.table th {
    font-weight: 600;
    text-align: right;
    background-color: var(--bg-light);
}

.table tr:hover {
    background-color: var(--primary-50);
}

/* تنسيقات الإشعارات */
.alert {
    padding: 1rem;
    border-radius: var(--rounded);
    margin-bottom: 1rem;
}

.alert-success {
    background-color: #d1fae5;
    color: #065f46;
    border: 1px solid #a7f3d0;
}

.alert-danger {
    background-color: #fee2e2;
    color: #b91c1c;
    border: 1px solid #fecaca;
}

.alert-warning {
    background-color: #fffbeb;
    color: #92400e;
    border: 1px solid #fef3c7;
}

.alert-info {
    background-color: #eff6ff;
    color: #1e40af;
    border: 1px solid #dbeafe;
}

/* تنسيقات الشارات */
.badge {
    display: inline-flex;
    align-items: center;
    padding: 0.25rem 0.5rem;
    font-size: 0.75rem;
    font-weight: 600;
    border-radius: var(--rounded-full);
}

.badge-primary {
    background-color: var(--primary-100);
    color: var(--primary-800);
}

.badge-success {
    background-color: #d1fae5;
    color: #065f46;
}

.badge-danger {
    background-color: #fee2e2;
    color: #b91c1c;
}

.badge-warning {
    background-color: #fffbeb;
    color: #92400e;
}

.badge-info {
    background-color: #eff6ff;
    color: #1e40af;
}

/* تنسيقات الشريط الجانبي */
.sidebar {
    background-color: white;
    box-shadow: var(--shadow-md);
    width: 280px;
    height: 100vh;
    position: fixed;
    top: 0;
    right: 0;
    z-index: 50;
    transition: transform var(--transition-normal);
}

.sidebar-header {
    padding: 1.5rem;
    display: flex;
    align-items: center;
    border-bottom: 1px solid var(--border-light);
}

.sidebar-logo {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--primary);
}

.sidebar-nav {
    padding: 1rem 0;
}

.sidebar-item {
    padding: 0.75rem 1.5rem;
    display: flex;
    align-items: center;
    color: var(--text-secondary);
    transition: background-color var(--transition-fast), color var(--transition-fast);
}

.sidebar-item:hover {
    background-color: var(--primary-50);
    color: var(--primary);
}

.sidebar-item.active {
    background-color: var(--primary-50);
    color: var(--primary);
    border-right: 3px solid var(--primary);
}

.sidebar-icon {
    margin-left: 0.75rem;
    font-size: 1.25rem;
}

/* تنسيقات الشريط العلوي */
.navbar {
    background-color: white;
    box-shadow: var(--shadow);
    padding: 0.75rem 1.5rem;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.navbar-brand {
    font-size: 1.25rem;
    font-weight: 700;
    color: var(--primary);
}

.navbar-nav {
    display: flex;
    align-items: center;
}

.navbar-item {
    margin-right: 1rem;
    color: var(--text-secondary);
    transition: color var(--transition-fast);
}

.navbar-item:hover {
    color: var(--primary);
}

/* تنسيقات الصفحة الرئيسية */
.dashboard-stats {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.stat-card {
    background-color: white;
    border-radius: var(--rounded-lg);
    box-shadow: var(--shadow);
    padding: 1.5rem;
    transition: transform var(--transition-normal), box-shadow var(--transition-normal);
}

.stat-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
}

.stat-icon {
    width: 48px;
    height: 48px;
    border-radius: var(--rounded-full);
    background-color: var(--primary-50);
    color: var(--primary);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    margin-bottom: 1rem;
}

.stat-value {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: 0.25rem;
}

.stat-label {
    font-size: 0.875rem;
    color: var(--text-secondary);
}

/* تنسيقات نقطة البيع */
.pos-container {
    display: grid;
    grid-template-columns: 1fr 400px;
    gap: 1.5rem;
    height: calc(100vh - 4rem);
}

.pos-products {
    overflow-y: auto;
    padding: 1rem;
}

.pos-cart {
    background-color: white;
    border-radius: var(--rounded-lg);
    box-shadow: var(--shadow-lg);
    display: flex;
    flex-direction: column;
    height: 100%;
}

.pos-cart-header {
    padding: 1rem;
    border-bottom: 1px solid var(--border-light);
}

.pos-cart-items {
    flex: 1;
    overflow-y: auto;
    padding: 1rem;
}

.pos-cart-footer {
    padding: 1rem;
    border-top: 1px solid var(--border-light);
}

.pos-product-card {
    background-color: white;
    border-radius: var(--rounded-lg);
    box-shadow: var(--shadow);
    padding: 1rem;
    cursor: pointer;
    transition: transform var(--transition-fast), box-shadow var(--transition-fast);
}

.pos-product-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-md);
}

/* تنسيقات الطباعة */
@media print {
    body {
        background-color: white;
    }
    
    .no-print {
        display: none !important;
    }
    
    .print-only {
        display: block !important;
    }
    
    .card {
        box-shadow: none;
        border: 1px solid #e5e7eb;
    }
}
