<!DOCTYPE html>
<html lang="ar" dir="rtl" class="light">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>FoUaD - تفاصيل العميل</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdn.jsdelivr.net/npm/remixicon@2.5.0/fonts/remixicon.css" rel="stylesheet">
    <script>
        tailwind.config = {
            darkMode: 'class',
            theme: {
                extend: {
                    colors: {
                        primary: '#3B82F6',
                        secondary: '#10b981',
                        dark: {
                            100: '#1E293B',
                            200: '#0F172A',
                            300: '#0B1120',
                            400: '#060A15'
                        }
                    }
                }
            }
        }
    </script>
    <style>
        body {
            font-family: 'Tajawal', sans-serif;
            background-color: #f1f5f9;
            transition: background-color 0.3s ease, color 0.3s ease;
        }
        body.dark {
            background-color: #0F172A;
            color: #E2E8F0;
        }
        .card {
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }
        .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
        }
        .customer-avatar {
            background: linear-gradient(135deg, #3B82F6 0%, #2563EB 100%);
            box-shadow: 0 10px 15px -3px rgba(59, 130, 246, 0.3);
        }
        .action-button {
            transition: all 0.3s ease;
        }
        .action-button:hover {
            transform: translateY(-2px);
        }
        .page-header {
            background: linear-gradient(to right, #3B82F6, #2563EB);
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
        }
        .dark .page-header {
            background: linear-gradient(to right, #1E40AF, #1E3A8A);
        }
        .tab-button {
            position: relative;
            transition: all 0.3s ease;
        }
        .tab-button.active::after {
            content: '';
            position: absolute;
            bottom: -2px;
            left: 0;
            width: 100%;
            height: 2px;
            background-color: #3B82F6;
            border-radius: 2px;
        }
        .dark .tab-button.active::after {
            background-color: #60A5FA;
        }
        .tab-pane {
            display: none;
        }
        .tab-pane.active {
            display: block;
        }
        .badge {
            @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;
        }
        .badge-green {
            @apply bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300;
        }
        .badge-blue {
            @apply bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300;
        }
        .badge-red {
            @apply bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300;
        }
        .badge-yellow {
            @apply bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-300;
        }
        .badge-purple {
            @apply bg-purple-100 text-purple-800 dark:bg-purple-900/30 dark:text-purple-300;
        }
        .badge-gray {
            @apply bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300;
        }
    </style>
</head>
<body class="min-h-screen">
    {% include 'partials/navbar.html' %}

    <!-- Page Header -->
    <div class="page-header py-6 mb-8">
        <div class="container mx-auto px-4">
            <div class="flex flex-col md:flex-row justify-between items-center">
                <div class="flex items-center mb-4 md:mb-0">
                    <div class="customer-avatar w-16 h-16 rounded-full flex items-center justify-center text-white text-2xl font-bold mr-4">
                        {{ customer.name[0].upper() }}
                    </div>
                    <div>
                        <h1 class="text-2xl md:text-3xl font-bold text-white">{{ customer.name }}</h1>
                        <p class="text-blue-100">
                            {% if customer.phone %}
                                <span class="inline-flex items-center">
                                    <i class="ri-phone-line mr-1"></i> {{ customer.phone }}
                                </span>
                            {% endif %}
                            {% if customer.email %}
                                <span class="inline-flex items-center mr-4">
                                    <i class="ri-mail-line mr-1"></i> {{ customer.email }}
                                </span>
                            {% endif %}
                        </p>
                    </div>
                </div>
                <div class="flex space-x-2 space-x-reverse">
                    <a href="{{ url_for('customers.add_payment', id=customer.id) }}" class="action-button bg-green-600 text-white hover:bg-green-700 px-4 py-2 rounded-lg flex items-center shadow-md">
                        <i class="ri-bank-card-line ml-1"></i>
                        إضافة دفعة
                    </a>
                    <a href="{{ url_for('customers.edit', id=customer.id) }}" class="action-button bg-white text-primary hover:bg-blue-50 px-4 py-2 rounded-lg flex items-center shadow-md">
                        <i class="ri-edit-line ml-1"></i>
                        تعديل
                    </a>
                    <a href="{{ url_for('customers.index') }}" class="action-button bg-white text-primary hover:bg-blue-50 px-4 py-2 rounded-lg flex items-center shadow-md">
                        <i class="ri-arrow-right-line ml-1"></i>
                        العودة
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="container mx-auto px-4 pb-8">
        <!-- Stats Cards -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <!-- إجمالي المشتريات -->
            <div class="bg-white dark:bg-gray-800 rounded-xl shadow-md p-6 card">
                <div class="flex items-center">
                    <div class="w-12 h-12 bg-blue-100 dark:bg-blue-900/50 rounded-full flex items-center justify-center mr-4">
                        <i class="ri-money-dollar-circle-line text-2xl text-primary dark:text-blue-400"></i>
                    </div>
                    <div>
                        <p class="text-sm text-gray-500 dark:text-gray-400">إجمالي المشتريات</p>
                        <p class="text-2xl font-bold text-gray-800 dark:text-white">{{ "%.2f"|format(stats.total_spent) }} <span class="text-sm">ج.م</span></p>
                    </div>
                </div>
            </div>

            <!-- المبلغ المدفوع -->
            <div class="bg-white dark:bg-gray-800 rounded-xl shadow-md p-6 card">
                <div class="flex items-center">
                    <div class="w-12 h-12 bg-green-100 dark:bg-green-900/50 rounded-full flex items-center justify-center mr-4">
                        <i class="ri-bank-card-line text-2xl text-green-600 dark:text-green-400"></i>
                    </div>
                    <div>
                        <p class="text-sm text-gray-500 dark:text-gray-400">المبلغ المدفوع</p>
                        <p class="text-2xl font-bold text-gray-800 dark:text-white">{{ "%.2f"|format(stats.paid_amount) }} <span class="text-sm">ج.م</span></p>
                    </div>
                </div>
            </div>

            <!-- المبلغ المتبقي -->
            <div class="bg-white dark:bg-gray-800 rounded-xl shadow-md p-6 card">
                <div class="flex items-center">
                    <div class="w-12 h-12 bg-red-100 dark:bg-red-900/50 rounded-full flex items-center justify-center mr-4">
                        <i class="ri-refund-2-line text-2xl text-red-600 dark:text-red-400"></i>
                    </div>
                    <div>
                        <p class="text-sm text-gray-500 dark:text-gray-400">المبلغ المتبقي</p>
                        <p class="text-2xl font-bold text-gray-800 dark:text-white">{{ "%.2f"|format(stats.remaining_amount) }} <span class="text-sm">ج.م</span></p>
                    </div>
                </div>
            </div>

            <!-- عدد الطلبات -->
            <div class="bg-white dark:bg-gray-800 rounded-xl shadow-md p-6 card">
                <div class="flex items-center">
                    <div class="w-12 h-12 bg-purple-100 dark:bg-purple-900/50 rounded-full flex items-center justify-center mr-4">
                        <i class="ri-shopping-bag-line text-2xl text-purple-600 dark:text-purple-400"></i>
                    </div>
                    <div>
                        <p class="text-sm text-gray-500 dark:text-gray-400">عدد الطلبات</p>
                        <p class="text-2xl font-bold text-gray-800 dark:text-white">{{ stats.order_count }}</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- المبيعات الآجلة -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <!-- عدد المبيعات الآجلة -->
            <div class="bg-white dark:bg-gray-800 rounded-xl shadow-md p-6 card">
                <div class="flex items-center">
                    <div class="w-12 h-12 bg-yellow-100 dark:bg-yellow-900/50 rounded-full flex items-center justify-center mr-4">
                        <i class="ri-time-line text-2xl text-yellow-600 dark:text-yellow-400"></i>
                    </div>
                    <div>
                        <p class="text-sm text-gray-500 dark:text-gray-400">المبيعات الآجلة</p>
                        <p class="text-2xl font-bold text-gray-800 dark:text-white">{{ stats.deferred_orders_count }}</p>
                    </div>
                </div>
            </div>

            <!-- إجمالي المبيعات الآجلة -->
            <div class="bg-white dark:bg-gray-800 rounded-xl shadow-md p-6 card">
                <div class="flex items-center">
                    <div class="w-12 h-12 bg-orange-100 dark:bg-orange-900/50 rounded-full flex items-center justify-center mr-4">
                        <i class="ri-money-dollar-circle-line text-2xl text-orange-600 dark:text-orange-400"></i>
                    </div>
                    <div>
                        <p class="text-sm text-gray-500 dark:text-gray-400">إجمالي الآجل</p>
                        <p class="text-2xl font-bold text-gray-800 dark:text-white">{{ "%.2f"|format(stats.deferred_total) }} <span class="text-sm">ج.م</span></p>
                    </div>
                </div>
            </div>

            <!-- المدفوع من الآجل -->
            <div class="bg-white dark:bg-gray-800 rounded-xl shadow-md p-6 card">
                <div class="flex items-center">
                    <div class="w-12 h-12 bg-green-100 dark:bg-green-900/50 rounded-full flex items-center justify-center mr-4">
                        <i class="ri-bank-card-line text-2xl text-green-600 dark:text-green-400"></i>
                    </div>
                    <div>
                        <p class="text-sm text-gray-500 dark:text-gray-400">المدفوع من الآجل</p>
                        <p class="text-2xl font-bold text-gray-800 dark:text-white">{{ "%.2f"|format(stats.deferred_paid) }} <span class="text-sm">ج.م</span></p>
                    </div>
                </div>
            </div>

            <!-- المتبقي من الآجل -->
            <div class="bg-white dark:bg-gray-800 rounded-xl shadow-md p-6 card">
                <div class="flex items-center">
                    <div class="w-12 h-12 bg-red-100 dark:bg-red-900/50 rounded-full flex items-center justify-center mr-4">
                        <i class="ri-refund-2-line text-2xl text-red-600 dark:text-red-400"></i>
                    </div>
                    <div>
                        <p class="text-sm text-gray-500 dark:text-gray-400">المتبقي من الآجل</p>
                        <p class="text-2xl font-bold text-gray-800 dark:text-white">{{ "%.2f"|format(stats.deferred_remaining) }} <span class="text-sm">ج.م</span></p>
                    </div>
                </div>
            </div>
        </div>

        <!-- حالة الدفع -->
        <div class="bg-white dark:bg-gray-800 rounded-xl shadow-md p-6 mb-8">
            <h2 class="text-lg font-bold text-gray-800 dark:text-white mb-4">حالة الدفع</h2>

            <!-- إجمالي المشتريات -->
            <div class="mb-4">
                <h3 class="text-md font-medium text-gray-700 dark:text-gray-300 mb-2">إجمالي المشتريات</h3>
                <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-4 mb-2">
                    {% set payment_percentage = (stats.paid_amount / stats.total_spent * 100) if stats.total_spent > 0 else 0 %}
                    <div class="bg-blue-600 dark:bg-blue-500 h-4 rounded-full" style="width: {{ payment_percentage }}%"></div>
                </div>
                <div class="flex justify-between text-sm text-gray-600 dark:text-gray-400">
                    <span>{{ "%.1f"|format(payment_percentage) }}% مدفوع</span>
                    <span>{{ "%.1f"|format(100 - payment_percentage) }}% متبقي</span>
                </div>
                <div class="flex justify-between mt-1 text-sm">
                    <span class="font-medium text-green-600 dark:text-green-400">{{ "%.2f"|format(stats.paid_amount) }} ج.م</span>
                    <span class="font-medium text-red-600 dark:text-red-400">{{ "%.2f"|format(stats.remaining_amount) }} ج.م</span>
                </div>
            </div>

            <!-- المبيعات الآجلة -->
            <div>
                <h3 class="text-md font-medium text-gray-700 dark:text-gray-300 mb-2">المبيعات الآجلة</h3>
                <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-4 mb-2">
                    {% set deferred_payment_percentage = (stats.deferred_paid / stats.deferred_total * 100) if stats.deferred_total > 0 else 0 %}
                    <div class="bg-orange-600 dark:bg-orange-500 h-4 rounded-full" style="width: {{ deferred_payment_percentage }}%"></div>
                </div>
                <div class="flex justify-between text-sm text-gray-600 dark:text-gray-400">
                    <span>{{ "%.1f"|format(deferred_payment_percentage) }}% مدفوع</span>
                    <span>{{ "%.1f"|format(100 - deferred_payment_percentage) }}% متبقي</span>
                </div>
                <div class="flex justify-between mt-1 text-sm">
                    <span class="font-medium text-green-600 dark:text-green-400">{{ "%.2f"|format(stats.deferred_paid) }} ج.م</span>
                    <span class="font-medium text-red-600 dark:text-red-400">{{ "%.2f"|format(stats.deferred_remaining) }} ج.م</span>
                </div>
            </div>
        </div>

        <!-- Tabs -->
        <div class="mb-6 border-b border-gray-200 dark:border-gray-700">
            <div class="flex overflow-x-auto">
                <button onclick="showTab('info')" id="tab-info" class="tab-button active px-4 py-2 text-gray-600 dark:text-gray-300 font-medium">
                    <i class="ri-information-line ml-1"></i>
                    معلومات العميل
                </button>
                <button onclick="showTab('orders')" id="tab-orders" class="tab-button px-4 py-2 text-gray-600 dark:text-gray-300 font-medium">
                    <i class="ri-shopping-cart-line ml-1"></i>
                    الطلبات
                </button>
                <button onclick="showTab('deferred')" id="tab-deferred" class="tab-button px-4 py-2 text-gray-600 dark:text-gray-300 font-medium">
                    <i class="ri-time-line ml-1"></i>
                    المبيعات الآجلة
                </button>
                <button onclick="showTab('payments')" id="tab-payments" class="tab-button px-4 py-2 text-gray-600 dark:text-gray-300 font-medium">
                    <i class="ri-bank-card-line ml-1"></i>
                    المدفوعات
                </button>
                <button onclick="showTab('returns')" id="tab-returns" class="tab-button px-4 py-2 text-gray-600 dark:text-gray-300 font-medium">
                    <i class="ri-arrow-go-back-line ml-1"></i>
                    المرتجعات
                </button>
                <button onclick="showTab('products')" id="tab-products" class="tab-button px-4 py-2 text-gray-600 dark:text-gray-300 font-medium">
                    <i class="ri-archive-line ml-1"></i>
                    المنتجات
                </button>
                <button onclick="showTab('reports')" id="tab-reports" class="tab-button px-4 py-2 text-gray-600 dark:text-gray-300 font-medium">
                    <i class="ri-file-chart-line ml-1"></i>
                    التقارير
                </button>
            </div>
        </div>

        <!-- Tab Content -->
        <div id="tab-content">
            <!-- معلومات العميل -->
            <div id="content-info" class="tab-pane active">
                <div class="bg-white dark:bg-gray-800 rounded-xl shadow-md overflow-hidden">
                    <div class="bg-gradient-to-r from-blue-500 to-blue-600 dark:from-blue-700 dark:to-blue-800 px-6 py-4">
                        <h2 class="text-xl font-bold text-white flex items-center">
                            <i class="ri-user-line mr-2"></i>
                            معلومات العميل
                        </h2>
                    </div>
                    <div class="p-6">
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div class="space-y-4">
                                <div class="flex items-center border-b border-gray-200 dark:border-gray-700 pb-3">
                                    <div class="w-8 h-8 rounded-full bg-blue-100 dark:bg-blue-900/50 flex items-center justify-center mr-3">
                                        <i class="ri-user-line text-primary dark:text-blue-400"></i>
                                    </div>
                                    <div class="flex-1">
                                        <p class="text-sm text-gray-500 dark:text-gray-400">الاسم</p>
                                        <p class="font-medium text-gray-800 dark:text-white">{{ customer.name }}</p>
                                    </div>
                                </div>

                                <div class="flex items-center border-b border-gray-200 dark:border-gray-700 pb-3">
                                    <div class="w-8 h-8 rounded-full bg-blue-100 dark:bg-blue-900/50 flex items-center justify-center mr-3">
                                        <i class="ri-phone-line text-primary dark:text-blue-400"></i>
                                    </div>
                                    <div class="flex-1">
                                        <p class="text-sm text-gray-500 dark:text-gray-400">رقم الهاتف</p>
                                        <p class="font-medium text-gray-800 dark:text-white">{{ customer.phone or 'غير متوفر' }}</p>
                                    </div>
                                </div>

                                <div class="flex items-center border-b border-gray-200 dark:border-gray-700 pb-3">
                                    <div class="w-8 h-8 rounded-full bg-blue-100 dark:bg-blue-900/50 flex items-center justify-center mr-3">
                                        <i class="ri-mail-line text-primary dark:text-blue-400"></i>
                                    </div>
                                    <div class="flex-1">
                                        <p class="text-sm text-gray-500 dark:text-gray-400">البريد الإلكتروني</p>
                                        <p class="font-medium text-gray-800 dark:text-white">{{ customer.email or 'غير متوفر' }}</p>
                                    </div>
                                </div>
                            </div>

                            <div class="space-y-4">
                                <div class="flex items-center border-b border-gray-200 dark:border-gray-700 pb-3">
                                    <div class="w-8 h-8 rounded-full bg-blue-100 dark:bg-blue-900/50 flex items-center justify-center mr-3">
                                        <i class="ri-map-pin-line text-primary dark:text-blue-400"></i>
                                    </div>
                                    <div class="flex-1">
                                        <p class="text-sm text-gray-500 dark:text-gray-400">العنوان</p>
                                        <p class="font-medium text-gray-800 dark:text-white">{{ customer.address or 'غير متوفر' }}</p>
                                    </div>
                                </div>

                                <div class="flex items-center border-b border-gray-200 dark:border-gray-700 pb-3">
                                    <div class="w-8 h-8 rounded-full bg-blue-100 dark:bg-blue-900/50 flex items-center justify-center mr-3">
                                        <i class="ri-calendar-line text-primary dark:text-blue-400"></i>
                                    </div>
                                    <div class="flex-1">
                                        <p class="text-sm text-gray-500 dark:text-gray-400">تاريخ التسجيل</p>
                                        <p class="font-medium text-gray-800 dark:text-white">{{ customer.created_at.strftime('%Y-%m-%d') }}</p>
                                    </div>
                                </div>

                                <div class="flex items-center">
                                    <div class="w-8 h-8 rounded-full bg-blue-100 dark:bg-blue-900/50 flex items-center justify-center mr-3">
                                        <i class="ri-time-line text-primary dark:text-blue-400"></i>
                                    </div>
                                    <div class="flex-1">
                                        <p class="text-sm text-gray-500 dark:text-gray-400">آخر تحديث</p>
                                        <p class="font-medium text-gray-800 dark:text-white">
                                            {% if customer.updated_at %}
                                                {{ customer.updated_at.strftime('%Y-%m-%d') }}
                                            {% else %}
                                                {{ customer.created_at.strftime('%Y-%m-%d') }}
                                            {% endif %}
                                        </p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- الطلبات -->
            <div id="content-orders" class="tab-pane">
                <div class="bg-white dark:bg-gray-800 rounded-xl shadow-md overflow-hidden">
                    <div class="bg-gradient-to-r from-green-500 to-green-600 dark:from-green-700 dark:to-green-800 px-6 py-4 flex justify-between items-center">
                        <h2 class="text-xl font-bold text-white flex items-center">
                            <i class="ri-shopping-cart-line mr-2"></i>
                            طلبات العميل
                        </h2>
                        <a href="{{ url_for('pos.index', customer_id=customer.id) }}" class="bg-white/20 hover:bg-white/30 text-white px-4 py-1 rounded-lg text-sm font-medium transition-colors flex items-center">
                            <i class="ri-add-line ml-1"></i>
                            طلب جديد
                        </a>
                    </div>
                    <div class="p-6">
                        <div class="mb-4 flex flex-wrap gap-2">
                            <div class="relative">
                                <input type="text" id="order-search" placeholder="بحث برقم الفاتورة..." class="w-full px-4 py-2 rounded-lg border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-800 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <button id="order-search-btn" class="absolute left-2 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300">
                                    <i class="ri-search-line"></i>
                                </button>
                            </div>
                            <div class="relative">
                                <input type="date" id="order-date-from" class="px-4 py-2 rounded-lg border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-800 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <span class="absolute right-2 top-1/2 transform -translate-y-1/2 text-gray-400">من</span>
                            </div>
                            <div class="relative">
                                <input type="date" id="order-date-to" class="px-4 py-2 rounded-lg border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-800 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <span class="absolute right-2 top-1/2 transform -translate-y-1/2 text-gray-400">إلى</span>
                            </div>
                            <select id="order-status" class="px-4 py-2 rounded-lg border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-800 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <option value="">جميع الحالات</option>
                                <option value="completed">مكتمل</option>
                                <option value="cancelled">ملغي</option>
                                <option value="returned">مرتجع</option>
                                <option value="deferred">مؤجل</option>
                            </select>
                            <button id="order-filter-btn" class="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors">
                                <i class="ri-filter-line ml-1"></i>
                                تصفية
                            </button>
                        </div>

                        <div id="orders-table-container" class="overflow-x-auto">
                            <table class="w-full">
                                <thead>
                                    <tr class="bg-gray-50 dark:bg-gray-700 text-gray-500 dark:text-gray-300 text-xs uppercase">
                                        <th class="px-4 py-3 text-right font-medium rounded-tr-lg">رقم الفاتورة</th>
                                        <th class="px-4 py-3 text-right font-medium">التاريخ</th>
                                        <th class="px-4 py-3 text-right font-medium">المبلغ</th>
                                        <th class="px-4 py-3 text-right font-medium">المدفوع</th>
                                        <th class="px-4 py-3 text-right font-medium">المتبقي</th>
                                        <th class="px-4 py-3 text-right font-medium">طريقة الدفع</th>
                                        <th class="px-4 py-3 text-right font-medium">الحالة</th>
                                        <th class="px-4 py-3 text-right font-medium rounded-tl-lg">الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody id="orders-table-body" class="divide-y divide-gray-100 dark:divide-gray-700">
                                    <!-- سيتم تحميل البيانات هنا عبر JavaScript -->
                                </tbody>
                            </table>
                        </div>

                        <div id="orders-loading" class="py-8 flex justify-center">
                            <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 dark:border-blue-500"></div>
                        </div>

                        <div id="orders-empty" class="hidden py-8 text-center text-gray-500 dark:text-gray-400">
                            <div class="w-16 h-16 mx-auto bg-gray-100 dark:bg-gray-700 rounded-full flex items-center justify-center mb-4">
                                <i class="ri-shopping-cart-line text-3xl"></i>
                            </div>
                            <p>لا توجد طلبات للعرض</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- المبيعات الآجلة -->
            <div id="content-deferred" class="tab-pane">
                <div class="bg-white dark:bg-gray-800 rounded-xl shadow-md overflow-hidden">
                    <div class="bg-gradient-to-r from-yellow-500 to-yellow-600 dark:from-yellow-700 dark:to-yellow-800 px-6 py-4 flex justify-between items-center">
                        <h2 class="text-xl font-bold text-white flex items-center">
                            <i class="ri-time-line mr-2"></i>
                            المبيعات الآجلة
                        </h2>
                        <a href="{{ url_for('pos.index', customer_id=customer.id) }}" class="bg-white/20 hover:bg-white/30 text-white px-4 py-1 rounded-lg text-sm font-medium transition-colors flex items-center">
                            <i class="ri-add-line ml-1"></i>
                            بيع آجل جديد
                        </a>
                    </div>
                    <div class="p-6">
                        <div class="mb-4 flex flex-wrap gap-2">
                            <div class="relative">
                                <input type="text" id="deferred-search" placeholder="بحث برقم الفاتورة..." class="w-full px-4 py-2 rounded-lg border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-800 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <button id="deferred-search-btn" class="absolute left-2 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300">
                                    <i class="ri-search-line"></i>
                                </button>
                            </div>
                            <div class="relative">
                                <input type="date" id="deferred-date-from" class="px-4 py-2 rounded-lg border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-800 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <span class="absolute right-2 top-1/2 transform -translate-y-1/2 text-gray-400">من</span>
                            </div>
                            <div class="relative">
                                <input type="date" id="deferred-date-to" class="px-4 py-2 rounded-lg border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-800 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <span class="absolute right-2 top-1/2 transform -translate-y-1/2 text-gray-400">إلى</span>
                            </div>
                            <button id="deferred-filter-btn" class="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors">
                                <i class="ri-filter-line ml-1"></i>
                                تصفية
                            </button>
                        </div>

                        <div id="deferred-table-container" class="overflow-x-auto">
                            <table class="w-full">
                                <thead>
                                    <tr class="bg-gray-50 dark:bg-gray-700 text-gray-500 dark:text-gray-300 text-xs uppercase">
                                        <th class="px-4 py-3 text-right font-medium rounded-tr-lg">رقم الفاتورة</th>
                                        <th class="px-4 py-3 text-right font-medium">التاريخ</th>
                                        <th class="px-4 py-3 text-right font-medium">المبلغ</th>
                                        <th class="px-4 py-3 text-right font-medium">المدفوع</th>
                                        <th class="px-4 py-3 text-right font-medium">المتبقي</th>
                                        <th class="px-4 py-3 text-right font-medium">الحالة</th>
                                        <th class="px-4 py-3 text-right font-medium rounded-tl-lg">الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody id="deferred-table-body" class="divide-y divide-gray-100 dark:divide-gray-700">
                                    <!-- سيتم تحميل البيانات هنا عبر JavaScript -->
                                </tbody>
                            </table>
                        </div>

                        <div id="deferred-loading" class="py-8 flex justify-center">
                            <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-yellow-600 dark:border-yellow-500"></div>
                        </div>

                        <div id="deferred-empty" class="hidden py-8 text-center text-gray-500 dark:text-gray-400">
                            <div class="w-16 h-16 mx-auto bg-gray-100 dark:bg-gray-700 rounded-full flex items-center justify-center mb-4">
                                <i class="ri-time-line text-3xl"></i>
                            </div>
                            <p>لا توجد مبيعات آجلة للعرض</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- المدفوعات -->
            <div id="content-payments" class="tab-pane">
                <div class="bg-white dark:bg-gray-800 rounded-xl shadow-md overflow-hidden">
                    <div class="bg-gradient-to-r from-blue-500 to-blue-600 dark:from-blue-700 dark:to-blue-800 px-6 py-4 flex justify-between items-center">
                        <h2 class="text-xl font-bold text-white flex items-center">
                            <i class="ri-bank-card-line mr-2"></i>
                            مدفوعات العميل
                        </h2>
                        <a href="{{ url_for('customers.add_payment', id=customer.id) }}" class="bg-white/20 hover:bg-white/30 text-white px-4 py-1 rounded-lg text-sm font-medium transition-colors flex items-center">
                            <i class="ri-add-line ml-1"></i>
                            إضافة دفعة
                        </a>
                    </div>
                    <div class="p-6">
                        <div class="mb-4 flex flex-wrap gap-2">
                            <div class="relative">
                                <input type="date" id="payment-date-from" class="px-4 py-2 rounded-lg border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-800 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <span class="absolute right-2 top-1/2 transform -translate-y-1/2 text-gray-400">من</span>
                            </div>
                            <div class="relative">
                                <input type="date" id="payment-date-to" class="px-4 py-2 rounded-lg border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-800 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <span class="absolute right-2 top-1/2 transform -translate-y-1/2 text-gray-400">إلى</span>
                            </div>
                            <select id="payment-method" class="px-4 py-2 rounded-lg border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-800 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <option value="">جميع طرق الدفع</option>
                                <option value="cash">نقدي</option>
                                <option value="card">بطاقة</option>
                                <option value="bank_transfer">تحويل بنكي</option>
                                <option value="check">شيك</option>
                            </select>
                            <button id="payment-filter-btn" class="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors">
                                <i class="ri-filter-line ml-1"></i>
                                تصفية
                            </button>
                        </div>

                        <div id="payments-table-container" class="overflow-x-auto">
                            <table class="w-full">
                                <thead>
                                    <tr class="bg-gray-50 dark:bg-gray-700 text-gray-500 dark:text-gray-300 text-xs uppercase">
                                        <th class="px-4 py-3 text-right font-medium rounded-tr-lg">رقم المرجع</th>
                                        <th class="px-4 py-3 text-right font-medium">رقم الفاتورة</th>
                                        <th class="px-4 py-3 text-right font-medium">التاريخ</th>
                                        <th class="px-4 py-3 text-right font-medium">المبلغ</th>
                                        <th class="px-4 py-3 text-right font-medium">طريقة الدفع</th>
                                        <th class="px-4 py-3 text-right font-medium rounded-tl-lg">ملاحظات</th>
                                    </tr>
                                </thead>
                                <tbody id="payments-table-body" class="divide-y divide-gray-100 dark:divide-gray-700">
                                    <!-- سيتم تحميل البيانات هنا عبر JavaScript -->
                                </tbody>
                            </table>
                        </div>

                        <div id="payments-loading" class="py-8 flex justify-center">
                            <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 dark:border-blue-500"></div>
                        </div>

                        <div id="payments-empty" class="hidden py-8 text-center text-gray-500 dark:text-gray-400">
                            <div class="w-16 h-16 mx-auto bg-gray-100 dark:bg-gray-700 rounded-full flex items-center justify-center mb-4">
                                <i class="ri-bank-card-line text-3xl"></i>
                            </div>
                            <p>لا توجد مدفوعات للعرض</p>
                            <div class="mt-4">
                                <a href="{{ url_for('customers.add_payment', id=customer.id) }}" class="inline-flex items-center px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors">
                                    <i class="ri-add-line ml-1"></i>
                                    إضافة دفعة جديدة
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- المرتجعات -->
            <div id="content-returns" class="tab-pane">
                <div class="bg-white dark:bg-gray-800 rounded-xl shadow-md overflow-hidden">
                    <div class="bg-gradient-to-r from-yellow-500 to-yellow-600 dark:from-yellow-700 dark:to-yellow-800 px-6 py-4 flex justify-between items-center">
                        <h2 class="text-xl font-bold text-white flex items-center">
                            <i class="ri-arrow-go-back-line mr-2"></i>
                            مرتجعات العميل
                        </h2>
                    </div>
                    <div class="p-6">
                        <div class="mb-4 flex flex-wrap gap-2">
                            <div class="relative">
                                <input type="date" id="return-date-from" class="px-4 py-2 rounded-lg border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-800 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <span class="absolute right-2 top-1/2 transform -translate-y-1/2 text-gray-400">من</span>
                            </div>
                            <div class="relative">
                                <input type="date" id="return-date-to" class="px-4 py-2 rounded-lg border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-800 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <span class="absolute right-2 top-1/2 transform -translate-y-1/2 text-gray-400">إلى</span>
                            </div>
                            <button id="return-filter-btn" class="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors">
                                <i class="ri-filter-line ml-1"></i>
                                تصفية
                            </button>
                        </div>

                        <div id="returns-table-container" class="overflow-x-auto">
                            <table class="w-full">
                                <thead>
                                    <tr class="bg-gray-50 dark:bg-gray-700 text-gray-500 dark:text-gray-300 text-xs uppercase">
                                        <th class="px-4 py-3 text-right font-medium rounded-tr-lg">رقم المرجع</th>
                                        <th class="px-4 py-3 text-right font-medium">رقم الفاتورة</th>
                                        <th class="px-4 py-3 text-right font-medium">التاريخ</th>
                                        <th class="px-4 py-3 text-right font-medium">المبلغ</th>
                                        <th class="px-4 py-3 text-right font-medium">طريقة الدفع</th>
                                        <th class="px-4 py-3 text-right font-medium">عدد الأصناف</th>
                                        <th class="px-4 py-3 text-right font-medium rounded-tl-lg">الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody id="returns-table-body" class="divide-y divide-gray-100 dark:divide-gray-700">
                                    <!-- سيتم تحميل البيانات هنا عبر JavaScript -->
                                </tbody>
                            </table>
                        </div>

                        <div id="returns-loading" class="py-8 flex justify-center">
                            <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 dark:border-blue-500"></div>
                        </div>

                        <div id="returns-empty" class="hidden py-8 text-center text-gray-500 dark:text-gray-400">
                            <div class="w-16 h-16 mx-auto bg-gray-100 dark:bg-gray-700 rounded-full flex items-center justify-center mb-4">
                                <i class="ri-arrow-go-back-line text-3xl"></i>
                            </div>
                            <p>لا توجد مرتجعات للعرض</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- المنتجات -->
            <div id="content-products" class="tab-pane">
                <div class="bg-white dark:bg-gray-800 rounded-xl shadow-md overflow-hidden">
                    <div class="bg-gradient-to-r from-purple-500 to-purple-600 dark:from-purple-700 dark:to-purple-800 px-6 py-4 flex justify-between items-center">
                        <h2 class="text-xl font-bold text-white flex items-center">
                            <i class="ri-archive-line mr-2"></i>
                            المنتجات المشتراة
                        </h2>
                    </div>
                    <div class="p-6">
                        <div class="mb-4 flex flex-wrap gap-2">
                            <div class="relative flex-1">
                                <input type="text" id="product-search" placeholder="بحث باسم المنتج أو الكود..." class="w-full px-4 py-2 rounded-lg border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-800 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <button id="product-search-btn" class="absolute left-2 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300">
                                    <i class="ri-search-line"></i>
                                </button>
                            </div>
                        </div>

                        <div id="products-table-container" class="overflow-x-auto">
                            <table class="w-full">
                                <thead>
                                    <tr class="bg-gray-50 dark:bg-gray-700 text-gray-500 dark:text-gray-300 text-xs uppercase">
                                        <th class="px-4 py-3 text-right font-medium rounded-tr-lg">المنتج</th>
                                        <th class="px-4 py-3 text-right font-medium">الكود</th>
                                        <th class="px-4 py-3 text-right font-medium">الكمية المشتراة</th>
                                        <th class="px-4 py-3 text-right font-medium">عدد مرات الشراء</th>
                                        <th class="px-4 py-3 text-right font-medium">آخر شراء</th>
                                        <th class="px-4 py-3 text-right font-medium rounded-tl-lg">السعر الحالي</th>
                                    </tr>
                                </thead>
                                <tbody id="products-table-body" class="divide-y divide-gray-100 dark:divide-gray-700">
                                    <!-- سيتم تحميل البيانات هنا عبر JavaScript -->
                                </tbody>
                            </table>
                        </div>

                        <div id="products-loading" class="py-8 flex justify-center">
                            <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 dark:border-blue-500"></div>
                        </div>

                        <div id="products-empty" class="hidden py-8 text-center text-gray-500 dark:text-gray-400">
                            <div class="w-16 h-16 mx-auto bg-gray-100 dark:bg-gray-700 rounded-full flex items-center justify-center mb-4">
                                <i class="ri-archive-line text-3xl"></i>
                            </div>
                            <p>لا توجد منتجات للعرض</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- التقارير -->
            <div id="content-reports" class="tab-pane">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <!-- المشتريات الشهرية -->
                    <div class="bg-white dark:bg-gray-800 rounded-xl shadow-md overflow-hidden">
                        <div class="bg-gray-50 dark:bg-gray-700 px-4 py-3 border-b border-gray-200 dark:border-gray-600">
                            <h3 class="text-lg font-medium text-gray-800 dark:text-white">المشتريات الشهرية</h3>
                        </div>
                        <div class="p-4">
                            <div class="h-64">
                                <canvas id="monthly-orders-chart"></canvas>
                            </div>
                            <div class="mt-4 text-center">
                                <a href="#" class="text-blue-600 dark:text-blue-400 hover:underline">عرض التقرير الكامل</a>
                            </div>
                        </div>
                    </div>

                    <!-- المنتجات الأكثر شراءً -->
                    <div class="bg-white dark:bg-gray-800 rounded-xl shadow-md overflow-hidden">
                        <div class="bg-gray-50 dark:bg-gray-700 px-4 py-3 border-b border-gray-200 dark:border-gray-600">
                            <h3 class="text-lg font-medium text-gray-800 dark:text-white">المنتجات الأكثر شراءً</h3>
                        </div>
                        <div class="p-4">
                            <div class="h-64">
                                <canvas id="top-products-chart"></canvas>
                            </div>
                            <div class="mt-4 text-center">
                                <a href="#" class="text-blue-600 dark:text-blue-400 hover:underline">عرض التقرير الكامل</a>
                            </div>
                        </div>
                    </div>

                    <!-- تقرير المدفوعات -->
                    <div class="bg-white dark:bg-gray-800 rounded-xl shadow-md overflow-hidden">
                        <div class="bg-gray-50 dark:bg-gray-700 px-4 py-3 border-b border-gray-200 dark:border-gray-600">
                            <h3 class="text-lg font-medium text-gray-800 dark:text-white">تقرير المدفوعات</h3>
                        </div>
                        <div class="p-4">
                            <div class="h-64">
                                <canvas id="payments-chart"></canvas>
                            </div>
                            <div class="mt-4 text-center">
                                <a href="#" class="text-blue-600 dark:text-blue-400 hover:underline">عرض التقرير الكامل</a>
                            </div>
                        </div>
                    </div>

                    <!-- تقرير الديون -->
                    <div class="bg-white dark:bg-gray-800 rounded-xl shadow-md overflow-hidden">
                        <div class="bg-gray-50 dark:bg-gray-700 px-4 py-3 border-b border-gray-200 dark:border-gray-600">
                            <h3 class="text-lg font-medium text-gray-800 dark:text-white">تقرير الديون</h3>
                        </div>
                        <div class="p-4">
                            <div class="h-64">
                                <canvas id="debt-chart"></canvas>
                            </div>
                            <div class="mt-4 text-center">
                                <a href="#" class="text-blue-600 dark:text-blue-400 hover:underline">عرض التقرير الكامل</a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    {% include 'partials/footer.html' %}

    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script>
        // تبديل الوضع المظلم
        const darkModeToggle = document.getElementById('darkModeToggle');
        if (darkModeToggle) {
            darkModeToggle.addEventListener('click', function() {
                document.body.classList.toggle('dark');
                if (document.body.classList.contains('dark')) {
                    localStorage.setItem('darkMode', 'enabled');
                } else {
                    localStorage.setItem('darkMode', 'disabled');
                }
            });
        }

        // تحقق من حالة الوضع المظلم عند تحميل الصفحة
        if (localStorage.getItem('darkMode') === 'enabled') {
            document.body.classList.add('dark');
        }

        // وظيفة تبديل التبويبات
        function showTab(tabName) {
            // إخفاء جميع التبويبات
            document.querySelectorAll('.tab-pane').forEach(tab => {
                tab.classList.remove('active');
            });

            // إزالة الحالة النشطة من جميع أزرار التبويب
            document.querySelectorAll('.tab-button').forEach(button => {
                button.classList.remove('active');
            });

            // إظهار التبويب المحدد
            document.getElementById('content-' + tabName).classList.add('active');

            // تنشيط زر التبويب المحدد
            document.getElementById('tab-' + tabName).classList.add('active');

            // حفظ التبويب النشط في التخزين المحلي
            localStorage.setItem('activeCustomerTab', tabName);

            // تحميل البيانات للتبويب المحدد إذا لم يتم تحميلها بعد
            if (tabName === 'orders' && !window.ordersLoaded) {
                loadOrdersData();
            } else if (tabName === 'deferred' && !window.deferredLoaded) {
                loadDeferredData();
            } else if (tabName === 'payments' && !window.paymentsLoaded) {
                loadPaymentsData();
            } else if (tabName === 'returns' && !window.returnsLoaded) {
                loadReturnsData();
            } else if (tabName === 'products' && !window.productsLoaded) {
                loadProductsData();
            } else if (tabName === 'reports' && !window.reportsLoaded) {
                loadReportsData();
            }
        }

        // استعادة التبويب النشط من التخزين المحلي عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            const activeTab = localStorage.getItem('activeCustomerTab');
            if (activeTab) {
                showTab(activeTab);
            }

            // تأثيرات الحركة للبطاقات
            document.querySelectorAll('.card').forEach(card => {
                card.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-5px)';
                    this.style.boxShadow = '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)';
                });

                card.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0)';
                    this.style.boxShadow = '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)';
                });
            });

            // إعداد أحداث البحث والتصفية
            setupOrdersEvents();
            setupDeferredEvents();
            setupPaymentsEvents();
            setupReturnsEvents();
            setupProductsEvents();
        });

        // تحميل بيانات الطلبات
        function loadOrdersData() {
            const ordersLoading = document.getElementById('orders-loading');
            const ordersEmpty = document.getElementById('orders-empty');
            const ordersTableBody = document.getElementById('orders-table-body');

            if (ordersLoading) ordersLoading.classList.remove('hidden');
            if (ordersEmpty) ordersEmpty.classList.add('hidden');

            // جمع معلمات البحث والتصفية
            const search = document.getElementById('order-search')?.value || '';
            const dateFrom = document.getElementById('order-date-from')?.value || '';
            const dateTo = document.getElementById('order-date-to')?.value || '';
            const status = document.getElementById('order-status')?.value || '';

            // بناء عنوان URL مع معلمات البحث
            let url = `/api/customers/{{ customer.id }}/orders?`;
            if (search) url += `search=${encodeURIComponent(search)}&`;
            if (dateFrom) url += `date_from=${encodeURIComponent(dateFrom)}&`;
            if (dateTo) url += `date_to=${encodeURIComponent(dateTo)}&`;
            if (status) url += `status=${encodeURIComponent(status)}&`;

            // جلب البيانات من الخادم
            fetch(url)
                .then(response => response.json())
                .then(data => {
                    window.ordersLoaded = true;

                    if (ordersLoading) ordersLoading.classList.add('hidden');

                    if (!data.orders || data.orders.length === 0) {
                        if (ordersEmpty) ordersEmpty.classList.remove('hidden');
                        if (ordersTableBody) ordersTableBody.innerHTML = '';
                        return;
                    }

                    // عرض البيانات في الجدول
                    if (ordersTableBody) {
                        ordersTableBody.innerHTML = data.orders.map(order => `
                            <tr class="hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors duration-200">
                                <td class="px-4 py-3 text-sm font-medium text-gray-900 dark:text-white">
                                    ${order.invoice_number}
                                </td>
                                <td class="px-4 py-3 text-sm text-gray-500 dark:text-gray-300">
                                    ${order.created_at}
                                </td>
                                <td class="px-4 py-3 text-sm font-medium text-gray-900 dark:text-white">
                                    ${order.total.toFixed(2)} <span class="text-xs text-gray-500 dark:text-gray-400">ج.م</span>
                                </td>
                                <td class="px-4 py-3 text-sm font-medium text-green-600 dark:text-green-400">
                                    ${order.paid_amount.toFixed(2)} <span class="text-xs text-gray-500 dark:text-gray-400">ج.م</span>
                                </td>
                                <td class="px-4 py-3 text-sm font-medium text-red-600 dark:text-red-400">
                                    ${order.remaining_amount.toFixed(2)} <span class="text-xs text-gray-500 dark:text-gray-400">ج.م</span>
                                </td>
                                <td class="px-4 py-3">
                                    ${getPaymentMethodBadge(order.payment_method)}
                                </td>
                                <td class="px-4 py-3">
                                    ${getStatusBadge(order.status)}
                                </td>
                                <td class="px-4 py-3 text-sm">
                                    <div class="flex space-x-1 space-x-reverse">
                                        <a href="/sales/${order.id}/details" class="p-1.5 bg-blue-50 dark:bg-blue-900/20 text-primary dark:text-blue-400 rounded-lg hover:bg-blue-100 dark:hover:bg-blue-900/40 transition-colors" title="عرض التفاصيل">
                                            <i class="ri-eye-line"></i>
                                        </a>
                                        <a href="/pos/print/${order.id}" class="p-1.5 bg-green-50 dark:bg-green-900/20 text-green-600 dark:text-green-400 rounded-lg hover:bg-green-100 dark:hover:bg-green-900/40 transition-colors" target="_blank" title="طباعة الفاتورة">
                                            <i class="ri-printer-line"></i>
                                        </a>
                                    </div>
                                </td>
                            </tr>
                        `).join('');
                    }
                })
                .catch(error => {
                    console.error('Error loading orders data:', error);
                    if (ordersLoading) ordersLoading.classList.add('hidden');
                    if (ordersTableBody) ordersTableBody.innerHTML = `
                        <tr>
                            <td colspan="8" class="px-4 py-6 text-center text-red-500 dark:text-red-400">
                                <i class="ri-error-warning-line text-2xl mb-2"></i>
                                <p>حدث خطأ أثناء تحميل البيانات</p>
                            </td>
                        </tr>
                    `;
                });
        }

        // تحميل بيانات المدفوعات
        function loadPaymentsData() {
            const paymentsLoading = document.getElementById('payments-loading');
            const paymentsEmpty = document.getElementById('payments-empty');
            const paymentsTableBody = document.getElementById('payments-table-body');

            if (paymentsLoading) paymentsLoading.classList.remove('hidden');
            if (paymentsEmpty) paymentsEmpty.classList.add('hidden');

            // جمع معلمات البحث والتصفية
            const dateFrom = document.getElementById('payment-date-from')?.value || '';
            const dateTo = document.getElementById('payment-date-to')?.value || '';
            const paymentMethod = document.getElementById('payment-method')?.value || '';

            // بناء عنوان URL مع معلمات البحث
            let url = `/api/customers/{{ customer.id }}/payments?`;
            if (dateFrom) url += `date_from=${encodeURIComponent(dateFrom)}&`;
            if (dateTo) url += `date_to=${encodeURIComponent(dateTo)}&`;
            if (paymentMethod) url += `payment_method=${encodeURIComponent(paymentMethod)}&`;

            // جلب البيانات من الخادم
            fetch(url)
                .then(response => response.json())
                .then(data => {
                    window.paymentsLoaded = true;

                    if (paymentsLoading) paymentsLoading.classList.add('hidden');

                    if (!data.payments || data.payments.length === 0) {
                        if (paymentsEmpty) paymentsEmpty.classList.remove('hidden');
                        if (paymentsTableBody) paymentsTableBody.innerHTML = '';
                        return;
                    }

                    // عرض البيانات في الجدول
                    if (paymentsTableBody) {
                        paymentsTableBody.innerHTML = data.payments.map(payment => `
                            <tr class="hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors duration-200">
                                <td class="px-4 py-3 text-sm font-medium text-gray-900 dark:text-white">
                                    ${payment.reference_number}
                                </td>
                                <td class="px-4 py-3 text-sm text-gray-500 dark:text-gray-300">
                                    ${payment.order_invoice}
                                </td>
                                <td class="px-4 py-3 text-sm text-gray-500 dark:text-gray-300">
                                    ${payment.payment_date}
                                </td>
                                <td class="px-4 py-3 text-sm font-medium text-gray-900 dark:text-white">
                                    ${payment.amount.toFixed(2)} <span class="text-xs text-gray-500 dark:text-gray-400">ج.م</span>
                                </td>
                                <td class="px-4 py-3">
                                    ${getPaymentMethodBadge(payment.payment_method)}
                                </td>
                                <td class="px-4 py-3 text-sm text-gray-500 dark:text-gray-300">
                                    ${payment.notes || '-'}
                                </td>
                            </tr>
                        `).join('');
                    }
                })
                .catch(error => {
                    console.error('Error loading payments data:', error);
                    if (paymentsLoading) paymentsLoading.classList.add('hidden');
                    if (paymentsTableBody) paymentsTableBody.innerHTML = `
                        <tr>
                            <td colspan="6" class="px-4 py-6 text-center text-red-500 dark:text-red-400">
                                <i class="ri-error-warning-line text-2xl mb-2"></i>
                                <p>حدث خطأ أثناء تحميل البيانات</p>
                            </td>
                        </tr>
                    `;
                });
        }

        // تحميل بيانات المرتجعات
        function loadReturnsData() {
            const returnsLoading = document.getElementById('returns-loading');
            const returnsEmpty = document.getElementById('returns-empty');
            const returnsTableBody = document.getElementById('returns-table-body');

            if (returnsLoading) returnsLoading.classList.remove('hidden');
            if (returnsEmpty) returnsEmpty.classList.add('hidden');

            // جمع معلمات البحث والتصفية
            const dateFrom = document.getElementById('return-date-from')?.value || '';
            const dateTo = document.getElementById('return-date-to')?.value || '';

            // بناء عنوان URL مع معلمات البحث
            let url = `/api/customers/{{ customer.id }}/returns?`;
            if (dateFrom) url += `date_from=${encodeURIComponent(dateFrom)}&`;
            if (dateTo) url += `date_to=${encodeURIComponent(dateTo)}&`;

            // جلب البيانات من الخادم
            fetch(url)
                .then(response => response.json())
                .then(data => {
                    window.returnsLoaded = true;

                    if (returnsLoading) returnsLoading.classList.add('hidden');

                    if (!data.returns || data.returns.length === 0) {
                        if (returnsEmpty) returnsEmpty.classList.remove('hidden');
                        if (returnsTableBody) returnsTableBody.innerHTML = '';
                        return;
                    }

                    // عرض البيانات في الجدول
                    if (returnsTableBody) {
                        returnsTableBody.innerHTML = data.returns.map(returnOrder => `
                            <tr class="hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors duration-200">
                                <td class="px-4 py-3 text-sm font-medium text-gray-900 dark:text-white">
                                    ${returnOrder.reference_number}
                                </td>
                                <td class="px-4 py-3 text-sm text-gray-500 dark:text-gray-300">
                                    ${returnOrder.order_invoice}
                                </td>
                                <td class="px-4 py-3 text-sm text-gray-500 dark:text-gray-300">
                                    ${returnOrder.created_at}
                                </td>
                                <td class="px-4 py-3 text-sm font-medium text-gray-900 dark:text-white">
                                    ${returnOrder.total_amount.toFixed(2)} <span class="text-xs text-gray-500 dark:text-gray-400">ج.م</span>
                                </td>
                                <td class="px-4 py-3">
                                    ${getPaymentMethodBadge(returnOrder.payment_method)}
                                </td>
                                <td class="px-4 py-3 text-sm text-gray-500 dark:text-gray-300">
                                    ${returnOrder.items_count}
                                </td>
                                <td class="px-4 py-3 text-sm">
                                    <div class="flex space-x-1 space-x-reverse">
                                        <a href="/returns/${returnOrder.id}/details" class="p-1.5 bg-blue-50 dark:bg-blue-900/20 text-primary dark:text-blue-400 rounded-lg hover:bg-blue-100 dark:hover:bg-blue-900/40 transition-colors" title="عرض التفاصيل">
                                            <i class="ri-eye-line"></i>
                                        </a>
                                    </div>
                                </td>
                            </tr>
                        `).join('');
                    }
                })
                .catch(error => {
                    console.error('Error loading returns data:', error);
                    if (returnsLoading) returnsLoading.classList.add('hidden');
                    if (returnsTableBody) returnsTableBody.innerHTML = `
                        <tr>
                            <td colspan="7" class="px-4 py-6 text-center text-red-500 dark:text-red-400">
                                <i class="ri-error-warning-line text-2xl mb-2"></i>
                                <p>حدث خطأ أثناء تحميل البيانات</p>
                            </td>
                        </tr>
                    `;
                });
        }
        // تحميل بيانات المنتجات
        function loadProductsData() {
            const productsLoading = document.getElementById('products-loading');
            const productsEmpty = document.getElementById('products-empty');
            const productsTableBody = document.getElementById('products-table-body');

            if (productsLoading) productsLoading.classList.remove('hidden');
            if (productsEmpty) productsEmpty.classList.add('hidden');

            // جمع معلمات البحث
            const search = document.getElementById('product-search')?.value || '';

            // بناء عنوان URL مع معلمات البحث
            let url = `/api/customers/{{ customer.id }}/products?`;
            if (search) url += `search=${encodeURIComponent(search)}&`;

            // جلب البيانات من الخادم
            fetch(url)
                .then(response => response.json())
                .then(data => {
                    window.productsLoaded = true;

                    if (productsLoading) productsLoading.classList.add('hidden');

                    if (!data.products || data.products.length === 0) {
                        if (productsEmpty) productsEmpty.classList.remove('hidden');
                        if (productsTableBody) productsTableBody.innerHTML = '';
                        return;
                    }

                    // عرض البيانات في الجدول
                    if (productsTableBody) {
                        productsTableBody.innerHTML = data.products.map(product => `
                            <tr class="hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors duration-200">
                                <td class="px-4 py-3 text-sm font-medium text-gray-900 dark:text-white">
                                    ${product.name}
                                </td>
                                <td class="px-4 py-3 text-sm text-gray-500 dark:text-gray-300">
                                    ${product.code || '-'}
                                </td>
                                <td class="px-4 py-3 text-sm font-medium text-gray-900 dark:text-white">
                                    ${product.total_quantity}
                                </td>
                                <td class="px-4 py-3 text-sm text-gray-500 dark:text-gray-300">
                                    ${product.order_count}
                                </td>
                                <td class="px-4 py-3 text-sm text-gray-500 dark:text-gray-300">
                                    ${product.last_purchase_date || '-'}
                                </td>
                                <td class="px-4 py-3 text-sm font-medium text-gray-900 dark:text-white">
                                    ${product.price.toFixed(2)} <span class="text-xs text-gray-500 dark:text-gray-400">ج.م</span>
                                </td>
                            </tr>
                        `).join('');
                    }
                })
                .catch(error => {
                    console.error('Error loading products data:', error);
                    if (productsLoading) productsLoading.classList.add('hidden');
                    if (productsTableBody) productsTableBody.innerHTML = `
                        <tr>
                            <td colspan="6" class="px-4 py-6 text-center text-red-500 dark:text-red-400">
                                <i class="ri-error-warning-line text-2xl mb-2"></i>
                                <p>حدث خطأ أثناء تحميل البيانات</p>
                            </td>
                        </tr>
                    `;
                });
        }

        // تحميل بيانات التقارير
        function loadReportsData() {
            fetch(`/api/customers/{{ customer.id }}/reports`)
                .then(response => response.json())
                .then(data => {
                    window.reportsLoaded = true;

                    // تحميل بيانات المشتريات الشهرية
                    if (data.monthly_orders && data.monthly_orders.length > 0) {
                        renderMonthlyOrdersChart(data.monthly_orders);
                    }

                    // تحميل بيانات المنتجات الأكثر شراءً
                    if (data.top_products && data.top_products.length > 0) {
                        renderTopProductsChart(data.top_products);
                    }

                    // تحميل بيانات المدفوعات
                    if (data.payment_stats && data.payment_stats.length > 0) {
                        renderPaymentsChart(data.payment_stats);
                    }

                    // تحميل بيانات الديون
                    renderDebtChart();
                })
                .catch(error => {
                    console.error('Error loading reports data:', error);
                });
        }

        // رسم مخطط المشتريات الشهرية
        function renderMonthlyOrdersChart(data) {
            const ctx = document.getElementById('monthly-orders-chart');
            if (!ctx) return;

            const months = data.map(item => {
                const [year, month] = item.month.split('-');
                return `${month}/${year}`;
            });

            const values = data.map(item => item.total);

            const isDarkMode = document.body.classList.contains('dark');
            const textColor = isDarkMode ? '#E2E8F0' : '#1E293B';
            const gridColor = isDarkMode ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.1)';

            new Chart(ctx, {
                type: 'bar',
                data: {
                    labels: months,
                    datasets: [{
                        label: 'المشتريات الشهرية',
                        data: values,
                        backgroundColor: 'rgba(59, 130, 246, 0.5)',
                        borderColor: 'rgba(59, 130, 246, 1)',
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true,
                            ticks: {
                                color: textColor
                            },
                            grid: {
                                color: gridColor
                            }
                        },
                        x: {
                            ticks: {
                                color: textColor
                            },
                            grid: {
                                color: gridColor
                            }
                        }
                    },
                    plugins: {
                        legend: {
                            labels: {
                                color: textColor
                            }
                        }
                    }
                }
            });
        }

        // رسم مخطط المنتجات الأكثر شراءً
        function renderTopProductsChart(data) {
            const ctx = document.getElementById('top-products-chart');
            if (!ctx) return;

            const labels = data.map(item => item.name);
            const values = data.map(item => item.total_value);

            const isDarkMode = document.body.classList.contains('dark');
            const textColor = isDarkMode ? '#E2E8F0' : '#1E293B';

            new Chart(ctx, {
                type: 'pie',
                data: {
                    labels: labels,
                    datasets: [{
                        data: values,
                        backgroundColor: [
                            'rgba(255, 99, 132, 0.7)',
                            'rgba(54, 162, 235, 0.7)',
                            'rgba(255, 206, 86, 0.7)',
                            'rgba(75, 192, 192, 0.7)',
                            'rgba(153, 102, 255, 0.7)',
                            'rgba(255, 159, 64, 0.7)',
                            'rgba(199, 199, 199, 0.7)',
                            'rgba(83, 102, 255, 0.7)',
                            'rgba(40, 159, 64, 0.7)',
                            'rgba(210, 199, 199, 0.7)'
                        ],
                        borderColor: [
                            'rgba(255, 99, 132, 1)',
                            'rgba(54, 162, 235, 1)',
                            'rgba(255, 206, 86, 1)',
                            'rgba(75, 192, 192, 1)',
                            'rgba(153, 102, 255, 1)',
                            'rgba(255, 159, 64, 1)',
                            'rgba(199, 199, 199, 1)',
                            'rgba(83, 102, 255, 1)',
                            'rgba(40, 159, 64, 1)',
                            'rgba(210, 199, 199, 1)'
                        ],
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'right',
                            labels: {
                                color: textColor
                            }
                        }
                    }
                }
            });
        }

        // رسم مخطط المدفوعات
        function renderPaymentsChart(data) {
            const ctx = document.getElementById('payments-chart');
            if (!ctx) return;

            const months = data.map(item => {
                const [year, month] = item.month.split('-');
                return `${month}/${year}`;
            });

            const values = data.map(item => item.total);

            const isDarkMode = document.body.classList.contains('dark');
            const textColor = isDarkMode ? '#E2E8F0' : '#1E293B';
            const gridColor = isDarkMode ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.1)';

            new Chart(ctx, {
                type: 'line',
                data: {
                    labels: months,
                    datasets: [{
                        label: 'المدفوعات الشهرية',
                        data: values,
                        backgroundColor: 'rgba(16, 185, 129, 0.2)',
                        borderColor: 'rgba(16, 185, 129, 1)',
                        borderWidth: 2,
                        tension: 0.4,
                        fill: true
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true,
                            ticks: {
                                color: textColor
                            },
                            grid: {
                                color: gridColor
                            }
                        },
                        x: {
                            ticks: {
                                color: textColor
                            },
                            grid: {
                                color: gridColor
                            }
                        }
                    },
                    plugins: {
                        legend: {
                            labels: {
                                color: textColor
                            }
                        }
                    }
                }
            });
        }

        // رسم مخطط الديون
        function renderDebtChart() {
            const ctx = document.getElementById('debt-chart');
            if (!ctx) return;

            const totalSpent = {{ stats.total_spent }};
            const paidAmount = {{ stats.paid_amount }};
            const remainingAmount = {{ stats.remaining_amount }};

            const isDarkMode = document.body.classList.contains('dark');
            const textColor = isDarkMode ? '#E2E8F0' : '#1E293B';

            new Chart(ctx, {
                type: 'doughnut',
                data: {
                    labels: ['المدفوع', 'المتبقي'],
                    datasets: [{
                        data: [paidAmount, remainingAmount],
                        backgroundColor: [
                            'rgba(16, 185, 129, 0.7)',
                            'rgba(239, 68, 68, 0.7)'
                        ],
                        borderColor: [
                            'rgba(16, 185, 129, 1)',
                            'rgba(239, 68, 68, 1)'
                        ],
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'bottom',
                            labels: {
                                color: textColor
                            }
                        }
                    }
                }
            });
        }

        // إعداد أحداث البحث والتصفية للطلبات
        function setupOrdersEvents() {
            const orderSearchBtn = document.getElementById('order-search-btn');
            const orderFilterBtn = document.getElementById('order-filter-btn');

            if (orderSearchBtn) {
                orderSearchBtn.addEventListener('click', function() {
                    loadOrdersData();
                });
            }

            if (orderFilterBtn) {
                orderFilterBtn.addEventListener('click', function() {
                    loadOrdersData();
                });
            }

            // إضافة حدث الضغط على Enter في حقل البحث
            const orderSearch = document.getElementById('order-search');
            if (orderSearch) {
                orderSearch.addEventListener('keypress', function(e) {
                    if (e.key === 'Enter') {
                        loadOrdersData();
                    }
                });
            }
        }

        // إعداد أحداث البحث والتصفية للمبيعات الآجلة
        function setupDeferredEvents() {
            const deferredSearchBtn = document.getElementById('deferred-search-btn');
            const deferredFilterBtn = document.getElementById('deferred-filter-btn');

            if (deferredSearchBtn) {
                deferredSearchBtn.addEventListener('click', function() {
                    loadDeferredData();
                });
            }

            if (deferredFilterBtn) {
                deferredFilterBtn.addEventListener('click', function() {
                    loadDeferredData();
                });
            }

            // إضافة حدث الضغط على Enter في حقل البحث
            const deferredSearch = document.getElementById('deferred-search');
            if (deferredSearch) {
                deferredSearch.addEventListener('keypress', function(e) {
                    if (e.key === 'Enter') {
                        loadDeferredData();
                    }
                });
            }
        }

        // تحميل بيانات المبيعات الآجلة
        function loadDeferredData() {
            const deferredLoading = document.getElementById('deferred-loading');
            const deferredEmpty = document.getElementById('deferred-empty');
            const deferredTableBody = document.getElementById('deferred-table-body');

            if (deferredLoading) deferredLoading.classList.remove('hidden');
            if (deferredEmpty) deferredEmpty.classList.add('hidden');

            // جمع معلمات البحث والتصفية
            const search = document.getElementById('deferred-search')?.value || '';
            const dateFrom = document.getElementById('deferred-date-from')?.value || '';
            const dateTo = document.getElementById('deferred-date-to')?.value || '';

            // بناء عنوان URL مع معلمات البحث
            let url = `/api/customers/{{ customer.id }}/deferred_orders?`;
            if (search) url += `search=${encodeURIComponent(search)}&`;
            if (dateFrom) url += `date_from=${encodeURIComponent(dateFrom)}&`;
            if (dateTo) url += `date_to=${encodeURIComponent(dateTo)}&`;

            // جلب البيانات من الخادم
            fetch(url)
                .then(response => response.json())
                .then(data => {
                    window.deferredLoaded = true;

                    if (deferredLoading) deferredLoading.classList.add('hidden');

                    if (!data.orders || data.orders.length === 0) {
                        if (deferredEmpty) deferredEmpty.classList.remove('hidden');
                        if (deferredTableBody) deferredTableBody.innerHTML = '';
                        return;
                    }

                    // عرض البيانات في الجدول
                    if (deferredTableBody) {
                        deferredTableBody.innerHTML = data.orders.map(order => {
                            // حساب المبلغ المدفوع والمتبقي
                            const paidAmount = order.paid_amount || 0;
                            const remainingAmount = order.total - paidAmount;

                            return `
                            <tr class="hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors duration-200">
                                <td class="px-4 py-3 text-sm font-medium text-gray-900 dark:text-white">
                                    ${order.invoice_number}
                                </td>
                                <td class="px-4 py-3 text-sm text-gray-500 dark:text-gray-300">
                                    ${order.created_at}
                                </td>
                                <td class="px-4 py-3 text-sm font-medium text-gray-900 dark:text-white">
                                    ${order.total.toFixed(2)} <span class="text-xs text-gray-500 dark:text-gray-400">ج.م</span>
                                </td>
                                <td class="px-4 py-3 text-sm font-medium text-green-600 dark:text-green-400">
                                    ${paidAmount.toFixed(2)} <span class="text-xs text-gray-500 dark:text-gray-400">ج.م</span>
                                </td>
                                <td class="px-4 py-3 text-sm font-medium text-red-600 dark:text-red-400">
                                    ${remainingAmount.toFixed(2)} <span class="text-xs text-gray-500 dark:text-gray-400">ج.م</span>
                                </td>
                                <td class="px-4 py-3">
                                    ${getStatusBadge(order.status)}
                                </td>
                                <td class="px-4 py-3 text-sm">
                                    <div class="flex space-x-1 space-x-reverse">
                                        <a href="/sales/${order.id}/details" class="p-1.5 bg-blue-50 dark:bg-blue-900/20 text-primary dark:text-blue-400 rounded-lg hover:bg-blue-100 dark:hover:bg-blue-900/40 transition-colors" title="عرض التفاصيل">
                                            <i class="ri-eye-line"></i>
                                        </a>
                                        <a href="/customers/${order.customer_id}/add_payment?order_id=${order.id}" class="p-1.5 bg-green-50 dark:bg-green-900/20 text-green-600 dark:text-green-400 rounded-lg hover:bg-green-100 dark:hover:bg-green-900/40 transition-colors" title="إضافة دفعة">
                                            <i class="ri-bank-card-line"></i>
                                        </a>
                                        <a href="/pos/print/${order.id}" class="p-1.5 bg-purple-50 dark:bg-purple-900/20 text-purple-600 dark:text-purple-400 rounded-lg hover:bg-purple-100 dark:hover:bg-purple-900/40 transition-colors" target="_blank" title="طباعة الفاتورة">
                                            <i class="ri-printer-line"></i>
                                        </a>
                                    </div>
                                </td>
                            </tr>
                        `}).join('');
                    }
                })
                .catch(error => {
                    console.error('Error loading deferred orders data:', error);
                    if (deferredLoading) deferredLoading.classList.add('hidden');
                    if (deferredTableBody) deferredTableBody.innerHTML = `
                        <tr>
                            <td colspan="7" class="px-4 py-6 text-center text-red-500 dark:text-red-400">
                                <i class="ri-error-warning-line text-2xl mb-2"></i>
                                <p>حدث خطأ أثناء تحميل البيانات</p>
                            </td>
                        </tr>
                    `;
                });
        }

        // إعداد أحداث البحث والتصفية للمدفوعات
        function setupPaymentsEvents() {
            const paymentFilterBtn = document.getElementById('payment-filter-btn');

            if (paymentFilterBtn) {
                paymentFilterBtn.addEventListener('click', function() {
                    loadPaymentsData();
                });
            }
        }

        // إعداد أحداث البحث والتصفية للمرتجعات
        function setupReturnsEvents() {
            const returnFilterBtn = document.getElementById('return-filter-btn');

            if (returnFilterBtn) {
                returnFilterBtn.addEventListener('click', function() {
                    loadReturnsData();
                });
            }
        }

        // إعداد أحداث البحث والتصفية للمنتجات
        function setupProductsEvents() {
            const productSearchBtn = document.getElementById('product-search-btn');

            if (productSearchBtn) {
                productSearchBtn.addEventListener('click', function() {
                    loadProductsData();
                });
            }

            // إضافة حدث الضغط على Enter في حقل البحث
            const productSearch = document.getElementById('product-search');
            if (productSearch) {
                productSearch.addEventListener('keypress', function(e) {
                    if (e.key === 'Enter') {
                        loadProductsData();
                    }
                });
            }
        }

        // وظائف مساعدة لعرض الشارات
        function getPaymentMethodBadge(method) {
            switch (method) {
                case 'cash':
                    return `<span class="badge badge-green"><i class="ri-money-dollar-circle-line mr-1"></i> نقدي</span>`;
                case 'card':
                    return `<span class="badge badge-blue"><i class="ri-bank-card-line mr-1"></i> بطاقة</span>`;
                case 'bank_transfer':
                    return `<span class="badge badge-purple"><i class="ri-bank-line mr-1"></i> تحويل بنكي</span>`;
                case 'check':
                    return `<span class="badge badge-yellow"><i class="ri-bill-line mr-1"></i> شيك</span>`;
                case 'store_credit':
                    return `<span class="badge badge-blue"><i class="ri-refund-2-line mr-1"></i> رصيد متجر</span>`;
                default:
                    return `<span class="badge badge-gray">${method}</span>`;
            }
        }

        function getStatusBadge(status) {
            switch (status) {
                case 'completed':
                    return `<span class="badge badge-green"><i class="ri-check-line mr-1"></i> مكتمل</span>`;
                case 'cancelled':
                    return `<span class="badge badge-red"><i class="ri-close-line mr-1"></i> ملغي</span>`;
                case 'returned':
                    return `<span class="badge badge-yellow"><i class="ri-arrow-go-back-line mr-1"></i> مرتجع</span>`;
                case 'partially_returned':
                    return `<span class="badge badge-yellow"><i class="ri-arrow-go-back-line mr-1"></i> مرتجع جزئي</span>`;
                case 'deferred':
                    return `<span class="badge badge-purple"><i class="ri-time-line mr-1"></i> مؤجل</span>`;
                case 'pending':
                    return `<span class="badge badge-yellow"><i class="ri-time-line mr-1"></i> قيد الانتظار</span>`;
                default:
                    return `<span class="badge badge-gray">${status}</span>`;
            }
        }
    </script>