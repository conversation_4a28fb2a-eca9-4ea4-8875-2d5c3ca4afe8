<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>FoUaD - لوحة التقارير</title>
    <script src="https://cdn.tailwindcss.com/3.4.16"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#6366F1',
                        secondary: '#10b981',
                        success: '#22c55e',
                        warning: '#f59e0b',
                        danger: '#ef4444',
                        info: '#3b82f6'
                    },
                    borderRadius: {
                        'none': '0px',
                        'sm': '4px',
                        DEFAULT: '8px',
                        'md': '12px',
                        'lg': '16px',
                        'xl': '20px',
                        '2xl': '24px',
                        '3xl': '32px',
                        'full': '9999px'
                    }
                }
            }
        }
    </script>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@400;500;700&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Pacifico&display=swap" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/remixicon@3.5.0/fonts/remixicon.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        :where([class^="ri-"])::before { content: "\f3c2"; }
        body {
            font-family: 'Tajawal', sans-serif;
            background-color: #f9fafb;
        }
        .scrollbar-hide::-webkit-scrollbar {
            display: none;
        }
        .scrollbar-hide {
            -ms-overflow-style: none;
            scrollbar-width: none;
        }
        .bg-pattern {
            background-image: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%236366F1' fill-opacity='0.05'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
        }
        .glass-effect {
            backdrop-filter: blur(8px);
            background: rgba(255, 255, 255, 0.8);
            border: 1px solid rgba(255, 255, 255, 0.3);
            box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.07);
        }
        .gradient-border {
            position: relative;
            border-radius: 0.5rem;
            isolation: isolate;
        }
        .gradient-border::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            border-radius: inherit;
            padding: 2px;
            background: linear-gradient(to bottom right, #6366F1, #10b981);
            -webkit-mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
            -webkit-mask-composite: xor;
            mask-composite: exclude;
            z-index: -1;
        }
        .animated-card {
            transition: all 0.3s ease;
        }
        .animated-card:hover {
            transform: translateY(-5px);
        }
        .report-card {
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
            transition: all 0.3s ease;
            border-radius: 1rem;
        }
        .report-card:hover {
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
        }
    </style>
</head>
<body class="bg-pattern">
    <div class="flex h-screen overflow-hidden">
        <!-- Sidebar -->
        {% include 'partials/sidebar.html' %}
        
        <!-- Main Content -->
        <div class="flex-1 flex flex-col overflow-hidden">
            <!-- Top Navigation -->
            {% include 'partials/topnav.html' %}
            
            <!-- Content Area -->
            <main class="flex-1 overflow-y-auto p-6">
                <!-- Page Header -->
                <div class="mb-6 flex flex-wrap items-center justify-between">
                    <div>
                        <h1 class="text-2xl font-bold text-gray-800">لوحة التقارير</h1>
                        <p class="text-gray-600">استعراض البيانات التحليلية ومؤشرات الأداء الرئيسية</p>
                    </div>
                    <div class="flex gap-3">
                        <a href="{{ url_for('reports.sales_report') }}" class="bg-gray-100 text-gray-700 px-4 py-2.5 rounded-lg hover:bg-gray-200 transition-all duration-300 flex items-center gap-2 whitespace-nowrap">
                            <div class="w-5 h-5 flex items-center justify-center">
                                <i class="ri-bar-chart-line"></i>
                            </div>
                            <span>تقرير المبيعات</span>
                        </a>
                        <a href="{{ url_for('reports.inventory_report') }}" class="bg-gray-100 text-gray-700 px-4 py-2.5 rounded-lg hover:bg-gray-200 transition-all duration-300 flex items-center gap-2 whitespace-nowrap">
                            <div class="w-5 h-5 flex items-center justify-center">
                                <i class="ri-stack-line"></i>
                            </div>
                            <span>تقرير المخزون</span>
                        </a>
                    </div>
                </div>
                
                <!-- Summary Cards -->
                <div class="mb-6">
                    <h2 class="text-lg font-bold text-gray-800 mb-3">ملخص الأداء</h2>
                    <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                        <!-- Sales Card -->
                        <div class="bg-white p-5 rounded-xl shadow-sm animated-card report-card border border-gray-100 overflow-hidden relative">
                            <div class="absolute top-0 left-0 w-2 h-full bg-indigo-500"></div>
                            <div class="flex items-start justify-between">
                                <div>
                                    <h3 class="text-gray-500 text-sm font-medium">المبيعات</h3>
                                    <div class="mt-2">
                                        <span class="text-2xl font-bold text-gray-800">{{ "%.2f"|format(summary.sales.month) }} ج.م</span>
                                        <div class="flex items-center text-xs mt-1">
                                            <span class="text-gray-500">{{ date_ranges.month }}</span>
                                        </div>
                                    </div>
                                </div>
                                <div class="w-10 h-10 rounded-full bg-indigo-100 text-indigo-500 flex items-center justify-center">
                                    <i class="ri-line-chart-line"></i>
                                </div>
                            </div>
                            <div class="mt-4 flex justify-between">
                                <div>
                                    <span class="text-xs text-gray-500">اليوم</span>
                                    <div class="text-sm font-medium">{{ "%.2f"|format(summary.sales.today) }} ج.م</div>
                                </div>
                                <div>
                                    <span class="text-xs text-gray-500">هذا العام</span>
                                    <div class="text-sm font-medium">{{ "%.2f"|format(summary.sales.year) }} ج.م</div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Orders Card -->
                        <div class="bg-white p-5 rounded-xl shadow-sm animated-card report-card border border-gray-100 overflow-hidden relative">
                            <div class="absolute top-0 left-0 w-2 h-full bg-blue-500"></div>
                            <div class="flex items-start justify-between">
                                <div>
                                    <h3 class="text-gray-500 text-sm font-medium">الطلبات</h3>
                                    <div class="mt-2">
                                        <span class="text-2xl font-bold text-gray-800">{{ summary.orders.month }}</span>
                                        <div class="flex items-center text-xs mt-1">
                                            <span class="text-gray-500">{{ date_ranges.month }}</span>
                                        </div>
                                    </div>
                                </div>
                                <div class="w-10 h-10 rounded-full bg-blue-100 text-blue-500 flex items-center justify-center">
                                    <i class="ri-shopping-bag-3-line"></i>
                                </div>
                            </div>
                            <div class="mt-4 flex justify-between">
                                <div>
                                    <span class="text-xs text-gray-500">اليوم</span>
                                    <div class="text-sm font-medium">{{ summary.orders.today }}</div>
                                </div>
                                <div>
                                    <span class="text-xs text-gray-500">هذا العام</span>
                                    <div class="text-sm font-medium">{{ summary.orders.year }}</div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Profits Card -->
                        <div class="bg-white p-5 rounded-xl shadow-sm animated-card report-card border border-gray-100 overflow-hidden relative">
                            <div class="absolute top-0 left-0 w-2 h-full bg-green-500"></div>
                            <div class="flex items-start justify-between">
                                <div>
                                    <h3 class="text-gray-500 text-sm font-medium">الأرباح</h3>
                                    <div class="mt-2">
                                        <span class="text-2xl font-bold text-gray-800">{{ "%.2f"|format(summary.profit.month) }} ج.م</span>
                                        <div class="flex items-center text-xs mt-1">
                                            <span class="text-gray-500">{{ date_ranges.month }}</span>
                                        </div>
                                    </div>
                                </div>
                                <div class="w-10 h-10 rounded-full bg-green-100 text-green-500 flex items-center justify-center">
                                    <i class="ri-coins-line"></i>
                                </div>
                            </div>
                            <div class="mt-4 flex justify-between">
                                <div>
                                    <span class="text-xs text-gray-500">هامش الربح</span>
                                    <div class="text-sm font-medium">{{ "%.1f"|format(summary.profit.month_margin) }}%</div>
                                </div>
                                <div>
                                    <span class="text-xs text-gray-500">هذا العام</span>
                                    <div class="text-sm font-medium">{{ "%.2f"|format(summary.profit.year) }} ج.م</div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Purchases Card -->
                        <div class="bg-white p-5 rounded-xl shadow-sm animated-card report-card border border-gray-100 overflow-hidden relative">
                            <div class="absolute top-0 left-0 w-2 h-full bg-purple-500"></div>
                            <div class="flex items-start justify-between">
                                <div>
                                    <h3 class="text-gray-500 text-sm font-medium">المشتريات</h3>
                                    <div class="mt-2">
                                        <span class="text-2xl font-bold text-gray-800">{{ "%.2f"|format(summary.purchases.month) }} ج.م</span>
                                        <div class="flex items-center text-xs mt-1">
                                            <span class="text-gray-500">{{ date_ranges.month }}</span>
                                        </div>
                                    </div>
                                </div>
                                <div class="w-10 h-10 rounded-full bg-purple-100 text-purple-500 flex items-center justify-center">
                                    <i class="ri-shopping-cart-line"></i>
                                </div>
                            </div>
                            <div class="mt-4 flex justify-between">
                                <div>
                                    <span class="text-xs text-gray-500">اليوم</span>
                                    <div class="text-sm font-medium">{{ "%.2f"|format(summary.purchases.today) }} ج.م</div>
                                </div>
                                <div>
                                    <span class="text-xs text-gray-500">هذا العام</span>
                                    <div class="text-sm font-medium">{{ "%.2f"|format(summary.purchases.year) }} ج.م</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Charts -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                    <!-- Sales Chart -->
                    <div class="bg-white p-5 rounded-xl shadow-sm border border-gray-100 report-card">
                        <div class="flex justify-between items-center mb-4">
                            <h3 class="font-medium text-gray-800">المبيعات الشهرية</h3>
                            <div class="text-sm text-gray-500">{{ date_ranges.year }}</div>
                        </div>
                        <div class="h-60">
                            <canvas id="monthlySalesChart"></canvas>
                        </div>
                    </div>
                    
                    <!-- Category Distribution -->
                    <div class="bg-white p-5 rounded-xl shadow-sm border border-gray-100 report-card">
                        <div class="flex justify-between items-center mb-4">
                            <h3 class="font-medium text-gray-800">مبيعات حسب طريقة الدفع</h3>
                            <div class="text-sm text-gray-500">{{ date_ranges.month }}</div>
                        </div>
                        <div class="h-60">
                            <canvas id="paymentMethodChart"></canvas>
                        </div>
                    </div>
                </div>
                
                <!-- Product Data -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                    <!-- Top Products -->
                    <div class="bg-white p-5 rounded-xl shadow-sm border border-gray-100 report-card">
                        <h3 class="font-medium text-gray-800 mb-4">المنتجات الأكثر مبيعاً</h3>
                        <div class="overflow-x-auto">
                            <table class="min-w-full">
                                <thead>
                                    <tr class="border-b border-gray-200">
                                        <th class="text-right text-xs font-medium text-gray-500 py-2">المنتج</th>
                                        <th class="text-center text-xs font-medium text-gray-500 py-2">الكمية</th>
                                        <th class="text-left text-xs font-medium text-gray-500 py-2">الإيرادات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for product in report_data.top_products %}
                                    <tr class="border-b border-gray-100 hover:bg-gray-50">
                                        <td class="py-2 text-sm font-medium text-gray-900">{{ product[0] }}</td>
                                        <td class="py-2 text-sm text-center text-gray-700">{{ product[1] }}</td>
                                        <td class="py-2 text-sm text-left text-gray-700">{{ "%.2f"|format(product[2]) }} ج.م</td>
                                    </tr>
                                    {% else %}
                                    <tr>
                                        <td colspan="3" class="py-4 text-center text-sm text-gray-500">لا توجد بيانات</td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    </div>
                    
                    <!-- Top Categories -->
                    <div class="bg-white p-5 rounded-xl shadow-sm border border-gray-100 report-card">
                        <h3 class="font-medium text-gray-800 mb-4">أفضل التصنيفات</h3>
                        <div class="overflow-x-auto">
                            <table class="min-w-full">
                                <thead>
                                    <tr class="border-b border-gray-200">
                                        <th class="text-right text-xs font-medium text-gray-500 py-2">التصنيف</th>
                                        <th class="text-center text-xs font-medium text-gray-500 py-2">الكمية</th>
                                        <th class="text-left text-xs font-medium text-gray-500 py-2">الإيرادات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for category in report_data.top_categories %}
                                    <tr class="border-b border-gray-100 hover:bg-gray-50">
                                        <td class="py-2 text-sm font-medium text-gray-900">{{ category[0] }}</td>
                                        <td class="py-2 text-sm text-center text-gray-700">{{ category[1] }}</td>
                                        <td class="py-2 text-sm text-left text-gray-700">{{ "%.2f"|format(category[2]) }} ج.م</td>
                                    </tr>
                                    {% else %}
                                    <tr>
                                        <td colspan="3" class="py-4 text-center text-sm text-gray-500">لا توجد بيانات</td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
                
                <!-- Inventory and Customers -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <!-- Low Stock -->
                    <div class="bg-white p-5 rounded-xl shadow-sm border border-gray-100 report-card">
                        <h3 class="font-medium text-gray-800 mb-4">منتجات منخفضة المخزون</h3>
                        <div class="overflow-x-auto">
                            <table class="min-w-full">
                                <thead>
                                    <tr class="border-b border-gray-200">
                                        <th class="text-right text-xs font-medium text-gray-500 py-2">المنتج</th>
                                        <th class="text-center text-xs font-medium text-gray-500 py-2">الكمية المتاحة</th>
                                        <th class="text-left text-xs font-medium text-gray-500 py-2">الحد الأدنى</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for product in report_data.low_stock_products %}
                                    <tr class="border-b border-gray-100 hover:bg-gray-50">
                                        <td class="py-2 text-sm font-medium text-gray-900">{{ product.name }}</td>
                                        <td class="py-2 text-sm text-center">
                                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium {{ 'bg-red-100 text-red-800' if product.stock_quantity <= product.minimum_stock else 'bg-green-100 text-green-800' }}">
                                                {{ product.stock_quantity }}
                                            </span>
                                        </td>
                                        <td class="py-2 text-sm text-left text-gray-700">{{ product.minimum_stock }}</td>
                                    </tr>
                                    {% else %}
                                    <tr>
                                        <td colspan="3" class="py-4 text-center text-sm text-gray-500">لا توجد منتجات منخفضة المخزون</td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    </div>
                    
                    <!-- Top Customers -->
                    <div class="bg-white p-5 rounded-xl shadow-sm border border-gray-100 report-card">
                        <h3 class="font-medium text-gray-800 mb-4">أفضل العملاء</h3>
                        <div class="overflow-x-auto">
                            <table class="min-w-full">
                                <thead>
                                    <tr class="border-b border-gray-200">
                                        <th class="text-right text-xs font-medium text-gray-500 py-2">العميل</th>
                                        <th class="text-center text-xs font-medium text-gray-500 py-2">عدد الطلبات</th>
                                        <th class="text-left text-xs font-medium text-gray-500 py-2">إجمالي المشتريات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for customer in report_data.top_customers %}
                                    <tr class="border-b border-gray-100 hover:bg-gray-50">
                                        <td class="py-2 text-sm font-medium text-gray-900">{{ customer[0] }}</td>
                                        <td class="py-2 text-sm text-center text-gray-700">{{ customer[1] }}</td>
                                        <td class="py-2 text-sm text-left text-gray-700">{{ "%.2f"|format(customer[2]) }} ج.م</td>
                                    </tr>
                                    {% else %}
                                    <tr>
                                        <td colspan="3" class="py-4 text-center text-sm text-gray-500">لا توجد بيانات</td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>
    
    <script>
        // Monthly Sales Chart
        fetch('/sales/monthly-chart')
            .then(response => response.json())
            .then(data => {
                const ctx = document.getElementById('monthlySalesChart').getContext('2d');
                new Chart(ctx, {
                    type: 'bar',
                    data: {
                        labels: data.months,
                        datasets: [{
                            label: 'المبيعات الشهرية',
                            data: data.sales,
                            backgroundColor: 'rgba(99, 102, 241, 0.5)',
                            borderColor: 'rgba(99, 102, 241, 1)',
                            borderWidth: 1
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        scales: {
                            y: {
                                beginAtZero: true,
                                ticks: {
                                    callback: function(value) {
                                        return value.toLocaleString() + ' ج.م';
                                    }
                                }
                            }
                        },
                        plugins: {
                            legend: {
                                display: false
                            },
                            tooltip: {
                                callbacks: {
                                    label: function(context) {
                                        return context.raw.toLocaleString() + ' ج.م';
                                    }
                                }
                            }
                        }
                    }
                });
            })
            .catch(error => console.error('Error loading monthly sales chart:', error));
        
        // Payment Method Chart
        fetch('/sales/payment-method-chart')
            .then(response => response.json())
            .then(data => {
                const ctx = document.getElementById('paymentMethodChart').getContext('2d');
                new Chart(ctx, {
                    type: 'doughnut',
                    data: {
                        labels: data.labels.map(label => label === 'cash' ? 'كاش' : label === 'card' ? 'بطاقة' : label),
                        datasets: [{
                            data: data.data,
                            backgroundColor: [
                                'rgba(16, 185, 129, 0.7)',  // green
                                'rgba(59, 130, 246, 0.7)',  // blue
                                'rgba(249, 115, 22, 0.7)',  // orange
                                'rgba(139, 92, 246, 0.7)'   // purple
                            ],
                            borderColor: [
                                'rgba(16, 185, 129, 1)',
                                'rgba(59, 130, 246, 1)',
                                'rgba(249, 115, 22, 1)',
                                'rgba(139, 92, 246, 1)'
                            ],
                            borderWidth: 1
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: {
                                position: 'right'
                            },
                            tooltip: {
                                callbacks: {
                                    label: function(context) {
                                        const label = context.label || '';
                                        const value = context.raw || 0;
                                        const total = context.dataset.data.reduce((acc, val) => acc + val, 0);
                                        const percentage = Math.round((value / total) * 100);
                                        return `${label}: ${value.toLocaleString()} ج.م (${percentage}%)`;
                                    }
                                }
                            }
                        }
                    }
                });
            })
            .catch(error => console.error('Error loading payment method chart:', error));
    </script>
</body>
</html>