# تم تنظيف السجل في: 2025-05-24 19:53:16
2025-05-24 19:58:22 - ERROR - nobara - error_logger:190 - خطأ في جانب العميل: Uncaught SyntaxError: Identifier 'DashboardManager' has already been declared
معلومات إضافية: {
  "source": "client",
  "client_stack": "SyntaxError: Identifier 'DashboardManager' has already been declared",
  "url": "http://************:5000/home",
  "timestamp": "2025-05-24T16:58:21.640Z",
  "browser": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********",
  "os": "Win32",
  "component": "home",
  "user_id": 1,
  "username": "admin",
  "client_ip": "************",
  "request_info": {
    "url": "http://************:5000/api/log-error",
    "base_url": "http://************:5000/api/log-error",
    "path": "/api/log-error",
    "method": "POST",
    "endpoint": "errors_api_blueprint.log_client_error",
    "remote_addr": "************",
    "user_agent": null,
    "referrer": "http://************:5000/home",
    "content_type": "application/json",
    "content_length": 436,
    "is_xhr": false,
    "is_secure": false,
    "timestamp": "2025-05-24 19:58:22",
    "headers": {
      "Host": "************:5000",
      "Connection": "keep-alive",
      "Content-Length": "436",
      "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********",
      "Content-Type": "application/json",
      "Accept": "*/*",
      "Origin": "http://************:5000",
      "Referer": "http://************:5000/home",
      "Accept-Encoding": "gzip, deflate",
      "Accept-Language": "en-US,en;q=0.9",
      "Cookie": "NEXT_LOCALE=en; session=.eJwljjkOg0AMAP_iOgXeZX3wGWR8KGkhVFH-HiKk6UYjzQfW2vN4wvLez3zA-gpYoIUJa8xWlUzKKK6utnFFKAt3n-Vi7m38bbXWg7bJEVWtUUrVlRUOVdx0cHRKVJPm0yROaMboETg6uZaTqURzpogMsuxwjZxH7vcNwvcHu5gvhg.aDHu2A.A3-CejEWJdQfEhVvRf38q1XW2LQ"
    },
    "args": {},
    "form": {},
    "cookies": {
      "NEXT_LOCALE": "en",
      "session": ".eJwljjkOg0AMAP_iOgXeZX3wGWR8KGkhVFH-HiKk6UYjzQfW2vN4wvLez3zA-gpYoIUJa8xWlUzKKK6utnFFKAt3n-Vi7m38bbXWg7bJEVWtUUrVlRUOVdx0cHRKVJPm0yROaMboETg6uZaTqURzpogMsuxwjZxH7vcNwvcHu5gvhg.aDHu2A.A3-CejEWJdQfEhVvRf38q1XW2LQ"
    }
  }
}
2025-05-24 19:58:26 - ERROR - nobara - error_logger:190 - خطأ في جانب العميل: Uncaught SyntaxError: Identifier 'DashboardManager' has already been declared
معلومات إضافية: {
  "source": "client",
  "client_stack": "SyntaxError: Identifier 'DashboardManager' has already been declared",
  "url": "http://************:5000/home",
  "timestamp": "2025-05-24T16:58:25.909Z",
  "browser": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********",
  "os": "Win32",
  "component": "home",
  "user_id": 1,
  "username": "admin",
  "client_ip": "************",
  "request_info": {
    "url": "http://************:5000/api/log-error",
    "base_url": "http://************:5000/api/log-error",
    "path": "/api/log-error",
    "method": "POST",
    "endpoint": "errors_api_blueprint.log_client_error",
    "remote_addr": "************",
    "user_agent": null,
    "referrer": "http://************:5000/home",
    "content_type": "application/json",
    "content_length": 436,
    "is_xhr": false,
    "is_secure": false,
    "timestamp": "2025-05-24 19:58:26",
    "headers": {
      "Host": "************:5000",
      "Connection": "keep-alive",
      "Content-Length": "436",
      "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********",
      "Content-Type": "application/json",
      "Accept": "*/*",
      "Origin": "http://************:5000",
      "Referer": "http://************:5000/home",
      "Accept-Encoding": "gzip, deflate",
      "Accept-Language": "en-US,en;q=0.9",
      "Cookie": "NEXT_LOCALE=en; session=.eJwljjkOg0AMAP_iOgXeZX3wGWR8KGkhVFH-HiKk6UYjzQfW2vN4wvLez3zA-gpYoIUJa8xWlUzKKK6utnFFKAt3n-Vi7m38bbXWg7bJEVWtUUrVlRUOVdx0cHRKVJPm0yROaMboETg6uZaTqURzpogMsuxwjZxH7vcNwvcHu5gvhg.aDHu2A.A3-CejEWJdQfEhVvRf38q1XW2LQ"
    },
    "args": {},
    "form": {},
    "cookies": {
      "NEXT_LOCALE": "en",
      "session": ".eJwljjkOg0AMAP_iOgXeZX3wGWR8KGkhVFH-HiKk6UYjzQfW2vN4wvLez3zA-gpYoIUJa8xWlUzKKK6utnFFKAt3n-Vi7m38bbXWg7bJEVWtUUrVlRUOVdx0cHRKVJPm0yROaMboETg6uZaTqURzpogMsuxwjZxH7vcNwvcHu5gvhg.aDHu2A.A3-CejEWJdQfEhVvRf38q1XW2LQ"
    }
  }
}
2025-05-24 21:47:52 - ERROR - nobara - error_logger:270 - استثناء: TemplateNotFound: warehouses/warehouses.html
الوقت: 2025-05-24 21:47:52
نوع الاستثناء: TemplateNotFound
الملف: app.py
السطر: 809
الدالة: handle_user_exception
معلومات الطلب: {
  "url": "http://************:5000/warehouses",
  "base_url": "http://************:5000/warehouses",
  "path": "/warehouses",
  "method": "GET",
  "endpoint": "warehouses.index",
  "remote_addr": "************",
  "user_agent": null,
  "referrer": "http://************:5000/reports",
  "content_type": null,
  "content_length": null,
  "is_xhr": false,
  "is_secure": false,
  "timestamp": "2025-05-24 21:47:51",
  "headers": {
    "Host": "************:5000",
    "Connection": "keep-alive",
    "Upgrade-Insecure-Requests": "1",
    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********",
    "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7",
    "Referer": "http://************:5000/reports",
    "Accept-Encoding": "gzip, deflate",
    "Accept-Language": "en-US,en;q=0.9",
    "Cookie": "NEXT_LOCALE=en; session=.eJwljjkOg0AMAP_iOgXeZX3wGWR8KGkhVFH-HiKk6UYjzQfW2vN4wvLez3zA-gpYoIUJa8xWlUzKKK6utnFFKAt3n-Vi7m38bbXWg7bJEVWtUUrVlRUOVdx0cHRKVJPm0yROaMboETg6uZaTqURzpogMsuxwjZxH7vcNwvcHu5gvhg.aDHu2A.A3-CejEWJdQfEhVvRf38q1XW2LQ"
  },
  "args": {},
  "form": {},
  "cookies": {
    "NEXT_LOCALE": "en",
    "session": ".eJwljjkOg0AMAP_iOgXeZX3wGWR8KGkhVFH-HiKk6UYjzQfW2vN4wvLez3zA-gpYoIUJa8xWlUzKKK6utnFFKAt3n-Vi7m38bbXWg7bJEVWtUUrVlRUOVdx0cHRKVJPm0yROaMboETg6uZaTqURzpogMsuxwjZxH7vcNwvcHu5gvhg.aDHu2A.A3-CejEWJdQfEhVvRf38q1XW2LQ"
  }
}
معلومات النظام: {
  "memory_usage": "93.78 MB",
  "cpu_percent": "0.0%",
  "memory_percent": "80.5%",
  "disk_usage": "46.7%",
  "open_files": 11,
  "threads": 5,
  "connections": 0,
  "uptime": "1:49:49"
}
تتبع الاستثناء:
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\app.py", line 917, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\app.py", line 902, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)  # type: ignore[no-any-return]
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask_login\utils.py", line 290, in decorated_view
    return current_app.ensure_sync(func)(*args, **kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\routes\warehouses.py", line 45, in index
    return render_template('warehouses/warehouses.html',
                          warehouses=warehouses,
    ...<2 lines>...
                          status=status,
                          stats=stats)
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\templating.py", line 149, in render_template
    template = app.jinja_env.get_or_select_template(template_name_or_list)
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\jinja2\environment.py", line 1087, in get_or_select_template
    return self.get_template(template_name_or_list, parent, globals)
           ~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\jinja2\environment.py", line 1016, in get_template
    return self._load_template(name, globals)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\jinja2\environment.py", line 975, in _load_template
    template = self.loader.load(self, name, self.make_globals(globals))
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\jinja2\loaders.py", line 126, in load
    source, filename, uptodate = self.get_source(environment, name)
                                 ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\templating.py", line 65, in get_source
    return self._get_source_fast(environment, template)
           ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\templating.py", line 99, in _get_source_fast
    raise TemplateNotFound(template)
jinja2.exceptions.TemplateNotFound: warehouses/warehouses.html

2025-05-24 21:48:03 - ERROR - nobara - error_logger:270 - استثناء: TemplateNotFound: warehouses/warehouses.html
الوقت: 2025-05-24 21:48:03
نوع الاستثناء: TemplateNotFound
الملف: app.py
السطر: 809
الدالة: handle_user_exception
معلومات الطلب: {
  "url": "http://************:5000/warehouses",
  "base_url": "http://************:5000/warehouses",
  "path": "/warehouses",
  "method": "GET",
  "endpoint": "warehouses.index",
  "remote_addr": "************",
  "user_agent": null,
  "referrer": "http://************:5000/reports",
  "content_type": null,
  "content_length": null,
  "is_xhr": false,
  "is_secure": false,
  "timestamp": "2025-05-24 21:48:03",
  "headers": {
    "Host": "************:5000",
    "Connection": "keep-alive",
    "Upgrade-Insecure-Requests": "1",
    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********",
    "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7",
    "Referer": "http://************:5000/reports",
    "Accept-Encoding": "gzip, deflate",
    "Accept-Language": "en-US,en;q=0.9",
    "Cookie": "NEXT_LOCALE=en; session=.eJwljjkOg0AMAP_iOgXeZX3wGWR8KGkhVFH-HiKk6UYjzQfW2vN4wvLez3zA-gpYoIUJa8xWlUzKKK6utnFFKAt3n-Vi7m38bbXWg7bJEVWtUUrVlRUOVdx0cHRKVJPm0yROaMboETg6uZaTqURzpogMsuxwjZxH7vcNwvcHu5gvhg.aDHu2A.A3-CejEWJdQfEhVvRf38q1XW2LQ"
  },
  "args": {},
  "form": {},
  "cookies": {
    "NEXT_LOCALE": "en",
    "session": ".eJwljjkOg0AMAP_iOgXeZX3wGWR8KGkhVFH-HiKk6UYjzQfW2vN4wvLez3zA-gpYoIUJa8xWlUzKKK6utnFFKAt3n-Vi7m38bbXWg7bJEVWtUUrVlRUOVdx0cHRKVJPm0yROaMboETg6uZaTqURzpogMsuxwjZxH7vcNwvcHu5gvhg.aDHu2A.A3-CejEWJdQfEhVvRf38q1XW2LQ"
  }
}
معلومات النظام: {
  "memory_usage": "94.38 MB",
  "cpu_percent": "0.0%",
  "memory_percent": "80.8%",
  "disk_usage": "46.7%",
  "open_files": 11,
  "threads": 5,
  "connections": 0,
  "uptime": "1:50:00"
}
تتبع الاستثناء:
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\app.py", line 917, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\app.py", line 902, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)  # type: ignore[no-any-return]
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask_login\utils.py", line 290, in decorated_view
    return current_app.ensure_sync(func)(*args, **kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\routes\warehouses.py", line 45, in index
    return render_template('warehouses/warehouses.html',
                          warehouses=warehouses,
    ...<2 lines>...
                          status=status,
                          stats=stats)
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\templating.py", line 149, in render_template
    template = app.jinja_env.get_or_select_template(template_name_or_list)
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\jinja2\environment.py", line 1087, in get_or_select_template
    return self.get_template(template_name_or_list, parent, globals)
           ~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\jinja2\environment.py", line 1016, in get_template
    return self._load_template(name, globals)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\jinja2\environment.py", line 975, in _load_template
    template = self.loader.load(self, name, self.make_globals(globals))
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\jinja2\loaders.py", line 126, in load
    source, filename, uptodate = self.get_source(environment, name)
                                 ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\templating.py", line 65, in get_source
    return self._get_source_fast(environment, template)
           ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\templating.py", line 99, in _get_source_fast
    raise TemplateNotFound(template)
jinja2.exceptions.TemplateNotFound: warehouses/warehouses.html

2025-05-24 21:48:26 - ERROR - nobara - error_logger:270 - استثناء: TemplateNotFound: user_form.html
الوقت: 2025-05-24 21:48:26
نوع الاستثناء: TemplateNotFound
الملف: app.py
السطر: 809
الدالة: handle_user_exception
معلومات الطلب: {
  "url": "http://************:5000/users/1/edit",
  "base_url": "http://************:5000/users/1/edit",
  "path": "/users/1/edit",
  "method": "GET",
  "endpoint": "users.edit",
  "remote_addr": "************",
  "user_agent": null,
  "referrer": "http://************:5000/users",
  "content_type": null,
  "content_length": null,
  "is_xhr": false,
  "is_secure": false,
  "timestamp": "2025-05-24 21:48:25",
  "headers": {
    "Host": "************:5000",
    "Connection": "keep-alive",
    "Upgrade-Insecure-Requests": "1",
    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********",
    "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7",
    "Referer": "http://************:5000/users",
    "Accept-Encoding": "gzip, deflate",
    "Accept-Language": "en-US,en;q=0.9",
    "Cookie": "NEXT_LOCALE=en; session=.eJwljjkOg0AMAP_iOgXeZX3wGWR8KGkhVFH-HiKk6UYjzQfW2vN4wvLez3zA-gpYoIUJa8xWlUzKKK6utnFFKAt3n-Vi7m38bbXWg7bJEVWtUUrVlRUOVdx0cHRKVJPm0yROaMboETg6uZaTqURzpogMsuxwjZxH7vcNwvcHu5gvhg.aDHu2A.A3-CejEWJdQfEhVvRf38q1XW2LQ"
  },
  "args": {},
  "form": {},
  "cookies": {
    "NEXT_LOCALE": "en",
    "session": ".eJwljjkOg0AMAP_iOgXeZX3wGWR8KGkhVFH-HiKk6UYjzQfW2vN4wvLez3zA-gpYoIUJa8xWlUzKKK6utnFFKAt3n-Vi7m38bbXWg7bJEVWtUUrVlRUOVdx0cHRKVJPm0yROaMboETg6uZaTqURzpogMsuxwjZxH7vcNwvcHu5gvhg.aDHu2A.A3-CejEWJdQfEhVvRf38q1XW2LQ"
  }
}
معلومات النظام: {
  "memory_usage": "94.91 MB",
  "cpu_percent": "0.0%",
  "memory_percent": "81.1%",
  "disk_usage": "46.7%",
  "open_files": 11,
  "threads": 5,
  "connections": 0,
  "uptime": "1:50:22"
}
تتبع الاستثناء:
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\app.py", line 917, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\app.py", line 902, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)  # type: ignore[no-any-return]
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask_login\utils.py", line 290, in decorated_view
    return current_app.ensure_sync(func)(*args, **kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\routes\users.py", line 222, in edit
    return render_template('user_form.html', user=user, action='edit')
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\templating.py", line 149, in render_template
    template = app.jinja_env.get_or_select_template(template_name_or_list)
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\jinja2\environment.py", line 1087, in get_or_select_template
    return self.get_template(template_name_or_list, parent, globals)
           ~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\jinja2\environment.py", line 1016, in get_template
    return self._load_template(name, globals)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\jinja2\environment.py", line 975, in _load_template
    template = self.loader.load(self, name, self.make_globals(globals))
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\jinja2\loaders.py", line 126, in load
    source, filename, uptodate = self.get_source(environment, name)
                                 ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\templating.py", line 65, in get_source
    return self._get_source_fast(environment, template)
           ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\templating.py", line 99, in _get_source_fast
    raise TemplateNotFound(template)
jinja2.exceptions.TemplateNotFound: user_form.html

2025-05-24 21:48:31 - ERROR - nobara - error_logger:270 - استثناء: TemplateNotFound: user_permissions.html
الوقت: 2025-05-24 21:48:31
نوع الاستثناء: TemplateNotFound
الملف: app.py
السطر: 809
الدالة: handle_user_exception
معلومات الطلب: {
  "url": "http://************:5000/users/1/permissions",
  "base_url": "http://************:5000/users/1/permissions",
  "path": "/users/1/permissions",
  "method": "GET",
  "endpoint": "users.user_permissions",
  "remote_addr": "************",
  "user_agent": null,
  "referrer": "http://************:5000/users",
  "content_type": null,
  "content_length": null,
  "is_xhr": false,
  "is_secure": false,
  "timestamp": "2025-05-24 21:48:31",
  "headers": {
    "Host": "************:5000",
    "Connection": "keep-alive",
    "Upgrade-Insecure-Requests": "1",
    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********",
    "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7",
    "Referer": "http://************:5000/users",
    "Accept-Encoding": "gzip, deflate",
    "Accept-Language": "en-US,en;q=0.9",
    "Cookie": "NEXT_LOCALE=en; session=.eJwljjkOg0AMAP_iOgXeZX3wGWR8KGkhVFH-HiKk6UYjzQfW2vN4wvLez3zA-gpYoIUJa8xWlUzKKK6utnFFKAt3n-Vi7m38bbXWg7bJEVWtUUrVlRUOVdx0cHRKVJPm0yROaMboETg6uZaTqURzpogMsuxwjZxH7vcNwvcHu5gvhg.aDHu2A.A3-CejEWJdQfEhVvRf38q1XW2LQ"
  },
  "args": {},
  "form": {},
  "cookies": {
    "NEXT_LOCALE": "en",
    "session": ".eJwljjkOg0AMAP_iOgXeZX3wGWR8KGkhVFH-HiKk6UYjzQfW2vN4wvLez3zA-gpYoIUJa8xWlUzKKK6utnFFKAt3n-Vi7m38bbXWg7bJEVWtUUrVlRUOVdx0cHRKVJPm0yROaMboETg6uZaTqURzpogMsuxwjZxH7vcNwvcHu5gvhg.aDHu2A.A3-CejEWJdQfEhVvRf38q1XW2LQ"
  }
}
معلومات النظام: {
  "memory_usage": "96.42 MB",
  "cpu_percent": "0.0%",
  "memory_percent": "81.1%",
  "disk_usage": "46.7%",
  "open_files": 11,
  "threads": 5,
  "connections": 0,
  "uptime": "1:50:28"
}
تتبع الاستثناء:
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\app.py", line 917, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\app.py", line 902, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)  # type: ignore[no-any-return]
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask_login\utils.py", line 290, in decorated_view
    return current_app.ensure_sync(func)(*args, **kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\routes\users.py", line 316, in user_permissions
    return render_template(
        'user_permissions.html',
    ...<3 lines>...
        current_user=current_user
    )
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\templating.py", line 149, in render_template
    template = app.jinja_env.get_or_select_template(template_name_or_list)
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\jinja2\environment.py", line 1087, in get_or_select_template
    return self.get_template(template_name_or_list, parent, globals)
           ~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\jinja2\environment.py", line 1016, in get_template
    return self._load_template(name, globals)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\jinja2\environment.py", line 975, in _load_template
    template = self.loader.load(self, name, self.make_globals(globals))
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\jinja2\loaders.py", line 126, in load
    source, filename, uptodate = self.get_source(environment, name)
                                 ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\templating.py", line 65, in get_source
    return self._get_source_fast(environment, template)
           ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\templating.py", line 99, in _get_source_fast
    raise TemplateNotFound(template)
jinja2.exceptions.TemplateNotFound: user_permissions.html

2025-05-24 21:48:46 - ERROR - nobara - error_logger:270 - استثناء: TemplateNotFound: user_form.html
الوقت: 2025-05-24 21:48:46
نوع الاستثناء: TemplateNotFound
الملف: app.py
السطر: 809
الدالة: handle_user_exception
معلومات الطلب: {
  "url": "http://************:5000/users/create",
  "base_url": "http://************:5000/users/create",
  "path": "/users/create",
  "method": "GET",
  "endpoint": "users.create",
  "remote_addr": "************",
  "user_agent": null,
  "referrer": "http://************:5000/users",
  "content_type": null,
  "content_length": null,
  "is_xhr": false,
  "is_secure": false,
  "timestamp": "2025-05-24 21:48:46",
  "headers": {
    "Host": "************:5000",
    "Connection": "keep-alive",
    "Upgrade-Insecure-Requests": "1",
    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********",
    "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7",
    "Referer": "http://************:5000/users",
    "Accept-Encoding": "gzip, deflate",
    "Accept-Language": "en-US,en;q=0.9",
    "Cookie": "NEXT_LOCALE=en; session=.eJwljjkOg0AMAP_iOgXeZX3wGWR8KGkhVFH-HiKk6UYjzQfW2vN4wvLez3zA-gpYoIUJa8xWlUzKKK6utnFFKAt3n-Vi7m38bbXWg7bJEVWtUUrVlRUOVdx0cHRKVJPm0yROaMboETg6uZaTqURzpogMsuxwjZxH7vcNwvcHu5gvhg.aDHu2A.A3-CejEWJdQfEhVvRf38q1XW2LQ"
  },
  "args": {},
  "form": {},
  "cookies": {
    "NEXT_LOCALE": "en",
    "session": ".eJwljjkOg0AMAP_iOgXeZX3wGWR8KGkhVFH-HiKk6UYjzQfW2vN4wvLez3zA-gpYoIUJa8xWlUzKKK6utnFFKAt3n-Vi7m38bbXWg7bJEVWtUUrVlRUOVdx0cHRKVJPm0yROaMboETg6uZaTqURzpogMsuxwjZxH7vcNwvcHu5gvhg.aDHu2A.A3-CejEWJdQfEhVvRf38q1XW2LQ"
  }
}
معلومات النظام: {
  "memory_usage": "96.40 MB",
  "cpu_percent": "0.0%",
  "memory_percent": "81.2%",
  "disk_usage": "46.7%",
  "open_files": 11,
  "threads": 5,
  "connections": 0,
  "uptime": "1:50:43"
}
تتبع الاستثناء:
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\app.py", line 917, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\app.py", line 902, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)  # type: ignore[no-any-return]
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask_login\utils.py", line 290, in decorated_view
    return current_app.ensure_sync(func)(*args, **kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\routes\users.py", line 155, in create
    return render_template('user_form.html', user=None, action='create')
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\templating.py", line 149, in render_template
    template = app.jinja_env.get_or_select_template(template_name_or_list)
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\jinja2\environment.py", line 1087, in get_or_select_template
    return self.get_template(template_name_or_list, parent, globals)
           ~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\jinja2\environment.py", line 1016, in get_template
    return self._load_template(name, globals)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\jinja2\environment.py", line 975, in _load_template
    template = self.loader.load(self, name, self.make_globals(globals))
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\jinja2\loaders.py", line 126, in load
    source, filename, uptodate = self.get_source(environment, name)
                                 ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\templating.py", line 65, in get_source
    return self._get_source_fast(environment, template)
           ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\templating.py", line 99, in _get_source_fast
    raise TemplateNotFound(template)
jinja2.exceptions.TemplateNotFound: user_form.html

2025-05-24 21:50:18 - ERROR - nobara - error_logger:270 - استثناء: TemplateNotFound: profile.html
الوقت: 2025-05-24 21:50:18
نوع الاستثناء: TemplateNotFound
الملف: app.py
السطر: 809
الدالة: handle_user_exception
معلومات الطلب: {
  "url": "http://************:5000/profile",
  "base_url": "http://************:5000/profile",
  "path": "/profile",
  "method": "GET",
  "endpoint": "users.profile",
  "remote_addr": "************",
  "user_agent": null,
  "referrer": "http://************:5000/settings",
  "content_type": null,
  "content_length": null,
  "is_xhr": false,
  "is_secure": false,
  "timestamp": "2025-05-24 21:50:18",
  "headers": {
    "Host": "************:5000",
    "Connection": "keep-alive",
    "Upgrade-Insecure-Requests": "1",
    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********",
    "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7",
    "Referer": "http://************:5000/settings",
    "Accept-Encoding": "gzip, deflate",
    "Accept-Language": "en-US,en;q=0.9",
    "Cookie": "NEXT_LOCALE=en; session=.eJwljjkOg0AMAP_iOgXeZX3wGWR8KGkhVFH-HiKk6UYjzQfW2vN4wvLez3zA-gpYoIUJa8xWlUzKKK6utnFFKAt3n-Vi7m38bbXWg7bJEVWtUUrVlRUOVdx0cHRKVJPm0yROaMboETg6uZaTqURzpogMsuxwjZxH7vcNwvcHu5gvhg.aDHu2A.A3-CejEWJdQfEhVvRf38q1XW2LQ"
  },
  "args": {},
  "form": {},
  "cookies": {
    "NEXT_LOCALE": "en",
    "session": ".eJwljjkOg0AMAP_iOgXeZX3wGWR8KGkhVFH-HiKk6UYjzQfW2vN4wvLez3zA-gpYoIUJa8xWlUzKKK6utnFFKAt3n-Vi7m38bbXWg7bJEVWtUUrVlRUOVdx0cHRKVJPm0yROaMboETg6uZaTqURzpogMsuxwjZxH7vcNwvcHu5gvhg.aDHu2A.A3-CejEWJdQfEhVvRf38q1XW2LQ"
  }
}
معلومات النظام: {
  "memory_usage": "96.48 MB",
  "cpu_percent": "0.0%",
  "memory_percent": "85.6%",
  "disk_usage": "46.7%",
  "open_files": 11,
  "threads": 5,
  "connections": 0,
  "uptime": "1:52:15"
}
تتبع الاستثناء:
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\app.py", line 917, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\app.py", line 902, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)  # type: ignore[no-any-return]
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask_login\utils.py", line 290, in decorated_view
    return current_app.ensure_sync(func)(*args, **kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\routes\users.py", line 361, in profile
    return render_template('profile.html', user=current_user, current_user=current_user)
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\templating.py", line 149, in render_template
    template = app.jinja_env.get_or_select_template(template_name_or_list)
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\jinja2\environment.py", line 1087, in get_or_select_template
    return self.get_template(template_name_or_list, parent, globals)
           ~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\jinja2\environment.py", line 1016, in get_template
    return self._load_template(name, globals)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\jinja2\environment.py", line 975, in _load_template
    template = self.loader.load(self, name, self.make_globals(globals))
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\jinja2\loaders.py", line 126, in load
    source, filename, uptodate = self.get_source(environment, name)
                                 ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\templating.py", line 65, in get_source
    return self._get_source_fast(environment, template)
           ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\templating.py", line 99, in _get_source_fast
    raise TemplateNotFound(template)
jinja2.exceptions.TemplateNotFound: profile.html

2025-05-24 22:02:25 - ERROR - nobara - error_logger:270 - استثناء: TemplateNotFound: warehouses/warehouses.html
الوقت: 2025-05-24 22:02:25
نوع الاستثناء: TemplateNotFound
الملف: app.py
السطر: 809
الدالة: handle_user_exception
معلومات الطلب: {
  "url": "http://************:5000/warehouses",
  "base_url": "http://************:5000/warehouses",
  "path": "/warehouses",
  "method": "GET",
  "endpoint": "warehouses.index",
  "remote_addr": "************",
  "user_agent": null,
  "referrer": "http://************:5000/settings/error-logs",
  "content_type": null,
  "content_length": null,
  "is_xhr": false,
  "is_secure": false,
  "timestamp": "2025-05-24 22:02:25",
  "headers": {
    "Host": "************:5000",
    "Connection": "keep-alive",
    "Upgrade-Insecure-Requests": "1",
    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********",
    "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7",
    "Referer": "http://************:5000/settings/error-logs",
    "Accept-Encoding": "gzip, deflate",
    "Accept-Language": "en-US,en;q=0.9",
    "Cookie": "NEXT_LOCALE=en; session=.eJzNUMtuxDAI_JWIcw7BD2zyK-1q5QDWVuop3pxW--91nEP7CZXQMDMwEuIF9_pd2sMarB8vmJ69QTtErDWY4fNYyJUTQ5yGiCd6GjyNAQ7O0x8rXInh2Ai44dC1lC8xuPzGnMLtPf-HI25zf8tu7QHrcz-sqy-FFZyWnFhDqdUSccIsLFy2VFU55eQl5F7Bu3hOq3NeaVsEkbk4slxrj1WMzLhxTOrJkEt2sixZCEtJKKoYPQlXocJZnSRSNaVivv_ifjTbr2sQ3j9nUX3N.aDIXZw.NRl-0k4Ix1b0W-v4eXFXfPaq2g4"
  },
  "args": {},
  "form": {},
  "cookies": {
    "NEXT_LOCALE": "en",
    "session": ".eJzNUMtuxDAI_JWIcw7BD2zyK-1q5QDWVuop3pxW--91nEP7CZXQMDMwEuIF9_pd2sMarB8vmJ69QTtErDWY4fNYyJUTQ5yGiCd6GjyNAQ7O0x8rXInh2Ai44dC1lC8xuPzGnMLtPf-HI25zf8tu7QHrcz-sqy-FFZyWnFhDqdUSccIsLFy2VFU55eQl5F7Bu3hOq3NeaVsEkbk4slxrj1WMzLhxTOrJkEt2sixZCEtJKKoYPQlXocJZnSRSNaVivv_ifjTbr2sQ3j9nUX3N.aDIXZw.NRl-0k4Ix1b0W-v4eXFXfPaq2g4"
  }
}
معلومات النظام: {
  "memory_usage": "94.89 MB",
  "cpu_percent": "0.0%",
  "memory_percent": "86.4%",
  "disk_usage": "46.7%",
  "open_files": 11,
  "threads": 5,
  "connections": 0,
  "uptime": "2:04:22"
}
تتبع الاستثناء:
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\app.py", line 917, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\app.py", line 902, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)  # type: ignore[no-any-return]
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask_login\utils.py", line 290, in decorated_view
    return current_app.ensure_sync(func)(*args, **kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\routes\warehouses.py", line 45, in index
    return render_template('warehouses/warehouses.html',
                          warehouses=warehouses,
    ...<2 lines>...
                          status=status,
                          stats=stats)
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\templating.py", line 149, in render_template
    template = app.jinja_env.get_or_select_template(template_name_or_list)
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\jinja2\environment.py", line 1087, in get_or_select_template
    return self.get_template(template_name_or_list, parent, globals)
           ~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\jinja2\environment.py", line 1016, in get_template
    return self._load_template(name, globals)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\jinja2\environment.py", line 975, in _load_template
    template = self.loader.load(self, name, self.make_globals(globals))
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\jinja2\loaders.py", line 126, in load
    source, filename, uptodate = self.get_source(environment, name)
                                 ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\templating.py", line 65, in get_source
    return self._get_source_fast(environment, template)
           ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\templating.py", line 99, in _get_source_fast
    raise TemplateNotFound(template)
jinja2.exceptions.TemplateNotFound: warehouses/warehouses.html

