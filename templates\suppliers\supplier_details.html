<!DOCTYPE html>
<html lang="ar" dir="rtl" class="light">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>FoUaD - تفاصيل المورد</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdn.jsdelivr.net/npm/remixicon@2.5.0/fonts/remixicon.css" rel="stylesheet">
    <script>
        tailwind.config = {
            darkMode: 'class',
            theme: {
                extend: {
                    colors: {
                        primary: '#3B82F6',
                        secondary: '#10b981',
                        dark: {
                            100: '#1E293B',
                            200: '#0F172A',
                            300: '#0B1120',
                            400: '#060A15'
                        }
                    }
                }
            }
        }
    </script>
    <style>
        body {
            font-family: 'Tajawal', sans-serif;
            background-color: #f1f5f9;
            transition: background-color 0.3s ease, color 0.3s ease;
        }
        body.dark {
            background-color: #0F172A;
            color: #E2E8F0;
        }
        .card {
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }
        .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
        }
        .supplier-avatar {
            background: linear-gradient(135deg, #10B981 0%, #059669 100%);
            box-shadow: 0 10px 15px -3px rgba(16, 185, 129, 0.3);
        }
        .action-button {
            transition: all 0.3s ease;
        }
        .action-button:hover {
            transform: translateY(-2px);
        }
        .page-header {
            background: linear-gradient(to right, #10B981, #059669);
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
        }
        .dark .page-header {
            background: linear-gradient(to right, #065F46, #064E3B);
        }
        .tab-button {
            position: relative;
            transition: all 0.3s ease;
        }
        .tab-button.active::after {
            content: '';
            position: absolute;
            bottom: -2px;
            left: 0;
            width: 100%;
            height: 2px;
            background-color: #10B981;
            border-radius: 2px;
        }
        .dark .tab-button.active::after {
            background-color: #059669;
        }
        .tab-pane {
            display: none;
        }
        .tab-pane.active {
            display: block;
        }
        .badge {
            @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;
        }
        .badge-green {
            @apply bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300;
        }
        .badge-blue {
            @apply bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300;
        }
        .badge-red {
            @apply bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300;
        }
        .badge-yellow {
            @apply bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-300;
        }
        .badge-purple {
            @apply bg-purple-100 text-purple-800 dark:bg-purple-900/30 dark:text-purple-300;
        }
        .badge-gray {
            @apply bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300;
        }
    </style>
</head>
<body class="min-h-screen">
    {% include 'partials/navbar.html' %}

    <!-- Page Header -->
    <div class="page-header py-6 mb-8">
        <div class="container mx-auto px-4">
            <div class="flex flex-col md:flex-row justify-between items-center">
                <div class="flex items-center mb-4 md:mb-0">
                    <div class="supplier-avatar w-16 h-16 rounded-full flex items-center justify-center text-white text-2xl font-bold mr-4">
                        {{ supplier.name[0].upper() }}
                    </div>
                    <div>
                        <h1 class="text-2xl md:text-3xl font-bold text-white">{{ supplier.name }}</h1>
                        <p class="text-green-100">
                            {% if supplier.phone %}
                                <span class="inline-flex items-center">
                                    <i class="ri-phone-line mr-1"></i> {{ supplier.phone }}
                                </span>
                            {% endif %}
                            {% if supplier.email %}
                                <span class="inline-flex items-center mr-4">
                                    <i class="ri-mail-line mr-1"></i> {{ supplier.email }}
                                </span>
                            {% endif %}
                        </p>
                    </div>
                </div>
                <div class="flex space-x-2 space-x-reverse">
                    <a href="{{ url_for('suppliers.edit', id=supplier.id) }}" class="action-button bg-white text-green-600 hover:bg-green-50 px-4 py-2 rounded-lg flex items-center shadow-md">
                        <i class="ri-edit-line ml-1"></i>
                        تعديل
                    </a>
                    <a href="{{ url_for('suppliers.index') }}" class="action-button bg-white text-green-600 hover:bg-green-50 px-4 py-2 rounded-lg flex items-center shadow-md">
                        <i class="ri-arrow-right-line ml-1"></i>
                        العودة
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="container mx-auto px-4 pb-8">
        <!-- Stats Cards -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <!-- إجمالي المشتريات -->
            <div class="bg-white dark:bg-gray-800 rounded-xl shadow-md p-6 card">
                <div class="flex items-center">
                    <div class="w-12 h-12 bg-green-100 dark:bg-green-900/50 rounded-full flex items-center justify-center mr-4">
                        <i class="ri-money-dollar-circle-line text-2xl text-green-600 dark:text-green-400"></i>
                    </div>
                    <div>
                        <p class="text-sm text-gray-500 dark:text-gray-400">إجمالي المشتريات</p>
                        <p class="text-2xl font-bold text-gray-800 dark:text-white">{{ "%.2f"|format(stats.total_purchased) }} <span class="text-sm">ج.م</span></p>
                    </div>
                </div>
            </div>

            <!-- المبلغ المدفوع -->
            <div class="bg-white dark:bg-gray-800 rounded-xl shadow-md p-6 card">
                <div class="flex items-center">
                    <div class="w-12 h-12 bg-blue-100 dark:bg-blue-900/50 rounded-full flex items-center justify-center mr-4">
                        <i class="ri-bank-card-line text-2xl text-blue-600 dark:text-blue-400"></i>
                    </div>
                    <div>
                        <p class="text-sm text-gray-500 dark:text-gray-400">المبلغ المدفوع</p>
                        <p class="text-2xl font-bold text-gray-800 dark:text-white">{{ "%.2f"|format(stats.paid_amount) }} <span class="text-sm">ج.م</span></p>
                    </div>
                </div>
            </div>

            <!-- المبلغ المتبقي -->
            <div class="bg-white dark:bg-gray-800 rounded-xl shadow-md p-6 card">
                <div class="flex items-center">
                    <div class="w-12 h-12 bg-red-100 dark:bg-red-900/50 rounded-full flex items-center justify-center mr-4">
                        <i class="ri-refund-2-line text-2xl text-red-600 dark:text-red-400"></i>
                    </div>
                    <div>
                        <p class="text-sm text-gray-500 dark:text-gray-400">المبلغ المتبقي</p>
                        <p class="text-2xl font-bold text-gray-800 dark:text-white">{{ "%.2f"|format(stats.remaining_amount) }} <span class="text-sm">ج.م</span></p>
                    </div>
                </div>
            </div>

            <!-- عدد المشتريات -->
            <div class="bg-white dark:bg-gray-800 rounded-xl shadow-md p-6 card">
                <div class="flex items-center">
                    <div class="w-12 h-12 bg-purple-100 dark:bg-purple-900/50 rounded-full flex items-center justify-center mr-4">
                        <i class="ri-shopping-bag-line text-2xl text-purple-600 dark:text-purple-400"></i>
                    </div>
                    <div>
                        <p class="text-sm text-gray-500 dark:text-gray-400">عدد المشتريات</p>
                        <p class="text-2xl font-bold text-gray-800 dark:text-white">{{ stats.purchase_count }}</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- حالة الدفع -->
        <div class="bg-white dark:bg-gray-800 rounded-xl shadow-md p-6 mb-8">
            <h2 class="text-lg font-bold text-gray-800 dark:text-white mb-4">حالة الدفع</h2>
            <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-4 mb-2">
                {% set payment_percentage = (stats.paid_amount / stats.total_purchased * 100) if stats.total_purchased > 0 else 0 %}
                <div class="bg-green-600 dark:bg-green-500 h-4 rounded-full" style="width: {{ payment_percentage }}%"></div>
            </div>
            <div class="flex justify-between text-sm text-gray-600 dark:text-gray-400">
                <span>{{ "%.1f"|format(payment_percentage) }}% مدفوع</span>
                <span>{{ "%.1f"|format(100 - payment_percentage) }}% متبقي</span>
            </div>
        </div>

        <!-- Tabs -->
        <div class="mb-6 border-b border-gray-200 dark:border-gray-700">
            <div class="flex overflow-x-auto">
                <button onclick="showTab('info')" id="tab-info" class="tab-button active px-4 py-2 text-gray-600 dark:text-gray-300 font-medium">
                    <i class="ri-information-line ml-1"></i>
                    معلومات المورد
                </button>
                <button onclick="showTab('purchases')" id="tab-purchases" class="tab-button px-4 py-2 text-gray-600 dark:text-gray-300 font-medium">
                    <i class="ri-shopping-cart-line ml-1"></i>
                    فواتير الشراء
                </button>
                <button onclick="showTab('payments')" id="tab-payments" class="tab-button px-4 py-2 text-gray-600 dark:text-gray-300 font-medium">
                    <i class="ri-bank-card-line ml-1"></i>
                    المدفوعات
                </button>
                <button onclick="showTab('products')" id="tab-products" class="tab-button px-4 py-2 text-gray-600 dark:text-gray-300 font-medium">
                    <i class="ri-archive-line ml-1"></i>
                    المنتجات
                </button>
                <button onclick="showTab('reports')" id="tab-reports" class="tab-button px-4 py-2 text-gray-600 dark:text-gray-300 font-medium">
                    <i class="ri-file-chart-line ml-1"></i>
                    التقارير
                </button>
            </div>
        </div>

        <!-- Tab Content -->
        <div id="tab-content">
            <!-- معلومات المورد -->
            <div id="content-info" class="tab-pane active">
                <div class="bg-white dark:bg-gray-800 rounded-xl shadow-md overflow-hidden">
                    <div class="bg-gradient-to-r from-green-500 to-green-600 dark:from-green-700 dark:to-green-800 px-6 py-4">
                        <h2 class="text-xl font-bold text-white flex items-center">
                            <i class="ri-user-line mr-2"></i>
                            معلومات المورد
                        </h2>
                    </div>
                    <div class="p-6">
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div class="space-y-4">
                                <div class="flex items-center border-b border-gray-200 dark:border-gray-700 pb-3">
                                    <div class="w-8 h-8 rounded-full bg-green-100 dark:bg-green-900/50 flex items-center justify-center mr-3">
                                        <i class="ri-user-line text-green-600 dark:text-green-400"></i>
                                    </div>
                                    <div class="flex-1">
                                        <p class="text-sm text-gray-500 dark:text-gray-400">الاسم</p>
                                        <p class="font-medium text-gray-800 dark:text-white">{{ supplier.name }}</p>
                                    </div>
                                </div>

                                <div class="flex items-center border-b border-gray-200 dark:border-gray-700 pb-3">
                                    <div class="w-8 h-8 rounded-full bg-green-100 dark:bg-green-900/50 flex items-center justify-center mr-3">
                                        <i class="ri-phone-line text-green-600 dark:text-green-400"></i>
                                    </div>
                                    <div class="flex-1">
                                        <p class="text-sm text-gray-500 dark:text-gray-400">رقم الهاتف</p>
                                        <p class="font-medium text-gray-800 dark:text-white">{{ supplier.phone or 'غير متوفر' }}</p>
                                    </div>
                                </div>

                                <div class="flex items-center border-b border-gray-200 dark:border-gray-700 pb-3">
                                    <div class="w-8 h-8 rounded-full bg-green-100 dark:bg-green-900/50 flex items-center justify-center mr-3">
                                        <i class="ri-mail-line text-green-600 dark:text-green-400"></i>
                                    </div>
                                    <div class="flex-1">
                                        <p class="text-sm text-gray-500 dark:text-gray-400">البريد الإلكتروني</p>
                                        <p class="font-medium text-gray-800 dark:text-white">{{ supplier.email or 'غير متوفر' }}</p>
                                    </div>
                                </div>
                            </div>

                            <div class="space-y-4">
                                <div class="flex items-center border-b border-gray-200 dark:border-gray-700 pb-3">
                                    <div class="w-8 h-8 rounded-full bg-green-100 dark:bg-green-900/50 flex items-center justify-center mr-3">
                                        <i class="ri-map-pin-line text-green-600 dark:text-green-400"></i>
                                    </div>
                                    <div class="flex-1">
                                        <p class="text-sm text-gray-500 dark:text-gray-400">العنوان</p>
                                        <p class="font-medium text-gray-800 dark:text-white">{{ supplier.address or 'غير متوفر' }}</p>
                                    </div>
                                </div>

                                <div class="flex items-center border-b border-gray-200 dark:border-gray-700 pb-3">
                                    <div class="w-8 h-8 rounded-full bg-green-100 dark:bg-green-900/50 flex items-center justify-center mr-3">
                                        <i class="ri-calendar-line text-green-600 dark:text-green-400"></i>
                                    </div>
                                    <div class="flex-1">
                                        <p class="text-sm text-gray-500 dark:text-gray-400">تاريخ التسجيل</p>
                                        <p class="font-medium text-gray-800 dark:text-white">{{ supplier.created_at.strftime('%Y-%m-%d') }}</p>
                                    </div>
                                </div>

                                <div class="flex items-center">
                                    <div class="w-8 h-8 rounded-full bg-green-100 dark:bg-green-900/50 flex items-center justify-center mr-3">
                                        <i class="ri-time-line text-green-600 dark:text-green-400"></i>
                                    </div>
                                    <div class="flex-1">
                                        <p class="text-sm text-gray-500 dark:text-gray-400">آخر تحديث</p>
                                        <p class="font-medium text-gray-800 dark:text-white">
                                            {% if supplier.updated_at %}
                                                {{ supplier.updated_at.strftime('%Y-%m-%d') }}
                                            {% else %}
                                                {{ supplier.created_at.strftime('%Y-%m-%d') }}
                                            {% endif %}
                                        </p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- فواتير الشراء -->
            <div id="content-purchases" class="tab-pane">
                <div class="bg-white dark:bg-gray-800 rounded-xl shadow-md overflow-hidden">
                    <div class="bg-gradient-to-r from-blue-500 to-blue-600 dark:from-blue-700 dark:to-blue-800 px-6 py-4 flex justify-between items-center">
                        <h2 class="text-xl font-bold text-white flex items-center">
                            <i class="ri-shopping-cart-line mr-2"></i>
                            فواتير الشراء
                        </h2>
                        <a href="{{ url_for('purchases.create', supplier_id=supplier.id) }}" class="bg-white text-blue-600 hover:bg-blue-50 px-3 py-1 rounded-lg text-sm flex items-center shadow-sm">
                            <i class="ri-add-line ml-1"></i>
                            فاتورة جديدة
                        </a>
                    </div>
                    <div class="p-6">
                        {% if purchases %}
                        <div class="overflow-x-auto">
                            <table class="w-full">
                                <thead>
                                    <tr class="bg-gray-50 dark:bg-gray-700 text-gray-500 dark:text-gray-300 text-xs uppercase">
                                        <th class="px-4 py-3 text-right font-medium rounded-tr-lg">رقم الفاتورة</th>
                                        <th class="px-4 py-3 text-right font-medium">التاريخ</th>
                                        <th class="px-4 py-3 text-right font-medium">المبلغ</th>
                                        <th class="px-4 py-3 text-right font-medium">المدفوع</th>
                                        <th class="px-4 py-3 text-right font-medium">المتبقي</th>
                                        <th class="px-4 py-3 text-right font-medium">طريقة الدفع</th>
                                        <th class="px-4 py-3 text-right font-medium">الحالة</th>
                                        <th class="px-4 py-3 text-right font-medium rounded-tl-lg">الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody class="divide-y divide-gray-100 dark:divide-gray-700">
                                    {% for purchase in purchases %}
                                    <tr class="hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors duration-200">
                                        <td class="px-4 py-3 text-sm font-medium text-gray-900 dark:text-white">
                                            {{ purchase.invoice_number }}
                                        </td>
                                        <td class="px-4 py-3 text-sm text-gray-500 dark:text-gray-300">
                                            {{ purchase.created_at.strftime('%Y-%m-%d %H:%M') }}
                                        </td>
                                        <td class="px-4 py-3 text-sm font-medium text-gray-900 dark:text-white">
                                            {{ "%.2f"|format(purchase.total) }} <span class="text-xs text-gray-500 dark:text-gray-400">ج.م</span>
                                        </td>
                                        <td class="px-4 py-3 text-sm font-medium text-green-600 dark:text-green-400">
                                            {{ "%.2f"|format(purchase.paid_amount or 0) }} <span class="text-xs text-gray-500 dark:text-gray-400">ج.م</span>
                                        </td>
                                        <td class="px-4 py-3 text-sm font-medium text-red-600 dark:text-red-400">
                                            {{ "%.2f"|format(purchase.total - (purchase.paid_amount or 0)) }} <span class="text-xs text-gray-500 dark:text-gray-400">ج.م</span>
                                        </td>
                                        <td class="px-4 py-3">
                                            {% if purchase.payment_method == 'cash' %}
                                                <span class="px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-300">
                                                    <i class="ri-money-dollar-circle-line mr-1"></i> نقدي
                                                </span>
                                            {% elif purchase.payment_method == 'card' %}
                                                <span class="px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-300">
                                                    <i class="ri-bank-card-line mr-1"></i> بطاقة
                                                </span>
                                            {% elif purchase.payment_method == 'credit' %}
                                                <span class="px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full bg-purple-100 dark:bg-purple-900/30 text-purple-800 dark:text-purple-300">
                                                    <i class="ri-calendar-line mr-1"></i> آجل
                                                </span>
                                            {% else %}
                                                <span class="px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-300">
                                                    {{ purchase.payment_method }}
                                                </span>
                                            {% endif %}
                                        </td>
                                        <td class="px-4 py-3">
                                            {% if purchase.status == 'completed' %}
                                                <span class="px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-300">
                                                    <i class="ri-check-line mr-1"></i> مكتمل
                                                </span>
                                            {% elif purchase.status == 'cancelled' %}
                                                <span class="px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-300">
                                                    <i class="ri-close-line mr-1"></i> ملغي
                                                </span>
                                            {% elif purchase.status == 'pending' %}
                                                <span class="px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full bg-yellow-100 dark:bg-yellow-900/30 text-yellow-800 dark:text-yellow-300">
                                                    <i class="ri-time-line mr-1"></i> قيد الانتظار
                                                </span>
                                            {% elif purchase.status == 'partially_paid' %}
                                                <span class="px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full bg-orange-100 dark:bg-orange-900/30 text-orange-800 dark:text-orange-300">
                                                    <i class="ri-funds-line mr-1"></i> مدفوع جزئياً
                                                </span>
                                            {% else %}
                                                <span class="px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-300">
                                                    {{ purchase.status }}
                                                </span>
                                            {% endif %}
                                        </td>
                                        <td class="px-4 py-3 text-sm">
                                            <div class="flex space-x-1 space-x-reverse">
                                                <a href="{{ url_for('purchases.details', id=purchase.id) }}" class="p-1.5 bg-blue-50 dark:bg-blue-900/20 text-blue-600 dark:text-blue-400 rounded-lg hover:bg-blue-100 dark:hover:bg-blue-900/40 transition-colors" title="عرض التفاصيل">
                                                    <i class="ri-eye-line"></i>
                                                </a>
                                                <a href="{{ url_for('purchases.print_invoice', id=purchase.id) }}" class="p-1.5 bg-green-50 dark:bg-green-900/20 text-green-600 dark:text-green-400 rounded-lg hover:bg-green-100 dark:hover:bg-green-900/40 transition-colors" target="_blank" title="طباعة الفاتورة">
                                                    <i class="ri-printer-line"></i>
                                                </a>
                                                <a href="{{ url_for('purchases.add_payment', id=purchase.id) }}" class="p-1.5 bg-purple-50 dark:bg-purple-900/20 text-purple-600 dark:text-purple-400 rounded-lg hover:bg-purple-100 dark:hover:bg-purple-900/40 transition-colors" title="تسجيل دفعة">
                                                    <i class="ri-money-dollar-circle-line"></i>
                                                </a>
                                            </div>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                        {% else %}
                        <div class="flex flex-col items-center justify-center py-12 text-gray-400 dark:text-gray-500">
                            <div class="w-20 h-20 rounded-full bg-gray-100 dark:bg-gray-700 flex items-center justify-center mb-4">
                                <i class="ri-shopping-bag-line ri-3x"></i>
                            </div>
                            <p class="text-lg">لا توجد فواتير شراء لهذا المورد</p>
                            <a href="{{ url_for('purchases.create', supplier_id=supplier.id) }}" class="mt-4 px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors">
                                <i class="ri-add-line ml-1"></i>
                                إنشاء فاتورة شراء جديدة
                            </a>
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>

            <!-- المدفوعات -->
            <div id="content-payments" class="tab-pane">
                <div class="bg-white dark:bg-gray-800 rounded-xl shadow-md overflow-hidden">
                    <div class="bg-gradient-to-r from-purple-500 to-purple-600 dark:from-purple-700 dark:to-purple-800 px-6 py-4">
                        <h2 class="text-xl font-bold text-white flex items-center">
                            <i class="ri-bank-card-line mr-2"></i>
                            سجل المدفوعات
                        </h2>
                    </div>
                    <div class="p-6">
                        {% if payments %}
                        <div class="overflow-x-auto">
                            <table class="w-full">
                                <thead>
                                    <tr class="bg-gray-50 dark:bg-gray-700 text-gray-500 dark:text-gray-300 text-xs uppercase">
                                        <th class="px-4 py-3 text-right font-medium rounded-tr-lg">رقم الفاتورة</th>
                                        <th class="px-4 py-3 text-right font-medium">تاريخ الدفع</th>
                                        <th class="px-4 py-3 text-right font-medium">المبلغ</th>
                                        <th class="px-4 py-3 text-right font-medium">طريقة الدفع</th>
                                        <th class="px-4 py-3 text-right font-medium">ملاحظات</th>
                                        <th class="px-4 py-3 text-right font-medium rounded-tl-lg">الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody class="divide-y divide-gray-100 dark:divide-gray-700">
                                    {% for payment in payments %}
                                    <tr class="hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors duration-200">
                                        <td class="px-4 py-3 text-sm font-medium text-gray-900 dark:text-white">
                                            {{ payment.purchase.invoice_number }}
                                        </td>
                                        <td class="px-4 py-3 text-sm text-gray-500 dark:text-gray-300">
                                            {{ payment.payment_date.strftime('%Y-%m-%d') }}
                                        </td>
                                        <td class="px-4 py-3 text-sm font-medium text-gray-900 dark:text-white">
                                            {{ "%.2f"|format(payment.amount) }} <span class="text-xs text-gray-500 dark:text-gray-400">ج.م</span>
                                        </td>
                                        <td class="px-4 py-3">
                                            {% if payment.payment_method == 'cash' %}
                                                <span class="px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-300">
                                                    <i class="ri-money-dollar-circle-line mr-1"></i> نقدي
                                                </span>
                                            {% elif payment.payment_method == 'card' %}
                                                <span class="px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-300">
                                                    <i class="ri-bank-card-line mr-1"></i> بطاقة
                                                </span>
                                            {% elif payment.payment_method == 'bank_transfer' %}
                                                <span class="px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full bg-purple-100 dark:bg-purple-900/30 text-purple-800 dark:text-purple-300">
                                                    <i class="ri-bank-line mr-1"></i> تحويل بنكي
                                                </span>
                                            {% elif payment.payment_method == 'check' %}
                                                <span class="px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full bg-yellow-100 dark:bg-yellow-900/30 text-yellow-800 dark:text-yellow-300">
                                                    <i class="ri-bank-card-line mr-1"></i> شيك
                                                </span>
                                            {% else %}
                                                <span class="px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-300">
                                                    {{ payment.payment_method }}
                                                </span>
                                            {% endif %}
                                        </td>
                                        <td class="px-4 py-3 text-sm text-gray-500 dark:text-gray-300">
                                            {{ payment.notes or '-' }}
                                        </td>
                                        <td class="px-4 py-3 text-sm">
                                            <div class="flex space-x-1 space-x-reverse">
                                                <a href="{{ url_for('purchases.details', id=payment.purchase_id) }}" class="p-1.5 bg-blue-50 dark:bg-blue-900/20 text-blue-600 dark:text-blue-400 rounded-lg hover:bg-blue-100 dark:hover:bg-blue-900/40 transition-colors" title="عرض الفاتورة">
                                                    <i class="ri-eye-line"></i>
                                                </a>
                                                <a href="{{ url_for('purchases.print_receipt', payment_id=payment.id) }}" class="p-1.5 bg-green-50 dark:bg-green-900/20 text-green-600 dark:text-green-400 rounded-lg hover:bg-green-100 dark:hover:bg-green-900/40 transition-colors" target="_blank" title="طباعة إيصال">
                                                    <i class="ri-printer-line"></i>
                                                </a>
                                            </div>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                        {% else %}
                        <div class="flex flex-col items-center justify-center py-12 text-gray-400 dark:text-gray-500">
                            <div class="w-20 h-20 rounded-full bg-gray-100 dark:bg-gray-700 flex items-center justify-center mb-4">
                                <i class="ri-bank-card-line ri-3x"></i>
                            </div>
                            <p class="text-lg">لا توجد مدفوعات مسجلة لهذا المورد</p>
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>

            <!-- المنتجات -->
            <div id="content-products" class="tab-pane">
                <div class="bg-white dark:bg-gray-800 rounded-xl shadow-md overflow-hidden">
                    <div class="bg-gradient-to-r from-yellow-500 to-yellow-600 dark:from-yellow-700 dark:to-yellow-800 px-6 py-4">
                        <h2 class="text-xl font-bold text-white flex items-center">
                            <i class="ri-archive-line mr-2"></i>
                            منتجات المورد
                        </h2>
                    </div>
                    <div class="p-6">
                        {% if supplier_products %}
                        <div class="overflow-x-auto">
                            <table class="w-full">
                                <thead>
                                    <tr class="bg-gray-50 dark:bg-gray-700 text-gray-500 dark:text-gray-300 text-xs uppercase">
                                        <th class="px-4 py-3 text-right font-medium rounded-tr-lg">الباركود</th>
                                        <th class="px-4 py-3 text-right font-medium">اسم المنتج</th>
                                        <th class="px-4 py-3 text-right font-medium">الكمية المشتراة</th>
                                        <th class="px-4 py-3 text-right font-medium">متوسط سعر الشراء</th>
                                        <th class="px-4 py-3 text-right font-medium">آخر سعر شراء</th>
                                        <th class="px-4 py-3 text-right font-medium">آخر تاريخ شراء</th>
                                        <th class="px-4 py-3 text-right font-medium rounded-tl-lg">الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody class="divide-y divide-gray-100 dark:divide-gray-700">
                                    {% for product in supplier_products %}
                                    <tr class="hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors duration-200">
                                        <td class="px-4 py-3 text-sm font-medium text-gray-900 dark:text-white">
                                            {{ product.barcode }}
                                        </td>
                                        <td class="px-4 py-3 text-sm font-medium text-gray-900 dark:text-white">
                                            {{ product.name }}
                                        </td>
                                        <td class="px-4 py-3 text-sm text-gray-500 dark:text-gray-300">
                                            {{ product.total_purchased_quantity }}
                                        </td>
                                        <td class="px-4 py-3 text-sm font-medium text-gray-900 dark:text-white">
                                            {{ "%.2f"|format(product.avg_purchase_price) }} <span class="text-xs text-gray-500 dark:text-gray-400">ج.م</span>
                                        </td>
                                        <td class="px-4 py-3 text-sm font-medium text-gray-900 dark:text-white">
                                            {{ "%.2f"|format(product.last_purchase_price) }} <span class="text-xs text-gray-500 dark:text-gray-400">ج.م</span>
                                        </td>
                                        <td class="px-4 py-3 text-sm text-gray-500 dark:text-gray-300">
                                            {{ product.last_purchase_date.strftime('%Y-%m-%d') }}
                                        </td>
                                        <td class="px-4 py-3 text-sm">
                                            <div class="flex space-x-1 space-x-reverse">
                                                <a href="{{ url_for('products.details', id=product.id) }}" class="p-1.5 bg-blue-50 dark:bg-blue-900/20 text-blue-600 dark:text-blue-400 rounded-lg hover:bg-blue-100 dark:hover:bg-blue-900/40 transition-colors" title="عرض المنتج">
                                                    <i class="ri-eye-line"></i>
                                                </a>
                                                <a href="{{ url_for('purchases.create', supplier_id=supplier.id, product_id=product.id) }}" class="p-1.5 bg-green-50 dark:bg-green-900/20 text-green-600 dark:text-green-400 rounded-lg hover:bg-green-100 dark:hover:bg-green-900/40 transition-colors" title="شراء المنتج">
                                                    <i class="ri-shopping-cart-line"></i>
                                                </a>
                                            </div>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                        {% else %}
                        <div class="flex flex-col items-center justify-center py-12 text-gray-400 dark:text-gray-500">
                            <div class="w-20 h-20 rounded-full bg-gray-100 dark:bg-gray-700 flex items-center justify-center mb-4">
                                <i class="ri-archive-line ri-3x"></i>
                            </div>
                            <p class="text-lg">لا توجد منتجات مشتراة من هذا المورد</p>
                            <a href="{{ url_for('purchases.create', supplier_id=supplier.id) }}" class="mt-4 px-4 py-2 bg-yellow-600 hover:bg-yellow-700 text-white rounded-lg transition-colors">
                                <i class="ri-add-line ml-1"></i>
                                إنشاء طلب شراء جديد
                            </a>
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>

            <!-- التقارير -->
            <div id="content-reports" class="tab-pane">
                <div class="bg-white dark:bg-gray-800 rounded-xl shadow-md overflow-hidden">
                    <div class="bg-gradient-to-r from-red-500 to-red-600 dark:from-red-700 dark:to-red-800 px-6 py-4">
                        <h2 class="text-xl font-bold text-white flex items-center">
                            <i class="ri-file-chart-line mr-2"></i>
                            تقارير المورد
                        </h2>
                    </div>
                    <div class="p-6">
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <!-- تقرير المشتريات الشهرية -->
                            <div class="bg-white dark:bg-gray-800 rounded-xl shadow-md overflow-hidden border border-gray-200 dark:border-gray-700">
                                <div class="bg-gray-50 dark:bg-gray-700 px-4 py-3 border-b border-gray-200 dark:border-gray-600">
                                    <h3 class="text-lg font-medium text-gray-800 dark:text-white">المشتريات الشهرية</h3>
                                </div>
                                <div class="p-4">
                                    <div class="h-64">
                                        <canvas id="monthly-purchases-chart"></canvas>
                                    </div>
                                    <div class="mt-4 text-center">
                                        <a href="#" class="text-blue-600 dark:text-blue-400 hover:underline">عرض التقرير الكامل</a>
                                    </div>
                                </div>
                            </div>

                            <!-- تقرير المنتجات الأكثر شراءً -->
                            <div class="bg-white dark:bg-gray-800 rounded-xl shadow-md overflow-hidden border border-gray-200 dark:border-gray-700">
                                <div class="bg-gray-50 dark:bg-gray-700 px-4 py-3 border-b border-gray-200 dark:border-gray-600">
                                    <h3 class="text-lg font-medium text-gray-800 dark:text-white">المنتجات الأكثر شراءً</h3>
                                </div>
                                <div class="p-4">
                                    <div class="h-64">
                                        <canvas id="top-products-chart"></canvas>
                                    </div>
                                    <div class="mt-4 text-center">
                                        <a href="#" class="text-blue-600 dark:text-blue-400 hover:underline">عرض التقرير الكامل</a>
                                    </div>
                                </div>
                            </div>

                            <!-- تقرير المدفوعات -->
                            <div class="bg-white dark:bg-gray-800 rounded-xl shadow-md overflow-hidden border border-gray-200 dark:border-gray-700">
                                <div class="bg-gray-50 dark:bg-gray-700 px-4 py-3 border-b border-gray-200 dark:border-gray-600">
                                    <h3 class="text-lg font-medium text-gray-800 dark:text-white">تقرير المدفوعات</h3>
                                </div>
                                <div class="p-4">
                                    <div class="h-64">
                                        <canvas id="payments-chart"></canvas>
                                    </div>
                                    <div class="mt-4 text-center">
                                        <a href="#" class="text-blue-600 dark:text-blue-400 hover:underline">عرض التقرير الكامل</a>
                                    </div>
                                </div>
                            </div>

                            <!-- تقرير الديون -->
                            <div class="bg-white dark:bg-gray-800 rounded-xl shadow-md overflow-hidden border border-gray-200 dark:border-gray-700">
                                <div class="bg-gray-50 dark:bg-gray-700 px-4 py-3 border-b border-gray-200 dark:border-gray-600">
                                    <h3 class="text-lg font-medium text-gray-800 dark:text-white">تقرير الديون</h3>
                                </div>
                                <div class="p-4">
                                    <div class="h-64">
                                        <canvas id="debt-chart"></canvas>
                                    </div>
                                    <div class="mt-4 text-center">
                                        <a href="#" class="text-blue-600 dark:text-blue-400 hover:underline">عرض التقرير الكامل</a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    {% include 'partials/footer.html' %}

    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script>
        // تبديل الوضع المظلم
        const darkModeToggle = document.getElementById('darkModeToggle');
        if (darkModeToggle) {
            darkModeToggle.addEventListener('click', function() {
                document.body.classList.toggle('dark');
                if (document.body.classList.contains('dark')) {
                    localStorage.setItem('darkMode', 'enabled');
                } else {
                    localStorage.setItem('darkMode', 'disabled');
                }
            });
        }

        // تحقق من حالة الوضع المظلم عند تحميل الصفحة
        if (localStorage.getItem('darkMode') === 'enabled') {
            document.body.classList.add('dark');
        }

        // وظيفة تبديل التبويبات
        function showTab(tabName) {
            // إخفاء جميع التبويبات
            document.querySelectorAll('.tab-pane').forEach(tab => {
                tab.classList.remove('active');
            });

            // إزالة الحالة النشطة من جميع أزرار التبويب
            document.querySelectorAll('.tab-button').forEach(button => {
                button.classList.remove('active');
            });

            // إظهار التبويب المحدد
            document.getElementById('content-' + tabName).classList.add('active');

            // تنشيط زر التبويب المحدد
            document.getElementById('tab-' + tabName).classList.add('active');

            // حفظ التبويب النشط في التخزين المحلي
            localStorage.setItem('activeSupplierTab', tabName);

            // تحميل البيانات للتبويب المحدد إذا لم يتم تحميلها بعد
            if (tabName === 'reports' && !window.reportsLoaded) {
                loadReportsData();
            }
        }

        // استعادة التبويب النشط من التخزين المحلي عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            const activeTab = localStorage.getItem('activeSupplierTab');
            if (activeTab) {
                showTab(activeTab);
            }

            // تأثيرات الحركة للبطاقات
            document.querySelectorAll('.card').forEach(card => {
                card.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-5px)';
                    this.style.boxShadow = '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)';
                });

                card.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0)';
                    this.style.boxShadow = '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)';
                });
            });
        });

        // تحميل بيانات التقارير
        function loadReportsData() {
            fetch(`/api/suppliers/{{ supplier.id }}/reports`)
                .then(response => response.json())
                .then(data => {
                    window.reportsLoaded = true;

                    // تحميل بيانات المشتريات الشهرية
                    if (data.monthly_purchases && data.monthly_purchases.length > 0) {
                        renderMonthlyPurchasesChart(data.monthly_purchases);
                    }

                    // تحميل بيانات المنتجات الأكثر شراءً
                    if (data.top_products && data.top_products.length > 0) {
                        renderTopProductsChart(data.top_products);
                    }

                    // تحميل بيانات المدفوعات
                    if (data.payment_stats && data.payment_stats.length > 0) {
                        renderPaymentsChart(data.payment_stats);
                    }

                    // تحميل بيانات الديون
                    renderDebtChart();
                })
                .catch(error => {
                    console.error('Error loading reports data:', error);
                });
        }

        // رسم مخطط المشتريات الشهرية
        function renderMonthlyPurchasesChart(data) {
            const ctx = document.getElementById('monthly-purchases-chart');
            if (!ctx) return;

            const months = data.map(item => {
                const [year, month] = item.month.split('-');
                return `${month}/${year}`;
            });

            const values = data.map(item => item.total);

            const isDarkMode = document.body.classList.contains('dark');
            const textColor = isDarkMode ? '#E2E8F0' : '#1E293B';
            const gridColor = isDarkMode ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.1)';

            new Chart(ctx, {
                type: 'bar',
                data: {
                    labels: months,
                    datasets: [{
                        label: 'المشتريات الشهرية',
                        data: values,
                        backgroundColor: 'rgba(59, 130, 246, 0.5)',
                        borderColor: 'rgba(59, 130, 246, 1)',
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true,
                            ticks: {
                                color: textColor
                            },
                            grid: {
                                color: gridColor
                            }
                        },
                        x: {
                            ticks: {
                                color: textColor
                            },
                            grid: {
                                color: gridColor
                            }
                        }
                    },
                    plugins: {
                        legend: {
                            labels: {
                                color: textColor
                            }
                        }
                    }
                }
            });
        }

        // رسم مخطط المنتجات الأكثر شراءً
        function renderTopProductsChart(data) {
            const ctx = document.getElementById('top-products-chart');
            if (!ctx) return;

            const labels = data.map(item => item.name);
            const values = data.map(item => item.total_value);

            const isDarkMode = document.body.classList.contains('dark');
            const textColor = isDarkMode ? '#E2E8F0' : '#1E293B';

            new Chart(ctx, {
                type: 'pie',
                data: {
                    labels: labels,
                    datasets: [{
                        data: values,
                        backgroundColor: [
                            'rgba(255, 99, 132, 0.7)',
                            'rgba(54, 162, 235, 0.7)',
                            'rgba(255, 206, 86, 0.7)',
                            'rgba(75, 192, 192, 0.7)',
                            'rgba(153, 102, 255, 0.7)',
                            'rgba(255, 159, 64, 0.7)',
                            'rgba(199, 199, 199, 0.7)',
                            'rgba(83, 102, 255, 0.7)',
                            'rgba(40, 159, 64, 0.7)',
                            'rgba(210, 199, 199, 0.7)'
                        ],
                        borderColor: [
                            'rgba(255, 99, 132, 1)',
                            'rgba(54, 162, 235, 1)',
                            'rgba(255, 206, 86, 1)',
                            'rgba(75, 192, 192, 1)',
                            'rgba(153, 102, 255, 1)',
                            'rgba(255, 159, 64, 1)',
                            'rgba(199, 199, 199, 1)',
                            'rgba(83, 102, 255, 1)',
                            'rgba(40, 159, 64, 1)',
                            'rgba(210, 199, 199, 1)'
                        ],
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'right',
                            labels: {
                                color: textColor
                            }
                        }
                    }
                }
            });
        }

        // رسم مخطط المدفوعات
        function renderPaymentsChart(data) {
            const ctx = document.getElementById('payments-chart');
            if (!ctx) return;

            const months = data.map(item => {
                const [year, month] = item.month.split('-');
                return `${month}/${year}`;
            });

            const values = data.map(item => item.total);

            const isDarkMode = document.body.classList.contains('dark');
            const textColor = isDarkMode ? '#E2E8F0' : '#1E293B';
            const gridColor = isDarkMode ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.1)';

            new Chart(ctx, {
                type: 'line',
                data: {
                    labels: months,
                    datasets: [{
                        label: 'المدفوعات الشهرية',
                        data: values,
                        backgroundColor: 'rgba(16, 185, 129, 0.2)',
                        borderColor: 'rgba(16, 185, 129, 1)',
                        borderWidth: 2,
                        tension: 0.4,
                        fill: true
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true,
                            ticks: {
                                color: textColor
                            },
                            grid: {
                                color: gridColor
                            }
                        },
                        x: {
                            ticks: {
                                color: textColor
                            },
                            grid: {
                                color: gridColor
                            }
                        }
                    },
                    plugins: {
                        legend: {
                            labels: {
                                color: textColor
                            }
                        }
                    }
                }
            });
        }

        // رسم مخطط الديون
        function renderDebtChart() {
            const ctx = document.getElementById('debt-chart');
            if (!ctx) return;

            const totalPurchased = {{ stats.total_purchased }};
            const paidAmount = {{ stats.paid_amount }};
            const remainingAmount = {{ stats.remaining_amount }};

            const isDarkMode = document.body.classList.contains('dark');
            const textColor = isDarkMode ? '#E2E8F0' : '#1E293B';

            new Chart(ctx, {
                type: 'doughnut',
                data: {
                    labels: ['المدفوع', 'المتبقي'],
                    datasets: [{
                        data: [paidAmount, remainingAmount],
                        backgroundColor: [
                            'rgba(16, 185, 129, 0.7)',
                            'rgba(239, 68, 68, 0.7)'
                        ],
                        borderColor: [
                            'rgba(16, 185, 129, 1)',
                            'rgba(239, 68, 68, 1)'
                        ],
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'bottom',
                            labels: {
                                color: textColor
                            }
                        }
                    }
                }
            });
        }
    </script>
</body>
</html>