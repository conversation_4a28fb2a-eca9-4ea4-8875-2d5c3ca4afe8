<!-- Header -->
<header class="header bg-card shadow-md border-b border-primary sticky top-0 z-40">
    <div class="container-fluid">
        <div class="flex items-center justify-between h-16">
            <!-- <PERSON><PERSON> & Menu Toggle -->
            <div class="flex items-center gap-4">
                <button id="sidebar-toggle" class="btn btn-icon btn-secondary lg:hidden">
                    <i class="ri-menu-line"></i>
                </button>
                
                <div class="flex items-center gap-3">
                    <div class="w-10 h-10 bg-gradient-primary rounded-lg flex items-center justify-center text-white font-bold text-lg">
                        ن
                    </div>
                    <div class="hidden sm:block">
                        <h1 class="text-lg font-bold text-primary">{{ get_app_config().APP_NAME_AR }}</h1>
                        <p class="text-xs text-secondary">نظام نقاط البيع</p>
                    </div>
                </div>
            </div>
            
            <!-- Header Actions -->
            <div class="flex items-center gap-2 header-actions">
                <!-- Search -->
                <div class="hidden md:block relative">
                    <input type="text" 
                           id="global-search"
                           placeholder="البحث السريع..." 
                           class="form-control w-64 pl-10 pr-4 py-2 text-sm">
                    <i class="ri-search-line absolute left-3 top-1/2 transform -translate-y-1/2 text-muted"></i>
                </div>
                
                <!-- Theme Toggle -->
                <button id="theme-toggle" class="btn btn-icon btn-secondary" title="تبديل الثيم">
                    <i class="ri-moon-line"></i>
                </button>
                
                <!-- Language Toggle -->
                <button id="language-toggle" class="btn btn-icon btn-secondary" title="تبديل اللغة">
                    <span class="font-bold">EN</span>
                </button>
                
                <!-- Notifications -->
                <button id="notifications-toggle" class="btn btn-icon btn-secondary relative">
                    <i class="ri-notification-line"></i>
                    <span class="absolute -top-1 -right-1 w-5 h-5 bg-red text-white text-xs rounded-full flex items-center justify-center">3</span>
                </button>
                
                <!-- User Menu -->
                <div class="relative">
                    <button id="user-menu-toggle" class="flex items-center gap-2 p-2 rounded-lg hover:bg-hover transition-colors">
                        <div class="w-8 h-8 bg-gradient-secondary rounded-full flex items-center justify-center text-white text-sm font-medium">
                            {% if current_user.is_authenticated %}
                                {{ current_user.first_name[0] }}{{ current_user.last_name[0] }}
                            {% else %}
                                ؟
                            {% endif %}
                        </div>
                        <div class="hidden sm:block text-right">
                            <p class="text-sm font-medium text-primary">
                                {% if current_user.is_authenticated %}
                                    {{ current_user.full_name }}
                                {% else %}
                                    ضيف
                                {% endif %}
                            </p>
                            <p class="text-xs text-secondary">
                                {% if current_user.is_authenticated and current_user.role %}
                                    {{ current_user.role.name_ar }}
                                {% else %}
                                    غير مسجل
                                {% endif %}
                            </p>
                        </div>
                        <i class="ri-arrow-down-s-line text-muted"></i>
                    </button>
                    
                    <!-- User Dropdown Menu -->
                    <div id="user-menu" class="absolute left-0 mt-2 w-48 bg-card rounded-lg shadow-lg border border-primary hidden z-50">
                        <div class="py-2">
                            <a href="#" class="flex items-center gap-3 px-4 py-2 text-sm text-primary hover:bg-hover">
                                <i class="ri-user-line"></i>
                                الملف الشخصي
                            </a>
                            <a href="{{ url_for('settings.index') }}" class="flex items-center gap-3 px-4 py-2 text-sm text-primary hover:bg-hover">
                                <i class="ri-settings-line"></i>
                                الإعدادات
                            </a>
                            <div class="border-t border-primary my-2"></div>
                            <a href="{{ url_for('auth.logout') }}" class="flex items-center gap-3 px-4 py-2 text-sm text-red-600 hover:bg-hover">
                                <i class="ri-logout-box-line"></i>
                                تسجيل الخروج
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</header>

<script>
// User menu toggle
document.addEventListener('DOMContentLoaded', function() {
    const userMenuToggle = document.getElementById('user-menu-toggle');
    const userMenu = document.getElementById('user-menu');
    
    if (userMenuToggle && userMenu) {
        userMenuToggle.addEventListener('click', function(e) {
            e.stopPropagation();
            userMenu.classList.toggle('hidden');
        });
        
        // Close menu when clicking outside
        document.addEventListener('click', function() {
            userMenu.classList.add('hidden');
        });
        
        userMenu.addEventListener('click', function(e) {
            e.stopPropagation();
        });
    }
});
</script>
