<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Nobara - تقرير معاملات الخزينة</title>
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <!-- Remixicon -->
    <link href="https://cdn.jsdelivr.net/npm/remixicon@2.5.0/fonts/remixicon.css" rel="stylesheet">
    <!-- Alpine.js -->
    <script defer src="https://cdn.jsdelivr.net/npm/alpinejs@3.x.x/dist/cdn.min.js"></script>
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script>
        tailwind.config = {
            darkMode: 'class',
            theme: {
                extend: {
                    colors: {
                        primary: {
                            50: '#eff6ff',
                            100: '#dbeafe',
                            200: '#bfdbfe',
                            300: '#93c5fd',
                            400: '#60a5fa',
                            500: '#3b82f6',
                            600: '#2563eb',
                            700: '#1d4ed8',
                            800: '#1e40af',
                            900: '#1e3a8a',
                            950: '#172554',
                        },
                        dark: {
                            100: '#1E293B',
                            200: '#0F172A',
                            300: '#0B1120',
                            400: '#060A15'
                        }
                    }
                }
            }
        }
    </script>
    <style>
        body {
            font-family: 'Cairo', 'Tajawal', sans-serif;
            transition: background-color 0.3s ease, color 0.3s ease;
        }
        
        .bg-pattern {
            background-color: #f9fafb;
            background-image: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%233B82F6' fill-opacity='0.05'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
        }
        
        .dark .bg-pattern {
            background-color: #0F172A;
            background-image: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%233B82F6' fill-opacity='0.1'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
        }
        
        .report-card {
            transition: all 0.3s ease;
            border: 1px solid #e5e7eb;
        }
        
        .report-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px -5px rgba(59, 130, 246, 0.1), 0 8px 10px -6px rgba(59, 130, 246, 0.1);
            border-color: #93c5fd;
        }
        
        .dark .report-card {
            border-color: #1E293B;
        }
        
        .dark .report-card:hover {
            box-shadow: 0 10px 25px -5px rgba(59, 130, 246, 0.2), 0 8px 10px -6px rgba(59, 130, 246, 0.2);
            border-color: #3b82f6;
        }
        
        .powered-by {
            position: fixed;
            bottom: 1rem;
            left: 1rem;
            background-color: #ffffff;
            border-radius: 1rem;
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
            padding: 0.75rem 1rem;
            display: flex;
            align-items: center;
            z-index: 40;
            transition: all 0.3s ease;
            border: 1px solid #e5e7eb;
        }
        
        .dark .powered-by {
            background-color: #1E293B;
            border-color: #2D3748;
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.3), 0 4px 6px -2px rgba(0, 0, 0, 0.2);
        }
        
        .powered-by:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
        }
        
        .dark .powered-by:hover {
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.3), 0 10px 10px -5px rgba(0, 0, 0, 0.2);
        }
        
        @media print {
            .no-print {
                display: none !important;
            }
            
            .print-only {
                display: block !important;
            }
            
            body {
                background: white !important;
                font-size: 12pt;
            }
            
            .print-break-inside-avoid {
                break-inside: avoid;
            }
            
            .print-break-after {
                break-after: page;
            }
        }
    </style>
</head>
<body class="bg-pattern dark:bg-dark-200 dark:text-gray-100" x-data="{ darkMode: localStorage.getItem('darkMode') === 'enabled' }">
    <div class="flex h-screen overflow-hidden">
        <!-- Sidebar -->
        <div class="no-print">
            {% include 'partials/sidebar.html' %}
        </div>

        <!-- Main Content -->
        <div class="flex-1 flex flex-col overflow-hidden">
            <!-- Top Navigation -->
            <div class="no-print">
                {% include 'partials/topnav.html' %}
            </div>

            <!-- Content Area -->
            <main class="flex-1 overflow-y-auto p-6">
                <div class="mb-6 flex justify-between items-center no-print">
                    <div>
                        <h1 class="text-2xl font-bold text-gray-800 dark:text-white">تقرير معاملات الخزينة</h1>
                        <p class="text-gray-600 dark:text-gray-400">تقرير عن جميع معاملات الخزينة من إيداعات وسحوبات وتحويلات</p>
                    </div>
                    <div class="flex items-center gap-2">
                        <button @click="darkMode = !darkMode; localStorage.setItem('darkMode', darkMode ? 'enabled' : 'disabled'); document.documentElement.classList.toggle('dark')" class="p-2 rounded-full bg-gray-100 dark:bg-dark-100 hover:bg-gray-200 dark:hover:bg-dark-300 transition-colors">
                            <i class="ri-moon-line dark:hidden"></i>
                            <i class="ri-sun-line hidden dark:block"></i>
                        </button>
                        <button onclick="window.print()" class="flex items-center gap-1 bg-primary-500 hover:bg-primary-600 text-white py-2 px-4 rounded-lg transition-colors">
                            <i class="ri-printer-line"></i>
                            <span>طباعة التقرير</span>
                        </button>
                    </div>
                </div>

                <!-- فلاتر البحث -->
                <div class="bg-white dark:bg-dark-100 rounded-xl p-4 mb-6 no-print">
                    <form method="GET" action="{{ url_for('reports.cash_transactions_report') }}" class="grid grid-cols-1 md:grid-cols-5 gap-4">
                        <div>
                            <label for="date_from" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">من تاريخ</label>
                            <input type="date" id="date_from" name="date_from" value="{{ date_from }}" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 dark:bg-dark-200 dark:text-gray-100">
                        </div>
                        <div>
                            <label for="date_to" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">إلى تاريخ</label>
                            <input type="date" id="date_to" name="date_to" value="{{ date_to }}" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 dark:bg-dark-200 dark:text-gray-100">
                        </div>
                        <div>
                            <label for="cash_register_id" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">الخزينة</label>
                            <select id="cash_register_id" name="cash_register_id" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 dark:bg-dark-200 dark:text-gray-100">
                                <option value="">جميع الخزائن</option>
                                {% for register in cash_registers %}
                                <option value="{{ register.id }}" {% if selected_register|int == register.id %}selected{% endif %}>{{ register.name }}</option>
                                {% endfor %}
                            </select>
                        </div>
                        <div>
                            <label for="transaction_type" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">نوع المعاملة</label>
                            <select id="transaction_type" name="transaction_type" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 dark:bg-dark-200 dark:text-gray-100">
                                <option value="">جميع المعاملات</option>
                                <option value="deposit" {% if selected_type == 'deposit' %}selected{% endif %}>إيداع</option>
                                <option value="withdraw" {% if selected_type == 'withdraw' %}selected{% endif %}>سحب</option>
                                <option value="transfer" {% if selected_type == 'transfer' %}selected{% endif %}>تحويل</option>
                            </select>
                        </div>
                        <div>
                            <label for="user_id" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">الموظف</label>
                            <select id="user_id" name="user_id" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 dark:bg-dark-200 dark:text-gray-100">
                                <option value="">جميع الموظفين</option>
                                {% for user in users %}
                                <option value="{{ user.id }}" {% if selected_user|int == user.id %}selected{% endif %}>{{ user.username }}</option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="md:col-span-5 flex justify-end">
                            <button type="submit" class="bg-primary-500 hover:bg-primary-600 text-white py-2 px-6 rounded-lg inline-flex items-center transition-colors">
                                <i class="ri-filter-3-line ml-1"></i>
                                <span>تطبيق الفلتر</span>
                            </button>
                        </div>
                    </form>
                </div>

                <!-- ملخص التقرير -->
                <div class="bg-white dark:bg-dark-100 rounded-xl p-6 mb-6 print-break-inside-avoid">
                    <div class="print-only text-center mb-6">
                        <img src="{{ url_for('static', filename='img/nobara-logo.svg') }}" alt="Nobara" class="h-16 mx-auto mb-2">
                        <h1 class="text-2xl font-bold text-gray-800">تقرير معاملات الخزينة</h1>
                        <p class="text-gray-600">الفترة من {{ date_from }} إلى {{ date_to }}</p>
                    </div>
                    
                    <h2 class="text-xl font-bold text-gray-800 dark:text-white mb-4 flex items-center">
                        <i class="ri-bar-chart-box-line ml-2 text-primary-500"></i>
                        ملخص التقرير
                    </h2>
                    
                    <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
                        <div class="bg-blue-50 dark:bg-blue-900/20 rounded-xl p-4 border border-blue-100 dark:border-blue-900/30">
                            <div class="text-blue-500 dark:text-blue-400 text-sm font-medium mb-1">عدد المعاملات</div>
                            <div class="text-2xl font-bold text-gray-800 dark:text-white">{{ transactions|length }}</div>
                        </div>
                        <div class="bg-green-50 dark:bg-green-900/20 rounded-xl p-4 border border-green-100 dark:border-green-900/30">
                            <div class="text-green-500 dark:text-green-400 text-sm font-medium mb-1">إجمالي الإيداعات</div>
                            <div class="text-2xl font-bold text-gray-800 dark:text-white">{{ "%.2f"|format(total_deposits) }} ج.م</div>
                        </div>
                        <div class="bg-red-50 dark:bg-red-900/20 rounded-xl p-4 border border-red-100 dark:border-red-900/30">
                            <div class="text-red-500 dark:text-red-400 text-sm font-medium mb-1">إجمالي السحوبات</div>
                            <div class="text-2xl font-bold text-gray-800 dark:text-white">{{ "%.2f"|format(total_withdrawals) }} ج.م</div>
                        </div>
                        <div class="bg-purple-50 dark:bg-purple-900/20 rounded-xl p-4 border border-purple-100 dark:border-purple-900/30">
                            <div class="text-purple-500 dark:text-purple-400 text-sm font-medium mb-1">صافي الحركة</div>
                            <div class="text-2xl font-bold text-gray-800 dark:text-white">{{ "%.2f"|format(total_deposits - total_withdrawals) }} ج.م</div>
                        </div>
                    </div>
                    
                    <div class="mb-6">
                        <h3 class="text-lg font-medium text-gray-800 dark:text-white mb-3">توزيع المعاملات حسب النوع</h3>
                        <div class="h-64">
                            <canvas id="transactionsChart"></canvas>
                        </div>
                    </div>
                </div>

                <!-- جدول المعاملات -->
                <div class="bg-white dark:bg-dark-100 rounded-xl overflow-hidden mb-6 print-break-inside-avoid">
                    <div class="p-4 border-b border-gray-200 dark:border-gray-700">
                        <h2 class="text-xl font-bold text-gray-800 dark:text-white flex items-center">
                            <i class="ri-exchange-dollar-line ml-2 text-primary-500"></i>
                            تفاصيل المعاملات
                        </h2>
                    </div>
                    
                    <div class="overflow-x-auto">
                        <table class="w-full">
                            <thead class="bg-gray-50 dark:bg-dark-300 text-gray-700 dark:text-gray-300">
                                <tr>
                                    <th class="py-3 px-4 text-right">#</th>
                                    <th class="py-3 px-4 text-right">التاريخ</th>
                                    <th class="py-3 px-4 text-right">الخزينة</th>
                                    <th class="py-3 px-4 text-right">نوع المعاملة</th>
                                    <th class="py-3 px-4 text-right">المبلغ</th>
                                    <th class="py-3 px-4 text-right">الرصيد السابق</th>
                                    <th class="py-3 px-4 text-right">الرصيد الجديد</th>
                                    <th class="py-3 px-4 text-right">المرجع</th>
                                    <th class="py-3 px-4 text-right">الموظف</th>
                                    <th class="py-3 px-4 text-right">ملاحظات</th>
                                </tr>
                            </thead>
                            <tbody class="divide-y divide-gray-200 dark:divide-gray-700">
                                {% for transaction in transactions %}
                                <tr class="hover:bg-gray-50 dark:hover:bg-dark-300/50">
                                    <td class="py-3 px-4">{{ transaction.id }}</td>
                                    <td class="py-3 px-4">{{ transaction.created_at.strftime('%Y-%m-%d %H:%M') }}</td>
                                    <td class="py-3 px-4">{{ transaction.cash_register.name }}</td>
                                    <td class="py-3 px-4">
                                        {% if transaction.transaction_type == 'deposit' %}
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400">
                                            <i class="ri-arrow-down-circle-line ml-1"></i>
                                            إيداع
                                        </span>
                                        {% elif transaction.transaction_type == 'withdraw' %}
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400">
                                            <i class="ri-arrow-up-circle-line ml-1"></i>
                                            سحب
                                        </span>
                                        {% elif transaction.transaction_type == 'transfer' %}
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-400">
                                            <i class="ri-exchange-line ml-1"></i>
                                            تحويل
                                        </span>
                                        {% endif %}
                                    </td>
                                    <td class="py-3 px-4 {% if transaction.transaction_type == 'deposit' %}text-green-600 dark:text-green-400{% elif transaction.transaction_type == 'withdraw' %}text-red-600 dark:text-red-400{% else %}text-blue-600 dark:text-blue-400{% endif %}">
                                        {{ "%.2f"|format(transaction.amount) }} ج.م
                                    </td>
                                    <td class="py-3 px-4">{{ "%.2f"|format(transaction.previous_balance) }} ج.م</td>
                                    <td class="py-3 px-4">{{ "%.2f"|format(transaction.new_balance) }} ج.م</td>
                                    <td class="py-3 px-4">{{ transaction.reference or '-' }}</td>
                                    <td class="py-3 px-4">{{ transaction.user.username }}</td>
                                    <td class="py-3 px-4">{{ transaction.notes or '-' }}</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </main>
        </div>
    </div>
    
    <!-- Powered By -->
    <div class="powered-by no-print">
        <div class="w-8 h-8 rounded-full bg-primary-50 dark:bg-primary-900/50 flex items-center justify-center ml-2 text-primary-600 dark:text-primary-400">
            <i class="ri-code-s-slash-line"></i>
        </div>
        <div>
            <div class="text-xs text-gray-600 dark:text-gray-400">
                Powered By <span class="font-bold text-primary-600 dark:text-primary-400">ENG/ Fouad Saber</span>
            </div>
            <div class="text-xs text-gray-500 dark:text-gray-500">Tel: 01020073527</div>
        </div>
    </div>
    
    <script>
        // تفعيل وضع الظلام إذا كان مخزنًا
        if (localStorage.getItem('darkMode') === 'enabled') {
            document.documentElement.classList.add('dark');
        }
        
        // رسم مخطط توزيع المعاملات حسب النوع
        document.addEventListener('DOMContentLoaded', function() {
            const ctx = document.getElementById('transactionsChart').getContext('2d');
            
            const transactionsChart = new Chart(ctx, {
                type: 'pie',
                data: {
                    labels: ['إيداع', 'سحب', 'تحويل'],
                    datasets: [{
                        data: [
                            {{ deposits_count }},
                            {{ withdrawals_count }},
                            {{ transfers_count }}
                        ],
                        backgroundColor: [
                            '#10b981',
                            '#ef4444',
                            '#3b82f6'
                        ],
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'right',
                            labels: {
                                font: {
                                    family: 'Cairo, Tajawal, sans-serif'
                                }
                            }
                        }
                    }
                }
            });
        });
    </script>
</body>
</html>
