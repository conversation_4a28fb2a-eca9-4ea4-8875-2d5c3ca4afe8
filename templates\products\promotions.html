{% extends 'layout.html' %}

{% block title %}إدارة العروض والإجراءات{% endblock %}

{% block page_content %}
<div class="container mx-auto px-4 py-6">
    <div class="flex justify-between items-center mb-6">
        <h1 class="text-2xl font-bold text-gray-800">إدارة العروض والإجراءات</h1>
        <div class="flex space-x-2 space-x-reverse">
            <a href="{{ url_for('products.create_promotion') }}" class="bg-primary text-white px-4 py-2 rounded-lg shadow-md hover:bg-indigo-700 transition-all duration-300 flex items-center gap-2">
                <i class="ri-add-line"></i>
                <span>إضافة عرض جديد</span>
            </a>
            <a href="{{ url_for('products.index') }}" class="bg-gray-200 text-gray-700 px-4 py-2 rounded-lg shadow-md hover:bg-gray-300 transition-all duration-300 flex items-center gap-2">
                <i class="ri-arrow-right-line"></i>
                <span>العودة للمنتجات</span>
            </a>
        </div>
    </div>

    <!-- فلاتر البحث -->
    <div class="bg-white rounded-lg shadow-md p-6 mb-6">
        <form method="get" action="{{ url_for('products.promotions') }}" class="flex flex-wrap gap-4">
            <div class="flex-1 min-w-[200px]">
                <input type="text" name="search" value="{{ search }}" placeholder="بحث باسم العرض أو الباركود..." class="w-full rounded-md border-gray-300 shadow-sm focus:border-primary focus:ring focus:ring-primary focus:ring-opacity-50">
            </div>
            <div class="w-full sm:w-auto">
                <select name="category_id" class="w-full rounded-md border-gray-300 shadow-sm focus:border-primary focus:ring focus:ring-primary focus:ring-opacity-50">
                    <option value="">جميع التصنيفات</option>
                    {% for category in categories %}
                    <option value="{{ category.id }}" {% if category_id|int == category.id %}selected{% endif %}>{{ category.name }}</option>
                    {% endfor %}
                </select>
            </div>
            <div class="w-full sm:w-auto">
                <select name="status" class="w-full rounded-md border-gray-300 shadow-sm focus:border-primary focus:ring focus:ring-primary focus:ring-opacity-50">
                    <option value="">جميع الحالات</option>
                    <option value="active" {% if status == 'active' %}selected{% endif %}>نشط</option>
                    <option value="inactive" {% if status == 'inactive' %}selected{% endif %}>غير نشط</option>
                </select>
            </div>
            <div>
                <button type="submit" class="bg-primary text-white px-4 py-2 rounded-lg shadow-md hover:bg-indigo-700 transition-all duration-300 flex items-center gap-2">
                    <i class="ri-search-line"></i>
                    <span>بحث</span>
                </button>
            </div>
        </form>
    </div>

    <!-- جدول العروض -->
    <div class="bg-white rounded-lg shadow-md overflow-hidden">
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">العرض</th>
                        <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">النوع</th>
                        <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">القيمة</th>
                        <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">تاريخ البداية</th>
                        <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">تاريخ النهاية</th>
                        <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الحالة</th>
                        <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الباركود</th>
                        <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الإجراءات</th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    {% for promotion in promotions %}
                    <tr class="hover:bg-gray-50">
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="flex items-center">
                                <div class="flex-shrink-0 h-10 w-10 rounded-full bg-indigo-100 flex items-center justify-center">
                                    <i class="ri-price-tag-3-line text-primary text-lg"></i>
                                </div>
                                <div class="mr-4">
                                    <div class="text-sm font-medium text-gray-900">{{ promotion.name }}</div>
                                    <div class="text-sm text-gray-500">{{ promotion.products_count }} منتج</div>
                                </div>
                            </div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <span class="text-sm text-gray-900">
                                {% if promotion.type == 'discount_percentage' %}
                                خصم نسبة مئوية
                                {% elif promotion.type == 'discount_fixed' %}
                                خصم مبلغ ثابت
                                {% elif promotion.type == 'buy_x_get_y' %}
                                اشتري X واحصل على Y
                                {% else %}
                                {{ promotion.type }}
                                {% endif %}
                            </span>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <span class="text-sm text-gray-900">
                                {% if promotion.type == 'discount_percentage' %}
                                {{ promotion.value }}%
                                {% elif promotion.type == 'discount_fixed' %}
                                {{ promotion.value }} ج.م
                                {% elif promotion.type == 'buy_x_get_y' %}
                                اشتري {{ promotion.value.buy }} واحصل على {{ promotion.value.get }}
                                {% else %}
                                {{ promotion.value }}
                                {% endif %}
                            </span>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ promotion.start_date }}</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ promotion.end_date }}</td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            {% if promotion.is_active %}
                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">
                                نشط
                            </span>
                            {% else %}
                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 text-red-800">
                                غير نشط
                            </span>
                            {% endif %}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ promotion.barcode }}</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                            <div class="flex space-x-2 space-x-reverse">
                                <a href="{{ url_for('products.edit_promotion', id=promotion.id) }}" class="text-indigo-600 hover:text-indigo-900">
                                    <i class="ri-edit-line"></i>
                                </a>
                                <button type="button" class="text-red-600 hover:text-red-900 delete-promotion-btn" data-id="{{ promotion.id }}" data-name="{{ promotion.name }}">
                                    <i class="ri-delete-bin-line"></i>
                                </button>
                                <button type="button" class="text-blue-600 hover:text-blue-900 print-barcode-btn" data-barcode="{{ promotion.barcode }}" data-name="{{ promotion.name }}">
                                    <i class="ri-printer-line"></i>
                                </button>
                                <button type="button" class="text-green-600 hover:text-green-900 toggle-status-btn" data-id="{{ promotion.id }}" data-status="{{ '1' if promotion.is_active else '0' }}">
                                    <i class="ri-toggle-{% if promotion.is_active %}line{% else %}fill{% endif %}"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        
        <!-- ترقيم الصفحات -->
        {% if total_pages > 1 %}
        <div class="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6">
            <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                <div>
                    <p class="text-sm text-gray-700">
                        عرض
                        <span class="font-medium">{{ (page - 1) * per_page + 1 }}</span>
                        إلى
                        <span class="font-medium">{{ min(page * per_page, total_count) }}</span>
                        من أصل
                        <span class="font-medium">{{ total_count }}</span>
                        نتيجة
                    </p>
                </div>
                <div>
                    <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px space-x-reverse" aria-label="Pagination">
                        {% if page > 1 %}
                        <a href="{{ url_for('products.promotions', page=page-1, search=search, category_id=category_id, status=status) }}" class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                            <span class="sr-only">السابق</span>
                            <i class="ri-arrow-left-s-line"></i>
                        </a>
                        {% endif %}
                        
                        {% for p in range(1, total_pages + 1) %}
                        <a href="{{ url_for('products.promotions', page=p, search=search, category_id=category_id, status=status) }}" class="relative inline-flex items-center px-4 py-2 border border-gray-300 {% if p == page %}bg-primary text-white{% else %}bg-white text-gray-700{% endif %} hover:bg-gray-50 hover:text-gray-700">
                            {{ p }}
                        </a>
                        {% endfor %}
                        
                        {% if page < total_pages %}
                        <a href="{{ url_for('products.promotions', page=page+1, search=search, category_id=category_id, status=status) }}" class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                            <span class="sr-only">التالي</span>
                            <i class="ri-arrow-right-s-line"></i>
                        </a>
                        {% endif %}
                    </nav>
                </div>
            </div>
        </div>
        {% endif %}
    </div>
</div>

<!-- نموذج حذف العرض -->
<div id="deletePromotionModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden">
    <div class="bg-white rounded-lg shadow-xl p-6 w-full max-w-md">
        <div class="flex justify-between items-center mb-4">
            <h3 class="text-lg font-bold text-gray-800">حذف العرض</h3>
            <button type="button" class="text-gray-400 hover:text-gray-500" id="closeDeleteModal">
                <i class="ri-close-line text-xl"></i>
            </button>
        </div>
        
        <div class="mb-6">
            <p class="text-gray-700">هل أنت متأكد من حذف العرض: <span id="deletePromotionName" class="font-semibold"></span>؟</p>
            <p class="text-gray-500 text-sm mt-2">لا يمكن التراجع عن هذا الإجراء.</p>
        </div>
        
        <div class="flex justify-end space-x-2 space-x-reverse">
            <button type="button" id="cancelDeleteBtn" class="bg-gray-200 text-gray-700 px-4 py-2 rounded-lg shadow-md hover:bg-gray-300 transition-all duration-300">
                إلغاء
            </button>
            <button type="button" id="confirmDeleteBtn" class="bg-red-600 text-white px-4 py-2 rounded-lg shadow-md hover:bg-red-700 transition-all duration-300">
                حذف
            </button>
        </div>
    </div>
</div>

<!-- نموذج طباعة الباركود -->
<div id="printBarcodeModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden">
    <div class="bg-white rounded-lg shadow-xl p-6 w-full max-w-md">
        <div class="flex justify-between items-center mb-4">
            <h3 class="text-lg font-bold text-gray-800">طباعة باركود العرض</h3>
            <button type="button" class="text-gray-400 hover:text-gray-500" id="closePrintModal">
                <i class="ri-close-line text-xl"></i>
            </button>
        </div>
        
        <div class="mb-6">
            <div id="barcodePreview" class="bg-white p-4 border rounded-lg flex flex-col items-center justify-center">
                <div class="text-center mb-2">
                    <div class="text-lg font-bold" id="printPromotionName"></div>
                </div>
                <svg id="promotionBarcode" class="w-full h-20"></svg>
            </div>
        </div>
        
        <div class="flex justify-end space-x-2 space-x-reverse">
            <button type="button" id="cancelPrintBtn" class="bg-gray-200 text-gray-700 px-4 py-2 rounded-lg shadow-md hover:bg-gray-300 transition-all duration-300">
                إلغاء
            </button>
            <button type="button" id="printBarcodeBtn" class="bg-primary text-white px-4 py-2 rounded-lg shadow-md hover:bg-indigo-700 transition-all duration-300">
                طباعة
            </button>
            <button type="button" id="downloadBarcodeBtn" class="bg-green-600 text-white px-4 py-2 rounded-lg shadow-md hover:bg-green-700 transition-all duration-300">
                تحميل
            </button>
        </div>
    </div>
</div>

<!-- نموذج تغيير حالة العرض -->
<div id="toggleStatusModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden">
    <div class="bg-white rounded-lg shadow-xl p-6 w-full max-w-md">
        <div class="flex justify-between items-center mb-4">
            <h3 class="text-lg font-bold text-gray-800" id="toggleStatusTitle">تغيير حالة العرض</h3>
            <button type="button" class="text-gray-400 hover:text-gray-500" id="closeToggleModal">
                <i class="ri-close-line text-xl"></i>
            </button>
        </div>
        
        <div class="mb-6">
            <p class="text-gray-700" id="toggleStatusMessage"></p>
        </div>
        
        <div class="flex justify-end space-x-2 space-x-reverse">
            <button type="button" id="cancelToggleBtn" class="bg-gray-200 text-gray-700 px-4 py-2 rounded-lg shadow-md hover:bg-gray-300 transition-all duration-300">
                إلغاء
            </button>
            <button type="button" id="confirmToggleBtn" class="bg-primary text-white px-4 py-2 rounded-lg shadow-md hover:bg-indigo-700 transition-all duration-300">
                تأكيد
            </button>
        </div>
    </div>
</div>

<!-- مكتبة JsBarcode لإنشاء الباركود -->
<script src="https://cdn.jsdelivr.net/npm/jsbarcode@3.11.5/dist/JsBarcode.all.min.js"></script>
<!-- مكتبة html2canvas لتحميل الباركود كصورة -->
<script src="https://cdn.jsdelivr.net/npm/html2canvas@1.4.1/dist/html2canvas.min.js"></script>

{% endblock %}

{% block scripts %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // نموذج حذف العرض
        const deleteModal = document.getElementById('deletePromotionModal');
        const deletePromotionName = document.getElementById('deletePromotionName');
        const closeDeleteModalBtn = document.getElementById('closeDeleteModal');
        const cancelDeleteBtn = document.getElementById('cancelDeleteBtn');
        const confirmDeleteBtn = document.getElementById('confirmDeleteBtn');
        const deleteBtns = document.querySelectorAll('.delete-promotion-btn');
        
        let promotionIdToDelete = null;
        
        // نموذج طباعة الباركود
        const printModal = document.getElementById('printBarcodeModal');
        const printPromotionName = document.getElementById('printPromotionName');
        const closePrintModalBtn = document.getElementById('closePrintModal');
        const cancelPrintBtn = document.getElementById('cancelPrintBtn');
        const printBarcodeBtn = document.getElementById('printBarcodeBtn');
        const downloadBarcodeBtn = document.getElementById('downloadBarcodeBtn');
        const printBtns = document.querySelectorAll('.print-barcode-btn');
        
        // نموذج تغيير حالة العرض
        const toggleModal = document.getElementById('toggleStatusModal');
        const toggleStatusTitle = document.getElementById('toggleStatusTitle');
        const toggleStatusMessage = document.getElementById('toggleStatusMessage');
        const closeToggleModalBtn = document.getElementById('closeToggleModal');
        const cancelToggleBtn = document.getElementById('cancelToggleBtn');
        const confirmToggleBtn = document.getElementById('confirmToggleBtn');
        const toggleBtns = document.querySelectorAll('.toggle-status-btn');
        
        let promotionIdToToggle = null;
        let newStatus = null;
        
        // فتح نموذج حذف العرض
        deleteBtns.forEach(btn => {
            btn.addEventListener('click', function() {
                promotionIdToDelete = this.dataset.id;
                deletePromotionName.textContent = this.dataset.name;
                deleteModal.classList.remove('hidden');
            });
        });
        
        // إغلاق نموذج حذف العرض
        function closeDeleteModal() {
            deleteModal.classList.add('hidden');
            promotionIdToDelete = null;
        }
        
        closeDeleteModalBtn.addEventListener('click', closeDeleteModal);
        cancelDeleteBtn.addEventListener('click', closeDeleteModal);
        
        // تأكيد حذف العرض
        confirmDeleteBtn.addEventListener('click', function() {
            if (promotionIdToDelete) {
                // إرسال طلب الحذف
                fetch(`{{ url_for('products.delete_promotion', id=0) }}`.replace('0', promotionIdToDelete), {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // إعادة تحميل الصفحة
                        window.location.reload();
                    } else {
                        alert(data.message || 'حدث خطأ أثناء حذف العرض');
                        closeDeleteModal();
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('حدث خطأ أثناء الاتصال بالخادم');
                    closeDeleteModal();
                });
            }
        });
        
        // فتح نموذج طباعة الباركود
        printBtns.forEach(btn => {
            btn.addEventListener('click', function() {
                const barcode = this.dataset.barcode;
                const name = this.dataset.name;
                
                printPromotionName.textContent = name;
                
                // إنشاء الباركود
                JsBarcode("#promotionBarcode", barcode, {
                    format: "CODE128",
                    width: 2,
                    height: 80,
                    displayValue: true,
                    textMargin: 2,
                    fontSize: 14,
                    lineColor: "#000"
                });
                
                printModal.classList.remove('hidden');
            });
        });
        
        // إغلاق نموذج طباعة الباركود
        function closePrintModal() {
            printModal.classList.add('hidden');
        }
        
        closePrintModalBtn.addEventListener('click', closePrintModal);
        cancelPrintBtn.addEventListener('click', closePrintModal);
        
        // طباعة الباركود
        printBarcodeBtn.addEventListener('click', function() {
            const barcodePreview = document.getElementById('barcodePreview');
            
            // إنشاء نافذة طباعة
            const printWindow = window.open('', '_blank');
            printWindow.document.write(`
                <!DOCTYPE html>
                <html lang="ar" dir="rtl">
                <head>
                    <meta charset="UTF-8">
                    <title>طباعة باركود العرض</title>
                    <style>
                        body {
                            font-family: Arial, sans-serif;
                            margin: 0;
                            padding: 20px;
                            text-align: center;
                        }
                    </style>
                </head>
                <body>
                    ${barcodePreview.outerHTML}
                    <script>
                        setTimeout(() => { window.print(); window.close(); }, 500);
                    <\/script>
                </body>
                </html>
            `);
            printWindow.document.close();
        });
        
        // تحميل الباركود كصورة
        downloadBarcodeBtn.addEventListener('click', function() {
            const barcodePreview = document.getElementById('barcodePreview');
            
            html2canvas(barcodePreview).then(canvas => {
                const link = document.createElement('a');
                link.download = 'promotion_barcode.png';
                link.href = canvas.toDataURL('image/png');
                link.click();
            });
        });
        
        // فتح نموذج تغيير حالة العرض
        toggleBtns.forEach(btn => {
            btn.addEventListener('click', function() {
                promotionIdToToggle = this.dataset.id;
                const currentStatus = parseInt(this.dataset.status);
                newStatus = currentStatus === 1 ? 0 : 1;
                
                toggleStatusTitle.textContent = newStatus === 1 ? 'تفعيل العرض' : 'إلغاء تفعيل العرض';
                toggleStatusMessage.textContent = newStatus === 1 ? 'هل أنت متأكد من تفعيل هذا العرض؟' : 'هل أنت متأكد من إلغاء تفعيل هذا العرض؟';
                
                toggleModal.classList.remove('hidden');
            });
        });
        
        // إغلاق نموذج تغيير حالة العرض
        function closeToggleModal() {
            toggleModal.classList.add('hidden');
            promotionIdToToggle = null;
            newStatus = null;
        }
        
        closeToggleModalBtn.addEventListener('click', closeToggleModal);
        cancelToggleBtn.addEventListener('click', closeToggleModal);
        
        // تأكيد تغيير حالة العرض
        confirmToggleBtn.addEventListener('click', function() {
            if (promotionIdToToggle !== null && newStatus !== null) {
                // إرسال طلب تغيير الحالة
                fetch(`{{ url_for('products.toggle_promotion_status', id=0) }}`.replace('0', promotionIdToToggle), {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: new URLSearchParams({
                        'status': newStatus
                    })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // إعادة تحميل الصفحة
                        window.location.reload();
                    } else {
                        alert(data.message || 'حدث خطأ أثناء تغيير حالة العرض');
                        closeToggleModal();
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('حدث خطأ أثناء الاتصال بالخادم');
                    closeToggleModal();
                });
            }
        });
    });
</script>
{% endblock %}
