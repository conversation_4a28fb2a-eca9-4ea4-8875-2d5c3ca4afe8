"""
نموذج المستخدم
"""

from datetime import datetime, timedelta
from app import db
from flask_login import UserMixin
from werkzeug.security import generate_password_hash, check_password_hash
from sqlalchemy.orm import relationship


class User(UserMixin, db.Model):
    """نموذج المستخدم"""
    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(64), unique=True, nullable=False)
    email = db.Column(db.String(120), unique=True, nullable=False)
    password_hash = db.Column(db.String(256), nullable=False)
    full_name = db.Column(db.String(128))
    phone = db.Column(db.String(20))
    position = db.Column(db.String(64))
    profile_image = db.Column(db.String(256))
    is_active = db.Column(db.<PERSON>, default=True)
    role = db.Column(db.String(20), default='user')  # تم تغيير role_id إلى role كحقل نصي
    last_login = db.Column(db.DateTime)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    def __repr__(self):
        return f"<User {self.username}>"

    def set_password(self, password):
        """تعيين كلمة المرور المشفرة"""
        self.password_hash = generate_password_hash(password)

    def check_password(self, password):
        """التحقق من صحة كلمة المرور"""
        return check_password_hash(self.password_hash, password)

    def has_permission(self, module, action):
        """التحقق مما إذا كان المستخدم لديه صلاحية محددة"""
        # المدير لديه جميع الصلاحيات
        if self.role == 'admin':
            return True

        # في الوقت الحالي، نعتبر أن المستخدم العادي ليس لديه صلاحيات خاصة
        # يمكن تطوير نظام الصلاحيات لاحقًا
        return False

    def generate_reset_token(self, expiry_hours=24):
        """إنشاء رمز إعادة تعيين كلمة المرور"""
        import secrets
        import string
        from models.security import PasswordResetToken

        # إنشاء رمز عشوائي
        token_chars = string.ascii_letters + string.digits
        token = ''.join(secrets.choice(token_chars) for _ in range(32))

        # حساب تاريخ انتهاء الصلاحية
        expires_at = datetime.utcnow() + timedelta(hours=expiry_hours)

        # إنشاء سجل رمز إعادة تعيين كلمة المرور
        reset_token = PasswordResetToken(
            user_id=self.id,
            token=token,
            expires_at=expires_at
        )

        # حفظ الرمز في قاعدة البيانات
        db.session.add(reset_token)
        db.session.commit()

        return token

    def verify_reset_token(self, token):
        """التحقق من صحة رمز إعادة تعيين كلمة المرور"""
        from models.security import PasswordResetToken

        # البحث عن الرمز في قاعدة البيانات
        reset_token = PasswordResetToken.query.filter_by(
            user_id=self.id,
            token=token,
            is_used=False
        ).first()

        # التحقق من وجود الرمز وعدم انتهاء صلاحيته
        if reset_token and not reset_token.is_expired:
            return True

        return False

    def clear_reset_token(self, token):
        """مسح رمز إعادة تعيين كلمة المرور"""
        from models.security import PasswordResetToken

        # البحث عن الرمز في قاعدة البيانات
        reset_token = PasswordResetToken.query.filter_by(
            user_id=self.id,
            token=token
        ).first()

        # تحديث حالة الرمز
        if reset_token:
            reset_token.is_used = True
            db.session.commit()

    def update_last_login(self):
        """تحديث تاريخ آخر تسجيل دخول"""
        self.last_login = datetime.utcnow()
        db.session.commit()

    def log_activity(self, action, module, description=None, ip_address=None, user_agent=None):
        """تسجيل نشاط المستخدم"""
        from models.security import ActivityLog

        # إنشاء سجل النشاط
        activity = ActivityLog(
            user_id=self.id,
            action=action,
            module=module,
            description=description,
            ip_address=ip_address,
            user_agent=user_agent
        )

        # حفظ سجل النشاط في قاعدة البيانات
        db.session.add(activity)
        db.session.commit()

    def create_session(self, session_id, ip_address=None, user_agent=None):
        """إنشاء جلسة جديدة للمستخدم"""
        from models.security import UserSession

        # إنشاء سجل الجلسة
        session = UserSession(
            user_id=self.id,
            session_id=session_id,
            ip_address=ip_address,
            user_agent=user_agent
        )

        # حفظ سجل الجلسة في قاعدة البيانات
        db.session.add(session)
        db.session.commit()

        return session

    def get_active_sessions(self):
        """الحصول على جلسات المستخدم النشطة"""
        from models.security import UserSession

        # البحث عن الجلسات النشطة
        sessions = UserSession.query.filter_by(
            user_id=self.id,
            is_active=True
        ).all()

        return sessions

    def invalidate_all_sessions(self):
        """إبطال جميع جلسات المستخدم"""
        from models.security import UserSession

        # تحديث حالة جميع الجلسات
        UserSession.query.filter_by(
            user_id=self.id,
            is_active=True
        ).update({
            'is_active': False,
            'logout_at': datetime.utcnow()
        })

        db.session.commit()

    def to_dict(self):
        """تحويل بيانات المستخدم إلى قاموس"""
        return {
            'id': self.id,
            'username': self.username,
            'email': self.email,
            'full_name': self.full_name,
            'phone': self.phone,
            'position': self.position,
            'profile_image': self.profile_image,
            'is_active': self.is_active,
            'role': self.role,
            'last_login': self.last_login.strftime('%Y-%m-%d %H:%M:%S') if self.last_login else None,
            'created_at': self.created_at.strftime('%Y-%m-%d %H:%M:%S') if self.created_at else None,
            'updated_at': self.updated_at.strftime('%Y-%m-%d %H:%M:%S') if self.updated_at else None
        }
