from flask import Blueprint, render_template, request, jsonify, flash, redirect, url_for
from flask_login import login_required, current_user
from models import Customer, Order, OrderItem, Payment, ReturnOrder, Product
from app import db
from sqlalchemy import func, desc, and_, or_
from datetime import datetime, timedelta

customers_blueprint = Blueprint('customers', __name__)

@customers_blueprint.route('/customers')
@login_required
def index():
    # Get filter parameters
    search = request.args.get('search', '')

    # Prepare query
    query = Customer.query

    # Apply filters
    if search:
        query = query.filter(
            Customer.name.ilike(f'%{search}%') |
            Customer.phone.ilike(f'%{search}%') |
            Customer.email.ilike(f'%{search}%')
        )

    # Execute query with pagination
    page = request.args.get('page', 1, type=int)
    per_page = 10
    customers_paginated = query.order_by(Customer.name).paginate(page=page, per_page=per_page)

    # Get customer statistics
    total_customers = Customer.query.count()

    # Top customers by order value
    top_customers = db.session.query(
        Customer,
        func.sum(Order.total).label('total_spent')
    ).join(
        Order
    ).group_by(
        Customer.id
    ).order_by(
        func.sum(Order.total).desc()
    ).limit(5).all()

    # Recent customers
    recent_customers = Customer.query.order_by(Customer.created_at.desc()).limit(5).all()

    # Stats to pass to template
    stats = {
        'total_customers': total_customers,
        'top_customers': top_customers,
        'recent_customers': recent_customers
    }

    return render_template(
        'customers/customers.html',
        customers=customers_paginated,
        stats=stats,
        search=search,
        current_user=current_user
    )

@customers_blueprint.route('/customers/create', methods=['GET', 'POST'])
@login_required
def create():
    if request.method == 'POST':
        try:
            # Get form data
            name = request.form.get('name')
            phone = request.form.get('phone')
            email = request.form.get('email')
            address = request.form.get('address')

            # Create customer
            customer = Customer(
                name=name,
                phone=phone,
                email=email,
                address=address
            )

            db.session.add(customer)
            db.session.commit()

            flash('تم إضافة العميل بنجاح', 'success')
            return redirect(url_for('customers.index'))

        except Exception as e:
            db.session.rollback()
            flash(f'حدث خطأ أثناء إضافة العميل: {str(e)}', 'danger')

    return render_template('customers/customer_form.html', customer=None, action='create')

@customers_blueprint.route('/customers/<int:id>/edit', methods=['GET', 'POST'])
@login_required
def edit(id):
    customer = Customer.query.get_or_404(id)

    if request.method == 'POST':
        try:
            # Get form data
            customer.name = request.form.get('name')
            customer.phone = request.form.get('phone')
            customer.email = request.form.get('email')
            customer.address = request.form.get('address')

            db.session.commit()

            flash('تم تحديث بيانات العميل بنجاح', 'success')
            return redirect(url_for('customers.index'))

        except Exception as e:
            db.session.rollback()
            flash(f'حدث خطأ أثناء تحديث بيانات العميل: {str(e)}', 'danger')

    return render_template('customers/customer_form.html', customer=customer, action='edit')

@customers_blueprint.route('/customers/<int:id>/delete', methods=['POST'])
@login_required
def delete(id):
    customer = Customer.query.get_or_404(id)

    try:
        # Check if customer has orders
        if customer.orders:
            flash('لا يمكن حذف العميل لأنه لديه طلبات مسجلة', 'warning')
            return redirect(url_for('customers.index'))

        db.session.delete(customer)
        db.session.commit()
        flash('تم حذف العميل بنجاح', 'success')
    except Exception as e:
        db.session.rollback()
        flash(f'حدث خطأ أثناء حذف العميل: {str(e)}', 'danger')

    return redirect(url_for('customers.index'))

@customers_blueprint.route('/api/customers')
@login_required
def api_customers():
    search = request.args.get('search', '')

    query = Customer.query

    if search:
        query = query.filter(
            Customer.name.ilike(f'%{search}%') |
            Customer.phone.ilike(f'%{search}%')
        )

    customers = query.order_by(Customer.name).all()

    return jsonify({
        'customers': [customer.to_dict() for customer in customers]
    })

@customers_blueprint.route('/customers/<int:id>/details')
@login_required
def details(id):
    customer = Customer.query.get_or_404(id)

    # Get customer's orders
    orders = Order.query.filter_by(customer_id=id).order_by(Order.created_at.desc()).all()

    # Calculate total spent
    total_spent = db.session.query(func.sum(Order.total)).filter(Order.customer_id == id).scalar() or 0

    # Calculate number of orders
    order_count = len(orders)

    # Calculate average order value
    avg_order_value = total_spent / order_count if order_count > 0 else 0

    # Get customer's payments
    payments = Payment.query.join(Order).filter(Order.customer_id == id).order_by(Payment.payment_date.desc()).all()

    # حساب المبلغ المدفوع من المدفوعات الفعلية
    paid_amount = db.session.query(func.sum(Payment.amount)).join(Order).filter(Order.customer_id == id).scalar() or 0

    # حساب المبلغ المتبقي
    remaining_amount = total_spent - paid_amount

    # الحصول على الطلبات الآجلة
    deferred_orders = Order.query.filter(
        Order.customer_id == id,
        (Order.status == 'deferred') | (Order.payment_method == 'deferred')
    ).order_by(Order.created_at.desc()).all()

    # حساب إجمالي المبيعات الآجلة
    deferred_total = sum(order.total for order in deferred_orders)

    # حساب المبلغ المدفوع من المبيعات الآجلة
    deferred_paid = sum(
        sum(payment.amount for payment in order.payments)
        for order in deferred_orders if hasattr(order, 'payments')
    )

    # حساب المبلغ المتبقي من المبيعات الآجلة
    deferred_remaining = deferred_total - deferred_paid

    # Get customer's returns
    returns = ReturnOrder.query.join(Order).filter(Order.customer_id == id).order_by(ReturnOrder.created_at.desc()).all()

    # Calculate total returns amount
    returns_amount = db.session.query(func.sum(ReturnOrder.total_amount)).join(Order).filter(Order.customer_id == id).scalar() or 0

    # Get frequently purchased products
    frequently_purchased = db.session.query(
        Product,
        func.sum(OrderItem.quantity).label('total_quantity'),
        func.count(OrderItem.id).label('order_count'),
        func.max(Order.created_at).label('last_purchase_date')
    ).join(
        OrderItem, Product.id == OrderItem.product_id
    ).join(
        Order, OrderItem.order_id == Order.id
    ).filter(
        Order.customer_id == id
    ).group_by(
        Product.id
    ).order_by(
        func.sum(OrderItem.quantity).desc()
    ).limit(10).all()

    # إعداد إحصائيات العميل
    stats = {
        'total_spent': total_spent,
        'order_count': order_count,
        'avg_order_value': avg_order_value,
        'last_order_date': orders[0].created_at if orders else None,
        'paid_amount': paid_amount,
        'remaining_amount': remaining_amount,
        'returns_amount': returns_amount,
        'returns_count': len(returns),
        'deferred_orders_count': len(deferred_orders),
        'deferred_total': deferred_total,
        'deferred_paid': deferred_paid,
        'deferred_remaining': deferred_remaining
    }

    # Get monthly order data for reports
    monthly_orders = db.session.query(
        func.strftime('%Y-%m', Order.created_at).label('month'),
        func.sum(Order.total).label('total')
    ).filter(
        Order.customer_id == id,
        Order.created_at >= datetime.now() - timedelta(days=365)  # Last year
    ).group_by(
        func.strftime('%Y-%m', Order.created_at)
    ).order_by(
        func.strftime('%Y-%m', Order.created_at)
    ).all()

    # Get top products by purchase value
    top_products = db.session.query(
        Product,
        func.sum(OrderItem.total).label('total_value')
    ).join(
        OrderItem, Product.id == OrderItem.product_id
    ).join(
        Order, OrderItem.order_id == Order.id
    ).filter(
        Order.customer_id == id
    ).group_by(
        Product.id
    ).order_by(
        func.sum(OrderItem.total).desc()
    ).limit(5).all()

    # Prepare report data
    report_data = {
        'monthly_orders': monthly_orders,
        'top_products': top_products
    }

    return render_template(
        'customers/customer_details.html',
        customer=customer,
        orders=orders,
        payments=payments,
        returns=returns,
        frequently_purchased=frequently_purchased,
        stats=stats,
        report_data=report_data,
        current_user=current_user,
        now=datetime.now()
    )

@customers_blueprint.route('/api/customers/<int:id>/orders')
@login_required
def api_customer_orders(id):
    """الحصول على قائمة طلبات العميل"""
    try:
        # الحصول على معلمات البحث والفلترة
        search = request.args.get('search', '')
        date_from = request.args.get('date_from', '')
        date_to = request.args.get('date_to', '')
        status = request.args.get('status', '')

        # إعداد الاستعلام
        query = Order.query.filter(Order.customer_id == id)

        # تطبيق الفلاتر
        if search:
            query = query.filter(Order.invoice_number.ilike(f'%{search}%'))

        if date_from:
            try:
                date_from_obj = datetime.strptime(date_from, '%Y-%m-%d')
                query = query.filter(Order.created_at >= date_from_obj)
            except:
                pass

        if date_to:
            try:
                date_to_obj = datetime.strptime(date_to, '%Y-%m-%d')
                # Add one day to include the end date
                date_to_obj = date_to_obj + timedelta(days=1)
                query = query.filter(Order.created_at < date_to_obj)
            except:
                pass

        if status:
            query = query.filter(Order.status == status)

        # التصنيف والترقيم
        sort_by = request.args.get('sort_by', 'created_at')
        sort_dir = request.args.get('sort_dir', 'desc')

        if sort_dir == 'desc':
            query = query.order_by(getattr(Order, sort_by).desc())
        else:
            query = query.order_by(getattr(Order, sort_by).asc())

        # الحصول على نتائج الاستعلام
        orders = query.all()

        # تحويل النتائج إلى قاموس
        orders_data = []
        for order in orders:
            # حساب المبلغ المدفوع
            paid_amount = sum(payment.amount for payment in order.payments) if hasattr(order, 'payments') else 0

            orders_data.append({
                'id': order.id,
                'invoice_number': order.invoice_number,
                'created_at': order.created_at.strftime('%Y-%m-%d %H:%M'),
                'total': order.total,
                'paid_amount': paid_amount,
                'remaining_amount': order.total - paid_amount,
                'status': order.status,
                'payment_method': order.payment_method,
                'items_count': len(order.items) if hasattr(order, 'items') else 0
            })

        # إرجاع النتائج
        return jsonify({
            'orders': orders_data
        })
    except Exception as e:
        return jsonify({'error': f'فشل في الحصول على طلبات العميل: {str(e)}'}), 500

@customers_blueprint.route('/api/customers/<int:id>/payments')
@login_required
def api_customer_payments(id):
    """الحصول على قائمة مدفوعات العميل"""
    try:
        # الحصول على معلمات البحث والفلترة
        date_from = request.args.get('date_from', '')
        date_to = request.args.get('date_to', '')
        payment_method = request.args.get('payment_method', '')

        # إعداد الاستعلام
        query = Payment.query.join(Order).filter(Order.customer_id == id)

        # تطبيق الفلاتر
        if date_from:
            try:
                date_from_obj = datetime.strptime(date_from, '%Y-%m-%d')
                query = query.filter(Payment.payment_date >= date_from_obj)
            except:
                pass

        if date_to:
            try:
                date_to_obj = datetime.strptime(date_to, '%Y-%m-%d')
                # Add one day to include the end date
                date_to_obj = date_to_obj + timedelta(days=1)
                query = query.filter(Payment.payment_date < date_to_obj)
            except:
                pass

        if payment_method:
            query = query.filter(Payment.payment_method == payment_method)

        # التصنيف والترقيم
        sort_by = request.args.get('sort_by', 'payment_date')
        sort_dir = request.args.get('sort_dir', 'desc')

        if sort_dir == 'desc':
            query = query.order_by(getattr(Payment, sort_by).desc())
        else:
            query = query.order_by(getattr(Payment, sort_by).asc())

        # الحصول على نتائج الاستعلام
        payments = query.all()

        # تحويل النتائج إلى قاموس
        payments_data = []
        for payment in payments:
            payments_data.append({
                'id': payment.id,
                'reference_number': payment.reference_number,
                'order_invoice': payment.order.invoice_number if payment.order else '',
                'payment_date': payment.payment_date.strftime('%Y-%m-%d'),
                'amount': payment.amount,
                'payment_method': payment.payment_method,
                'notes': payment.notes
            })

        # إرجاع النتائج
        return jsonify({
            'payments': payments_data
        })
    except Exception as e:
        return jsonify({'error': f'فشل في الحصول على مدفوعات العميل: {str(e)}'}), 500

@customers_blueprint.route('/api/customers/<int:id>/returns')
@login_required
def api_customer_returns(id):
    """الحصول على قائمة مرتجعات العميل"""
    try:
        # الحصول على معلمات البحث والفلترة
        date_from = request.args.get('date_from', '')
        date_to = request.args.get('date_to', '')

        # إعداد الاستعلام
        query = ReturnOrder.query.join(Order).filter(Order.customer_id == id)

        # تطبيق الفلاتر
        if date_from:
            try:
                date_from_obj = datetime.strptime(date_from, '%Y-%m-%d')
                query = query.filter(ReturnOrder.created_at >= date_from_obj)
            except:
                pass

        if date_to:
            try:
                date_to_obj = datetime.strptime(date_to, '%Y-%m-%d')
                # Add one day to include the end date
                date_to_obj = date_to_obj + timedelta(days=1)
                query = query.filter(ReturnOrder.created_at < date_to_obj)
            except:
                pass

        # التصنيف والترقيم
        sort_by = request.args.get('sort_by', 'created_at')
        sort_dir = request.args.get('sort_dir', 'desc')

        if sort_dir == 'desc':
            query = query.order_by(getattr(ReturnOrder, sort_by).desc())
        else:
            query = query.order_by(getattr(ReturnOrder, sort_by).asc())

        # الحصول على نتائج الاستعلام
        returns = query.all()

        # تحويل النتائج إلى قاموس
        returns_data = []
        for return_order in returns:
            returns_data.append({
                'id': return_order.id,
                'reference_number': return_order.reference_number,
                'order_invoice': return_order.order.invoice_number if return_order.order else '',
                'created_at': return_order.created_at.strftime('%Y-%m-%d'),
                'total_amount': return_order.total_amount,
                'payment_method': return_order.payment_method,
                'status': return_order.status,
                'items_count': len(return_order.items) if hasattr(return_order, 'items') else 0
            })

        # إرجاع النتائج
        return jsonify({
            'returns': returns_data
        })
    except Exception as e:
        return jsonify({'error': f'فشل في الحصول على مرتجعات العميل: {str(e)}'}), 500

@customers_blueprint.route('/api/customers/<int:id>/deferred_orders')
@login_required
def api_customer_deferred_orders(id):
    """الحصول على قائمة المبيعات الآجلة للعميل"""
    try:
        # الحصول على معلمات البحث والفلترة
        search = request.args.get('search', '')
        date_from = request.args.get('date_from', '')
        date_to = request.args.get('date_to', '')

        # إعداد الاستعلام
        query = Order.query.filter(
            Order.customer_id == id,
            (Order.status == 'deferred') | (Order.payment_method == 'deferred')
        )

        # تطبيق الفلاتر
        if search:
            query = query.filter(Order.invoice_number.ilike(f'%{search}%'))

        if date_from:
            try:
                date_from_obj = datetime.strptime(date_from, '%Y-%m-%d')
                query = query.filter(Order.created_at >= date_from_obj)
            except:
                pass

        if date_to:
            try:
                date_to_obj = datetime.strptime(date_to, '%Y-%m-%d')
                # Add one day to include the end date
                date_to_obj = date_to_obj + timedelta(days=1)
                query = query.filter(Order.created_at < date_to_obj)
            except:
                pass

        # التصنيف والترقيم
        sort_by = request.args.get('sort_by', 'created_at')
        sort_dir = request.args.get('sort_dir', 'desc')

        if sort_dir == 'desc':
            query = query.order_by(getattr(Order, sort_by).desc())
        else:
            query = query.order_by(getattr(Order, sort_by).asc())

        # الحصول على نتائج الاستعلام
        orders = query.all()

        # تحويل النتائج إلى قاموس
        orders_data = []
        for order in orders:
            # حساب المبلغ المدفوع
            paid_amount = sum(payment.amount for payment in order.payments) if hasattr(order, 'payments') else 0

            orders_data.append({
                'id': order.id,
                'customer_id': order.customer_id,
                'invoice_number': order.invoice_number,
                'created_at': order.created_at.strftime('%Y-%m-%d %H:%M'),
                'total': order.total,
                'paid_amount': paid_amount,
                'remaining_amount': order.total - paid_amount,
                'status': order.status,
                'payment_method': order.payment_method,
                'items_count': len(order.items) if hasattr(order, 'items') else 0
            })

        # إرجاع النتائج
        return jsonify({
            'orders': orders_data
        })
    except Exception as e:
        return jsonify({'error': f'فشل في الحصول على المبيعات الآجلة للعميل: {str(e)}'}), 500

@customers_blueprint.route('/api/customers/<int:id>/products')
@login_required
def api_customer_products(id):
    """الحصول على قائمة المنتجات المشتراة من قبل العميل"""
    try:
        # الحصول على معلمات البحث
        search = request.args.get('search', '')

        # إعداد الاستعلام الأساسي
        base_query = db.session.query(
            Product,
            func.sum(OrderItem.quantity).label('total_quantity'),
            func.count(OrderItem.id).label('order_count'),
            func.max(Order.created_at).label('last_purchase_date')
        ).join(
            OrderItem, Product.id == OrderItem.product_id
        ).join(
            Order, OrderItem.order_id == Order.id
        ).filter(
            Order.customer_id == id
        )

        # تطبيق البحث
        if search:
            base_query = base_query.filter(
                or_(
                    Product.name.ilike(f'%{search}%'),
                    Product.code.ilike(f'%{search}%')
                )
            )

        # تجميع النتائج حسب المنتج
        customer_products = base_query.group_by(Product.id).all()

        # تحويل النتائج إلى قاموس
        products_data = []
        for product_data in customer_products:
            product = product_data[0]

            products_data.append({
                'id': product.id,
                'name': product.name,
                'code': product.code,
                'total_quantity': product_data[1],
                'order_count': product_data[2],
                'last_purchase_date': product_data[3].strftime('%Y-%m-%d') if product_data[3] else None,
                'price': product.price
            })

        # إرجاع النتائج
        return jsonify({
            'products': products_data
        })
    except Exception as e:
        return jsonify({'error': f'فشل في الحصول على منتجات العميل: {str(e)}'}), 500

@customers_blueprint.route('/customers/<int:id>/add-payment', methods=['GET', 'POST'])
@login_required
def add_payment(id):
    """إضافة دفعة جديدة للعميل"""
    customer = Customer.query.get_or_404(id)

    if request.method == 'POST':
        try:
            # الحصول على بيانات النموذج
            amount = float(request.form.get('amount', 0))
            payment_method = request.form.get('payment_method', 'cash')
            notes = request.form.get('notes', '')
            order_id = request.form.get('order_id', '')
            payment_date = request.form.get('payment_date', '')

            # التحقق من البيانات
            if amount <= 0:
                flash('يجب أن يكون المبلغ أكبر من صفر', 'danger')
                return redirect(url_for('customers.details', id=id))

            # تحويل تاريخ الدفع إلى كائن datetime
            if payment_date:
                try:
                    payment_date = datetime.strptime(payment_date, '%Y-%m-%d')
                except:
                    payment_date = datetime.now()
            else:
                payment_date = datetime.now()

            # إنشاء رقم مرجعي للدفعة
            reference_number = Payment.generate_reference_number()

            # إنشاء الدفعة
            payment = Payment(
                reference_number=reference_number,
                amount=amount,
                payment_method=payment_method,
                payment_date=payment_date,
                notes=notes,
                created_by=current_user.id
            )

            # إذا تم تحديد طلب معين
            if order_id and order_id.isdigit():
                order = Order.query.get(int(order_id))
                if order and order.customer_id == customer.id:
                    payment.order_id = order.id

                    # تحديث حالة الطلب إذا تم دفع كامل المبلغ
                    total_paid = sum(p.amount for p in order.payments) + amount
                    if total_paid >= order.total:
                        order.status = 'completed'
                        flash('تم دفع كامل المبلغ وتحديث حالة الطلب إلى مكتمل', 'success')
                    elif order.status == 'deferred':
                        # تحديث خطة الدفع إن وجدت
                        payment_plan = DeferredPaymentPlan.query.filter_by(order_id=order.id).first()
                        if payment_plan:
                            # البحث عن القسط المستحق
                            installment = DeferredPaymentInstallment.query.filter_by(
                                plan_id=payment_plan.id,
                                status='pending'
                            ).order_by(DeferredPaymentInstallment.due_date).first()

                            if installment:
                                installment.mark_as_paid(payment.id, payment_date)

            # إنشاء معاملة في الخزينة إذا كان الدفع نقدي
            if payment_method == 'cash':
                # البحث عن الخزينة الافتراضية
                default_register = CashRegister.query.filter_by(is_default=True).first()

                if default_register:
                    # إنشاء معاملة إيداع في الخزينة
                    transaction = CashTransaction(
                        cash_register_id=default_register.id,
                        transaction_type='deposit',
                        amount=amount,
                        previous_balance=default_register.current_balance,
                        new_balance=default_register.current_balance + amount,
                        reference=f'payment_{reference_number}',
                        notes=f'دفعة من العميل {customer.name}' + (f' للطلب {order.invoice_number}' if payment.order_id else ''),
                        created_by=current_user.id
                    )
                    db.session.add(transaction)

                    # تحديث رصيد الخزينة
                    default_register.current_balance += amount

            db.session.add(payment)
            db.session.commit()

            flash('تم إضافة الدفعة بنجاح', 'success')

        except Exception as e:
            db.session.rollback()
            flash(f'حدث خطأ أثناء إضافة الدفعة: {str(e)}', 'danger')

        return redirect(url_for('customers.details', id=id))

    # الحصول على طلبات العميل المؤجلة أو غير المكتملة
    orders = Order.query.filter(
        Order.customer_id == id,
        (Order.status == 'deferred') | (Order.payment_method == 'deferred') |
        ((Order.total > db.session.query(func.sum(Payment.amount)).filter(Payment.order_id == Order.id).scalar_subquery()) &
         (Order.status != 'cancelled') &
         (Order.status != 'returned'))
    ).order_by(Order.created_at.desc()).all()

    # تحديد الطلب المحدد إذا تم تمريره في الـ URL
    selected_order_id = request.args.get('order_id', None, type=int)

    # الحصول على إحصائيات العميل
    total_spent = db.session.query(func.sum(Order.total)).filter(Order.customer_id == id).scalar() or 0
    paid_amount = db.session.query(func.sum(Payment.amount)).join(Order).filter(Order.customer_id == id).scalar() or 0
    remaining_amount = total_spent - paid_amount
    order_count = Order.query.filter_by(customer_id=id).count()

    stats = {
        'total_spent': total_spent,
        'paid_amount': paid_amount,
        'remaining_amount': remaining_amount,
        'order_count': order_count
    }

    return render_template(
        'customer_payment.html',
        customer=customer,
        orders=orders,
        stats=stats,
        now=datetime.now(),
        current_user=current_user,
        selected_order_id=selected_order_id
    )

@customers_blueprint.route('/api/customers/<int:id>/reports')
@login_required
def api_customer_reports(id):
    """الحصول على تقارير العميل"""
    try:
        # الحصول على إحصائيات الطلبات الشهرية
        monthly_orders = db.session.query(
            func.strftime('%Y-%m', Order.created_at).label('month'),
            func.sum(Order.total).label('total')
        ).filter(
            Order.customer_id == id,
            Order.created_at >= datetime.now() - timedelta(days=365)  # Last year
        ).group_by(
            func.strftime('%Y-%m', Order.created_at)
        ).order_by(
            func.strftime('%Y-%m', Order.created_at)
        ).all()

        # تحويل النتائج إلى قاموس
        monthly_data = [{'month': month, 'total': total} for month, total in monthly_orders]

        # الحصول على المنتجات الأكثر شراءً
        top_products = db.session.query(
            Product.id,
            Product.name,
            func.sum(OrderItem.quantity).label('quantity'),
            func.sum(OrderItem.total).label('total_value')
        ).join(
            OrderItem, Product.id == OrderItem.product_id
        ).join(
            Order, OrderItem.order_id == Order.id
        ).filter(
            Order.customer_id == id
        ).group_by(
            Product.id
        ).order_by(
            func.sum(OrderItem.total).desc()
        ).limit(10).all()

        # تحويل النتائج إلى قاموس
        top_products_data = [{
            'id': id,
            'name': name,
            'quantity': quantity,
            'total_value': total_value
        } for id, name, quantity, total_value in top_products]

        # الحصول على إحصائيات المدفوعات
        payment_stats = db.session.query(
            func.strftime('%Y-%m', Payment.payment_date).label('month'),
            func.sum(Payment.amount).label('total')
        ).join(
            Order, Payment.order_id == Order.id
        ).filter(
            Order.customer_id == id,
            Payment.payment_date >= datetime.now() - timedelta(days=365)  # Last year
        ).group_by(
            func.strftime('%Y-%m', Payment.payment_date)
        ).order_by(
            func.strftime('%Y-%m', Payment.payment_date)
        ).all()

        # تحويل النتائج إلى قاموس
        payment_data = [{'month': month, 'total': total} for month, total in payment_stats]

        # إرجاع النتائج
        return jsonify({
            'monthly_orders': monthly_data,
            'top_products': top_products_data,
            'payment_stats': payment_data
        })
    except Exception as e:
        return jsonify({'error': f'فشل في الحصول على تقارير العميل: {str(e)}'}), 500