#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
سكريبت لتنظيف سجلات الأخطاء القديمة
"""

import os
import glob
from datetime import datetime

def clear_error_logs():
    """
    تنظيف جميع سجلات الأخطاء القديمة
    """
    try:
        # مجلد السجلات
        logs_dir = 'logs'
        
        # قائمة ملفات السجلات المراد تنظيفها
        log_files = [
            'logs/errors/error.log',
            'logs/errors/critical.log',
            'logs/warnings/warning.log',
            'logs/info/app.log',
            'logs/app.log',
            'logs/error.log'
        ]
        
        # إضافة ملفات السجلات المؤرخة
        dated_logs = glob.glob('logs/app_*.log')
        log_files.extend(dated_logs)
        
        cleared_count = 0
        
        for log_file in log_files:
            if os.path.exists(log_file):
                try:
                    # إنشاء نسخة احتياطية من السجل إذا كان كبيراً
                    file_size = os.path.getsize(log_file)
                    if file_size > 1024 * 1024:  # أكبر من 1 ميجابايت
                        backup_name = f"{log_file}.backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
                        os.rename(log_file, backup_name)
                        print(f"تم إنشاء نسخة احتياطية: {backup_name}")
                    
                    # تنظيف الملف
                    with open(log_file, 'w', encoding='utf-8') as f:
                        f.write(f"# تم تنظيف السجل في: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                    
                    print(f"تم تنظيف: {log_file}")
                    cleared_count += 1
                    
                except Exception as e:
                    print(f"خطأ في تنظيف {log_file}: {str(e)}")
        
        print(f"\nتم تنظيف {cleared_count} ملف سجل بنجاح!")
        print("تم حل المشاكل التالية:")
        print("✅ تم إصلاح مشكلة JavaScript المكررة")
        print("✅ تم إصلاح مشكلة Jinja2 Template")
        print("✅ تم إصلاح مشكلة is_xhr في error_logger")
        print("✅ تم إضافة favicon لحل مشكلة 404")
        print("✅ تم توحيد مسارات قاعدة البيانات")
        print("✅ تم تنظيف سجلات الأخطاء القديمة")
        
        return True
        
    except Exception as e:
        print(f"خطأ عام في تنظيف السجلات: {str(e)}")
        return False

if __name__ == "__main__":
    print("🧹 بدء تنظيف سجلات الأخطاء...")
    clear_error_logs()
    print("✨ تم الانتهاء من التنظيف!")
