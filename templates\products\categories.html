<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>FoUaD - إدارة التصنيفات</title>
    <script src="https://cdn.tailwindcss.com/3.4.16"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#6366F1',
                        secondary: '#10b981'
                    },
                    borderRadius: {
                        'none': '0px',
                        'sm': '4px',
                        DEFAULT: '8px',
                        'md': '12px',
                        'lg': '16px',
                        'xl': '20px',
                        '2xl': '24px',
                        '3xl': '32px',
                        'full': '9999px',
                        'button': '8px'
                    }
                }
            }
        }
    </script>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@400;500;700&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Pacifico&display=swap" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/remixicon@3.5.0/fonts/remixicon.css" rel="stylesheet">
    <style>
        :where([class^="ri-"])::before { content: "\f3c2"; }
        body {
            font-family: 'Tajawal', sans-serif;
            background-color: #f9fafb;
        }
        .scrollbar-hide::-webkit-scrollbar {
            display: none;
        }
        .scrollbar-hide {
            -ms-overflow-style: none;
            scrollbar-width: none;
        }
        .bg-pattern {
            background-image: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%236366F1' fill-opacity='0.05'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
        }
        .glass-effect {
            backdrop-filter: blur(8px);
            background: rgba(255, 255, 255, 0.8);
            border: 1px solid rgba(255, 255, 255, 0.3);
            box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.07);
        }
        .pulse-animation {
            animation: pulse 2s infinite;
        }
        @keyframes pulse {
            0% {
                box-shadow: 0 0 0 0 rgba(99, 102, 241, 0.4);
            }
            70% {
                box-shadow: 0 0 0 10px rgba(99, 102, 241, 0);
            }
            100% {
                box-shadow: 0 0 0 0 rgba(99, 102, 241, 0);
            }
        }
        .category-card {
            transition: all 0.3s ease;
        }
        .category-card:hover {
            transform: translateY(-5px);
        }
    </style>
</head>
<body class="bg-pattern">
    <div class="flex h-screen overflow-hidden">
        <!-- Sidebar -->
        {% include 'partials/sidebar.html' %}
        
        <!-- Main Content -->
        <div class="flex-1 flex flex-col overflow-hidden">
            <!-- Top Navigation -->
            {% include 'partials/topnav.html' %}
            
            <!-- Content Area -->
            <main class="flex-1 overflow-y-auto p-6">
                <div class="mb-6 flex flex-wrap items-center justify-between">
                    <div>
                        <h1 class="text-2xl font-bold text-gray-800">إدارة التصنيفات</h1>
                        <p class="text-gray-600">عرض وإدارة تصنيفات المنتجات في النظام</p>
                    </div>
                    <a href="{{ url_for('products.create_category') }}" class="bg-gradient-to-r from-primary to-indigo-600 text-white px-4 py-2.5 rounded-lg shadow-md hover:shadow-lg transition-all duration-300 flex items-center gap-2 pulse-animation !rounded-button whitespace-nowrap">
                        <div class="w-5 h-5 flex items-center justify-center">
                            <i class="ri-add-line"></i>
                        </div>
                        <span>إضافة تصنيف جديد</span>
                    </a>
                </div>
                
                <!-- Categories Grid -->
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                    {% for category in categories %}
                    <div class="category-card glass-effect rounded-lg overflow-hidden">
                        <div class="h-2 {{ 'bg-blue-500' if category.color == 'blue' else 'bg-green-500' if category.color == 'green' else 'bg-purple-500' if category.color == 'purple' else 'bg-red-500' if category.color == 'red' else 'bg-yellow-500' if category.color == 'yellow' else 'bg-gray-500' }}"></div>
                        <div class="p-5">
                            <div class="flex justify-between items-start mb-3">
                                <div>
                                    <h3 class="font-medium text-gray-800">{{ category.name }}</h3>
                                    <p class="text-sm text-gray-500 mt-1 line-clamp-2">{{ category.description or 'لا يوجد وصف' }}</p>
                                </div>
                                <div class="w-10 h-10 rounded-full {{ 'bg-blue-100' if category.color == 'blue' else 'bg-green-100' if category.color == 'green' else 'bg-purple-100' if category.color == 'purple' else 'bg-red-100' if category.color == 'red' else 'bg-yellow-100' if category.color == 'yellow' else 'bg-gray-100' }} flex items-center justify-center {{ 'text-blue-500' if category.color == 'blue' else 'text-green-500' if category.color == 'green' else 'text-purple-500' if category.color == 'purple' else 'text-red-500' if category.color == 'red' else 'text-yellow-500' if category.color == 'yellow' else 'text-gray-500' }}">
                                    <i class="ri-price-tag-3-line"></i>
                                </div>
                            </div>
                            
                            <div class="flex items-center justify-between pt-3 border-t border-gray-100">
                                <div class="text-sm text-gray-500">
                                    <span class="font-medium">{{ category.products|length }}</span> منتج
                                </div>
                                <div class="flex space-x-2 space-x-reverse">
                                    <a href="{{ url_for('products.edit_category', id=category.id) }}" class="text-indigo-600 hover:text-indigo-900 p-1 rounded-full hover:bg-indigo-50 transition-all">
                                        <div class="w-5 h-5 flex items-center justify-center">
                                            <i class="ri-edit-line"></i>
                                        </div>
                                    </a>
                                    
                                    <button type="button" onclick="confirmDelete({{ category.id }}, '{{ category.name }}')" class="text-red-600 hover:text-red-900 p-1 rounded-full hover:bg-red-50 transition-all">
                                        <div class="w-5 h-5 flex items-center justify-center">
                                            <i class="ri-delete-bin-line"></i>
                                        </div>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                    {% else %}
                    <div class="col-span-full">
                        <div class="glass-effect rounded-lg p-10 text-center">
                            <div class="flex flex-col items-center">
                                <div class="w-16 h-16 rounded-full bg-gray-100 flex items-center justify-center text-gray-400 mb-4">
                                    <i class="ri-price-tag-3-line ri-2x"></i>
                                </div>
                                <h3 class="text-lg font-medium text-gray-800 mb-2">لا توجد تصنيفات</h3>
                                <p class="text-gray-500 max-w-md mb-6">لم يتم إضافة تصنيفات بعد، يمكنك إضافة تصنيفات جديدة لتنظيم المنتجات الخاصة بك.</p>
                                <a href="{{ url_for('products.create_category') }}" class="px-4 py-2 bg-primary text-white rounded-md hover:bg-indigo-700 transition-all">
                                    إضافة تصنيف جديد
                                </a>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
            </main>
        </div>
    </div>
    
    <!-- Delete Confirmation Modal -->
    <div id="deleteModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden">
        <div class="bg-white rounded-lg w-80 sm:w-96 overflow-hidden">
            <div class="p-5">
                <div class="flex items-center justify-center w-12 h-12 rounded-full bg-red-100 text-red-600 mx-auto mb-4">
                    <i class="ri-delete-bin-line ri-lg"></i>
                </div>
                <h3 class="text-lg font-medium text-gray-900 text-center mb-2">تأكيد الحذف</h3>
                <p class="text-sm text-gray-500 text-center mb-5">
                    هل أنت متأكد من حذف التصنيف:<br>
                    <span id="deleteCategoryName" class="font-medium"></span>؟
                </p>
                <div class="flex justify-center gap-2">
                    <button id="confirmDelete" class="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 transition-all !rounded-button whitespace-nowrap text-sm">
                        نعم، حذف
                    </button>
                    <button id="cancelDelete" class="px-4 py-2 bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 transition-all !rounded-button whitespace-nowrap text-sm">
                        إلغاء
                    </button>
                </div>
            </div>
        </div>
    </div>
    
    <form id="deleteForm" method="POST" style="display: none;">
    </form>
    
    <script>
        let categoryIdToDelete = null;
        const deleteModal = document.getElementById('deleteModal');
        const deleteForm = document.getElementById('deleteForm');
        const deleteCategoryName = document.getElementById('deleteCategoryName');
        
        function confirmDelete(id, name) {
            categoryIdToDelete = id;
            deleteCategoryName.textContent = name;
            deleteModal.classList.remove('hidden');
        }
        
        document.getElementById('confirmDelete').addEventListener('click', function() {
            if (categoryIdToDelete) {
                deleteForm.action = `/categories/${categoryIdToDelete}/delete`;
                deleteForm.submit();
            }
        });
        
        document.getElementById('cancelDelete').addEventListener('click', function() {
            closeModal();
        });
        
        document.addEventListener('click', function(event) {
            if (event.target === deleteModal) {
                closeModal();
            }
        });
        
        function closeModal() {
            deleteModal.classList.add('hidden');
            categoryIdToDelete = null;
        }
    </script>
</body>
</html>