// Global variables
let products = [];
let cartItems = [];
let suspendedInvoices = [];
let selectedCategory = 'all';
let selectedWarehouse = '';
let selectedPaymentMethod = 'cash';
let currentItemDiscountIndex = -1;
let currentItemDiscountType = 'fixed';
let selectedCustomerId = '';
let selectedCustomerName = 'عميل نقدي';

// Initialize page on load
document.addEventListener('DOMContentLoaded', function() {
    // تعيين المخزن المحدد من القائمة المنسدلة
    const warehouseSelect = document.getElementById('warehouse-select');
    if (warehouseSelect) {
        selectedWarehouse = warehouseSelect.value;

        // إضافة مستمع حدث لتغيير المخزن
        warehouseSelect.addEventListener('change', function() {
            selectedWarehouse = this.value;
            // إعادة تحميل المنتجات من المخزن الجديد
            loadProducts();
            // عرض إشعار بتغيير المخزن
            showNotification(`تم تغيير المخزن إلى ${this.options[this.selectedIndex].text}`, 'info');
        });
    }

    loadProducts();
    setupEventListeners();
    updateCartDisplay();
    loadSuspendedInvoices();
    setupNumpad();
    // setupCustomerSearch() is now called from customer_search.js
});

// Load products via API
function loadProducts(search = '') {
    // بناء عنوان URL مع المعلمات المطلوبة
    let url = '/api/pos/products';
    let params = [];

    // إضافة معلمة التصنيف إذا كانت محددة
    if (selectedCategory !== 'all') {
        params.push(`category_id=${selectedCategory}`);
    }

    // إضافة معلمة البحث إذا كانت موجودة
    if (search) {
        params.push(`search=${search}`);
    }

    // إضافة معلمة المخزن إذا كانت محددة
    if (selectedWarehouse) {
        params.push(`warehouse_id=${selectedWarehouse}`);
    }

    // إضافة المعلمات إلى عنوان URL
    if (params.length > 0) {
        url += '?' + params.join('&');
    }

    console.log('Loading products with URL:', url);

    // Show loading state with improved styling
    const container = document.getElementById('products-container');
    container.innerHTML = `
        <div class="flex items-center justify-center h-full col-span-full text-gray-400">
            <div class="text-center">
                <div class="w-20 h-20 mx-auto rounded-full bg-gradient-to-br from-blue-50 to-blue-100 flex items-center justify-center mb-4 shadow-md">
                    <i class="ri-loader-4-line text-3xl text-blue-400 animate-spin"></i>
                </div>
                <p class="text-base font-medium text-gray-500">جاري تحميل المنتجات...</p>
                <p class="text-sm mt-2 text-gray-400">يرجى الانتظار قليلاً</p>
            </div>
        </div>
    `;

    console.log('Loading products from URL:', url);

    // Add a small delay to show loading indicator
    setTimeout(() => {
        fetch(url)
            .then(response => {
                if (!response.ok) {
                    throw new Error(`Network response was not ok: ${response.status} ${response.statusText}`);
                }
                return response.json();
            })
            .then(data => {
                if (!data || !data.products) {
                    console.error('Invalid data format received:', data);
                    throw new Error('بيانات غير صالحة تم استلامها من الخادم');
                }

                console.log(`Loaded ${data.products.length} products successfully`);
                products = data.products;
                renderProducts();

                // Show success notification
                if (search || selectedCategory !== 'all') {
                    showNotification(`تم تحميل ${data.products.length} منتج بنجاح`, 'success');
                }
            })
            .catch(error => {
                console.error('Error loading products:', error);
                container.innerHTML = `
                    <div class="flex items-center justify-center h-full col-span-full text-gray-400">
                        <div class="text-center">
                            <div class="w-20 h-20 mx-auto rounded-full bg-gradient-to-br from-red-50 to-red-100 flex items-center justify-center mb-4 shadow-md">
                                <i class="ri-error-warning-line text-3xl text-red-400"></i>
                            </div>
                            <p class="text-base font-medium text-gray-500">حدث خطأ أثناء تحميل المنتجات</p>
                            <p class="text-sm mt-2 text-gray-400">${error.message}</p>
                            <button id="retry-load" class="mt-4 px-4 py-2 bg-blue-500 text-white rounded-lg text-sm hover:bg-blue-600 transition-all duration-300 flex items-center mx-auto">
                                <i class="ri-refresh-line mr-1"></i>
                                إعادة المحاولة
                            </button>
                        </div>
                    </div>
                `;

                document.getElementById('retry-load').addEventListener('click', () => loadProducts(search));
            });
    }, 300);
}

// Render products in the grid
function renderProducts() {
    const container = document.getElementById('products-container');
    const template = document.getElementById('product-template');

    // Clear container
    container.innerHTML = '';

    // Show message if no products
    if (products.length === 0) {
        container.innerHTML = `
            <div class="flex items-center justify-center h-full col-span-full text-gray-400">
                <div class="text-center">
                    <div class="w-20 h-20 mx-auto rounded-full bg-gradient-to-br from-blue-50 to-blue-100 flex items-center justify-center mb-4 shadow-md">
                        <i class="ri-shopping-bag-line text-3xl text-blue-400"></i>
                    </div>
                    <p class="text-base font-medium text-gray-500">لا توجد منتجات متاحة</p>
                    <p class="text-sm mt-2 text-gray-400">جرب البحث بكلمات أخرى أو تغيير التصنيف</p>
                    <button id="refresh-products" class="mt-4 px-4 py-2 bg-blue-500 text-white rounded-lg text-sm hover:bg-blue-600 transition-all duration-300 flex items-center mx-auto">
                        <i class="ri-refresh-line mr-1"></i>
                        تحديث المنتجات
                    </button>
                </div>
            </div>
        `;

        // Add event listener to refresh button
        setTimeout(() => {
            const refreshBtn = document.getElementById('refresh-products');
            if (refreshBtn) {
                refreshBtn.addEventListener('click', () => loadProducts());
            }
        }, 0);

        return;
    }

    console.log(`Rendering ${products.length} products`);

    // Render each product
    products.forEach(product => {
        try {
            const clone = template.content.cloneNode(true);
            const card = clone.querySelector('.product-card');
            const image = clone.querySelector('.product-image');
            const imageBackground = clone.querySelector('.product-image div:first-child');
            const name = clone.querySelector('.product-name');
            const price = clone.querySelector('.product-price');
            const stock = clone.querySelector('.product-stock');
            const addButton = clone.querySelector('.product-image button');

            // Set product data
            name.textContent = product.name;
            price.textContent = `${product.price.toFixed(2)} ج.م`;

            // Set product image or placeholder
            if (product.image) {
                imageBackground.style.backgroundImage = `url('/static/uploads/${product.image}')`;
            } else {
                // Add placeholder icon to the image div
                const placeholderIcon = document.createElement('i');
                placeholderIcon.className = 'ri-shopping-bag-line text-3xl text-gray-400 absolute';
                image.appendChild(placeholderIcon);
            }

            // Set stock status
            if (product.stock_status === 'out_of_stock') {
                stock.textContent = 'نفذ';
                stock.classList.add('bg-red-100', 'text-red-800');
                card.classList.add('opacity-70');

                // Disable add button for out of stock products
                if (addButton) {
                    addButton.classList.add('hidden');
                }
            } else if (product.stock_status === 'low_stock') {
                stock.textContent = 'منخفض';
                stock.classList.add('bg-yellow-100', 'text-yellow-800');
            } else {
                stock.textContent = 'متوفر';
                stock.classList.add('bg-green-100', 'text-green-800');
            }

            // Add click event to add to cart
            card.addEventListener('click', () => {
                if (product.stock_status !== 'out_of_stock') {
                    addToCart(product);
                    card.classList.add('scale-95');
                    setTimeout(() => {
                        card.classList.remove('scale-95');
                    }, 150);
                } else {
                    showNotification('المنتج غير متوفر في المخزون', 'error');
                }
            });

            // Add click event to quick add button
            if (addButton) {
                addButton.addEventListener('click', (e) => {
                    e.stopPropagation(); // Prevent card click event
                    if (product.stock_status !== 'out_of_stock') {
                        addToCart(product);
                        addButton.classList.add('scale-90');
                        setTimeout(() => {
                            addButton.classList.remove('scale-90');
                        }, 150);
                    } else {
                        showNotification('المنتج غير متوفر في المخزون', 'error');
                    }
                });
            }

            container.appendChild(clone);
        } catch (error) {
            console.error(`Error rendering product ${product.id}:`, error);
        }
    });

    console.log('Products rendered successfully');
}

// Add product to cart
function addToCart(product) {
    // Validate product
    if (!product) {
        console.error('Invalid product (null or undefined)');
        showNotification('خطأ: المنتج غير صالح', 'error');
        return;
    }

    // Ensure product has an ID
    if (!product.id) {
        console.error('Product has no ID:', product);
        showNotification('خطأ: المنتج ليس له معرف', 'error');
        return;
    }

    // Convert ID to number if it's a string
    let productId = product.id;
    if (typeof productId === 'string' && !isNaN(parseInt(productId))) {
        productId = parseInt(productId);
    }

    // Check if product is already in cart
    const existingItem = cartItems.find(item => item.id === productId);

    if (existingItem) {
        // Increase quantity if possible
        if (existingItem.quantity < product.stock_quantity) {
            existingItem.quantity += 1;
            updateItemTotal(existingItem);

            // Play add sound
            playSound('add');

            // Show notification with animation
            showNotification(`تم زيادة كمية ${product.name} (${existingItem.quantity})`, 'success');
        } else {
            showNotification('لا يمكن إضافة المزيد من هذا المنتج، الكمية المتاحة غير كافية', 'warning');
        }
    } else {
        // Make sure product has all required properties
        if (typeof product.price !== 'number' || isNaN(product.price)) {
            console.error('Invalid product price:', product.price);
            showNotification('خطأ: سعر المنتج غير صالح', 'error');
            return;
        }

        // Add new item with enhanced properties
        const newItem = {
            id: productId,
            name: product.name || 'منتج غير معروف',
            price: product.price,
            original_price: product.price,
            quantity: 1,
            total: product.price,
            max_quantity: product.stock_quantity || 1,
            discount: 0,
            discount_type: 'fixed',
            image_path: product.image_path || null,
            code: product.code || '',
            category_name: product.category_name || '',
            added_at: new Date().toISOString()
        };

        cartItems.push(newItem);

        // Play add sound
        playSound('add');

        // Show notification with animation
        showNotification(`تم إضافة ${product.name} إلى السلة`, 'success');
    }

    // Enable checkout button
    document.getElementById('checkout-btn').disabled = false;

    // Update cart display with animation
    updateCartDisplay();
}

// Play sound effect
function playSound(type) {
    // يمكن إضافة أصوات مختلفة هنا في المستقبل
    if (type === 'add') {
        // يمكن إضافة صوت هنا
    } else if (type === 'remove') {
        // يمكن إضافة صوت هنا
    }
}

// Remove item from cart
function removeFromCart(index) {
    cartItems.splice(index, 1);
    updateCartDisplay();
}

// Update item quantity
function updateItemQuantity(index, newQuantity) {
    const item = cartItems[index];
    const product = products.find(p => p.id === item.id);

    // Validate quantity
    if (newQuantity <= 0) {
        if (confirm('هل تريد إزالة هذا المنتج من السلة؟')) {
            removeFromCart(index);
        }
        return;
    }

    if (newQuantity > item.max_quantity) {
        showNotification('الكمية المطلوبة غير متوفرة في المخزون', 'warning');
        return;
    }

    // Update quantity
    item.quantity = newQuantity;
    updateItemTotal(item);
    updateCartDisplay();
}

// Update item total price
function updateItemTotal(item) {
    // Calculate price after discount
    let priceAfterDiscount = item.price;
    if (item.discount > 0) {
        if (item.discount_type === 'percentage') {
            priceAfterDiscount = item.price * (1 - (item.discount / 100));
        } else {
            priceAfterDiscount = item.price - item.discount;
        }
    }

    // Update total
    item.total = priceAfterDiscount * item.quantity;
}

// Apply discount to item
function applyItemDiscount(index, discount, discountType) {
    const item = cartItems[index];

    // Validate discount
    if (discountType === 'fixed' && discount > item.price) {
        showNotification('قيمة الخصم لا يمكن أن تكون أكبر من سعر المنتج', 'error');
        return false;
    }

    if (discountType === 'percentage' && discount > 100) {
        showNotification('نسبة الخصم لا يمكن أن تكون أكبر من 100%', 'error');
        return false;
    }

    // Apply discount
    item.discount = discount;
    item.discount_type = discountType;
    updateItemTotal(item);
    updateCartDisplay();
    return true;
}

// Apply discount to entire cart
function applyCartDiscount(discount, discountType) {
    // Validate discount
    if (discount <= 0) {
        return;
    }

    const subtotal = calculateSubtotal();

    if (discountType === 'fixed' && discount > subtotal) {
        showNotification('قيمة الخصم لا يمكن أن تكون أكبر من إجمالي السلة', 'error');
        return;
    }

    if (discountType === 'percentage' && discount > 100) {
        showNotification('نسبة الخصم لا يمكن أن تكون أكبر من 100%', 'error');
        return;
    }

    // Calculate discount amount
    let discountAmount;
    if (discountType === 'percentage') {
        discountAmount = subtotal * (discount / 100);
    } else {
        discountAmount = discount;
    }

    // Distribute discount proportionally among items
    const totalBeforeDiscount = subtotal;
    let remainingDiscount = discountAmount;

    for (let i = 0; i < cartItems.length; i++) {
        const item = cartItems[i];
        const itemSubtotal = item.price * item.quantity;
        const itemProportion = itemSubtotal / totalBeforeDiscount;
        const itemDiscountAmount = i === cartItems.length - 1 ? remainingDiscount : Math.round(discountAmount * itemProportion * 100) / 100;

        if (discountType === 'percentage') {
            item.discount = discount;
            item.discount_type = 'percentage';
        } else {
            item.discount = itemDiscountAmount / item.quantity;
            item.discount_type = 'fixed';
        }

        updateItemTotal(item);
        remainingDiscount -= itemDiscountAmount;
    }

    updateCartDisplay();
}

// Calculate subtotal (before discount and tax)
function calculateSubtotal() {
    return cartItems.reduce((sum, item) => sum + (item.price * item.quantity), 0);
}

// Calculate total discount amount
function calculateDiscount() {
    return cartItems.reduce((sum, item) => {
        const itemPrice = item.price * item.quantity;
        const itemTotal = item.total;
        return sum + (itemPrice - itemTotal);
    }, 0);
}

// Calculate tax amount
function calculateTax() {
    const taxPercentage = parseFloat(document.getElementById('tax-percentage').value) || 0;
    const subtotalAfterDiscount = cartItems.reduce((sum, item) => sum + item.total, 0);
    return subtotalAfterDiscount * (taxPercentage / 100);
}

// Calculate total (after discount and tax)
function calculateTotal() {
    const subtotal = cartItems.reduce((sum, item) => sum + item.total, 0);
    const tax = calculateTax();
    return subtotal + tax;
}

// Update cart display
function updateCartDisplay() {
    const container = document.getElementById('cart-items');
    const template = document.getElementById('cart-item-template');

    // Clear container
    container.innerHTML = '';

    if (cartItems.length === 0) {
        // Show empty cart message with improved styling
        container.innerHTML = `
            <div class="flex flex-col items-center justify-center h-full text-gray-400">
                <div class="w-20 h-20 rounded-full bg-gradient-to-br from-blue-50 to-blue-100 flex items-center justify-center mb-3 shadow-sm">
                    <i class="ri-shopping-cart-line text-3xl text-blue-400"></i>
                </div>
                <p class="text-sm font-medium">سلة المشتريات فارغة</p>
                <p class="text-xs mt-2 text-gray-500">قم بإضافة منتجات من القائمة لبدء عملية البيع</p>
                <button id="browse-products-btn" class="mt-4 px-4 py-2 bg-blue-500 text-white rounded-lg text-sm hover:bg-blue-600 transition-all duration-300 flex items-center">
                    <i class="ri-shopping-basket-line mr-1"></i>
                    تصفح المنتجات
                </button>
            </div>
        `;

        // Add event listener to the browse products button
        setTimeout(() => {
            const browseBtn = document.getElementById('browse-products-btn');
            if (browseBtn) {
                browseBtn.addEventListener('click', () => {
                    // يمكن إضافة تأثير لتسليط الضوء على قسم المنتجات
                    const productsContainer = document.getElementById('products-container');
                    if (productsContainer) {
                        productsContainer.scrollIntoView({ behavior: 'smooth' });
                        productsContainer.classList.add('highlight-container');
                        setTimeout(() => {
                            productsContainer.classList.remove('highlight-container');
                        }, 1500);
                    }
                });
            }
        }, 0);

        // Disable checkout button
        document.getElementById('checkout-btn').disabled = true;

        // Update summary
        document.getElementById('cart-subtotal').textContent = '0.00 ج.م';
        document.getElementById('cart-discount').textContent = '- 0.00 ج.م';
        document.getElementById('cart-tax').textContent = '+ 0.00 ج.م';
        document.getElementById('cart-total').textContent = '0.00 ج.م';

        return;
    }

    // Render each cart item with enhanced styling
    cartItems.forEach((item, index) => {
        const clone = template.content.cloneNode(true);
        const card = clone.querySelector('.cart-item');
        const name = clone.querySelector('.cart-item-name');
        const price = clone.querySelector('.cart-item-price');
        const quantity = clone.querySelector('.cart-item-quantity');
        const total = clone.querySelector('.cart-item-total');
        const decreaseBtn = clone.querySelector('.cart-item-decrease');
        const increaseBtn = clone.querySelector('.cart-item-increase');
        const removeBtn = clone.querySelector('.cart-item-remove');
        const discountBtn = clone.querySelector('.cart-item-discount');
        const itemImage = clone.querySelector('.cart-item-image');

        // Add animation class for new items
        if (item.added_at && (new Date() - new Date(item.added_at) < 2000)) {
            card.classList.add('animate-new-item');
            setTimeout(() => {
                card.classList.remove('animate-new-item');
            }, 1000);
        }

        // Set item data
        name.textContent = item.name;
        price.textContent = `${item.price.toFixed(2)} ج.م`;
        quantity.textContent = item.quantity;
        total.textContent = `${item.total.toFixed(2)} ج.م`;

        // Set item image if available
        if (item.image_path) {
            itemImage.style.backgroundImage = `url('/static/uploads/${item.image_path}')`;
            itemImage.classList.remove('bg-blue-100');
            itemImage.innerHTML = '';
        } else {
            // Use product code or first letter as placeholder
            const placeholder = item.code ? item.code.substring(0, 2) : item.name.substring(0, 1);
            itemImage.innerHTML = `<span class="text-blue-500 font-bold">${placeholder}</span>`;
        }

        // Show discount indicator if applied
        if (item.discount > 0) {
            const discountText = item.discount_type === 'percentage'
                ? `خصم ${item.discount}%`
                : `خصم ${item.discount.toFixed(2)} ج.م`;

            discountBtn.innerHTML = `
                <i class="ri-coupon-line mr-1"></i>
                <span>${discountText}</span>
            `;
            discountBtn.classList.add('text-green-500');
            card.classList.add('border-green-200', 'bg-gradient-to-br', 'from-green-50', 'to-white');

            // Add discount badge
            const discountBadge = document.createElement('div');
            discountBadge.className = 'absolute -top-2 -right-2 bg-green-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center shadow-sm';
            discountBadge.innerHTML = '<i class="ri-percent-line text-xs"></i>';
            card.appendChild(discountBadge);
        } else {
            // Add subtle gradient background for non-discounted items
            card.classList.add('bg-gradient-to-br', 'from-blue-50/50', 'to-white');
        }

        // Add stock indicator if stock is low
        if (item.max_quantity <= 5 && item.max_quantity > 0) {
            const stockIndicator = document.createElement('div');
            stockIndicator.className = 'text-xs text-amber-600 mt-1 flex items-center';
            stockIndicator.innerHTML = `<i class="ri-alert-line mr-1"></i> متبقي ${item.max_quantity} فقط`;
            card.querySelector('.cart-item-details').appendChild(stockIndicator);
        }

        // Add event listeners
        decreaseBtn.addEventListener('click', () => {
            updateItemQuantity(index, item.quantity - 1);
        });

        increaseBtn.addEventListener('click', () => {
            updateItemQuantity(index, item.quantity + 1);
        });

        removeBtn.addEventListener('click', () => {
            removeFromCart(index);
        });

        discountBtn.addEventListener('click', () => {
            openItemDiscountModal(index);
        });

        container.appendChild(clone);
    });

    // Enable checkout button
    document.getElementById('checkout-btn').disabled = false;

    // Update summary
    const subtotal = calculateSubtotal();
    const discount = calculateDiscount();
    const tax = calculateTax();
    const total = calculateTotal();

    document.getElementById('cart-subtotal').textContent = `${subtotal.toFixed(2)} ج.م`;
    document.getElementById('cart-discount').textContent = `- ${discount.toFixed(2)} ج.م`;
    document.getElementById('cart-tax').textContent = `+ ${tax.toFixed(2)} ج.م`;
    document.getElementById('cart-total').textContent = `${total.toFixed(2)} ج.م`;
}
