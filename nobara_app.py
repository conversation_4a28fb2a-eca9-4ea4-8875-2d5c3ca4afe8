"""
Nobara POS System - Main Application
نظام نوبارا لنقاط البيع - التطبيق الرئيسي
"""

import os
import logging
from datetime import datetime
from flask import Flask, request, session, g
from flask_sqlalchemy import SQLAlchemy
from flask_login import <PERSON><PERSON><PERSON><PERSON><PERSON>, current_user
from flask_wtf.csrf import CSRFProtect
from flask_babel import Babel, get_locale
from config import config

# Initialize extensions
db = SQLAlchemy()
login_manager = LoginManager()
csrf = CSRFProtect()
babel = Babel()

def create_app(config_name=None):
    """Application factory pattern"""
    
    if config_name is None:
        config_name = os.environ.get('FLASK_ENV', 'development')
    
    app = Flask(__name__)
    app.config.from_object(config[config_name])
    
    # Initialize extensions
    db.init_app(app)
    login_manager.init_app(app)
    csrf.init_app(app)
    babel.init_app(app)
    
    # Configure login manager
    login_manager.login_view = 'auth.login'
    login_manager.login_message = 'يرجى تسجيل الدخول للوصول إلى هذه الصفحة'
    login_manager.login_message_category = 'info'
    
    # Configure logging
    setup_logging(app)
    
    # Register blueprints
    register_blueprints(app)
    
    # Register error handlers
    register_error_handlers(app)
    
    # Register template filters and globals
    register_template_helpers(app)
    
    # Register before/after request handlers
    register_request_handlers(app)
    
    # Create database tables
    with app.app_context():
        create_database_tables()
    
    app.logger.info(f'Nobara POS System started - {datetime.now()}')
    
    return app

def setup_logging(app):
    """Setup application logging"""
    if not app.debug and not app.testing:
        # Create logs directory if it doesn't exist
        if not os.path.exists('logs'):
            os.mkdir('logs')
        
        # Setup file handler
        file_handler = logging.handlers.RotatingFileHandler(
            app.config['LOG_FILE'],
            maxBytes=app.config['LOG_MAX_BYTES'],
            backupCount=app.config['LOG_BACKUP_COUNT']
        )
        
        file_handler.setFormatter(logging.Formatter(
            '%(asctime)s %(levelname)s: %(message)s [in %(pathname)s:%(lineno)d]'
        ))
        
        file_handler.setLevel(getattr(logging, app.config['LOG_LEVEL']))
        app.logger.addHandler(file_handler)
        app.logger.setLevel(getattr(logging, app.config['LOG_LEVEL']))

def register_blueprints(app):
    """Register application blueprints"""
    
    # Import blueprints
    from app.auth import bp as auth_bp
    from app.main import bp as main_bp
    from app.pos import bp as pos_bp
    from app.products import bp as products_bp
    from app.sales import bp as sales_bp
    from app.purchases import bp as purchases_bp
    from app.inventory import bp as inventory_bp
    from app.customers import bp as customers_bp
    from app.suppliers import bp as suppliers_bp
    from app.reports import bp as reports_bp
    from app.settings import bp as settings_bp
    from app.api import bp as api_bp
    
    # Register blueprints
    app.register_blueprint(auth_bp, url_prefix='/auth')
    app.register_blueprint(main_bp)
    app.register_blueprint(pos_bp, url_prefix='/pos')
    app.register_blueprint(products_bp, url_prefix='/products')
    app.register_blueprint(sales_bp, url_prefix='/sales')
    app.register_blueprint(purchases_bp, url_prefix='/purchases')
    app.register_blueprint(inventory_bp, url_prefix='/inventory')
    app.register_blueprint(customers_bp, url_prefix='/customers')
    app.register_blueprint(suppliers_bp, url_prefix='/suppliers')
    app.register_blueprint(reports_bp, url_prefix='/reports')
    app.register_blueprint(settings_bp, url_prefix='/settings')
    app.register_blueprint(api_bp, url_prefix='/api')

def register_error_handlers(app):
    """Register error handlers"""
    
    @app.errorhandler(404)
    def not_found_error(error):
        return render_template('errors/404.html'), 404
    
    @app.errorhandler(500)
    def internal_error(error):
        db.session.rollback()
        return render_template('errors/500.html'), 500
    
    @app.errorhandler(403)
    def forbidden_error(error):
        return render_template('errors/403.html'), 403

def register_template_helpers(app):
    """Register template filters and global functions"""
    
    @app.template_global()
    def get_app_config():
        """Get application configuration for templates"""
        return {
            'APP_NAME': app.config['APP_NAME'],
            'APP_NAME_AR': app.config['APP_NAME_AR'],
            'APP_VERSION': app.config['APP_VERSION'],
            'DEVELOPER_NAME': app.config['DEVELOPER_NAME'],
            'DEVELOPER_PHONE': app.config['DEVELOPER_PHONE'],
            'POWERED_BY': app.config['POWERED_BY'],
            'PRIMARY_COLORS': app.config['PRIMARY_COLORS']
        }
    
    @app.template_filter('currency')
    def currency_filter(amount):
        """Format currency"""
        if amount is None:
            amount = 0
        return f"{amount:,.2f} {app.config['CURRENCY_SYMBOL']}"
    
    @app.template_filter('datetime_format')
    def datetime_format(value, format='%Y-%m-%d %H:%M'):
        """Format datetime"""
        if value is None:
            return ""
        return value.strftime(format)
    
    @app.template_filter('date_format')
    def date_format(value, format='%Y-%m-%d'):
        """Format date"""
        if value is None:
            return ""
        return value.strftime(format)

def register_request_handlers(app):
    """Register before/after request handlers"""
    
    @app.before_request
    def before_request():
        """Before request handler"""
        # Set language
        if 'language' not in session:
            session['language'] = app.config['DEFAULT_LANGUAGE']
        
        # Set theme
        if 'theme' not in session:
            session['theme'] = app.config['DEFAULT_THEME']
        
        # Store current user in g
        g.current_user = current_user
        g.language = session.get('language', app.config['DEFAULT_LANGUAGE'])
        g.theme = session.get('theme', app.config['DEFAULT_THEME'])
    
    @app.after_request
    def after_request(response):
        """After request handler"""
        # Add security headers
        response.headers['X-Content-Type-Options'] = 'nosniff'
        response.headers['X-Frame-Options'] = 'DENY'
        response.headers['X-XSS-Protection'] = '1; mode=block'
        
        return response

@babel.localeselector
def get_locale():
    """Get current locale for Babel"""
    return session.get('language', 'ar')

def create_database_tables():
    """Create database tables"""
    try:
        # Import all models to ensure they are registered
        from app.models import User, Role, Product, Category, Sale, Purchase, Customer, Supplier
        
        # Create all tables
        db.create_all()
        
        # Create default data
        create_default_data()
        
    except Exception as e:
        print(f"Error creating database tables: {e}")

def create_default_data():
    """Create default data"""
    from app.models import User, Role
    from werkzeug.security import generate_password_hash
    
    # Create default roles
    if not Role.query.first():
        admin_role = Role(name='admin', name_ar='مدير', description='System Administrator')
        cashier_role = Role(name='cashier', name_ar='كاشير', description='Cashier')
        user_role = Role(name='user', name_ar='مستخدم', description='Regular User')
        
        db.session.add(admin_role)
        db.session.add(cashier_role)
        db.session.add(user_role)
        db.session.commit()
    
    # Create default admin user
    if not User.query.filter_by(username='admin').first():
        admin_role = Role.query.filter_by(name='admin').first()
        admin_user = User(
            username='admin',
            email='<EMAIL>',
            first_name='Fouad',
            last_name='Saber',
            password_hash=generate_password_hash('admin'),
            role=admin_role,
            is_active=True
        )
        
        db.session.add(admin_user)
        db.session.commit()

if __name__ == '__main__':
    app = create_app()
    app.run(host='0.0.0.0', port=5000, debug=True)
