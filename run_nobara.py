# -*- coding: utf-8 -*-
"""
Nobara POS System - Simple Runner
نظام نوبارا لنقاط البيع - ملف التشغيل المبسط
Developer: ENG/ Fouad Saber - Tel: 01020073527
"""

import os
import sys
import socket
import webbrowser
from datetime import datetime
from flask import Flask, render_template_string, request, redirect, url_for, flash
from flask_sqlalchemy import SQLAlchemy
from flask_login import LoginManager, UserMixin, login_user, logout_user, login_required, current_user
from werkzeug.security import generate_password_hash, check_password_hash

# Initialize Flask app
app = Flask(__name__)
app.config['SECRET_KEY'] = 'nobara-pos-secret-key-2024'
app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///nobara_pos.db'
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False

# Initialize extensions
db = SQLAlchemy(app)
login_manager = LoginManager()
login_manager.init_app(app)
login_manager.login_view = 'login'

# Models
class User(UserMixin, db.Model):
    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(80), unique=True, nullable=False)
    email = db.Column(db.String(120), unique=True, nullable=False)
    password_hash = db.Column(db.String(120), nullable=False)
    first_name = db.Column(db.String(50), nullable=False)
    last_name = db.Column(db.String(50), nullable=False)
    is_active = db.Column(db.Boolean, default=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

class Product(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(200), nullable=False)
    price = db.Column(db.Float, nullable=False, default=0.0)
    quantity = db.Column(db.Integer, nullable=False, default=0)
    is_active = db.Column(db.Boolean, default=True)

class Customer(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(200), nullable=False)
    phone = db.Column(db.String(20))
    email = db.Column(db.String(120))

@login_manager.user_loader
def load_user(user_id):
    return User.query.get(int(user_id))

# Routes
@app.route('/')
def index():
    if current_user.is_authenticated:
        return redirect(url_for('dashboard'))
    return redirect(url_for('login'))

@app.route('/login', methods=['GET', 'POST'])
def login():
    if request.method == 'POST':
        username = request.form.get('username')
        password = request.form.get('password')
        
        user = User.query.filter_by(username=username).first()
        
        if user and check_password_hash(user.password_hash, password):
            login_user(user)
            return redirect(url_for('dashboard'))
        else:
            flash('Invalid credentials')
    
    return render_template_string('''
    <!DOCTYPE html>
    <html>
    <head>
        <title>Nobara Login</title>
        <meta charset="utf-8">
        <style>
            body { font-family: Arial; background: #f0f0f0; margin: 0; padding: 50px; }
            .login-box { background: white; padding: 30px; border-radius: 10px; max-width: 400px; margin: 0 auto; }
            input { width: 100%; padding: 10px; margin: 10px 0; border: 1px solid #ddd; border-radius: 5px; }
            button { width: 100%; padding: 10px; background: #007bff; color: white; border: none; border-radius: 5px; cursor: pointer; }
            button:hover { background: #0056b3; }
            .logo { text-align: center; font-size: 24px; margin-bottom: 20px; color: #007bff; }
        </style>
    </head>
    <body>
        <div class="login-box">
            <div class="logo">🏪 Nobara POS</div>
            <form method="POST">
                <input type="text" name="username" placeholder="Username" required>
                <input type="password" name="password" placeholder="Password" required>
                <button type="submit">Login</button>
            </form>
            <p style="text-align: center; margin-top: 20px; color: #666;">
                Default: admin / admin
            </p>
        </div>
    </body>
    </html>
    ''')

@app.route('/dashboard')
@login_required
def dashboard():
    total_products = Product.query.count()
    total_customers = Customer.query.count()
    
    return render_template_string('''
    <!DOCTYPE html>
    <html>
    <head>
        <title>Nobara Dashboard</title>
        <meta charset="utf-8">
        <style>
            body { font-family: Arial; background: #f8f9fa; margin: 0; padding: 0; }
            .header { background: white; padding: 15px 30px; border-bottom: 1px solid #ddd; }
            .header h1 { margin: 0; color: #007bff; display: inline-block; }
            .logout { float: right; background: #dc3545; color: white; padding: 8px 15px; text-decoration: none; border-radius: 5px; }
            .container { padding: 30px; }
            .stats { display: flex; gap: 20px; margin-bottom: 30px; }
            .stat-card { background: white; padding: 20px; border-radius: 10px; flex: 1; text-align: center; }
            .stat-number { font-size: 36px; font-weight: bold; color: #007bff; }
            .stat-label { color: #666; margin-top: 5px; }
            .cards { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; }
            .card { background: white; padding: 20px; border-radius: 10px; }
            .card h3 { margin-top: 0; color: #333; }
            .btn { background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; display: inline-block; }
            .btn:hover { background: #0056b3; }
        </style>
    </head>
    <body>
        <div class="header">
            <h1>🏪 Nobara POS Dashboard</h1>
            <a href="{{ url_for('logout') }}" class="logout">Logout</a>
        </div>
        
        <div class="container">
            <div class="stats">
                <div class="stat-card">
                    <div class="stat-number">{{ total_products }}</div>
                    <div class="stat-label">Products</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">{{ total_customers }}</div>
                    <div class="stat-label">Customers</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">0</div>
                    <div class="stat-label">Sales Today</div>
                </div>
            </div>
            
            <div class="cards">
                <div class="card">
                    <h3>Point of Sale</h3>
                    <p>Start new sales transactions</p>
                    <a href="#" onclick="alert('POS coming soon!')" class="btn">Open POS</a>
                </div>
                
                <div class="card">
                    <h3>Products</h3>
                    <p>Manage your inventory</p>
                    <a href="#" onclick="alert('Products coming soon!')" class="btn">Manage Products</a>
                </div>
                
                <div class="card">
                    <h3>Customers</h3>
                    <p>Customer management</p>
                    <a href="#" onclick="alert('Customers coming soon!')" class="btn">Manage Customers</a>
                </div>
                
                <div class="card">
                    <h3>Reports</h3>
                    <p>View sales reports</p>
                    <a href="#" onclick="alert('Reports coming soon!')" class="btn">View Reports</a>
                </div>
            </div>
        </div>
        
        <div style="text-align: center; padding: 20px; color: #666; border-top: 1px solid #ddd; margin-top: 50px;">
            &copy; 2024 Nobara POS | Powered By ENG/ Fouad Saber - Tel: 01020073527
        </div>
    </body>
    </html>
    ''', total_products=total_products, total_customers=total_customers)

@app.route('/logout')
@login_required
def logout():
    logout_user()
    return redirect(url_for('login'))

def get_local_ip():
    try:
        s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        s.connect(("*******", 80))
        local_ip = s.getsockname()[0]
        s.close()
        return local_ip
    except:
        return "127.0.0.1"

def setup_database():
    """Setup database and create default data"""
    with app.app_context():
        db.create_all()
        
        # Create admin user if not exists
        if not User.query.filter_by(username='admin').first():
            admin = User(
                username='admin',
                email='<EMAIL>',
                password_hash=generate_password_hash('admin'),
                first_name='Fouad',
                last_name='Saber'
            )
            db.session.add(admin)
            
            # Add sample products
            products = [
                Product(name='Sample Product 1', price=100.0, quantity=50),
                Product(name='Sample Product 2', price=200.0, quantity=30),
                Product(name='Sample Product 3', price=50.0, quantity=100),
            ]
            
            customers = [
                Customer(name='Cash Customer', phone='01000000000'),
                Customer(name='Ahmed Mohamed', phone='01111111111'),
                Customer(name='Fatma Ali', phone='01222222222'),
            ]
            
            for product in products:
                db.session.add(product)
            
            for customer in customers:
                db.session.add(customer)
            
            db.session.commit()
            print("✅ Database setup completed!")

if __name__ == "__main__":
    print("🏪 Starting Nobara POS System...")
    print("📱 Version: 2.0.0")
    print("👨‍💻 Developer: ENG/ Fouad Saber")
    print("📞 Phone: 01020073527")
    print("📧 Email: <EMAIL>")
    print()
    
    # Setup database
    setup_database()
    
    local_ip = get_local_ip()
    print(f"🌐 Local Access: http://localhost:5000")
    print(f"🌐 Network Access: http://{local_ip}:5000")
    print()
    print("🎯 Login Credentials:")
    print("   Username: admin")
    print("   Password: admin")
    print()
    print("✅ System is ready!")
    print("🚀 Opening browser...")
    
    # Open browser
    try:
        webbrowser.open('http://localhost:5000')
    except:
        print("⚠️  Could not open browser automatically")
        print("   Please open http://localhost:5000 manually")
    
    print()
    print("=" * 60)
    
    # Run the app
    try:
        app.run(host='0.0.0.0', port=5000, debug=False)
    except KeyboardInterrupt:
        print("\n🛑 System stopped by user")
    except Exception as e:
        print(f"\n❌ Error: {e}")
    finally:
        print("\n👋 Thank you for using Nobara POS!")
        print("🏆 Powered By ENG/ Fouad Saber - Tel: 01020073527")
