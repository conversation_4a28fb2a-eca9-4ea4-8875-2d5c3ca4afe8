<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تحليل العملاء - نظام إدارة المبيعات</title>
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <!-- Remixicon -->
    <link href="https://cdn.jsdelivr.net/npm/remixicon@2.5.0/fonts/remixicon.css" rel="stylesheet">
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        .report-card {
            transition: all 0.3s ease;
        }
        .report-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 15px rgba(0, 0, 0, 0.1);
        }
    </style>
</head>
<body class="bg-gray-50">
    <div class="flex h-screen overflow-hidden">
        <!-- Sidebar -->
        {% include 'partials/sidebar.html' %}

        <!-- Main Content -->
        <div class="flex-1 flex flex-col overflow-hidden">
            <!-- Top Navigation -->
            {% include 'partials/topnav.html' %}

            <!-- Content Area -->
            <main class="flex-1 overflow-y-auto p-6">
                <div class="mb-6 flex items-center justify-between">
                    <div>
                        <h1 class="text-2xl font-bold text-gray-800">تحليل العملاء</h1>
                        <p class="text-gray-600">تحليل سلوك العملاء وتقسيمهم إلى شرائح</p>
                    </div>
                    <div class="flex space-x-2 space-x-reverse">
                        <a href="{{ url_for('reports.sales_index') }}" class="px-4 py-2 bg-blue-100 text-blue-700 rounded-md hover:bg-blue-200 transition-all">
                            <i class="ri-arrow-right-line"></i>
                            العودة لتقارير المبيعات
                        </a>
                        <a href="{{ url_for('reports.index') }}" class="px-4 py-2 bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200 transition-all">
                            <i class="ri-home-line"></i>
                            الرئيسية
                        </a>
                    </div>
                </div>

                <!-- Filters -->
                <div class="bg-white p-4 rounded-lg shadow-sm mb-6">
                    <form action="{{ url_for('reports.customer_analysis_report') }}" method="get" class="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <div>
                            <label for="date_from" class="block text-sm font-medium text-gray-700 mb-1">من تاريخ</label>
                            <input type="date" id="date_from" name="date_from" value="{{ date_from }}"
                                   class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500">
                        </div>
                        <div>
                            <label for="date_to" class="block text-sm font-medium text-gray-700 mb-1">إلى تاريخ</label>
                            <input type="date" id="date_to" name="date_to" value="{{ date_to }}"
                                   class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500">
                        </div>
                        <div class="flex items-end">
                            <button type="submit" class="bg-indigo-600 text-white px-4 py-2 rounded-md hover:bg-indigo-700 transition-all">
                                <i class="ri-filter-3-line ml-1"></i>
                                تطبيق الفلتر
                            </button>
                        </div>
                    </form>
                </div>

                <!-- Customer Summary -->
                <div class="bg-white p-6 rounded-lg shadow-sm mb-6">
                    <h2 class="text-lg font-bold text-gray-800 mb-4">ملخص العملاء</h2>
                    <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
                        <div class="p-4 bg-indigo-50 rounded-lg">
                            <h3 class="text-sm font-medium text-gray-500 mb-1">إجمالي العملاء</h3>
                            <p class="text-2xl font-bold text-indigo-600">
                                {{ total_customers }}
                            </p>
                        </div>
                        <div class="p-4 bg-green-50 rounded-lg">
                            <h3 class="text-sm font-medium text-gray-500 mb-1">إجمالي الإيرادات</h3>
                            <p class="text-2xl font-bold text-green-600">
                                {{ "%.2f"|format(total_revenue) }} ج.م
                            </p>
                        </div>
                        <div class="p-4 bg-blue-50 rounded-lg">
                            <h3 class="text-sm font-medium text-gray-500 mb-1">متوسط قيمة العميل</h3>
                            <p class="text-2xl font-bold text-blue-600">
                                {% if total_customers > 0 %}
                                    {{ "%.2f"|format(total_revenue / total_customers) }} ج.م
                                {% else %}
                                    0.00 ج.م
                                {% endif %}
                            </p>
                        </div>
                        <div class="p-4 bg-purple-50 rounded-lg">
                            <h3 class="text-sm font-medium text-gray-500 mb-1">الفترة الزمنية</h3>
                            <p class="text-lg font-bold text-purple-600">
                                {{ date_from }} - {{ date_to }}
                            </p>
                        </div>
                    </div>
                </div>

                <!-- Customer Segments Chart -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                    <div class="bg-white p-6 rounded-lg shadow-sm">
                        <h2 class="text-lg font-bold text-gray-800 mb-4">توزيع شرائح العملاء</h2>
                        <div class="h-80">
                            <canvas id="segmentChart"></canvas>
                        </div>
                    </div>
                    <div class="bg-white p-6 rounded-lg shadow-sm">
                        <h2 class="text-lg font-bold text-gray-800 mb-4">توزيع حالة العملاء</h2>
                        <div class="h-80">
                            <canvas id="statusChart"></canvas>
                        </div>
                    </div>
                </div>

                <!-- RFM Explanation -->
                <div class="bg-white p-6 rounded-lg shadow-sm mb-6">
                    <h2 class="text-lg font-bold text-gray-800 mb-4">تحليل RFM (Recency, Frequency, Monetary)</h2>
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                        <div class="p-4 bg-blue-50 rounded-lg">
                            <h3 class="text-sm font-bold text-blue-700 mb-1">R - الحداثة (Recency)</h3>
                            <p class="text-sm text-gray-600">
                                عدد الأيام منذ آخر عملية شراء. كلما كانت القيمة أقل، كان العميل أكثر نشاطًا.
                            </p>
                        </div>
                        <div class="p-4 bg-green-50 rounded-lg">
                            <h3 class="text-sm font-bold text-green-700 mb-1">F - التكرار (Frequency)</h3>
                            <p class="text-sm text-gray-600">
                                عدد مرات الشراء. كلما زادت القيمة، كان العميل أكثر ولاءً.
                            </p>
                        </div>
                        <div class="p-4 bg-purple-50 rounded-lg">
                            <h3 class="text-sm font-bold text-purple-700 mb-1">M - القيمة المالية (Monetary)</h3>
                            <p class="text-sm text-gray-600">
                                إجمالي المبالغ التي أنفقها العميل. كلما زادت القيمة، كان العميل أكثر قيمة.
                            </p>
                        </div>
                    </div>
                    <div class="mt-4">
                        <h3 class="text-sm font-bold text-gray-700 mb-2">شرائح العملاء:</h3>
                        <ul class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-2 text-sm">
                            <li class="flex items-center"><span class="w-3 h-3 bg-indigo-500 rounded-full mr-2"></span> <strong>عميل مميز:</strong> عميل نشط ومتكرر وذو قيمة عالية</li>
                            <li class="flex items-center"><span class="w-3 h-3 bg-green-500 rounded-full mr-2"></span> <strong>عميل قيم:</strong> عميل نشط وذو قيمة جيدة</li>
                            <li class="flex items-center"><span class="w-3 h-3 bg-blue-500 rounded-full mr-2"></span> <strong>عميل جديد واعد:</strong> عميل نشط حديثًا</li>
                            <li class="flex items-center"><span class="w-3 h-3 bg-yellow-500 rounded-full mr-2"></span> <strong>عميل سابق ذو قيمة:</strong> عميل غير نشط لكن ذو قيمة عالية</li>
                            <li class="flex items-center"><span class="w-3 h-3 bg-orange-500 rounded-full mr-2"></span> <strong>عميل متكرر غير نشط:</strong> عميل كان متكررًا لكنه غير نشط حاليًا</li>
                            <li class="flex items-center"><span class="w-3 h-3 bg-red-500 rounded-full mr-2"></span> <strong>عميل في خطر الفقدان:</strong> عميل غير نشط ومنخفض القيمة</li>
                        </ul>
                    </div>
                </div>

                <!-- Customer Table -->
                <div class="bg-white rounded-lg shadow-sm overflow-hidden">
                    <h2 class="text-lg font-bold text-gray-800 p-6 border-b">تفاصيل العملاء</h2>
                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        العميل
                                    </th>
                                    <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        عدد الطلبات
                                    </th>
                                    <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        إجمالي المشتريات
                                    </th>
                                    <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        متوسط قيمة الطلب
                                    </th>
                                    <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        آخر طلب
                                    </th>
                                    <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        الحالة
                                    </th>
                                    <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        الشريحة
                                    </th>
                                    <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        RFM
                                    </th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                {% for customer in customers_data %}
                                <tr class="hover:bg-gray-50">
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm font-medium text-gray-900">{{ customer.name }}</div>
                                        <div class="text-xs text-gray-500">{{ customer.phone }}</div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                        {{ customer.orders_count }}
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                        {{ "%.2f"|format(customer.total_spent) }} ج.م
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                        {{ "%.2f"|format(customer.avg_order_value) }} ج.م
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                        {{ customer.last_purchase.strftime('%Y-%m-%d') }}
                                        <div class="text-xs text-gray-400">منذ {{ customer.days_since_last }} يوم</div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        {% if customer.status == 'نشط' %}
                                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">
                                                {{ customer.status }}
                                            </span>
                                        {% elif customer.status == 'في خطر' %}
                                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-yellow-100 text-yellow-800">
                                                {{ customer.status }}
                                            </span>
                                        {% else %}
                                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 text-red-800">
                                                {{ customer.status }}
                                            </span>
                                        {% endif %}
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                        {{ customer.segment }}
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                        <div class="flex space-x-1 space-x-reverse">
                                            <span class="px-2 py-1 text-xs font-semibold rounded bg-blue-100 text-blue-800">R{{ customer.r_score }}</span>
                                            <span class="px-2 py-1 text-xs font-semibold rounded bg-green-100 text-green-800">F{{ customer.f_score }}</span>
                                            <span class="px-2 py-1 text-xs font-semibold rounded bg-purple-100 text-purple-800">M{{ customer.m_score }}</span>
                                        </div>
                                    </td>
                                </tr>
                                {% else %}
                                <tr>
                                    <td colspan="8" class="px-6 py-4 text-center text-gray-500">
                                        لا توجد بيانات للفترة المحددة
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <script>
        // Prepare data for segment chart
        const segmentLabels = [
            {% for segment in segment_data %}
                '{{ segment.name }}'{% if not loop.last %},{% endif %}
            {% endfor %}
        ];

        const segmentCounts = [
            {% for segment in segment_data %}
                {{ segment.count }}{% if not loop.last %},{% endif %}
            {% endfor %}
        ];

        // Prepare data for status chart
        const statusCounts = {
            'نشط': 0,
            'في خطر': 0,
            'غير نشط': 0
        };

        {% for customer in customers_data %}
            statusCounts['{{ customer.status }}'] += 1;
        {% endfor %}

        // Create segment chart
        const segmentCtx = document.getElementById('segmentChart').getContext('2d');
        const segmentChart = new Chart(segmentCtx, {
            type: 'pie',
            data: {
                labels: segmentLabels,
                datasets: [{
                    data: segmentCounts,
                    backgroundColor: [
                        'rgba(99, 102, 241, 0.8)',
                        'rgba(16, 185, 129, 0.8)',
                        'rgba(59, 130, 246, 0.8)',
                        'rgba(245, 158, 11, 0.8)',
                        'rgba(249, 115, 22, 0.8)',
                        'rgba(239, 68, 68, 0.8)',
                        'rgba(156, 163, 175, 0.8)'
                    ],
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'right'
                    }
                }
            }
        });

        // Create status chart
        const statusCtx = document.getElementById('statusChart').getContext('2d');
        const statusChart = new Chart(statusCtx, {
            type: 'doughnut',
            data: {
                labels: ['نشط', 'في خطر', 'غير نشط'],
                datasets: [{
                    data: [statusCounts['نشط'], statusCounts['في خطر'], statusCounts['غير نشط']],
                    backgroundColor: [
                        'rgba(16, 185, 129, 0.8)',
                        'rgba(245, 158, 11, 0.8)',
                        'rgba(239, 68, 68, 0.8)'
                    ],
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'right'
                    }
                }
            }
        });
    </script>
</body>
</html>
