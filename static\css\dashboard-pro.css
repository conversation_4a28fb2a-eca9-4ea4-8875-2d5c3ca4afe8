/**
 * Dashboard Pro - تصميم احترافي للوحة التحكم
 */

:root {
    /* الألوان الأساسية */
    --primary: #3b82f6;
    --primary-dark: #2563eb;
    --primary-light: #60a5fa;
    --primary-lighter: #93c5fd;
    --primary-lightest: #dbeafe;

    /* ألوان الخلفية */
    --bg-white: #ffffff;
    --bg-light: #f9fafb;
    --bg-lighter: #f3f4f6;
    --bg-lightest: #f1f5f9;

    /* ألوان النص */
    --text-dark: #1e293b;
    --text-medium: #475569;
    --text-light: #64748b;
    --text-lighter: #94a3b8;

    /* ألوان الحالة */
    --success: #10b981;
    --success-light: #d1fae5;

    --error: #ef4444;
    --error-light: #fee2e2;

    --warning: #f59e0b;
    --warning-light: #fef3c7;

    --info: #3b82f6;
    --info-light: #dbeafe;

    /* ألوان البطاقات */
    --sales-color: #3b82f6;
    --purchases-color: #8b5cf6;
    --profit-color: #10b981;
    --returns-color: #f59e0b;
    --inventory-color: #ef4444;
    --customers-color: #ec4899;

    /* ألوان الظلال */
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-md: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --shadow-lg: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);

    /* تأثيرات الانتقال */
    --transition-fast: 150ms;
    --transition-normal: 300ms;
    --transition-slow: 500ms;
}

/* تصميم عام */
.dashboard-container {
    max-width: 1920px;
    margin: 0 auto;
}

/* تصميم الشريط العلوي */
.dashboard-header {
    position: relative;
    border-radius: 16px;
    overflow: hidden;
    background: linear-gradient(to left, var(--primary), var(--primary-dark));
    box-shadow: 0 10px 25px -5px rgba(59, 130, 246, 0.5);
}

.dashboard-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: url("data:image/svg+xml,%3Csvg width='100' height='100' viewBox='0 0 100 100' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M11 18c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm48 25c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm-43-7c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm63 31c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM34 90c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm56-76c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM12 86c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm28-65c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm23-11c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-6 60c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm29 22c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zM32 63c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm57-13c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-9-21c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM60 91c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM35 41c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM12 60c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2z' fill='%23ffffff' fill-opacity='0.05' fill-rule='evenodd'/%3E%3C/svg%3E");
    opacity: 0.8;
}

.dashboard-header-title {
    position: relative;
    display: inline-block;
}

.dashboard-header-title::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 0;
    width: 40px;
    height: 3px;
    background-color: rgba(255, 255, 255, 0.5);
    border-radius: 3px;
}

/* تصميم البطاقات */
.stat-card {
    position: relative;
    border-radius: 16px;
    overflow: hidden;
    transition: all 0.3s ease;
    box-shadow: var(--shadow);
}

.stat-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-md);
}

.stat-card::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 4px;
    background: linear-gradient(to right, var(--card-accent-color), transparent);
}

.stat-card.sales {
    --card-accent-color: var(--sales-color);
}

.stat-card.purchases {
    --card-accent-color: var(--purchases-color);
}

.stat-card.profit {
    --card-accent-color: var(--profit-color);
}

.stat-card.returns {
    --card-accent-color: var(--returns-color);
}

.stat-card.inventory {
    --card-accent-color: var(--inventory-color);
}

.stat-card.customers {
    --card-accent-color: var(--customers-color);
}

.stat-icon {
    transition: all 0.3s ease;
}

.stat-card:hover .stat-icon {
    transform: scale(1.1) rotate(10deg);
}

/* تصميم الرسوم البيانية */
.chart-container {
    border-radius: 16px;
    overflow: hidden;
    transition: all 0.3s ease;
    box-shadow: var(--shadow);
}

.chart-container:hover {
    box-shadow: var(--shadow-md);
}

.chart-title {
    position: relative;
    display: inline-block;
}

.chart-title::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 0;
    width: 30px;
    height: 2px;
    background: linear-gradient(to right, var(--primary), var(--primary-light));
    border-radius: 2px;
}

/* تصميم الجداول */
.data-table {
    border-radius: 12px;
    overflow: hidden;
    box-shadow: var(--shadow-sm);
}

.data-table th {
    background-color: var(--bg-light);
    font-weight: 600;
    text-transform: uppercase;
    font-size: 0.75rem;
    letter-spacing: 0.05em;
}

.data-table tr {
    transition: all 0.2s ease;
    border-bottom: 1px solid var(--bg-lighter);
}

.data-table tr:last-child {
    border-bottom: none;
}

.data-table tr:hover {
    background-color: var(--bg-lighter);
}

/* تصميم القائمة الجانبية */
.dashboard-sidebar {
    position: sticky;
    top: 1.5rem;
    height: calc(100vh - 3rem);
}

.sidebar-menu {
    border-radius: 12px;
    overflow: hidden;
    box-shadow: var(--shadow);
}

.sidebar-menu-item {
    transition: all 0.2s ease;
    border-right: 3px solid transparent;
}

.sidebar-menu-item:hover, .sidebar-menu-item.active {
    background-color: rgba(59, 130, 246, 0.1);
    border-right-color: var(--primary);
}

.sidebar-menu-item.active a {
    font-weight: 500;
}

/* تأثيرات حركية */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in-up {
    animation: fadeInUp 0.5s ease-out forwards;
    opacity: 0;
}

.fade-in-up-1 { animation-delay: 0.1s; }
.fade-in-up-2 { animation-delay: 0.2s; }
.fade-in-up-3 { animation-delay: 0.3s; }
.fade-in-up-4 { animation-delay: 0.4s; }

@keyframes pulse {
    0%, 100% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
}

.pulse {
    animation: pulse 2s infinite;
}

@keyframes pulse-animation {
    0% {
        box-shadow: 0 0 0 0 rgba(239, 68, 68, 0.7);
    }
    70% {
        box-shadow: 0 0 0 10px rgba(239, 68, 68, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(239, 68, 68, 0);
    }
}

.pulse-animation {
    animation: pulse-animation 1.5s infinite;
}

/* توافق وضع الدارك مود */
.dark-mode {
    --bg-white: #1e293b;
    --bg-light: #334155;
    --bg-lighter: #475569;
    --bg-lightest: #1e293b;

    --text-dark: #f8fafc;
    --text-medium: #e2e8f0;
    --text-light: #cbd5e1;
    --text-lighter: #94a3b8;
}

.dark-mode .dashboard-header {
    background: linear-gradient(to left, #1e40af, #1e3a8a);
    box-shadow: 0 10px 25px -5px rgba(30, 64, 175, 0.5);
}

.dark-mode .stat-card {
    background-color: var(--bg-white);
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.2), 0 2px 4px -1px rgba(0, 0, 0, 0.1);
}

.dark-mode .chart-container {
    background-color: var(--bg-white);
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.2), 0 2px 4px -1px rgba(0, 0, 0, 0.1);
}

.dark-mode .data-table th {
    background-color: var(--bg-light);
    color: var(--text-light);
}

.dark-mode .data-table tr {
    border-bottom: 1px solid var(--bg-lighter);
}

.dark-mode .data-table tr:hover {
    background-color: var(--bg-light);
}

.dark-mode .sidebar-menu {
    background-color: var(--bg-white);
}

.dark-mode .sidebar-menu-item:hover, .dark-mode .sidebar-menu-item.active {
    background-color: rgba(59, 130, 246, 0.2);
}
