"""
وحدة المساعدة المحاسبية - توفر وظائف مساعدة للعمليات المحاسبية
"""

from datetime import datetime
from app import db
from models.accounting import (
    Account, JournalEntry, JournalEntryItem, FiscalPeriod, Currency,
    AccountingSettings, AccountingTransaction
)
from models import Order, OrderItem, Payment, Purchase, PurchaseItem, ReturnOrder, ReturnItem


class AccountingHelper:
    """فئة المساعدة المحاسبية - توفر وظائف مساعدة للعمليات المحاسبية"""

    @staticmethod
    def get_current_fiscal_period():
        """الحصول على الفترة المالية الحالية"""
        today = datetime.now().date()
        fiscal_period = FiscalPeriod.query.filter(
            FiscalPeriod.start_date <= today,
            FiscalPeriod.end_date >= today,
            FiscalPeriod.status == 'open'
        ).first()
        return fiscal_period

    @staticmethod
    def get_default_currency():
        """الحصول على العملة الافتراضية"""
        return Currency.get_default_currency()

    @staticmethod
    def get_accounting_settings():
        """الحصول على إعدادات المحاسبة"""
        return AccountingSettings.get_settings()

    @staticmethod
    def generate_journal_entry_reference():
        """توليد رقم مرجعي للقيد المحاسبي"""
        # الحصول على آخر قيد محاسبي
        last_entry = JournalEntry.query.order_by(JournalEntry.id.desc()).first()

        # توليد الرقم المرجعي
        if last_entry and last_entry.reference_number.startswith('JE-'):
            try:
                last_number = int(last_entry.reference_number.split('-')[1])
                new_number = last_number + 1
            except (ValueError, IndexError):
                new_number = 1
        else:
            new_number = 1

        # تنسيق الرقم المرجعي
        return f"JE-{new_number:06d}"

    @classmethod
    def create_journal_entry(cls, date, description, items, user_id=None, entry_type='manual', source_document_type=None, source_document_id=None):
        """إنشاء قيد محاسبي"""
        # التحقق من توازن القيد
        total_debit = sum(item.get('debit', 0) for item in items)
        total_credit = sum(item.get('credit', 0) for item in items)

        if round(total_debit, 2) != round(total_credit, 2):
            return None, 'القيد غير متوازن'

        # الحصول على الفترة المالية
        fiscal_period = cls.get_current_fiscal_period()
        if not fiscal_period:
            return None, 'لا توجد فترة مالية مفتوحة'

        # إنشاء القيد المحاسبي
        journal_entry = JournalEntry(
            reference_number=cls.generate_journal_entry_reference(),
            date=date,
            description=description,
            entry_type=entry_type,
            source_document_type=source_document_type,
            source_document_id=source_document_id,
            fiscal_period_id=fiscal_period.id,
            user_id=user_id
        )

        # إضافة بنود القيد
        for item in items:
            journal_item = JournalEntryItem(
                account_id=item['account_id'],
                description=item.get('description', ''),
                debit=item.get('debit', 0),
                credit=item.get('credit', 0)
            )
            journal_entry.items.append(journal_item)

        # إضافة القيد إلى قاعدة البيانات
        db.session.add(journal_entry)
        db.session.commit()

        return journal_entry, 'تم إنشاء القيد المحاسبي بنجاح'

    @classmethod
    def create_sale_journal_entry(cls, order, user_id=None):
        """إنشاء قيد محاسبي لعملية بيع"""
        # الحصول على إعدادات المحاسبة
        settings = cls.get_accounting_settings()

        # التحقق من وجود الحسابات الضرورية
        if not settings.default_sales_account_id:
            return None, 'لم يتم تعيين حساب المبيعات الافتراضي'

        if not settings.default_accounts_receivable_account_id and order.payment_method != 'cash':
            return None, 'لم يتم تعيين حساب الذمم المدينة الافتراضي'

        if not settings.default_cash_account_id and order.payment_method == 'cash':
            return None, 'لم يتم تعيين حساب النقدية الافتراضي'

        if not settings.default_inventory_account_id:
            return None, 'لم يتم تعيين حساب المخزون الافتراضي'

        if not settings.default_cost_of_goods_sold_account_id:
            return None, 'لم يتم تعيين حساب تكلفة البضاعة المباعة الافتراضي'

        # إنشاء بنود القيد
        items = []

        # الجانب المدين (الأصول)
        if order.payment_method == 'cash':
            # حساب النقدية
            items.append({
                'account_id': settings.default_cash_account_id,
                'description': f'مبيعات نقدية - فاتورة رقم {order.invoice_number}',
                'debit': order.total,
                'credit': 0
            })
        else:
            # حساب الذمم المدينة
            items.append({
                'account_id': settings.default_accounts_receivable_account_id,
                'description': f'مبيعات آجلة - فاتورة رقم {order.invoice_number}',
                'debit': order.total,
                'credit': 0
            })

        # الجانب الدائن (الإيرادات)
        # حساب المبيعات
        items.append({
            'account_id': settings.default_sales_account_id,
            'description': f'إيرادات مبيعات - فاتورة رقم {order.invoice_number}',
            'debit': 0,
            'credit': order.subtotal
        })

        # حساب الضريبة (إذا وجدت)
        if order.tax > 0 and settings.default_tax_account_id:
            items.append({
                'account_id': settings.default_tax_account_id,
                'description': f'ضريبة مبيعات - فاتورة رقم {order.invoice_number}',
                'debit': 0,
                'credit': order.tax
            })

        # قيد تكلفة البضاعة المباعة
        # حساب تكلفة البضاعة المباعة
        total_cost = sum(item.product.cost_price * item.quantity for item in order.items if item.product and item.product.cost_price)

        if total_cost > 0:
            items.append({
                'account_id': settings.default_cost_of_goods_sold_account_id,
                'description': f'تكلفة البضاعة المباعة - فاتورة رقم {order.invoice_number}',
                'debit': total_cost,
                'credit': 0
            })

            # حساب المخزون
            items.append({
                'account_id': settings.default_inventory_account_id,
                'description': f'تخفيض المخزون - فاتورة رقم {order.invoice_number}',
                'debit': 0,
                'credit': total_cost
            })

        # إنشاء القيد المحاسبي
        return cls.create_journal_entry(
            date=order.created_at.date(),
            description=f'قيد مبيعات - فاتورة رقم {order.invoice_number}',
            items=items,
            user_id=user_id or order.user_id,
            entry_type='sale',
            source_document_type='order',
            source_document_id=order.id
        )

    @classmethod
    def create_purchase_journal_entry(cls, purchase, user_id=None):
        """إنشاء قيد محاسبي لعملية شراء"""
        # الحصول على إعدادات المحاسبة
        settings = cls.get_accounting_settings()

        # التحقق من وجود الحسابات الضرورية
        if not settings.default_purchases_account_id:
            return None, 'لم يتم تعيين حساب المشتريات الافتراضي'

        if not settings.default_accounts_payable_account_id and purchase.payment_method != 'cash':
            return None, 'لم يتم تعيين حساب الذمم الدائنة الافتراضي'

        if not settings.default_cash_account_id and purchase.payment_method == 'cash':
            return None, 'لم يتم تعيين حساب النقدية الافتراضي'

        if not settings.default_inventory_account_id:
            return None, 'لم يتم تعيين حساب المخزون الافتراضي'

        # إنشاء بنود القيد
        items = []

        # الجانب المدين (الأصول/المصروفات)
        # حساب المخزون
        items.append({
            'account_id': settings.default_inventory_account_id,
            'description': f'شراء مخزون - فاتورة رقم {purchase.invoice_number}',
            'debit': purchase.subtotal,
            'credit': 0
        })

        # حساب الضريبة (إذا وجدت)
        if purchase.tax > 0 and settings.default_tax_account_id:
            items.append({
                'account_id': settings.default_tax_account_id,
                'description': f'ضريبة مشتريات - فاتورة رقم {purchase.invoice_number}',
                'debit': purchase.tax,
                'credit': 0
            })

        # الجانب الدائن (الخصوم)
        if purchase.payment_method == 'cash':
            # حساب النقدية
            items.append({
                'account_id': settings.default_cash_account_id,
                'description': f'مشتريات نقدية - فاتورة رقم {purchase.invoice_number}',
                'debit': 0,
                'credit': purchase.total
            })
        else:
            # حساب الذمم الدائنة
            items.append({
                'account_id': settings.default_accounts_payable_account_id,
                'description': f'مشتريات آجلة - فاتورة رقم {purchase.invoice_number}',
                'debit': 0,
                'credit': purchase.total
            })

        # إنشاء القيد المحاسبي
        return cls.create_journal_entry(
            date=purchase.created_at.date(),
            description=f'قيد مشتريات - فاتورة رقم {purchase.invoice_number}',
            items=items,
            user_id=user_id or purchase.user_id,
            entry_type='purchase',
            source_document_type='purchase',
            source_document_id=purchase.id
        )

    @classmethod
    def create_payment_journal_entry(cls, payment, user_id=None):
        """إنشاء قيد محاسبي لعملية دفع"""
        # الحصول على إعدادات المحاسبة
        settings = cls.get_accounting_settings()

        # التحقق من نوع الدفع (لعميل أو لمورد)
        if payment.order_id:
            # دفعة من عميل
            order = payment.order

            # التحقق من وجود الحسابات الضرورية
            if not settings.default_cash_account_id and payment.payment_method == 'cash':
                return None, 'لم يتم تعيين حساب النقدية الافتراضي'

            if not settings.default_bank_account_id and payment.payment_method == 'bank':
                return None, 'لم يتم تعيين حساب البنك الافتراضي'

            if not settings.default_accounts_receivable_account_id:
                return None, 'لم يتم تعيين حساب الذمم المدينة الافتراضي'

            # إنشاء بنود القيد
            items = []

            # الجانب المدين (الأصول)
            if payment.payment_method == 'cash':
                # حساب النقدية
                items.append({
                    'account_id': settings.default_cash_account_id,
                    'description': f'دفعة نقدية من عميل - فاتورة رقم {order.invoice_number}',
                    'debit': payment.amount,
                    'credit': 0
                })
            elif payment.payment_method == 'bank':
                # حساب البنك
                items.append({
                    'account_id': settings.default_bank_account_id,
                    'description': f'دفعة بنكية من عميل - فاتورة رقم {order.invoice_number}',
                    'debit': payment.amount,
                    'credit': 0
                })

            # الجانب الدائن (الخصوم)
            # حساب الذمم المدينة
            items.append({
                'account_id': settings.default_accounts_receivable_account_id,
                'description': f'تحصيل من عميل - فاتورة رقم {order.invoice_number}',
                'debit': 0,
                'credit': payment.amount
            })

            # إنشاء القيد المحاسبي
            return cls.create_journal_entry(
                date=payment.payment_date,
                description=f'قيد دفع من عميل - فاتورة رقم {order.invoice_number}',
                items=items,
                user_id=user_id,
                entry_type='payment',
                source_document_type='payment',
                source_document_id=payment.id
            )

        elif payment.purchase_id:
            # دفعة لمورد
            purchase = payment.purchase

            # التحقق من وجود الحسابات الضرورية
            if not settings.default_cash_account_id and payment.payment_method == 'cash':
                return None, 'لم يتم تعيين حساب النقدية الافتراضي'

            if not settings.default_bank_account_id and payment.payment_method == 'bank':
                return None, 'لم يتم تعيين حساب البنك الافتراضي'

            if not settings.default_accounts_payable_account_id:
                return None, 'لم يتم تعيين حساب الذمم الدائنة الافتراضي'

            # إنشاء بنود القيد
            items = []

            # الجانب المدين (الأصول)
            # حساب الذمم الدائنة
            items.append({
                'account_id': settings.default_accounts_payable_account_id,
                'description': f'دفع لمورد - فاتورة رقم {purchase.invoice_number}',
                'debit': payment.amount,
                'credit': 0
            })

            # الجانب الدائن (الخصوم)
            if payment.payment_method == 'cash':
                # حساب النقدية
                items.append({
                    'account_id': settings.default_cash_account_id,
                    'description': f'دفعة نقدية لمورد - فاتورة رقم {purchase.invoice_number}',
                    'debit': 0,
                    'credit': payment.amount
                })
            elif payment.payment_method == 'bank':
                # حساب البنك
                items.append({
                    'account_id': settings.default_bank_account_id,
                    'description': f'دفعة بنكية لمورد - فاتورة رقم {purchase.invoice_number}',
                    'debit': 0,
                    'credit': payment.amount
                })

            # إنشاء القيد المحاسبي
            return cls.create_journal_entry(
                date=payment.payment_date,
                description=f'قيد دفع لمورد - فاتورة رقم {purchase.invoice_number}',
                items=items,
                user_id=user_id,
                entry_type='payment',
                source_document_type='payment',
                source_document_id=payment.id
            )

        return None, 'الدفعة غير مرتبطة بفاتورة بيع أو شراء'

    @classmethod
    def create_return_journal_entry(cls, return_order, user_id=None):
        """إنشاء قيد محاسبي لعملية مرتجع"""
        # الحصول على إعدادات المحاسبة
        settings = cls.get_accounting_settings()

        # التحقق من وجود الحسابات الضرورية
        if not settings.default_sales_return_account_id:
            return None, 'لم يتم تعيين حساب مرتجعات المبيعات الافتراضي'

        if not settings.default_accounts_receivable_account_id and return_order.refund_method != 'cash':
            return None, 'لم يتم تعيين حساب الذمم المدينة الافتراضي'

        if not settings.default_cash_account_id and return_order.refund_method == 'cash':
            return None, 'لم يتم تعيين حساب النقدية الافتراضي'

        if not settings.default_inventory_account_id:
            return None, 'لم يتم تعيين حساب المخزون الافتراضي'

        if not settings.default_cost_of_goods_sold_account_id:
            return None, 'لم يتم تعيين حساب تكلفة البضاعة المباعة الافتراضي'

        # إنشاء بنود القيد
        items = []

        # الجانب المدين (الأصول/المصروفات)
        # حساب مرتجعات المبيعات
        items.append({
            'account_id': settings.default_sales_return_account_id,
            'description': f'مرتجعات مبيعات - فاتورة رقم {return_order.order.invoice_number}',
            'debit': return_order.subtotal,
            'credit': 0
        })

        # حساب الضريبة (إذا وجدت)
        if return_order.tax > 0 and settings.default_tax_account_id:
            items.append({
                'account_id': settings.default_tax_account_id,
                'description': f'ضريبة مرتجعات مبيعات - فاتورة رقم {return_order.order.invoice_number}',
                'debit': return_order.tax,
                'credit': 0
            })

        # الجانب الدائن (الخصوم)
        if return_order.refund_method == 'cash':
            # حساب النقدية
            items.append({
                'account_id': settings.default_cash_account_id,
                'description': f'رد نقدي لمرتجعات مبيعات - فاتورة رقم {return_order.order.invoice_number}',
                'debit': 0,
                'credit': return_order.total
            })
        else:
            # حساب الذمم المدينة
            items.append({
                'account_id': settings.default_accounts_receivable_account_id,
                'description': f'رد لمرتجعات مبيعات - فاتورة رقم {return_order.order.invoice_number}',
                'debit': 0,
                'credit': return_order.total
            })

        # قيد إعادة المخزون
        # حساب المخزون
        total_cost = sum(item.product.cost_price * item.quantity for item in return_order.items if item.product and item.product.cost_price)

        if total_cost > 0:
            items.append({
                'account_id': settings.default_inventory_account_id,
                'description': f'إعادة المخزون - فاتورة رقم {return_order.order.invoice_number}',
                'debit': total_cost,
                'credit': 0
            })

            # حساب تكلفة البضاعة المباعة
            items.append({
                'account_id': settings.default_cost_of_goods_sold_account_id,
                'description': f'تخفيض تكلفة البضاعة المباعة - فاتورة رقم {return_order.order.invoice_number}',
                'debit': 0,
                'credit': total_cost
            })

        # إنشاء القيد المحاسبي
        return cls.create_journal_entry(
            date=return_order.created_at.date(),
            description=f'قيد مرتجعات مبيعات - فاتورة رقم {return_order.order.invoice_number}',
            items=items,
            user_id=user_id or return_order.user_id,
            entry_type='return',
            source_document_type='return_order',
            source_document_id=return_order.id
        )

    @classmethod
    def create_expense_journal_entry(cls, expense, user_id=None):
        """إنشاء قيد محاسبي لعملية مصروفات"""
        # الحصول على إعدادات المحاسبة
        settings = cls.get_accounting_settings()

        # التحقق من وجود الحسابات الضرورية
        if not expense.account_id:
            return None, 'لم يتم تعيين حساب المصروفات'

        if not settings.default_cash_account_id and expense.payment_method == 'cash':
            return None, 'لم يتم تعيين حساب النقدية الافتراضي'

        if not settings.default_bank_account_id and expense.payment_method == 'bank':
            return None, 'لم يتم تعيين حساب البنك الافتراضي'

        # إنشاء بنود القيد
        items = []

        # الجانب المدين (المصروفات)
        items.append({
            'account_id': expense.account_id,
            'description': f'مصروفات - {expense.description}',
            'debit': expense.amount,
            'credit': 0
        })

        # الجانب الدائن (الأصول)
        if expense.payment_method == 'cash':
            # حساب النقدية
            items.append({
                'account_id': settings.default_cash_account_id,
                'description': f'دفع نقدي لمصروفات - {expense.description}',
                'debit': 0,
                'credit': expense.amount
            })
        elif expense.payment_method == 'bank':
            # حساب البنك
            items.append({
                'account_id': settings.default_bank_account_id,
                'description': f'دفع بنكي لمصروفات - {expense.description}',
                'debit': 0,
                'credit': expense.amount
            })

        # إنشاء القيد المحاسبي
        return cls.create_journal_entry(
            date=expense.date,
            description=f'قيد مصروفات - {expense.description}',
            items=items,
            user_id=user_id or expense.user_id,
            entry_type='expense',
            source_document_type='expense',
            source_document_id=expense.id
        )

    @classmethod
    def generate_financial_statements(cls, fiscal_period_id=None, statement_type='balance_sheet'):
        """توليد القوائم المالية"""
        from models.accounting import FinancialStatement, FinancialStatementItem, FiscalPeriod

        # الحصول على الفترة المالية
        if not fiscal_period_id:
            fiscal_period = cls.get_current_fiscal_period()
            if not fiscal_period:
                return None, 'لا توجد فترة مالية مفتوحة'
            fiscal_period_id = fiscal_period.id
        else:
            fiscal_period = FiscalPeriod.query.get(fiscal_period_id)
            if not fiscal_period:
                return None, 'الفترة المالية غير موجودة'

        # إنشاء القائمة المالية
        statement = FinancialStatement(
            statement_type=statement_type,
            fiscal_period_id=fiscal_period_id,
            date=datetime.now().date(),
            status='draft'
        )

        db.session.add(statement)
        db.session.flush()  # للحصول على معرف القائمة المالية

        # توليد بنود القائمة المالية بناءً على نوع القائمة
        if statement_type == 'balance_sheet':
            # الميزانية العمومية
            cls._generate_balance_sheet_items(statement, fiscal_period)
        elif statement_type == 'income_statement':
            # قائمة الدخل
            cls._generate_income_statement_items(statement, fiscal_period)
        elif statement_type == 'cash_flow':
            # قائمة التدفقات النقدية
            cls._generate_cash_flow_items(statement, fiscal_period)

        db.session.commit()

        return statement, 'تم توليد القائمة المالية بنجاح'

    @classmethod
    def _generate_balance_sheet_items(cls, statement, fiscal_period):
        """توليد بنود الميزانية العمومية"""
        from models.accounting import Account, FinancialStatementItem

        # الحصول على جميع الحسابات النشطة
        accounts = Account.query.filter_by(is_active=True).all()

        # تصنيف الحسابات
        assets = [account for account in accounts if account.account_type == 'asset']
        liabilities = [account for account in accounts if account.account_type == 'liability']
        equity = [account for account in accounts if account.account_type == 'equity']

        # إضافة بنود الأصول
        line_number = 1

        # عنوان الأصول
        assets_header = FinancialStatementItem(
            statement_id=statement.id,
            line_number=line_number,
            section='assets',
            description='الأصول',
            is_total=False,
            is_subtotal=False
        )
        db.session.add(assets_header)
        line_number += 1

        # الأصول المتداولة
        current_assets_header = FinancialStatementItem(
            statement_id=statement.id,
            line_number=line_number,
            section='assets',
            subsection='current_assets',
            description='الأصول المتداولة',
            is_total=False,
            is_subtotal=False
        )
        db.session.add(current_assets_header)
        line_number += 1

        # إضافة الأصول المتداولة
        current_assets_total = 0
        for account in [a for a in assets if a.account_subtype == 'current_asset']:
            balance = account.get_balance_for_period(fiscal_period.start_date, fiscal_period.end_date)
            current_assets_total += balance

            item = FinancialStatementItem(
                statement_id=statement.id,
                account_id=account.id,
                line_number=line_number,
                section='assets',
                subsection='current_assets',
                description=account.name,
                amount=balance
            )
            db.session.add(item)
            line_number += 1

        # إجمالي الأصول المتداولة
        current_assets_total_item = FinancialStatementItem(
            statement_id=statement.id,
            line_number=line_number,
            section='assets',
            subsection='current_assets',
            description='إجمالي الأصول المتداولة',
            amount=current_assets_total,
            is_total=False,
            is_subtotal=True
        )
        db.session.add(current_assets_total_item)
        line_number += 1

        # الأصول الثابتة
        fixed_assets_header = FinancialStatementItem(
            statement_id=statement.id,
            line_number=line_number,
            section='assets',
            subsection='fixed_assets',
            description='الأصول الثابتة',
            is_total=False,
            is_subtotal=False
        )
        db.session.add(fixed_assets_header)
        line_number += 1

        # إضافة الأصول الثابتة
        fixed_assets_total = 0
        for account in [a for a in assets if a.account_subtype == 'fixed_asset']:
            balance = account.get_balance_for_period(fiscal_period.start_date, fiscal_period.end_date)
            fixed_assets_total += balance

            item = FinancialStatementItem(
                statement_id=statement.id,
                account_id=account.id,
                line_number=line_number,
                section='assets',
                subsection='fixed_assets',
                description=account.name,
                amount=balance
            )
            db.session.add(item)
            line_number += 1

        # إجمالي الأصول الثابتة
        fixed_assets_total_item = FinancialStatementItem(
            statement_id=statement.id,
            line_number=line_number,
            section='assets',
            subsection='fixed_assets',
            description='إجمالي الأصول الثابتة',
            amount=fixed_assets_total,
            is_total=False,
            is_subtotal=True
        )
        db.session.add(fixed_assets_total_item)
        line_number += 1

        # إجمالي الأصول
        total_assets = current_assets_total + fixed_assets_total
        total_assets_item = FinancialStatementItem(
            statement_id=statement.id,
            line_number=line_number,
            section='assets',
            description='إجمالي الأصول',
            amount=total_assets,
            is_total=True,
            is_subtotal=False
        )
        db.session.add(total_assets_item)
        line_number += 1

        # إضافة بنود الخصوم
        # عنوان الخصوم
        liabilities_header = FinancialStatementItem(
            statement_id=statement.id,
            line_number=line_number,
            section='liabilities',
            description='الخصوم',
            is_total=False,
            is_subtotal=False
        )
        db.session.add(liabilities_header)
        line_number += 1

        # الخصوم المتداولة
        current_liabilities_header = FinancialStatementItem(
            statement_id=statement.id,
            line_number=line_number,
            section='liabilities',
            subsection='current_liabilities',
            description='الخصوم المتداولة',
            is_total=False,
            is_subtotal=False
        )
        db.session.add(current_liabilities_header)
        line_number += 1

        # إضافة الخصوم المتداولة
        current_liabilities_total = 0
        for account in [a for a in liabilities if a.account_subtype == 'current_liability']:
            balance = account.get_balance_for_period(fiscal_period.start_date, fiscal_period.end_date)
            current_liabilities_total += balance

            item = FinancialStatementItem(
                statement_id=statement.id,
                account_id=account.id,
                line_number=line_number,
                section='liabilities',
                subsection='current_liabilities',
                description=account.name,
                amount=balance
            )
            db.session.add(item)
            line_number += 1

        # إجمالي الخصوم المتداولة
        current_liabilities_total_item = FinancialStatementItem(
            statement_id=statement.id,
            line_number=line_number,
            section='liabilities',
            subsection='current_liabilities',
            description='إجمالي الخصوم المتداولة',
            amount=current_liabilities_total,
            is_total=False,
            is_subtotal=True
        )
        db.session.add(current_liabilities_total_item)
        line_number += 1

        # الخصوم طويلة الأجل
        long_term_liabilities_header = FinancialStatementItem(
            statement_id=statement.id,
            line_number=line_number,
            section='liabilities',
            subsection='long_term_liabilities',
            description='الخصوم طويلة الأجل',
            is_total=False,
            is_subtotal=False
        )
        db.session.add(long_term_liabilities_header)
        line_number += 1

        # إضافة الخصوم طويلة الأجل
        long_term_liabilities_total = 0
        for account in [a for a in liabilities if a.account_subtype == 'long_term_liability']:
            balance = account.get_balance_for_period(fiscal_period.start_date, fiscal_period.end_date)
            long_term_liabilities_total += balance

            item = FinancialStatementItem(
                statement_id=statement.id,
                account_id=account.id,
                line_number=line_number,
                section='liabilities',
                subsection='long_term_liabilities',
                description=account.name,
                amount=balance
            )
            db.session.add(item)
            line_number += 1

        # إجمالي الخصوم طويلة الأجل
        long_term_liabilities_total_item = FinancialStatementItem(
            statement_id=statement.id,
            line_number=line_number,
            section='liabilities',
            subsection='long_term_liabilities',
            description='إجمالي الخصوم طويلة الأجل',
            amount=long_term_liabilities_total,
            is_total=False,
            is_subtotal=True
        )
        db.session.add(long_term_liabilities_total_item)
        line_number += 1

        # إجمالي الخصوم
        total_liabilities = current_liabilities_total + long_term_liabilities_total
        total_liabilities_item = FinancialStatementItem(
            statement_id=statement.id,
            line_number=line_number,
            section='liabilities',
            description='إجمالي الخصوم',
            amount=total_liabilities,
            is_total=True,
            is_subtotal=False
        )
        db.session.add(total_liabilities_item)
        line_number += 1

        # إضافة بنود حقوق الملكية
        # عنوان حقوق الملكية
        equity_header = FinancialStatementItem(
            statement_id=statement.id,
            line_number=line_number,
            section='equity',
            description='حقوق الملكية',
            is_total=False,
            is_subtotal=False
        )
        db.session.add(equity_header)
        line_number += 1

        # إضافة حقوق الملكية
        equity_total = 0
        for account in equity:
            balance = account.get_balance_for_period(fiscal_period.start_date, fiscal_period.end_date)
            equity_total += balance

            item = FinancialStatementItem(
                statement_id=statement.id,
                account_id=account.id,
                line_number=line_number,
                section='equity',
                description=account.name,
                amount=balance
            )
            db.session.add(item)
            line_number += 1

        # إجمالي حقوق الملكية
        equity_total_item = FinancialStatementItem(
            statement_id=statement.id,
            line_number=line_number,
            section='equity',
            description='إجمالي حقوق الملكية',
            amount=equity_total,
            is_total=True,
            is_subtotal=False
        )
        db.session.add(equity_total_item)
        line_number += 1

        # إجمالي الخصوم وحقوق الملكية
        total_liabilities_equity = total_liabilities + equity_total
        total_liabilities_equity_item = FinancialStatementItem(
            statement_id=statement.id,
            line_number=line_number,
            section='liabilities_equity',
            description='إجمالي الخصوم وحقوق الملكية',
            amount=total_liabilities_equity,
            is_total=True,
            is_subtotal=False
        )
        db.session.add(total_liabilities_equity_item)

    @classmethod
    def _generate_income_statement_items(cls, statement, fiscal_period):
        """توليد بنود قائمة الدخل"""
        from models.accounting import Account, FinancialStatementItem

        # الحصول على جميع الحسابات النشطة
        accounts = Account.query.filter_by(is_active=True).all()

        # تصنيف الحسابات
        revenues = [account for account in accounts if account.account_type == 'revenue']
        expenses = [account for account in accounts if account.account_type == 'expense']

        # إضافة بنود الإيرادات
        line_number = 1

        # عنوان الإيرادات
        revenues_header = FinancialStatementItem(
            statement_id=statement.id,
            line_number=line_number,
            section='revenues',
            description='الإيرادات',
            is_total=False,
            is_subtotal=False
        )
        db.session.add(revenues_header)
        line_number += 1

        # إضافة الإيرادات
        revenues_total = 0
        for account in revenues:
            balance = account.get_balance_for_period(fiscal_period.start_date, fiscal_period.end_date)
            revenues_total += balance

            item = FinancialStatementItem(
                statement_id=statement.id,
                account_id=account.id,
                line_number=line_number,
                section='revenues',
                description=account.name,
                amount=balance
            )
            db.session.add(item)
            line_number += 1

        # إجمالي الإيرادات
        revenues_total_item = FinancialStatementItem(
            statement_id=statement.id,
            line_number=line_number,
            section='revenues',
            description='إجمالي الإيرادات',
            amount=revenues_total,
            is_total=True,
            is_subtotal=False
        )
        db.session.add(revenues_total_item)
        line_number += 1

        # تكلفة البضاعة المباعة
        cogs_accounts = [account for account in expenses if account.account_subtype == 'cost_of_goods_sold']
        cogs_total = 0

        if cogs_accounts:
            # عنوان تكلفة البضاعة المباعة
            cogs_header = FinancialStatementItem(
                statement_id=statement.id,
                line_number=line_number,
                section='expenses',
                subsection='cost_of_goods_sold',
                description='تكلفة البضاعة المباعة',
                is_total=False,
                is_subtotal=False
            )
            db.session.add(cogs_header)
            line_number += 1

            # إضافة تكلفة البضاعة المباعة
            for account in cogs_accounts:
                balance = account.get_balance_for_period(fiscal_period.start_date, fiscal_period.end_date)
                cogs_total += balance

                item = FinancialStatementItem(
                    statement_id=statement.id,
                    account_id=account.id,
                    line_number=line_number,
                    section='expenses',
                    subsection='cost_of_goods_sold',
                    description=account.name,
                    amount=balance
                )
                db.session.add(item)
                line_number += 1

            # إجمالي تكلفة البضاعة المباعة
            cogs_total_item = FinancialStatementItem(
                statement_id=statement.id,
                line_number=line_number,
                section='expenses',
                subsection='cost_of_goods_sold',
                description='إجمالي تكلفة البضاعة المباعة',
                amount=cogs_total,
                is_total=False,
                is_subtotal=True
            )
            db.session.add(cogs_total_item)
            line_number += 1

        # إجمالي الربح
        gross_profit = revenues_total - cogs_total
        gross_profit_item = FinancialStatementItem(
            statement_id=statement.id,
            line_number=line_number,
            section='profit',
            description='إجمالي الربح',
            amount=gross_profit,
            is_total=False,
            is_subtotal=True
        )
        db.session.add(gross_profit_item)
        line_number += 1

        # المصروفات التشغيلية
        operating_expenses = [account for account in expenses if account.account_subtype == 'operating_expense']
        operating_expenses_total = 0

        if operating_expenses:
            # عنوان المصروفات التشغيلية
            operating_expenses_header = FinancialStatementItem(
                statement_id=statement.id,
                line_number=line_number,
                section='expenses',
                subsection='operating_expenses',
                description='المصروفات التشغيلية',
                is_total=False,
                is_subtotal=False
            )
            db.session.add(operating_expenses_header)
            line_number += 1

            # إضافة المصروفات التشغيلية
            for account in operating_expenses:
                balance = account.get_balance_for_period(fiscal_period.start_date, fiscal_period.end_date)
                operating_expenses_total += balance

                item = FinancialStatementItem(
                    statement_id=statement.id,
                    account_id=account.id,
                    line_number=line_number,
                    section='expenses',
                    subsection='operating_expenses',
                    description=account.name,
                    amount=balance
                )
                db.session.add(item)
                line_number += 1

            # إجمالي المصروفات التشغيلية
            operating_expenses_total_item = FinancialStatementItem(
                statement_id=statement.id,
                line_number=line_number,
                section='expenses',
                subsection='operating_expenses',
                description='إجمالي المصروفات التشغيلية',
                amount=operating_expenses_total,
                is_total=False,
                is_subtotal=True
            )
            db.session.add(operating_expenses_total_item)
            line_number += 1

        # المصروفات الإدارية والعمومية
        general_expenses = [account for account in expenses if account.account_subtype == 'general_expense']
        general_expenses_total = 0

        if general_expenses:
            # عنوان المصروفات الإدارية والعمومية
            general_expenses_header = FinancialStatementItem(
                statement_id=statement.id,
                line_number=line_number,
                section='expenses',
                subsection='general_expenses',
                description='المصروفات الإدارية والعمومية',
                is_total=False,
                is_subtotal=False
            )
            db.session.add(general_expenses_header)
            line_number += 1

            # إضافة المصروفات الإدارية والعمومية
            for account in general_expenses:
                balance = account.get_balance_for_period(fiscal_period.start_date, fiscal_period.end_date)
                general_expenses_total += balance

                item = FinancialStatementItem(
                    statement_id=statement.id,
                    account_id=account.id,
                    line_number=line_number,
                    section='expenses',
                    subsection='general_expenses',
                    description=account.name,
                    amount=balance
                )
                db.session.add(item)
                line_number += 1

            # إجمالي المصروفات الإدارية والعمومية
            general_expenses_total_item = FinancialStatementItem(
                statement_id=statement.id,
                line_number=line_number,
                section='expenses',
                subsection='general_expenses',
                description='إجمالي المصروفات الإدارية والعمومية',
                amount=general_expenses_total,
                is_total=False,
                is_subtotal=True
            )
            db.session.add(general_expenses_total_item)
            line_number += 1

        # إجمالي المصروفات
        total_expenses = cogs_total + operating_expenses_total + general_expenses_total
        total_expenses_item = FinancialStatementItem(
            statement_id=statement.id,
            line_number=line_number,
            section='expenses',
            description='إجمالي المصروفات',
            amount=total_expenses,
            is_total=True,
            is_subtotal=False
        )
        db.session.add(total_expenses_item)
        line_number += 1

        # صافي الربح
        net_profit = revenues_total - total_expenses
        net_profit_item = FinancialStatementItem(
            statement_id=statement.id,
            line_number=line_number,
            section='profit',
            description='صافي الربح',
            amount=net_profit,
            is_total=True,
            is_subtotal=False
        )
        db.session.add(net_profit_item)

    @classmethod
    def _generate_cash_flow_items(cls, statement, fiscal_period):
        """توليد بنود قائمة التدفقات النقدية"""
        from models.accounting import Account, JournalEntry, JournalEntryItem, FinancialStatementItem

        # الحصول على حسابات النقدية والبنوك
        cash_accounts = Account.query.filter(
            Account.account_type == 'asset',
            Account.is_active == True,
            (Account.is_cash_account == True) | (Account.is_bank_account == True)
        ).all()

        if not cash_accounts:
            return

        # الحصول على الرصيد الافتتاحي للنقدية
        opening_balance = 0
        for account in cash_accounts:
            # الحصول على الرصيد في بداية الفترة
            opening_balance += account.get_balance_for_period(
                fiscal_period.start_date - timedelta(days=1),
                fiscal_period.start_date - timedelta(days=1)
            )

        # إضافة بنود التدفقات النقدية
        line_number = 1

        # الرصيد الافتتاحي
        opening_balance_item = FinancialStatementItem(
            statement_id=statement.id,
            line_number=line_number,
            section='opening_balance',
            description='الرصيد الافتتاحي للنقدية',
            amount=opening_balance,
            is_total=False,
            is_subtotal=False
        )
        db.session.add(opening_balance_item)
        line_number += 1

        # التدفقات النقدية من الأنشطة التشغيلية
        operating_cash_flow_header = FinancialStatementItem(
            statement_id=statement.id,
            line_number=line_number,
            section='operating_activities',
            description='التدفقات النقدية من الأنشطة التشغيلية',
            is_total=False,
            is_subtotal=False
        )
        db.session.add(operating_cash_flow_header)
        line_number += 1

        # الحصول على القيود المحاسبية للفترة
        journal_entries = JournalEntry.query.filter(
            JournalEntry.date >= fiscal_period.start_date,
            JournalEntry.date <= fiscal_period.end_date,
            JournalEntry.status == 'posted'
        ).all()

        # تصنيف القيود حسب النوع
        operating_entries = [entry for entry in journal_entries if entry.entry_type in ['sale', 'purchase', 'expense', 'receipt', 'payment']]
        investing_entries = [entry for entry in journal_entries if entry.entry_type in ['asset_purchase', 'asset_sale']]
        financing_entries = [entry for entry in journal_entries if entry.entry_type in ['loan', 'capital']]

        # حساب التدفقات النقدية من الأنشطة التشغيلية
        operating_cash_inflow = 0
        operating_cash_outflow = 0

        for entry in operating_entries:
            for item in entry.items:
                if item.account.is_cash_account or item.account.is_bank_account:
                    if item.debit > 0:
                        operating_cash_inflow += item.debit
                    elif item.credit > 0:
                        operating_cash_outflow += item.credit

        # إضافة التدفقات النقدية الواردة من الأنشطة التشغيلية
        operating_inflow_item = FinancialStatementItem(
            statement_id=statement.id,
            line_number=line_number,
            section='operating_activities',
            subsection='inflow',
            description='التدفقات النقدية الواردة من الأنشطة التشغيلية',
            amount=operating_cash_inflow,
            is_total=False,
            is_subtotal=False
        )
        db.session.add(operating_inflow_item)
        line_number += 1

        # إضافة التدفقات النقدية الصادرة من الأنشطة التشغيلية
        operating_outflow_item = FinancialStatementItem(
            statement_id=statement.id,
            line_number=line_number,
            section='operating_activities',
            subsection='outflow',
            description='التدفقات النقدية الصادرة من الأنشطة التشغيلية',
            amount=operating_cash_outflow,
            is_total=False,
            is_subtotal=False
        )
        db.session.add(operating_outflow_item)
        line_number += 1

        # صافي التدفقات النقدية من الأنشطة التشغيلية
        net_operating_cash_flow = operating_cash_inflow - operating_cash_outflow
        net_operating_cash_flow_item = FinancialStatementItem(
            statement_id=statement.id,
            line_number=line_number,
            section='operating_activities',
            description='صافي التدفقات النقدية من الأنشطة التشغيلية',
            amount=net_operating_cash_flow,
            is_total=False,
            is_subtotal=True
        )
        db.session.add(net_operating_cash_flow_item)
        line_number += 1

        # التدفقات النقدية من الأنشطة الاستثمارية
        investing_cash_flow_header = FinancialStatementItem(
            statement_id=statement.id,
            line_number=line_number,
            section='investing_activities',
            description='التدفقات النقدية من الأنشطة الاستثمارية',
            is_total=False,
            is_subtotal=False
        )
        db.session.add(investing_cash_flow_header)
        line_number += 1

        # حساب التدفقات النقدية من الأنشطة الاستثمارية
        investing_cash_inflow = 0
        investing_cash_outflow = 0

        for entry in investing_entries:
            for item in entry.items:
                if item.account.is_cash_account or item.account.is_bank_account:
                    if item.debit > 0:
                        investing_cash_inflow += item.debit
                    elif item.credit > 0:
                        investing_cash_outflow += item.credit

        # إضافة التدفقات النقدية الواردة من الأنشطة الاستثمارية
        investing_inflow_item = FinancialStatementItem(
            statement_id=statement.id,
            line_number=line_number,
            section='investing_activities',
            subsection='inflow',
            description='التدفقات النقدية الواردة من الأنشطة الاستثمارية',
            amount=investing_cash_inflow,
            is_total=False,
            is_subtotal=False
        )
        db.session.add(investing_inflow_item)
        line_number += 1

        # إضافة التدفقات النقدية الصادرة من الأنشطة الاستثمارية
        investing_outflow_item = FinancialStatementItem(
            statement_id=statement.id,
            line_number=line_number,
            section='investing_activities',
            subsection='outflow',
            description='التدفقات النقدية الصادرة من الأنشطة الاستثمارية',
            amount=investing_cash_outflow,
            is_total=False,
            is_subtotal=False
        )
        db.session.add(investing_outflow_item)
        line_number += 1

        # صافي التدفقات النقدية من الأنشطة الاستثمارية
        net_investing_cash_flow = investing_cash_inflow - investing_cash_outflow
        net_investing_cash_flow_item = FinancialStatementItem(
            statement_id=statement.id,
            line_number=line_number,
            section='investing_activities',
            description='صافي التدفقات النقدية من الأنشطة الاستثمارية',
            amount=net_investing_cash_flow,
            is_total=False,
            is_subtotal=True
        )
        db.session.add(net_investing_cash_flow_item)
        line_number += 1

        # التدفقات النقدية من الأنشطة التمويلية
        financing_cash_flow_header = FinancialStatementItem(
            statement_id=statement.id,
            line_number=line_number,
            section='financing_activities',
            description='التدفقات النقدية من الأنشطة التمويلية',
            is_total=False,
            is_subtotal=False
        )
        db.session.add(financing_cash_flow_header)
        line_number += 1

        # حساب التدفقات النقدية من الأنشطة التمويلية
        financing_cash_inflow = 0
        financing_cash_outflow = 0

        for entry in financing_entries:
            for item in entry.items:
                if item.account.is_cash_account or item.account.is_bank_account:
                    if item.debit > 0:
                        financing_cash_inflow += item.debit
                    elif item.credit > 0:
                        financing_cash_outflow += item.credit

        # إضافة التدفقات النقدية الواردة من الأنشطة التمويلية
        financing_inflow_item = FinancialStatementItem(
            statement_id=statement.id,
            line_number=line_number,
            section='financing_activities',
            subsection='inflow',
            description='التدفقات النقدية الواردة من الأنشطة التمويلية',
            amount=financing_cash_inflow,
            is_total=False,
            is_subtotal=False
        )
        db.session.add(financing_inflow_item)
        line_number += 1

        # إضافة التدفقات النقدية الصادرة من الأنشطة التمويلية
        financing_outflow_item = FinancialStatementItem(
            statement_id=statement.id,
            line_number=line_number,
            section='financing_activities',
            subsection='outflow',
            description='التدفقات النقدية الصادرة من الأنشطة التمويلية',
            amount=financing_cash_outflow,
            is_total=False,
            is_subtotal=False
        )
        db.session.add(financing_outflow_item)
        line_number += 1

        # صافي التدفقات النقدية من الأنشطة التمويلية
        net_financing_cash_flow = financing_cash_inflow - financing_cash_outflow
        net_financing_cash_flow_item = FinancialStatementItem(
            statement_id=statement.id,
            line_number=line_number,
            section='financing_activities',
            description='صافي التدفقات النقدية من الأنشطة التمويلية',
            amount=net_financing_cash_flow,
            is_total=False,
            is_subtotal=True
        )
        db.session.add(net_financing_cash_flow_item)
        line_number += 1

        # صافي التغير في النقدية
        net_change_in_cash = net_operating_cash_flow + net_investing_cash_flow + net_financing_cash_flow
        net_change_in_cash_item = FinancialStatementItem(
            statement_id=statement.id,
            line_number=line_number,
            section='net_change',
            description='صافي التغير في النقدية',
            amount=net_change_in_cash,
            is_total=True,
            is_subtotal=False
        )
        db.session.add(net_change_in_cash_item)
        line_number += 1

        # الرصيد الختامي للنقدية
        closing_balance = opening_balance + net_change_in_cash
        closing_balance_item = FinancialStatementItem(
            statement_id=statement.id,
            line_number=line_number,
            section='closing_balance',
            description='الرصيد الختامي للنقدية',
            amount=closing_balance,
            is_total=True,
            is_subtotal=False
        )
        db.session.add(closing_balance_item)

    @classmethod
    def calculate_financial_ratios(cls, fiscal_period_id=None):
        """حساب النسب المالية"""
        from models.accounting import FinancialRatio, FiscalPeriod, Account

        # الحصول على الفترة المالية
        if not fiscal_period_id:
            fiscal_period = cls.get_current_fiscal_period()
            if not fiscal_period:
                return None, 'لا توجد فترة مالية مفتوحة'
            fiscal_period_id = fiscal_period.id
        else:
            fiscal_period = FiscalPeriod.query.get(fiscal_period_id)
            if not fiscal_period:
                return None, 'الفترة المالية غير موجودة'

        # الحصول على جميع الحسابات النشطة
        accounts = Account.query.filter_by(is_active=True).all()

        # تصنيف الحسابات
        assets = [account for account in accounts if account.account_type == 'asset']
        liabilities = [account for account in accounts if account.account_type == 'liability']
        equity = [account for account in accounts if account.account_type == 'equity']
        revenues = [account for account in accounts if account.account_type == 'revenue']
        expenses = [account for account in accounts if account.account_type == 'expense']

        # حساب إجمالي الأصول
        total_assets = sum(account.get_balance_for_period(fiscal_period.start_date, fiscal_period.end_date) for account in assets)

        # حساب إجمالي الخصوم
        total_liabilities = sum(account.get_balance_for_period(fiscal_period.start_date, fiscal_period.end_date) for account in liabilities)

        # حساب إجمالي حقوق الملكية
        total_equity = sum(account.get_balance_for_period(fiscal_period.start_date, fiscal_period.end_date) for account in equity)

        # حساب إجمالي الإيرادات
        total_revenues = sum(account.get_balance_for_period(fiscal_period.start_date, fiscal_period.end_date) for account in revenues)

        # حساب إجمالي المصروفات
        total_expenses = sum(account.get_balance_for_period(fiscal_period.start_date, fiscal_period.end_date) for account in expenses)

        # حساب صافي الربح
        net_profit = total_revenues - total_expenses

        # حساب النسب المالية
        ratios = []

        # نسبة السيولة (الأصول المتداولة / الخصوم المتداولة)
        current_assets = sum(account.get_balance_for_period(fiscal_period.start_date, fiscal_period.end_date) for account in assets if account.account_subtype == 'current_asset')
        current_liabilities = sum(account.get_balance_for_period(fiscal_period.start_date, fiscal_period.end_date) for account in liabilities if account.account_subtype == 'current_liability')

        if current_liabilities > 0:
            current_ratio = current_assets / current_liabilities
        else:
            current_ratio = 0

        ratios.append({
            'code': 'current_ratio',
            'name': 'نسبة السيولة',
            'value': current_ratio,
            'category': 'liquidity'
        })

        # نسبة الديون إلى الأصول (إجمالي الخصوم / إجمالي الأصول)
        if total_assets > 0:
            debt_to_assets_ratio = total_liabilities / total_assets
        else:
            debt_to_assets_ratio = 0

        ratios.append({
            'code': 'debt_to_assets_ratio',
            'name': 'نسبة الديون إلى الأصول',
            'value': debt_to_assets_ratio,
            'category': 'solvency'
        })

        # نسبة الديون إلى حقوق الملكية (إجمالي الخصوم / إجمالي حقوق الملكية)
        if total_equity > 0:
            debt_to_equity_ratio = total_liabilities / total_equity
        else:
            debt_to_equity_ratio = 0

        ratios.append({
            'code': 'debt_to_equity_ratio',
            'name': 'نسبة الديون إلى حقوق الملكية',
            'value': debt_to_equity_ratio,
            'category': 'solvency'
        })

        # العائد على الأصول (صافي الربح / إجمالي الأصول)
        if total_assets > 0:
            return_on_assets = net_profit / total_assets
        else:
            return_on_assets = 0

        ratios.append({
            'code': 'return_on_assets',
            'name': 'العائد على الأصول',
            'value': return_on_assets,
            'category': 'profitability'
        })

        # العائد على حقوق الملكية (صافي الربح / إجمالي حقوق الملكية)
        if total_equity > 0:
            return_on_equity = net_profit / total_equity
        else:
            return_on_equity = 0

        ratios.append({
            'code': 'return_on_equity',
            'name': 'العائد على حقوق الملكية',
            'value': return_on_equity,
            'category': 'profitability'
        })

        # هامش الربح الصافي (صافي الربح / إجمالي الإيرادات)
        if total_revenues > 0:
            net_profit_margin = net_profit / total_revenues
        else:
            net_profit_margin = 0

        ratios.append({
            'code': 'net_profit_margin',
            'name': 'هامش الربح الصافي',
            'value': net_profit_margin,
            'category': 'profitability'
        })

        return ratios, 'تم حساب النسب المالية بنجاح'
