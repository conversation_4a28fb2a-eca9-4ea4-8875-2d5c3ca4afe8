from flask import Blueprint, render_template, request, jsonify, flash, redirect, url_for
from flask_login import login_required, current_user
from models import Shift, CashRegister, CashTransaction, Order, User, Notification
from app import db
from sqlalchemy import func, desc
from datetime import datetime, timedelta
import json
from routes.settings import load_settings

shifts_blueprint = Blueprint('shifts', __name__)

@shifts_blueprint.route('/shifts')
@login_required
def shifts_index():
    """صفحة الشيفتات الرئيسية"""
    # التحقق من صلاحيات المستخدم
    if not current_user.has_permission('shifts', 'view'):
        flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'error')
        return redirect(url_for('dashboard.index'))

    # الحصول على الشيفتات
    shifts_query = Shift.query

    # تطبيق الفلاتر
    status = request.args.get('status')
    user_id = request.args.get('user_id')
    register_id = request.args.get('register_id')
    date_from = request.args.get('date_from')
    date_to = request.args.get('date_to')

    if status:
        shifts_query = shifts_query.filter_by(status=status)

    if user_id:
        shifts_query = shifts_query.filter_by(user_id=user_id)

    if register_id:
        shifts_query = shifts_query.filter_by(cash_register_id=register_id)

    if date_from:
        date_from = datetime.strptime(date_from, '%Y-%m-%d')
        shifts_query = shifts_query.filter(Shift.start_time >= date_from)

    if date_to:
        date_to = datetime.strptime(date_to, '%Y-%m-%d')
        date_to = date_to + timedelta(days=1)  # لتضمين اليوم المحدد
        shifts_query = shifts_query.filter(Shift.start_time < date_to)

    # ترتيب الشيفتات
    shifts = shifts_query.order_by(desc(Shift.start_time)).all()

    # الحصول على الخزائن والمستخدمين للفلترة
    cash_registers = CashRegister.query.filter_by(is_active=True).all()
    users = User.query.all()

    # تحميل إعدادات المتجر
    settings = load_settings()

    return render_template(
        'shifts/index.html',
        shifts=shifts,
        cash_registers=cash_registers,
        users=users,
        selected_status=status,
        selected_user=user_id,
        selected_register=register_id,
        date_from=date_from.strftime('%Y-%m-%d') if date_from else '',
        date_to=date_to.strftime('%Y-%m-%d') if date_to and isinstance(date_to, datetime) else '',
        settings=settings
    )

@shifts_blueprint.route('/shifts/open', methods=['GET', 'POST'])
@login_required
def open_shift():
    """فتح شيفت جديد"""
    # التحقق من صلاحيات المستخدم
    if not current_user.has_permission('shifts', 'add'):
        flash('ليس لديك صلاحية لفتح شيفت جديد', 'error')
        return redirect(url_for('shifts.shifts_index'))

    # التحقق من عدم وجود شيفت مفتوح للمستخدم الحالي
    open_shift = Shift.query.filter_by(user_id=current_user.id, status='open').first()
    if open_shift:
        flash('لديك شيفت مفتوح بالفعل', 'error')
        return redirect(url_for('shifts.shift_details', shift_id=open_shift.id))

    # الحصول على جميع الخزائن النشطة
    cash_registers = CashRegister.query.filter_by(is_active=True).all()

    if request.method == 'POST':
        register_id = request.form.get('register_id')
        start_balance = float(request.form.get('start_balance', 0))
        notes = request.form.get('notes')

        # التحقق من البيانات
        if not register_id:
            flash('يرجى اختيار الخزينة', 'error')
            return redirect(url_for('shifts.open_shift'))

        # الحصول على الخزينة
        cash_register = CashRegister.query.get_or_404(register_id)

        # التحقق من عدم وجود شيفت مفتوح لنفس الخزينة
        open_shift_for_register = Shift.query.filter_by(cash_register_id=register_id, status='open').first()
        if open_shift_for_register:
            flash(f'يوجد شيفت مفتوح بالفعل لهذه الخزينة بواسطة {open_shift_for_register.user.username}', 'error')
            return redirect(url_for('shifts.open_shift'))

        # إنشاء شيفت جديد
        shift = Shift(
            cash_register_id=register_id,
            user_id=current_user.id,
            start_balance=start_balance,
            notes=notes
        )

        db.session.add(shift)
        db.session.commit()

        flash('تم فتح الشيفت بنجاح', 'success')
        return redirect(url_for('shifts.shift_details', shift_id=shift.id))

    # تحميل إعدادات المتجر
    settings = load_settings()
    return render_template('shifts/open_shift.html', cash_registers=cash_registers, settings=settings)

@shifts_blueprint.route('/shifts/<int:shift_id>')
@login_required
def shift_details(shift_id):
    """تفاصيل الشيفت"""
    # التحقق من صلاحيات المستخدم
    if not current_user.has_permission('shifts', 'view'):
        flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'error')
        return redirect(url_for('dashboard.index'))

    # الحصول على الشيفت
    shift = Shift.query.get_or_404(shift_id)

    # الحصول على المعاملات المرتبطة بالشيفت
    transactions = CashTransaction.query.filter_by(shift_id=shift.id).order_by(desc(CashTransaction.created_at)).all()

    # الحصول على المبيعات المرتبطة بالشيفت
    sales = Order.query.filter(
        Order.created_at.between(shift.start_time, shift.end_time or datetime.utcnow()),
        Order.user_id == shift.user_id,
        Order.status == 'completed'
    ).order_by(desc(Order.created_at)).all()

    # حساب إجمالي المبيعات
    total_sales = sum(order.total for order in sales)

    # حساب إجمالي المبيعات النقدية
    cash_sales = sum(order.total for order in sales if order.payment_method == 'cash')

    # حساب إجمالي المبيعات بالبطاقة
    card_sales = sum(order.total for order in sales if order.payment_method == 'card')

    # حساب إجمالي الإيداعات
    deposits = db.session.query(func.sum(CashTransaction.amount)).filter(
        CashTransaction.shift_id == shift.id,
        CashTransaction.transaction_type == 'deposit'
    ).scalar() or 0

    # حساب إجمالي السحوبات
    withdrawals = db.session.query(func.sum(CashTransaction.amount)).filter(
        CashTransaction.shift_id == shift.id,
        CashTransaction.transaction_type == 'withdraw'
    ).scalar() or 0

    # تحميل إعدادات المتجر
    settings = load_settings()

    return render_template(
        'shifts/details.html',
        shift=shift,
        transactions=transactions,
        sales=sales,
        total_sales=total_sales,
        cash_sales=cash_sales,
        card_sales=card_sales,
        deposits=deposits,
        withdrawals=withdrawals,
        settings=settings
    )

@shifts_blueprint.route('/shifts/<int:shift_id>/close', methods=['GET', 'POST'])
@login_required
def close_shift(shift_id):
    """إغلاق الشيفت"""
    # التحقق من صلاحيات المستخدم
    if not current_user.has_permission('shifts', 'edit'):
        flash('ليس لديك صلاحية لإغلاق الشيفت', 'error')
        return redirect(url_for('shifts.shifts_index'))

    # الحصول على الشيفت
    shift = Shift.query.get_or_404(shift_id)

    # التحقق من أن الشيفت مفتوح
    if shift.status != 'open':
        flash('الشيفت مغلق بالفعل', 'error')
        return redirect(url_for('shifts.shift_details', shift_id=shift.id))

    # التحقق من أن المستخدم الحالي هو صاحب الشيفت أو مدير
    if shift.user_id != current_user.id and current_user.role != 'admin':
        flash('ليس لديك صلاحية لإغلاق هذا الشيفت', 'error')
        return redirect(url_for('shifts.shifts_index'))

    if request.method == 'POST':
        end_balance = float(request.form.get('end_balance', 0))
        notes = request.form.get('notes')

        # إغلاق الشيفت
        success, message = shift.close_shift(end_balance, notes)

        if success:
            # تحديث رصيد الخزينة
            cash_register = shift.cash_register
            cash_register.current_balance = end_balance

            db.session.commit()

            flash(message, 'success')
            return redirect(url_for('shifts.shift_details', shift_id=shift.id))
        else:
            flash(message, 'error')
            return redirect(url_for('shifts.close_shift', shift_id=shift.id))

    # حساب الرصيد المتوقع
    sales_total = db.session.query(func.sum(Order.total)).filter(
        Order.created_at.between(shift.start_time, datetime.utcnow()),
        Order.user_id == shift.user_id,
        Order.payment_method == 'cash',
        Order.status == 'completed'
    ).scalar() or 0

    deposits = db.session.query(func.sum(CashTransaction.amount)).filter(
        CashTransaction.created_at.between(shift.start_time, datetime.utcnow()),
        CashTransaction.cash_register_id == shift.cash_register_id,
        CashTransaction.transaction_type == 'deposit',
        CashTransaction.shift_id == shift.id
    ).scalar() or 0

    withdrawals = db.session.query(func.sum(CashTransaction.amount)).filter(
        CashTransaction.created_at.between(shift.start_time, datetime.utcnow()),
        CashTransaction.cash_register_id == shift.cash_register_id,
        CashTransaction.transaction_type == 'withdraw',
        CashTransaction.shift_id == shift.id
    ).scalar() or 0

    expected_balance = shift.start_balance + sales_total + deposits - withdrawals

    # تحميل إعدادات المتجر
    settings = load_settings()

    return render_template(
        'shifts/close_shift.html',
        shift=shift,
        expected_balance=expected_balance,
        sales_total=sales_total,
        deposits=deposits,
        withdrawals=withdrawals,
        settings=settings
    )

@shifts_blueprint.route('/shifts/<int:shift_id>/report')
@login_required
def shift_report(shift_id):
    """تقرير الشيفت"""
    # التحقق من صلاحيات المستخدم
    if not current_user.has_permission('shifts', 'view'):
        flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'error')
        return redirect(url_for('dashboard.index'))

    # الحصول على الشيفت
    shift = Shift.query.get_or_404(shift_id)

    # الحصول على المعاملات المرتبطة بالشيفت
    transactions = CashTransaction.query.filter_by(shift_id=shift.id).order_by(desc(CashTransaction.created_at)).all()

    # الحصول على المبيعات المرتبطة بالشيفت
    sales = Order.query.filter(
        Order.created_at.between(shift.start_time, shift.end_time or datetime.utcnow()),
        Order.user_id == shift.user_id,
        Order.status == 'completed'
    ).order_by(desc(Order.created_at)).all()

    # حساب إجمالي المبيعات
    total_sales = sum(order.total for order in sales)

    # حساب إجمالي المبيعات النقدية
    cash_sales = sum(order.total for order in sales if order.payment_method == 'cash')

    # حساب إجمالي المبيعات بالبطاقة
    card_sales = sum(order.total for order in sales if order.payment_method == 'card')

    # حساب إجمالي الإيداعات
    deposits = db.session.query(func.sum(CashTransaction.amount)).filter(
        CashTransaction.shift_id == shift.id,
        CashTransaction.transaction_type == 'deposit'
    ).scalar() or 0

    # حساب إجمالي السحوبات
    withdrawals = db.session.query(func.sum(CashTransaction.amount)).filter(
        CashTransaction.shift_id == shift.id,
        CashTransaction.transaction_type == 'withdraw'
    ).scalar() or 0

    # تحميل إعدادات المتجر
    settings = load_settings()

    return render_template(
        'shifts/report.html',
        shift=shift,
        transactions=transactions,
        sales=sales,
        total_sales=total_sales,
        cash_sales=cash_sales,
        card_sales=card_sales,
        deposits=deposits,
        withdrawals=withdrawals,
        settings=settings
    )
