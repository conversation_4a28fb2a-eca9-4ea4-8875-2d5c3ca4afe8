/**
 * نظام الإشعارات المتطور
 * يوفر واجهة برمجية سهلة لعرض الإشعارات بتصميم احترافي
 */

class NotificationsSystem {
    constructor(options = {}) {
        // الإعدادات الافتراضية
        this.options = {
            containerSelector: options.containerSelector || '#notification-container',
            apiEndpoint: options.apiEndpoint || '/api/notifications',
            autoUpdate: options.autoUpdate !== undefined ? options.autoUpdate : true,
            updateInterval: options.updateInterval || 30000, // 30 seconds
            maxNotifications: options.maxNotifications || 5,
            darkMode: options.darkMode || false,
            position: options.position || 'top-right', // top-right, top-left, bottom-right, bottom-left, top-center, bottom-center
            animationDuration: options.animationDuration || 300,
            defaultDuration: options.defaultDuration || 5000, // 5 seconds
            sounds: options.sounds || {
                enabled: false,
                success: '/static/sounds/success.mp3',
                error: '/static/sounds/error.mp3',
                warning: '/static/sounds/warning.mp3',
                info: '/static/sounds/info.mp3'
            }
        };

        // إنشاء حاوية الإشعارات إذا لم تكن موجودة
        this.createContainer();

        // بدء التحديث التلقائي إذا كان مفعلاً
        if (this.options.autoUpdate) {
            this.startAutoUpdate();
        }

        // تهيئة الأصوات إذا كانت مفعلة
        if (this.options.sounds.enabled) {
            this.initSounds();
        }
    }

    /**
     * إنشاء حاوية الإشعارات
     */
    createContainer() {
        let container = document.querySelector(this.options.containerSelector);

        if (!container) {
            container = document.createElement('div');
            container.id = this.options.containerSelector.replace('#', '');
            document.body.appendChild(container);
        }

        // تطبيق الأنماط على الحاوية
        container.style.position = 'fixed';
        container.style.zIndex = '9999';
        container.style.display = 'flex';
        container.style.flexDirection = 'column';
        container.style.gap = '10px';
        container.style.maxWidth = '400px';
        container.style.width = '100%';
        container.style.pointerEvents = 'none'; // لتمرير النقرات إلى العناصر التي تحتها

        // تحديد موضع الحاوية
        switch (this.options.position) {
            case 'top-right':
                container.style.top = '20px';
                container.style.right = '20px';
                container.style.alignItems = 'flex-end';
                break;
            case 'top-left':
                container.style.top = '20px';
                container.style.left = '20px';
                container.style.alignItems = 'flex-start';
                break;
            case 'bottom-right':
                container.style.bottom = '20px';
                container.style.right = '20px';
                container.style.alignItems = 'flex-end';
                break;
            case 'bottom-left':
                container.style.bottom = '20px';
                container.style.left = '20px';
                container.style.alignItems = 'flex-start';
                break;
            case 'top-center':
                container.style.top = '20px';
                container.style.left = '50%';
                container.style.transform = 'translateX(-50%)';
                container.style.alignItems = 'center';
                break;
            case 'bottom-center':
                container.style.bottom = '20px';
                container.style.left = '50%';
                container.style.transform = 'translateX(-50%)';
                container.style.alignItems = 'center';
                break;
        }

        this.container = container;
    }

    /**
     * تهيئة الأصوات
     */
    initSounds() {
        this.sounds = {
            success: new Audio(this.options.sounds.success),
            error: new Audio(this.options.sounds.error),
            warning: new Audio(this.options.sounds.warning),
            info: new Audio(this.options.sounds.info)
        };

        // تحميل الأصوات مسبقاً
        for (const sound in this.sounds) {
            this.sounds[sound].load();
        }
    }

    /**
     * بدء التحديث التلقائي
     */
    startAutoUpdate() {
        this.updateInterval = setInterval(() => {
            this.fetchNotifications();
        }, this.options.updateInterval);
    }

    /**
     * إيقاف التحديث التلقائي
     */
    stopAutoUpdate() {
        if (this.updateInterval) {
            clearInterval(this.updateInterval);
        }
    }

    /**
     * جلب الإشعارات من الخادم
     */
    fetchNotifications() {
        fetch(`${this.options.apiEndpoint}/recent`)
            .then(response => response.json())
            .then(data => {
                if (data.notifications && data.notifications.length > 0) {
                    // عرض الإشعارات الجديدة فقط
                    const unreadNotifications = data.notifications.filter(notification => !notification.is_read);
                    unreadNotifications.forEach(notification => {
                        this.show(notification.title, notification.message, notification.type, {
                            link: notification.link,
                            id: notification.id
                        });
                    });
                }
            })
            .catch(error => {
                console.error('Error fetching notifications:', error);
            });
    }

    /**
     * عرض إشعار جديد
     * @param {string} title - عنوان الإشعار
     * @param {string} message - نص الإشعار
     * @param {string} type - نوع الإشعار (success, error, warning, info)
     * @param {object} options - خيارات إضافية
     */
    show(title, message, type = 'info', options = {}) {
        // التحقق من عدد الإشعارات الحالية
        const currentNotifications = this.container.querySelectorAll('.notification-item');
        if (currentNotifications.length >= this.options.maxNotifications) {
            // إزالة أقدم إشعار
            currentNotifications[0].remove();
        }

        // إنشاء عنصر الإشعار
        const notification = document.createElement('div');
        notification.className = `notification-item notification-${type}`;
        notification.style.backgroundColor = this.getBackgroundColor(type);
        notification.style.color = this.getTextColor(type);
        notification.style.borderRadius = '12px';
        notification.style.boxShadow = '0 8px 16px rgba(0, 0, 0, 0.15)';
        notification.style.padding = '16px';
        notification.style.marginBottom = '12px';
        notification.style.width = '100%';
        notification.style.maxWidth = '400px';
        notification.style.position = 'relative';
        notification.style.overflow = 'hidden';
        notification.style.opacity = '0';
        notification.style.transform = 'translateY(20px) scale(0.95)';
        notification.style.transition = `all ${this.options.animationDuration}ms cubic-bezier(0.68, -0.55, 0.27, 1.55)`;
        notification.style.pointerEvents = 'auto'; // لتمكين التفاعل مع الإشعار
        notification.style.display = 'flex';
        notification.style.flexDirection = 'column';
        notification.style.cursor = options.link ? 'pointer' : 'default';

        // إضافة حدود متدرجة
        notification.style.border = '1px solid ' + this.getProgressColor(type, 0.2);

        // إضافة تأثير الخلفية المتدرجة
        notification.style.backgroundImage = `linear-gradient(to bottom right, ${this.getBackgroundColor(type)}, ${this.getBackgroundColor(type, 0.8)})`;

        // إضافة شريط التقدم
        const progressBar = document.createElement('div');
        progressBar.className = 'notification-progress';
        progressBar.style.position = 'absolute';
        progressBar.style.bottom = '0';
        progressBar.style.left = '0';
        progressBar.style.height = '4px';
        progressBar.style.width = '100%';
        progressBar.style.backgroundImage = `linear-gradient(to right, ${this.getProgressColor(type, 0.7)}, ${this.getProgressColor(type)})`;
        progressBar.style.transformOrigin = 'left';
        progressBar.style.transform = 'scaleX(1)';
        progressBar.style.transition = `transform ${options.duration || this.options.defaultDuration}ms linear`;
        progressBar.style.borderBottomLeftRadius = '12px';
        progressBar.style.borderBottomRightRadius = '12px';

        // إنشاء محتوى الإشعار
        const content = document.createElement('div');
        content.className = 'notification-content';
        content.style.display = 'flex';
        content.style.alignItems = 'flex-start';
        content.style.gap = '12px';

        // إضافة أيقونة الإشعار
        const icon = document.createElement('div');
        icon.className = 'notification-icon';
        icon.style.flexShrink = '0';
        icon.style.width = '32px';
        icon.style.height = '32px';
        icon.style.borderRadius = '50%';
        icon.style.backgroundColor = this.getIconBackgroundColor(type);
        icon.style.display = 'flex';
        icon.style.alignItems = 'center';
        icon.style.justifyContent = 'center';
        icon.style.boxShadow = `0 2px 8px ${this.getProgressColor(type, 0.3)}`;
        icon.style.backgroundImage = `linear-gradient(135deg, ${this.getIconBackgroundColor(type)}, ${this.getProgressColor(type)})`;
        icon.innerHTML = this.getIcon(type);

        // إضافة نص الإشعار
        const textContainer = document.createElement('div');
        textContainer.className = 'notification-text';
        textContainer.style.flex = '1';

        // إضافة العنوان
        const titleElement = document.createElement('div');
        titleElement.className = 'notification-title';
        titleElement.style.fontWeight = 'bold';
        titleElement.style.marginBottom = '6px';
        titleElement.style.fontSize = '16px';
        titleElement.textContent = title;

        // إضافة الرسالة
        const messageElement = document.createElement('div');
        messageElement.className = 'notification-message';
        messageElement.style.fontSize = '14px';
        messageElement.style.opacity = '0.9';
        messageElement.style.lineHeight = '1.5';
        messageElement.textContent = message;

        // إضافة وقت الإشعار
        const timeElement = document.createElement('div');
        timeElement.className = 'notification-time';
        timeElement.style.fontSize = '12px';
        timeElement.style.marginTop = '8px';
        timeElement.style.opacity = '0.6';

        const now = new Date();
        const timeStr = now.getHours().toString().padStart(2, '0') + ':' +
                        now.getMinutes().toString().padStart(2, '0');
        timeElement.textContent = timeStr;

        // إضافة الوقت إلى حاوية النص
        messageElement.appendChild(timeElement);

        // إضافة زر الإغلاق
        const closeButton = document.createElement('button');
        closeButton.className = 'notification-close';
        closeButton.style.background = 'none';
        closeButton.style.border = 'none';
        closeButton.style.color = 'inherit';
        closeButton.style.opacity = '0.5';
        closeButton.style.cursor = 'pointer';
        closeButton.style.padding = '4px';
        closeButton.style.marginLeft = '8px';
        closeButton.style.fontSize = '18px';
        closeButton.style.lineHeight = '1';
        closeButton.style.borderRadius = '50%';
        closeButton.style.width = '24px';
        closeButton.style.height = '24px';
        closeButton.style.display = 'flex';
        closeButton.style.alignItems = 'center';
        closeButton.style.justifyContent = 'center';
        closeButton.style.transition = 'all 0.2s ease';
        closeButton.innerHTML = '<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><line x1="18" y1="6" x2="6" y2="18"></line><line x1="6" y1="6" x2="18" y2="18"></line></svg>';

        closeButton.addEventListener('mouseover', () => {
            closeButton.style.opacity = '1';
            closeButton.style.backgroundColor = 'rgba(0, 0, 0, 0.1)';
        });

        closeButton.addEventListener('mouseout', () => {
            closeButton.style.opacity = '0.5';
            closeButton.style.backgroundColor = 'transparent';
        });

        closeButton.addEventListener('click', (e) => {
            e.stopPropagation();
            this.dismiss(notification, options.id);
        });

        // تجميع العناصر
        textContainer.appendChild(titleElement);
        textContainer.appendChild(messageElement);
        content.appendChild(icon);
        content.appendChild(textContainer);
        content.appendChild(closeButton);
        notification.appendChild(content);
        notification.appendChild(progressBar);

        // إضافة الإشعار إلى الحاوية
        this.container.appendChild(notification);

        // تشغيل الصوت إذا كان مفعلاً
        if (this.options.sounds.enabled && this.sounds) {
            this.sounds[type]?.play().catch(e => console.log('Could not play notification sound', e));
        }

        // تطبيق التأثير الحركي
        setTimeout(() => {
            notification.style.opacity = '1';
            notification.style.transform = 'translateY(0) scale(1)';
        }, 10);

        // بدء شريط التقدم مع تأخير بسيط
        setTimeout(() => {
            progressBar.style.transform = 'scaleX(0)';
        }, 100);

        // إضافة معالج النقر للرابط
        if (options.link) {
            notification.addEventListener('click', () => {
                window.location.href = options.link;
            });
        }

        // إزالة الإشعار بعد انتهاء المدة
        const duration = options.duration || this.options.defaultDuration;
        setTimeout(() => {
            this.dismiss(notification, options.id);
        }, duration);

        return notification;
    }

    /**
     * إزالة إشعار
     * @param {HTMLElement} notification - عنصر الإشعار
     * @param {number} id - معرف الإشعار في قاعدة البيانات
     */
    dismiss(notification, id) {
        // تطبيق تأثير الخروج
        notification.style.opacity = '0';
        notification.style.transform = 'translateY(-20px) scale(0.95)';
        notification.style.pointerEvents = 'none'; // منع التفاعل أثناء الخروج

        // إضافة تأثير الحدود
        notification.style.borderColor = 'transparent';
        notification.style.boxShadow = '0 0 0 rgba(0, 0, 0, 0)';

        // إزالة الإشعار بعد انتهاء التأثير
        setTimeout(() => {
            // تطبيق تأثير الارتفاع
            notification.style.maxHeight = '0';
            notification.style.margin = '0';
            notification.style.padding = '0';

            // إزالة العنصر بعد انتهاء تأثير الارتفاع
            setTimeout(() => {
                notification.remove();
            }, 200);
        }, this.options.animationDuration);

        // تحديث حالة الإشعار في قاعدة البيانات إذا كان له معرف
        if (id) {
            fetch(`/api/notifications/mark-read/${id}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                }
            }).catch(error => {
                console.error('Error marking notification as read:', error);
            });
        }
    }

    /**
     * الحصول على لون خلفية الإشعار
     * @param {string} type - نوع الإشعار
     * @param {number} opacity - درجة الشفافية (اختياري)
     * @returns {string} - لون الخلفية
     */
    getBackgroundColor(type, opacity = 1) {
        let color;

        if (this.options.darkMode) {
            switch (type) {
                case 'success': color = '#1a3a2a'; break;
                case 'error': color = '#3a1a1a'; break;
                case 'warning': color = '#3a2a1a'; break;
                case 'info': color = '#1a2a3a'; break;
                default: color = '#1a2a3a'; break;
            }
        } else {
            switch (type) {
                case 'success': color = '#e6f7ef'; break;
                case 'error': color = '#fde8e8'; break;
                case 'warning': color = '#fef6e6'; break;
                case 'info': color = '#e6f1fe'; break;
                default: color = '#e6f1fe'; break;
            }
        }

        // إذا كانت الشفافية أقل من 1، قم بتحويل اللون إلى صيغة rgba
        if (opacity < 1) {
            // تحويل اللون من صيغة hex إلى rgb
            const r = parseInt(color.slice(1, 3), 16);
            const g = parseInt(color.slice(3, 5), 16);
            const b = parseInt(color.slice(5, 7), 16);
            return `rgba(${r}, ${g}, ${b}, ${opacity})`;
        }

        return color;
    }

    /**
     * الحصول على لون النص
     * @param {string} type - نوع الإشعار
     * @returns {string} - لون النص
     */
    getTextColor(type) {
        if (this.options.darkMode) {
            switch (type) {
                case 'success': return '#4ade80';
                case 'error': return '#f87171';
                case 'warning': return '#fbbf24';
                case 'info': return '#60a5fa';
                default: return '#60a5fa';
            }
        } else {
            switch (type) {
                case 'success': return '#047857';
                case 'error': return '#b91c1c';
                case 'warning': return '#b45309';
                case 'info': return '#1e40af';
                default: return '#1e40af';
            }
        }
    }

    /**
     * الحصول على لون خلفية الأيقونة
     * @param {string} type - نوع الإشعار
     * @returns {string} - لون خلفية الأيقونة
     */
    getIconBackgroundColor(type) {
        switch (type) {
            case 'success': return '#4ade80';
            case 'error': return '#f87171';
            case 'warning': return '#fbbf24';
            case 'info': return '#60a5fa';
            default: return '#60a5fa';
        }
    }

    /**
     * الحصول على لون شريط التقدم
     * @param {string} type - نوع الإشعار
     * @param {number} opacity - درجة الشفافية (اختياري)
     * @returns {string} - لون شريط التقدم
     */
    getProgressColor(type, opacity = 1) {
        let color;
        switch (type) {
            case 'success': color = '#10b981'; break;
            case 'error': color = '#ef4444'; break;
            case 'warning': color = '#f59e0b'; break;
            case 'info': color = '#3b82f6'; break;
            default: color = '#3b82f6'; break;
        }

        // إذا كانت الشفافية أقل من 1، قم بتحويل اللون إلى صيغة rgba
        if (opacity < 1) {
            // تحويل اللون من صيغة hex إلى rgb
            const r = parseInt(color.slice(1, 3), 16);
            const g = parseInt(color.slice(3, 5), 16);
            const b = parseInt(color.slice(5, 7), 16);
            return `rgba(${r}, ${g}, ${b}, ${opacity})`;
        }

        return color;
    }

    /**
     * الحصول على أيقونة الإشعار
     * @param {string} type - نوع الإشعار
     * @returns {string} - كود HTML للأيقونة
     */
    getIcon(type) {
        switch (type) {
            case 'success':
                return '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="16" height="16" fill="white"><path d="M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41L9 16.17z"></path></svg>';
            case 'error':
                return '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="16" height="16" fill="white"><path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12 19 6.41z"></path></svg>';
            case 'warning':
                return '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="16" height="16" fill="white"><path d="M12 2L1 21h22L12 2zm0 3.83L19.13 19H4.87L12 5.83zM11 10v4h2v-4h-2zm0 6v2h2v-2h-2z"></path></svg>';
            case 'info':
                return '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="16" height="16" fill="white"><path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 15c-.55 0-1-.45-1-1v-4c0-.55.45-1 1-1s1 .45 1 1v4c0 .55-.45 1-1 1zm1-8h-2V7h2v2z"></path></svg>';
            default:
                return '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="16" height="16" fill="white"><path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 15c-.55 0-1-.45-1-1v-4c0-.55.45-1 1-1s1 .45 1 1v4c0 .55-.45 1-1 1zm1-8h-2V7h2v2z"></path></svg>';
        }
    }
}

// تصدير الكلاس للاستخدام العالمي
window.NotificationsSystem = NotificationsSystem;
