{% extends 'layout.html' %}

{% block title %}لوحة تحكم المخزون{% endblock %}

{% block page_content %}
<div class="container mx-auto px-4 py-8">
    <div class="mb-6 flex justify-between items-center">
        <div>
            <h1 class="text-2xl font-bold">لوحة تحكم المخزون</h1>
            <p class="text-gray-600">نظرة عامة على حالة المخزون والإحصائيات</p>
        </div>
        <div class="flex space-x-2 space-x-reverse">
            <a href="{{ url_for('warehouses.reports') }}" class="bg-indigo-500 text-white px-4 py-2 rounded-lg hover:bg-indigo-600 transition-all ml-2">
                <i class="ri-file-chart-line ml-1"></i>تقارير المخزون
            </a>
            <a href="{{ url_for('warehouses.index') }}" class="bg-gray-100 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-200 transition-all">
                <i class="ri-arrow-right-line ml-1"></i>العودة للمخازن
            </a>
        </div>
    </div>

    <!-- الإحصائيات العامة -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
        <div class="bg-white rounded-lg shadow p-4 border-r-4 border-blue-500">
            <div class="flex justify-between items-center">
                <div>
                    <p class="text-gray-500 text-sm">إجمالي المنتجات</p>
                    <p class="text-2xl font-bold">{{ stats.total_products }}</p>
                </div>
                <div class="text-blue-500 text-3xl">
                    <i class="ri-shopping-bag-line"></i>
                </div>
            </div>
        </div>
        <div class="bg-white rounded-lg shadow p-4 border-r-4 border-green-500">
            <div class="flex justify-between items-center">
                <div>
                    <p class="text-gray-500 text-sm">إجمالي المخازن</p>
                    <p class="text-2xl font-bold">{{ stats.total_warehouses }}</p>
                </div>
                <div class="text-green-500 text-3xl">
                    <i class="ri-store-2-line"></i>
                </div>
            </div>
        </div>
        <div class="bg-white rounded-lg shadow p-4 border-r-4 border-yellow-500">
            <div class="flex justify-between items-center">
                <div>
                    <p class="text-gray-500 text-sm">مخزون منخفض</p>
                    <p class="text-2xl font-bold">{{ stats.low_stock_count }}</p>
                </div>
                <div class="text-yellow-500 text-3xl">
                    <i class="ri-error-warning-line"></i>
                </div>
            </div>
        </div>
        <div class="bg-white rounded-lg shadow p-4 border-r-4 border-red-500">
            <div class="flex justify-between items-center">
                <div>
                    <p class="text-gray-500 text-sm">نفذ من المخزون</p>
                    <p class="text-2xl font-bold">{{ stats.out_of_stock_count }}</p>
                </div>
                <div class="text-red-500 text-3xl">
                    <i class="ri-close-circle-line"></i>
                </div>
            </div>
        </div>
    </div>

    <!-- الرسومات البيانية -->
    <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
        <div class="bg-white rounded-lg shadow p-4">
            <h2 class="text-lg font-semibold mb-4">توزيع المخزون حسب المخازن</h2>
            <div class="h-64">
                <canvas id="warehouseDistributionChart"></canvas>
            </div>
        </div>
        <div class="bg-white rounded-lg shadow p-4">
            <h2 class="text-lg font-semibold mb-4">حالة المخزون</h2>
            <div class="h-64">
                <canvas id="inventoryStatusChart"></canvas>
            </div>
        </div>
    </div>

    <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
        <div class="bg-white rounded-lg shadow p-4">
            <h2 class="text-lg font-semibold mb-4">قيمة المخزون حسب المخازن</h2>
            <div class="h-64">
                <canvas id="inventoryValueChart"></canvas>
            </div>
        </div>
        <div class="bg-white rounded-lg shadow p-4">
            <h2 class="text-lg font-semibold mb-4">توزيع المنتجات حسب التصنيفات</h2>
            <div class="h-64">
                <canvas id="categoriesChart"></canvas>
            </div>
        </div>
    </div>

    <!-- المنتجات الأكثر والأقل مخزوناً -->
    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div class="bg-white rounded-lg shadow overflow-hidden">
            <div class="p-4 bg-gray-50 border-b">
                <h2 class="text-lg font-semibold">المنتجات الأكثر مخزوناً</h2>
            </div>
            <table class="min-w-full">
                <thead>
                    <tr class="bg-gray-100">
                        <th class="px-6 py-3 border-b text-right">المنتج</th>
                        <th class="px-6 py-3 border-b text-right">الكمية</th>
                        <th class="px-6 py-3 border-b text-right">المخزن</th>
                    </tr>
                </thead>
                <tbody>
                    {% for item in top_inventory %}
                    <tr class="hover:bg-gray-50">
                        <td class="px-6 py-4 border-b font-medium">{{ item.product.name }}</td>
                        <td class="px-6 py-4 border-b">{{ item.quantity }}</td>
                        <td class="px-6 py-4 border-b">{{ item.warehouse.name }}</td>
                    </tr>
                    {% endfor %}
                    
                    {% if top_inventory|length == 0 %}
                    <tr>
                        <td colspan="3" class="px-6 py-4 text-center text-gray-500">لا توجد بيانات</td>
                    </tr>
                    {% endif %}
                </tbody>
            </table>
        </div>
        
        <div class="bg-white rounded-lg shadow overflow-hidden">
            <div class="p-4 bg-gray-50 border-b">
                <h2 class="text-lg font-semibold">المنتجات ذات المخزون المنخفض</h2>
            </div>
            <table class="min-w-full">
                <thead>
                    <tr class="bg-gray-100">
                        <th class="px-6 py-3 border-b text-right">المنتج</th>
                        <th class="px-6 py-3 border-b text-right">الكمية</th>
                        <th class="px-6 py-3 border-b text-right">المخزن</th>
                    </tr>
                </thead>
                <tbody>
                    {% for item in low_inventory %}
                    <tr class="hover:bg-gray-50 {% if item.quantity <= 0 %}bg-red-50{% else %}bg-yellow-50{% endif %}">
                        <td class="px-6 py-4 border-b font-medium">{{ item.product.name }}</td>
                        <td class="px-6 py-4 border-b">{{ item.quantity }}</td>
                        <td class="px-6 py-4 border-b">{{ item.warehouse.name }}</td>
                    </tr>
                    {% endfor %}
                    
                    {% if low_inventory|length == 0 %}
                    <tr>
                        <td colspan="3" class="px-6 py-4 text-center text-gray-500">لا توجد بيانات</td>
                    </tr>
                    {% endif %}
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- Chart.js -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

<script>
    // بيانات الرسومات البيانية
    const warehouseData = {{ warehouse_data|tojson }};
    const statusData = {{ status_data|tojson }};
    const valueData = {{ value_data|tojson }};
    const categoryData = {{ category_data|tojson }};
    
    // توزيع المخزون حسب المخازن
    const warehouseCtx = document.getElementById('warehouseDistributionChart').getContext('2d');
    new Chart(warehouseCtx, {
        type: 'bar',
        data: {
            labels: warehouseData.labels,
            datasets: [{
                label: 'عدد المنتجات',
                data: warehouseData.data,
                backgroundColor: 'rgba(59, 130, 246, 0.7)',
                borderColor: 'rgba(59, 130, 246, 1)',
                borderWidth: 1
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true
                }
            }
        }
    });
    
    // حالة المخزون
    const statusCtx = document.getElementById('inventoryStatusChart').getContext('2d');
    new Chart(statusCtx, {
        type: 'pie',
        data: {
            labels: statusData.labels,
            datasets: [{
                data: statusData.data,
                backgroundColor: [
                    'rgba(34, 197, 94, 0.7)',
                    'rgba(234, 179, 8, 0.7)',
                    'rgba(239, 68, 68, 0.7)'
                ],
                borderColor: [
                    'rgba(34, 197, 94, 1)',
                    'rgba(234, 179, 8, 1)',
                    'rgba(239, 68, 68, 1)'
                ],
                borderWidth: 1
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false
        }
    });
    
    // قيمة المخزون حسب المخازن
    const valueCtx = document.getElementById('inventoryValueChart').getContext('2d');
    new Chart(valueCtx, {
        type: 'bar',
        data: {
            labels: valueData.labels,
            datasets: [{
                label: 'قيمة المخزون (ج.م)',
                data: valueData.data,
                backgroundColor: 'rgba(16, 185, 129, 0.7)',
                borderColor: 'rgba(16, 185, 129, 1)',
                borderWidth: 1
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true
                }
            }
        }
    });
    
    // توزيع المنتجات حسب التصنيفات
    const categoryCtx = document.getElementById('categoriesChart').getContext('2d');
    new Chart(categoryCtx, {
        type: 'doughnut',
        data: {
            labels: categoryData.labels,
            datasets: [{
                data: categoryData.data,
                backgroundColor: [
                    'rgba(59, 130, 246, 0.7)',
                    'rgba(16, 185, 129, 0.7)',
                    'rgba(249, 115, 22, 0.7)',
                    'rgba(139, 92, 246, 0.7)',
                    'rgba(236, 72, 153, 0.7)',
                    'rgba(234, 179, 8, 0.7)'
                ],
                borderWidth: 1
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false
        }
    });
</script>
{% endblock page_content %}
