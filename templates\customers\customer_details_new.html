<!DOCTYPE html>
<html lang="ar" dir="rtl" class="light">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>FoUaD - تفاصيل العميل</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdn.jsdelivr.net/npm/remixicon@2.5.0/fonts/remixicon.css" rel="stylesheet">
    <script>
        tailwind.config = {
            darkMode: 'class',
            theme: {
                extend: {
                    colors: {
                        primary: '#3B82F6',
                        secondary: '#10b981',
                        dark: {
                            100: '#1E293B',
                            200: '#0F172A',
                            300: '#0B1120',
                            400: '#060A15'
                        }
                    }
                }
            }
        }
    </script>
    <style>
        body {
            font-family: 'Tajawal', sans-serif;
            background-color: #f1f5f9;
            transition: background-color 0.3s ease, color 0.3s ease;
        }
        body.dark {
            background-color: #0F172A;
            color: #E2E8F0;
        }
        .card {
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }
        .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
        }
        .customer-avatar {
            background: linear-gradient(135deg, #3B82F6 0%, #2563EB 100%);
            box-shadow: 0 10px 15px -3px rgba(59, 130, 246, 0.3);
        }
        .action-button {
            transition: all 0.3s ease;
        }
        .action-button:hover {
            transform: translateY(-2px);
        }
        .page-header {
            background: linear-gradient(to right, #3B82F6, #2563EB);
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
        }
        .dark .page-header {
            background: linear-gradient(to right, #1E40AF, #1E3A8A);
        }
        .tab-button {
            position: relative;
            transition: all 0.3s ease;
        }
        .tab-button.active::after {
            content: '';
            position: absolute;
            bottom: -2px;
            left: 0;
            width: 100%;
            height: 2px;
            background-color: #3B82F6;
            border-radius: 2px;
        }
        .dark .tab-button.active::after {
            background-color: #60A5FA;
        }
        .tab-pane {
            display: none;
        }
        .tab-pane.active {
            display: block;
        }
        .badge {
            @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;
        }
        .badge-green {
            @apply bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300;
        }
        .badge-blue {
            @apply bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300;
        }
        .badge-red {
            @apply bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300;
        }
        .badge-yellow {
            @apply bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-300;
        }
        .badge-purple {
            @apply bg-purple-100 text-purple-800 dark:bg-purple-900/30 dark:text-purple-300;
        }
        .badge-gray {
            @apply bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300;
        }
    </style>
</head>
<body class="min-h-screen">
    {% include 'partials/navbar.html' %}

    <!-- Page Header -->
    <div class="page-header py-6 mb-8">
        <div class="container mx-auto px-4">
            <div class="flex flex-col md:flex-row justify-between items-center">
                <div class="flex items-center mb-4 md:mb-0">
                    <div class="customer-avatar w-16 h-16 rounded-full flex items-center justify-center text-white text-2xl font-bold mr-4">
                        {{ customer.name[0].upper() }}
                    </div>
                    <div>
                        <h1 class="text-2xl md:text-3xl font-bold text-white">{{ customer.name }}</h1>
                        <p class="text-blue-100">
                            {% if customer.phone %}
                                <span class="inline-flex items-center">
                                    <i class="ri-phone-line mr-1"></i> {{ customer.phone }}
                                </span>
                            {% endif %}
                            {% if customer.email %}
                                <span class="inline-flex items-center mr-4">
                                    <i class="ri-mail-line mr-1"></i> {{ customer.email }}
                                </span>
                            {% endif %}
                        </p>
                    </div>
                </div>
                <div class="flex space-x-2 space-x-reverse">
                    <a href="{{ url_for('customers.edit', id=customer.id) }}" class="action-button bg-white text-primary hover:bg-blue-50 px-4 py-2 rounded-lg flex items-center shadow-md">
                        <i class="ri-edit-line ml-1"></i>
                        تعديل
                    </a>
                    <a href="{{ url_for('customers.index') }}" class="action-button bg-white text-primary hover:bg-blue-50 px-4 py-2 rounded-lg flex items-center shadow-md">
                        <i class="ri-arrow-right-line ml-1"></i>
                        العودة
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="container mx-auto px-4 pb-8">
        <!-- Stats Cards -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <!-- إجمالي المشتريات -->
            <div class="bg-white dark:bg-gray-800 rounded-xl shadow-md p-6 card">
                <div class="flex items-center">
                    <div class="w-12 h-12 bg-blue-100 dark:bg-blue-900/50 rounded-full flex items-center justify-center mr-4">
                        <i class="ri-money-dollar-circle-line text-2xl text-primary dark:text-blue-400"></i>
                    </div>
                    <div>
                        <p class="text-sm text-gray-500 dark:text-gray-400">إجمالي المشتريات</p>
                        <p class="text-2xl font-bold text-gray-800 dark:text-white">{{ "%.2f"|format(stats.total_spent) }} <span class="text-sm">ج.م</span></p>
                    </div>
                </div>
            </div>

            <!-- المبلغ المدفوع -->
            <div class="bg-white dark:bg-gray-800 rounded-xl shadow-md p-6 card">
                <div class="flex items-center">
                    <div class="w-12 h-12 bg-green-100 dark:bg-green-900/50 rounded-full flex items-center justify-center mr-4">
                        <i class="ri-bank-card-line text-2xl text-green-600 dark:text-green-400"></i>
                    </div>
                    <div>
                        <p class="text-sm text-gray-500 dark:text-gray-400">المبلغ المدفوع</p>
                        <p class="text-2xl font-bold text-gray-800 dark:text-white">{{ "%.2f"|format(stats.paid_amount) }} <span class="text-sm">ج.م</span></p>
                    </div>
                </div>
            </div>

            <!-- المبلغ المتبقي -->
            <div class="bg-white dark:bg-gray-800 rounded-xl shadow-md p-6 card">
                <div class="flex items-center">
                    <div class="w-12 h-12 bg-red-100 dark:bg-red-900/50 rounded-full flex items-center justify-center mr-4">
                        <i class="ri-refund-2-line text-2xl text-red-600 dark:text-red-400"></i>
                    </div>
                    <div>
                        <p class="text-sm text-gray-500 dark:text-gray-400">المبلغ المتبقي</p>
                        <p class="text-2xl font-bold text-gray-800 dark:text-white">{{ "%.2f"|format(stats.remaining_amount) }} <span class="text-sm">ج.م</span></p>
                    </div>
                </div>
            </div>

            <!-- عدد الطلبات -->
            <div class="bg-white dark:bg-gray-800 rounded-xl shadow-md p-6 card">
                <div class="flex items-center">
                    <div class="w-12 h-12 bg-purple-100 dark:bg-purple-900/50 rounded-full flex items-center justify-center mr-4">
                        <i class="ri-shopping-bag-line text-2xl text-purple-600 dark:text-purple-400"></i>
                    </div>
                    <div>
                        <p class="text-sm text-gray-500 dark:text-gray-400">عدد الطلبات</p>
                        <p class="text-2xl font-bold text-gray-800 dark:text-white">{{ stats.order_count }}</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- حالة الدفع -->
        <div class="bg-white dark:bg-gray-800 rounded-xl shadow-md p-6 mb-8">
            <h2 class="text-lg font-bold text-gray-800 dark:text-white mb-4">حالة الدفع</h2>
            <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-4 mb-2">
                {% set payment_percentage = (stats.paid_amount / stats.total_spent * 100) if stats.total_spent > 0 else 0 %}
                <div class="bg-blue-600 dark:bg-blue-500 h-4 rounded-full" style="width: {{ payment_percentage }}%"></div>
            </div>
            <div class="flex justify-between text-sm text-gray-600 dark:text-gray-400">
                <span>{{ "%.1f"|format(payment_percentage) }}% مدفوع</span>
                <span>{{ "%.1f"|format(100 - payment_percentage) }}% متبقي</span>
            </div>
        </div>

        <!-- Tabs -->
        <div class="mb-6 border-b border-gray-200 dark:border-gray-700">
            <div class="flex overflow-x-auto">
                <button onclick="showTab('info')" id="tab-info" class="tab-button active px-4 py-2 text-gray-600 dark:text-gray-300 font-medium">
                    <i class="ri-information-line ml-1"></i>
                    معلومات العميل
                </button>
                <button onclick="showTab('orders')" id="tab-orders" class="tab-button px-4 py-2 text-gray-600 dark:text-gray-300 font-medium">
                    <i class="ri-shopping-cart-line ml-1"></i>
                    الطلبات
                </button>
                <button onclick="showTab('payments')" id="tab-payments" class="tab-button px-4 py-2 text-gray-600 dark:text-gray-300 font-medium">
                    <i class="ri-bank-card-line ml-1"></i>
                    المدفوعات
                </button>
                <button onclick="showTab('returns')" id="tab-returns" class="tab-button px-4 py-2 text-gray-600 dark:text-gray-300 font-medium">
                    <i class="ri-arrow-go-back-line ml-1"></i>
                    المرتجعات
                </button>
                <button onclick="showTab('products')" id="tab-products" class="tab-button px-4 py-2 text-gray-600 dark:text-gray-300 font-medium">
                    <i class="ri-archive-line ml-1"></i>
                    المنتجات
                </button>
                <button onclick="showTab('reports')" id="tab-reports" class="tab-button px-4 py-2 text-gray-600 dark:text-gray-300 font-medium">
                    <i class="ri-file-chart-line ml-1"></i>
                    التقارير
                </button>
            </div>
        </div>

        <!-- Tab Content -->
        <div id="tab-content">
            <!-- معلومات العميل -->
            <div id="content-info" class="tab-pane active">
                <div class="bg-white dark:bg-gray-800 rounded-xl shadow-md overflow-hidden">
                    <div class="bg-gradient-to-r from-blue-500 to-blue-600 dark:from-blue-700 dark:to-blue-800 px-6 py-4">
                        <h2 class="text-xl font-bold text-white flex items-center">
                            <i class="ri-user-line mr-2"></i>
                            معلومات العميل
                        </h2>
                    </div>
                    <div class="p-6">
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div class="space-y-4">
                                <div class="flex items-center border-b border-gray-200 dark:border-gray-700 pb-3">
                                    <div class="w-8 h-8 rounded-full bg-blue-100 dark:bg-blue-900/50 flex items-center justify-center mr-3">
                                        <i class="ri-user-line text-primary dark:text-blue-400"></i>
                                    </div>
                                    <div class="flex-1">
                                        <p class="text-sm text-gray-500 dark:text-gray-400">الاسم</p>
                                        <p class="font-medium text-gray-800 dark:text-white">{{ customer.name }}</p>
                                    </div>
                                </div>

                                <div class="flex items-center border-b border-gray-200 dark:border-gray-700 pb-3">
                                    <div class="w-8 h-8 rounded-full bg-blue-100 dark:bg-blue-900/50 flex items-center justify-center mr-3">
                                        <i class="ri-phone-line text-primary dark:text-blue-400"></i>
                                    </div>
                                    <div class="flex-1">
                                        <p class="text-sm text-gray-500 dark:text-gray-400">رقم الهاتف</p>
                                        <p class="font-medium text-gray-800 dark:text-white">{{ customer.phone or 'غير متوفر' }}</p>
                                    </div>
                                </div>

                                <div class="flex items-center border-b border-gray-200 dark:border-gray-700 pb-3">
                                    <div class="w-8 h-8 rounded-full bg-blue-100 dark:bg-blue-900/50 flex items-center justify-center mr-3">
                                        <i class="ri-mail-line text-primary dark:text-blue-400"></i>
                                    </div>
                                    <div class="flex-1">
                                        <p class="text-sm text-gray-500 dark:text-gray-400">البريد الإلكتروني</p>
                                        <p class="font-medium text-gray-800 dark:text-white">{{ customer.email or 'غير متوفر' }}</p>
                                    </div>
                                </div>
                            </div>

                            <div class="space-y-4">
                                <div class="flex items-center border-b border-gray-200 dark:border-gray-700 pb-3">
                                    <div class="w-8 h-8 rounded-full bg-blue-100 dark:bg-blue-900/50 flex items-center justify-center mr-3">
                                        <i class="ri-map-pin-line text-primary dark:text-blue-400"></i>
                                    </div>
                                    <div class="flex-1">
                                        <p class="text-sm text-gray-500 dark:text-gray-400">العنوان</p>
                                        <p class="font-medium text-gray-800 dark:text-white">{{ customer.address or 'غير متوفر' }}</p>
                                    </div>
                                </div>

                                <div class="flex items-center border-b border-gray-200 dark:border-gray-700 pb-3">
                                    <div class="w-8 h-8 rounded-full bg-blue-100 dark:bg-blue-900/50 flex items-center justify-center mr-3">
                                        <i class="ri-calendar-line text-primary dark:text-blue-400"></i>
                                    </div>
                                    <div class="flex-1">
                                        <p class="text-sm text-gray-500 dark:text-gray-400">تاريخ التسجيل</p>
                                        <p class="font-medium text-gray-800 dark:text-white">{{ customer.created_at.strftime('%Y-%m-%d') }}</p>
                                    </div>
                                </div>

                                <div class="flex items-center">
                                    <div class="w-8 h-8 rounded-full bg-blue-100 dark:bg-blue-900/50 flex items-center justify-center mr-3">
                                        <i class="ri-time-line text-primary dark:text-blue-400"></i>
                                    </div>
                                    <div class="flex-1">
                                        <p class="text-sm text-gray-500 dark:text-gray-400">آخر تحديث</p>
                                        <p class="font-medium text-gray-800 dark:text-white">
                                            {% if customer.updated_at %}
                                                {{ customer.updated_at.strftime('%Y-%m-%d') }}
                                            {% else %}
                                                {{ customer.created_at.strftime('%Y-%m-%d') }}
                                            {% endif %}
                                        </p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- الطلبات -->
            <div id="content-orders" class="tab-pane">
                <div class="bg-white dark:bg-gray-800 rounded-xl shadow-md overflow-hidden">
                    <div class="bg-gradient-to-r from-green-500 to-green-600 dark:from-green-700 dark:to-green-800 px-6 py-4 flex justify-between items-center">
                        <h2 class="text-xl font-bold text-white flex items-center">
                            <i class="ri-shopping-cart-line mr-2"></i>
                            طلبات العميل
                        </h2>
                        <a href="{{ url_for('pos.index', customer_id=customer.id) }}" class="bg-white/20 hover:bg-white/30 text-white px-4 py-1 rounded-lg text-sm font-medium transition-colors flex items-center">
                            <i class="ri-add-line ml-1"></i>
                            طلب جديد
                        </a>
                    </div>
                    <div class="p-6">
                        <div class="mb-4 flex flex-wrap gap-2">
                            <div class="relative">
                                <input type="text" id="order-search" placeholder="بحث برقم الفاتورة..." class="w-full px-4 py-2 rounded-lg border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-800 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <button id="order-search-btn" class="absolute left-2 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300">
                                    <i class="ri-search-line"></i>
                                </button>
                            </div>
                            <div class="relative">
                                <input type="date" id="order-date-from" class="px-4 py-2 rounded-lg border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-800 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <span class="absolute right-2 top-1/2 transform -translate-y-1/2 text-gray-400">من</span>
                            </div>
                            <div class="relative">
                                <input type="date" id="order-date-to" class="px-4 py-2 rounded-lg border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-800 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <span class="absolute right-2 top-1/2 transform -translate-y-1/2 text-gray-400">إلى</span>
                            </div>
                            <select id="order-status" class="px-4 py-2 rounded-lg border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-800 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <option value="">جميع الحالات</option>
                                <option value="completed">مكتمل</option>
                                <option value="cancelled">ملغي</option>
                                <option value="returned">مرتجع</option>
                                <option value="deferred">مؤجل</option>
                            </select>
                            <button id="order-filter-btn" class="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors">
                                <i class="ri-filter-line ml-1"></i>
                                تصفية
                            </button>
                        </div>

                        <div id="orders-table-container" class="overflow-x-auto">
                            <table class="w-full">
                                <thead>
                                    <tr class="bg-gray-50 dark:bg-gray-700 text-gray-500 dark:text-gray-300 text-xs uppercase">
                                        <th class="px-4 py-3 text-right font-medium rounded-tr-lg">رقم الفاتورة</th>
                                        <th class="px-4 py-3 text-right font-medium">التاريخ</th>
                                        <th class="px-4 py-3 text-right font-medium">المبلغ</th>
                                        <th class="px-4 py-3 text-right font-medium">المدفوع</th>
                                        <th class="px-4 py-3 text-right font-medium">المتبقي</th>
                                        <th class="px-4 py-3 text-right font-medium">طريقة الدفع</th>
                                        <th class="px-4 py-3 text-right font-medium">الحالة</th>
                                        <th class="px-4 py-3 text-right font-medium rounded-tl-lg">الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody id="orders-table-body" class="divide-y divide-gray-100 dark:divide-gray-700">
                                    <!-- سيتم تحميل البيانات هنا عبر JavaScript -->
                                </tbody>
                            </table>
                        </div>

                        <div id="orders-loading" class="py-8 flex justify-center">
                            <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 dark:border-blue-500"></div>
                        </div>

                        <div id="orders-empty" class="hidden py-8 text-center text-gray-500 dark:text-gray-400">
                            <div class="w-16 h-16 mx-auto bg-gray-100 dark:bg-gray-700 rounded-full flex items-center justify-center mb-4">
                                <i class="ri-shopping-cart-line text-3xl"></i>
                            </div>
                            <p>لا توجد طلبات للعرض</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- المدفوعات -->
            <div id="content-payments" class="tab-pane">
                <div class="bg-white dark:bg-gray-800 rounded-xl shadow-md overflow-hidden">
                    <div class="bg-gradient-to-r from-blue-500 to-blue-600 dark:from-blue-700 dark:to-blue-800 px-6 py-4 flex justify-between items-center">
                        <h2 class="text-xl font-bold text-white flex items-center">
                            <i class="ri-bank-card-line mr-2"></i>
                            مدفوعات العميل
                        </h2>
                    </div>
                    <div class="p-6">
                        <div class="mb-4 flex flex-wrap gap-2">
                            <div class="relative">
                                <input type="date" id="payment-date-from" class="px-4 py-2 rounded-lg border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-800 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <span class="absolute right-2 top-1/2 transform -translate-y-1/2 text-gray-400">من</span>
                            </div>
                            <div class="relative">
                                <input type="date" id="payment-date-to" class="px-4 py-2 rounded-lg border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-800 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <span class="absolute right-2 top-1/2 transform -translate-y-1/2 text-gray-400">إلى</span>
                            </div>
                            <select id="payment-method" class="px-4 py-2 rounded-lg border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-800 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <option value="">جميع طرق الدفع</option>
                                <option value="cash">نقدي</option>
                                <option value="card">بطاقة</option>
                                <option value="bank_transfer">تحويل بنكي</option>
                                <option value="check">شيك</option>
                            </select>
                            <button id="payment-filter-btn" class="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors">
                                <i class="ri-filter-line ml-1"></i>
                                تصفية
                            </button>
                        </div>

                        <div id="payments-table-container" class="overflow-x-auto">
                            <table class="w-full">
                                <thead>
                                    <tr class="bg-gray-50 dark:bg-gray-700 text-gray-500 dark:text-gray-300 text-xs uppercase">
                                        <th class="px-4 py-3 text-right font-medium rounded-tr-lg">رقم المرجع</th>
                                        <th class="px-4 py-3 text-right font-medium">رقم الفاتورة</th>
                                        <th class="px-4 py-3 text-right font-medium">التاريخ</th>
                                        <th class="px-4 py-3 text-right font-medium">المبلغ</th>
                                        <th class="px-4 py-3 text-right font-medium">طريقة الدفع</th>
                                        <th class="px-4 py-3 text-right font-medium rounded-tl-lg">ملاحظات</th>
                                    </tr>
                                </thead>
                                <tbody id="payments-table-body" class="divide-y divide-gray-100 dark:divide-gray-700">
                                    <!-- سيتم تحميل البيانات هنا عبر JavaScript -->
                                </tbody>
                            </table>
                        </div>

                        <div id="payments-loading" class="py-8 flex justify-center">
                            <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 dark:border-blue-500"></div>
                        </div>

                        <div id="payments-empty" class="hidden py-8 text-center text-gray-500 dark:text-gray-400">
                            <div class="w-16 h-16 mx-auto bg-gray-100 dark:bg-gray-700 rounded-full flex items-center justify-center mb-4">
                                <i class="ri-bank-card-line text-3xl"></i>
                            </div>
                            <p>لا توجد مدفوعات للعرض</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- المرتجعات -->
            <div id="content-returns" class="tab-pane">
                <div class="bg-white dark:bg-gray-800 rounded-xl shadow-md overflow-hidden">
                    <div class="bg-gradient-to-r from-yellow-500 to-yellow-600 dark:from-yellow-700 dark:to-yellow-800 px-6 py-4 flex justify-between items-center">
                        <h2 class="text-xl font-bold text-white flex items-center">
                            <i class="ri-arrow-go-back-line mr-2"></i>
                            مرتجعات العميل
                        </h2>
                    </div>
                    <div class="p-6">
                        <div class="mb-4 flex flex-wrap gap-2">
                            <div class="relative">
                                <input type="date" id="return-date-from" class="px-4 py-2 rounded-lg border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-800 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <span class="absolute right-2 top-1/2 transform -translate-y-1/2 text-gray-400">من</span>
                            </div>
                            <div class="relative">
                                <input type="date" id="return-date-to" class="px-4 py-2 rounded-lg border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-800 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <span class="absolute right-2 top-1/2 transform -translate-y-1/2 text-gray-400">إلى</span>
                            </div>
                            <button id="return-filter-btn" class="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors">
                                <i class="ri-filter-line ml-1"></i>
                                تصفية
                            </button>
                        </div>

                        <div id="returns-table-container" class="overflow-x-auto">
                            <table class="w-full">
                                <thead>
                                    <tr class="bg-gray-50 dark:bg-gray-700 text-gray-500 dark:text-gray-300 text-xs uppercase">
                                        <th class="px-4 py-3 text-right font-medium rounded-tr-lg">رقم المرجع</th>
                                        <th class="px-4 py-3 text-right font-medium">رقم الفاتورة</th>
                                        <th class="px-4 py-3 text-right font-medium">التاريخ</th>
                                        <th class="px-4 py-3 text-right font-medium">المبلغ</th>
                                        <th class="px-4 py-3 text-right font-medium">طريقة الدفع</th>
                                        <th class="px-4 py-3 text-right font-medium">عدد الأصناف</th>
                                        <th class="px-4 py-3 text-right font-medium rounded-tl-lg">الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody id="returns-table-body" class="divide-y divide-gray-100 dark:divide-gray-700">
                                    <!-- سيتم تحميل البيانات هنا عبر JavaScript -->
                                </tbody>
                            </table>
                        </div>

                        <div id="returns-loading" class="py-8 flex justify-center">
                            <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 dark:border-blue-500"></div>
                        </div>

                        <div id="returns-empty" class="hidden py-8 text-center text-gray-500 dark:text-gray-400">
                            <div class="w-16 h-16 mx-auto bg-gray-100 dark:bg-gray-700 rounded-full flex items-center justify-center mb-4">
                                <i class="ri-arrow-go-back-line text-3xl"></i>
                            </div>
                            <p>لا توجد مرتجعات للعرض</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- المنتجات -->
            <div id="content-products" class="tab-pane">
                <div class="bg-white dark:bg-gray-800 rounded-xl shadow-md overflow-hidden">
                    <div class="bg-gradient-to-r from-purple-500 to-purple-600 dark:from-purple-700 dark:to-purple-800 px-6 py-4 flex justify-between items-center">
                        <h2 class="text-xl font-bold text-white flex items-center">
                            <i class="ri-archive-line mr-2"></i>
                            المنتجات المشتراة
                        </h2>
                    </div>
                    <div class="p-6">
                        <div class="mb-4 flex flex-wrap gap-2">
                            <div class="relative flex-1">
                                <input type="text" id="product-search" placeholder="بحث باسم المنتج أو الكود..." class="w-full px-4 py-2 rounded-lg border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-800 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <button id="product-search-btn" class="absolute left-2 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300">
                                    <i class="ri-search-line"></i>
                                </button>
                            </div>
                        </div>

                        <div id="products-table-container" class="overflow-x-auto">
                            <table class="w-full">
                                <thead>
                                    <tr class="bg-gray-50 dark:bg-gray-700 text-gray-500 dark:text-gray-300 text-xs uppercase">
                                        <th class="px-4 py-3 text-right font-medium rounded-tr-lg">المنتج</th>
                                        <th class="px-4 py-3 text-right font-medium">الكود</th>
                                        <th class="px-4 py-3 text-right font-medium">الكمية المشتراة</th>
                                        <th class="px-4 py-3 text-right font-medium">عدد مرات الشراء</th>
                                        <th class="px-4 py-3 text-right font-medium">آخر شراء</th>
                                        <th class="px-4 py-3 text-right font-medium rounded-tl-lg">السعر الحالي</th>
                                    </tr>
                                </thead>
                                <tbody id="products-table-body" class="divide-y divide-gray-100 dark:divide-gray-700">
                                    <!-- سيتم تحميل البيانات هنا عبر JavaScript -->
                                </tbody>
                            </table>
                        </div>

                        <div id="products-loading" class="py-8 flex justify-center">
                            <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 dark:border-blue-500"></div>
                        </div>

                        <div id="products-empty" class="hidden py-8 text-center text-gray-500 dark:text-gray-400">
                            <div class="w-16 h-16 mx-auto bg-gray-100 dark:bg-gray-700 rounded-full flex items-center justify-center mb-4">
                                <i class="ri-archive-line text-3xl"></i>
                            </div>
                            <p>لا توجد منتجات للعرض</p>
                        </div>
                    </div>
                </div>
            </div>