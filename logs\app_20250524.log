# تم تنظيف السجل في: 2025-05-24 19:53:16
2025-05-24 19:57:59,303 - app - INFO - استخدام قاعدة البيانات المحلية: sqlite:///instance/fouad_pos.db
2025-05-24 19:57:59,557 - nobara - INFO - معلومات النظام:
النظام: Windows-10-10.0.19045-SP0
إصدار بايثون: 3.13.1
اسم المضيف: DESKTOP-646FJ2N
عنوان IP: ************
عدد وحدات المعالجة: 4
إجمالي الذاكرة: 6.93 GB
الذاكرة المتاحة: 1.10 GB
استخدام القرص: 46.0%
إصدار التطبيق: غير معروف
وقت البدء: 2025-05-24 19:57:59
2025-05-24 19:58:00,137 - app - INFO - تم تهيئة النظام المحاسبي بنجاح
2025-05-24 19:58:00,433 - app - INFO - تم تهيئة جدولة النسخ الاحتياطي التلقائي
2025-05-24 19:58:00,441 - app - INFO - عمود warehouse_id موجود بالفعل في جدول order
2025-05-24 19:58:00,450 - security - WARNING - No JWT secret provided. Generated a new one. Please set JWT_SECRET in your environment.
2025-05-24 19:58:00,989 - __main__ - INFO - جاري بدء التطبيق على العنوان: http://************:5000
2025-05-24 19:58:02,792 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://************:5000
2025-05-24 19:58:02,887 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-24 19:58:03,011 - werkzeug - INFO -  * Restarting with stat
2025-05-24 19:58:06,565 - app - INFO - استخدام قاعدة البيانات المحلية: sqlite:///instance/fouad_pos.db
2025-05-24 19:58:06,899 - nobara - INFO - معلومات النظام:
النظام: Windows-10-10.0.19045-SP0
إصدار بايثون: 3.13.1
اسم المضيف: DESKTOP-646FJ2N
عنوان IP: ************
عدد وحدات المعالجة: 4
إجمالي الذاكرة: 6.93 GB
الذاكرة المتاحة: 1.01 GB
استخدام القرص: 46.0%
إصدار التطبيق: غير معروف
وقت البدء: 2025-05-24 19:58:06
2025-05-24 19:58:07,515 - app - INFO - تم تهيئة النظام المحاسبي بنجاح
2025-05-24 19:58:07,799 - app - INFO - تم تهيئة جدولة النسخ الاحتياطي التلقائي
2025-05-24 19:58:07,829 - app - INFO - عمود warehouse_id موجود بالفعل في جدول order
2025-05-24 19:58:07,847 - security - WARNING - No JWT secret provided. Generated a new one. Please set JWT_SECRET in your environment.
2025-05-24 19:58:08,555 - __main__ - INFO - جاري بدء التطبيق على العنوان: http://************:5000
2025-05-24 19:58:09,528 - app - INFO - استخدام قاعدة البيانات المحلية: sqlite:///c:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\instance\fouad_pos.db
2025-05-24 19:58:10,544 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://************:5000
2025-05-24 19:58:10,546 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-24 19:58:10,550 - werkzeug - INFO -  * Restarting with stat
2025-05-24 19:58:10,651 - nobara - INFO - معلومات النظام:
النظام: Windows-10-10.0.19045-SP0
إصدار بايثون: 3.13.1
اسم المضيف: DESKTOP-646FJ2N
عنوان IP: ************
عدد وحدات المعالجة: 4
إجمالي الذاكرة: 6.93 GB
الذاكرة المتاحة: 0.96 GB
استخدام القرص: 46.0%
إصدار التطبيق: غير معروف
وقت البدء: 2025-05-24 19:58:10
2025-05-24 19:58:12,059 - app - INFO - تم تهيئة النظام المحاسبي بنجاح
2025-05-24 19:58:12,877 - app - INFO - تم تهيئة جدولة النسخ الاحتياطي التلقائي
2025-05-24 19:58:12,899 - app - INFO - عمود warehouse_id موجود بالفعل في جدول order
2025-05-24 19:58:12,915 - security - WARNING - No JWT secret provided. Generated a new one. Please set JWT_SECRET in your environment.
2025-05-24 19:58:13,711 - __main__ - INFO - جاري بدء التطبيق على العنوان: http://************:5000
2025-05-24 19:58:15,104 - werkzeug - WARNING -  * Debugger is active!
2025-05-24 19:58:15,168 - werkzeug - INFO -  * Debugger PIN: 692-333-133
2025-05-24 19:58:17,374 - nobara - WARNING - خطأ في جانب العميل: Console Error: فشل في إرسال الخطأ إلى الخادم: {}
2025-05-24 19:58:17,467 - werkzeug - INFO - ************ - - [24/May/2025 19:58:17] "[32mGET / HTTP/1.1[0m" 302 -
2025-05-24 19:58:17,483 - werkzeug - INFO - ************ - - [24/May/2025 19:58:17] "POST /api/log-error HTTP/1.1" 200 -
2025-05-24 19:58:17,723 - werkzeug - INFO - ************ - - [24/May/2025 19:58:17] "[32mGET / HTTP/1.1[0m" 302 -
2025-05-24 19:58:17,766 - werkzeug - INFO - ************ - - [24/May/2025 19:58:17] "[32mGET / HTTP/1.1[0m" 302 -
2025-05-24 19:58:17,774 - app - INFO - استخدام قاعدة البيانات المحلية: sqlite:///C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\instance\fouad_pos.db
2025-05-24 19:58:17,865 - werkzeug - INFO - ************ - - [24/May/2025 19:58:17] "GET /settings/error-logs HTTP/1.1" 200 -
2025-05-24 19:58:18,056 - nobara - INFO - معلومات النظام:
النظام: Windows-10-10.0.19045-SP0
إصدار بايثون: 3.13.1
اسم المضيف: DESKTOP-646FJ2N
عنوان IP: ************
عدد وحدات المعالجة: 4
إجمالي الذاكرة: 6.93 GB
الذاكرة المتاحة: 1.02 GB
استخدام القرص: 46.0%
إصدار التطبيق: غير معروف
وقت البدء: 2025-05-24 19:58:18
2025-05-24 19:58:18,116 - werkzeug - INFO - ************ - - [24/May/2025 19:58:18] "GET /home HTTP/1.1" 200 -
2025-05-24 19:58:18,187 - werkzeug - INFO - ************ - - [24/May/2025 19:58:18] "GET /home HTTP/1.1" 200 -
2025-05-24 19:58:18,313 - werkzeug - INFO - ************ - - [24/May/2025 19:58:18] "GET /home HTTP/1.1" 200 -
2025-05-24 19:58:18,346 - werkzeug - INFO - ************ - - [24/May/2025 19:58:18] "GET /static/css/nobara-pro.css?v=1.0.0 HTTP/1.1" 200 -
2025-05-24 19:58:18,369 - werkzeug - INFO - ************ - - [24/May/2025 19:58:18] "GET /static/css/dashboard-pro.css?v=1.0.1 HTTP/1.1" 200 -
2025-05-24 19:58:18,502 - werkzeug - INFO - ************ - - [24/May/2025 19:58:18] "[36mGET /static/css/nobara-pro.css?v=1.0.0 HTTP/1.1[0m" 304 -
2025-05-24 19:58:18,574 - werkzeug - INFO - ************ - - [24/May/2025 19:58:18] "GET /static/uploads/logo_20250518120852.jpeg HTTP/1.1" 200 -
2025-05-24 19:58:18,640 - werkzeug - INFO - ************ - - [24/May/2025 19:58:18] "[36mGET /static/css/dashboard-pro.css?v=1.0.1 HTTP/1.1[0m" 304 -
2025-05-24 19:58:18,695 - werkzeug - INFO - ************ - - [24/May/2025 19:58:18] "GET /static/js/dashboard-manager.js?v=1.0.0 HTTP/1.1" 200 -
2025-05-24 19:58:19,128 - app - INFO - تم تهيئة النظام المحاسبي بنجاح
2025-05-24 19:58:19,384 - werkzeug - INFO - ************ - - [24/May/2025 19:58:19] "GET /static/js/dashboard-manager.js?v=1.0.2 HTTP/1.1" 200 -
2025-05-24 19:58:19,452 - werkzeug - INFO - ************ - - [24/May/2025 19:58:19] "GET /static/js/error-logger.js?v=1.0.2 HTTP/1.1" 200 -
2025-05-24 19:58:19,464 - werkzeug - INFO - ************ - - [24/May/2025 19:58:19] "GET /static/js/notifications-system.js?v=1.0.2 HTTP/1.1" 200 -
2025-05-24 19:58:19,474 - werkzeug - INFO - ************ - - [24/May/2025 19:58:19] "GET /static/js/system-manager.js?v=1.0.2 HTTP/1.1" 200 -
2025-05-24 19:58:19,484 - werkzeug - INFO - ************ - - [24/May/2025 19:58:19] "GET /static/js/dashboard-pro.js?v=1.0.2 HTTP/1.1" 200 -
2025-05-24 19:58:19,531 - werkzeug - INFO - ************ - - [24/May/2025 19:58:19] "[36mGET /static/css/nobara-pro.css?v=1.0.0 HTTP/1.1[0m" 304 -
2025-05-24 19:58:19,677 - app - INFO - تم تهيئة جدولة النسخ الاحتياطي التلقائي
2025-05-24 19:58:19,699 - app - INFO - عمود warehouse_id موجود بالفعل في جدول order
2025-05-24 19:58:19,707 - security - WARNING - No JWT secret provided. Generated a new one. Please set JWT_SECRET in your environment.
2025-05-24 19:58:19,753 - werkzeug - INFO - ************ - - [24/May/2025 19:58:19] "GET /api/notifications/count HTTP/1.1" 200 -
2025-05-24 19:58:19,782 - werkzeug - INFO - ************ - - [24/May/2025 19:58:19] "[36mGET /static/css/dashboard-pro.css?v=1.0.1 HTTP/1.1[0m" 304 -
2025-05-24 19:58:19,888 - werkzeug - INFO - ************ - - [24/May/2025 19:58:19] "GET /api/dashboard HTTP/1.1" 200 -
2025-05-24 19:58:20,604 - __main__ - INFO - جاري بدء التطبيق على العنوان: http://************:5000
2025-05-24 19:58:22,293 - werkzeug - WARNING -  * Debugger is active!
2025-05-24 19:58:22,670 - nobara - ERROR - خطأ في جانب العميل: Uncaught SyntaxError: Identifier 'DashboardManager' has already been declared
معلومات إضافية: {
  "source": "client",
  "client_stack": "SyntaxError: Identifier 'DashboardManager' has already been declared",
  "url": "http://************:5000/home",
  "timestamp": "2025-05-24T16:58:21.640Z",
  "browser": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********",
  "os": "Win32",
  "component": "home",
  "user_id": 1,
  "username": "admin",
  "client_ip": "************",
  "request_info": {
    "url": "http://************:5000/api/log-error",
    "base_url": "http://************:5000/api/log-error",
    "path": "/api/log-error",
    "method": "POST",
    "endpoint": "errors_api_blueprint.log_client_error",
    "remote_addr": "************",
    "user_agent": null,
    "referrer": "http://************:5000/home",
    "content_type": "application/json",
    "content_length": 436,
    "is_xhr": false,
    "is_secure": false,
    "timestamp": "2025-05-24 19:58:22",
    "headers": {
      "Host": "************:5000",
      "Connection": "keep-alive",
      "Content-Length": "436",
      "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********",
      "Content-Type": "application/json",
      "Accept": "*/*",
      "Origin": "http://************:5000",
      "Referer": "http://************:5000/home",
      "Accept-Encoding": "gzip, deflate",
      "Accept-Language": "en-US,en;q=0.9",
      "Cookie": "NEXT_LOCALE=en; session=.eJwljjkOg0AMAP_iOgXeZX3wGWR8KGkhVFH-HiKk6UYjzQfW2vN4wvLez3zA-gpYoIUJa8xWlUzKKK6utnFFKAt3n-Vi7m38bbXWg7bJEVWtUUrVlRUOVdx0cHRKVJPm0yROaMboETg6uZaTqURzpogMsuxwjZxH7vcNwvcHu5gvhg.aDHu2A.A3-CejEWJdQfEhVvRf38q1XW2LQ"
    },
    "args": {},
    "form": {},
    "cookies": {
      "NEXT_LOCALE": "en",
      "session": ".eJwljjkOg0AMAP_iOgXeZX3wGWR8KGkhVFH-HiKk6UYjzQfW2vN4wvLez3zA-gpYoIUJa8xWlUzKKK6utnFFKAt3n-Vi7m38bbXWg7bJEVWtUUrVlRUOVdx0cHRKVJPm0yROaMboETg6uZaTqURzpogMsuxwjZxH7vcNwvcHu5gvhg.aDHu2A.A3-CejEWJdQfEhVvRf38q1XW2LQ"
    }
  }
}
2025-05-24 19:58:22,735 - werkzeug - INFO - ************ - - [24/May/2025 19:58:22] "POST /api/log-error HTTP/1.1" 200 -
2025-05-24 19:58:22,814 - werkzeug - INFO -  * Debugger PIN: 692-333-133
2025-05-24 19:58:23,084 - werkzeug - INFO - ************ - - [24/May/2025 19:58:23] "[32mGET / HTTP/1.1[0m" 302 -
2025-05-24 19:58:23,559 - werkzeug - INFO - ************ - - [24/May/2025 19:58:23] "GET /home HTTP/1.1" 200 -
2025-05-24 19:58:24,091 - werkzeug - INFO - ************ - - [24/May/2025 19:58:24] "[36mGET /static/css/nobara-pro.css?v=1.0.0 HTTP/1.1[0m" 304 -
2025-05-24 19:58:24,254 - werkzeug - INFO - ************ - - [24/May/2025 19:58:24] "[36mGET /static/css/dashboard-pro.css?v=1.0.1 HTTP/1.1[0m" 304 -
2025-05-24 19:58:24,340 - werkzeug - INFO - ************ - - [24/May/2025 19:58:24] "[36mGET /static/uploads/logo_20250518120852.jpeg HTTP/1.1[0m" 304 -
2025-05-24 19:58:24,389 - werkzeug - INFO - ************ - - [24/May/2025 19:58:24] "[36mGET /static/js/dashboard-manager.js?v=1.0.0 HTTP/1.1[0m" 304 -
2025-05-24 19:58:25,205 - werkzeug - INFO - ************ - - [24/May/2025 19:58:25] "[36mGET /static/js/error-logger.js?v=1.0.2 HTTP/1.1[0m" 304 -
2025-05-24 19:58:25,262 - werkzeug - INFO - ************ - - [24/May/2025 19:58:25] "[36mGET /static/js/notifications-system.js?v=1.0.2 HTTP/1.1[0m" 304 -
2025-05-24 19:58:25,407 - werkzeug - INFO - ************ - - [24/May/2025 19:58:25] "[36mGET /static/js/system-manager.js?v=1.0.2 HTTP/1.1[0m" 304 -
2025-05-24 19:58:25,575 - werkzeug - INFO - ************ - - [24/May/2025 19:58:25] "[36mGET /static/js/dashboard-manager.js?v=1.0.2 HTTP/1.1[0m" 304 -
2025-05-24 19:58:25,653 - werkzeug - INFO - ************ - - [24/May/2025 19:58:25] "[36mGET /static/js/dashboard-pro.js?v=1.0.2 HTTP/1.1[0m" 304 -
2025-05-24 19:58:25,772 - werkzeug - INFO - ************ - - [24/May/2025 19:58:25] "GET /api/notifications/count HTTP/1.1" 200 -
2025-05-24 19:58:25,942 - werkzeug - INFO - ************ - - [24/May/2025 19:58:25] "[36mGET /static/css/nobara-pro.css?v=1.0.0 HTTP/1.1[0m" 304 -
2025-05-24 19:58:25,990 - werkzeug - INFO - ************ - - [24/May/2025 19:58:25] "GET /api/dashboard HTTP/1.1" 200 -
2025-05-24 19:58:26,279 - werkzeug - INFO - ************ - - [24/May/2025 19:58:26] "[36mGET /static/css/dashboard-pro.css?v=1.0.1 HTTP/1.1[0m" 304 -
2025-05-24 19:58:26,862 - nobara - ERROR - خطأ في جانب العميل: Uncaught SyntaxError: Identifier 'DashboardManager' has already been declared
معلومات إضافية: {
  "source": "client",
  "client_stack": "SyntaxError: Identifier 'DashboardManager' has already been declared",
  "url": "http://************:5000/home",
  "timestamp": "2025-05-24T16:58:25.909Z",
  "browser": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********",
  "os": "Win32",
  "component": "home",
  "user_id": 1,
  "username": "admin",
  "client_ip": "************",
  "request_info": {
    "url": "http://************:5000/api/log-error",
    "base_url": "http://************:5000/api/log-error",
    "path": "/api/log-error",
    "method": "POST",
    "endpoint": "errors_api_blueprint.log_client_error",
    "remote_addr": "************",
    "user_agent": null,
    "referrer": "http://************:5000/home",
    "content_type": "application/json",
    "content_length": 436,
    "is_xhr": false,
    "is_secure": false,
    "timestamp": "2025-05-24 19:58:26",
    "headers": {
      "Host": "************:5000",
      "Connection": "keep-alive",
      "Content-Length": "436",
      "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********",
      "Content-Type": "application/json",
      "Accept": "*/*",
      "Origin": "http://************:5000",
      "Referer": "http://************:5000/home",
      "Accept-Encoding": "gzip, deflate",
      "Accept-Language": "en-US,en;q=0.9",
      "Cookie": "NEXT_LOCALE=en; session=.eJwljjkOg0AMAP_iOgXeZX3wGWR8KGkhVFH-HiKk6UYjzQfW2vN4wvLez3zA-gpYoIUJa8xWlUzKKK6utnFFKAt3n-Vi7m38bbXWg7bJEVWtUUrVlRUOVdx0cHRKVJPm0yROaMboETg6uZaTqURzpogMsuxwjZxH7vcNwvcHu5gvhg.aDHu2A.A3-CejEWJdQfEhVvRf38q1XW2LQ"
    },
    "args": {},
    "form": {},
    "cookies": {
      "NEXT_LOCALE": "en",
      "session": ".eJwljjkOg0AMAP_iOgXeZX3wGWR8KGkhVFH-HiKk6UYjzQfW2vN4wvLez3zA-gpYoIUJa8xWlUzKKK6utnFFKAt3n-Vi7m38bbXWg7bJEVWtUUrVlRUOVdx0cHRKVJPm0yROaMboETg6uZaTqURzpogMsuxwjZxH7vcNwvcHu5gvhg.aDHu2A.A3-CejEWJdQfEhVvRf38q1XW2LQ"
    }
  }
}
2025-05-24 19:58:26,892 - werkzeug - INFO - ************ - - [24/May/2025 19:58:26] "POST /api/log-error HTTP/1.1" 200 -
2025-05-24 19:58:27,205 - werkzeug - INFO - ************ - - [24/May/2025 19:58:27] "[33mGET /static/img/nobara-logo.svg HTTP/1.1[0m" 404 -
2025-05-24 19:58:27,310 - werkzeug - INFO - ************ - - [24/May/2025 19:58:27] "[36mGET /static/uploads/logo_20250518120852.jpeg HTTP/1.1[0m" 304 -
2025-05-24 19:58:27,494 - werkzeug - INFO - ************ - - [24/May/2025 19:58:27] "[36mGET /static/js/dashboard-manager.js?v=1.0.0 HTTP/1.1[0m" 304 -
2025-05-24 19:58:27,639 - werkzeug - INFO - ************ - - [24/May/2025 19:58:27] "[36mGET /static/uploads/logo_20250518120852.jpeg HTTP/1.1[0m" 304 -
2025-05-24 19:58:27,719 - werkzeug - INFO - ************ - - [24/May/2025 19:58:27] "[36mGET /static/js/dashboard-manager.js?v=1.0.0 HTTP/1.1[0m" 304 -
2025-05-24 19:58:31,372 - werkzeug - INFO - ************ - - [24/May/2025 19:58:31] "[36mGET /static/js/error-logger.js?v=1.0.2 HTTP/1.1[0m" 304 -
2025-05-24 19:58:32,223 - werkzeug - INFO - ************ - - [24/May/2025 19:58:32] "[36mGET /static/js/notifications-system.js?v=1.0.2 HTTP/1.1[0m" 304 -
2025-05-24 19:58:32,784 - werkzeug - INFO - ************ - - [24/May/2025 19:58:32] "[36mGET /static/js/system-manager.js?v=1.0.2 HTTP/1.1[0m" 304 -
2025-05-24 19:58:34,965 - werkzeug - INFO - ************ - - [24/May/2025 19:58:34] "[36mGET /static/js/error-logger.js?v=1.0.2 HTTP/1.1[0m" 304 -
2025-05-24 19:58:35,000 - werkzeug - INFO - ************ - - [24/May/2025 19:58:34] "[36mGET /static/js/notifications-system.js?v=1.0.2 HTTP/1.1[0m" 304 -
2025-05-24 19:58:35,042 - werkzeug - INFO - ************ - - [24/May/2025 19:58:35] "[36mGET /static/js/system-manager.js?v=1.0.2 HTTP/1.1[0m" 304 -
2025-05-24 19:58:35,074 - werkzeug - INFO - ************ - - [24/May/2025 19:58:35] "[36mGET /static/js/dashboard-manager.js?v=1.0.2 HTTP/1.1[0m" 304 -
2025-05-24 19:58:35,114 - werkzeug - INFO - ************ - - [24/May/2025 19:58:35] "[36mGET /static/js/dashboard-pro.js?v=1.0.2 HTTP/1.1[0m" 304 -
2025-05-24 19:58:36,819 - werkzeug - INFO - ************ - - [24/May/2025 19:58:36] "GET /settings HTTP/1.1" 200 -
2025-05-24 19:58:36,925 - werkzeug - INFO - ************ - - [24/May/2025 19:58:36] "[36mGET /static/uploads/logo_20250518120852.jpeg HTTP/1.1[0m" 304 -
2025-05-24 19:58:37,811 - werkzeug - INFO - ************ - - [24/May/2025 19:58:37] "GET /api/notifications/count HTTP/1.1" 200 -
2025-05-24 19:58:39,334 - werkzeug - INFO - ************ - - [24/May/2025 19:58:39] "GET /settings/error-logs HTTP/1.1" 200 -
2025-05-24 19:58:39,511 - werkzeug - INFO - ************ - - [24/May/2025 19:58:39] "[36mGET /static/css/nobara-pro.css?v=1.0.0 HTTP/1.1[0m" 304 -
2025-05-24 19:58:39,673 - werkzeug - INFO - ************ - - [24/May/2025 19:58:39] "[36mGET /static/css/dashboard-pro.css?v=1.0.1 HTTP/1.1[0m" 304 -
2025-05-24 19:58:39,680 - werkzeug - INFO - ************ - - [24/May/2025 19:58:39] "[33mGET /static/img/nobara-logo.svg HTTP/1.1[0m" 404 -
2025-05-24 19:58:40,056 - werkzeug - INFO - ************ - - [24/May/2025 19:58:40] "[36mGET /static/js/error-logger.js?v=1.0.2 HTTP/1.1[0m" 304 -
2025-05-24 19:58:40,056 - werkzeug - INFO - ************ - - [24/May/2025 19:58:40] "[36mGET /static/js/notifications-system.js?v=1.0.2 HTTP/1.1[0m" 304 -
2025-05-24 19:58:40,062 - werkzeug - INFO - ************ - - [24/May/2025 19:58:40] "[36mGET /static/js/dashboard-manager.js?v=1.0.2 HTTP/1.1[0m" 304 -
2025-05-24 19:58:40,064 - werkzeug - INFO - ************ - - [24/May/2025 19:58:40] "[36mGET /static/js/system-manager.js?v=1.0.2 HTTP/1.1[0m" 304 -
2025-05-24 19:58:40,206 - werkzeug - INFO - ************ - - [24/May/2025 19:58:40] "[36mGET /static/js/dashboard-pro.js?v=1.0.2 HTTP/1.1[0m" 304 -
2025-05-24 19:58:40,384 - werkzeug - INFO - ************ - - [24/May/2025 19:58:40] "GET /api/notifications/count HTTP/1.1" 200 -
2025-05-24 19:59:15,249 - api.errors - ERROR - خطأ أثناء تصدير سجلات الأخطاء: User.log_activity() got an unexpected keyword argument 'action'
2025-05-24 19:59:15,319 - werkzeug - INFO - ************ - - [24/May/2025 19:59:15] "[35m[1mGET /api/errors/export?format=csv HTTP/1.1[0m" 500 -
2025-05-24 19:59:37,945 - werkzeug - INFO - ************ - - [24/May/2025 19:59:37] "GET /settings/error-logs HTTP/1.1" 200 -
2025-05-24 19:59:38,097 - werkzeug - INFO - ************ - - [24/May/2025 19:59:38] "[36mGET /static/css/nobara-pro.css?v=1.0.0 HTTP/1.1[0m" 304 -
2025-05-24 19:59:38,118 - werkzeug - INFO - ************ - - [24/May/2025 19:59:38] "[36mGET /static/css/dashboard-pro.css?v=1.0.1 HTTP/1.1[0m" 304 -
2025-05-24 19:59:38,168 - werkzeug - INFO - ************ - - [24/May/2025 19:59:38] "[33mGET /static/img/nobara-logo.svg HTTP/1.1[0m" 404 -
2025-05-24 19:59:38,552 - werkzeug - INFO - ************ - - [24/May/2025 19:59:38] "[36mGET /static/js/error-logger.js?v=1.0.2 HTTP/1.1[0m" 304 -
2025-05-24 19:59:38,713 - werkzeug - INFO - ************ - - [24/May/2025 19:59:38] "[36mGET /static/js/notifications-system.js?v=1.0.2 HTTP/1.1[0m" 304 -
2025-05-24 19:59:38,781 - werkzeug - INFO - ************ - - [24/May/2025 19:59:38] "[36mGET /static/js/system-manager.js?v=1.0.2 HTTP/1.1[0m" 304 -
2025-05-24 19:59:38,848 - werkzeug - INFO - ************ - - [24/May/2025 19:59:38] "[36mGET /static/js/dashboard-manager.js?v=1.0.2 HTTP/1.1[0m" 304 -
2025-05-24 19:59:38,913 - werkzeug - INFO - ************ - - [24/May/2025 19:59:38] "[36mGET /static/js/dashboard-pro.js?v=1.0.2 HTTP/1.1[0m" 304 -
2025-05-24 19:59:38,995 - werkzeug - INFO - ************ - - [24/May/2025 19:59:38] "GET /api/notifications/count HTTP/1.1" 200 -
2025-05-24 19:59:39,897 - werkzeug - INFO - ************ - - [24/May/2025 19:59:39] "GET /settings/error-logs HTTP/1.1" 200 -
2025-05-24 19:59:39,992 - werkzeug - INFO - ************ - - [24/May/2025 19:59:39] "[36mGET /static/css/nobara-pro.css?v=1.0.0 HTTP/1.1[0m" 304 -
2025-05-24 19:59:40,084 - werkzeug - INFO - ************ - - [24/May/2025 19:59:40] "[36mGET /static/css/dashboard-pro.css?v=1.0.1 HTTP/1.1[0m" 304 -
2025-05-24 19:59:40,144 - werkzeug - INFO - ************ - - [24/May/2025 19:59:40] "[33mGET /static/img/nobara-logo.svg HTTP/1.1[0m" 404 -
2025-05-24 19:59:40,782 - werkzeug - INFO - ************ - - [24/May/2025 19:59:40] "[36mGET /static/js/error-logger.js?v=1.0.2 HTTP/1.1[0m" 304 -
2025-05-24 19:59:40,898 - werkzeug - INFO - ************ - - [24/May/2025 19:59:40] "[36mGET /static/js/notifications-system.js?v=1.0.2 HTTP/1.1[0m" 304 -
2025-05-24 19:59:40,987 - werkzeug - INFO - ************ - - [24/May/2025 19:59:40] "[36mGET /static/js/system-manager.js?v=1.0.2 HTTP/1.1[0m" 304 -
2025-05-24 19:59:41,067 - werkzeug - INFO - ************ - - [24/May/2025 19:59:41] "[36mGET /static/js/dashboard-manager.js?v=1.0.2 HTTP/1.1[0m" 304 -
2025-05-24 19:59:41,213 - werkzeug - INFO - ************ - - [24/May/2025 19:59:41] "[36mGET /static/js/dashboard-pro.js?v=1.0.2 HTTP/1.1[0m" 304 -
2025-05-24 19:59:41,302 - werkzeug - INFO - ************ - - [24/May/2025 19:59:41] "GET /api/notifications/count HTTP/1.1" 200 -
2025-05-24 21:42:43,638 - werkzeug - INFO - ************ - - [24/May/2025 21:42:43] "GET /settings/error-logs HTTP/1.1" 200 -
2025-05-24 21:42:43,705 - werkzeug - INFO - ************ - - [24/May/2025 21:42:43] "[36mGET /static/css/nobara-pro.css?v=1.0.0 HTTP/1.1[0m" 304 -
2025-05-24 21:42:43,709 - werkzeug - INFO - ************ - - [24/May/2025 21:42:43] "[33mGET /static/img/nobara-logo.svg HTTP/1.1[0m" 404 -
2025-05-24 21:42:43,711 - werkzeug - INFO - ************ - - [24/May/2025 21:42:43] "[36mGET /static/css/dashboard-pro.css?v=1.0.1 HTTP/1.1[0m" 304 -
2025-05-24 21:42:44,570 - werkzeug - INFO - ************ - - [24/May/2025 21:42:44] "[36mGET /static/js/error-logger.js?v=1.0.2 HTTP/1.1[0m" 304 -
2025-05-24 21:42:44,573 - werkzeug - INFO - ************ - - [24/May/2025 21:42:44] "[36mGET /static/js/notifications-system.js?v=1.0.2 HTTP/1.1[0m" 304 -
2025-05-24 21:42:44,578 - werkzeug - INFO - ************ - - [24/May/2025 21:42:44] "[36mGET /static/js/dashboard-manager.js?v=1.0.2 HTTP/1.1[0m" 304 -
2025-05-24 21:42:44,582 - werkzeug - INFO - ************ - - [24/May/2025 21:42:44] "[36mGET /static/js/system-manager.js?v=1.0.2 HTTP/1.1[0m" 304 -
2025-05-24 21:42:44,590 - werkzeug - INFO - ************ - - [24/May/2025 21:42:44] "[36mGET /static/js/dashboard-pro.js?v=1.0.2 HTTP/1.1[0m" 304 -
2025-05-24 21:42:44,870 - werkzeug - INFO - ************ - - [24/May/2025 21:42:44] "GET /api/notifications/count HTTP/1.1" 200 -
2025-05-24 21:43:21,602 - werkzeug - INFO - ************ - - [24/May/2025 21:43:21] "GET /sales HTTP/1.1" 200 -
2025-05-24 21:43:21,693 - werkzeug - INFO - ************ - - [24/May/2025 21:43:21] "[33mGET /static/img/nobara-logo.svg HTTP/1.1[0m" 404 -
2025-05-24 21:43:21,714 - werkzeug - INFO - ************ - - [24/May/2025 21:43:21] "GET /static/js/sales_returns.js HTTP/1.1" 200 -
2025-05-24 21:43:22,791 - werkzeug - INFO - ************ - - [24/May/2025 21:43:22] "GET /api/notifications/count HTTP/1.1" 200 -
2025-05-24 21:43:46,222 - werkzeug - INFO - ************ - - [24/May/2025 21:43:46] "GET /pos HTTP/1.1" 200 -
2025-05-24 21:43:46,308 - werkzeug - INFO - ************ - - [24/May/2025 21:43:46] "[36mGET /static/css/nobara-pro.css?v=1.0.0 HTTP/1.1[0m" 304 -
2025-05-24 21:43:46,341 - werkzeug - INFO - ************ - - [24/May/2025 21:43:46] "[36mGET /static/css/dashboard-pro.css?v=1.0.1 HTTP/1.1[0m" 304 -
2025-05-24 21:43:46,344 - werkzeug - INFO - ************ - - [24/May/2025 21:43:46] "[33mGET /static/img/nobara-logo.svg HTTP/1.1[0m" 404 -
2025-05-24 21:43:46,355 - werkzeug - INFO - ************ - - [24/May/2025 21:43:46] "GET /static/js/pos.js HTTP/1.1" 200 -
2025-05-24 21:43:46,447 - werkzeug - INFO - ************ - - [24/May/2025 21:43:46] "GET /static/css/pos-theme.css?v=1.0.0 HTTP/1.1" 200 -
2025-05-24 21:43:46,473 - werkzeug - INFO - ************ - - [24/May/2025 21:43:46] "GET /static/js/pos_part2.js HTTP/1.1" 200 -
2025-05-24 21:43:46,500 - werkzeug - INFO - ************ - - [24/May/2025 21:43:46] "GET /static/js/pos_part4.js HTTP/1.1" 200 -
2025-05-24 21:43:46,521 - werkzeug - INFO - ************ - - [24/May/2025 21:43:46] "GET /static/js/pos_part3.js HTTP/1.1" 200 -
2025-05-24 21:43:46,586 - werkzeug - INFO - ************ - - [24/May/2025 21:43:46] "GET /static/js/pos_credit.js HTTP/1.1" 200 -
2025-05-24 21:43:46,595 - werkzeug - INFO - ************ - - [24/May/2025 21:43:46] "GET /static/js/pos_returns.js HTTP/1.1" 200 -
2025-05-24 21:43:46,700 - werkzeug - INFO - ************ - - [24/May/2025 21:43:46] "GET /static/js/invoice-search.js HTTP/1.1" 200 -
2025-05-24 21:43:46,710 - werkzeug - INFO - ************ - - [24/May/2025 21:43:46] "GET /static/js/customer_search.js HTTP/1.1" 200 -
2025-05-24 21:43:46,960 - werkzeug - INFO - ************ - - [24/May/2025 21:43:46] "GET /api/notifications/count HTTP/1.1" 200 -
2025-05-24 21:43:47,261 - werkzeug - INFO - ************ - - [24/May/2025 21:43:47] "GET /api/pos/products HTTP/1.1" 200 -
2025-05-24 21:44:35,407 - werkzeug - INFO - ************ - - [24/May/2025 21:44:35] "GET /sales HTTP/1.1" 200 -
2025-05-24 21:44:35,480 - werkzeug - INFO - ************ - - [24/May/2025 21:44:35] "[36mGET /static/js/sales_returns.js HTTP/1.1[0m" 304 -
2025-05-24 21:44:35,487 - werkzeug - INFO - ************ - - [24/May/2025 21:44:35] "[33mGET /static/img/nobara-logo.svg HTTP/1.1[0m" 404 -
2025-05-24 21:44:36,022 - werkzeug - INFO - ************ - - [24/May/2025 21:44:36] "GET /api/notifications/count HTTP/1.1" 200 -
2025-05-24 21:45:47,228 - werkzeug - INFO - ************ - - [24/May/2025 21:45:47] "GET /deferred-sales HTTP/1.1" 200 -
2025-05-24 21:45:47,311 - werkzeug - INFO - ************ - - [24/May/2025 21:45:47] "[33mGET /static/img/nobara-logo.svg HTTP/1.1[0m" 404 -
2025-05-24 21:45:47,556 - werkzeug - INFO - ************ - - [24/May/2025 21:45:47] "GET /api/notifications/count HTTP/1.1" 200 -
2025-05-24 21:46:07,327 - werkzeug - INFO - ************ - - [24/May/2025 21:46:07] "GET /pos HTTP/1.1" 200 -
2025-05-24 21:46:07,392 - werkzeug - INFO - ************ - - [24/May/2025 21:46:07] "[36mGET /static/css/nobara-pro.css?v=1.0.0 HTTP/1.1[0m" 304 -
2025-05-24 21:46:07,415 - werkzeug - INFO - ************ - - [24/May/2025 21:46:07] "[36mGET /static/css/dashboard-pro.css?v=1.0.1 HTTP/1.1[0m" 304 -
2025-05-24 21:46:07,417 - werkzeug - INFO - ************ - - [24/May/2025 21:46:07] "[36mGET /static/css/pos-theme.css?v=1.0.0 HTTP/1.1[0m" 304 -
2025-05-24 21:46:07,420 - werkzeug - INFO - ************ - - [24/May/2025 21:46:07] "[33mGET /static/img/nobara-logo.svg HTTP/1.1[0m" 404 -
2025-05-24 21:46:07,421 - werkzeug - INFO - ************ - - [24/May/2025 21:46:07] "[36mGET /static/js/pos.js HTTP/1.1[0m" 304 -
2025-05-24 21:46:07,448 - werkzeug - INFO - ************ - - [24/May/2025 21:46:07] "[36mGET /static/js/pos_part2.js HTTP/1.1[0m" 304 -
2025-05-24 21:46:07,449 - werkzeug - INFO - ************ - - [24/May/2025 21:46:07] "[36mGET /static/js/pos_part3.js HTTP/1.1[0m" 304 -
2025-05-24 21:46:07,453 - werkzeug - INFO - ************ - - [24/May/2025 21:46:07] "[36mGET /static/js/pos_part4.js HTTP/1.1[0m" 304 -
2025-05-24 21:46:07,466 - werkzeug - INFO - ************ - - [24/May/2025 21:46:07] "[36mGET /static/js/pos_credit.js HTTP/1.1[0m" 304 -
2025-05-24 21:46:07,503 - werkzeug - INFO - ************ - - [24/May/2025 21:46:07] "[36mGET /static/js/invoice-search.js HTTP/1.1[0m" 304 -
2025-05-24 21:46:07,507 - werkzeug - INFO - ************ - - [24/May/2025 21:46:07] "[36mGET /static/js/pos_returns.js HTTP/1.1[0m" 304 -
2025-05-24 21:46:07,518 - werkzeug - INFO - ************ - - [24/May/2025 21:46:07] "[36mGET /static/js/customer_search.js HTTP/1.1[0m" 304 -
2025-05-24 21:46:07,881 - werkzeug - INFO - ************ - - [24/May/2025 21:46:07] "GET /api/notifications/count HTTP/1.1" 200 -
2025-05-24 21:46:08,166 - werkzeug - INFO - ************ - - [24/May/2025 21:46:08] "GET /api/pos/products HTTP/1.1" 200 -
2025-05-24 21:46:42,446 - werkzeug - INFO - ************ - - [24/May/2025 21:46:42] "GET /purchases HTTP/1.1" 200 -
2025-05-24 21:46:42,537 - werkzeug - INFO - ************ - - [24/May/2025 21:46:42] "[33mGET /static/img/nobara-logo.svg HTTP/1.1[0m" 404 -
2025-05-24 21:46:43,020 - werkzeug - INFO - ************ - - [24/May/2025 21:46:43] "GET /api/notifications/count HTTP/1.1" 200 -
2025-05-24 21:46:45,787 - werkzeug - INFO - ************ - - [24/May/2025 21:46:45] "GET /deferred-purchases HTTP/1.1" 200 -
2025-05-24 21:46:45,865 - werkzeug - INFO - ************ - - [24/May/2025 21:46:45] "[33mGET /static/img/nobara-logo.svg HTTP/1.1[0m" 404 -
2025-05-24 21:46:46,123 - werkzeug - INFO - ************ - - [24/May/2025 21:46:46] "GET /api/notifications/count HTTP/1.1" 200 -
2025-05-24 21:47:10,555 - werkzeug - INFO - ************ - - [24/May/2025 21:47:10] "GET /customers HTTP/1.1" 200 -
2025-05-24 21:47:10,650 - werkzeug - INFO - ************ - - [24/May/2025 21:47:10] "[33mGET /static/img/nobara-logo.svg HTTP/1.1[0m" 404 -
2025-05-24 21:47:10,982 - werkzeug - INFO - ************ - - [24/May/2025 21:47:10] "GET /api/notifications/count HTTP/1.1" 200 -
2025-05-24 21:47:12,945 - werkzeug - INFO - ************ - - [24/May/2025 21:47:12] "GET /suppliers HTTP/1.1" 200 -
2025-05-24 21:47:13,012 - werkzeug - INFO - ************ - - [24/May/2025 21:47:13] "[33mGET /static/img/nobara-logo.svg HTTP/1.1[0m" 404 -
2025-05-24 21:47:13,533 - werkzeug - INFO - ************ - - [24/May/2025 21:47:13] "GET /api/notifications/count HTTP/1.1" 200 -
2025-05-24 21:47:15,376 - werkzeug - INFO - ************ - - [24/May/2025 21:47:15] "GET /reports HTTP/1.1" 200 -
2025-05-24 21:47:15,453 - werkzeug - INFO - ************ - - [24/May/2025 21:47:15] "[36mGET /static/uploads/logo_20250518120852.jpeg HTTP/1.1[0m" 304 -
2025-05-24 21:47:16,005 - werkzeug - INFO - ************ - - [24/May/2025 21:47:16] "GET /api/notifications/count HTTP/1.1" 200 -
2025-05-24 21:47:20,035 - werkzeug - INFO - ************ - - [24/May/2025 21:47:20] "GET /reports/cash-transactions HTTP/1.1" 200 -
2025-05-24 21:47:20,115 - werkzeug - INFO - ************ - - [24/May/2025 21:47:20] "[33mGET /static/img/nobara-logo.svg HTTP/1.1[0m" 404 -
2025-05-24 21:47:20,499 - werkzeug - INFO - ************ - - [24/May/2025 21:47:20] "[33mGET /static/img/nobara-logo.svg HTTP/1.1[0m" 404 -
2025-05-24 21:47:20,632 - werkzeug - INFO - ************ - - [24/May/2025 21:47:20] "GET /api/notifications/count HTTP/1.1" 200 -
2025-05-24 21:47:52,723 - nobara - ERROR - استثناء: TemplateNotFound: warehouses/warehouses.html
الوقت: 2025-05-24 21:47:52
نوع الاستثناء: TemplateNotFound
الملف: app.py
السطر: 809
الدالة: handle_user_exception
معلومات الطلب: {
  "url": "http://************:5000/warehouses",
  "base_url": "http://************:5000/warehouses",
  "path": "/warehouses",
  "method": "GET",
  "endpoint": "warehouses.index",
  "remote_addr": "************",
  "user_agent": null,
  "referrer": "http://************:5000/reports",
  "content_type": null,
  "content_length": null,
  "is_xhr": false,
  "is_secure": false,
  "timestamp": "2025-05-24 21:47:51",
  "headers": {
    "Host": "************:5000",
    "Connection": "keep-alive",
    "Upgrade-Insecure-Requests": "1",
    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********",
    "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7",
    "Referer": "http://************:5000/reports",
    "Accept-Encoding": "gzip, deflate",
    "Accept-Language": "en-US,en;q=0.9",
    "Cookie": "NEXT_LOCALE=en; session=.eJwljjkOg0AMAP_iOgXeZX3wGWR8KGkhVFH-HiKk6UYjzQfW2vN4wvLez3zA-gpYoIUJa8xWlUzKKK6utnFFKAt3n-Vi7m38bbXWg7bJEVWtUUrVlRUOVdx0cHRKVJPm0yROaMboETg6uZaTqURzpogMsuxwjZxH7vcNwvcHu5gvhg.aDHu2A.A3-CejEWJdQfEhVvRf38q1XW2LQ"
  },
  "args": {},
  "form": {},
  "cookies": {
    "NEXT_LOCALE": "en",
    "session": ".eJwljjkOg0AMAP_iOgXeZX3wGWR8KGkhVFH-HiKk6UYjzQfW2vN4wvLez3zA-gpYoIUJa8xWlUzKKK6utnFFKAt3n-Vi7m38bbXWg7bJEVWtUUrVlRUOVdx0cHRKVJPm0yROaMboETg6uZaTqURzpogMsuxwjZxH7vcNwvcHu5gvhg.aDHu2A.A3-CejEWJdQfEhVvRf38q1XW2LQ"
  }
}
معلومات النظام: {
  "memory_usage": "93.78 MB",
  "cpu_percent": "0.0%",
  "memory_percent": "80.5%",
  "disk_usage": "46.7%",
  "open_files": 11,
  "threads": 5,
  "connections": 0,
  "uptime": "1:49:49"
}
تتبع الاستثناء:
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\app.py", line 917, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\app.py", line 902, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)  # type: ignore[no-any-return]
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask_login\utils.py", line 290, in decorated_view
    return current_app.ensure_sync(func)(*args, **kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\routes\warehouses.py", line 45, in index
    return render_template('warehouses/warehouses.html',
                          warehouses=warehouses,
    ...<2 lines>...
                          status=status,
                          stats=stats)
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\templating.py", line 149, in render_template
    template = app.jinja_env.get_or_select_template(template_name_or_list)
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\jinja2\environment.py", line 1087, in get_or_select_template
    return self.get_template(template_name_or_list, parent, globals)
           ~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\jinja2\environment.py", line 1016, in get_template
    return self._load_template(name, globals)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\jinja2\environment.py", line 975, in _load_template
    template = self.loader.load(self, name, self.make_globals(globals))
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\jinja2\loaders.py", line 126, in load
    source, filename, uptodate = self.get_source(environment, name)
                                 ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\templating.py", line 65, in get_source
    return self._get_source_fast(environment, template)
           ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\templating.py", line 99, in _get_source_fast
    raise TemplateNotFound(template)
jinja2.exceptions.TemplateNotFound: warehouses/warehouses.html

2025-05-24 21:47:52,737 - werkzeug - INFO - ************ - - [24/May/2025 21:47:52] "[35m[1mGET /warehouses HTTP/1.1[0m" 500 -
2025-05-24 21:48:03,423 - nobara - ERROR - استثناء: TemplateNotFound: warehouses/warehouses.html
الوقت: 2025-05-24 21:48:03
نوع الاستثناء: TemplateNotFound
الملف: app.py
السطر: 809
الدالة: handle_user_exception
معلومات الطلب: {
  "url": "http://************:5000/warehouses",
  "base_url": "http://************:5000/warehouses",
  "path": "/warehouses",
  "method": "GET",
  "endpoint": "warehouses.index",
  "remote_addr": "************",
  "user_agent": null,
  "referrer": "http://************:5000/reports",
  "content_type": null,
  "content_length": null,
  "is_xhr": false,
  "is_secure": false,
  "timestamp": "2025-05-24 21:48:03",
  "headers": {
    "Host": "************:5000",
    "Connection": "keep-alive",
    "Upgrade-Insecure-Requests": "1",
    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********",
    "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7",
    "Referer": "http://************:5000/reports",
    "Accept-Encoding": "gzip, deflate",
    "Accept-Language": "en-US,en;q=0.9",
    "Cookie": "NEXT_LOCALE=en; session=.eJwljjkOg0AMAP_iOgXeZX3wGWR8KGkhVFH-HiKk6UYjzQfW2vN4wvLez3zA-gpYoIUJa8xWlUzKKK6utnFFKAt3n-Vi7m38bbXWg7bJEVWtUUrVlRUOVdx0cHRKVJPm0yROaMboETg6uZaTqURzpogMsuxwjZxH7vcNwvcHu5gvhg.aDHu2A.A3-CejEWJdQfEhVvRf38q1XW2LQ"
  },
  "args": {},
  "form": {},
  "cookies": {
    "NEXT_LOCALE": "en",
    "session": ".eJwljjkOg0AMAP_iOgXeZX3wGWR8KGkhVFH-HiKk6UYjzQfW2vN4wvLez3zA-gpYoIUJa8xWlUzKKK6utnFFKAt3n-Vi7m38bbXWg7bJEVWtUUrVlRUOVdx0cHRKVJPm0yROaMboETg6uZaTqURzpogMsuxwjZxH7vcNwvcHu5gvhg.aDHu2A.A3-CejEWJdQfEhVvRf38q1XW2LQ"
  }
}
معلومات النظام: {
  "memory_usage": "94.38 MB",
  "cpu_percent": "0.0%",
  "memory_percent": "80.8%",
  "disk_usage": "46.7%",
  "open_files": 11,
  "threads": 5,
  "connections": 0,
  "uptime": "1:50:00"
}
تتبع الاستثناء:
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\app.py", line 917, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\app.py", line 902, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)  # type: ignore[no-any-return]
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask_login\utils.py", line 290, in decorated_view
    return current_app.ensure_sync(func)(*args, **kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\routes\warehouses.py", line 45, in index
    return render_template('warehouses/warehouses.html',
                          warehouses=warehouses,
    ...<2 lines>...
                          status=status,
                          stats=stats)
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\templating.py", line 149, in render_template
    template = app.jinja_env.get_or_select_template(template_name_or_list)
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\jinja2\environment.py", line 1087, in get_or_select_template
    return self.get_template(template_name_or_list, parent, globals)
           ~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\jinja2\environment.py", line 1016, in get_template
    return self._load_template(name, globals)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\jinja2\environment.py", line 975, in _load_template
    template = self.loader.load(self, name, self.make_globals(globals))
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\jinja2\loaders.py", line 126, in load
    source, filename, uptodate = self.get_source(environment, name)
                                 ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\templating.py", line 65, in get_source
    return self._get_source_fast(environment, template)
           ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\templating.py", line 99, in _get_source_fast
    raise TemplateNotFound(template)
jinja2.exceptions.TemplateNotFound: warehouses/warehouses.html

2025-05-24 21:48:03,440 - werkzeug - INFO - ************ - - [24/May/2025 21:48:03] "[35m[1mGET /warehouses HTTP/1.1[0m" 500 -
2025-05-24 21:48:23,720 - werkzeug - INFO - ************ - - [24/May/2025 21:48:23] "GET /users HTTP/1.1" 200 -
2025-05-24 21:48:23,792 - werkzeug - INFO - ************ - - [24/May/2025 21:48:23] "[33mGET /static/img/nobara-logo.svg HTTP/1.1[0m" 404 -
2025-05-24 21:48:24,154 - werkzeug - INFO - ************ - - [24/May/2025 21:48:24] "GET /api/notifications/count HTTP/1.1" 200 -
2025-05-24 21:48:26,061 - nobara - ERROR - استثناء: TemplateNotFound: user_form.html
الوقت: 2025-05-24 21:48:26
نوع الاستثناء: TemplateNotFound
الملف: app.py
السطر: 809
الدالة: handle_user_exception
معلومات الطلب: {
  "url": "http://************:5000/users/1/edit",
  "base_url": "http://************:5000/users/1/edit",
  "path": "/users/1/edit",
  "method": "GET",
  "endpoint": "users.edit",
  "remote_addr": "************",
  "user_agent": null,
  "referrer": "http://************:5000/users",
  "content_type": null,
  "content_length": null,
  "is_xhr": false,
  "is_secure": false,
  "timestamp": "2025-05-24 21:48:25",
  "headers": {
    "Host": "************:5000",
    "Connection": "keep-alive",
    "Upgrade-Insecure-Requests": "1",
    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********",
    "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7",
    "Referer": "http://************:5000/users",
    "Accept-Encoding": "gzip, deflate",
    "Accept-Language": "en-US,en;q=0.9",
    "Cookie": "NEXT_LOCALE=en; session=.eJwljjkOg0AMAP_iOgXeZX3wGWR8KGkhVFH-HiKk6UYjzQfW2vN4wvLez3zA-gpYoIUJa8xWlUzKKK6utnFFKAt3n-Vi7m38bbXWg7bJEVWtUUrVlRUOVdx0cHRKVJPm0yROaMboETg6uZaTqURzpogMsuxwjZxH7vcNwvcHu5gvhg.aDHu2A.A3-CejEWJdQfEhVvRf38q1XW2LQ"
  },
  "args": {},
  "form": {},
  "cookies": {
    "NEXT_LOCALE": "en",
    "session": ".eJwljjkOg0AMAP_iOgXeZX3wGWR8KGkhVFH-HiKk6UYjzQfW2vN4wvLez3zA-gpYoIUJa8xWlUzKKK6utnFFKAt3n-Vi7m38bbXWg7bJEVWtUUrVlRUOVdx0cHRKVJPm0yROaMboETg6uZaTqURzpogMsuxwjZxH7vcNwvcHu5gvhg.aDHu2A.A3-CejEWJdQfEhVvRf38q1XW2LQ"
  }
}
معلومات النظام: {
  "memory_usage": "94.91 MB",
  "cpu_percent": "0.0%",
  "memory_percent": "81.1%",
  "disk_usage": "46.7%",
  "open_files": 11,
  "threads": 5,
  "connections": 0,
  "uptime": "1:50:22"
}
تتبع الاستثناء:
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\app.py", line 917, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\app.py", line 902, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)  # type: ignore[no-any-return]
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask_login\utils.py", line 290, in decorated_view
    return current_app.ensure_sync(func)(*args, **kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\routes\users.py", line 222, in edit
    return render_template('user_form.html', user=user, action='edit')
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\templating.py", line 149, in render_template
    template = app.jinja_env.get_or_select_template(template_name_or_list)
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\jinja2\environment.py", line 1087, in get_or_select_template
    return self.get_template(template_name_or_list, parent, globals)
           ~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\jinja2\environment.py", line 1016, in get_template
    return self._load_template(name, globals)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\jinja2\environment.py", line 975, in _load_template
    template = self.loader.load(self, name, self.make_globals(globals))
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\jinja2\loaders.py", line 126, in load
    source, filename, uptodate = self.get_source(environment, name)
                                 ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\templating.py", line 65, in get_source
    return self._get_source_fast(environment, template)
           ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\templating.py", line 99, in _get_source_fast
    raise TemplateNotFound(template)
jinja2.exceptions.TemplateNotFound: user_form.html

2025-05-24 21:48:26,068 - werkzeug - INFO - ************ - - [24/May/2025 21:48:26] "[35m[1mGET /users/1/edit HTTP/1.1[0m" 500 -
2025-05-24 21:48:29,942 - permissions_manager - INFO - تم إنشاء الصلاحيات بنجاح
2025-05-24 21:48:30,946 - permissions_manager - INFO - تم منح الصلاحيات لـ 1 مدير بنجاح
2025-05-24 21:48:31,742 - nobara - ERROR - استثناء: TemplateNotFound: user_permissions.html
الوقت: 2025-05-24 21:48:31
نوع الاستثناء: TemplateNotFound
الملف: app.py
السطر: 809
الدالة: handle_user_exception
معلومات الطلب: {
  "url": "http://************:5000/users/1/permissions",
  "base_url": "http://************:5000/users/1/permissions",
  "path": "/users/1/permissions",
  "method": "GET",
  "endpoint": "users.user_permissions",
  "remote_addr": "************",
  "user_agent": null,
  "referrer": "http://************:5000/users",
  "content_type": null,
  "content_length": null,
  "is_xhr": false,
  "is_secure": false,
  "timestamp": "2025-05-24 21:48:31",
  "headers": {
    "Host": "************:5000",
    "Connection": "keep-alive",
    "Upgrade-Insecure-Requests": "1",
    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********",
    "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7",
    "Referer": "http://************:5000/users",
    "Accept-Encoding": "gzip, deflate",
    "Accept-Language": "en-US,en;q=0.9",
    "Cookie": "NEXT_LOCALE=en; session=.eJwljjkOg0AMAP_iOgXeZX3wGWR8KGkhVFH-HiKk6UYjzQfW2vN4wvLez3zA-gpYoIUJa8xWlUzKKK6utnFFKAt3n-Vi7m38bbXWg7bJEVWtUUrVlRUOVdx0cHRKVJPm0yROaMboETg6uZaTqURzpogMsuxwjZxH7vcNwvcHu5gvhg.aDHu2A.A3-CejEWJdQfEhVvRf38q1XW2LQ"
  },
  "args": {},
  "form": {},
  "cookies": {
    "NEXT_LOCALE": "en",
    "session": ".eJwljjkOg0AMAP_iOgXeZX3wGWR8KGkhVFH-HiKk6UYjzQfW2vN4wvLez3zA-gpYoIUJa8xWlUzKKK6utnFFKAt3n-Vi7m38bbXWg7bJEVWtUUrVlRUOVdx0cHRKVJPm0yROaMboETg6uZaTqURzpogMsuxwjZxH7vcNwvcHu5gvhg.aDHu2A.A3-CejEWJdQfEhVvRf38q1XW2LQ"
  }
}
معلومات النظام: {
  "memory_usage": "96.42 MB",
  "cpu_percent": "0.0%",
  "memory_percent": "81.1%",
  "disk_usage": "46.7%",
  "open_files": 11,
  "threads": 5,
  "connections": 0,
  "uptime": "1:50:28"
}
تتبع الاستثناء:
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\app.py", line 917, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\app.py", line 902, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)  # type: ignore[no-any-return]
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask_login\utils.py", line 290, in decorated_view
    return current_app.ensure_sync(func)(*args, **kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\routes\users.py", line 316, in user_permissions
    return render_template(
        'user_permissions.html',
    ...<3 lines>...
        current_user=current_user
    )
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\templating.py", line 149, in render_template
    template = app.jinja_env.get_or_select_template(template_name_or_list)
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\jinja2\environment.py", line 1087, in get_or_select_template
    return self.get_template(template_name_or_list, parent, globals)
           ~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\jinja2\environment.py", line 1016, in get_template
    return self._load_template(name, globals)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\jinja2\environment.py", line 975, in _load_template
    template = self.loader.load(self, name, self.make_globals(globals))
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\jinja2\loaders.py", line 126, in load
    source, filename, uptodate = self.get_source(environment, name)
                                 ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\templating.py", line 65, in get_source
    return self._get_source_fast(environment, template)
           ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\templating.py", line 99, in _get_source_fast
    raise TemplateNotFound(template)
jinja2.exceptions.TemplateNotFound: user_permissions.html

2025-05-24 21:48:31,770 - werkzeug - INFO - ************ - - [24/May/2025 21:48:31] "[35m[1mGET /users/1/permissions HTTP/1.1[0m" 500 -
2025-05-24 21:48:46,564 - permissions_manager - INFO - الصلاحيات موجودة بالفعل في النظام
2025-05-24 21:48:46,694 - nobara - ERROR - استثناء: TemplateNotFound: user_form.html
الوقت: 2025-05-24 21:48:46
نوع الاستثناء: TemplateNotFound
الملف: app.py
السطر: 809
الدالة: handle_user_exception
معلومات الطلب: {
  "url": "http://************:5000/users/create",
  "base_url": "http://************:5000/users/create",
  "path": "/users/create",
  "method": "GET",
  "endpoint": "users.create",
  "remote_addr": "************",
  "user_agent": null,
  "referrer": "http://************:5000/users",
  "content_type": null,
  "content_length": null,
  "is_xhr": false,
  "is_secure": false,
  "timestamp": "2025-05-24 21:48:46",
  "headers": {
    "Host": "************:5000",
    "Connection": "keep-alive",
    "Upgrade-Insecure-Requests": "1",
    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********",
    "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7",
    "Referer": "http://************:5000/users",
    "Accept-Encoding": "gzip, deflate",
    "Accept-Language": "en-US,en;q=0.9",
    "Cookie": "NEXT_LOCALE=en; session=.eJwljjkOg0AMAP_iOgXeZX3wGWR8KGkhVFH-HiKk6UYjzQfW2vN4wvLez3zA-gpYoIUJa8xWlUzKKK6utnFFKAt3n-Vi7m38bbXWg7bJEVWtUUrVlRUOVdx0cHRKVJPm0yROaMboETg6uZaTqURzpogMsuxwjZxH7vcNwvcHu5gvhg.aDHu2A.A3-CejEWJdQfEhVvRf38q1XW2LQ"
  },
  "args": {},
  "form": {},
  "cookies": {
    "NEXT_LOCALE": "en",
    "session": ".eJwljjkOg0AMAP_iOgXeZX3wGWR8KGkhVFH-HiKk6UYjzQfW2vN4wvLez3zA-gpYoIUJa8xWlUzKKK6utnFFKAt3n-Vi7m38bbXWg7bJEVWtUUrVlRUOVdx0cHRKVJPm0yROaMboETg6uZaTqURzpogMsuxwjZxH7vcNwvcHu5gvhg.aDHu2A.A3-CejEWJdQfEhVvRf38q1XW2LQ"
  }
}
معلومات النظام: {
  "memory_usage": "96.40 MB",
  "cpu_percent": "0.0%",
  "memory_percent": "81.2%",
  "disk_usage": "46.7%",
  "open_files": 11,
  "threads": 5,
  "connections": 0,
  "uptime": "1:50:43"
}
تتبع الاستثناء:
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\app.py", line 917, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\app.py", line 902, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)  # type: ignore[no-any-return]
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask_login\utils.py", line 290, in decorated_view
    return current_app.ensure_sync(func)(*args, **kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\routes\users.py", line 155, in create
    return render_template('user_form.html', user=None, action='create')
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\templating.py", line 149, in render_template
    template = app.jinja_env.get_or_select_template(template_name_or_list)
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\jinja2\environment.py", line 1087, in get_or_select_template
    return self.get_template(template_name_or_list, parent, globals)
           ~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\jinja2\environment.py", line 1016, in get_template
    return self._load_template(name, globals)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\jinja2\environment.py", line 975, in _load_template
    template = self.loader.load(self, name, self.make_globals(globals))
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\jinja2\loaders.py", line 126, in load
    source, filename, uptodate = self.get_source(environment, name)
                                 ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\templating.py", line 65, in get_source
    return self._get_source_fast(environment, template)
           ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\templating.py", line 99, in _get_source_fast
    raise TemplateNotFound(template)
jinja2.exceptions.TemplateNotFound: user_form.html

2025-05-24 21:48:46,720 - werkzeug - INFO - ************ - - [24/May/2025 21:48:46] "[35m[1mGET /users/create HTTP/1.1[0m" 500 -
2025-05-24 21:48:57,603 - werkzeug - INFO - ************ - - [24/May/2025 21:48:57] "GET /settings HTTP/1.1" 200 -
2025-05-24 21:48:57,706 - werkzeug - INFO - ************ - - [24/May/2025 21:48:57] "[36mGET /static/uploads/logo_20250518120852.jpeg HTTP/1.1[0m" 304 -
2025-05-24 21:48:58,000 - werkzeug - INFO - ************ - - [24/May/2025 21:48:57] "GET /api/notifications/count HTTP/1.1" 200 -
2025-05-24 21:48:59,145 - werkzeug - INFO - ************ - - [24/May/2025 21:48:59] "GET /settings/store-info HTTP/1.1" 200 -
2025-05-24 21:48:59,207 - werkzeug - INFO - ************ - - [24/May/2025 21:48:59] "[36mGET /static/uploads/logo_20250518120852.jpeg HTTP/1.1[0m" 304 -
2025-05-24 21:48:59,903 - werkzeug - INFO - ************ - - [24/May/2025 21:48:59] "GET /api/notifications/count HTTP/1.1" 200 -
2025-05-24 21:50:05,781 - werkzeug - INFO - ************ - - [24/May/2025 21:50:05] "GET /api/notifications/recent HTTP/1.1" 200 -
2025-05-24 21:50:09,389 - werkzeug - INFO - ************ - - [24/May/2025 21:50:09] "GET /api/notifications/recent HTTP/1.1" 200 -
2025-05-24 21:50:18,987 - nobara - ERROR - استثناء: TemplateNotFound: profile.html
الوقت: 2025-05-24 21:50:18
نوع الاستثناء: TemplateNotFound
الملف: app.py
السطر: 809
الدالة: handle_user_exception
معلومات الطلب: {
  "url": "http://************:5000/profile",
  "base_url": "http://************:5000/profile",
  "path": "/profile",
  "method": "GET",
  "endpoint": "users.profile",
  "remote_addr": "************",
  "user_agent": null,
  "referrer": "http://************:5000/settings",
  "content_type": null,
  "content_length": null,
  "is_xhr": false,
  "is_secure": false,
  "timestamp": "2025-05-24 21:50:18",
  "headers": {
    "Host": "************:5000",
    "Connection": "keep-alive",
    "Upgrade-Insecure-Requests": "1",
    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********",
    "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7",
    "Referer": "http://************:5000/settings",
    "Accept-Encoding": "gzip, deflate",
    "Accept-Language": "en-US,en;q=0.9",
    "Cookie": "NEXT_LOCALE=en; session=.eJwljjkOg0AMAP_iOgXeZX3wGWR8KGkhVFH-HiKk6UYjzQfW2vN4wvLez3zA-gpYoIUJa8xWlUzKKK6utnFFKAt3n-Vi7m38bbXWg7bJEVWtUUrVlRUOVdx0cHRKVJPm0yROaMboETg6uZaTqURzpogMsuxwjZxH7vcNwvcHu5gvhg.aDHu2A.A3-CejEWJdQfEhVvRf38q1XW2LQ"
  },
  "args": {},
  "form": {},
  "cookies": {
    "NEXT_LOCALE": "en",
    "session": ".eJwljjkOg0AMAP_iOgXeZX3wGWR8KGkhVFH-HiKk6UYjzQfW2vN4wvLez3zA-gpYoIUJa8xWlUzKKK6utnFFKAt3n-Vi7m38bbXWg7bJEVWtUUrVlRUOVdx0cHRKVJPm0yROaMboETg6uZaTqURzpogMsuxwjZxH7vcNwvcHu5gvhg.aDHu2A.A3-CejEWJdQfEhVvRf38q1XW2LQ"
  }
}
معلومات النظام: {
  "memory_usage": "96.48 MB",
  "cpu_percent": "0.0%",
  "memory_percent": "85.6%",
  "disk_usage": "46.7%",
  "open_files": 11,
  "threads": 5,
  "connections": 0,
  "uptime": "1:52:15"
}
تتبع الاستثناء:
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\app.py", line 917, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\app.py", line 902, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)  # type: ignore[no-any-return]
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask_login\utils.py", line 290, in decorated_view
    return current_app.ensure_sync(func)(*args, **kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\routes\users.py", line 361, in profile
    return render_template('profile.html', user=current_user, current_user=current_user)
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\templating.py", line 149, in render_template
    template = app.jinja_env.get_or_select_template(template_name_or_list)
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\jinja2\environment.py", line 1087, in get_or_select_template
    return self.get_template(template_name_or_list, parent, globals)
           ~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\jinja2\environment.py", line 1016, in get_template
    return self._load_template(name, globals)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\jinja2\environment.py", line 975, in _load_template
    template = self.loader.load(self, name, self.make_globals(globals))
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\jinja2\loaders.py", line 126, in load
    source, filename, uptodate = self.get_source(environment, name)
                                 ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\templating.py", line 65, in get_source
    return self._get_source_fast(environment, template)
           ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\templating.py", line 99, in _get_source_fast
    raise TemplateNotFound(template)
jinja2.exceptions.TemplateNotFound: profile.html

2025-05-24 21:50:19,000 - werkzeug - INFO - ************ - - [24/May/2025 21:50:19] "[35m[1mGET /profile HTTP/1.1[0m" 500 -
2025-05-24 21:59:11,727 - werkzeug - INFO - ************ - - [24/May/2025 21:59:11] "GET /settings HTTP/1.1" 200 -
2025-05-24 21:59:11,881 - werkzeug - INFO - ************ - - [24/May/2025 21:59:11] "[36mGET /static/uploads/logo_20250518120852.jpeg HTTP/1.1[0m" 304 -
2025-05-24 21:59:12,743 - werkzeug - INFO - ************ - - [24/May/2025 21:59:12] "GET /api/notifications/count HTTP/1.1" 200 -
2025-05-24 21:59:14,534 - werkzeug - INFO - ************ - - [24/May/2025 21:59:14] "GET /settings/error-logs HTTP/1.1" 200 -
2025-05-24 21:59:14,694 - werkzeug - INFO - ************ - - [24/May/2025 21:59:14] "[36mGET /static/css/nobara-pro.css?v=1.0.0 HTTP/1.1[0m" 304 -
2025-05-24 21:59:14,698 - werkzeug - INFO - ************ - - [24/May/2025 21:59:14] "[36mGET /static/css/dashboard-pro.css?v=1.0.1 HTTP/1.1[0m" 304 -
2025-05-24 21:59:14,715 - werkzeug - INFO - ************ - - [24/May/2025 21:59:14] "[33mGET /static/img/nobara-logo.svg HTTP/1.1[0m" 404 -
2025-05-24 21:59:15,758 - werkzeug - INFO - ************ - - [24/May/2025 21:59:15] "[36mGET /static/js/notifications-system.js?v=1.0.2 HTTP/1.1[0m" 304 -
2025-05-24 21:59:15,809 - werkzeug - INFO - ************ - - [24/May/2025 21:59:15] "[36mGET /static/js/error-logger.js?v=1.0.2 HTTP/1.1[0m" 304 -
2025-05-24 21:59:15,831 - werkzeug - INFO - ************ - - [24/May/2025 21:59:15] "[36mGET /static/js/dashboard-pro.js?v=1.0.2 HTTP/1.1[0m" 304 -
2025-05-24 21:59:15,848 - werkzeug - INFO - ************ - - [24/May/2025 21:59:15] "[36mGET /static/js/system-manager.js?v=1.0.2 HTTP/1.1[0m" 304 -
2025-05-24 21:59:15,878 - werkzeug - INFO - ************ - - [24/May/2025 21:59:15] "GET /api/notifications/count HTTP/1.1" 200 -
2025-05-24 21:59:15,943 - werkzeug - INFO - ************ - - [24/May/2025 21:59:15] "GET /static/js/dashboard-manager.js?v=1.0.2 HTTP/1.1" 200 -
2025-05-24 22:00:10,040 - werkzeug - INFO - ************ - - [24/May/2025 22:00:10] "GET /warehouses HTTP/1.1" 200 -
2025-05-24 22:00:10,139 - werkzeug - INFO - ************ - - [24/May/2025 22:00:10] "[36mGET /static/css/nobara-pro.css?v=1.0.0 HTTP/1.1[0m" 304 -
2025-05-24 22:00:10,158 - werkzeug - INFO - ************ - - [24/May/2025 22:00:10] "[33mGET /static/img/nobara-logo.svg HTTP/1.1[0m" 404 -
2025-05-24 22:00:10,159 - werkzeug - INFO - ************ - - [24/May/2025 22:00:10] "[36mGET /static/css/dashboard-pro.css?v=1.0.1 HTTP/1.1[0m" 304 -
2025-05-24 22:00:10,467 - werkzeug - INFO - ************ - - [24/May/2025 22:00:10] "[36mGET /static/js/error-logger.js?v=1.0.2 HTTP/1.1[0m" 304 -
2025-05-24 22:00:10,481 - werkzeug - INFO - ************ - - [24/May/2025 22:00:10] "[36mGET /static/js/system-manager.js?v=1.0.2 HTTP/1.1[0m" 304 -
2025-05-24 22:00:10,481 - werkzeug - INFO - ************ - - [24/May/2025 22:00:10] "[36mGET /static/js/dashboard-pro.js?v=1.0.2 HTTP/1.1[0m" 304 -
2025-05-24 22:00:10,481 - werkzeug - INFO - ************ - - [24/May/2025 22:00:10] "[36mGET /static/js/notifications-system.js?v=1.0.2 HTTP/1.1[0m" 304 -
2025-05-24 22:00:10,483 - werkzeug - INFO - ************ - - [24/May/2025 22:00:10] "[36mGET /static/js/dashboard-manager.js?v=1.0.2 HTTP/1.1[0m" 304 -
2025-05-24 22:00:10,554 - werkzeug - INFO - ************ - - [24/May/2025 22:00:10] "GET /api/notifications/count HTTP/1.1" 200 -
2025-05-24 22:00:29,174 - werkzeug - INFO - ************ - - [24/May/2025 22:00:29] "GET /reports HTTP/1.1" 200 -
2025-05-24 22:00:29,265 - werkzeug - INFO - ************ - - [24/May/2025 22:00:29] "[36mGET /static/uploads/logo_20250518120852.jpeg HTTP/1.1[0m" 304 -
2025-05-24 22:00:29,838 - werkzeug - INFO - ************ - - [24/May/2025 22:00:29] "GET /api/notifications/count HTTP/1.1" 200 -
2025-05-24 22:00:31,303 - werkzeug - INFO - ************ - - [24/May/2025 22:00:31] "GET /users HTTP/1.1" 200 -
2025-05-24 22:00:31,399 - werkzeug - INFO - ************ - - [24/May/2025 22:00:31] "[33mGET /static/img/nobara-logo.svg HTTP/1.1[0m" 404 -
2025-05-24 22:00:31,776 - werkzeug - INFO - ************ - - [24/May/2025 22:00:31] "GET /api/notifications/count HTTP/1.1" 200 -
2025-05-24 22:00:33,510 - nobara - ERROR - استثناء: BuildError: Could not build url for endpoint 'users.update' with values ['id']. Did you mean 'users.delete' instead?
الوقت: 2025-05-24 22:00:33
نوع الاستثناء: BuildError
الملف: app.py
السطر: 809
الدالة: handle_user_exception
معلومات الطلب: {
  "url": "http://************:5000/users/1/edit",
  "base_url": "http://************:5000/users/1/edit",
  "path": "/users/1/edit",
  "method": "GET",
  "endpoint": "users.edit",
  "remote_addr": "************",
  "user_agent": null,
  "referrer": "http://************:5000/users",
  "content_type": null,
  "content_length": null,
  "is_xhr": false,
  "is_secure": false,
  "timestamp": "2025-05-24 22:00:33",
  "headers": {
    "Host": "************:5000",
    "Connection": "keep-alive",
    "Upgrade-Insecure-Requests": "1",
    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********",
    "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7",
    "Referer": "http://************:5000/users",
    "Accept-Encoding": "gzip, deflate",
    "Accept-Language": "en-US,en;q=0.9",
    "Cookie": "NEXT_LOCALE=en; session=.eJwljjkOg0AMAP_iOgXeZX3wGWR8KGkhVFH-HiKk6UYjzQfW2vN4wvLez3zA-gpYoIUJa8xWlUzKKK6utnFFKAt3n-Vi7m38bbXWg7bJEVWtUUrVlRUOVdx0cHRKVJPm0yROaMboETg6uZaTqURzpogMsuxwjZxH7vcNwvcHu5gvhg.aDHu2A.A3-CejEWJdQfEhVvRf38q1XW2LQ"
  },
  "args": {},
  "form": {},
  "cookies": {
    "NEXT_LOCALE": "en",
    "session": ".eJwljjkOg0AMAP_iOgXeZX3wGWR8KGkhVFH-HiKk6UYjzQfW2vN4wvLez3zA-gpYoIUJa8xWlUzKKK6utnFFKAt3n-Vi7m38bbXWg7bJEVWtUUrVlRUOVdx0cHRKVJPm0yROaMboETg6uZaTqURzpogMsuxwjZxH7vcNwvcHu5gvhg.aDHu2A.A3-CejEWJdQfEhVvRf38q1XW2LQ"
  }
}
معلومات النظام: {
  "memory_usage": "93.84 MB",
  "cpu_percent": "0.0%",
  "memory_percent": "83.5%",
  "disk_usage": "46.7%",
  "open_files": 11,
  "threads": 5,
  "connections": 0,
  "uptime": "2:02:30"
}
تتبع الاستثناء:
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\app.py", line 917, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\app.py", line 902, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)  # type: ignore[no-any-return]
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask_login\utils.py", line 290, in decorated_view
    return current_app.ensure_sync(func)(*args, **kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\routes\users.py", line 222, in edit
    return render_template('user_form.html', user=user, action='edit')
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\templating.py", line 150, in render_template
    return _render(app, template, context)
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\templating.py", line 131, in _render
    rv = template.render(context)
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\jinja2\environment.py", line 1295, in render
    self.environment.handle_exception()
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\jinja2\environment.py", line 942, in handle_exception
    raise rewrite_traceback_stack(source=source)
  File "c:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\templates\user_form.html", line 1, in top-level template code
    {% extends 'core/base.html' %}
    ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\templates\core\base.html", line 19, in top-level template code
    {% block content %}{% endblock %}
    ^^^^^^^^^
  File "c:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\templates\user_form.html", line 110, in block 'content'
    <form method="POST" action="{% if action == 'create' %}{{ url_for('users.store') }}{% else %}{{ url_for('users.update', id=user.id) }}{% endif %}">
    ^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\app.py", line 1121, in url_for
    return self.handle_url_build_error(error, endpoint, values)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\app.py", line 1110, in url_for
    rv = url_adapter.build(  # type: ignore[union-attr]
        endpoint,
    ...<3 lines>...
        force_external=_external,
    )
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\werkzeug\routing\map.py", line 924, in build
    raise BuildError(endpoint, values, method, self)
werkzeug.routing.exceptions.BuildError: Could not build url for endpoint 'users.update' with values ['id']. Did you mean 'users.delete' instead?

2025-05-24 22:00:33,522 - werkzeug - INFO - ************ - - [24/May/2025 22:00:33] "[35m[1mGET /users/1/edit HTTP/1.1[0m" 500 -
2025-05-24 22:00:35,897 - werkzeug - INFO - ************ - - [24/May/2025 22:00:35] "GET /warehouses HTTP/1.1" 200 -
2025-05-24 22:00:35,999 - werkzeug - INFO - ************ - - [24/May/2025 22:00:35] "[36mGET /static/css/nobara-pro.css?v=1.0.0 HTTP/1.1[0m" 304 -
2025-05-24 22:00:36,002 - werkzeug - INFO - ************ - - [24/May/2025 22:00:36] "[36mGET /static/css/dashboard-pro.css?v=1.0.1 HTTP/1.1[0m" 304 -
2025-05-24 22:00:36,003 - werkzeug - INFO - ************ - - [24/May/2025 22:00:36] "[33mGET /static/img/nobara-logo.svg HTTP/1.1[0m" 404 -
2025-05-24 22:00:36,260 - werkzeug - INFO - ************ - - [24/May/2025 22:00:36] "[36mGET /static/js/notifications-system.js?v=1.0.2 HTTP/1.1[0m" 304 -
2025-05-24 22:00:36,261 - werkzeug - INFO - ************ - - [24/May/2025 22:00:36] "[36mGET /static/js/error-logger.js?v=1.0.2 HTTP/1.1[0m" 304 -
2025-05-24 22:00:36,268 - werkzeug - INFO - ************ - - [24/May/2025 22:00:36] "[36mGET /static/js/system-manager.js?v=1.0.2 HTTP/1.1[0m" 304 -
2025-05-24 22:00:36,270 - werkzeug - INFO - ************ - - [24/May/2025 22:00:36] "[36mGET /static/js/dashboard-manager.js?v=1.0.2 HTTP/1.1[0m" 304 -
2025-05-24 22:00:36,310 - werkzeug - INFO - ************ - - [24/May/2025 22:00:36] "[36mGET /static/js/dashboard-pro.js?v=1.0.2 HTTP/1.1[0m" 304 -
2025-05-24 22:00:36,343 - werkzeug - INFO - ************ - - [24/May/2025 22:00:36] "GET /api/notifications/count HTTP/1.1" 200 -
2025-05-24 22:00:37,727 - werkzeug - INFO - ************ - - [24/May/2025 22:00:37] "GET /warehouses HTTP/1.1" 200 -
2025-05-24 22:00:37,796 - werkzeug - INFO - ************ - - [24/May/2025 22:00:37] "[36mGET /static/css/nobara-pro.css?v=1.0.0 HTTP/1.1[0m" 304 -
2025-05-24 22:00:37,802 - werkzeug - INFO - ************ - - [24/May/2025 22:00:37] "[33mGET /static/img/nobara-logo.svg HTTP/1.1[0m" 404 -
2025-05-24 22:00:37,805 - werkzeug - INFO - ************ - - [24/May/2025 22:00:37] "[36mGET /static/css/dashboard-pro.css?v=1.0.1 HTTP/1.1[0m" 304 -
2025-05-24 22:00:38,042 - werkzeug - INFO - ************ - - [24/May/2025 22:00:38] "[36mGET /static/js/error-logger.js?v=1.0.2 HTTP/1.1[0m" 304 -
2025-05-24 22:00:38,046 - werkzeug - INFO - ************ - - [24/May/2025 22:00:38] "[36mGET /static/js/notifications-system.js?v=1.0.2 HTTP/1.1[0m" 304 -
2025-05-24 22:00:38,058 - werkzeug - INFO - ************ - - [24/May/2025 22:00:38] "[36mGET /static/js/dashboard-manager.js?v=1.0.2 HTTP/1.1[0m" 304 -
2025-05-24 22:00:38,062 - werkzeug - INFO - ************ - - [24/May/2025 22:00:38] "[36mGET /static/js/system-manager.js?v=1.0.2 HTTP/1.1[0m" 304 -
2025-05-24 22:00:38,067 - werkzeug - INFO - ************ - - [24/May/2025 22:00:38] "[36mGET /static/js/dashboard-pro.js?v=1.0.2 HTTP/1.1[0m" 304 -
2025-05-24 22:00:38,136 - werkzeug - INFO - ************ - - [24/May/2025 22:00:38] "GET /api/notifications/count HTTP/1.1" 200 -
2025-05-24 22:00:51,059 - werkzeug - INFO - ************ - - [24/May/2025 22:00:51] "[32mPOST /warehouses/create HTTP/1.1[0m" 302 -
2025-05-24 22:00:51,371 - werkzeug - INFO - ************ - - [24/May/2025 22:00:51] "GET /warehouses HTTP/1.1" 200 -
2025-05-24 22:00:54,503 - nobara - WARNING - خطأ في جانب العميل: Console Error: Error: {}
2025-05-24 22:00:54,506 - werkzeug - INFO - ************ - - [24/May/2025 22:00:54] "POST /api/log-error HTTP/1.1" 200 -
2025-05-24 22:00:55,797 - werkzeug - INFO - ************ - - [24/May/2025 22:00:55] "[32mPOST /warehouses/create HTTP/1.1[0m" 302 -
2025-05-24 22:00:55,819 - werkzeug - INFO - ************ - - [24/May/2025 22:00:55] "GET /warehouses HTTP/1.1" 200 -
2025-05-24 22:00:57,167 - nobara - WARNING - خطأ في جانب العميل: Console Error: Error: {}
2025-05-24 22:00:57,170 - werkzeug - INFO - ************ - - [24/May/2025 22:00:57] "POST /api/log-error HTTP/1.1" 200 -
2025-05-24 22:01:15,588 - werkzeug - INFO - ************ - - [24/May/2025 22:01:15] "GET /warehouses HTTP/1.1" 200 -
2025-05-24 22:01:15,676 - werkzeug - INFO - ************ - - [24/May/2025 22:01:15] "[36mGET /static/css/nobara-pro.css?v=1.0.0 HTTP/1.1[0m" 304 -
2025-05-24 22:01:15,679 - werkzeug - INFO - ************ - - [24/May/2025 22:01:15] "[36mGET /static/css/dashboard-pro.css?v=1.0.1 HTTP/1.1[0m" 304 -
2025-05-24 22:01:15,683 - werkzeug - INFO - ************ - - [24/May/2025 22:01:15] "[33mGET /static/img/nobara-logo.svg HTTP/1.1[0m" 404 -
2025-05-24 22:01:16,001 - werkzeug - INFO - ************ - - [24/May/2025 22:01:16] "[36mGET /static/js/notifications-system.js?v=1.0.2 HTTP/1.1[0m" 304 -
2025-05-24 22:01:16,005 - werkzeug - INFO - ************ - - [24/May/2025 22:01:16] "[36mGET /static/js/error-logger.js?v=1.0.2 HTTP/1.1[0m" 304 -
2025-05-24 22:01:16,012 - werkzeug - INFO - ************ - - [24/May/2025 22:01:16] "[36mGET /static/js/system-manager.js?v=1.0.2 HTTP/1.1[0m" 304 -
2025-05-24 22:01:16,031 - werkzeug - INFO - ************ - - [24/May/2025 22:01:16] "[36mGET /static/js/dashboard-manager.js?v=1.0.2 HTTP/1.1[0m" 304 -
2025-05-24 22:01:16,035 - werkzeug - INFO - ************ - - [24/May/2025 22:01:16] "[36mGET /static/js/dashboard-pro.js?v=1.0.2 HTTP/1.1[0m" 304 -
2025-05-24 22:01:16,140 - werkzeug - INFO - ************ - - [24/May/2025 22:01:16] "GET /api/notifications/count HTTP/1.1" 200 -
2025-05-24 22:01:18,675 - werkzeug - INFO - ************ - - [24/May/2025 22:01:18] "GET /home HTTP/1.1" 200 -
2025-05-24 22:01:18,794 - werkzeug - INFO - ************ - - [24/May/2025 22:01:18] "[36mGET /static/css/nobara-pro.css?v=1.0.0 HTTP/1.1[0m" 304 -
2025-05-24 22:01:18,798 - werkzeug - INFO - ************ - - [24/May/2025 22:01:18] "[36mGET /static/css/dashboard-pro.css?v=1.0.1 HTTP/1.1[0m" 304 -
2025-05-24 22:01:18,800 - werkzeug - INFO - ************ - - [24/May/2025 22:01:18] "[36mGET /static/uploads/logo_20250518120852.jpeg HTTP/1.1[0m" 304 -
2025-05-24 22:01:19,323 - werkzeug - INFO - ************ - - [24/May/2025 22:01:19] "[36mGET /static/js/error-logger.js?v=1.0.2 HTTP/1.1[0m" 304 -
2025-05-24 22:01:19,328 - werkzeug - INFO - ************ - - [24/May/2025 22:01:19] "[36mGET /static/js/notifications-system.js?v=1.0.2 HTTP/1.1[0m" 304 -
2025-05-24 22:01:19,338 - werkzeug - INFO - ************ - - [24/May/2025 22:01:19] "[36mGET /static/js/system-manager.js?v=1.0.2 HTTP/1.1[0m" 304 -
2025-05-24 22:01:19,340 - werkzeug - INFO - ************ - - [24/May/2025 22:01:19] "[36mGET /static/js/dashboard-manager.js?v=1.0.2 HTTP/1.1[0m" 304 -
2025-05-24 22:01:19,349 - werkzeug - INFO - ************ - - [24/May/2025 22:01:19] "[36mGET /static/js/dashboard-pro.js?v=1.0.2 HTTP/1.1[0m" 304 -
2025-05-24 22:01:19,492 - werkzeug - INFO - ************ - - [24/May/2025 22:01:19] "GET /api/notifications/count HTTP/1.1" 200 -
2025-05-24 22:01:19,702 - werkzeug - INFO - ************ - - [24/May/2025 22:01:19] "GET /api/dashboard HTTP/1.1" 200 -
2025-05-24 22:01:31,666 - werkzeug - INFO - ************ - - [24/May/2025 22:01:31] "GET /pos HTTP/1.1" 200 -
2025-05-24 22:01:31,781 - werkzeug - INFO - ************ - - [24/May/2025 22:01:31] "[36mGET /static/css/nobara-pro.css?v=1.0.0 HTTP/1.1[0m" 304 -
2025-05-24 22:01:31,800 - werkzeug - INFO - ************ - - [24/May/2025 22:01:31] "[36mGET /static/css/dashboard-pro.css?v=1.0.1 HTTP/1.1[0m" 304 -
2025-05-24 22:01:31,822 - werkzeug - INFO - ************ - - [24/May/2025 22:01:31] "[36mGET /static/css/pos-theme.css?v=1.0.0 HTTP/1.1[0m" 304 -
2025-05-24 22:01:31,833 - werkzeug - INFO - ************ - - [24/May/2025 22:01:31] "[36mGET /static/js/pos.js HTTP/1.1[0m" 304 -
2025-05-24 22:01:31,852 - werkzeug - INFO - ************ - - [24/May/2025 22:01:31] "[33mGET /static/img/nobara-logo.svg HTTP/1.1[0m" 404 -
2025-05-24 22:01:31,871 - werkzeug - INFO - ************ - - [24/May/2025 22:01:31] "[36mGET /static/js/pos_part2.js HTTP/1.1[0m" 304 -
2025-05-24 22:01:31,879 - werkzeug - INFO - ************ - - [24/May/2025 22:01:31] "[36mGET /static/js/pos_part3.js HTTP/1.1[0m" 304 -
2025-05-24 22:01:31,884 - werkzeug - INFO - ************ - - [24/May/2025 22:01:31] "[36mGET /static/js/pos_part4.js HTTP/1.1[0m" 304 -
2025-05-24 22:01:31,903 - werkzeug - INFO - ************ - - [24/May/2025 22:01:31] "[36mGET /static/js/pos_credit.js HTTP/1.1[0m" 304 -
2025-05-24 22:01:31,911 - werkzeug - INFO - ************ - - [24/May/2025 22:01:31] "[36mGET /static/js/pos_returns.js HTTP/1.1[0m" 304 -
2025-05-24 22:01:31,917 - werkzeug - INFO - ************ - - [24/May/2025 22:01:31] "[36mGET /static/js/invoice-search.js HTTP/1.1[0m" 304 -
2025-05-24 22:01:31,939 - werkzeug - INFO - ************ - - [24/May/2025 22:01:31] "[36mGET /static/js/customer_search.js HTTP/1.1[0m" 304 -
2025-05-24 22:01:32,367 - werkzeug - INFO - ************ - - [24/May/2025 22:01:32] "GET /api/notifications/count HTTP/1.1" 200 -
2025-05-24 22:01:32,636 - werkzeug - INFO - ************ - - [24/May/2025 22:01:32] "GET /api/pos/products?warehouse_id=1 HTTP/1.1" 200 -
2025-05-24 22:01:34,941 - werkzeug - INFO - ************ - - [24/May/2025 22:01:34] "GET /products HTTP/1.1" 200 -
2025-05-24 22:01:35,041 - werkzeug - INFO - ************ - - [24/May/2025 22:01:35] "[33mGET /static/img/nobara-logo.svg HTTP/1.1[0m" 404 -
2025-05-24 22:01:35,483 - werkzeug - INFO - ************ - - [24/May/2025 22:01:35] "GET /api/notifications/count HTTP/1.1" 200 -
2025-05-24 22:01:39,108 - werkzeug - INFO - ************ - - [24/May/2025 22:01:39] "GET /sales HTTP/1.1" 200 -
2025-05-24 22:01:39,210 - werkzeug - INFO - ************ - - [24/May/2025 22:01:39] "[36mGET /static/js/sales_returns.js HTTP/1.1[0m" 304 -
2025-05-24 22:01:39,221 - werkzeug - INFO - ************ - - [24/May/2025 22:01:39] "[33mGET /static/img/nobara-logo.svg HTTP/1.1[0m" 404 -
2025-05-24 22:01:39,715 - werkzeug - INFO - ************ - - [24/May/2025 22:01:39] "GET /api/notifications/count HTTP/1.1" 200 -
2025-05-24 22:01:41,180 - werkzeug - INFO - ************ - - [24/May/2025 22:01:41] "GET /returns HTTP/1.1" 200 -
2025-05-24 22:01:41,269 - werkzeug - INFO - ************ - - [24/May/2025 22:01:41] "[33mGET /static/img/nobara-logo.svg HTTP/1.1[0m" 404 -
2025-05-24 22:01:41,649 - werkzeug - INFO - ************ - - [24/May/2025 22:01:41] "GET /api/notifications/count HTTP/1.1" 200 -
2025-05-24 22:01:43,474 - werkzeug - INFO - ************ - - [24/May/2025 22:01:43] "GET /purchases HTTP/1.1" 200 -
2025-05-24 22:01:43,555 - werkzeug - INFO - ************ - - [24/May/2025 22:01:43] "[33mGET /static/img/nobara-logo.svg HTTP/1.1[0m" 404 -
2025-05-24 22:01:43,898 - werkzeug - INFO - ************ - - [24/May/2025 22:01:43] "GET /api/notifications/count HTTP/1.1" 200 -
2025-05-24 22:01:45,269 - werkzeug - INFO - ************ - - [24/May/2025 22:01:45] "GET /customers HTTP/1.1" 200 -
2025-05-24 22:01:45,364 - werkzeug - INFO - ************ - - [24/May/2025 22:01:45] "[33mGET /static/img/nobara-logo.svg HTTP/1.1[0m" 404 -
2025-05-24 22:01:45,894 - werkzeug - INFO - ************ - - [24/May/2025 22:01:45] "GET /api/notifications/count HTTP/1.1" 200 -
2025-05-24 22:01:46,493 - werkzeug - INFO - ************ - - [24/May/2025 22:01:46] "GET /suppliers HTTP/1.1" 200 -
2025-05-24 22:01:46,579 - werkzeug - INFO - ************ - - [24/May/2025 22:01:46] "[33mGET /static/img/nobara-logo.svg HTTP/1.1[0m" 404 -
2025-05-24 22:01:47,165 - werkzeug - INFO - ************ - - [24/May/2025 22:01:47] "GET /api/notifications/count HTTP/1.1" 200 -
2025-05-24 22:02:17,482 - werkzeug - INFO - ************ - - [24/May/2025 22:02:17] "GET /suppliers HTTP/1.1" 200 -
2025-05-24 22:02:17,565 - werkzeug - INFO - ************ - - [24/May/2025 22:02:17] "[33mGET /static/img/nobara-logo.svg HTTP/1.1[0m" 404 -
2025-05-24 22:02:17,950 - werkzeug - INFO - ************ - - [24/May/2025 22:02:17] "GET /api/notifications/count HTTP/1.1" 200 -
2025-05-24 22:02:19,565 - werkzeug - INFO - ************ - - [24/May/2025 22:02:19] "GET /settings HTTP/1.1" 200 -
2025-05-24 22:02:19,665 - werkzeug - INFO - ************ - - [24/May/2025 22:02:19] "[36mGET /static/uploads/logo_20250518120852.jpeg HTTP/1.1[0m" 304 -
2025-05-24 22:02:20,107 - werkzeug - INFO - ************ - - [24/May/2025 22:02:20] "GET /api/notifications/count HTTP/1.1" 200 -
2025-05-24 22:02:22,146 - werkzeug - INFO - ************ - - [24/May/2025 22:02:22] "GET /settings/error-logs HTTP/1.1" 200 -
2025-05-24 22:02:22,224 - werkzeug - INFO - ************ - - [24/May/2025 22:02:22] "[36mGET /static/css/dashboard-pro.css?v=1.0.1 HTTP/1.1[0m" 304 -
2025-05-24 22:02:22,226 - werkzeug - INFO - ************ - - [24/May/2025 22:02:22] "[36mGET /static/css/nobara-pro.css?v=1.0.0 HTTP/1.1[0m" 304 -
2025-05-24 22:02:22,256 - werkzeug - INFO - ************ - - [24/May/2025 22:02:22] "[33mGET /static/img/nobara-logo.svg HTTP/1.1[0m" 404 -
2025-05-24 22:02:22,705 - werkzeug - INFO - ************ - - [24/May/2025 22:02:22] "[36mGET /static/js/error-logger.js?v=1.0.2 HTTP/1.1[0m" 304 -
2025-05-24 22:02:22,709 - werkzeug - INFO - ************ - - [24/May/2025 22:02:22] "[36mGET /static/js/notifications-system.js?v=1.0.2 HTTP/1.1[0m" 304 -
2025-05-24 22:02:22,717 - werkzeug - INFO - ************ - - [24/May/2025 22:02:22] "[36mGET /static/js/system-manager.js?v=1.0.2 HTTP/1.1[0m" 304 -
2025-05-24 22:02:22,738 - werkzeug - INFO - ************ - - [24/May/2025 22:02:22] "GET /static/js/dashboard-manager.js?v=1.0.2 HTTP/1.1" 200 -
2025-05-24 22:02:22,775 - werkzeug - INFO - ************ - - [24/May/2025 22:02:22] "[36mGET /static/js/dashboard-pro.js?v=1.0.2 HTTP/1.1[0m" 304 -
2025-05-24 22:02:22,869 - werkzeug - INFO - ************ - - [24/May/2025 22:02:22] "GET /api/notifications/count HTTP/1.1" 200 -
2025-05-24 22:02:25,620 - nobara - ERROR - استثناء: TemplateNotFound: warehouses/warehouses.html
الوقت: 2025-05-24 22:02:25
نوع الاستثناء: TemplateNotFound
الملف: app.py
السطر: 809
الدالة: handle_user_exception
معلومات الطلب: {
  "url": "http://************:5000/warehouses",
  "base_url": "http://************:5000/warehouses",
  "path": "/warehouses",
  "method": "GET",
  "endpoint": "warehouses.index",
  "remote_addr": "************",
  "user_agent": null,
  "referrer": "http://************:5000/settings/error-logs",
  "content_type": null,
  "content_length": null,
  "is_xhr": false,
  "is_secure": false,
  "timestamp": "2025-05-24 22:02:25",
  "headers": {
    "Host": "************:5000",
    "Connection": "keep-alive",
    "Upgrade-Insecure-Requests": "1",
    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********",
    "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7",
    "Referer": "http://************:5000/settings/error-logs",
    "Accept-Encoding": "gzip, deflate",
    "Accept-Language": "en-US,en;q=0.9",
    "Cookie": "NEXT_LOCALE=en; session=.eJzNUMtuxDAI_JWIcw7BD2zyK-1q5QDWVuop3pxW--91nEP7CZXQMDMwEuIF9_pd2sMarB8vmJ69QTtErDWY4fNYyJUTQ5yGiCd6GjyNAQ7O0x8rXInh2Ai44dC1lC8xuPzGnMLtPf-HI25zf8tu7QHrcz-sqy-FFZyWnFhDqdUSccIsLFy2VFU55eQl5F7Bu3hOq3NeaVsEkbk4slxrj1WMzLhxTOrJkEt2sixZCEtJKKoYPQlXocJZnSRSNaVivv_ifjTbr2sQ3j9nUX3N.aDIXZw.NRl-0k4Ix1b0W-v4eXFXfPaq2g4"
  },
  "args": {},
  "form": {},
  "cookies": {
    "NEXT_LOCALE": "en",
    "session": ".eJzNUMtuxDAI_JWIcw7BD2zyK-1q5QDWVuop3pxW--91nEP7CZXQMDMwEuIF9_pd2sMarB8vmJ69QTtErDWY4fNYyJUTQ5yGiCd6GjyNAQ7O0x8rXInh2Ai44dC1lC8xuPzGnMLtPf-HI25zf8tu7QHrcz-sqy-FFZyWnFhDqdUSccIsLFy2VFU55eQl5F7Bu3hOq3NeaVsEkbk4slxrj1WMzLhxTOrJkEt2sixZCEtJKKoYPQlXocJZnSRSNaVivv_ifjTbr2sQ3j9nUX3N.aDIXZw.NRl-0k4Ix1b0W-v4eXFXfPaq2g4"
  }
}
معلومات النظام: {
  "memory_usage": "94.89 MB",
  "cpu_percent": "0.0%",
  "memory_percent": "86.4%",
  "disk_usage": "46.7%",
  "open_files": 11,
  "threads": 5,
  "connections": 0,
  "uptime": "2:04:22"
}
تتبع الاستثناء:
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\app.py", line 917, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\app.py", line 902, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)  # type: ignore[no-any-return]
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask_login\utils.py", line 290, in decorated_view
    return current_app.ensure_sync(func)(*args, **kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\routes\warehouses.py", line 45, in index
    return render_template('warehouses/warehouses.html',
                          warehouses=warehouses,
    ...<2 lines>...
                          status=status,
                          stats=stats)
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\templating.py", line 149, in render_template
    template = app.jinja_env.get_or_select_template(template_name_or_list)
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\jinja2\environment.py", line 1087, in get_or_select_template
    return self.get_template(template_name_or_list, parent, globals)
           ~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\jinja2\environment.py", line 1016, in get_template
    return self._load_template(name, globals)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\jinja2\environment.py", line 975, in _load_template
    template = self.loader.load(self, name, self.make_globals(globals))
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\jinja2\loaders.py", line 126, in load
    source, filename, uptodate = self.get_source(environment, name)
                                 ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\templating.py", line 65, in get_source
    return self._get_source_fast(environment, template)
           ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\ملف البرنامج\PythonCashierSystem.v2 - Copy - Copy\PythonCashierSystem\.venv\Lib\site-packages\flask\templating.py", line 99, in _get_source_fast
    raise TemplateNotFound(template)
jinja2.exceptions.TemplateNotFound: warehouses/warehouses.html

2025-05-24 22:02:25,639 - werkzeug - INFO - ************ - - [24/May/2025 22:02:25] "[35m[1mGET /warehouses HTTP/1.1[0m" 500 -
2025-05-24 22:10:06,976 - werkzeug - INFO -  * Detected change in 'c:\\Users\\<USER>\\Desktop\\ملف البرنامج\\PythonCashierSystem.v2 - Copy - Copy\\PythonCashierSystem\\app\\models.py', reloading
2025-05-24 22:10:11,057 - werkzeug - INFO -  * Restarting with stat
