{% extends "base.html" %}

{% block title %}تقارير الإغلاق اليومي{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">تقارير الإغلاق اليومي</h3>
                    <div class="card-tools">
                        <a href="{{ url_for('cash.daily_closure') }}" class="btn btn-primary">
                            <i class="fas fa-plus"></i> إغلاق يومي جديد
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <!-- فلترة التقارير -->
                    <form method="GET" class="mb-4">
                        <div class="row">
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label for="register_id">الخزينة</label>
                                    <select class="form-control" id="register_id" name="register_id">
                                        <option value="">جميع الخزائن</option>
                                        {% for register in cash_registers %}
                                            <option value="{{ register.id }}" {{ 'selected' if selected_register|int == register.id }}>
                                                {{ register.name }}
                                            </option>
                                        {% endfor %}
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label for="date_from">من تاريخ</label>
                                    <input type="date" class="form-control" id="date_from" name="date_from" 
                                           value="{{ date_from }}">
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label for="date_to">إلى تاريخ</label>
                                    <input type="date" class="form-control" id="date_to" name="date_to" 
                                           value="{{ date_to }}">
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label for="status">الحالة</label>
                                    <select class="form-control" id="status" name="status">
                                        <option value="">جميع الحالات</option>
                                        <option value="pending" {{ 'selected' if selected_status == 'pending' }}>مفتوح</option>
                                        <option value="completed" {{ 'selected' if selected_status == 'completed' }}>مكتمل</option>
                                        <option value="cancelled" {{ 'selected' if selected_status == 'cancelled' }}>ملغي</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="row mt-3">
                            <div class="col-12">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-search"></i> بحث
                                </button>
                                <a href="{{ url_for('cash.closure_reports') }}" class="btn btn-secondary">
                                    <i class="fas fa-redo"></i> إعادة تعيين
                                </a>
                            </div>
                        </div>
                    </form>

                    <!-- جدول التقارير -->
                    <div class="table-responsive">
                        <table class="table table-bordered table-striped">
                            <thead>
                                <tr>
                                    <th>التاريخ</th>
                                    <th>الخزينة</th>
                                    <th>المستخدم</th>
                                    <th>الرصيد الافتتاحي</th>
                                    <th>الرصيد الختامي</th>
                                    <th>المبيعات</th>
                                    <th>المشتريات</th>
                                    <th>الإيداعات</th>
                                    <th>السحوبات</th>
                                    <th>الفرق</th>
                                    <th>الحالة</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for closure in closures %}
                                    <tr>
                                        <td>{{ closure.closure_date }}</td>
                                        <td>{{ closure.cash_register_name }}</td>
                                        <td>{{ closure.user_name }}</td>
                                        <td>{{ closure.opening_balance }}</td>
                                        <td>{{ closure.closing_balance if closure.status == 'completed' else '-' }}</td>
                                        <td>{{ closure.total_sales }}</td>
                                        <td>{{ closure.total_purchases }}</td>
                                        <td>{{ closure.total_deposits }}</td>
                                        <td>{{ closure.total_withdrawals }}</td>
                                        <td class="{{ 'text-danger' if closure.difference < 0 else 'text-success' }}">
                                            {{ closure.difference if closure.status == 'completed' else '-' }}
                                        </td>
                                        <td>
                                            {% if closure.status == 'pending' %}
                                                <span class="badge badge-warning">مفتوح</span>
                                            {% elif closure.status == 'completed' %}
                                                <span class="badge badge-success">مكتمل</span>
                                            {% else %}
                                                <span class="badge badge-danger">ملغي</span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            <a href="{{ url_for('cash.closure_details', closure_id=closure.id) }}" 
                                               class="btn btn-sm btn-info" title="عرض التفاصيل">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            {% if closure.status == 'pending' %}
                                                <a href="{{ url_for('cash.complete_closure', closure_id=closure.id) }}" 
                                                   class="btn btn-sm btn-success" title="إكمال الإغلاق">
                                                    <i class="fas fa-check"></i>
                                                </a>
                                            {% endif %}
                                        </td>
                                    </tr>
                                {% else %}
                                    <tr>
                                        <td colspan="12" class="text-center">لا توجد تقارير</td>
                                    </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // التحقق من صحة التواريخ
    const dateFrom = document.getElementById('date_from');
    const dateTo = document.getElementById('date_to');
    
    dateFrom.addEventListener('change', function() {
        if (dateTo.value && this.value > dateTo.value) {
            dateTo.value = this.value;
        }
    });
    
    dateTo.addEventListener('change', function() {
        if (dateFrom.value && this.value < dateFrom.value) {
            dateFrom.value = this.value;
        }
    });
});
</script>
{% endblock %} 