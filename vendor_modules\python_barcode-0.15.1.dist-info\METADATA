Metadata-Version: 2.1
Name: python-barcode
Version: 0.15.1
Summary: Create standard barcodes with Python. No external modules needed. (optional Pillow support included).
Home-page: https://github.com/WhyNotHugo/python-barcode
Author: <PERSON> et al
Author-email: <EMAIL>
License: MIT
Platform: UNKNOWN
Classifier: Development Status :: 5 - Production/Stable
Classifier: Environment :: Console
Classifier: Intended Audience :: Developers
Classifier: License :: OSI Approved :: MIT License
Classifier: Operating System :: OS Independent
Classifier: Programming Language :: Python
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3.7
Classifier: Programming Language :: Python :: 3.8
Classifier: Programming Language :: Python :: 3.9
Classifier: Programming Language :: Python :: 3.10
Classifier: Programming Language :: Python :: 3.11
Classifier: Topic :: Multimedia :: Graphics
Classifier: Topic :: Software Development :: Libraries :: Python Modules
Provides-Extra: images
Requires-Dist: pillow ; extra == 'images'

python-barcode
==============

.. image:: https://action-badges.now.sh/WhyNotHugo/python-barcode
  :target: https://github.com/WhyNotHugo/python-barcode/actions
  :alt: CI status

.. image:: https://readthedocs.org/projects/python-barcode/badge/
  :target: https://python-barcode.rtfd.org/
  :alt: documentation

.. image:: https://img.shields.io/pypi/v/python-barcode.svg
  :target: https://pypi.python.org/pypi/python-barcode
  :alt: version on pypi

.. image:: https://img.shields.io/pypi/dm/python-barcode.svg
  :target: https://pypi.python.org/pypi/python-barcode
  :alt: downloads

.. image:: https://img.shields.io/pypi/l/python-barcode.svg
  :target: https://github.com/WhyNotHugo/python-barcode/blob/main/LICENCE
  :alt: licence

**python-barcode** provides a simple way to create barcodes in Python.

There are no external dependencies when generating SVG files.
Pillow is required for generating images (e.g.: PNGs).

Support Python 3.7 to 3.11.

.. image:: example-ean13.png
  :target: https://github.com/WhyNotHugo/python-barcode
  :alt: python-barcode

Documentation
-------------

Full documentation is published at http://python-barcode.rtfd.io/

Licence
-------

python-barcode is licensed under the MIT licence. See LICENCE for details.


