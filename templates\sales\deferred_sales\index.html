<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>المبيعات الآجلة - نظام إدارة المبيعات</title>
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <!-- Remixicon -->
    <link href="https://cdn.jsdelivr.net/npm/remixicon@2.5.0/fonts/remixicon.css" rel="stylesheet">
    <style>
        .bg-pattern {
            background-color: #f9fafb;
            background-image: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23e5e7eb' fill-opacity='0.4'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
        }
        .glass-effect {
            background: rgba(255, 255, 255, 0.9);
            box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.07);
            backdrop-filter: blur(4px);
            -webkit-backdrop-filter: blur(4px);
            border: 1px solid rgba(255, 255, 255, 0.18);
            transition: all 0.3s ease;
        }
        .glass-effect:hover {
            box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.1);
        }
        .pulse-animation {
            position: relative;
        }
        .pulse-animation::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            border-radius: 0.5rem;
            box-shadow: 0 0 0 0 rgba(16, 185, 129, 0.7);
            animation: pulse 2s infinite;
        }
        @keyframes pulse {
            0% {
                transform: scale(1);
                box-shadow: 0 0 0 0 rgba(16, 185, 129, 0.7);
            }
            70% {
                transform: scale(1.05);
                box-shadow: 0 0 0 10px rgba(16, 185, 129, 0);
            }
            100% {
                transform: scale(1);
                box-shadow: 0 0 0 0 rgba(16, 185, 129, 0);
            }
        }
    </style>
</head>
<body class="bg-pattern min-h-screen">
    <div class="flex h-screen bg-gray-50">
        <!-- Sidebar -->
        {% include 'partials/sidebar.html' %}

        <!-- Main Content -->
        <div class="flex-1 overflow-x-hidden overflow-y-auto">
            <!-- Top Navigation -->
            {% include 'partials/topnav.html' %}

            <!-- Content Area -->
            <main class="flex-1 overflow-y-auto p-6">
                <div class="mb-6 flex flex-wrap items-center justify-between">
                    <div>
                        <h1 class="text-2xl font-bold text-gray-800">المبيعات الآجلة</h1>
                        <p class="text-gray-600">إدارة وعرض سجلات المبيعات الآجلة</p>
                    </div>
                    <div class="flex space-x-2 space-x-reverse">
                        <a href="{{ url_for('deferred_sales.reports') }}" class="bg-blue-600 text-white px-4 py-2 rounded-lg shadow-md hover:bg-blue-700 transition-all duration-300 flex items-center gap-2">
                            <i class="ri-bar-chart-line"></i>
                            <span>تقارير المبيعات الآجلة</span>
                        </a>
                        <a href="{{ url_for('sales.index') }}" class="bg-gray-200 text-gray-700 px-4 py-2 rounded-lg shadow-md hover:bg-gray-300 transition-all duration-300 flex items-center gap-2">
                            <i class="ri-arrow-right-line"></i>
                            <span>العودة للمبيعات</span>
                        </a>
                    </div>
                </div>

                <!-- Stats Cards -->
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
                    <div class="glass-effect rounded-lg p-5 transition-all duration-300 hover:shadow-lg">
                        <div class="flex justify-between">
                            <div>
                                <h3 class="text-gray-500 text-sm">إجمالي المبيعات الآجلة</h3>
                                <p class="text-2xl font-bold">{{ "%.2f"|format(stats.total_deferred_amount) }} ج.م</p>
                            </div>
                            <div class="w-12 h-12 rounded-full bg-blue-100 flex items-center justify-center text-blue-500">
                                <i class="ri-money-dollar-circle-line ri-lg"></i>
                            </div>
                        </div>
                        <div class="mt-4 text-sm">
                            <span class="text-blue-500 font-medium">{{ stats.total_deferred_orders }} طلب</span>
                        </div>
                    </div>

                    <div class="glass-effect rounded-lg p-5 transition-all duration-300 hover:shadow-lg">
                        <div class="flex justify-between">
                            <div>
                                <h3 class="text-gray-500 text-sm">إجمالي المدفوعات</h3>
                                <p class="text-2xl font-bold">{{ "%.2f"|format(stats.total_paid) }} ج.م</p>
                            </div>
                            <div class="w-12 h-12 rounded-full bg-green-100 flex items-center justify-center text-green-500">
                                <i class="ri-bank-card-line ri-lg"></i>
                            </div>
                        </div>
                        <div class="mt-4 text-sm">
                            <span class="text-green-500 font-medium">تم تحصيله</span>
                        </div>
                    </div>

                    <div class="glass-effect rounded-lg p-5 transition-all duration-300 hover:shadow-lg">
                        <div class="flex justify-between">
                            <div>
                                <h3 class="text-gray-500 text-sm">المبلغ المتبقي</h3>
                                <p class="text-2xl font-bold">{{ "%.2f"|format(stats.total_remaining) }} ج.م</p>
                            </div>
                            <div class="w-12 h-12 rounded-full bg-red-100 flex items-center justify-center text-red-500">
                                <i class="ri-refund-2-line ri-lg"></i>
                            </div>
                        </div>
                        <div class="mt-4 text-sm">
                            <span class="text-red-500 font-medium">متبقي للتحصيل</span>
                        </div>
                    </div>

                    <div class="glass-effect rounded-lg p-5 transition-all duration-300 hover:shadow-lg">
                        <div class="flex justify-between">
                            <div>
                                <h3 class="text-gray-500 text-sm">نسبة التحصيل</h3>
                                {% set collection_rate = (stats.total_paid / stats.total_deferred_amount * 100) if stats.total_deferred_amount > 0 else 0 %}
                                <p class="text-2xl font-bold">{{ "%.1f"|format(collection_rate) }}%</p>
                            </div>
                            <div class="w-12 h-12 rounded-full bg-purple-100 flex items-center justify-center text-purple-500">
                                <i class="ri-percent-line ri-lg"></i>
                            </div>
                        </div>
                        <div class="mt-4 text-sm">
                            <div class="w-full bg-gray-200 rounded-full h-2.5">
                                <div class="bg-purple-600 h-2.5 rounded-full" style="width: {{ collection_rate }}%"></div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Filters -->
                <div class="glass-effect rounded-lg p-4 mb-6">
                    <form action="{{ url_for('deferred_sales.index') }}" method="GET" class="grid grid-cols-1 md:grid-cols-4 gap-4">
                        <div>
                            <label for="search" class="block text-sm font-medium text-gray-700 mb-1">بحث</label>
                            <input type="text" id="search" name="search" value="{{ search }}" placeholder="رقم الفاتورة..." class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        </div>
                        <div>
                            <label for="customer" class="block text-sm font-medium text-gray-700 mb-1">العميل</label>
                            <select id="customer" name="customer" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <option value="">جميع العملاء</option>
                                {% for customer in customers %}
                                <option value="{{ customer.id }}" {% if customer_id|int == customer.id %}selected{% endif %}>{{ customer.name }}</option>
                                {% endfor %}
                            </select>
                        </div>
                        <div>
                            <label for="date_from" class="block text-sm font-medium text-gray-700 mb-1">من تاريخ</label>
                            <input type="date" id="date_from" name="date_from" value="{{ date_from }}" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        </div>
                        <div>
                            <label for="date_to" class="block text-sm font-medium text-gray-700 mb-1">إلى تاريخ</label>
                            <input type="date" id="date_to" name="date_to" value="{{ date_to }}" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        </div>
                        <div class="md:col-span-4 flex justify-end">
                            <button type="submit" class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-all duration-300">
                                <i class="ri-filter-3-line ml-1"></i>
                                تطبيق الفلتر
                            </button>
                            <a href="{{ url_for('deferred_sales.index') }}" class="mr-2 bg-gray-200 text-gray-700 px-4 py-2 rounded-md hover:bg-gray-300 transition-all duration-300">
                                <i class="ri-refresh-line ml-1"></i>
                                إعادة تعيين
                            </a>
                        </div>
                    </form>
                </div>

                <!-- Orders Table -->
                <div class="glass-effect rounded-lg overflow-hidden">
                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">رقم الفاتورة</th>
                                    <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">العميل</th>
                                    <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">التاريخ</th>
                                    <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الإجمالي</th>
                                    <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">المدفوع</th>
                                    <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">المتبقي</th>
                                    <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الحالة</th>
                                    <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                {% for order in orders.items %}
                                {% set paid_amount = order.to_dict().paid_amount %}
                                {% set remaining_amount = order.to_dict().remaining_amount %}
                                <tr class="hover:bg-gray-50">
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">{{ order.invoice_number }}</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ order.customer.name if order.customer else 'غير محدد' }}</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ order.created_at.strftime('%Y-%m-%d') }}</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ "%.2f"|format(order.total) }} ج.م</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-green-600">{{ "%.2f"|format(paid_amount) }} ج.م</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-red-600">{{ "%.2f"|format(remaining_amount) }} ج.م</td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-yellow-100 text-yellow-800">
                                            آجل
                                        </span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                        <a href="{{ url_for('deferred_sales.view', id=order.id) }}" class="text-indigo-600 hover:text-indigo-900 ml-3">
                                            <i class="ri-eye-line"></i>
                                            عرض
                                        </a>
                                    </td>
                                </tr>
                                {% else %}
                                <tr>
                                    <td colspan="8" class="px-6 py-4 text-center text-gray-500">لا توجد مبيعات آجلة</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    
                    <!-- Pagination -->
                    {% if orders.pages > 1 %}
                    <div class="px-6 py-4 bg-gray-50 border-t border-gray-200 flex items-center justify-between">
                        <div class="flex-1 flex justify-between sm:hidden">
                            {% if orders.has_prev %}
                            <a href="{{ url_for('deferred_sales.index', page=orders.prev_num, search=search, customer=customer_id, date_from=date_from, date_to=date_to) }}" class="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                                السابق
                            </a>
                            {% endif %}
                            {% if orders.has_next %}
                            <a href="{{ url_for('deferred_sales.index', page=orders.next_num, search=search, customer=customer_id, date_from=date_from, date_to=date_to) }}" class="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                                التالي
                            </a>
                            {% endif %}
                        </div>
                        <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                            <div>
                                <p class="text-sm text-gray-700">
                                    عرض
                                    <span class="font-medium">{{ orders.items|length }}</span>
                                    من أصل
                                    <span class="font-medium">{{ orders.total }}</span>
                                    نتيجة
                                </p>
                            </div>
                            <div>
                                <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                                    {% if orders.has_prev %}
                                    <a href="{{ url_for('deferred_sales.index', page=orders.prev_num, search=search, customer=customer_id, date_from=date_from, date_to=date_to) }}" class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                                        <span class="sr-only">السابق</span>
                                        <i class="ri-arrow-right-s-line"></i>
                                    </a>
                                    {% endif %}
                                    
                                    {% for page_num in orders.iter_pages(left_edge=1, right_edge=1, left_current=2, right_current=2) %}
                                        {% if page_num %}
                                            {% if page_num == orders.page %}
                                            <a href="#" class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-blue-50 text-sm font-medium text-blue-600 hover:bg-blue-100">
                                                {{ page_num }}
                                            </a>
                                            {% else %}
                                            <a href="{{ url_for('deferred_sales.index', page=page_num, search=search, customer=customer_id, date_from=date_from, date_to=date_to) }}" class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700 hover:bg-gray-50">
                                                {{ page_num }}
                                            </a>
                                            {% endif %}
                                        {% else %}
                                        <span class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700">
                                            ...
                                        </span>
                                        {% endif %}
                                    {% endfor %}
                                    
                                    {% if orders.has_next %}
                                    <a href="{{ url_for('deferred_sales.index', page=orders.next_num, search=search, customer=customer_id, date_from=date_from, date_to=date_to) }}" class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                                        <span class="sr-only">التالي</span>
                                        <i class="ri-arrow-left-s-line"></i>
                                    </a>
                                    {% endif %}
                                </nav>
                            </div>
                        </div>
                    </div>
                    {% endif %}
                </div>
            </main>
        </div>
    </div>
</body>
</html>
