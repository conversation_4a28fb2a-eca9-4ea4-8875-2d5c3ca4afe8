from flask import Blueprint, jsonify, request
from flask_login import login_required, current_user
from models import Inventory, Product, Warehouse, Category, InventoryMovement, db
from sqlalchemy import or_, and_
from datetime import datetime

inventory_api = Blueprint('inventory_api', __name__)

@inventory_api.route('/api/inventory', methods=['GET'])
@login_required
def get_inventory():
    """
    الحصول على بيانات المخزون مع دعم التصفية والترتيب والترقيم

    معلمات الاستعلام:
    - warehouse_id: معرف المخزن (اختياري)
    - search: نص البحث (اختياري)
    - status: حالة المخزون (all, in_stock, low_stock, out_of_stock)
    - category: معرف التصنيف (اختياري)
    - sort_by: حقل الترتيب (name, quantity, minimum_stock)
    - sort_order: ترتيب تصاعدي أو تنازلي (asc, desc)
    - page: رقم الصفحة (افتراضي: 1)
    - per_page: عدد العناصر في الصفحة (افتراضي: 20)
    """
    # الحصول على معلمات الاستعلام
    warehouse_id = request.args.get('warehouse_id', type=int)
    search = request.args.get('search', '')
    status = request.args.get('status', 'all')
    category_id = request.args.get('category', type=int)
    sort_by = request.args.get('sort_by', 'name')
    sort_order = request.args.get('sort_order', 'asc')
    page = request.args.get('page', 1, type=int)
    per_page = request.args.get('per_page', 20, type=int)

    # إعداد الاستعلام الأساسي
    query = Inventory.query

    # تطبيق فلتر المخزن
    if warehouse_id:
        query = query.filter_by(warehouse_id=warehouse_id)

    # تطبيق فلتر البحث
    if search:
        query = query.join(Product).filter(
            or_(
                Product.name.ilike(f'%{search}%'),
                Product.code.ilike(f'%{search}%')
            )
        )

    # تطبيق فلتر الحالة
    if status == 'in_stock':
        query = query.filter(Inventory.quantity > Inventory.minimum_stock)
    elif status == 'low_stock':
        query = query.filter(and_(
            Inventory.quantity > 0,
            Inventory.quantity <= Inventory.minimum_stock
        ))
    elif status == 'out_of_stock':
        query = query.filter(Inventory.quantity <= 0)

    # تطبيق فلتر التصنيف
    if category_id:
        query = query.join(Product).filter(Product.category_id == category_id)

    # تطبيق الترتيب
    if sort_by == 'name':
        query = query.join(Product)
        if sort_order == 'desc':
            query = query.order_by(Product.name.desc())
        else:
            query = query.order_by(Product.name.asc())
    elif sort_by == 'quantity':
        if sort_order == 'desc':
            query = query.order_by(Inventory.quantity.desc())
        else:
            query = query.order_by(Inventory.quantity.asc())
    elif sort_by == 'minimum_stock':
        if sort_order == 'desc':
            query = query.order_by(Inventory.minimum_stock.desc())
        else:
            query = query.order_by(Inventory.minimum_stock.asc())

    # حساب إجمالي العناصر للترقيم
    total_items = query.count()
    total_pages = (total_items + per_page - 1) // per_page  # تقريب لأعلى

    # تطبيق الترقيم
    query = query.paginate(page=page, per_page=per_page, error_out=False)

    # تحضير البيانات للاستجابة
    inventory_items = []
    for item in query.items:
        inventory_items.append({
            'id': item.id,
            'product': {
                'id': item.product.id,
                'name': item.product.name,
                'code': item.product.code,
                'category_id': item.product.category_id,
                'category_name': item.product.category.name if item.product.category else None
            },
            'warehouse': {
                'id': item.warehouse.id,
                'name': item.warehouse.name
            },
            'quantity': item.quantity,
            'minimum_stock': item.minimum_stock,
            'status': 'out_of_stock' if item.quantity <= 0 else ('low_stock' if item.quantity <= item.minimum_stock else 'in_stock')
        })

    # إعداد الاستجابة
    response = {
        'items': inventory_items,
        'page': page,
        'per_page': per_page,
        'total_items': total_items,
        'total_pages': total_pages
    }

    return jsonify(response)

@inventory_api.route('/api/inventory/stats', methods=['GET'])
@login_required
def get_inventory_stats():
    """
    الحصول على إحصائيات المخزون

    معلمات الاستعلام:
    - warehouse_id: معرف المخزن (اختياري)
    """
    # الحصول على معلمات الاستعلام
    warehouse_id = request.args.get('warehouse_id', type=int)

    # إعداد الاستعلام الأساسي
    query = Inventory.query

    # تطبيق فلتر المخزن
    if warehouse_id:
        query = query.filter_by(warehouse_id=warehouse_id)

    # حساب الإحصائيات
    total_items = query.count()
    in_stock = query.filter(Inventory.quantity > Inventory.minimum_stock).count()
    low_stock = query.filter(and_(
        Inventory.quantity > 0,
        Inventory.quantity <= Inventory.minimum_stock
    )).count()
    out_of_stock = query.filter(Inventory.quantity <= 0).count()

    # حساب قيمة المخزون
    inventory_value = 0
    for item in query.all():
        inventory_value += item.quantity * (item.product.price or 0)

    # إعداد الاستجابة
    response = {
        'total_items': total_items,
        'in_stock': in_stock,
        'low_stock': low_stock,
        'out_of_stock': out_of_stock,
        'inventory_value': round(inventory_value, 2)
    }

    return jsonify(response)

@inventory_api.route('/api/inventory/update', methods=['POST'])
@login_required
def update_inventory():
    """
    تحديث كمية المخزون لمنتج في مستودع محدد

    معلمات الطلب:
    - warehouse_id: معرف المستودع (مطلوب)
    - product_id: معرف المنتج (مطلوب)
    - quantity: الكمية الجديدة (مطلوب)
    - minimum_stock: الحد الأدنى للمخزون (اختياري)
    - notes: ملاحظات (اختياري)
    """
    try:
        data = request.json

        # التحقق من البيانات المطلوبة
        if not data or 'warehouse_id' not in data or 'product_id' not in data or 'quantity' not in data:
            return jsonify({
                'success': False,
                'message': 'يجب توفير معرف المستودع ومعرف المنتج والكمية'
            }), 400

        warehouse_id = data['warehouse_id']
        product_id = data['product_id']
        quantity = int(data['quantity'])
        minimum_stock = int(data.get('minimum_stock', 5))
        notes = data.get('notes', '')

        # التحقق من وجود المستودع والمنتج
        warehouse = Warehouse.query.get(warehouse_id)
        product = Product.query.get(product_id)

        if not warehouse:
            return jsonify({
                'success': False,
                'message': 'المستودع غير موجود'
            }), 404

        if not product:
            return jsonify({
                'success': False,
                'message': 'المنتج غير موجود'
            }), 404

        # البحث عن المخزون الحالي
        inventory = Inventory.query.filter_by(
            warehouse_id=warehouse_id,
            product_id=product_id
        ).first()

        if inventory:
            # تحديث المخزون الحالي وتسجيل الحركة
            movement_type = 'add' if quantity > inventory.quantity else 'remove'
            inventory.update_quantity(
                new_quantity=quantity,
                movement_type=movement_type,
                user_id=current_user.id,
                reference='api_update',
                notes=notes
            )
            inventory.minimum_stock = minimum_stock
        else:
            # إنشاء مخزون جديد
            inventory = Inventory(
                warehouse_id=warehouse_id,
                product_id=product_id,
                quantity=0,  # سيتم تحديثه لاحقاً
                minimum_stock=minimum_stock
            )
            db.session.add(inventory)
            db.session.flush()  # للحصول على معرف المخزون

            # تحديث الكمية وتسجيل الحركة
            inventory.update_quantity(
                new_quantity=quantity,
                movement_type='add',
                user_id=current_user.id,
                reference='initial_stock',
                notes=notes
            )

        db.session.commit()

        return jsonify({
            'success': True,
            'message': 'تم تحديث المخزون بنجاح',
            'inventory': {
                'id': inventory.id,
                'warehouse_id': inventory.warehouse_id,
                'warehouse_name': inventory.warehouse.name,
                'product_id': inventory.product_id,
                'product_name': inventory.product.name,
                'quantity': inventory.quantity,
                'minimum_stock': inventory.minimum_stock
            }
        })
    except Exception as e:
        db.session.rollback()
        return jsonify({
            'success': False,
            'message': f'حدث خطأ أثناء تحديث المخزون: {str(e)}'
        }), 500

@inventory_api.route('/api/inventory/transfer', methods=['POST'])
@login_required
def transfer_inventory():
    """
    نقل المخزون بين المستودعات

    معلمات الطلب:
    - source_warehouse_id: معرف المستودع المصدر (مطلوب)
    - target_warehouse_id: معرف المستودع الهدف (مطلوب)
    - product_id: معرف المنتج (مطلوب)
    - quantity: الكمية المراد نقلها (مطلوب)
    - notes: ملاحظات (اختياري)
    """
    try:
        data = request.json

        # التحقق من البيانات المطلوبة
        if not data or 'source_warehouse_id' not in data or 'target_warehouse_id' not in data or 'product_id' not in data or 'quantity' not in data:
            return jsonify({
                'success': False,
                'message': 'يجب توفير معرف المستودع المصدر ومعرف المستودع الهدف ومعرف المنتج والكمية'
            }), 400

        source_warehouse_id = data['source_warehouse_id']
        target_warehouse_id = data['target_warehouse_id']
        product_id = data['product_id']
        quantity = int(data['quantity'])
        notes = data.get('notes', '')

        # التحقق من أن المستودعين مختلفين
        if source_warehouse_id == target_warehouse_id:
            return jsonify({
                'success': False,
                'message': 'لا يمكن نقل المخزون إلى نفس المستودع'
            }), 400

        # التحقق من وجود المستودعين والمنتج
        source_warehouse = Warehouse.query.get(source_warehouse_id)
        target_warehouse = Warehouse.query.get(target_warehouse_id)
        product = Product.query.get(product_id)

        if not source_warehouse:
            return jsonify({
                'success': False,
                'message': 'المستودع المصدر غير موجود'
            }), 404

        if not target_warehouse:
            return jsonify({
                'success': False,
                'message': 'المستودع الهدف غير موجود'
            }), 404

        if not product:
            return jsonify({
                'success': False,
                'message': 'المنتج غير موجود'
            }), 404

        # التحقق من وجود المنتج في المستودع المصدر
        source_inventory = Inventory.query.filter_by(
            warehouse_id=source_warehouse_id,
            product_id=product_id
        ).first()

        if not source_inventory:
            return jsonify({
                'success': False,
                'message': 'المنتج غير موجود في المستودع المصدر'
            }), 404

        # التحقق من توفر الكمية المطلوبة
        if source_inventory.quantity < quantity:
            return jsonify({
                'success': False,
                'message': f'الكمية المتوفرة في المستودع المصدر ({source_inventory.quantity}) أقل من الكمية المطلوبة ({quantity})'
            }), 400

        # البحث عن المخزون في المستودع الهدف
        target_inventory = Inventory.query.filter_by(
            warehouse_id=target_warehouse_id,
            product_id=product_id
        ).first()

        # إنشاء مرجع للنقل
        transfer_reference = f"TRANSFER-{datetime.now().strftime('%Y%m%d%H%M%S')}"

        # خصم الكمية من المستودع المصدر
        new_source_quantity = source_inventory.quantity - quantity
        source_inventory.update_quantity(
            new_quantity=new_source_quantity,
            movement_type='transfer_out',
            user_id=current_user.id,
            reference=transfer_reference,
            notes=f'نقل إلى {target_warehouse.name}: {notes}'
        )

        # إضافة الكمية إلى المستودع الهدف
        if target_inventory:
            # تحديث المخزون الحالي وتسجيل الحركة
            new_target_quantity = target_inventory.quantity + quantity
            target_inventory.update_quantity(
                new_quantity=new_target_quantity,
                movement_type='transfer_in',
                user_id=current_user.id,
                reference=transfer_reference,
                notes=f'نقل من {source_warehouse.name}: {notes}'
            )
        else:
            # إنشاء مخزون جديد في المستودع الهدف
            target_inventory = Inventory(
                warehouse_id=target_warehouse_id,
                product_id=product_id,
                quantity=0,  # سيتم تحديثه لاحقاً
                minimum_stock=source_inventory.minimum_stock
            )
            db.session.add(target_inventory)
            db.session.flush()  # للحصول على معرف المخزون

            # تحديث الكمية وتسجيل الحركة
            target_inventory.update_quantity(
                new_quantity=quantity,
                movement_type='transfer_in',
                user_id=current_user.id,
                reference=transfer_reference,
                notes=f'نقل من {source_warehouse.name}: {notes}'
            )

        db.session.commit()

        return jsonify({
            'success': True,
            'message': 'تم نقل المخزون بنجاح',
            'source_inventory': {
                'id': source_inventory.id,
                'warehouse_id': source_inventory.warehouse_id,
                'warehouse_name': source_inventory.warehouse.name,
                'product_id': source_inventory.product_id,
                'product_name': source_inventory.product.name,
                'quantity': source_inventory.quantity
            },
            'target_inventory': {
                'id': target_inventory.id,
                'warehouse_id': target_inventory.warehouse_id,
                'warehouse_name': target_inventory.warehouse.name,
                'product_id': target_inventory.product_id,
                'product_name': target_inventory.product.name,
                'quantity': target_inventory.quantity
            }
        })
    except Exception as e:
        db.session.rollback()
        return jsonify({
            'success': False,
            'message': f'حدث خطأ أثناء نقل المخزون: {str(e)}'
        }), 500

@inventory_api.route('/api/inventory/movements', methods=['GET'])
@login_required
def get_inventory_movements():
    """
    الحصول على سجل حركة المخزون

    معلمات الاستعلام:
    - warehouse_id: معرف المستودع (اختياري)
    - product_id: معرف المنتج (اختياري)
    - movement_type: نوع الحركة (add, remove, transfer_in, transfer_out) (اختياري)
    - date_from: تاريخ البداية (اختياري)
    - date_to: تاريخ النهاية (اختياري)
    - page: رقم الصفحة (افتراضي: 1)
    - per_page: عدد العناصر في الصفحة (افتراضي: 20)
    """
    try:
        # الحصول على معلمات الاستعلام
        warehouse_id = request.args.get('warehouse_id', type=int)
        product_id = request.args.get('product_id', type=int)
        movement_type = request.args.get('movement_type', '')
        date_from = request.args.get('date_from', '')
        date_to = request.args.get('date_to', '')
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 20, type=int)

        # إعداد الاستعلام الأساسي
        query = InventoryMovement.query

        # تطبيق الفلاتر
        if warehouse_id:
            query = query.filter_by(warehouse_id=warehouse_id)

        if product_id:
            query = query.filter_by(product_id=product_id)

        if movement_type:
            query = query.filter_by(movement_type=movement_type)

        if date_from:
            try:
                from datetime import datetime
                date_from = datetime.strptime(date_from, '%Y-%m-%d')
                query = query.filter(InventoryMovement.created_at >= date_from)
            except:
                pass

        if date_to:
            try:
                from datetime import datetime, timedelta
                date_to = datetime.strptime(date_to, '%Y-%m-%d')
                date_to = date_to + timedelta(days=1)  # لتضمين اليوم المحدد كاملاً
                query = query.filter(InventoryMovement.created_at < date_to)
            except:
                pass

        # ترتيب النتائج
        query = query.order_by(InventoryMovement.created_at.desc())

        # تطبيق الترقيم
        pagination = query.paginate(page=page, per_page=per_page, error_out=False)

        # إعداد البيانات للاستجابة
        movements = []
        for movement in pagination.items:
            movements.append(movement.to_dict())

        # إعداد معلومات الترقيم
        pagination_info = {
            'page': pagination.page,
            'per_page': pagination.per_page,
            'total_pages': pagination.pages,
            'total_items': pagination.total
        }

        return jsonify({
            'success': True,
            'movements': movements,
            'pagination': pagination_info
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'حدث خطأ أثناء جلب سجل حركة المخزون: {str(e)}'
        }), 500

def register_inventory_api(app):
    """تسجيل واجهة برمجة التطبيقات للمخزون"""
    app.register_blueprint(inventory_api)
