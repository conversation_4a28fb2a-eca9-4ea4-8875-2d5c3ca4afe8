"""
سكريبت لتنفيذ عمليات هجرة قاعدة البيانات
"""

import os
import sys
import logging
import sqlite3
from datetime import datetime

# إعداد التسجيل
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler(os.path.join('logs', f'migrations_{datetime.now().strftime("%Y%m%d")}.log'), encoding='utf-8')
    ]
)
logger = logging.getLogger(__name__)

# التأكد من وجود مجلد السجلات
os.makedirs('logs', exist_ok=True)

def backup_database():
    """إنشاء نسخة احتياطية من قاعدة البيانات قبل تنفيذ الهجرة"""
    try:
        # التأكد من وجود مجلد النسخ الاحتياطية
        backup_dir = 'backups'
        os.makedirs(backup_dir, exist_ok=True)

        # مسار قاعدة البيانات الأصلية
        db_path = os.path.join('instance', 'fouad_pos.db')

        # التحقق من وجود قاعدة البيانات
        if not os.path.exists(db_path):
            logger.warning(f"قاعدة البيانات غير موجودة في المسار: {db_path}")
            return False

        # اسم ملف النسخة الاحتياطية
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        backup_filename = f"pre_migration_backup_{timestamp}.db"
        backup_path = os.path.join(backup_dir, backup_filename)

        # نسخ قاعدة البيانات
        import shutil
        shutil.copy2(db_path, backup_path)

        logger.info(f"تم إنشاء نسخة احتياطية من قاعدة البيانات في: {backup_path}")
        return True
    except Exception as e:
        logger.error(f"خطأ في إنشاء نسخة احتياطية من قاعدة البيانات: {str(e)}")
        return False

def execute_sql_migration(db_path, sql_statements):
    """تنفيذ عبارات SQL مباشرة على قاعدة البيانات"""
    try:
        # التحقق من وجود قاعدة البيانات
        if not os.path.exists(db_path):
            logger.warning(f"قاعدة البيانات غير موجودة في المسار: {db_path}")
            return False

        # الاتصال بقاعدة البيانات
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()

        # تنفيذ عبارات SQL
        for sql in sql_statements:
            try:
                cursor.execute(sql)
                logger.info(f"تم تنفيذ العبارة SQL: {sql[:50]}...")
            except sqlite3.Error as e:
                logger.warning(f"خطأ في تنفيذ العبارة SQL: {sql[:50]}... الخطأ: {str(e)}")

        # حفظ التغييرات
        conn.commit()

        # إغلاق الاتصال
        cursor.close()
        conn.close()

        logger.info(f"تم تنفيذ عبارات SQL على قاعدة البيانات {db_path} بنجاح")
        return True
    except Exception as e:
        logger.error(f"خطأ في تنفيذ عبارات SQL على قاعدة البيانات {db_path}: {str(e)}")
        return False

def run_direct_migrations():
    """تنفيذ عمليات الهجرة مباشرة باستخدام SQL"""
    try:
        # إنشاء نسخة احتياطية من قاعدة البيانات
        if not backup_database():
            logger.warning("فشل إنشاء نسخة احتياطية من قاعدة البيانات. هل ترغب في المتابعة؟ (y/n)")
            response = input().lower()
            if response != 'y':
                logger.info("تم إلغاء عملية الهجرة")
                return

        # مسار قاعدة البيانات
        db_path = os.path.join('instance', 'fouad_pos.db')

        # تنفيذ عمليات الهجرة
        logger.info("بدء تنفيذ عمليات الهجرة...")

        # إضافة الأعمدة المفقودة في جدول payment_method
        logger.info("إضافة الأعمدة المفقودة في جدول payment_method...")
        payment_method_statements = [
            "ALTER TABLE payment_method ADD COLUMN affects_cash_register BOOLEAN DEFAULT 1",
            "ALTER TABLE payment_method ADD COLUMN is_credit BOOLEAN DEFAULT 0",
            "ALTER TABLE payment_method ADD COLUMN allow_partial_payment BOOLEAN DEFAULT 1",
            "ALTER TABLE payment_method ADD COLUMN requires_approval BOOLEAN DEFAULT 0",
            "ALTER TABLE payment_method ADD COLUMN payment_category VARCHAR(20) DEFAULT 'cash'",
            "ALTER TABLE payment_method ADD COLUMN updated_at DATETIME DEFAULT CURRENT_TIMESTAMP"
        ]
        execute_sql_migration(db_path, payment_method_statements)

        # إضافة الأعمدة المفقودة في جدول user
        logger.info("إضافة الأعمدة المفقودة في جدول user...")
        user_statements = [
            "ALTER TABLE user ADD COLUMN phone VARCHAR(20)",
            "ALTER TABLE user ADD COLUMN position VARCHAR(64)",
            "ALTER TABLE user ADD COLUMN profile_image VARCHAR(256)",
            "ALTER TABLE user ADD COLUMN is_active BOOLEAN DEFAULT 1",
            "ALTER TABLE user ADD COLUMN last_login DATETIME"
        ]
        execute_sql_migration(db_path, user_statements)

        # إضافة الأعمدة المفقودة في جدول product
        logger.info("إضافة الأعمدة المفقودة في جدول product...")
        product_statements = [
            "ALTER TABLE product ADD COLUMN barcode VARCHAR(64)",
            "ALTER TABLE product ADD COLUMN tax_rate FLOAT DEFAULT 0",
            "ALTER TABLE product ADD COLUMN is_service BOOLEAN DEFAULT 0",
            "ALTER TABLE product ADD COLUMN updated_at DATETIME DEFAULT CURRENT_TIMESTAMP"
        ]
        execute_sql_migration(db_path, product_statements)

        # إضافة الفهارس لتحسين الأداء
        logger.info("إضافة الفهارس لتحسين الأداء...")
        index_statements = [
            "CREATE INDEX IF NOT EXISTS ix_product_name ON product (name)",
            "CREATE INDEX IF NOT EXISTS ix_order_created_at ON 'order' (created_at)",
            "CREATE INDEX IF NOT EXISTS ix_customer_name ON customer (name)",
            "CREATE INDEX IF NOT EXISTS ix_customer_phone ON customer (phone)"
        ]
        execute_sql_migration(db_path, index_statements)

        # إنشاء جدول دليل الحسابات (Chart of Accounts)
        logger.info("إنشاء جدول دليل الحسابات...")
        account_statements = [
            """
            CREATE TABLE IF NOT EXISTS account (
                id INTEGER PRIMARY KEY,
                code VARCHAR(20) NOT NULL UNIQUE,
                name VARCHAR(128) NOT NULL,
                description TEXT,
                account_type VARCHAR(20) NOT NULL,
                parent_id INTEGER,
                is_active BOOLEAN DEFAULT 1,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (parent_id) REFERENCES account (id)
            )
            """,
            "CREATE INDEX IF NOT EXISTS ix_account_account_type ON account (account_type)",
            "CREATE INDEX IF NOT EXISTS ix_account_parent_id ON account (parent_id)"
        ]
        execute_sql_migration(db_path, account_statements)

        # إنشاء جدول القيود المحاسبية (Journal Entries)
        logger.info("إنشاء جدول القيود المحاسبية...")
        journal_entry_statements = [
            """
            CREATE TABLE IF NOT EXISTS journal_entry (
                id INTEGER PRIMARY KEY,
                reference_number VARCHAR(20) NOT NULL UNIQUE,
                date DATE NOT NULL,
                description TEXT,
                status VARCHAR(20) NOT NULL DEFAULT 'draft',
                user_id INTEGER,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES user (id)
            )
            """,
            "CREATE INDEX IF NOT EXISTS ix_journal_entry_date ON journal_entry (date)",
            "CREATE INDEX IF NOT EXISTS ix_journal_entry_status ON journal_entry (status)"
        ]
        execute_sql_migration(db_path, journal_entry_statements)

        # إنشاء جدول تفاصيل القيود المحاسبية (Journal Entry Items)
        logger.info("إنشاء جدول تفاصيل القيود المحاسبية...")
        journal_entry_item_statements = [
            """
            CREATE TABLE IF NOT EXISTS journal_entry_item (
                id INTEGER PRIMARY KEY,
                journal_entry_id INTEGER NOT NULL,
                account_id INTEGER NOT NULL,
                description TEXT,
                debit FLOAT NOT NULL DEFAULT 0,
                credit FLOAT NOT NULL DEFAULT 0,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (journal_entry_id) REFERENCES journal_entry (id) ON DELETE CASCADE,
                FOREIGN KEY (account_id) REFERENCES account (id)
            )
            """,
            "CREATE INDEX IF NOT EXISTS ix_journal_entry_item_journal_entry_id ON journal_entry_item (journal_entry_id)",
            "CREATE INDEX IF NOT EXISTS ix_journal_entry_item_account_id ON journal_entry_item (account_id)"
        ]
        execute_sql_migration(db_path, journal_entry_item_statements)

        # إنشاء جدول أنواع الضرائب (Tax Types)
        logger.info("إنشاء جدول أنواع الضرائب...")
        tax_type_statements = [
            """
            CREATE TABLE IF NOT EXISTS tax_type (
                id INTEGER PRIMARY KEY,
                name VARCHAR(64) NOT NULL,
                code VARCHAR(20) NOT NULL UNIQUE,
                rate FLOAT NOT NULL,
                description TEXT,
                is_active BOOLEAN DEFAULT 1,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )
            """
        ]
        execute_sql_migration(db_path, tax_type_statements)

        # إنشاء جدول العملات (Currencies)
        logger.info("إنشاء جدول العملات...")
        currency_statements = [
            """
            CREATE TABLE IF NOT EXISTS currency (
                id INTEGER PRIMARY KEY,
                code VARCHAR(3) NOT NULL UNIQUE,
                name VARCHAR(64) NOT NULL,
                symbol VARCHAR(10),
                exchange_rate FLOAT NOT NULL DEFAULT 1,
                is_default BOOLEAN DEFAULT 0,
                is_active BOOLEAN DEFAULT 1,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )
            """,
            "CREATE INDEX IF NOT EXISTS ix_currency_is_default ON currency (is_default)"
        ]
        execute_sql_migration(db_path, currency_statements)

        # إنشاء جدول الفترات المالية (Fiscal Periods)
        logger.info("إنشاء جدول الفترات المالية...")
        fiscal_period_statements = [
            """
            CREATE TABLE IF NOT EXISTS fiscal_period (
                id INTEGER PRIMARY KEY,
                name VARCHAR(128) NOT NULL,
                start_date DATE NOT NULL,
                end_date DATE NOT NULL,
                status VARCHAR(20) NOT NULL DEFAULT 'open',
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )
            """,
            "CREATE INDEX IF NOT EXISTS ix_fiscal_period_status ON fiscal_period (status)"
        ]
        execute_sql_migration(db_path, fiscal_period_statements)

        # إنشاء جدول طرق الدفع المحاسبية (Accounting Payment Methods)
        logger.info("إنشاء جدول طرق الدفع المحاسبية...")
        accounting_payment_method_statements = [
            """
            CREATE TABLE IF NOT EXISTS accounting_payment_method (
                id INTEGER PRIMARY KEY,
                payment_method_id INTEGER NOT NULL,
                account_id INTEGER NOT NULL,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (payment_method_id) REFERENCES payment_method (id),
                FOREIGN KEY (account_id) REFERENCES account (id)
            )
            """
        ]
        execute_sql_migration(db_path, accounting_payment_method_statements)

        # إنشاء جدول الإعدادات المحاسبية (Accounting Settings)
        logger.info("إنشاء جدول الإعدادات المحاسبية...")
        accounting_settings_statements = [
            """
            CREATE TABLE IF NOT EXISTS accounting_settings (
                id INTEGER PRIMARY KEY,
                default_sales_account_id INTEGER,
                default_purchases_account_id INTEGER,
                default_inventory_account_id INTEGER,
                default_expenses_account_id INTEGER,
                default_tax_account_id INTEGER,
                default_cash_account_id INTEGER,
                default_bank_account_id INTEGER,
                default_accounts_receivable_account_id INTEGER,
                default_accounts_payable_account_id INTEGER,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (default_sales_account_id) REFERENCES account (id),
                FOREIGN KEY (default_purchases_account_id) REFERENCES account (id),
                FOREIGN KEY (default_inventory_account_id) REFERENCES account (id),
                FOREIGN KEY (default_expenses_account_id) REFERENCES account (id),
                FOREIGN KEY (default_tax_account_id) REFERENCES account (id),
                FOREIGN KEY (default_cash_account_id) REFERENCES account (id),
                FOREIGN KEY (default_bank_account_id) REFERENCES account (id),
                FOREIGN KEY (default_accounts_receivable_account_id) REFERENCES account (id),
                FOREIGN KEY (default_accounts_payable_account_id) REFERENCES account (id)
            )
            """
        ]
        execute_sql_migration(db_path, accounting_settings_statements)

        logger.info("تم تنفيذ جميع عمليات الهجرة بنجاح")
    except Exception as e:
        logger.error(f"خطأ في تنفيذ عمليات الهجرة: {str(e)}")
        raise

if __name__ == '__main__':
    # تنفيذ عمليات الهجرة
    run_direct_migrations()
