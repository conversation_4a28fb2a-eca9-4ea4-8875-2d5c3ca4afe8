<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>FoUaD - إدارة المنتجات</title>
    <script src="https://cdn.tailwindcss.com/3.4.16"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#3B82F6',
                        secondary: '#10b981',
                        dark: {
                            100: '#1E293B',
                            200: '#0F172A',
                            300: '#0B1120',
                            400: '#060A15'
                        },
                        accent: {
                            blue: '#3B82F6',
                            indigo: '#6366F1',
                            purple: '#8B5CF6',
                            green: '#10B981',
                            red: '#EF4444',
                            orange: '#F97316',
                            yellow: '#F59E0B'
                        }
                    },
                    borderRadius: {
                        'none': '0px',
                        'sm': '4px',
                        DEFAULT: '8px',
                        'md': '12px',
                        'lg': '16px',
                        'xl': '20px',
                        '2xl': '24px',
                        '3xl': '32px',
                        'full': '9999px',
                        'button': '8px'
                    }
                }
            },
            darkMode: 'class'
        }
    </script>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@400;500;700&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Pacifico&display=swap" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/remixicon@3.5.0/fonts/remixicon.css" rel="stylesheet">
    <style>
        :where([class^="ri-"])::before { content: "\f3c2"; }
        body {
            font-family: 'Tajawal', sans-serif;
            background-color: #f9fafb;
            transition: background-color 0.3s ease, color 0.3s ease;
        }
        body.dark {
            background-color: #0F172A;
            color: #E2E8F0;
        }
        .scrollbar-hide::-webkit-scrollbar {
            display: none;
        }
        .scrollbar-hide {
            -ms-overflow-style: none;
            scrollbar-width: none;
        }
        input[type="number"]::-webkit-inner-spin-button,
        input[type="number"]::-webkit-outer-spin-button {
            -webkit-appearance: none;
            margin: 0;
        }
        input[type="number"] {
            -moz-appearance: textfield;
        }
        .glass-effect {
            backdrop-filter: blur(8px);
            background: rgba(255, 255, 255, 0.8);
            border: 1px solid rgba(255, 255, 255, 0.3);
            box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.07);
            transition: all 0.3s ease;
        }
        .dark .glass-effect {
            background: rgba(30, 41, 59, 0.8);
            border: 1px solid rgba(30, 41, 59, 0.3);
            box-shadow: 0 8px 32px 0 rgba(0, 0, 0, 0.2);
        }
        .custom-checkbox {
            appearance: none;
            -webkit-appearance: none;
            width: 18px;
            height: 18px;
            border: 2px solid #d1d5db;
            border-radius: 4px;
            outline: none;
            cursor: pointer;
            position: relative;
            transition: all 0.2s;
        }
        .dark .custom-checkbox {
            border-color: #4B5563;
        }
        .custom-checkbox:checked {
            background-color: #3B82F6;
            border-color: #3B82F6;
        }
        .custom-checkbox:checked::before {
            content: '✓';
            position: absolute;
            color: white;
            font-size: 12px;
            font-weight: bold;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
        }
        .pulse-animation {
            animation: pulse 2s infinite;
        }
        @keyframes pulse {
            0% {
                box-shadow: 0 0 0 0 rgba(59, 130, 246, 0.4);
            }
            70% {
                box-shadow: 0 0 0 10px rgba(59, 130, 246, 0);
            }
            100% {
                box-shadow: 0 0 0 0 rgba(59, 130, 246, 0);
            }
        }
        .table-row-hover:hover {
            transform: translateY(-2px);
            transition: all 0.2s;
        }
        .bg-pattern {
            background-color: #f9fafb;
            background-image: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%233B82F6' fill-opacity='0.05'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
            transition: background-color 0.3s ease;
        }
        .dark .bg-pattern {
            background-color: #0F172A;
            background-image: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%233B82F6' fill-opacity='0.1'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
        }

        /* تحسينات إضافية للوضع الداكن */
        .dark .text-gray-800 { color: #E2E8F0; }
        .dark .text-gray-700 { color: #CBD5E1; }
        .dark .text-gray-600 { color: #94A3B8; }
        .dark .text-gray-500 { color: #64748B; }

        .dark .bg-white { background-color: #1E293B; }
        .dark .bg-gray-50 { background-color: #0F172A; }
        .dark .bg-gray-100 { background-color: #1E293B; }
        .dark .bg-gray-200 { background-color: #334155; }

        .dark .border-gray-200 { border-color: #334155; }
        .dark .border-gray-300 { border-color: #475569; }

        /* تأثيرات إضافية */
        .card-hover {
            transition: all 0.3s ease;
        }
        .card-hover:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px -5px rgba(59, 130, 246, 0.1), 0 10px 10px -5px rgba(59, 130, 246, 0.04);
        }
        .dark .card-hover:hover {
            box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.3), 0 10px 10px -5px rgba(0, 0, 0, 0.2);
        }

        /* زر تبديل الوضع الداكن */
        .theme-toggle {
            position: fixed;
            bottom: 20px;
            right: 20px;
            z-index: 50;
            width: 50px;
            height: 50px;
            border-radius: 50%;
            background-color: #3B82F6;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
            transition: all 0.3s ease;
        }
        .theme-toggle:hover {
            transform: scale(1.1);
        }
    </style>
</head>
<body class="bg-pattern">
    <div class="flex h-screen overflow-hidden">
        <!-- Sidebar -->
        {% include 'partials/sidebar.html' %}

        <!-- Main Content -->
        <div class="flex-1 flex flex-col overflow-hidden">
            <!-- Top Navigation -->
            {% include 'partials/topnav.html' %}

            <!-- Content Area -->
            <main class="flex-1 overflow-y-auto p-6">
                <div class="mb-8 flex flex-col md:flex-row md:items-center justify-between gap-4">
                    <div>
                        <h1 class="text-3xl font-bold text-gray-800 dark:text-white flex items-center">
                            <i class="ri-shopping-bag-3-fill text-primary dark:text-blue-400 ml-3"></i>
                            <span>إدارة المنتجات</span>
                        </h1>
                        <p class="text-gray-600 dark:text-gray-400 mt-2">عرض وإدارة قائمة المنتجات المتاحة في النظام</p>
                    </div>

                    <div class="flex flex-wrap gap-3 mt-4 md:mt-0">
                        <a href="{{ url_for('products.create') }}"
                           class="bg-gradient-to-r from-primary to-blue-600 dark:from-blue-600 dark:to-blue-700 text-white px-5 py-3 rounded-xl shadow-md hover:shadow-lg hover:shadow-blue-500/20 dark:hover:shadow-blue-800/20 transition-all duration-300 flex items-center gap-2 pulse-animation">
                            <i class="ri-add-line text-lg"></i>
                            <span class="font-medium">إضافة منتج جديد</span>
                        </a>

                        <div class="relative group">
                            <button type="button"
                                    class="bg-white dark:bg-dark-100 border border-gray-300 dark:border-gray-700 text-gray-700 dark:text-gray-300 px-5 py-3 rounded-xl shadow-sm hover:shadow-md transition-all duration-300 flex items-center gap-2">
                                <i class="ri-more-2-fill text-lg"></i>
                                <span class="font-medium">المزيد من الخيارات</span>
                                <i class="ri-arrow-down-s-line text-lg transition-transform duration-300 group-hover:rotate-180"></i>
                            </button>

                            <div class="absolute left-0 mt-2 w-64 bg-white dark:bg-dark-100 border border-gray-200 dark:border-gray-700 rounded-xl shadow-lg z-10 hidden group-hover:block transition-all duration-300 opacity-0 group-hover:opacity-100 transform group-hover:translate-y-0 translate-y-2">
                                <div class="p-2 space-y-1">
                                    <a href="{{ url_for('products.barcode') }}"
                                       class="flex items-center gap-3 px-4 py-3 text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-dark-200 rounded-lg transition-colors">
                                        <div class="w-8 h-8 rounded-lg bg-green-100 dark:bg-green-900/30 text-green-600 dark:text-green-400 flex items-center justify-center">
                                            <i class="ri-barcode-line"></i>
                                        </div>
                                        <div>
                                            <span class="font-medium block">طباعة الباركود</span>
                                            <span class="text-xs text-gray-500 dark:text-gray-400">طباعة باركود للمنتجات</span>
                                        </div>
                                    </a>

                                    <a href="{{ url_for('products.scale_barcode') }}"
                                       class="flex items-center gap-3 px-4 py-3 text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-dark-200 rounded-lg transition-colors">
                                        <div class="w-8 h-8 rounded-lg bg-orange-100 dark:bg-orange-900/30 text-orange-600 dark:text-orange-400 flex items-center justify-center">
                                            <i class="ri-scales-3-line"></i>
                                        </div>
                                        <div>
                                            <span class="font-medium block">ميزان الباركود</span>
                                            <span class="text-xs text-gray-500 dark:text-gray-400">إعدادات ميزان الباركود</span>
                                        </div>
                                    </a>

                                    <a href="{{ url_for('products.promotions') }}"
                                       class="flex items-center gap-3 px-4 py-3 text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-dark-200 rounded-lg transition-colors">
                                        <div class="w-8 h-8 rounded-lg bg-purple-100 dark:bg-purple-900/30 text-purple-600 dark:text-purple-400 flex items-center justify-center">
                                            <i class="ri-price-tag-3-line"></i>
                                        </div>
                                        <div>
                                            <span class="font-medium block">العروض والإجراءات</span>
                                            <span class="text-xs text-gray-500 dark:text-gray-400">إدارة العروض والخصومات</span>
                                        </div>
                                    </a>

                                    <a href="{{ url_for('products.categories') }}"
                                       class="flex items-center gap-3 px-4 py-3 text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-dark-200 rounded-lg transition-colors">
                                        <div class="w-8 h-8 rounded-lg bg-blue-100 dark:bg-blue-900/30 text-blue-600 dark:text-blue-400 flex items-center justify-center">
                                            <i class="ri-folder-line"></i>
                                        </div>
                                        <div>
                                            <span class="font-medium block">التصنيفات</span>
                                            <span class="text-xs text-gray-500 dark:text-gray-400">إدارة تصنيفات المنتجات</span>
                                        </div>
                                    </a>

                                    <a href="{{ url_for('products.import_export') }}"
                                       class="flex items-center gap-3 px-4 py-3 text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-dark-200 rounded-lg transition-colors">
                                        <div class="w-8 h-8 rounded-lg bg-teal-100 dark:bg-teal-900/30 text-teal-600 dark:text-teal-400 flex items-center justify-center">
                                            <i class="ri-file-transfer-line"></i>
                                        </div>
                                        <div>
                                            <span class="font-medium block">استيراد / تصدير</span>
                                            <span class="text-xs text-gray-500 dark:text-gray-400">استيراد وتصدير المنتجات</span>
                                        </div>
                                    </a>
                                </div>
                            </div>
                        </div>

                        <a href="{{ url_for('products.barcode') }}"
                           class="bg-gradient-to-r from-green-500 to-green-600 dark:from-green-600 dark:to-green-700 text-white px-4 py-3 rounded-xl shadow-md hover:shadow-lg hover:shadow-green-500/20 dark:hover:shadow-green-800/20 transition-all duration-300 flex items-center gap-2 md:hidden">
                            <i class="ri-barcode-line text-lg"></i>
                            <span class="font-medium">طباعة الباركود</span>
                        </a>
                    </div>
                </div>

                <!-- Stats Cards -->
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
                    <div class="glass-effect rounded-xl p-5 transition-all duration-300 hover:shadow-lg card-hover border border-gray-200 dark:border-gray-700">
                        <div class="flex justify-between items-center">
                            <div>
                                <h3 class="text-gray-500 dark:text-gray-400 text-sm font-medium mb-1">إجمالي المنتجات</h3>
                                <p class="text-3xl font-bold text-gray-800 dark:text-white">{{ stats.total_products }}</p>
                            </div>
                            <div class="w-14 h-14 rounded-full bg-blue-100 dark:bg-blue-900/30 flex items-center justify-center text-primary dark:text-blue-400 shadow-sm">
                                <i class="ri-shopping-bag-line text-2xl"></i>
                            </div>
                        </div>
                        <div class="mt-4">
                            <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2.5">
                                <div class="bg-primary h-2.5 rounded-full" style="width: {{ (stats.active_products / stats.total_products * 100)|round|int if stats.total_products > 0 else 0 }}%"></div>
                            </div>
                            <div class="flex justify-between mt-2 text-sm">
                                <span class="text-green-500 dark:text-green-400 font-medium">{{ stats.active_products }} نشط</span>
                                <span class="text-gray-500 dark:text-gray-400">{{ (stats.active_products / stats.total_products * 100)|round|int if stats.total_products > 0 else 0 }}٪ من الإجمالي</span>
                            </div>
                        </div>
                    </div>

                    <div class="glass-effect rounded-xl p-5 transition-all duration-300 hover:shadow-lg card-hover border border-gray-200 dark:border-gray-700">
                        <div class="flex justify-between items-center">
                            <div>
                                <h3 class="text-gray-500 dark:text-gray-400 text-sm font-medium mb-1">المنتجات النشطة</h3>
                                <p class="text-3xl font-bold text-gray-800 dark:text-white">{{ stats.active_products }}</p>
                            </div>
                            <div class="w-14 h-14 rounded-full bg-green-100 dark:bg-green-900/30 flex items-center justify-center text-green-500 dark:text-green-400 shadow-sm">
                                <i class="ri-check-double-line text-2xl"></i>
                            </div>
                        </div>
                        <div class="mt-4">
                            <div class="flex items-center">
                                <div class="flex-1 h-1 bg-gray-200 dark:bg-gray-700 rounded-full mx-2">
                                    <div class="h-1 bg-green-500 dark:bg-green-400 rounded-full" style="width: {{ (stats.active_products / stats.total_products * 100)|round|int if stats.total_products > 0 else 0 }}%"></div>
                                </div>
                                <span class="text-sm font-medium text-green-500 dark:text-green-400 min-w-[40px] text-center">{{ (stats.active_products / stats.total_products * 100)|round|int if stats.total_products > 0 else 0 }}٪</span>
                            </div>
                            <p class="mt-2 text-sm text-gray-500 dark:text-gray-400">منتجات متاحة للبيع</p>
                        </div>
                    </div>

                    <div class="glass-effect rounded-xl p-5 transition-all duration-300 hover:shadow-lg card-hover border border-gray-200 dark:border-gray-700">
                        <div class="flex justify-between items-center">
                            <div>
                                <h3 class="text-gray-500 dark:text-gray-400 text-sm font-medium mb-1">منخفضة المخزون</h3>
                                <p class="text-3xl font-bold text-gray-800 dark:text-white">{{ stats.low_stock_products }}</p>
                            </div>
                            <div class="w-14 h-14 rounded-full bg-orange-100 dark:bg-orange-900/30 flex items-center justify-center text-orange-500 dark:text-orange-400 shadow-sm">
                                <i class="ri-alert-line text-2xl"></i>
                            </div>
                        </div>
                        <div class="mt-4">
                            {% if stats.low_stock_alerts %}
                            <div class="space-y-2">
                                {% for product in stats.low_stock_alerts[:2] %}
                                <div class="flex items-center justify-between text-sm">
                                    <span class="truncate max-w-[150px]">{{ product.name }}</span>
                                    <span class="text-orange-500 dark:text-orange-400 font-medium">{{ product.stock_quantity }} قطعة</span>
                                </div>
                                {% endfor %}
                            </div>
                            {% else %}
                            <p class="text-sm text-gray-500 dark:text-gray-400">لا توجد منتجات منخفضة المخزون</p>
                            {% endif %}
                        </div>
                    </div>

                    <div class="glass-effect rounded-xl p-5 transition-all duration-300 hover:shadow-lg card-hover border border-gray-200 dark:border-gray-700">
                        <div class="flex justify-between items-center">
                            <div>
                                <h3 class="text-gray-500 dark:text-gray-400 text-sm font-medium mb-1">التصنيفات</h3>
                                <p class="text-3xl font-bold text-gray-800 dark:text-white">{{ stats.category_count }}</p>
                            </div>
                            <div class="w-14 h-14 rounded-full bg-purple-100 dark:bg-purple-900/30 flex items-center justify-center text-purple-500 dark:text-purple-400 shadow-sm">
                                <i class="ri-price-tag-3-line text-2xl"></i>
                            </div>
                        </div>
                        <div class="mt-4">
                            <a href="{{ url_for('products.categories') }}" class="flex items-center justify-center py-2 px-4 bg-primary/10 dark:bg-primary/20 text-primary dark:text-blue-400 rounded-lg font-medium text-sm hover:bg-primary/20 dark:hover:bg-primary/30 transition-colors">
                                <i class="ri-settings-line ml-1"></i>
                                <span>إدارة التصنيفات</span>
                            </a>
                        </div>
                    </div>
                </div>

                <!-- Filters & Search -->
                <div class="glass-effect rounded-xl p-6 mb-6 border border-gray-200 dark:border-gray-700">
                    <div class="flex items-center justify-between mb-4">
                        <h3 class="text-lg font-bold text-gray-800 dark:text-white flex items-center">
                            <i class="ri-filter-3-line ml-2 text-primary dark:text-blue-400"></i>
                            <span>فلترة المنتجات</span>
                        </h3>
                        <a href="{{ url_for('products.index') }}" class="text-sm text-gray-500 dark:text-gray-400 hover:text-primary dark:hover:text-blue-400 transition-colors flex items-center">
                            <i class="ri-refresh-line ml-1"></i>
                            <span>إعادة ضبط الفلاتر</span>
                        </a>
                    </div>

                    <form method="GET" action="{{ url_for('products.index') }}" class="grid grid-cols-1 md:grid-cols-4 gap-5">
                        <div class="relative">
                            <label for="search" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">بحث</label>
                            <div class="relative">
                                <div class="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none text-gray-400 dark:text-gray-500">
                                    <i class="ri-search-line"></i>
                                </div>
                                <input type="text" id="search" name="search" value="{{ search }}"
                                    placeholder="اسم المنتج أو الكود..."
                                    class="w-full pr-10 px-4 py-2.5 bg-white dark:bg-dark-100 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary dark:focus:ring-blue-500 focus:border-transparent transition-all text-sm">
                            </div>
                        </div>

                        <div>
                            <label for="category" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">التصنيف</label>
                            <div class="relative">
                                <select id="category" name="category"
                                    class="w-full px-4 py-2.5 bg-white dark:bg-dark-100 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary dark:focus:ring-blue-500 focus:border-transparent transition-all text-sm appearance-none">
                                    <option value="">كل التصنيفات</option>
                                    {% for category in categories %}
                                    <option value="{{ category.id }}" {% if category_id|int == category.id %}selected{% endif %}>{{ category.name }}</option>
                                    {% endfor %}
                                </select>
                                <div class="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none text-gray-400 dark:text-gray-500">
                                    <i class="ri-arrow-down-s-line"></i>
                                </div>
                            </div>
                        </div>

                        <div>
                            <label for="status" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">الحالة</label>
                            <div class="relative">
                                <select id="status" name="status"
                                    class="w-full px-4 py-2.5 bg-white dark:bg-dark-100 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary dark:focus:ring-blue-500 focus:border-transparent transition-all text-sm appearance-none">
                                    <option value="">جميع الحالات</option>
                                    <option value="active" {% if status == 'active' %}selected{% endif %}>نشط</option>
                                    <option value="inactive" {% if status == 'inactive' %}selected{% endif %}>غير نشط</option>
                                </select>
                                <div class="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none text-gray-400 dark:text-gray-500">
                                    <i class="ri-arrow-down-s-line"></i>
                                </div>
                            </div>
                        </div>

                        <div class="flex items-end">
                            <button type="submit" class="w-full px-4 py-2.5 bg-gradient-to-r from-primary to-blue-600 dark:from-blue-600 dark:to-blue-700 text-white rounded-lg hover:shadow-lg focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary dark:focus:ring-blue-500 transition-all text-sm font-medium flex items-center justify-center">
                                <i class="ri-filter-line ml-1"></i>
                                <span>تطبيق الفلاتر</span>
                            </button>
                        </div>
                    </form>
                </div>

                <!-- Products Table -->
                <div class="glass-effect rounded-xl overflow-hidden border border-gray-200 dark:border-gray-700">
                    <div class="p-4 bg-gray-50 dark:bg-dark-200 border-b border-gray-200 dark:border-gray-700 flex justify-between items-center">
                        <h3 class="text-lg font-bold text-gray-800 dark:text-white flex items-center">
                            <i class="ri-shopping-bag-3-line ml-2 text-primary dark:text-blue-400"></i>
                            <span>قائمة المنتجات</span>
                        </h3>
                        <div class="text-sm text-gray-500 dark:text-gray-400">
                            إجمالي: <span class="font-medium text-gray-800 dark:text-white">{{ products.total }}</span> منتج
                        </div>
                    </div>

                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                            <thead class="bg-gray-50 dark:bg-dark-200">
                                <tr>
                                    <th scope="col" class="px-6 py-4 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">المنتج</th>
                                    <th scope="col" class="px-6 py-4 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">الكود</th>
                                    <th scope="col" class="px-6 py-4 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">التصنيف</th>
                                    <th scope="col" class="px-6 py-4 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">السعر</th>
                                    <th scope="col" class="px-6 py-4 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">المخزون</th>
                                    <th scope="col" class="px-6 py-4 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">الحالة</th>
                                    <th scope="col" class="px-6 py-4 text-center text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white dark:bg-dark-100 divide-y divide-gray-200 dark:divide-gray-700">
                                {% for product in products.items %}
                                <tr class="hover:bg-gray-50 dark:hover:bg-dark-200 transition-colors duration-150">
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="flex items-center">
                                            <div class="flex-shrink-0 h-12 w-12 rounded-lg bg-gray-100 dark:bg-dark-300 flex items-center justify-center text-gray-500 dark:text-gray-400 overflow-hidden border border-gray-200 dark:border-gray-600">
                                                {% if product.image_path %}
                                                <img src="{{ product.image_path }}" alt="{{ product.name }}" class="h-12 w-12 rounded-lg object-cover">
                                                {% else %}
                                                <i class="ri-shopping-bag-line text-xl"></i>
                                                {% endif %}
                                            </div>
                                            <div class="mr-4">
                                                <div class="text-sm font-medium text-gray-900 dark:text-white">{{ product.name }}</div>
                                                <div class="text-xs text-gray-500 dark:text-gray-400 mt-0.5">{{ product.brand or 'بدون ماركة' }}</div>
                                            </div>
                                        </div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm text-gray-900 dark:text-white font-mono">{{ product.code or '-' }}</div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        {% if product.category %}
                                        <span class="px-3 py-1.5 text-xs rounded-full inline-flex items-center
                                            {{ 'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300' if product.category.color == 'blue' else
                                               'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300' if product.category.color == 'green' else
                                               'bg-purple-100 text-purple-800 dark:bg-purple-900/30 dark:text-purple-300' if product.category.color == 'purple' else
                                               'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300' if product.category.color == 'red' else
                                               'bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-300' }}">
                                            <i class="ri-price-tag-3-line ml-1 text-xs"></i>
                                            {{ product.category.name }}
                                        </span>
                                        {% else %}
                                        <span class="text-gray-500 dark:text-gray-400 text-sm">بدون تصنيف</span>
                                        {% endif %}
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="font-medium text-gray-900 dark:text-white">{{ product.price }} ج.م</div>
                                        {% if product.cost_price %}
                                        <div class="text-xs text-gray-500 dark:text-gray-400 mt-0.5">
                                            <span class="inline-flex items-center">
                                                <i class="ri-money-dollar-circle-line ml-0.5"></i>
                                                التكلفة: {{ product.cost_price }} ج.م
                                            </span>
                                        </div>
                                        {% endif %}
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        {% if product.get_stock_status() == 'out_of_stock' %}
                                        <div class="flex flex-col">
                                            <span class="flex items-center text-red-600 dark:text-red-400 text-sm font-medium">
                                                <span class="w-2 h-2 bg-red-600 dark:bg-red-400 rounded-full mr-1.5 animate-pulse"></span>
                                                نفذ المخزون
                                            </span>
                                            <div class="mt-1 w-full bg-gray-200 dark:bg-gray-700 rounded-full h-1.5">
                                                <div class="bg-red-600 dark:bg-red-500 h-1.5 rounded-full" style="width: 0%"></div>
                                            </div>
                                        </div>
                                        {% elif product.get_stock_status() == 'low_stock' %}
                                        <div class="flex flex-col">
                                            <span class="flex items-center text-orange-600 dark:text-orange-400 text-sm font-medium">
                                                <span class="w-2 h-2 bg-orange-600 dark:bg-orange-400 rounded-full mr-1.5"></span>
                                                {{ product.stock_quantity }} قطعة (منخفض)
                                            </span>
                                            <div class="mt-1 w-full bg-gray-200 dark:bg-gray-700 rounded-full h-1.5">
                                                <div class="bg-orange-500 dark:bg-orange-400 h-1.5 rounded-full" style="width: {{ (product.stock_quantity / product.minimum_stock * 50)|round|int if product.minimum_stock > 0 else 0 }}%"></div>
                                            </div>
                                        </div>
                                        {% else %}
                                        <div class="flex flex-col">
                                            <span class="flex items-center text-green-600 dark:text-green-400 text-sm font-medium">
                                                <span class="w-2 h-2 bg-green-600 dark:bg-green-400 rounded-full mr-1.5"></span>
                                                {{ product.stock_quantity }} قطعة
                                            </span>
                                            <div class="mt-1 w-full bg-gray-200 dark:bg-gray-700 rounded-full h-1.5">
                                                <div class="bg-green-500 dark:bg-green-400 h-1.5 rounded-full" style="width: 100%"></div>
                                            </div>
                                        </div>
                                        {% endif %}
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        {% if product.is_active %}
                                        <span class="px-3 py-1.5 text-xs rounded-full bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-300 inline-flex items-center">
                                            <i class="ri-checkbox-circle-line ml-1"></i>
                                            نشط
                                        </span>
                                        {% else %}
                                        <span class="px-3 py-1.5 text-xs rounded-full bg-gray-100 dark:bg-gray-800 text-gray-800 dark:text-gray-300 inline-flex items-center">
                                            <i class="ri-stop-circle-line ml-1"></i>
                                            غير نشط
                                        </span>
                                        {% endif %}
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-center">
                                        <div class="flex justify-center space-x-1 space-x-reverse">
                                            <a href="{{ url_for('products.edit', id=product.id) }}"
                                               class="text-primary dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300 p-1.5 rounded-lg hover:bg-blue-50 dark:hover:bg-blue-900/30 transition-all tooltip"
                                               data-tooltip="تعديل">
                                                <i class="ri-edit-line text-lg"></i>
                                            </a>

                                            <button type="button" onclick="confirmDelete({{ product.id }}, '{{ product.name }}')"
                                                    class="text-red-600 dark:text-red-400 hover:text-red-700 dark:hover:text-red-300 p-1.5 rounded-lg hover:bg-red-50 dark:hover:bg-red-900/30 transition-all tooltip"
                                                    data-tooltip="حذف">
                                                <i class="ri-delete-bin-line text-lg"></i>
                                            </button>

                                            <a href="{{ url_for('products.barcode') }}?product_id={{ product.id }}"
                                               class="text-green-600 dark:text-green-400 hover:text-green-700 dark:hover:text-green-300 p-1.5 rounded-lg hover:bg-green-50 dark:hover:bg-green-900/30 transition-all tooltip"
                                               data-tooltip="طباعة الباركود">
                                                <i class="ri-barcode-line text-lg"></i>
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                                {% else %}
                                <tr>
                                    <td colspan="7" class="px-6 py-12 text-center">
                                        <div class="flex flex-col items-center">
                                            <div class="w-20 h-20 rounded-full bg-gray-100 dark:bg-dark-300 flex items-center justify-center text-gray-400 dark:text-gray-500 mb-4">
                                                <i class="ri-search-line text-4xl"></i>
                                            </div>
                                            <p class="text-gray-600 dark:text-gray-300 mb-2 text-lg">لا توجد منتجات مطابقة</p>
                                            <p class="text-gray-500 dark:text-gray-400 text-sm">حاول استخدام معايير بحث مختلفة أو <a href="{{ url_for('products.index') }}" class="text-primary dark:text-blue-400 hover:underline">عرض جميع المنتجات</a></p>
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    {% if products.pages > 1 %}
                    <div class="px-6 py-4 bg-gray-50 dark:bg-dark-200 border-t border-gray-200 dark:border-gray-700 flex flex-col sm:flex-row items-center justify-between gap-4">
                        <div class="text-sm text-gray-500 dark:text-gray-400 flex items-center">
                            <span class="inline-flex items-center">
                                <i class="ri-file-list-line ml-1.5"></i>
                                عرض <span class="font-medium text-gray-700 dark:text-gray-300 mx-1">{{ products.items|length }}</span> من
                                <span class="font-medium text-gray-700 dark:text-gray-300 mx-1">{{ products.total }}</span> منتج
                            </span>
                            <span class="mx-2 text-gray-300 dark:text-gray-600">|</span>
                            <span class="inline-flex items-center">
                                <i class="ri-pages-line ml-1.5"></i>
                                صفحة <span class="font-medium text-gray-700 dark:text-gray-300 mx-1">{{ products.page }}</span> من
                                <span class="font-medium text-gray-700 dark:text-gray-300 mx-1">{{ products.pages }}</span>
                            </span>
                        </div>

                        <div class="flex space-x-1 space-x-reverse">
                            {% if products.has_prev %}
                            <a href="{{ url_for('products.index', page=products.prev_num, search=search, category=category_id, status=status) }}"
                               class="flex items-center px-3 py-2 text-sm text-gray-700 dark:text-gray-300 bg-white dark:bg-dark-100 rounded-lg border border-gray-300 dark:border-gray-600 hover:bg-gray-50 dark:hover:bg-dark-200 transition-colors">
                                <i class="ri-arrow-right-s-line ml-1"></i>
                                <span>السابق</span>
                            </a>
                            {% else %}
                            <span class="flex items-center px-3 py-2 text-sm text-gray-400 dark:text-gray-600 bg-gray-100 dark:bg-dark-300 rounded-lg border border-gray-200 dark:border-gray-700 opacity-60 cursor-not-allowed">
                                <i class="ri-arrow-right-s-line ml-1"></i>
                                <span>السابق</span>
                            </span>
                            {% endif %}

                            <div class="hidden sm:flex space-x-1 space-x-reverse">
                            {% for page_num in products.iter_pages(left_edge=1, right_edge=1, left_current=1, right_current=1) %}
                                {% if page_num %}
                                    {% if page_num == products.page %}
                                    <span class="flex items-center justify-center w-10 h-10 text-white bg-primary dark:bg-blue-600 rounded-lg text-sm font-medium shadow-sm">
                                        {{ page_num }}
                                    </span>
                                    {% else %}
                                    <a href="{{ url_for('products.index', page=page_num, search=search, category=category_id, status=status) }}"
                                       class="flex items-center justify-center w-10 h-10 text-gray-700 dark:text-gray-300 bg-white dark:bg-dark-100 rounded-lg border border-gray-300 dark:border-gray-600 hover:bg-gray-50 dark:hover:bg-dark-200 text-sm transition-colors">
                                        {{ page_num }}
                                    </a>
                                    {% endif %}
                                {% else %}
                                <span class="flex items-center justify-center w-10 h-10 text-gray-500 dark:text-gray-400">...</span>
                                {% endif %}
                            {% endfor %}
                            </div>

                            {% if products.has_next %}
                            <a href="{{ url_for('products.index', page=products.next_num, search=search, category=category_id, status=status) }}"
                               class="flex items-center px-3 py-2 text-sm text-gray-700 dark:text-gray-300 bg-white dark:bg-dark-100 rounded-lg border border-gray-300 dark:border-gray-600 hover:bg-gray-50 dark:hover:bg-dark-200 transition-colors">
                                <span>التالي</span>
                                <i class="ri-arrow-left-s-line mr-1"></i>
                            </a>
                            {% else %}
                            <span class="flex items-center px-3 py-2 text-sm text-gray-400 dark:text-gray-600 bg-gray-100 dark:bg-dark-300 rounded-lg border border-gray-200 dark:border-gray-700 opacity-60 cursor-not-allowed">
                                <span>التالي</span>
                                <i class="ri-arrow-left-s-line mr-1"></i>
                            </span>
                            {% endif %}
                        </div>
                    </div>
                    {% endif %}
                </div>
            </main>
        </div>
    </div>

    <!-- Delete Confirmation Modal -->
    <div id="deleteModal" class="fixed inset-0 bg-black bg-opacity-50 dark:bg-black dark:bg-opacity-70 flex items-center justify-center z-50 hidden backdrop-blur-sm">
        <div class="bg-white dark:bg-dark-100 rounded-xl w-80 sm:w-96 overflow-hidden shadow-2xl transform transition-all duration-300 scale-100">
            <div class="p-6">
                <div class="flex items-center justify-center w-16 h-16 rounded-full bg-red-100 dark:bg-red-900/30 text-red-600 dark:text-red-400 mx-auto mb-5 animate-pulse">
                    <i class="ri-delete-bin-line text-2xl"></i>
                </div>
                <h3 class="text-xl font-bold text-gray-900 dark:text-white text-center mb-3">تأكيد الحذف</h3>
                <p class="text-gray-600 dark:text-gray-300 text-center mb-6">
                    هل أنت متأكد من حذف المنتج:<br>
                    <span id="deleteProductName" class="font-medium text-gray-800 dark:text-gray-200 block mt-2"></span>
                </p>
                <div class="flex justify-center gap-3">
                    <button id="confirmDelete" class="px-5 py-2.5 bg-gradient-to-r from-red-500 to-red-600 dark:from-red-600 dark:to-red-700 text-white rounded-lg shadow-md hover:shadow-lg focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 dark:focus:ring-red-600 transition-all text-sm font-medium flex items-center justify-center">
                        <i class="ri-delete-bin-line ml-1.5"></i>
                        <span>نعم، حذف</span>
                    </button>
                    <button id="cancelDelete" class="px-5 py-2.5 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-lg shadow-md hover:bg-gray-200 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 dark:focus:ring-gray-600 transition-all text-sm font-medium flex items-center justify-center">
                        <i class="ri-close-line ml-1.5"></i>
                        <span>إلغاء</span>
                    </button>
                </div>
            </div>
        </div>
    </div>

    <form id="deleteForm" method="POST" style="display: none;">
    </form>

    <!-- زر تبديل الوضع الداكن -->
    <button id="themeToggle" class="theme-toggle">
        <i class="ri-moon-line text-xl" id="darkIcon"></i>
        <i class="ri-sun-line text-xl hidden" id="lightIcon"></i>
    </button>

    <script>
        // إدارة نموذج حذف المنتج
        let productIdToDelete = null;
        const deleteModal = document.getElementById('deleteModal');
        const deleteForm = document.getElementById('deleteForm');
        const deleteProductName = document.getElementById('deleteProductName');

        function confirmDelete(id, name) {
            productIdToDelete = id;
            deleteProductName.textContent = name;
            deleteModal.classList.remove('hidden');
            // إضافة تأثير حركي
            setTimeout(() => {
                deleteModal.querySelector('div').classList.add('scale-100');
            }, 10);
        }

        document.getElementById('confirmDelete').addEventListener('click', function() {
            if (productIdToDelete) {
                // إضافة تأثير تحميل
                this.innerHTML = '<i class="ri-loader-2-line animate-spin ml-1.5"></i><span>جاري الحذف...</span>';
                this.disabled = true;

                deleteForm.action = `/products/${productIdToDelete}/delete`;
                deleteForm.submit();
            }
        });

        document.getElementById('cancelDelete').addEventListener('click', function() {
            closeModal();
        });

        document.addEventListener('click', function(event) {
            if (event.target === deleteModal) {
                closeModal();
            }
        });

        function closeModal() {
            // إضافة تأثير حركي للإغلاق
            deleteModal.querySelector('div').classList.remove('scale-100');
            deleteModal.querySelector('div').classList.add('scale-95', 'opacity-0');

            setTimeout(() => {
                deleteModal.classList.add('hidden');
                deleteModal.querySelector('div').classList.remove('scale-95', 'opacity-0');
                productIdToDelete = null;
            }, 200);
        }

        // إدارة الوضع الداكن
        const themeToggle = document.getElementById('themeToggle');
        const darkIcon = document.getElementById('darkIcon');
        const lightIcon = document.getElementById('lightIcon');
        const html = document.documentElement;
        const body = document.body;

        // التحقق من الوضع المحفوظ
        const savedTheme = localStorage.getItem('theme');
        if (savedTheme === 'dark') {
            enableDarkMode();
        }

        themeToggle.addEventListener('click', toggleTheme);

        function toggleTheme() {
            if (body.classList.contains('dark')) {
                disableDarkMode();
            } else {
                enableDarkMode();
            }
        }

        function enableDarkMode() {
            body.classList.add('dark');
            darkIcon.classList.add('hidden');
            lightIcon.classList.remove('hidden');
            localStorage.setItem('theme', 'dark');
        }

        function disableDarkMode() {
            body.classList.remove('dark');
            lightIcon.classList.add('hidden');
            darkIcon.classList.remove('hidden');
            localStorage.setItem('theme', 'light');
        }

        // إضافة تأثيرات التلميح للأزرار
        document.addEventListener('DOMContentLoaded', function() {
            const tooltips = document.querySelectorAll('.tooltip');

            tooltips.forEach(tooltip => {
                const tooltipText = tooltip.getAttribute('data-tooltip');
                if (tooltipText) {
                    tooltip.addEventListener('mouseenter', function(e) {
                        const tip = document.createElement('div');
                        tip.className = 'absolute z-50 px-2 py-1 text-xs text-white bg-gray-900 dark:bg-gray-800 rounded pointer-events-none opacity-0 transition-opacity duration-300';
                        tip.textContent = tooltipText;
                        tip.style.bottom = '100%';
                        tip.style.left = '50%';
                        tip.style.transform = 'translateX(-50%) translateY(-5px)';
                        tip.style.marginBottom = '5px';
                        tip.style.whiteSpace = 'nowrap';

                        tooltip.style.position = 'relative';
                        tooltip.appendChild(tip);

                        setTimeout(() => {
                            tip.classList.remove('opacity-0');
                            tip.classList.add('opacity-100');
                        }, 10);
                    });

                    tooltip.addEventListener('mouseleave', function() {
                        const tip = tooltip.querySelector('div');
                        if (tip) {
                            tip.classList.remove('opacity-100');
                            tip.classList.add('opacity-0');

                            setTimeout(() => {
                                tip.remove();
                            }, 300);
                        }
                    });
                }
            });
        });
    </script>
</body>
</html>