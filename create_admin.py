#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
سكريبت لإنشاء مستخدم مدير جديد
"""

import os
import sqlite3
from werkzeug.security import generate_password_hash

def create_admin_user(username, password, email, full_name=None):
    """
    إنشاء مستخدم مدير جديد

    المعلمات:
        username (str): اسم المستخدم
        password (str): كلمة المرور
        email (str): البريد الإلكتروني
        full_name (str, optional): الاسم الكامل

    العائد:
        bool: نجاح العملية
    """
    # استخدام مسار قاعدة البيانات
    db_path = os.path.join(os.path.abspath(os.path.dirname(__file__)), 'fouad_pos.db')

    try:
        # التأكد من وجود قاعدة البيانات
        if not os.path.exists(db_path):
            print(f"قاعدة البيانات غير موجودة في: {db_path}")
            return False

        # الاتصال بقاعدة البيانات
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()

        # التحقق من وجود المستخدم
        cursor.execute("SELECT id FROM user WHERE username = ?", (username,))
        existing_user = cursor.fetchone()

        if existing_user:
            print(f"المستخدم {username} موجود بالفعل!")
            conn.close()
            return True

        # إنشاء مستخدم جديد
        password_hash = generate_password_hash(password)
        created_at = "2025-05-20 00:00:00"  # تاريخ الإنشاء

        # إدراج المستخدم في قاعدة البيانات
        cursor.execute(
            "INSERT INTO user (username, email, password_hash, full_name, role, created_at) VALUES (?, ?, ?, ?, ?, ?)",
            (username, email, password_hash, full_name, 'admin', created_at)
        )

        # حفظ التغييرات
        conn.commit()
        conn.close()

        print(f"تم إنشاء المستخدم المدير {username} بنجاح!")
        return True
    except Exception as e:
        print(f"خطأ أثناء إنشاء المستخدم: {str(e)}")
        return False

if __name__ == "__main__":
    # إنشاء مستخدم مدير جديد
    create_admin_user(
        username="admin",
        password="admin",
        email="<EMAIL>",
        full_name="مدير النظام"
    )
