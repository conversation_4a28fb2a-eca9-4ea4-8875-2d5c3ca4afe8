{% extends "base.html" %}

{% block title %}لوحة التحكم - نوبارا{% endblock %}

{% block page_title %}لوحة التحكم{% endblock %}
{% block page_subtitle %}مرحباً بك {{ current_user.full_name }}، إليك نظرة عامة على نشاط اليوم{% endblock %}

{% block page_actions %}
<div class="flex items-center gap-3">
    <button onclick="refreshDashboard()" class="btn btn-secondary btn-sm">
        <i class="ri-refresh-line ml-2"></i>
        تحديث
    </button>
    <a href="{{ url_for('pos.index') }}" class="btn btn-primary btn-sm">
        <i class="ri-shopping-cart-line ml-2"></i>
        نقطة البيع
    </a>
</div>
{% endblock %}

{% block content %}
<!-- إحصائيات سريعة -->
<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
    <!-- مبيعات اليوم -->
    <div class="card hover:shadow-lg transition-shadow">
        <div class="card-body">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-secondary text-sm font-medium">مبيعات اليوم</p>
                    <p class="text-2xl font-bold text-primary" id="today-sales">{{ "%.2f"|format(today_sales) }}</p>
                    <p class="text-xs text-muted">جنيه مصري</p>
                </div>
                <div class="w-12 h-12 bg-gradient-primary rounded-lg flex items-center justify-center">
                    <i class="ri-money-dollar-circle-line text-white text-xl"></i>
                </div>
            </div>
        </div>
    </div>
    
    <!-- فواتير اليوم -->
    <div class="card hover:shadow-lg transition-shadow">
        <div class="card-body">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-secondary text-sm font-medium">فواتير اليوم</p>
                    <p class="text-2xl font-bold text-primary" id="today-invoices">{{ today_invoices }}</p>
                    <p class="text-xs text-muted">فاتورة</p>
                </div>
                <div class="w-12 h-12 bg-gradient-accent rounded-lg flex items-center justify-center">
                    <i class="ri-file-list-line text-white text-xl"></i>
                </div>
            </div>
        </div>
    </div>
    
    <!-- إجمالي العملاء -->
    <div class="card hover:shadow-lg transition-shadow">
        <div class="card-body">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-secondary text-sm font-medium">إجمالي العملاء</p>
                    <p class="text-2xl font-bold text-primary" id="total-customers">{{ total_customers }}</p>
                    <p class="text-xs text-muted">عميل نشط</p>
                </div>
                <div class="w-12 h-12 bg-gradient-secondary rounded-lg flex items-center justify-center">
                    <i class="ri-user-line text-white text-xl"></i>
                </div>
            </div>
        </div>
    </div>
    
    <!-- المنتجات منخفضة المخزون -->
    <div class="card hover:shadow-lg transition-shadow">
        <div class="card-body">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-secondary text-sm font-medium">تنبيهات المخزون</p>
                    <p class="text-2xl font-bold text-red-600" id="low-stock">{{ low_stock_count }}</p>
                    <p class="text-xs text-muted">منتج منخفض</p>
                </div>
                <div class="w-12 h-12 bg-red-500 rounded-lg flex items-center justify-center">
                    <i class="ri-alert-line text-white text-xl"></i>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- الرسوم البيانية والتقارير -->
<div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
    <!-- مخطط المبيعات -->
    <div class="card">
        <div class="card-header">
            <div class="flex items-center justify-between">
                <h3 class="card-title">مبيعات آخر 7 أيام</h3>
                <div class="flex items-center gap-2">
                    <button onclick="changeSalesChartPeriod(7)" class="btn btn-xs btn-secondary active" data-period="7">7 أيام</button>
                    <button onclick="changeSalesChartPeriod(30)" class="btn btn-xs btn-secondary" data-period="30">30 يوم</button>
                </div>
            </div>
        </div>
        <div class="card-body">
            <canvas id="sales-chart" width="400" height="200"></canvas>
        </div>
    </div>
    
    <!-- أفضل المنتجات -->
    <div class="card">
        <div class="card-header">
            <h3 class="card-title">أفضل المنتجات مبيعاً</h3>
        </div>
        <div class="card-body">
            <div class="space-y-4">
                {% for product in top_products %}
                <div class="flex items-center justify-between p-3 bg-secondary rounded-lg">
                    <div class="flex items-center gap-3">
                        <div class="w-8 h-8 bg-gradient-primary rounded-full flex items-center justify-center text-white text-sm font-bold">
                            {{ loop.index }}
                        </div>
                        <div>
                            <p class="font-medium text-primary">{{ product.name }}</p>
                            <p class="text-xs text-secondary">{{ "%.2f"|format(product.total_sales) }} ج.م</p>
                        </div>
                    </div>
                    <div class="text-left">
                        <div class="w-16 h-2 bg-tertiary rounded-full overflow-hidden">
                            <div class="h-full bg-gradient-primary" style="width: {{ (product.total_sales / top_products[0].total_sales * 100) if top_products else 0 }}%"></div>
                        </div>
                    </div>
                </div>
                {% else %}
                <div class="text-center py-8 text-muted">
                    <i class="ri-bar-chart-line text-4xl mb-2"></i>
                    <p>لا توجد بيانات مبيعات</p>
                </div>
                {% endfor %}
            </div>
        </div>
    </div>
</div>

<!-- آخر المبيعات والأنشطة -->
<div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
    <!-- آخر المبيعات -->
    <div class="card">
        <div class="card-header">
            <div class="flex items-center justify-between">
                <h3 class="card-title">آخر المبيعات</h3>
                <a href="{{ url_for('sales.index') }}" class="btn btn-xs btn-primary">عرض الكل</a>
            </div>
        </div>
        <div class="card-body">
            <div class="space-y-3">
                {% for sale in recent_sales %}
                <div class="flex items-center justify-between p-3 border border-primary rounded-lg hover:bg-hover transition-colors">
                    <div class="flex items-center gap-3">
                        <div class="w-10 h-10 bg-gradient-secondary rounded-lg flex items-center justify-center">
                            <i class="ri-shopping-cart-line text-white"></i>
                        </div>
                        <div>
                            <p class="font-medium text-primary">#{{ sale.sale_number }}</p>
                            <p class="text-xs text-secondary">{{ sale.created_at.strftime('%Y-%m-%d %H:%M') }}</p>
                        </div>
                    </div>
                    <div class="text-left">
                        <p class="font-bold text-primary">{{ "%.2f"|format(sale.total_amount) }} ج.م</p>
                        <p class="text-xs text-secondary">{{ sale.customer.name if sale.customer else 'عميل نقدي' }}</p>
                    </div>
                </div>
                {% else %}
                <div class="text-center py-8 text-muted">
                    <i class="ri-shopping-cart-line text-4xl mb-2"></i>
                    <p>لا توجد مبيعات حديثة</p>
                </div>
                {% endfor %}
            </div>
        </div>
    </div>
    
    <!-- إحصائيات المخازن -->
    <div class="card">
        <div class="card-header">
            <div class="flex items-center justify-between">
                <h3 class="card-title">حالة المخازن</h3>
                <a href="{{ url_for('inventory.index') }}" class="btn btn-xs btn-primary">إدارة المخازن</a>
            </div>
        </div>
        <div class="card-body">
            <div class="space-y-3">
                {% for warehouse in warehouses %}
                <div class="flex items-center justify-between p-3 border border-primary rounded-lg">
                    <div class="flex items-center gap-3">
                        <div class="w-10 h-10 bg-gradient-accent rounded-lg flex items-center justify-center">
                            <i class="ri-building-line text-white"></i>
                        </div>
                        <div>
                            <p class="font-medium text-primary">{{ warehouse.name }}</p>
                            <p class="text-xs text-secondary">{{ warehouse.location or 'غير محدد' }}</p>
                        </div>
                    </div>
                    <div class="text-left">
                        <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                            نشط
                        </span>
                    </div>
                </div>
                {% else %}
                <div class="text-center py-8 text-muted">
                    <i class="ri-building-line text-4xl mb-2"></i>
                    <p>لا توجد مخازن مسجلة</p>
                </div>
                {% endfor %}
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
// إعداد مخطط المبيعات
let salesChart;

function initSalesChart() {
    const ctx = document.getElementById('sales-chart').getContext('2d');
    const chartData = {{ sales_chart_data | tojson }};
    
    salesChart = new Chart(ctx, {
        type: 'line',
        data: {
            labels: chartData.map(item => item.date_ar),
            datasets: [{
                label: 'المبيعات (ج.م)',
                data: chartData.map(item => item.amount),
                borderColor: '#2563eb',
                backgroundColor: 'rgba(37, 99, 235, 0.1)',
                borderWidth: 3,
                fill: true,
                tension: 0.4,
                pointBackgroundColor: '#2563eb',
                pointBorderColor: '#ffffff',
                pointBorderWidth: 2,
                pointRadius: 6
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    grid: {
                        color: 'rgba(0, 0, 0, 0.1)'
                    },
                    ticks: {
                        callback: function(value) {
                            return value.toLocaleString() + ' ج.م';
                        }
                    }
                },
                x: {
                    grid: {
                        display: false
                    }
                }
            },
            interaction: {
                intersect: false,
                mode: 'index'
            }
        }
    });
}

// تغيير فترة مخطط المبيعات
function changeSalesChartPeriod(days) {
    // تحديث الأزرار
    document.querySelectorAll('[data-period]').forEach(btn => {
        btn.classList.remove('active');
    });
    document.querySelector(`[data-period="${days}"]`).classList.add('active');
    
    // جلب البيانات الجديدة
    fetch(`/api/dashboard/sales-chart?days=${days}`)
        .then(response => response.json())
        .then(data => {
            salesChart.data.labels = data.map(item => item.date_ar);
            salesChart.data.datasets[0].data = data.map(item => item.amount);
            salesChart.update();
        })
        .catch(error => console.error('Error updating chart:', error));
}

// تحديث لوحة التحكم
function refreshDashboard() {
    const refreshBtn = document.querySelector('[onclick="refreshDashboard()"]');
    const icon = refreshBtn.querySelector('i');
    
    // إضافة تأثير الدوران
    icon.classList.add('animate-spin');
    
    fetch('/api/dashboard/stats')
        .then(response => response.json())
        .then(data => {
            document.getElementById('today-sales').textContent = data.today_sales.toFixed(2);
            document.getElementById('today-invoices').textContent = data.today_invoices;
            document.getElementById('total-customers').textContent = data.total_customers;
            
            // إزالة تأثير الدوران
            setTimeout(() => {
                icon.classList.remove('animate-spin');
            }, 1000);
        })
        .catch(error => {
            console.error('Error refreshing dashboard:', error);
            icon.classList.remove('animate-spin');
        });
}

// تحديث تلقائي كل 5 دقائق
setInterval(refreshDashboard, 5 * 60 * 1000);

// تهيئة المخطط عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    initSalesChart();
});
</script>
{% endblock %}
