<!DOCTYPE html>
<html lang="ar" dir="rtl" class="light">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>FoUaD - تفاصيل المرتجع</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdn.jsdelivr.net/npm/remixicon@2.5.0/fonts/remixicon.css" rel="stylesheet">
    <script>
        tailwind.config = {
            darkMode: 'class',
            theme: {
                extend: {
                    colors: {
                        primary: '#3B82F6',
                        secondary: '#10b981',
                        dark: {
                            100: '#1E293B',
                            200: '#0F172A',
                            300: '#0B1120',
                            400: '#060A15'
                        }
                    }
                }
            }
        }
    </script>
    <style>
        body {
            font-family: 'Tajawal', sans-serif;
            background-color: #f9fafb;
            transition: background-color 0.3s ease, color 0.3s ease;
        }
        body.dark {
            background-color: #0F172A;
            color: #E2E8F0;
        }
        .bg-pattern {
            background-color: #f9fafb;
            background-image: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%233B82F6' fill-opacity='0.05'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
            transition: background-color 0.3s ease;
        }
        .dark .bg-pattern {
            background-color: #0F172A;
            background-image: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%233B82F6' fill-opacity='0.1'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
        }
    </style>
</head>
<body class="bg-pattern">
    <div class="flex h-screen overflow-hidden">
        <!-- Sidebar -->
        {% include 'partials/sidebar.html' %}

        <!-- Main Content -->
        <div class="flex-1 flex flex-col overflow-hidden">
            <!-- Top Navigation -->
            {% include 'partials/topnav.html' %}

            <!-- Content Area -->
            <main class="flex-1 overflow-y-auto p-6">
                <div class="mb-6 flex flex-col md:flex-row md:items-center justify-between gap-4">
                    <div>
                        <h1 class="text-2xl font-bold text-gray-800 dark:text-white flex items-center">
                            <i class="ri-arrow-go-back-line text-red-500 dark:text-red-400 ml-3"></i>
                            <span>تفاصيل المرتجع</span>
                        </h1>
                        <p class="text-gray-600 dark:text-gray-400 mt-1">عرض تفاصيل عملية الإرجاع رقم {{ return_order.reference_number }}</p>
                    </div>

                    <div class="flex flex-wrap gap-3 mt-4 md:mt-0">
                        <a href="{{ url_for('returns.index') }}"
                           class="bg-gradient-to-r from-gray-500 to-gray-600 dark:from-gray-600 dark:to-gray-700 text-white px-4 py-2.5 rounded-lg shadow-md hover:shadow-lg hover:shadow-gray-500/20 dark:hover:shadow-gray-800/20 transition-all duration-300 flex items-center gap-2">
                            <i class="ri-arrow-right-line text-lg"></i>
                            <span class="font-medium">العودة للمرتجعات</span>
                        </a>

                        <a href="{{ url_for('returns.print_return', id=return_order.id) }}" target="_blank"
                           class="bg-gradient-to-r from-green-500 to-green-600 dark:from-green-600 dark:to-green-700 text-white px-4 py-2.5 rounded-lg shadow-md hover:shadow-lg hover:shadow-green-500/20 dark:hover:shadow-green-800/20 transition-all duration-300 flex items-center gap-2">
                            <i class="ri-printer-line text-lg"></i>
                            <span class="font-medium">طباعة إيصال المرتجع</span>
                        </a>
                    </div>
                </div>

                <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
                    <!-- معلومات المرتجع -->
                    <div class="lg:col-span-1">
                        <div class="bg-white dark:bg-dark-100 rounded-lg overflow-hidden border border-gray-200 dark:border-gray-700 shadow-sm">
                            <div class="p-4 bg-gradient-to-r from-red-50 to-white dark:from-red-900/10 dark:to-dark-100 border-b border-gray-200 dark:border-gray-700">
                                <h3 class="text-base font-bold text-gray-800 dark:text-white flex items-center">
                                    <i class="ri-information-line ml-2 text-red-500 dark:text-red-400"></i>
                                    <span>معلومات المرتجع</span>
                                </h3>
                            </div>
                            <div class="p-4">
                                <div class="space-y-4">
                                    <div class="flex justify-between">
                                        <span class="text-gray-600 dark:text-gray-400">رقم المرتجع:</span>
                                        <span class="font-medium text-gray-800 dark:text-white">{{ return_order.reference_number }}</span>
                                    </div>
                                    <div class="flex justify-between">
                                        <span class="text-gray-600 dark:text-gray-400">رقم الفاتورة الأصلية:</span>
                                        <span class="font-medium text-gray-800 dark:text-white">{{ order.invoice_number }}</span>
                                    </div>
                                    <div class="flex justify-between">
                                        <span class="text-gray-600 dark:text-gray-400">تاريخ الإرجاع:</span>
                                        <span class="font-medium text-gray-800 dark:text-white">{{ return_order.created_at.strftime('%Y-%m-%d %H:%M') }}</span>
                                    </div>
                                    <div class="flex justify-between">
                                        <span class="text-gray-600 dark:text-gray-400">المبلغ المسترد:</span>
                                        <span class="font-bold text-red-500 dark:text-red-400">{{ "%.2f"|format(return_order.total_amount) }} ج.م</span>
                                    </div>
                                    <div class="flex justify-between">
                                        <span class="text-gray-600 dark:text-gray-400">طريقة الاسترداد:</span>
                                        <span class="font-medium text-gray-800 dark:text-white">
                                            {% if return_order.payment_method == 'cash' %}
                                                نقدي
                                            {% elif return_order.payment_method == 'card' %}
                                                بطاقة
                                            {% elif return_order.payment_method == 'store_credit' %}
                                                رصيد متجر
                                            {% else %}
                                                {{ return_order.payment_method }}
                                            {% endif %}
                                        </span>
                                    </div>
                                    <div class="flex justify-between">
                                        <span class="text-gray-600 dark:text-gray-400">الحالة:</span>
                                        <span class="px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full
                                            {% if return_order.status == 'completed' %}
                                                bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400
                                            {% elif return_order.status == 'pending' %}
                                                bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-400
                                            {% elif return_order.status == 'cancelled' %}
                                                bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400
                                            {% endif %}
                                        ">
                                            {% if return_order.status == 'completed' %}
                                                مكتمل
                                            {% elif return_order.status == 'pending' %}
                                                معلق
                                            {% elif return_order.status == 'cancelled' %}
                                                ملغي
                                            {% else %}
                                                {{ return_order.status }}
                                            {% endif %}
                                        </span>
                                    </div>
                                    <div class="flex justify-between">
                                        <span class="text-gray-600 dark:text-gray-400">المستخدم:</span>
                                        <span class="font-medium text-gray-800 dark:text-white">{{ return_order.user.username }}</span>
                                    </div>
                                    <div class="flex justify-between">
                                        <span class="text-gray-600 dark:text-gray-400">المخزن:</span>
                                        <span class="font-medium text-gray-800 dark:text-white">
                                            {% if order.warehouse %}
                                                {{ order.warehouse.name }}
                                            {% else %}
                                                المخزن الافتراضي
                                            {% endif %}
                                        </span>
                                    </div>
                                </div>

                                {% if return_order.notes %}
                                <div class="mt-6 pt-4 border-t border-gray-200 dark:border-gray-700">
                                    <h4 class="text-sm font-bold text-gray-700 dark:text-gray-300 mb-2">ملاحظات:</h4>
                                    <p class="text-gray-600 dark:text-gray-400 text-sm">{{ return_order.notes }}</p>
                                </div>
                                {% endif %}
                            </div>
                        </div>

                        <!-- معلومات العميل -->
                        <div class="bg-white dark:bg-dark-100 rounded-lg overflow-hidden border border-gray-200 dark:border-gray-700 shadow-sm mt-6">
                            <div class="p-4 bg-gradient-to-r from-blue-50 to-white dark:from-blue-900/10 dark:to-dark-100 border-b border-gray-200 dark:border-gray-700">
                                <h3 class="text-base font-bold text-gray-800 dark:text-white flex items-center">
                                    <i class="ri-user-3-line ml-2 text-blue-500 dark:text-blue-400"></i>
                                    <span>معلومات العميل</span>
                                </h3>
                            </div>
                            <div class="p-4">
                                {% if order.customer %}
                                <div class="space-y-4">
                                    <div class="flex justify-between">
                                        <span class="text-gray-600 dark:text-gray-400">اسم العميل:</span>
                                        <span class="font-medium text-gray-800 dark:text-white">{{ order.customer.name }}</span>
                                    </div>
                                    {% if order.customer.phone %}
                                    <div class="flex justify-between">
                                        <span class="text-gray-600 dark:text-gray-400">رقم الهاتف:</span>
                                        <span class="font-medium text-gray-800 dark:text-white">{{ order.customer.phone }}</span>
                                    </div>
                                    {% endif %}
                                    {% if order.customer.email %}
                                    <div class="flex justify-between">
                                        <span class="text-gray-600 dark:text-gray-400">البريد الإلكتروني:</span>
                                        <span class="font-medium text-gray-800 dark:text-white">{{ order.customer.email }}</span>
                                    </div>
                                    {% endif %}
                                </div>
                                {% else %}
                                <div class="text-center py-4">
                                    <span class="text-gray-600 dark:text-gray-400">عميل نقدي</span>
                                </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>

                    <!-- المنتجات المرتجعة -->
                    <div class="lg:col-span-2">
                        <div class="bg-white dark:bg-dark-100 rounded-lg overflow-hidden border border-gray-200 dark:border-gray-700 shadow-sm">
                            <div class="p-4 bg-gradient-to-r from-green-50 to-white dark:from-green-900/10 dark:to-dark-100 border-b border-gray-200 dark:border-gray-700">
                                <h3 class="text-base font-bold text-gray-800 dark:text-white flex items-center">
                                    <i class="ri-shopping-basket-line ml-2 text-green-500 dark:text-green-400"></i>
                                    <span>المنتجات المرتجعة</span>
                                </h3>
                            </div>
                            <div class="overflow-x-auto">
                                <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                                    <thead class="bg-gray-50 dark:bg-dark-200">
                                        <tr>
                                            <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">المنتج</th>
                                            <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">السعر</th>
                                            <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">الكمية</th>
                                            <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">الإجمالي</th>
                                            <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">سبب الإرجاع</th>
                                        </tr>
                                    </thead>
                                    <tbody class="bg-white dark:bg-dark-100 divide-y divide-gray-200 dark:divide-gray-700">
                                        {% for item in return_order.items %}
                                        <tr class="hover:bg-gray-50 dark:hover:bg-dark-200 transition-colors duration-150">
                                            <td class="px-6 py-4 whitespace-nowrap">
                                                <div class="flex items-center">
                                                    <div class="flex-shrink-0 h-10 w-10 rounded-lg bg-gray-100 dark:bg-dark-300 flex items-center justify-center text-gray-500 dark:text-gray-400 overflow-hidden border border-gray-200 dark:border-gray-600">
                                                        {% if item.product.image_path %}
                                                        <img src="{{ item.product.image_path }}" alt="{{ item.product.name }}" class="h-10 w-10 rounded-lg object-cover">
                                                        {% else %}
                                                        <i class="ri-shopping-bag-line text-xl"></i>
                                                        {% endif %}
                                                    </div>
                                                    <div class="mr-3">
                                                        <div class="text-sm font-medium text-gray-900 dark:text-white">{{ item.product.name }}</div>
                                                        <div class="text-xs text-gray-500 dark:text-gray-400 font-mono">{{ item.product.code or '-' }}</div>
                                                    </div>
                                                </div>
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap">
                                                <div class="text-sm text-gray-900 dark:text-white">{{ "%.2f"|format(item.price) }} ج.م</div>
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap">
                                                <div class="text-sm text-gray-900 dark:text-white">{{ item.quantity }}</div>
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap">
                                                <div class="text-sm font-bold text-red-500 dark:text-red-400">{{ "%.2f"|format(item.total) }} ج.م</div>
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap">
                                                <div class="text-sm text-gray-500 dark:text-gray-400">{{ item.reason or '-' }}</div>
                                            </td>
                                        </tr>
                                        {% else %}
                                        <tr>
                                            <td colspan="5" class="px-6 py-4 text-center text-gray-500 dark:text-gray-400">لا توجد منتجات مرتجعة</td>
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                    <tfoot class="bg-gray-50 dark:bg-dark-200">
                                        <tr>
                                            <td colspan="3" class="px-6 py-4 text-left text-sm font-bold text-gray-700 dark:text-gray-300">الإجمالي</td>
                                            <td colspan="2" class="px-6 py-4 text-right text-sm font-bold text-red-500 dark:text-red-400">{{ "%.2f"|format(return_order.total_amount) }} ج.م</td>
                                        </tr>
                                    </tfoot>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <!-- زر تبديل الوضع الداكن -->
    <div class="theme-toggle" id="themeToggle">
        <i class="ri-moon-line dark:hidden"></i>
        <i class="ri-sun-line hidden dark:block"></i>
    </div>

    <script>
        // تبديل الوضع الداكن
        document.addEventListener('DOMContentLoaded', function() {
            const themeToggle = document.getElementById('themeToggle');

            // التحقق من الوضع المحفوظ
            function applyTheme() {
                if (localStorage.getItem('theme') === 'dark') {
                    document.documentElement.classList.add('dark');
                    document.documentElement.classList.remove('light');
                } else {
                    document.documentElement.classList.remove('dark');
                    document.documentElement.classList.add('light');
                }
            }

            // تطبيق الوضع المحفوظ عند تحميل الصفحة
            applyTheme();

            // تبديل الوضع عند النقر على الزر
            themeToggle.addEventListener('click', function() {
                if (document.documentElement.classList.contains('dark')) {
                    localStorage.setItem('theme', 'light');
                } else {
                    localStorage.setItem('theme', 'dark');
                }
                applyTheme();
            });
        });
    </script>
</body>
</html>
