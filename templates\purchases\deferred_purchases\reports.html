<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تقارير المشتريات الآجلة - نظام إدارة المبيعات</title>
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <!-- Remixicon -->
    <link href="https://cdn.jsdelivr.net/npm/remixicon@2.5.0/fonts/remixicon.css" rel="stylesheet">
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        .bg-pattern {
            background-color: #f9fafb;
            background-image: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23e5e7eb' fill-opacity='0.4'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
        }
        .glass-effect {
            background: rgba(255, 255, 255, 0.9);
            box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.07);
            backdrop-filter: blur(4px);
            -webkit-backdrop-filter: blur(4px);
            border: 1px solid rgba(255, 255, 255, 0.18);
            transition: all 0.3s ease;
        }
        .glass-effect:hover {
            box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.1);
        }
    </style>
</head>
<body class="bg-pattern min-h-screen">
    <div class="flex h-screen bg-gray-50">
        <!-- Sidebar -->
        {% include 'partials/sidebar.html' %}

        <!-- Main Content -->
        <div class="flex-1 overflow-x-hidden overflow-y-auto">
            <!-- Top Navigation -->
            {% include 'partials/topnav.html' %}

            <!-- Content Area -->
            <main class="flex-1 overflow-y-auto p-6">
                <div class="mb-6 flex flex-wrap items-center justify-between">
                    <div>
                        <h1 class="text-2xl font-bold text-gray-800">تقارير المشتريات الآجلة</h1>
                        <p class="text-gray-600">تحليل وعرض إحصائيات المشتريات الآجلة</p>
                    </div>
                    <div class="flex space-x-2 space-x-reverse">
                        <a href="{{ url_for('deferred_purchases.index') }}" class="bg-gray-200 text-gray-700 px-4 py-2 rounded-lg shadow-md hover:bg-gray-300 transition-all duration-300 flex items-center gap-2">
                            <i class="ri-arrow-right-line"></i>
                            <span>العودة للمشتريات الآجلة</span>
                        </a>
                    </div>
                </div>

                <!-- Stats Cards -->
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
                    <div class="glass-effect rounded-lg p-5 transition-all duration-300 hover:shadow-lg">
                        <div class="flex justify-between">
                            <div>
                                <h3 class="text-gray-500 text-sm">إجمالي المشتريات الآجلة</h3>
                                <p class="text-2xl font-bold">{{ "%.2f"|format(stats.total_deferred_amount) }} ج.م</p>
                            </div>
                            <div class="w-12 h-12 rounded-full bg-blue-100 flex items-center justify-center text-blue-500">
                                <i class="ri-money-dollar-circle-line ri-lg"></i>
                            </div>
                        </div>
                        <div class="mt-4 text-sm">
                            <span class="text-blue-500 font-medium">{{ stats.total_deferred_purchases }} طلب</span>
                        </div>
                    </div>

                    <div class="glass-effect rounded-lg p-5 transition-all duration-300 hover:shadow-lg">
                        <div class="flex justify-between">
                            <div>
                                <h3 class="text-gray-500 text-sm">إجمالي المدفوعات</h3>
                                <p class="text-2xl font-bold">{{ "%.2f"|format(stats.total_paid) }} ج.م</p>
                            </div>
                            <div class="w-12 h-12 rounded-full bg-green-100 flex items-center justify-center text-green-500">
                                <i class="ri-bank-card-line ri-lg"></i>
                            </div>
                        </div>
                        <div class="mt-4 text-sm">
                            <span class="text-green-500 font-medium">تم دفعه</span>
                        </div>
                    </div>

                    <div class="glass-effect rounded-lg p-5 transition-all duration-300 hover:shadow-lg">
                        <div class="flex justify-between">
                            <div>
                                <h3 class="text-gray-500 text-sm">المبلغ المتبقي</h3>
                                <p class="text-2xl font-bold">{{ "%.2f"|format(stats.total_remaining) }} ج.م</p>
                            </div>
                            <div class="w-12 h-12 rounded-full bg-red-100 flex items-center justify-center text-red-500">
                                <i class="ri-refund-2-line ri-lg"></i>
                            </div>
                        </div>
                        <div class="mt-4 text-sm">
                            <span class="text-red-500 font-medium">متبقي للدفع</span>
                        </div>
                    </div>

                    <div class="glass-effect rounded-lg p-5 transition-all duration-300 hover:shadow-lg">
                        <div class="flex justify-between">
                            <div>
                                <h3 class="text-gray-500 text-sm">نسبة الدفع</h3>
                                {% set payment_rate = (stats.total_paid / stats.total_deferred_amount * 100) if stats.total_deferred_amount > 0 else 0 %}
                                <p class="text-2xl font-bold">{{ "%.1f"|format(payment_rate) }}%</p>
                            </div>
                            <div class="w-12 h-12 rounded-full bg-purple-100 flex items-center justify-center text-purple-500">
                                <i class="ri-percent-line ri-lg"></i>
                            </div>
                        </div>
                        <div class="mt-4 text-sm">
                            <div class="w-full bg-gray-200 rounded-full h-2.5">
                                <div class="bg-purple-600 h-2.5 rounded-full" style="width: {{ payment_rate }}%"></div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Charts -->
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
                    <!-- Purchases by Supplier -->
                    <div class="glass-effect rounded-lg overflow-hidden">
                        <div class="bg-blue-600 text-white px-6 py-4">
                            <h2 class="text-lg font-semibold">المشتريات الآجلة حسب المورد</h2>
                        </div>
                        <div class="p-6">
                            {% if stats.deferred_by_supplier %}
                            <div class="h-80">
                                <canvas id="supplierChart"></canvas>
                            </div>
                            {% else %}
                            <div class="text-center py-10 text-gray-500">
                                <p>لا توجد بيانات متاحة</p>
                            </div>
                            {% endif %}
                        </div>
                    </div>

                    <!-- Purchases by Month -->
                    <div class="glass-effect rounded-lg overflow-hidden">
                        <div class="bg-green-600 text-white px-6 py-4">
                            <h2 class="text-lg font-semibold">المشتريات الآجلة حسب الشهر</h2>
                        </div>
                        <div class="p-6">
                            {% if stats.deferred_by_month %}
                            <div class="h-80">
                                <canvas id="monthChart"></canvas>
                            </div>
                            {% else %}
                            <div class="text-center py-10 text-gray-500">
                                <p>لا توجد بيانات متاحة</p>
                            </div>
                            {% endif %}
                        </div>
                    </div>
                </div>

                <!-- Tables -->
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    <!-- Purchases by Supplier Table -->
                    <div class="glass-effect rounded-lg overflow-hidden">
                        <div class="bg-blue-600 text-white px-6 py-4">
                            <h2 class="text-lg font-semibold">المشتريات الآجلة حسب المورد</h2>
                        </div>
                        <div class="p-6">
                            {% if stats.deferred_by_supplier %}
                            <div class="overflow-x-auto">
                                <table class="min-w-full divide-y divide-gray-200">
                                    <thead class="bg-gray-50">
                                        <tr>
                                            <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">المورد</th>
                                            <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">عدد الطلبات</th>
                                            <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">المبلغ الإجمالي</th>
                                        </tr>
                                    </thead>
                                    <tbody class="bg-white divide-y divide-gray-200">
                                        {% for supplier in stats.deferred_by_supplier %}
                                        <tr class="hover:bg-gray-50">
                                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">{{ supplier.name }}</td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ supplier.purchases_count }}</td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ "%.2f"|format(supplier.total_amount) }} ج.م</td>
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                            {% else %}
                            <div class="text-center py-10 text-gray-500">
                                <p>لا توجد بيانات متاحة</p>
                            </div>
                            {% endif %}
                        </div>
                    </div>

                    <!-- Purchases by Month Table -->
                    <div class="glass-effect rounded-lg overflow-hidden">
                        <div class="bg-green-600 text-white px-6 py-4">
                            <h2 class="text-lg font-semibold">المشتريات الآجلة حسب الشهر</h2>
                        </div>
                        <div class="p-6">
                            {% if stats.deferred_by_month %}
                            <div class="overflow-x-auto">
                                <table class="min-w-full divide-y divide-gray-200">
                                    <thead class="bg-gray-50">
                                        <tr>
                                            <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الشهر</th>
                                            <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">عدد الطلبات</th>
                                            <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">المبلغ الإجمالي</th>
                                        </tr>
                                    </thead>
                                    <tbody class="bg-white divide-y divide-gray-200">
                                        {% for month in stats.deferred_by_month %}
                                        <tr class="hover:bg-gray-50">
                                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">{{ month.month }}</td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ month.purchases_count }}</td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ "%.2f"|format(month.total_amount) }} ج.م</td>
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                            {% else %}
                            <div class="text-center py-10 text-gray-500">
                                <p>لا توجد بيانات متاحة</p>
                            </div>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Chart.js configuration
            Chart.defaults.font.family = 'Tajawal, sans-serif';
            Chart.defaults.color = '#6B7280';
            
            // Supplier Chart
            {% if stats.deferred_by_supplier %}
            const supplierCtx = document.getElementById('supplierChart').getContext('2d');
            new Chart(supplierCtx, {
                type: 'bar',
                data: {
                    labels: [
                        {% for supplier in stats.deferred_by_supplier %}
                        "{{ supplier.name }}",
                        {% endfor %}
                    ],
                    datasets: [{
                        label: 'المبلغ الإجمالي (ج.م)',
                        data: [
                            {% for supplier in stats.deferred_by_supplier %}
                            {{ supplier.total_amount }},
                            {% endfor %}
                        ],
                        backgroundColor: 'rgba(59, 130, 246, 0.7)',
                        borderColor: 'rgba(59, 130, 246, 1)',
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    }
                }
            });
            {% endif %}
            
            // Month Chart
            {% if stats.deferred_by_month %}
            const monthCtx = document.getElementById('monthChart').getContext('2d');
            new Chart(monthCtx, {
                type: 'line',
                data: {
                    labels: [
                        {% for month in stats.deferred_by_month %}
                        "{{ month.month }}",
                        {% endfor %}
                    ],
                    datasets: [{
                        label: 'المبلغ الإجمالي (ج.م)',
                        data: [
                            {% for month in stats.deferred_by_month %}
                            {{ month.total_amount }},
                            {% endfor %}
                        ],
                        backgroundColor: 'rgba(16, 185, 129, 0.2)',
                        borderColor: 'rgba(16, 185, 129, 1)',
                        borderWidth: 2,
                        tension: 0.3,
                        fill: true
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    }
                }
            });
            {% endif %}
        });
    </script>
</body>
</html>
