<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Nobara - إعدادات النسخ الاحتياطي</title>
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <!-- Remixicon -->
    <link href="https://cdn.jsdelivr.net/npm/remixicon@2.5.0/fonts/remixicon.css" rel="stylesheet">
    <!-- Alpine.js -->
    <script defer src="https://cdn.jsdelivr.net/npm/alpinejs@3.x.x/dist/cdn.min.js"></script>
    <script>
        tailwind.config = {
            darkMode: 'class',
            theme: {
                extend: {
                    colors: {
                        primary: {
                            50: '#eff6ff',
                            100: '#dbeafe',
                            200: '#bfdbfe',
                            300: '#93c5fd',
                            400: '#60a5fa',
                            500: '#3b82f6',
                            600: '#2563eb',
                            700: '#1d4ed8',
                            800: '#1e40af',
                            900: '#1e3a8a',
                            950: '#172554',
                        },
                        dark: {
                            100: '#1E293B',
                            200: '#0F172A',
                            300: '#0B1120',
                            400: '#060A15'
                        }
                    }
                }
            }
        }
    </script>
    <style>
        body {
            font-family: 'Cairo', 'Tajawal', sans-serif;
            transition: background-color 0.3s ease, color 0.3s ease;
        }
        
        .bg-pattern {
            background-color: #f9fafb;
            background-image: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%233B82F6' fill-opacity='0.05'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
        }
        
        .dark .bg-pattern {
            background-color: #0F172A;
            background-image: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%233B82F6' fill-opacity='0.1'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
        }
        
        .powered-by {
            position: fixed;
            bottom: 1rem;
            left: 1rem;
            background-color: #ffffff;
            border-radius: 1rem;
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
            padding: 0.75rem 1rem;
            display: flex;
            align-items: center;
            z-index: 40;
            transition: all 0.3s ease;
            border: 1px solid #e5e7eb;
        }
        
        .dark .powered-by {
            background-color: #1E293B;
            border-color: #2D3748;
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.3), 0 4px 6px -2px rgba(0, 0, 0, 0.2);
        }
        
        .powered-by:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
        }
        
        .dark .powered-by:hover {
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.3), 0 10px 10px -5px rgba(0, 0, 0, 0.2);
        }
    </style>
</head>
<body class="bg-pattern dark:bg-dark-200 dark:text-gray-100" x-data="{ darkMode: localStorage.getItem('darkMode') === 'enabled' }">
    <div class="flex h-screen overflow-hidden">
        <!-- Sidebar -->
        {% include 'partials/sidebar.html' %}

        <!-- Main Content -->
        <div class="flex-1 flex flex-col overflow-hidden">
            <!-- Top Navigation -->
            {% include 'partials/topnav.html' %}

            <!-- Content Area -->
            <main class="flex-1 overflow-y-auto p-6">
                <div class="mb-6 flex justify-between items-center">
                    <div>
                        <h1 class="text-2xl font-bold text-gray-800 dark:text-white">إعدادات النسخ الاحتياطي</h1>
                        <p class="text-gray-600 dark:text-gray-400">إعداد النسخ الاحتياطي التلقائي واستعادة البيانات</p>
                    </div>
                    <div class="flex items-center gap-2">
                        <a href="{{ url_for('settings.index') }}" class="flex items-center gap-1 bg-gray-100 dark:bg-dark-100 hover:bg-gray-200 dark:hover:bg-dark-300 text-gray-700 dark:text-gray-300 py-2 px-4 rounded-lg transition-colors">
                            <i class="ri-arrow-right-line"></i>
                            <span>العودة للإعدادات</span>
                        </a>
                        <button @click="darkMode = !darkMode; localStorage.setItem('darkMode', darkMode ? 'enabled' : 'disabled'); document.documentElement.classList.toggle('dark')" class="p-2 rounded-full bg-gray-100 dark:bg-dark-100 hover:bg-gray-200 dark:hover:bg-dark-300 transition-colors">
                            <i class="ri-moon-line dark:hidden"></i>
                            <i class="ri-sun-line hidden dark:block"></i>
                        </button>
                    </div>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
                    <!-- إنشاء نسخة احتياطية -->
                    <div class="bg-white dark:bg-dark-100 rounded-xl shadow-sm overflow-hidden">
                        <div class="p-6">
                            <h3 class="text-lg font-medium text-gray-800 dark:text-white mb-4 flex items-center">
                                <i class="ri-save-3-line ml-2 text-primary-500"></i>
                                إنشاء نسخة احتياطية
                            </h3>
                            <p class="text-gray-600 dark:text-gray-400 mb-4">قم بإنشاء نسخة احتياطية يدوية من قاعدة البيانات الحالية.</p>
                            <form id="createBackupForm" action="{{ url_for('settings.create_backup') }}" method="POST">
                                <button type="submit" class="w-full bg-primary-500 hover:bg-primary-600 text-white py-2 px-4 rounded-lg inline-flex items-center justify-center transition-colors">
                                    <i class="ri-download-cloud-line ml-1"></i>
                                    <span>إنشاء نسخة احتياطية الآن</span>
                                </button>
                            </form>
                        </div>
                    </div>
                    
                    <!-- استعادة نسخة احتياطية -->
                    <div class="bg-white dark:bg-dark-100 rounded-xl shadow-sm overflow-hidden">
                        <div class="p-6">
                            <h3 class="text-lg font-medium text-gray-800 dark:text-white mb-4 flex items-center">
                                <i class="ri-refresh-line ml-2 text-primary-500"></i>
                                استعادة نسخة احتياطية
                            </h3>
                            <p class="text-gray-600 dark:text-gray-400 mb-4">استعادة قاعدة البيانات من نسخة احتياطية سابقة.</p>
                            <form id="restoreBackupForm" action="{{ url_for('settings.restore_backup') }}" method="POST" enctype="multipart/form-data">
                                <div class="mb-4">
                                    <label for="backup_file" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">اختر ملف النسخة الاحتياطية</label>
                                    <input type="file" id="backup_file" name="backup_file" accept=".db,.sqlite,.backup" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 dark:bg-dark-200 dark:text-gray-100">
                                </div>
                                <button type="submit" class="w-full bg-yellow-500 hover:bg-yellow-600 text-white py-2 px-4 rounded-lg inline-flex items-center justify-center transition-colors">
                                    <i class="ri-upload-cloud-line ml-1"></i>
                                    <span>استعادة النسخة الاحتياطية</span>
                                </button>
                            </form>
                        </div>
                    </div>
                    
                    <!-- النسخ الاحتياطية المتاحة -->
                    <div class="bg-white dark:bg-dark-100 rounded-xl shadow-sm overflow-hidden">
                        <div class="p-6">
                            <h3 class="text-lg font-medium text-gray-800 dark:text-white mb-4 flex items-center">
                                <i class="ri-folder-line ml-2 text-primary-500"></i>
                                النسخ الاحتياطية المتاحة
                            </h3>
                            <p class="text-gray-600 dark:text-gray-400 mb-4">قائمة بالنسخ الاحتياطية المتاحة على النظام.</p>
                            <div class="max-h-60 overflow-y-auto">
                                <ul class="divide-y divide-gray-200 dark:divide-gray-700">
                                    {% for backup in backups %}
                                    <li class="py-2">
                                        <div class="flex justify-between items-center">
                                            <div>
                                                <div class="text-sm font-medium text-gray-800 dark:text-white">{{ backup.filename }}</div>
                                                <div class="text-xs text-gray-500 dark:text-gray-400">{{ backup.date }}</div>
                                                <div class="text-xs text-gray-500 dark:text-gray-400">{{ backup.size }}</div>
                                            </div>
                                            <div class="flex gap-1">
                                                <a href="{{ url_for('settings.download_backup', filename=backup.filename) }}" class="p-1 text-blue-500 hover:text-blue-600" title="تنزيل">
                                                    <i class="ri-download-line"></i>
                                                </a>
                                                <button onclick="restoreBackupFromList('{{ backup.filename }}')" class="p-1 text-yellow-500 hover:text-yellow-600" title="استعادة">
                                                    <i class="ri-refresh-line"></i>
                                                </button>
                                                <button onclick="deleteBackup('{{ backup.filename }}')" class="p-1 text-red-500 hover:text-red-600" title="حذف">
                                                    <i class="ri-delete-bin-line"></i>
                                                </button>
                                            </div>
                                        </div>
                                    </li>
                                    {% else %}
                                    <li class="py-4 text-center text-gray-500 dark:text-gray-400">
                                        لا توجد نسخ احتياطية متاحة
                                    </li>
                                    {% endfor %}
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="bg-white dark:bg-dark-100 rounded-xl shadow-sm overflow-hidden mb-6">
                    <div class="p-6">
                        <h3 class="text-lg font-medium text-gray-800 dark:text-white mb-4 flex items-center">
                            <i class="ri-settings-3-line ml-2 text-primary-500"></i>
                            إعدادات النسخ الاحتياطي التلقائي
                        </h3>
                        
                        <form id="backupSettingsForm" action="{{ url_for('settings.update_backup_settings') }}" method="POST">
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <!-- تفعيل النسخ الاحتياطي التلقائي -->
                                <div class="md:col-span-2">
                                    <label class="flex items-center">
                                        <input type="checkbox" id="auto_backup" name="auto_backup" {% if backup_settings.auto_backup %}checked{% endif %} class="w-4 h-4 text-primary-600 border-gray-300 rounded focus:ring-primary-500 dark:bg-dark-200 dark:border-gray-600">
                                        <span class="mr-2 text-sm text-gray-700 dark:text-gray-300">تفعيل النسخ الاحتياطي التلقائي</span>
                                    </label>
                                </div>
                                
                                <!-- تكرار النسخ الاحتياطي -->
                                <div>
                                    <label for="backup_frequency" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">تكرار النسخ الاحتياطي</label>
                                    <select id="backup_frequency" name="backup_frequency" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 dark:bg-dark-200 dark:text-gray-100">
                                        <option value="daily" {% if backup_settings.backup_frequency == 'daily' %}selected{% endif %}>يومي</option>
                                        <option value="weekly" {% if backup_settings.backup_frequency == 'weekly' %}selected{% endif %}>أسبوعي</option>
                                        <option value="monthly" {% if backup_settings.backup_frequency == 'monthly' %}selected{% endif %}>شهري</option>
                                    </select>
                                </div>
                                
                                <!-- وقت النسخ الاحتياطي -->
                                <div>
                                    <label for="backup_time" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">وقت النسخ الاحتياطي</label>
                                    <input type="time" id="backup_time" name="backup_time" value="{{ backup_settings.backup_time }}" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 dark:bg-dark-200 dark:text-gray-100">
                                </div>
                                
                                <!-- يوم النسخ الاحتياطي (للأسبوعي والشهري) -->
                                <div id="backup_day_container" {% if backup_settings.backup_frequency == 'daily' %}style="display: none;"{% endif %}>
                                    <label for="backup_day" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                        <span id="backup_day_label">{% if backup_settings.backup_frequency == 'weekly' %}يوم الأسبوع{% else %}يوم الشهر{% endif %}</span>
                                    </label>
                                    <select id="backup_day" name="backup_day" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 dark:bg-dark-200 dark:text-gray-100">
                                        {% if backup_settings.backup_frequency == 'weekly' %}
                                            <option value="0" {% if backup_settings.backup_day == 0 %}selected{% endif %}>الأحد</option>
                                            <option value="1" {% if backup_settings.backup_day == 1 %}selected{% endif %}>الإثنين</option>
                                            <option value="2" {% if backup_settings.backup_day == 2 %}selected{% endif %}>الثلاثاء</option>
                                            <option value="3" {% if backup_settings.backup_day == 3 %}selected{% endif %}>الأربعاء</option>
                                            <option value="4" {% if backup_settings.backup_day == 4 %}selected{% endif %}>الخميس</option>
                                            <option value="5" {% if backup_settings.backup_day == 5 %}selected{% endif %}>الجمعة</option>
                                            <option value="6" {% if backup_settings.backup_day == 6 %}selected{% endif %}>السبت</option>
                                        {% else %}
                                            {% for day in range(1, 32) %}
                                            <option value="{{ day }}" {% if backup_settings.backup_day == day %}selected{% endif %}>{{ day }}</option>
                                            {% endfor %}
                                        {% endif %}
                                    </select>
                                </div>
                                
                                <!-- مسار حفظ النسخ الاحتياطية -->
                                <div>
                                    <label for="backup_path" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">مسار حفظ النسخ الاحتياطية</label>
                                    <div class="flex">
                                        <input type="text" id="backup_path" name="backup_path" value="{{ backup_settings.backup_path }}" class="flex-1 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-r-none rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 dark:bg-dark-200 dark:text-gray-100">
                                        <button type="button" onclick="browseBackupPath()" class="bg-gray-100 dark:bg-dark-300 hover:bg-gray-200 dark:hover:bg-dark-400 text-gray-700 dark:text-gray-300 px-3 py-2 rounded-l-lg border border-gray-300 dark:border-gray-600 border-r-0">
                                            <i class="ri-folder-open-line"></i>
                                        </button>
                                    </div>
                                </div>
                                
                                <!-- عدد النسخ الاحتياطية للاحتفاظ بها -->
                                <div>
                                    <label for="keep_backups" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">عدد النسخ الاحتياطية للاحتفاظ بها</label>
                                    <input type="number" id="keep_backups" name="keep_backups" value="{{ backup_settings.keep_backups }}" min="1" max="100" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 dark:bg-dark-200 dark:text-gray-100">
                                    <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">سيتم حذف النسخ الاحتياطية الأقدم تلقائيًا عند تجاوز هذا العدد</p>
                                </div>
                                
                                <div class="md:col-span-2 flex justify-end mt-4">
                                    <button type="submit" class="bg-primary-500 hover:bg-primary-600 text-white py-2 px-6 rounded-lg inline-flex items-center transition-colors">
                                        <i class="ri-save-line ml-1"></i>
                                        <span>حفظ الإعدادات</span>
                                    </button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </main>
        </div>
    </div>
    
    <!-- Powered By -->
    <div class="powered-by">
        <div class="w-8 h-8 rounded-full bg-primary-50 dark:bg-primary-900/50 flex items-center justify-center ml-2 text-primary-600 dark:text-primary-400">
            <i class="ri-code-s-slash-line"></i>
        </div>
        <div>
            <div class="text-xs text-gray-600 dark:text-gray-400">
                Powered By <span class="font-bold text-primary-600 dark:text-primary-400">ENG/ Fouad Saber</span>
            </div>
            <div class="text-xs text-gray-500 dark:text-gray-500">Tel: 01020073527</div>
        </div>
    </div>
</body>
</html>
