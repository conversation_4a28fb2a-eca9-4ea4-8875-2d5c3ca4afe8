"""
Nobara POS System - Main Routes
نظام نوبارا لنقاط البيع - المسارات الرئيسية
"""

from flask import render_template, request, jsonify, session, redirect, url_for
from flask_login import login_required, current_user
from app.main import bp
from app.models import User, Product, Sale, Customer, Warehouse, db
from sqlalchemy import func, desc
from datetime import datetime, timedelta
import logging

logger = logging.getLogger(__name__)

@bp.route('/')
@bp.route('/dashboard')
@login_required
def dashboard():
    """لوحة التحكم الرئيسية"""
    try:
        # إحصائيات اليوم
        today = datetime.utcnow().date()
        today_start = datetime.combine(today, datetime.min.time())
        today_end = datetime.combine(today, datetime.max.time())
        
        # مبيعات اليوم
        today_sales = db.session.query(func.sum(Sale.total_amount)).filter(
            Sale.sale_date.between(today_start, today_end),
            Sale.status == 'completed'
        ).scalar() or 0
        
        # عدد الفواتير اليوم
        today_invoices = Sale.query.filter(
            Sale.sale_date.between(today_start, today_end),
            Sale.status == 'completed'
        ).count()
        
        # إجمالي العملاء
        total_customers = Customer.query.filter_by(is_active=True).count()
        
        # إجمالي المنتجات
        total_products = Product.query.filter_by(is_active=True).count()
        
        # المنتجات منخفضة المخزون
        low_stock_products = Product.query.filter(
            Product.is_active == True,
            Product.track_inventory == True
        ).all()
        
        low_stock_count = 0
        for product in low_stock_products:
            if product.total_stock <= product.min_stock_level:
                low_stock_count += 1
        
        # آخر المبيعات
        recent_sales = Sale.query.filter_by(status='completed').order_by(
            desc(Sale.created_at)
        ).limit(5).all()
        
        # أفضل المنتجات مبيعاً (آخر 30 يوم)
        thirty_days_ago = datetime.utcnow() - timedelta(days=30)
        top_products = db.session.query(
            Product.name,
            func.sum(Sale.total_amount).label('total_sales')
        ).join(Sale.items).join(Product).filter(
            Sale.sale_date >= thirty_days_ago,
            Sale.status == 'completed'
        ).group_by(Product.id).order_by(desc('total_sales')).limit(5).all()
        
        # مبيعات آخر 7 أيام
        sales_chart_data = []
        for i in range(7):
            date = datetime.utcnow().date() - timedelta(days=i)
            date_start = datetime.combine(date, datetime.min.time())
            date_end = datetime.combine(date, datetime.max.time())
            
            daily_sales = db.session.query(func.sum(Sale.total_amount)).filter(
                Sale.sale_date.between(date_start, date_end),
                Sale.status == 'completed'
            ).scalar() or 0
            
            sales_chart_data.append({
                'date': date.strftime('%Y-%m-%d'),
                'date_ar': date.strftime('%d/%m'),
                'amount': float(daily_sales)
            })
        
        sales_chart_data.reverse()  # ترتيب تصاعدي
        
        # إحصائيات الشهر الحالي
        month_start = datetime.utcnow().replace(day=1, hour=0, minute=0, second=0, microsecond=0)
        month_sales = db.session.query(func.sum(Sale.total_amount)).filter(
            Sale.sale_date >= month_start,
            Sale.status == 'completed'
        ).scalar() or 0
        
        month_invoices = Sale.query.filter(
            Sale.sale_date >= month_start,
            Sale.status == 'completed'
        ).count()
        
        # المخازن
        warehouses = Warehouse.query.filter_by(is_active=True).all()
        
        dashboard_data = {
            'today_sales': float(today_sales),
            'today_invoices': today_invoices,
            'total_customers': total_customers,
            'total_products': total_products,
            'low_stock_count': low_stock_count,
            'month_sales': float(month_sales),
            'month_invoices': month_invoices,
            'recent_sales': recent_sales,
            'top_products': top_products,
            'sales_chart_data': sales_chart_data,
            'warehouses': warehouses
        }
        
        return render_template('main/dashboard.html', **dashboard_data)
        
    except Exception as e:
        logger.error(f'Error loading dashboard: {e}')
        return render_template('main/dashboard.html', error=str(e))

@bp.route('/api/dashboard/stats')
@login_required
def dashboard_stats():
    """API لإحصائيات لوحة التحكم"""
    try:
        today = datetime.utcnow().date()
        today_start = datetime.combine(today, datetime.min.time())
        today_end = datetime.combine(today, datetime.max.time())
        
        stats = {
            'today_sales': float(db.session.query(func.sum(Sale.total_amount)).filter(
                Sale.sale_date.between(today_start, today_end),
                Sale.status == 'completed'
            ).scalar() or 0),
            'today_invoices': Sale.query.filter(
                Sale.sale_date.between(today_start, today_end),
                Sale.status == 'completed'
            ).count(),
            'total_customers': Customer.query.filter_by(is_active=True).count(),
            'total_products': Product.query.filter_by(is_active=True).count(),
            'timestamp': datetime.utcnow().isoformat()
        }
        
        return jsonify(stats)
        
    except Exception as e:
        logger.error(f'Error getting dashboard stats: {e}')
        return jsonify({'error': str(e)}), 500

@bp.route('/api/dashboard/sales-chart')
@login_required
def sales_chart():
    """API لبيانات مخطط المبيعات"""
    try:
        days = request.args.get('days', 7, type=int)
        
        chart_data = []
        for i in range(days):
            date = datetime.utcnow().date() - timedelta(days=i)
            date_start = datetime.combine(date, datetime.min.time())
            date_end = datetime.combine(date, datetime.max.time())
            
            daily_sales = db.session.query(func.sum(Sale.total_amount)).filter(
                Sale.sale_date.between(date_start, date_end),
                Sale.status == 'completed'
            ).scalar() or 0
            
            chart_data.append({
                'date': date.strftime('%Y-%m-%d'),
                'date_ar': date.strftime('%d/%m'),
                'amount': float(daily_sales)
            })
        
        chart_data.reverse()
        
        return jsonify(chart_data)
        
    except Exception as e:
        logger.error(f'Error getting sales chart data: {e}')
        return jsonify({'error': str(e)}), 500

@bp.route('/api/dashboard/recent-activities')
@login_required
def recent_activities():
    """API للأنشطة الحديثة"""
    try:
        activities = []
        
        # آخر المبيعات
        recent_sales = Sale.query.filter_by(status='completed').order_by(
            desc(Sale.created_at)
        ).limit(10).all()
        
        for sale in recent_sales:
            activities.append({
                'type': 'sale',
                'icon': 'ri-shopping-cart-line',
                'title': f'فاتورة مبيعات #{sale.sale_number}',
                'description': f'بقيمة {sale.total_amount:.2f} ج.م',
                'time': sale.created_at.strftime('%H:%M'),
                'date': sale.created_at.strftime('%Y-%m-%d'),
                'user': sale.user.full_name if sale.user else 'غير محدد'
            })
        
        # ترتيب حسب التاريخ
        activities.sort(key=lambda x: x['date'] + ' ' + x['time'], reverse=True)
        
        return jsonify(activities[:10])
        
    except Exception as e:
        logger.error(f'Error getting recent activities: {e}')
        return jsonify({'error': str(e)}), 500

@bp.route('/settings/language/<language>')
@login_required
def set_language(language):
    """تغيير اللغة"""
    if language in ['ar', 'en']:
        session['language'] = language
        current_user.language = language
        current_user.updated_at = datetime.utcnow()
        db.session.commit()
        
        logger.info(f'User {current_user.username} changed language to {language}')
    
    return redirect(request.referrer or url_for('main.dashboard'))

@bp.route('/settings/theme/<theme>')
@login_required
def set_theme(theme):
    """تغيير الثيم"""
    if theme in ['light', 'dark']:
        session['theme'] = theme
        current_user.theme = theme
        current_user.updated_at = datetime.utcnow()
        db.session.commit()
        
        logger.info(f'User {current_user.username} changed theme to {theme}')
    
    return redirect(request.referrer or url_for('main.dashboard'))

@bp.route('/api/search')
@login_required
def global_search():
    """البحث العام"""
    try:
        query = request.args.get('q', '').strip()
        if not query or len(query) < 2:
            return jsonify([])
        
        results = []
        
        # البحث في المنتجات
        products = Product.query.filter(
            Product.is_active == True,
            db.or_(
                Product.name.contains(query),
                Product.sku.contains(query),
                Product.barcode.contains(query)
            )
        ).limit(5).all()
        
        for product in products:
            results.append({
                'type': 'product',
                'id': product.id,
                'title': product.name,
                'subtitle': f'SKU: {product.sku or "غير محدد"}',
                'url': url_for('products.view', id=product.id),
                'icon': 'ri-product-hunt-line'
            })
        
        # البحث في العملاء
        customers = Customer.query.filter(
            Customer.is_active == True,
            db.or_(
                Customer.name.contains(query),
                Customer.phone.contains(query),
                Customer.email.contains(query)
            )
        ).limit(5).all()
        
        for customer in customers:
            results.append({
                'type': 'customer',
                'id': customer.id,
                'title': customer.name,
                'subtitle': customer.phone or customer.email or '',
                'url': url_for('customers.view', id=customer.id),
                'icon': 'ri-user-line'
            })
        
        return jsonify(results)
        
    except Exception as e:
        logger.error(f'Error in global search: {e}')
        return jsonify({'error': str(e)}), 500
