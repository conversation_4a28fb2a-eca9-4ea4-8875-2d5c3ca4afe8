{% extends 'layout.html' %}

{% block title %}تنبيهات المخزون{% endblock %}

{% block page_content %}
<div class="container mx-auto px-4 py-8">
    <div class="mb-6 flex justify-between items-center">
        <div>
            <h1 class="text-2xl font-bold">تنبيهات المخزون</h1>
            <p class="text-gray-600">إدارة تنبيهات المخزون المنخفض والنافذ</p>
        </div>
        <div class="flex space-x-2 space-x-reverse">
            <a href="{{ url_for('warehouses.reports') }}" class="bg-indigo-500 text-white px-4 py-2 rounded-lg hover:bg-indigo-600 transition-all ml-2">
                <i class="ri-file-chart-line ml-1"></i>تقارير المخزون
            </a>
            <a href="{{ url_for('warehouses.index') }}" class="bg-gray-100 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-200 transition-all">
                <i class="ri-arrow-right-line ml-1"></i>العودة للمخازن
            </a>
        </div>
    </div>

    <!-- إحصائيات التنبيهات -->
    {% if stats %}
    <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
        <div class="bg-white rounded-lg shadow p-4 border-r-4 border-red-500">
            <div class="flex justify-between items-center">
                <div>
                    <p class="text-gray-500 text-sm">تنبيهات غير مقروءة</p>
                    <p class="text-2xl font-bold">{{ stats.unread }}</p>
                </div>
                <div class="text-red-500 text-3xl">
                    <i class="ri-notification-badge-line"></i>
                </div>
            </div>
        </div>
        <div class="bg-white rounded-lg shadow p-4 border-r-4 border-yellow-500">
            <div class="flex justify-between items-center">
                <div>
                    <p class="text-gray-500 text-sm">تنبيهات غير محلولة</p>
                    <p class="text-2xl font-bold">{{ stats.unresolved }}</p>
                </div>
                <div class="text-yellow-500 text-3xl">
                    <i class="ri-error-warning-line"></i>
                </div>
            </div>
        </div>
        <div class="bg-white rounded-lg shadow p-4 border-r-4 border-blue-500">
            <div class="flex justify-between items-center">
                <div>
                    <p class="text-gray-500 text-sm">مخزون منخفض</p>
                    <p class="text-2xl font-bold">{{ stats.low_stock }}</p>
                </div>
                <div class="text-blue-500 text-3xl">
                    <i class="ri-arrow-down-line"></i>
                </div>
            </div>
        </div>
        <div class="bg-white rounded-lg shadow p-4 border-r-4 border-red-500">
            <div class="flex justify-between items-center">
                <div>
                    <p class="text-gray-500 text-sm">نفذ من المخزون</p>
                    <p class="text-2xl font-bold">{{ stats.out_of_stock }}</p>
                </div>
                <div class="text-red-500 text-3xl">
                    <i class="ri-close-circle-line"></i>
                </div>
            </div>
        </div>
    </div>
    {% endif %}

    <!-- فلاتر البحث -->
    <div class="bg-white rounded-lg shadow p-4 mb-6">
        <form method="GET" action="{{ url_for('warehouses.alerts') }}" class="flex flex-wrap items-center gap-4">
            <div>
                <label class="block text-gray-700 mb-2">المخزن</label>
                <select name="warehouse_id" class="border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500">
                    <option value="">جميع المخازن</option>
                    {% for warehouse in warehouses %}
                    <option value="{{ warehouse.id }}" {% if warehouse_id == warehouse.id|string %}selected{% endif %}>{{ warehouse.name }}</option>
                    {% endfor %}
                </select>
            </div>
            <div>
                <label class="block text-gray-700 mb-2">نوع التنبيه</label>
                <select name="alert_type" class="border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500">
                    <option value="">جميع التنبيهات</option>
                    <option value="low_stock" {% if alert_type == 'low_stock' %}selected{% endif %}>مخزون منخفض</option>
                    <option value="out_of_stock" {% if alert_type == 'out_of_stock' %}selected{% endif %}>نفذ من المخزون</option>
                </select>
            </div>
            <div>
                <label class="block text-gray-700 mb-2">الحالة</label>
                <select name="is_resolved" class="border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500">
                    <option value="">جميع الحالات</option>
                    <option value="no" {% if is_resolved == 'no' %}selected{% endif %}>غير محلولة</option>
                    <option value="yes" {% if is_resolved == 'yes' %}selected{% endif %}>محلولة</option>
                </select>
            </div>
            <div class="self-end">
                <button type="submit" class="bg-blue-500 text-white px-4 py-2 rounded-md hover:bg-blue-600 transition-all">
                    تطبيق الفلتر
                </button>
            </div>
        </form>
    </div>

    <!-- جدول التنبيهات -->
    <div class="bg-white rounded-lg shadow overflow-hidden">
        <div class="p-4 bg-gray-50 border-b flex justify-between items-center">
            <h2 class="text-xl font-semibold">قائمة التنبيهات</h2>
            <button id="markAllReadBtn" class="bg-blue-100 text-blue-700 hover:bg-blue-200 px-3 py-1 rounded-md text-sm transition-all">
                <i class="ri-check-double-line ml-1"></i>تحديد الكل كمقروء
            </button>
        </div>

        <table class="min-w-full">
            <thead>
                <tr class="bg-gray-100">
                    <th class="px-6 py-3 border-b text-right">التاريخ</th>
                    <th class="px-6 py-3 border-b text-right">المنتج</th>
                    <th class="px-6 py-3 border-b text-right">المخزن</th>
                    <th class="px-6 py-3 border-b text-right">نوع التنبيه</th>
                    <th class="px-6 py-3 border-b text-right">الرسالة</th>
                    <th class="px-6 py-3 border-b text-right">الحالة</th>
                    <th class="px-6 py-3 border-b text-right">الإجراءات</th>
                </tr>
            </thead>
            <tbody>
                {% for alert in alerts %}
                <tr class="hover:bg-gray-50 {% if not alert.is_read %}bg-yellow-50 font-medium{% endif %} {% if alert.is_resolved %}opacity-70{% endif %}">
                    <td class="px-6 py-4 border-b">{{ alert.created_at.strftime('%Y-%m-%d %H:%M') }}</td>
                    <td class="px-6 py-4 border-b">{{ alert.inventory.product.name }}</td>
                    <td class="px-6 py-4 border-b">{{ alert.inventory.warehouse.name }}</td>
                    <td class="px-6 py-4 border-b">
                        {% if alert.alert_type == 'low_stock' %}
                        <span class="bg-blue-100 text-blue-800 text-xs font-medium px-2.5 py-0.5 rounded">مخزون منخفض</span>
                        {% elif alert.alert_type == 'out_of_stock' %}
                        <span class="bg-red-100 text-red-800 text-xs font-medium px-2.5 py-0.5 rounded">نفذ من المخزون</span>
                        {% endif %}
                    </td>
                    <td class="px-6 py-4 border-b text-sm">{{ alert.message }}</td>
                    <td class="px-6 py-4 border-b">
                        {% if alert.is_resolved %}
                        <span class="bg-green-100 text-green-800 text-xs font-medium px-2.5 py-0.5 rounded">محلولة</span>
                        {% else %}
                        <span class="bg-yellow-100 text-yellow-800 text-xs font-medium px-2.5 py-0.5 rounded">غير محلولة</span>
                        {% endif %}
                    </td>
                    <td class="px-6 py-4 border-b">
                        <div class="flex space-x-2 space-x-reverse">
                            {% if not alert.is_read %}
                            <button onclick="markAsRead({{ alert.id }})" class="bg-blue-100 text-blue-700 hover:bg-blue-200 px-2 py-1 rounded-md text-xs transition-all">
                                <i class="ri-check-line ml-1"></i>تحديد كمقروء
                            </button>
                            {% endif %}
                            
                            {% if not alert.is_resolved %}
                            <button onclick="markAsResolved({{ alert.id }})" class="bg-green-100 text-green-700 hover:bg-green-200 px-2 py-1 rounded-md text-xs transition-all">
                                <i class="ri-check-double-line ml-1"></i>تحديد كمحلول
                            </button>
                            {% endif %}
                            
                            <a href="{{ url_for('warehouses.inventory', id=alert.inventory.warehouse_id) }}" class="bg-gray-100 text-gray-700 hover:bg-gray-200 px-2 py-1 rounded-md text-xs transition-all">
                                <i class="ri-edit-line ml-1"></i>تعديل المخزون
                            </a>
                        </div>
                    </td>
                </tr>
                {% endfor %}
                
                {% if alerts|length == 0 %}
                <tr>
                    <td colspan="7" class="px-6 py-8 text-center text-gray-500">
                        <div class="flex flex-col items-center">
                            <i class="ri-notification-off-line text-4xl mb-2"></i>
                            <p>لا توجد تنبيهات مخزون</p>
                            {% if warehouse_id or alert_type or is_resolved %}
                            <p class="text-sm mt-1">جرب تغيير معايير البحث</p>
                            {% endif %}
                        </div>
                    </td>
                </tr>
                {% endif %}
            </tbody>
        </table>
        
        <!-- ترقيم الصفحات -->
        {% if total_pages > 1 %}
        <div class="px-6 py-4 border-t flex justify-center">
            <nav class="flex items-center space-x-2 space-x-reverse">
                {% if page > 1 %}
                <a href="{{ url_for('warehouses.alerts', page=page-1, warehouse_id=warehouse_id, alert_type=alert_type, is_resolved=is_resolved) }}" class="px-3 py-1 rounded border hover:bg-gray-100">
                    السابق
                </a>
                {% endif %}
                
                {% for p in range(1, total_pages + 1) %}
                <a href="{{ url_for('warehouses.alerts', page=p, warehouse_id=warehouse_id, alert_type=alert_type, is_resolved=is_resolved) }}" 
                   class="px-3 py-1 rounded border {% if p == page %}bg-blue-500 text-white{% else %}hover:bg-gray-100{% endif %}">
                    {{ p }}
                </a>
                {% endfor %}
                
                {% if page < total_pages %}
                <a href="{{ url_for('warehouses.alerts', page=page+1, warehouse_id=warehouse_id, alert_type=alert_type, is_resolved=is_resolved) }}" class="px-3 py-1 rounded border hover:bg-gray-100">
                    التالي
                </a>
                {% endif %}
            </nav>
        </div>
        {% endif %}
    </div>
</div>

<script>
    // تحديد التنبيه كمقروء
    function markAsRead(alertId) {
        fetch(`/warehouses/alerts/${alertId}/mark-read`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // تحديث الصفحة
                window.location.reload();
            }
        });
    }
    
    // تحديد التنبيه كمحلول
    function markAsResolved(alertId) {
        fetch(`/warehouses/alerts/${alertId}/mark-resolved`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // تحديث الصفحة
                window.location.reload();
            }
        });
    }
    
    // تحديد جميع التنبيهات كمقروءة
    document.getElementById('markAllReadBtn').addEventListener('click', function() {
        fetch('/warehouses/alerts/mark-all-read', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // تحديث الصفحة
                window.location.reload();
            }
        });
    });
</script>
{% endblock page_content %}
