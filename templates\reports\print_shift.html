<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Nobara - طباعة تقرير الشيفت</title>
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <!-- Remixicon -->
    <link href="https://cdn.jsdelivr.net/npm/remixicon@2.5.0/fonts/remixicon.css" rel="stylesheet">
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: {
                            50: '#eff6ff',
                            100: '#dbeafe',
                            200: '#bfdbfe',
                            300: '#93c5fd',
                            400: '#60a5fa',
                            500: '#3b82f6',
                            600: '#2563eb',
                            700: '#1d4ed8',
                            800: '#1e40af',
                            900: '#1e3a8a',
                            950: '#172554',
                        }
                    }
                }
            }
        }
    </script>
    <style>
        body {
            font-family: 'Cairo', 'Tajawal', sans-serif;
            background-color: white;
        }

        @media print {
            .no-print {
                display: none !important;
            }

            body {
                font-size: 12pt;
            }

            .print-break-inside-avoid {
                break-inside: avoid;
            }

            .print-break-after {
                break-after: page;
            }

            @page {
                size: A4;
                margin: 1cm;
            }
        }

        .receipt {
            max-width: 80mm;
            margin: 0 auto;
            padding: 10px;
            font-size: 12px;
            line-height: 1.4;
        }

        .receipt-header {
            text-align: center;
            margin-bottom: 10px;
        }

        .receipt-logo {
            max-width: 60px;
            margin: 0 auto 10px;
        }

        .receipt-title {
            font-size: 16px;
            font-weight: bold;
            margin-bottom: 5px;
        }

        .receipt-subtitle {
            font-size: 12px;
            margin-bottom: 5px;
        }

        .receipt-info {
            margin-bottom: 10px;
            border-top: 1px dashed #ccc;
            border-bottom: 1px dashed #ccc;
            padding: 5px 0;
        }

        .receipt-table {
            width: 100%;
            margin-bottom: 10px;
            border-collapse: collapse;
        }

        .receipt-table th,
        .receipt-table td {
            padding: 3px 0;
            text-align: right;
        }

        .receipt-table th {
            border-bottom: 1px solid #ccc;
        }

        .receipt-total {
            margin-top: 10px;
            border-top: 1px dashed #ccc;
            padding-top: 5px;
        }

        .receipt-footer {
            text-align: center;
            margin-top: 10px;
            font-size: 10px;
            border-top: 1px dashed #ccc;
            padding-top: 5px;
        }
    </style>
</head>
<body class="bg-gray-100">
    <div class="container mx-auto py-6">
        <div class="bg-white shadow-md rounded-lg overflow-hidden mb-6 max-w-4xl mx-auto">
            <!-- Header -->
            <div class="p-6 border-b border-gray-200">
                <div class="flex justify-between items-center">
                    <h1 class="text-2xl font-bold text-gray-800">تقرير الشيفت</h1>
                    <div class="no-print">
                        <button onclick="window.print()" class="bg-primary-500 hover:bg-primary-600 text-white py-2 px-4 rounded-lg inline-flex items-center transition-colors">
                            <i class="ri-printer-line ml-1"></i>
                            <span>طباعة</span>
                        </button>
                        <a href="{{ url_for('reports.shift_closure_report') }}" class="bg-gray-200 hover:bg-gray-300 text-gray-700 py-2 px-4 rounded-lg inline-flex items-center transition-colors mr-2">
                            <i class="ri-arrow-right-line ml-1"></i>
                            <span>العودة</span>
                        </a>
                    </div>
                </div>
            </div>

            <!-- تقرير A4 -->
            <div class="p-6 print-break-inside-avoid">
                <div class="text-center mb-6">
                    {% if settings and settings.business and settings.business.logo %}
                    <img src="{{ settings.business.logo }}" alt="{{ settings.business.name or 'Nobara' }}" class="h-16 mx-auto mb-2">
                    {% else %}
                    <img src="{{ url_for('static', filename='img/nobara-logo.svg') }}" alt="Nobara" class="h-16 mx-auto mb-2">
                    {% endif %}
                    <h1 class="text-2xl font-bold text-gray-800">تقرير الشيفت</h1>
                    <p class="text-gray-600">رقم الشيفت: {{ shift.id }}</p>
                </div>

                <div class="grid grid-cols-2 gap-4 mb-6">
                    <div class="bg-gray-50 rounded-lg p-4">
                        <h3 class="font-bold text-gray-700 mb-2">معلومات الشيفت</h3>
                        <div class="grid grid-cols-2 gap-2 text-sm">
                            <div class="text-gray-600">الموظف:</div>
                            <div class="font-medium">{{ shift.user.username }}</div>
                            <div class="text-gray-600">الخزينة:</div>
                            <div class="font-medium">{{ shift.cash_register.name }}</div>
                            <div class="text-gray-600">تاريخ البدء:</div>
                            <div class="font-medium">{{ shift.start_time.strftime('%Y-%m-%d %H:%M') }}</div>
                            <div class="text-gray-600">تاريخ الإغلاق:</div>
                            <div class="font-medium">{{ shift.end_time.strftime('%Y-%m-%d %H:%M') if shift.end_time else '-' }}</div>
                            <div class="text-gray-600">الحالة:</div>
                            <div class="font-medium">
                                {% if shift.status == 'open' %}
                                <span class="text-green-600">مفتوح</span>
                                {% else %}
                                <span class="text-red-600">مغلق</span>
                                {% endif %}
                            </div>
                        </div>
                    </div>

                    <div class="bg-gray-50 rounded-lg p-4">
                        <h3 class="font-bold text-gray-700 mb-2">ملخص الشيفت</h3>
                        <div class="grid grid-cols-2 gap-2 text-sm">
                            <div class="text-gray-600">رصيد البداية:</div>
                            <div class="font-medium">{{ "%.2f"|format(shift.start_balance) }} ج.م</div>
                            <div class="text-gray-600">رصيد النهاية:</div>
                            <div class="font-medium">{{ "%.2f"|format(shift.end_balance) if shift.end_balance else '-' }} ج.م</div>
                            <div class="text-gray-600">الرصيد المتوقع:</div>
                            <div class="font-medium">{{ "%.2f"|format(shift.expected_balance) if shift.expected_balance else '-' }} ج.م</div>
                            <div class="text-gray-600">الفرق:</div>
                            <div class="font-medium {% if shift.difference and shift.difference < 0 %}text-red-600{% elif shift.difference and shift.difference > 0 %}text-green-600{% endif %}">
                                {{ "%.2f"|format(shift.difference) if shift.difference else '-' }} ج.م
                            </div>
                            <div class="text-gray-600">ملاحظات:</div>
                            <div class="font-medium">{{ shift.notes or '-' }}</div>
                        </div>
                    </div>
                </div>

                <div class="grid grid-cols-4 gap-4 mb-6">
                    <div class="bg-blue-50 rounded-lg p-4 border border-blue-100">
                        <div class="text-blue-500 text-sm font-medium mb-1">إجمالي المبيعات</div>
                        <div class="text-2xl font-bold text-gray-800">{{ "%.2f"|format(total_sales) }} ج.م</div>
                    </div>
                    <div class="bg-green-50 rounded-lg p-4 border border-green-100">
                        <div class="text-green-500 text-sm font-medium mb-1">المبيعات النقدية</div>
                        <div class="text-2xl font-bold text-gray-800">{{ "%.2f"|format(cash_sales) }} ج.م</div>
                    </div>
                    <div class="bg-purple-50 rounded-lg p-4 border border-purple-100">
                        <div class="text-purple-500 text-sm font-medium mb-1">إجمالي الإيداعات</div>
                        <div class="text-2xl font-bold text-gray-800">{{ "%.2f"|format(deposits) }} ج.م</div>
                    </div>
                    <div class="bg-red-50 rounded-lg p-4 border border-red-100">
                        <div class="text-red-500 text-sm font-medium mb-1">إجمالي السحوبات</div>
                        <div class="text-2xl font-bold text-gray-800">{{ "%.2f"|format(withdrawals) }} ج.م</div>
                    </div>
                </div>

                <!-- المبيعات -->
                <div class="mb-6">
                    <h3 class="font-bold text-gray-800 mb-3">المبيعات</h3>
                    <div class="overflow-x-auto">
                        <table class="w-full border-collapse">
                            <thead>
                                <tr class="bg-gray-100">
                                    <th class="py-2 px-3 text-right border border-gray-200">#</th>
                                    <th class="py-2 px-3 text-right border border-gray-200">التاريخ</th>
                                    <th class="py-2 px-3 text-right border border-gray-200">رقم الفاتورة</th>
                                    <th class="py-2 px-3 text-right border border-gray-200">العميل</th>
                                    <th class="py-2 px-3 text-right border border-gray-200">طريقة الدفع</th>
                                    <th class="py-2 px-3 text-right border border-gray-200">الإجمالي</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for sale in sales %}
                                <tr class="hover:bg-gray-50">
                                    <td class="py-2 px-3 text-right border border-gray-200">{{ sale.id }}</td>
                                    <td class="py-2 px-3 text-right border border-gray-200">{{ sale.created_at.strftime('%Y-%m-%d %H:%M') }}</td>
                                    <td class="py-2 px-3 text-right border border-gray-200">{{ sale.invoice_number }}</td>
                                    <td class="py-2 px-3 text-right border border-gray-200">{{ sale.customer.name if sale.customer else 'عميل نقدي' }}</td>
                                    <td class="py-2 px-3 text-right border border-gray-200">
                                        {% if sale.payment_method == 'cash' %}
                                        <span class="text-green-600">نقدي</span>
                                        {% elif sale.payment_method == 'card' %}
                                        <span class="text-blue-600">بطاقة</span>
                                        {% elif sale.payment_method == 'credit' %}
                                        <span class="text-red-600">آجل</span>
                                        {% else %}
                                        {{ sale.payment_method }}
                                        {% endif %}
                                    </td>
                                    <td class="py-2 px-3 text-right border border-gray-200 font-medium">{{ "%.2f"|format(sale.total) }} ج.م</td>
                                </tr>
                                {% endfor %}
                                {% if not sales %}
                                <tr>
                                    <td colspan="6" class="py-4 text-center text-gray-500 border border-gray-200">لا توجد مبيعات</td>
                                </tr>
                                {% endif %}
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- المعاملات -->
                <div class="mb-6">
                    <h3 class="font-bold text-gray-800 mb-3">معاملات الخزينة</h3>
                    <div class="overflow-x-auto">
                        <table class="w-full border-collapse">
                            <thead>
                                <tr class="bg-gray-100">
                                    <th class="py-2 px-3 text-right border border-gray-200">#</th>
                                    <th class="py-2 px-3 text-right border border-gray-200">التاريخ</th>
                                    <th class="py-2 px-3 text-right border border-gray-200">نوع المعاملة</th>
                                    <th class="py-2 px-3 text-right border border-gray-200">المبلغ</th>
                                    <th class="py-2 px-3 text-right border border-gray-200">الرصيد السابق</th>
                                    <th class="py-2 px-3 text-right border border-gray-200">الرصيد الجديد</th>
                                    <th class="py-2 px-3 text-right border border-gray-200">المرجع</th>
                                    <th class="py-2 px-3 text-right border border-gray-200">ملاحظات</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for transaction in transactions %}
                                <tr class="hover:bg-gray-50">
                                    <td class="py-2 px-3 text-right border border-gray-200">{{ transaction.id }}</td>
                                    <td class="py-2 px-3 text-right border border-gray-200">{{ transaction.created_at.strftime('%Y-%m-%d %H:%M') }}</td>
                                    <td class="py-2 px-3 text-right border border-gray-200">
                                        {% if transaction.transaction_type == 'deposit' %}
                                        <span class="text-green-600">إيداع</span>
                                        {% elif transaction.transaction_type == 'withdraw' %}
                                        <span class="text-red-600">سحب</span>
                                        {% elif transaction.transaction_type == 'transfer' %}
                                        <span class="text-blue-600">تحويل</span>
                                        {% endif %}
                                    </td>
                                    <td class="py-2 px-3 text-right border border-gray-200 font-medium {% if transaction.transaction_type == 'deposit' %}text-green-600{% elif transaction.transaction_type == 'withdraw' %}text-red-600{% else %}text-blue-600{% endif %}">
                                        {{ "%.2f"|format(transaction.amount) }} ج.م
                                    </td>
                                    <td class="py-2 px-3 text-right border border-gray-200">{{ "%.2f"|format(transaction.previous_balance) }} ج.م</td>
                                    <td class="py-2 px-3 text-right border border-gray-200">{{ "%.2f"|format(transaction.new_balance) }} ج.م</td>
                                    <td class="py-2 px-3 text-right border border-gray-200">{{ transaction.reference or '-' }}</td>
                                    <td class="py-2 px-3 text-right border border-gray-200">{{ transaction.notes or '-' }}</td>
                                </tr>
                                {% endfor %}
                                {% if not transactions %}
                                <tr>
                                    <td colspan="8" class="py-4 text-center text-gray-500 border border-gray-200">لا توجد معاملات</td>
                                </tr>
                                {% endif %}
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- التوقيعات -->
                <div class="mt-10 grid grid-cols-3 gap-4">
                    <div class="text-center">
                        <div class="border-t border-gray-300 pt-2 mt-10 mx-auto w-40">
                            <p class="text-gray-600 text-sm">توقيع الموظف</p>
                        </div>
                    </div>
                    <div class="text-center">
                        <div class="border-t border-gray-300 pt-2 mt-10 mx-auto w-40">
                            <p class="text-gray-600 text-sm">توقيع المدير</p>
                        </div>
                    </div>
                    <div class="text-center">
                        <div class="border-t border-gray-300 pt-2 mt-10 mx-auto w-40">
                            <p class="text-gray-600 text-sm">توقيع المحاسب</p>
                        </div>
                    </div>
                </div>

                <div class="text-center mt-10 text-gray-500 text-xs">
                    <p>Powered By ENG/ Fouad Saber - Tel: ***********</p>
                    <p>تم الطباعة في: {{ now.strftime('%Y-%m-%d %H:%M:%S') }}</p>
                </div>
            </div>
        </div>

        <!-- تقرير الإيصال -->
        <div class="receipt print-break-after">
            <div class="receipt-header">
                {% if settings and settings.business and settings.business.logo %}
                <img src="{{ settings.business.logo }}" alt="{{ settings.business.name or 'Nobara' }}" class="receipt-logo">
                <div class="receipt-title">{{ settings.business.name or 'Nobara' }} - نظام نقاط البيع الذكي</div>
                {% else %}
                <img src="{{ url_for('static', filename='img/nobara-logo.svg') }}" alt="Nobara" class="receipt-logo">
                <div class="receipt-title">Nobara - نظام نقاط البيع الذكي</div>
                {% endif %}
                <div class="receipt-subtitle">تقرير إغلاق الشيفت</div>
            </div>

            <div class="receipt-info">
                <div><strong>رقم الشيفت:</strong> {{ shift.id }}</div>
                <div><strong>الموظف:</strong> {{ shift.user.username }}</div>
                <div><strong>الخزينة:</strong> {{ shift.cash_register.name }}</div>
                <div><strong>تاريخ البدء:</strong> {{ shift.start_time.strftime('%Y-%m-%d %H:%M') }}</div>
                <div><strong>تاريخ الإغلاق:</strong> {{ shift.end_time.strftime('%Y-%m-%d %H:%M') if shift.end_time else '-' }}</div>
            </div>

            <table class="receipt-table">
                <tr>
                    <td><strong>رصيد البداية:</strong></td>
                    <td>{{ "%.2f"|format(shift.start_balance) }} ج.م</td>
                </tr>
                <tr>
                    <td><strong>رصيد النهاية:</strong></td>
                    <td>{{ "%.2f"|format(shift.end_balance) if shift.end_balance else '-' }} ج.م</td>
                </tr>
                <tr>
                    <td><strong>الرصيد المتوقع:</strong></td>
                    <td>{{ "%.2f"|format(shift.expected_balance) if shift.expected_balance else '-' }} ج.م</td>
                </tr>
                <tr>
                    <td><strong>الفرق:</strong></td>
                    <td>{{ "%.2f"|format(shift.difference) if shift.difference else '-' }} ج.م</td>
                </tr>
            </table>

            <div style="border-top: 1px dashed #ccc; margin: 5px 0;"></div>

            <table class="receipt-table">
                <tr>
                    <td><strong>إجمالي المبيعات:</strong></td>
                    <td>{{ "%.2f"|format(total_sales) }} ج.م</td>
                </tr>
                <tr>
                    <td><strong>المبيعات النقدية:</strong></td>
                    <td>{{ "%.2f"|format(cash_sales) }} ج.م</td>
                </tr>
                <tr>
                    <td><strong>إجمالي الإيداعات:</strong></td>
                    <td>{{ "%.2f"|format(deposits) }} ج.م</td>
                </tr>
                <tr>
                    <td><strong>إجمالي السحوبات:</strong></td>
                    <td>{{ "%.2f"|format(withdrawals) }} ج.م</td>
                </tr>
            </table>

            <div class="receipt-footer">
                <div>Powered By ENG/ Fouad Saber</div>
                <div>Tel: ***********</div>
                <div>{{ now.strftime('%Y-%m-%d %H:%M:%S') }}</div>
            </div>
        </div>
    </div>

    <script>
        // طباعة تلقائية عند تحميل الصفحة
        window.onload = function() {
            // تأخير الطباعة لضمان تحميل جميع العناصر
            setTimeout(function() {
                window.print();
            }, 500);
        };
    </script>
</body>
</html>
