from flask import Blueprint, jsonify, request
from flask_login import login_required, current_user
from models import User, Permission, UserPermission, UserActivity, UserSession, Role, db
from sqlalchemy import or_, and_
from datetime import datetime, timedelta
from werkzeug.security import generate_password_hash
import secrets
import string
import logging

# إعداد التسجيل
logger = logging.getLogger("api.users")
logger.setLevel(logging.INFO)

users_api = Blueprint('users_api', __name__)

@users_api.route('/api/users', methods=['GET'])
@login_required
def get_users():
    """
    الحصول على قائمة المستخدمين مع دعم البحث والترتيب والترقيم
    
    معلمات الاستعلام:
    - search: نص البحث (اختياري)
    - role: دور المستخدم (admin, staff, all)
    - is_active: حالة المستخدم (true, false, all)
    - sort_by: حقل الترتيب (username, email, full_name, created_at)
    - sort_order: ترتيب تصاعدي أو تنازلي (asc, desc)
    - page: رقم الصفحة (افتراضي: 1)
    - per_page: عدد العناصر في الصفحة (افتراضي: 20)
    """
    try:
        # التحقق من صلاحية المستخدم الحالي
        if not current_user.has_permission('users', 'view'):
            return jsonify({
                'success': False,
                'message': 'ليس لديك صلاحية عرض المستخدمين'
            }), 403
            
        # الحصول على معلمات الاستعلام
        search = request.args.get('search', '')
        role = request.args.get('role', '')
        is_active = request.args.get('is_active', '')
        sort_by = request.args.get('sort_by', 'username')
        sort_order = request.args.get('sort_order', 'asc')
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 20, type=int)
        
        # إعداد الاستعلام الأساسي
        query = User.query
        
        # تطبيق فلتر البحث
        if search:
            query = query.filter(
                or_(
                    User.username.ilike(f'%{search}%'),
                    User.email.ilike(f'%{search}%'),
                    User.full_name.ilike(f'%{search}%')
                )
            )
        
        # تطبيق فلتر الدور
        if role and role != 'all':
            query = query.filter(User.role == role)
        
        # تطبيق فلتر الحالة
        if is_active == 'true':
            query = query.filter(User.is_active == True)
        elif is_active == 'false':
            query = query.filter(User.is_active == False)
        
        # تطبيق الترتيب
        if sort_by in ['username', 'email', 'full_name', 'created_at']:
            if sort_order == 'desc':
                query = query.order_by(getattr(User, sort_by).desc())
            else:
                query = query.order_by(getattr(User, sort_by).asc())
        
        # تطبيق الترقيم
        pagination = query.paginate(page=page, per_page=per_page, error_out=False)
        
        # إعداد البيانات للاستجابة
        users = []
        for user in pagination.items:
            users.append(user.to_dict())
        
        # إعداد معلومات الترقيم
        pagination_info = {
            'page': pagination.page,
            'per_page': pagination.per_page,
            'total_pages': pagination.pages,
            'total_items': pagination.total
        }
        
        return jsonify({
            'success': True,
            'users': users,
            'pagination': pagination_info
        })
    except Exception as e:
        logger.error(f"خطأ في الحصول على قائمة المستخدمين: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'حدث خطأ أثناء جلب قائمة المستخدمين: {str(e)}'
        }), 500

@users_api.route('/api/users/<int:id>', methods=['GET'])
@login_required
def get_user(id):
    """الحصول على تفاصيل مستخدم محدد"""
    try:
        # التحقق من صلاحية المستخدم الحالي
        if not current_user.has_permission('users', 'view'):
            return jsonify({
                'success': False,
                'message': 'ليس لديك صلاحية عرض المستخدمين'
            }), 403
            
        user = User.query.get_or_404(id)
        
        return jsonify({
            'success': True,
            'user': user.to_dict()
        })
    except Exception as e:
        logger.error(f"خطأ في الحصول على تفاصيل المستخدم: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'حدث خطأ أثناء جلب تفاصيل المستخدم: {str(e)}'
        }), 500

@users_api.route('/api/users', methods=['POST'])
@login_required
def create_user():
    """إنشاء مستخدم جديد"""
    try:
        # التحقق من صلاحية المستخدم الحالي
        if not current_user.has_permission('users', 'add'):
            return jsonify({
                'success': False,
                'message': 'ليس لديك صلاحية إضافة مستخدمين'
            }), 403
            
        data = request.json
        
        # التحقق من البيانات المطلوبة
        if not data or 'username' not in data or 'email' not in data or 'password' not in data:
            return jsonify({
                'success': False,
                'message': 'يجب توفير اسم المستخدم والبريد الإلكتروني وكلمة المرور'
            }), 400
            
        # التحقق من وجود اسم المستخدم أو البريد الإلكتروني
        username_exists = User.query.filter_by(username=data['username']).first()
        if username_exists:
            return jsonify({
                'success': False,
                'message': 'اسم المستخدم موجود بالفعل'
            }), 400
            
        email_exists = User.query.filter_by(email=data['email']).first()
        if email_exists:
            return jsonify({
                'success': False,
                'message': 'البريد الإلكتروني موجود بالفعل'
            }), 400
            
        # إنشاء المستخدم الجديد
        user = User(
            username=data['username'],
            email=data['email'],
            full_name=data.get('full_name', ''),
            role=data.get('role', 'staff'),
            phone=data.get('phone', ''),
            position=data.get('position', ''),
            is_active=data.get('is_active', True)
        )
        
        # تعيين كلمة المرور
        user.set_password(data['password'])
        
        db.session.add(user)
        db.session.commit()
        
        # تسجيل النشاط
        user.log_activity(
            activity_type='create',
            description=f'تم إنشاء المستخدم بواسطة {current_user.username}',
            ip_address=request.remote_addr
        )
        db.session.commit()
        
        return jsonify({
            'success': True,
            'message': 'تم إنشاء المستخدم بنجاح',
            'user': user.to_dict()
        })
    except Exception as e:
        db.session.rollback()
        logger.error(f"خطأ في إنشاء المستخدم: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'حدث خطأ أثناء إنشاء المستخدم: {str(e)}'
        }), 500

@users_api.route('/api/users/<int:id>', methods=['PUT'])
@login_required
def update_user(id):
    """تحديث مستخدم موجود"""
    try:
        # التحقق من صلاحية المستخدم الحالي
        if not current_user.has_permission('users', 'edit'):
            return jsonify({
                'success': False,
                'message': 'ليس لديك صلاحية تعديل المستخدمين'
            }), 403
            
        user = User.query.get_or_404(id)
        data = request.json
        
        # التحقق من البيانات المطلوبة
        if not data:
            return jsonify({
                'success': False,
                'message': 'لم يتم توفير بيانات للتحديث'
            }), 400
            
        # التحقق من وجود اسم المستخدم أو البريد الإلكتروني
        if 'username' in data and data['username'] != user.username:
            username_exists = User.query.filter(User.username == data['username'], User.id != id).first()
            if username_exists:
                return jsonify({
                    'success': False,
                    'message': 'اسم المستخدم موجود بالفعل'
                }), 400
                
        if 'email' in data and data['email'] != user.email:
            email_exists = User.query.filter(User.email == data['email'], User.id != id).first()
            if email_exists:
                return jsonify({
                    'success': False,
                    'message': 'البريد الإلكتروني موجود بالفعل'
                }), 400
                
        # تحديث بيانات المستخدم
        if 'username' in data:
            user.username = data['username']
        if 'email' in data:
            user.email = data['email']
        if 'full_name' in data:
            user.full_name = data['full_name']
        if 'role' in data:
            user.role = data['role']
        if 'phone' in data:
            user.phone = data['phone']
        if 'position' in data:
            user.position = data['position']
        if 'is_active' in data:
            user.is_active = data['is_active']
        if 'password' in data and data['password']:
            user.set_password(data['password'])
            
        db.session.commit()
        
        # تسجيل النشاط
        user.log_activity(
            activity_type='update',
            description=f'تم تحديث المستخدم بواسطة {current_user.username}',
            ip_address=request.remote_addr
        )
        db.session.commit()
        
        return jsonify({
            'success': True,
            'message': 'تم تحديث المستخدم بنجاح',
            'user': user.to_dict()
        })
    except Exception as e:
        db.session.rollback()
        logger.error(f"خطأ في تحديث المستخدم: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'حدث خطأ أثناء تحديث المستخدم: {str(e)}'
        }), 500

def register_users_api(app):
    """تسجيل واجهة برمجة التطبيقات للمستخدمين"""
    app.register_blueprint(users_api)
