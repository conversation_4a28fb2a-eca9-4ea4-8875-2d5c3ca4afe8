/**
 * مدير المخزون - مكتبة JavaScript لإدارة المخزون
 * يوفر وظائف متقدمة للبحث والتصفية والتحميل المتأخر للبيانات
 */

class InventoryManager {
    constructor(options = {}) {
        // الإعدادات الافتراضية
        this.options = {
            pageSize: options.pageSize || 20,
            apiEndpoint: options.apiEndpoint || '/api/inventory',
            containerSelector: options.containerSelector || '#inventory-container',
            paginationSelector: options.paginationSelector || '#pagination-container',
            searchSelector: options.searchSelector || '#search-input',
            filterSelector: options.filterSelector || '#filter-form',
            loadingSelector: options.loadingSelector || '#loading-indicator',
            emptySelector: options.emptySelector || '#empty-indicator',
            errorSelector: options.errorSelector || '#error-indicator',
            onItemClick: options.onItemClick || null,
            onDataLoaded: options.onDataLoaded || null,
            warehouseId: options.warehouseId || null,
            renderItem: options.renderItem || this._defaultRenderItem.bind(this),
        };

        // حالة البيانات
        this.state = {
            items: [],
            currentPage: 1,
            totalPages: 1,
            totalItems: 0,
            isLoading: false,
            hasError: false,
            errorMessage: '',
            filters: {
                search: '',
                status: 'all',
                category: '',
                sortBy: 'name',
                sortOrder: 'asc'
            }
        };

        // تهيئة العناصر
        this.container = document.querySelector(this.options.containerSelector);
        this.paginationContainer = document.querySelector(this.options.paginationSelector);
        this.searchInput = document.querySelector(this.options.searchSelector);
        this.filterForm = document.querySelector(this.options.filterSelector);
        this.loadingIndicator = document.querySelector(this.options.loadingSelector);
        this.emptyIndicator = document.querySelector(this.options.emptySelector);
        this.errorIndicator = document.querySelector(this.options.errorSelector);

        // التحقق من وجود العناصر المطلوبة
        if (!this.container) {
            console.error('InventoryManager: Container element not found');
            return;
        }

        // تسجيل أحداث المستمع
        this._registerEventListeners();

        // تحميل البيانات الأولية
        this.loadData();
    }

    /**
     * تحميل بيانات المخزون
     * @param {Object} params - معلمات إضافية للتحميل
     */
    loadData(params = {}) {
        // تحديث حالة التحميل
        this.state.isLoading = true;
        this._updateUI();

        // إعداد معلمات الطلب
        const queryParams = new URLSearchParams({
            page: params.page || this.state.currentPage,
            per_page: this.options.pageSize,
            search: params.search !== undefined ? params.search : this.state.filters.search,
            status: params.status || this.state.filters.status,
            category: params.category || this.state.filters.category,
            sort_by: params.sortBy || this.state.filters.sortBy,
            sort_order: params.sortOrder || this.state.filters.sortOrder
        });

        // إضافة معرف المخزن إذا كان موجودًا
        if (this.options.warehouseId) {
            queryParams.append('warehouse_id', this.options.warehouseId);
        }

        // إجراء طلب API
        fetch(`${this.options.apiEndpoint}?${queryParams.toString()}`)
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP error! Status: ${response.status}`);
                }
                return response.json();
            })
            .then(data => {
                // تحديث حالة البيانات
                this.state.items = data.items || [];
                this.state.currentPage = data.page || 1;
                this.state.totalPages = data.total_pages || 1;
                this.state.totalItems = data.total_items || 0;
                this.state.isLoading = false;
                this.state.hasError = false;

                // تحديث واجهة المستخدم
                this._updateUI();

                // استدعاء دالة رد الاتصال إذا كانت موجودة
                if (this.options.onDataLoaded) {
                    this.options.onDataLoaded(data);
                }
            })
            .catch(error => {
                console.error('Error loading inventory data:', error);
                this.state.isLoading = false;
                this.state.hasError = true;
                this.state.errorMessage = error.message;
                this._updateUI();
            });
    }

    /**
     * تحديث واجهة المستخدم بناءً على حالة البيانات
     * @private
     */
    _updateUI() {
        // إظهار/إخفاء مؤشر التحميل
        if (this.loadingIndicator) {
            this.loadingIndicator.style.display = this.state.isLoading ? 'block' : 'none';
        }

        // إظهار/إخفاء مؤشر الخطأ
        if (this.errorIndicator) {
            this.errorIndicator.style.display = this.state.hasError ? 'block' : 'none';
            if (this.state.hasError) {
                this.errorIndicator.querySelector('.error-message').textContent = this.state.errorMessage;
            }
        }

        // عرض البيانات أو مؤشر فارغ
        if (!this.state.isLoading && !this.state.hasError) {
            if (this.state.items.length === 0) {
                if (this.emptyIndicator) {
                    this.emptyIndicator.style.display = 'block';
                }
                this.container.innerHTML = '';
            } else {
                if (this.emptyIndicator) {
                    this.emptyIndicator.style.display = 'none';
                }
                this._renderItems();
            }

            // تحديث ترقيم الصفحات
            this._renderPagination();
        }
    }

    /**
     * عرض عناصر المخزون
     * @private
     */
    _renderItems() {
        // مسح المحتوى الحالي
        this.container.innerHTML = '';

        // عرض كل عنصر
        this.state.items.forEach(item => {
            const itemElement = this.options.renderItem(item);
            this.container.appendChild(itemElement);

            // إضافة حدث النقر إذا كان موجودًا
            if (this.options.onItemClick) {
                itemElement.addEventListener('click', () => {
                    this.options.onItemClick(item);
                });
            }
        });
    }

    /**
     * عرض ترقيم الصفحات
     * @private
     */
    _renderPagination() {
        if (!this.paginationContainer) return;

        // مسح المحتوى الحالي
        this.paginationContainer.innerHTML = '';

        // إنشاء عناصر ترقيم الصفحات
        if (this.state.totalPages > 1) {
            const paginationNav = document.createElement('nav');
            paginationNav.className = 'flex items-center space-x-2 space-x-reverse';

            // زر الصفحة السابقة
            if (this.state.currentPage > 1) {
                const prevButton = document.createElement('button');
                prevButton.className = 'px-3 py-1 rounded border hover:bg-gray-100';
                prevButton.textContent = 'السابق';
                prevButton.addEventListener('click', () => {
                    this.goToPage(this.state.currentPage - 1);
                });
                paginationNav.appendChild(prevButton);
            }

            // أزرار الصفحات
            for (let i = 1; i <= this.state.totalPages; i++) {
                const pageButton = document.createElement('button');
                pageButton.className = `px-3 py-1 rounded border ${i === this.state.currentPage ? 'bg-blue-500 text-white' : 'hover:bg-gray-100'}`;
                pageButton.textContent = i;
                pageButton.addEventListener('click', () => {
                    this.goToPage(i);
                });
                paginationNav.appendChild(pageButton);
            }

            // زر الصفحة التالية
            if (this.state.currentPage < this.state.totalPages) {
                const nextButton = document.createElement('button');
                nextButton.className = 'px-3 py-1 rounded border hover:bg-gray-100';
                nextButton.textContent = 'التالي';
                nextButton.addEventListener('click', () => {
                    this.goToPage(this.state.currentPage + 1);
                });
                paginationNav.appendChild(nextButton);
            }

            this.paginationContainer.appendChild(paginationNav);
        }
    }

    /**
     * الانتقال إلى صفحة محددة
     * @param {number} page - رقم الصفحة
     */
    goToPage(page) {
        if (page < 1 || page > this.state.totalPages) return;
        this.loadData({ page });
    }

    /**
     * تسجيل أحداث المستمع
     * @private
     */
    _registerEventListeners() {
        // مستمع البحث
        if (this.searchInput) {
            this.searchInput.addEventListener('input', this._debounce(() => {
                const searchValue = this.searchInput.value.trim();
                this.state.filters.search = searchValue;
                this.loadData({ page: 1, search: searchValue });
            }, 300));
        }

        // مستمع نموذج التصفية
        if (this.filterForm) {
            this.filterForm.addEventListener('submit', (event) => {
                event.preventDefault();
                const formData = new FormData(this.filterForm);
                
                // تحديث حالة التصفية
                this.state.filters.status = formData.get('status') || 'all';
                this.state.filters.category = formData.get('category') || '';
                this.state.filters.sortBy = formData.get('sort_by') || 'name';
                this.state.filters.sortOrder = formData.get('sort_order') || 'asc';
                
                // إعادة تحميل البيانات
                this.loadData({ page: 1 });
            });
        }
    }

    /**
     * تأخير تنفيذ الدالة (للبحث)
     * @param {Function} func - الدالة المراد تأخيرها
     * @param {number} wait - وقت الانتظار بالمللي ثانية
     * @returns {Function} - الدالة المؤخرة
     * @private
     */
    _debounce(func, wait) {
        let timeout;
        return function() {
            const context = this;
            const args = arguments;
            clearTimeout(timeout);
            timeout = setTimeout(() => {
                func.apply(context, args);
            }, wait);
        };
    }

    /**
     * عرض عنصر المخزون الافتراضي
     * @param {Object} item - بيانات العنصر
     * @returns {HTMLElement} - عنصر DOM
     * @private
     */
    _defaultRenderItem(item) {
        const itemElement = document.createElement('div');
        itemElement.className = 'bg-white rounded-lg shadow p-4 mb-4';
        
        // تحديد لون الحالة
        let statusClass = 'bg-green-100 text-green-800';
        if (item.quantity <= 0) {
            statusClass = 'bg-red-100 text-red-800';
        } else if (item.quantity <= item.minimum_stock) {
            statusClass = 'bg-yellow-100 text-yellow-800';
        }
        
        itemElement.innerHTML = `
            <div class="flex justify-between items-start mb-2">
                <h3 class="font-bold text-lg">${item.product.name}</h3>
                <span class="${statusClass} text-xs font-medium px-2.5 py-0.5 rounded">
                    ${item.quantity <= 0 ? 'نفذ المخزون' : (item.quantity <= item.minimum_stock ? 'مخزون منخفض' : 'متوفر')}
                </span>
            </div>
            <div class="grid grid-cols-2 gap-2 mb-3 text-sm">
                <div>
                    <span class="text-gray-500">الباركود:</span>
                    <span>${item.product.code || '-'}</span>
                </div>
                <div>
                    <span class="text-gray-500">الكمية:</span>
                    <span class="font-medium">${item.quantity}</span>
                </div>
                <div>
                    <span class="text-gray-500">الحد الأدنى:</span>
                    <span>${item.minimum_stock}</span>
                </div>
                <div>
                    <span class="text-gray-500">المخزن:</span>
                    <span>${item.warehouse.name}</span>
                </div>
            </div>
            <div class="flex space-x-2 space-x-reverse">
                <button type="button" class="edit-btn flex-1 bg-blue-100 text-blue-700 hover:bg-blue-200 px-3 py-2 rounded-md text-sm transition-all">
                    <i class="ri-edit-line ml-1"></i>تعديل
                </button>
                ${item.quantity > 0 ? `
                <button type="button" class="transfer-btn flex-1 bg-indigo-100 text-indigo-700 hover:bg-indigo-200 px-3 py-2 rounded-md text-sm transition-all">
                    <i class="ri-arrow-left-right-line ml-1"></i>نقل
                </button>
                ` : ''}
            </div>
        `;
        
        // إضافة أحداث الأزرار
        const editBtn = itemElement.querySelector('.edit-btn');
        if (editBtn) {
            editBtn.addEventListener('click', (event) => {
                event.stopPropagation();
                this._editItem(item);
            });
        }
        
        const transferBtn = itemElement.querySelector('.transfer-btn');
        if (transferBtn) {
            transferBtn.addEventListener('click', (event) => {
                event.stopPropagation();
                this._transferItem(item);
            });
        }
        
        return itemElement;
    }

    /**
     * تعديل عنصر المخزون
     * @param {Object} item - بيانات العنصر
     * @private
     */
    _editItem(item) {
        // استدعاء وظيفة التعديل العامة إذا كانت موجودة
        if (window.editInventory) {
            window.editInventory(item.product.id, item.product.name, item.quantity, item.minimum_stock);
        }
    }

    /**
     * نقل عنصر المخزون
     * @param {Object} item - بيانات العنصر
     * @private
     */
    _transferItem(item) {
        // استدعاء وظيفة النقل العامة إذا كانت موجودة
        if (window.transferInventory) {
            window.transferInventory(item.product.id, item.product.name, item.quantity);
        }
    }
}

// تصدير الفئة للاستخدام العام
window.InventoryManager = InventoryManager;
