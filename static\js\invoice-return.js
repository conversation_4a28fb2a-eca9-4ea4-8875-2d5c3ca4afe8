/**
 * Invoice Return Functionality
 * Handles retrieving and returning invoices
 */

// Global variables
let returnedInvoice = null;
let returnedItems = [];

/**
 * Initialize invoice return functionality
 */
document.addEventListener('DOMContentLoaded', function() {
    setupInvoiceReturnListeners();
});

/**
 * Setup event listeners for invoice return
 */
function setupInvoiceReturnListeners() {
    const invoiceSearchBtn = document.getElementById('invoice-search-btn');
    const invoiceSearchInput = document.getElementById('invoice-search-input');
    const returnInvoiceBtn = document.getElementById('return-invoice-btn');

    if (invoiceSearchBtn && invoiceSearchInput) {
        // Search for invoice when button is clicked
        invoiceSearchBtn.addEventListener('click', function() {
            const invoiceNumber = invoiceSearchInput.value.trim();
            if (invoiceNumber) {
                searchInvoice(invoiceNumber);
            } else {
                showNotification('يرجى إدخال رقم الفاتورة', 'warning');
            }
        });

        // Search for invoice when Enter key is pressed
        invoiceSearchInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                const invoiceNumber = invoiceSearchInput.value.trim();
                if (invoiceNumber) {
                    searchInvoice(invoiceNumber);
                } else {
                    showNotification('يرجى إدخال رقم الفاتورة', 'warning');
                }
            }
        });
    }

    if (returnInvoiceBtn) {
        // Process invoice return
        returnInvoiceBtn.addEventListener('click', function() {
            if (returnedInvoice && returnedItems.length > 0) {
                processInvoiceReturn();
            } else {
                showNotification('لا توجد فاتورة للإرجاع', 'warning');
            }
        });
    }
}

/**
 * Search for invoice by invoice number
 * @param {string} invoiceNumber - The invoice number to search for
 */
function searchInvoice(invoiceNumber) {
    showNotification('جاري البحث عن الفاتورة...', 'info');

    fetch(`/api/sales/invoice/${invoiceNumber}`)
        .then(response => {
            if (!response.ok) {
                throw new Error('Invoice not found');
            }
            return response.json();
        })
        .then(data => {
            if (data.success) {
                returnedInvoice = data.invoice;
                returnedItems = data.items;
                displayInvoiceDetails(returnedInvoice, returnedItems);
                showNotification('تم العثور على الفاتورة', 'success');
            } else {
                showNotification(data.message || 'حدث خطأ أثناء البحث عن الفاتورة', 'error');
            }
        })
        .catch(error => {
            console.error('Error searching for invoice:', error);
            showNotification('لم يتم العثور على الفاتورة', 'error');
        });
}

/**
 * Display invoice details in the return modal
 * @param {Object} invoice - The invoice object
 * @param {Array} items - The invoice items
 */
function displayInvoiceDetails(invoice, items) {
    const invoiceDetailsContainer = document.getElementById('invoice-details-container');
    const invoiceItemsContainer = document.getElementById('invoice-items-container');

    if (invoiceDetailsContainer) {
        // Display invoice details
        invoiceDetailsContainer.innerHTML = `
            <div class="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg mb-4">
                <div class="flex justify-between items-center mb-2">
                    <span class="text-gray-700 dark:text-gray-300 font-medium">رقم الفاتورة:</span>
                    <span class="text-primary dark:text-blue-400 font-bold">${invoice.invoice_number}</span>
                </div>
                <div class="flex justify-between items-center mb-2">
                    <span class="text-gray-700 dark:text-gray-300 font-medium">تاريخ الفاتورة:</span>
                    <span class="text-gray-800 dark:text-gray-200">${new Date(invoice.created_at).toLocaleString('ar-EG')}</span>
                </div>
                <div class="flex justify-between items-center mb-2">
                    <span class="text-gray-700 dark:text-gray-300 font-medium">العميل:</span>
                    <span class="text-gray-800 dark:text-gray-200">${invoice.customer_name || 'عميل نقدي'}</span>
                </div>
                <div class="flex justify-between items-center">
                    <span class="text-gray-700 dark:text-gray-300 font-medium">إجمالي الفاتورة:</span>
                    <span class="text-primary dark:text-blue-400 font-bold">${invoice.total.toFixed(2)} ج.م</span>
                </div>
            </div>
        `;
    }

    if (invoiceItemsContainer) {
        // Display invoice items
        let itemsHtml = '';

        // Get return reasons
        const returnReasons = [
            { id: 'defective', name: 'منتج معيب' },
            { id: 'wrong_item', name: 'منتج خاطئ' },
            { id: 'customer_dissatisfaction', name: 'عدم رضا العميل' },
            { id: 'size_color_issue', name: 'مشكلة في المقاس أو اللون' },
            { id: 'expired', name: 'منتج منتهي الصلاحية' },
            { id: 'damaged', name: 'منتج تالف' },
            { id: 'other', name: 'سبب آخر' }
        ];

        // Create reasons dropdown HTML
        let reasonsOptionsHtml = '<option value="">اختر سبب الإرجاع</option>';
        returnReasons.forEach(reason => {
            reasonsOptionsHtml += `<option value="${reason.id}">${reason.name}</option>`;
        });

        items.forEach((item, index) => {
            // Check if item can be returned (not already fully returned)
            const availableQuantity = item.quantity - (item.returned_quantity || 0);
            const canReturn = availableQuantity > 0;

            itemsHtml += `
                <div class="bg-white dark:bg-dark-100 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4 mb-3 ${!canReturn ? 'opacity-50' : ''}">
                    <div class="flex justify-between items-start">
                        <div class="flex-1">
                            <h4 class="font-medium text-gray-800 dark:text-white text-sm mb-2">${item.product_name}</h4>
                            <div class="flex items-center justify-between">
                                <div class="text-sm text-gray-600 dark:text-gray-400">
                                    الكمية الأصلية: ${item.quantity}
                                    ${item.returned_quantity ? `<span class="text-red-500 dark:text-red-400 mr-2">(تم إرجاع ${item.returned_quantity})</span>` : ''}
                                </div>
                                <div class="text-sm text-blue-600 dark:text-blue-400 font-medium">${item.price.toFixed(2)} ج.م</div>
                            </div>
                        </div>
                        <div class="flex items-center">
                            <div class="relative">
                                <input type="number" class="return-qty w-16 text-center border border-gray-300 dark:border-gray-600 dark:bg-dark-200 dark:text-white rounded-md"
                                    min="1" max="${availableQuantity}" value="${Math.min(availableQuantity, 1)}" data-index="${index}" ${!canReturn ? 'disabled' : ''}>
                            </div>
                            <div class="ml-2">
                                <input type="checkbox" class="return-item-checkbox w-5 h-5 text-primary"
                                    data-index="${index}" ${canReturn ? 'checked' : 'disabled'}>
                            </div>
                        </div>
                    </div>

                    ${canReturn ? `
                    <div class="mt-3">
                        <select class="return-reason w-full text-sm border border-gray-300 dark:border-gray-600 dark:bg-dark-200 dark:text-white rounded-md p-2" data-index="${index}">
                            ${reasonsOptionsHtml}
                        </select>
                    </div>
                    ` : ''}

                    <div class="flex justify-between items-center mt-3 pt-2 border-t border-gray-100 dark:border-gray-700">
                        <div class="text-xs text-gray-600 dark:text-gray-400">
                            <div>السعر: <span class="font-medium">${item.price.toFixed(2)} ج.م</span></div>
                        </div>
                        <div class="font-bold text-sm bg-primary bg-opacity-10 dark:bg-blue-900/30 px-3 py-1 rounded-full text-primary dark:text-blue-400">
                            ${(item.price * Math.min(availableQuantity, 1)).toFixed(2)} ج.م
                        </div>
                    </div>
                </div>
            `;
        });

        // Add payment method options
        itemsHtml += `
            <div class="bg-white dark:bg-dark-100 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4 mb-3">
                <h4 class="font-medium text-gray-800 dark:text-white text-sm mb-3">طريقة استرداد المبلغ</h4>
                <div class="flex flex-wrap gap-3">
                    <label class="flex items-center">
                        <input type="radio" name="return_payment_method" value="cash" class="w-4 h-4 text-primary" checked>
                        <span class="mr-2 text-sm text-gray-700 dark:text-gray-300">نقدي</span>
                    </label>
                    <label class="flex items-center">
                        <input type="radio" name="return_payment_method" value="card" class="w-4 h-4 text-primary">
                        <span class="mr-2 text-sm text-gray-700 dark:text-gray-300">بطاقة</span>
                    </label>
                    <label class="flex items-center">
                        <input type="radio" name="return_payment_method" value="store_credit" class="w-4 h-4 text-primary">
                        <span class="mr-2 text-sm text-gray-700 dark:text-gray-300">رصيد متجر</span>
                    </label>
                </div>
            </div>

            <div class="bg-white dark:bg-dark-100 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4 mb-3">
                <h4 class="font-medium text-gray-800 dark:text-white text-sm mb-3">ملاحظات</h4>
                <textarea id="return-notes" class="w-full border border-gray-300 dark:border-gray-600 dark:bg-dark-200 dark:text-white rounded-md p-2 text-sm" rows="2" placeholder="أضف ملاحظات حول سبب الإرجاع (اختياري)"></textarea>
            </div>
        `;

        invoiceItemsContainer.innerHTML = itemsHtml;

        // Add event listeners to checkboxes and quantity inputs
        document.querySelectorAll('.return-item-checkbox').forEach(checkbox => {
            checkbox.addEventListener('change', updateReturnTotal);
        });

        document.querySelectorAll('.return-qty').forEach(input => {
            input.addEventListener('change', function() {
                const index = parseInt(this.dataset.index);
                const max = parseInt(this.max);
                let value = parseInt(this.value);

                // Ensure value is within valid range
                if (isNaN(value) || value < 1) {
                    value = 1;
                } else if (value > max) {
                    value = max;
                }

                this.value = value;
                updateReturnTotal();
            });
        });

        // Show return button
        const returnInvoiceBtn = document.getElementById('return-invoice-btn');
        if (returnInvoiceBtn) {
            returnInvoiceBtn.classList.remove('hidden');
        }

        // Update return total
        updateReturnTotal();
    }
}

/**
 * Update the return total based on selected items and quantities
 */
function updateReturnTotal() {
    const returnTotalElement = document.getElementById('return-total');
    if (!returnTotalElement) return;

    let total = 0;

    document.querySelectorAll('.return-item-checkbox').forEach(checkbox => {
        if (checkbox.checked) {
            const index = parseInt(checkbox.dataset.index);
            const item = returnedItems[index];
            const qtyInput = document.querySelector(`.return-qty[data-index="${index}"]`);
            const qty = parseInt(qtyInput.value);

            if (item && !isNaN(qty)) {
                total += item.price * qty;
            }
        }
    });

    returnTotalElement.textContent = `${total.toFixed(2)} ج.م`;
}

/**
 * Process the invoice return
 */
function processInvoiceReturn() {
    // Collect items to return
    const itemsToReturn = [];

    document.querySelectorAll('.return-item-checkbox').forEach(checkbox => {
        if (checkbox.checked) {
            const index = parseInt(checkbox.dataset.index);
            const item = returnedItems[index];
            const qtyInput = document.querySelector(`.return-qty[data-index="${index}"]`);
            const qty = parseInt(qtyInput.value);

            // Get reason if available
            const reasonSelect = document.querySelector(`.return-reason[data-index="${index}"]`);
            const reason = reasonSelect ? reasonSelect.value : '';

            if (item && !isNaN(qty) && qty > 0) {
                itemsToReturn.push({
                    order_item_id: item.id,
                    product_id: item.product_id,
                    quantity: qty,
                    price: item.price,
                    reason: reason
                });
            }
        }
    });

    if (itemsToReturn.length === 0) {
        Swal.fire({
            title: 'تنبيه',
            text: 'يرجى تحديد المنتجات المراد إرجاعها',
            icon: 'warning',
            confirmButtonText: 'حسناً'
        });
        return;
    }

    // Get payment method
    const paymentMethod = document.querySelector('input[name="return_payment_method"]:checked')?.value || 'cash';

    // Get notes
    const notes = document.getElementById('return-notes')?.value || '';

    // Confirm return
    Swal.fire({
        title: 'تأكيد الإرجاع',
        text: `هل أنت متأكد من إرجاع ${itemsToReturn.length} منتج؟`,
        icon: 'question',
        showCancelButton: true,
        confirmButtonText: 'نعم، إرجاع',
        cancelButtonText: 'إلغاء'
    }).then((result) => {
        if (result.isConfirmed) {
            // Show loading
            Swal.fire({
                title: 'جاري معالجة الإرجاع',
                text: 'يرجى الانتظار...',
                allowOutsideClick: false,
                didOpen: () => {
                    Swal.showLoading();
                }
            });

            // Send return request
            fetch('/api/sales/return', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    invoice_id: returnedInvoice.id,
                    invoice_number: returnedInvoice.invoice_number,
                    items: itemsToReturn,
                    payment_method: paymentMethod,
                    notes: notes
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Show success message
                    Swal.fire({
                        title: 'تم الإرجاع بنجاح',
                        html: `
                            <p>تم استرجاع المنتجات وإعادتها للمخزون.</p>
                            <p class="mt-2 text-lg font-bold text-red-500">المبلغ المسترد: ${data.returned_amount.toFixed(2)} ج.م</p>
                            <p class="mt-2">رقم المرتجع: ${data.return_reference || ''}</p>
                        `,
                        icon: 'success',
                        confirmButtonText: 'تم',
                        showDenyButton: data.return_id ? true : false,
                        denyButtonText: 'طباعة إيصال المرتجع'
                    }).then((result) => {
                        // Reset return form
                        resetReturnForm();

                        // Close modal
                        const returnModal = document.getElementById('returnInvoiceModal');
                        if (returnModal) {
                            hideModal('returnInvoiceModal', 'return-modal-content');
                        }

                        if (result.isDenied && data.return_id) {
                            // Open print window
                            window.open(`/returns/print/${data.return_id}`, '_blank');
                        }

                        // Refresh page
                        window.location.reload();
                    });
                } else {
                    Swal.fire({
                        title: 'خطأ',
                        text: data.message || 'حدث خطأ أثناء إرجاع الفاتورة',
                        icon: 'error',
                        confirmButtonText: 'حسناً'
                    });
                }
            })
            .catch(error => {
                console.error('Error processing invoice return:', error);
                Swal.fire({
                    title: 'خطأ',
                    text: 'حدث خطأ أثناء الاتصال بالخادم',
                    icon: 'error',
                    confirmButtonText: 'حسناً'
                });
            });
        }
    });
}

/**
 * Reset the return form
 */
function resetReturnForm() {
    returnedInvoice = null;
    returnedItems = [];

    const invoiceSearchInput = document.getElementById('invoice-search-input');
    if (invoiceSearchInput) {
        invoiceSearchInput.value = '';
    }

    const invoiceDetailsContainer = document.getElementById('invoice-details-container');
    if (invoiceDetailsContainer) {
        invoiceDetailsContainer.innerHTML = '';
    }

    const invoiceItemsContainer = document.getElementById('invoice-items-container');
    if (invoiceItemsContainer) {
        invoiceItemsContainer.innerHTML = '';
    }

    const returnInvoiceBtn = document.getElementById('return-invoice-btn');
    if (returnInvoiceBtn) {
        returnInvoiceBtn.classList.add('hidden');
    }

    const returnTotalElement = document.getElementById('return-total');
    if (returnTotalElement) {
        returnTotalElement.textContent = '0.00 ج.م';
    }
}
