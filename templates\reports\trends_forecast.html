<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تقرير الاتجاهات والتنبؤ - نظام إدارة المبيعات</title>
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <!-- Remixicon -->
    <link href="https://cdn.jsdelivr.net/npm/remixicon@2.5.0/fonts/remixicon.css" rel="stylesheet">
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        .report-card {
            transition: all 0.3s ease;
        }
        .report-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 15px rgba(0, 0, 0, 0.1);
        }
    </style>
</head>
<body class="bg-gray-50">
    <div class="flex h-screen overflow-hidden">
        <!-- Sidebar -->
        {% include 'partials/sidebar.html' %}

        <!-- Main Content -->
        <div class="flex-1 flex flex-col overflow-hidden">
            <!-- Top Navigation -->
            {% include 'partials/topnav.html' %}

            <!-- Content Area -->
            <main class="flex-1 overflow-y-auto p-6">
                <div class="mb-6 flex items-center justify-between">
                    <div>
                        <h1 class="text-2xl font-bold text-gray-800">تقرير الاتجاهات والتنبؤ</h1>
                        <p class="text-gray-600">تحليل اتجاهات المبيعات والتنبؤ بالمبيعات المستقبلية</p>
                    </div>
                    <div class="flex space-x-2 space-x-reverse">
                        <a href="{{ url_for('reports.sales_index') }}" class="px-4 py-2 bg-blue-100 text-blue-700 rounded-md hover:bg-blue-200 transition-all">
                            <i class="ri-arrow-right-line"></i>
                            العودة لتقارير المبيعات
                        </a>
                        <a href="{{ url_for('reports.index') }}" class="px-4 py-2 bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200 transition-all">
                            <i class="ri-home-line"></i>
                            الرئيسية
                        </a>
                    </div>
                </div>

                <!-- Filters -->
                <div class="bg-white p-4 rounded-lg shadow-sm mb-6">
                    <form action="{{ url_for('reports.trends_forecast_report') }}" method="get" class="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <div>
                            <label for="period" class="block text-sm font-medium text-gray-700 mb-1">الفترة الزمنية</label>
                            <select id="period" name="period" class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500">
                                <option value="daily" {% if period == 'daily' %}selected{% endif %}>يومي</option>
                                <option value="weekly" {% if period == 'weekly' %}selected{% endif %}>أسبوعي</option>
                                <option value="monthly" {% if period == 'monthly' %}selected{% endif %}>شهري</option>
                            </select>
                        </div>
                        <div>
                            <label for="forecast_months" class="block text-sm font-medium text-gray-700 mb-1">عدد أشهر التنبؤ</label>
                            <select id="forecast_months" name="forecast_months" class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500">
                                <option value="1" {% if forecast_months == 1 %}selected{% endif %}>1 شهر</option>
                                <option value="3" {% if forecast_months == 3 %}selected{% endif %}>3 أشهر</option>
                                <option value="6" {% if forecast_months == 6 %}selected{% endif %}>6 أشهر</option>
                                <option value="12" {% if forecast_months == 12 %}selected{% endif %}>12 شهر</option>
                            </select>
                        </div>
                        <div class="flex items-end">
                            <button type="submit" class="bg-indigo-600 text-white px-4 py-2 rounded-md hover:bg-indigo-700 transition-all">
                                <i class="ri-filter-3-line ml-1"></i>
                                تطبيق الفلتر
                            </button>
                        </div>
                    </form>
                </div>

                <!-- Trend Summary -->
                <div class="bg-white p-6 rounded-lg shadow-sm mb-6">
                    <h2 class="text-lg font-bold text-gray-800 mb-4">ملخص الاتجاهات</h2>
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                        <div class="p-4 {% if percentage_change >= 0 %}bg-green-50{% else %}bg-red-50{% endif %} rounded-lg">
                            <h3 class="text-sm font-medium text-gray-500 mb-1">التغير في المبيعات</h3>
                            <p class="text-2xl font-bold {% if percentage_change >= 0 %}text-green-600{% else %}text-red-600{% endif %}">
                                {{ "%.2f"|format(percentage_change) }}%
                                {% if percentage_change >= 0 %}
                                    <i class="ri-arrow-up-line"></i>
                                {% else %}
                                    <i class="ri-arrow-down-line"></i>
                                {% endif %}
                            </p>
                            <p class="text-sm text-gray-600">
                                {{ "%.2f"|format(absolute_change) }} ج.م
                            </p>
                        </div>
                        <div class="p-4 {% if avg_growth_rate >= 0 %}bg-green-50{% else %}bg-red-50{% endif %} rounded-lg">
                            <h3 class="text-sm font-medium text-gray-500 mb-1">متوسط معدل النمو</h3>
                            <p class="text-2xl font-bold {% if avg_growth_rate >= 0 %}text-green-600{% else %}text-red-600{% endif %}">
                                {{ "%.2f"|format(avg_growth_rate * 100) }}%
                                {% if avg_growth_rate >= 0 %}
                                    <i class="ri-arrow-up-line"></i>
                                {% else %}
                                    <i class="ri-arrow-down-line"></i>
                                {% endif %}
                            </p>
                        </div>
                        <div class="p-4 bg-indigo-50 rounded-lg">
                            <h3 class="text-sm font-medium text-gray-500 mb-1">التنبؤ للفترة القادمة</h3>
                            <p class="text-2xl font-bold text-indigo-600">
                                {% if forecast_data %}
                                    {{ "%.2f"|format(forecast_data[0].total_amount) }} ج.م
                                {% else %}
                                    0.00 ج.م
                                {% endif %}
                            </p>
                        </div>
                    </div>
                </div>

                <!-- Trend Chart -->
                <div class="bg-white p-6 rounded-lg shadow-sm mb-6">
                    <h2 class="text-lg font-bold text-gray-800 mb-4">رسم بياني للاتجاهات والتنبؤ</h2>
                    <div class="h-80">
                        <canvas id="trendChart"></canvas>
                    </div>
                </div>

                <!-- Top Growing Products -->
                <div class="bg-white rounded-lg shadow-sm overflow-hidden mb-6">
                    <h2 class="text-lg font-bold text-gray-800 p-6 border-b">المنتجات الأكثر نمواً</h2>
                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        المنتج
                                    </th>
                                    <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        الكمية الحالية
                                    </th>
                                    <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        الكمية السابقة
                                    </th>
                                    <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        نسبة التغير
                                    </th>
                                    <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        المبيعات الحالية
                                    </th>
                                    <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        المبيعات السابقة
                                    </th>
                                    <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        نسبة التغير
                                    </th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                {% for product in top_products_data %}
                                <tr class="hover:bg-gray-50">
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                        {{ product.name }}
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                        {{ product.current_quantity }}
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                        {{ product.prev_quantity }}
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium {% if product.quantity_change_pct >= 0 %}text-green-600{% else %}text-red-600{% endif %}">
                                        {{ "%.2f"|format(product.quantity_change_pct) }}%
                                        {% if product.quantity_change_pct >= 0 %}
                                            <i class="ri-arrow-up-line"></i>
                                        {% else %}
                                            <i class="ri-arrow-down-line"></i>
                                        {% endif %}
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                        {{ "%.2f"|format(product.current_amount) }} ج.م
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                        {{ "%.2f"|format(product.prev_amount) }} ج.م
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium {% if product.amount_change_pct >= 0 %}text-green-600{% else %}text-red-600{% endif %}">
                                        {{ "%.2f"|format(product.amount_change_pct) }}%
                                        {% if product.amount_change_pct >= 0 %}
                                            <i class="ri-arrow-up-line"></i>
                                        {% else %}
                                            <i class="ri-arrow-down-line"></i>
                                        {% endif %}
                                    </td>
                                </tr>
                                {% else %}
                                <tr>
                                    <td colspan="7" class="px-6 py-4 text-center text-gray-500">
                                        لا توجد بيانات للفترة المحددة
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- Historical and Forecast Data -->
                <div class="bg-white rounded-lg shadow-sm overflow-hidden">
                    <h2 class="text-lg font-bold text-gray-800 p-6 border-b">البيانات التاريخية والتنبؤات</h2>
                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        التاريخ
                                    </th>
                                    <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        المبيعات
                                    </th>
                                    <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        النوع
                                    </th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                {% for item in historical_data %}
                                <tr class="hover:bg-gray-50">
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                        {{ item.date }}
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                        {{ "%.2f"|format(item.total_amount) }} ج.م
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-blue-100 text-blue-800">
                                            بيانات فعلية
                                        </span>
                                    </td>
                                </tr>
                                {% endfor %}

                                {% for item in forecast_data %}
                                <tr class="hover:bg-gray-50 bg-indigo-50">
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                        {{ item.date }}
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                        {{ "%.2f"|format(item.total_amount) }} ج.م
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-indigo-100 text-indigo-800">
                                            تنبؤ
                                        </span>
                                    </td>
                                </tr>
                                {% endfor %}

                                {% if not historical_data and not forecast_data %}
                                <tr>
                                    <td colspan="3" class="px-6 py-4 text-center text-gray-500">
                                        لا توجد بيانات للفترة المحددة
                                    </td>
                                </tr>
                                {% endif %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <script>
        // Prepare data for chart
        const historicalDates = [
            {% for item in historical_data %}
                '{{ item.date }}'{% if not loop.last %},{% endif %}
            {% endfor %}
        ];

        const historicalValues = [
            {% for item in historical_data %}
                {{ item.total_amount }}{% if not loop.last %},{% endif %}
            {% endfor %}
        ];

        const forecastDates = [
            {% for item in forecast_data %}
                '{{ item.date }}'{% if not loop.last %},{% endif %}
            {% endfor %}
        ];

        const forecastValues = [
            {% for item in forecast_data %}
                {{ item.total_amount }}{% if not loop.last %},{% endif %}
            {% endfor %}
        ];

        // Combine dates for x-axis
        const allDates = [...historicalDates, ...forecastDates];

        // Create datasets
        const historicalDataset = {
            label: 'البيانات التاريخية',
            data: historicalValues.concat(Array(forecastDates.length).fill(null)),
            backgroundColor: 'rgba(59, 130, 246, 0.5)',
            borderColor: 'rgba(59, 130, 246, 1)',
            borderWidth: 1
        };

        const forecastDataset = {
            label: 'التنبؤات',
            data: Array(historicalDates.length).fill(null).concat(forecastValues),
            backgroundColor: 'rgba(99, 102, 241, 0.5)',
            borderColor: 'rgba(99, 102, 241, 1)',
            borderWidth: 1,
            borderDash: [5, 5]
        };

        // Create chart
        const ctx = document.getElementById('trendChart').getContext('2d');
        const trendChart = new Chart(ctx, {
            type: 'line',
            data: {
                labels: allDates,
                datasets: [historicalDataset, forecastDataset]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true,
                        title: {
                            display: true,
                            text: 'المبيعات (ج.م)'
                        }
                    }
                }
            }
        });
    </script>
</body>
</html>
