/**
 * Dark Mode Functionality
 * Handles toggling between light and dark mode
 */

// Check for saved theme preference or use the system preference
document.addEventListener('DOMContentLoaded', function() {
    initTheme();
    setupThemeToggle();
});

/**
 * Initialize theme based on saved preference or system preference
 */
function initTheme() {
    // Check if theme is saved in localStorage
    const savedTheme = localStorage.getItem('theme');
    
    if (savedTheme) {
        // Apply saved theme
        applyTheme(savedTheme);
    } else {
        // Check system preference
        const prefersDarkMode = window.matchMedia('(prefers-color-scheme: dark)').matches;
        const theme = prefersDarkMode ? 'dark' : 'light';
        applyTheme(theme);
    }
}

/**
 * Apply theme to document and update toggle button
 * @param {string} theme - 'dark' or 'light'
 */
function applyTheme(theme) {
    document.body.setAttribute('data-theme', theme);
    localStorage.setItem('theme', theme);
    
    // Update toggle buttons if they exist
    const toggleButtons = document.querySelectorAll('.dark-mode-toggle');
    toggleButtons.forEach(button => {
        button.setAttribute('data-theme', theme);
        
        // Update icon in toggle button
        const toggleThumb = button.querySelector('.toggle-thumb');
        if (toggleThumb) {
            if (theme === 'dark') {
                toggleThumb.innerHTML = '<i class="ri-moon-line"></i>';
            } else {
                toggleThumb.innerHTML = '<i class="ri-sun-line"></i>';
            }
        }
    });
}

/**
 * Toggle between light and dark themes
 */
function toggleTheme() {
    const currentTheme = document.body.getAttribute('data-theme') || 'light';
    const newTheme = currentTheme === 'light' ? 'dark' : 'light';
    applyTheme(newTheme);
}

/**
 * Setup theme toggle buttons
 */
function setupThemeToggle() {
    const toggleButtons = document.querySelectorAll('.dark-mode-toggle');
    
    toggleButtons.forEach(button => {
        button.addEventListener('click', toggleTheme);
    });
}

/**
 * Create a dark mode toggle button
 * @returns {HTMLElement} The created toggle button
 */
function createDarkModeToggle() {
    const toggle = document.createElement('div');
    toggle.className = 'dark-mode-toggle';
    toggle.setAttribute('title', 'تبديل الوضع المظلم / الفاتح');
    
    const currentTheme = document.body.getAttribute('data-theme') || 'light';
    toggle.setAttribute('data-theme', currentTheme);
    
    const thumb = document.createElement('div');
    thumb.className = 'toggle-thumb';
    
    if (currentTheme === 'dark') {
        thumb.innerHTML = '<i class="ri-moon-line"></i>';
    } else {
        thumb.innerHTML = '<i class="ri-sun-line"></i>';
    }
    
    toggle.appendChild(thumb);
    toggle.addEventListener('click', toggleTheme);
    
    return toggle;
}
