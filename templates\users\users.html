<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة المستخدمين - نظام إدارة المبيعات</title>
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com/3.4.16"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#6366F1',
                        secondary: '#10b981',
                        success: '#22c55e',
                        warning: '#f59e0b',
                        danger: '#ef4444',
                        info: '#3b82f6'
                    },
                    container: {
                        center: true,
                        padding: {
                            DEFAULT: '1rem',
                            sm: '2rem',
                            lg: '4rem',
                            xl: '5rem',
                            '2xl': '6rem',
                        },
                    },
                    borderRadius: {
                        'none': '0px',
                        'sm': '4px',
                        DEFAULT: '8px',
                        'md': '12px',
                        'lg': '16px',
                        'xl': '20px',
                        '2xl': '24px',
                        '3xl': '32px',
                        'full': '9999px'
                    }
                }
            }
        }
    </script>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@400;500;700&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Pacifico&display=swap" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/remixicon@3.5.0/fonts/remixicon.css" rel="stylesheet">
    <style>
        :where([class^="ri-"])::before { content: "\f3c2"; }
        body {
            font-family: 'Tajawal', sans-serif;
            background-color: #f9fafb;
        }
        .scrollbar-hide::-webkit-scrollbar {
            display: none;
        }
        .scrollbar-hide {
            -ms-overflow-style: none;
            scrollbar-width: none;
        }
        .bg-pattern {
            background-image: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%236366F1' fill-opacity='0.05'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
        }
        .glass-effect {
            backdrop-filter: blur(8px);
            background: rgba(255, 255, 255, 0.8);
            border: 1px solid rgba(255, 255, 255, 0.3);
            box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.07);
        }
        .pulse-animation {
            animation: pulse 2s infinite;
        }
        @keyframes pulse {
            0% {
                box-shadow: 0 0 0 0 rgba(99, 102, 241, 0.4);
            }
            70% {
                box-shadow: 0 0 0 10px rgba(99, 102, 241, 0);
            }
            100% {
                box-shadow: 0 0 0 0 rgba(99, 102, 241, 0);
            }
        }
        .card-hover {
            transition: all 0.3s ease;
        }
        .card-hover:hover {
            transform: translateY(-5px);
        }
    </style>
</head>
<body class="bg-pattern">
    <div class="flex h-screen overflow-hidden">
        <!-- Sidebar -->
        {% include 'partials/sidebar.html' %}
        
        <!-- Main Content -->
        <div class="flex-1 flex flex-col overflow-hidden">
            <!-- Top Navigation -->
            {% include 'partials/topnav.html' %}
            
            <!-- Content Area -->
            <main class="flex-1 overflow-y-auto p-6">
                <!-- Page Header -->
                <div class="mb-6 flex flex-wrap items-center justify-between">
                    <div>
                        <h1 class="text-2xl font-bold text-gray-800">إدارة المستخدمين</h1>
                        <p class="text-gray-600">إدارة المستخدمين والصلاحيات في النظام</p>
                    </div>
                    {% if current_user.has_permission('users', 'add') %}
                    <a href="{{ url_for('users.create') }}" class="px-4 py-2 bg-primary text-white rounded-md hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary transition-all whitespace-nowrap">
                        <i class="ri-user-add-line"></i>
                        إضافة مستخدم جديد
                    </a>
                    {% endif %}
                </div>
                
                <!-- Stats Cards -->
                <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
                    <div class="bg-gradient-to-br from-indigo-500 to-indigo-600 rounded-xl shadow-lg p-6 text-white card-hover">
                        <div class="flex items-center justify-between">
                            <div>
                                <h3 class="text-indigo-100 text-sm font-medium">إجمالي المستخدمين</h3>
                                <div class="mt-2 flex items-baseline">
                                    <span class="text-3xl font-bold">{{ stats.total_users }}</span>
                                    <span class="mr-2 text-sm text-indigo-100">مستخدم</span>
                                </div>
                            </div>
                            <div class="w-12 h-12 rounded-full bg-white bg-opacity-20 text-white flex items-center justify-center">
                                <i class="ri-user-line text-xl"></i>
                            </div>
                        </div>
                    </div>
                    
                    <div class="bg-gradient-to-br from-yellow-500 to-amber-500 rounded-xl shadow-lg p-6 text-white card-hover">
                        <div class="flex items-center justify-between">
                            <div>
                                <h3 class="text-yellow-100 text-sm font-medium">المديرون</h3>
                                <div class="mt-2 flex items-baseline">
                                    <span class="text-3xl font-bold">{{ stats.admin_count }}</span>
                                    <span class="mr-2 text-sm text-yellow-100">مدير</span>
                                </div>
                            </div>
                            <div class="w-12 h-12 rounded-full bg-white bg-opacity-20 text-white flex items-center justify-center">
                                <i class="ri-shield-user-line text-xl"></i>
                            </div>
                        </div>
                    </div>
                    
                    <div class="bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl shadow-lg p-6 text-white card-hover">
                        <div class="flex items-center justify-between">
                            <div>
                                <h3 class="text-blue-100 text-sm font-medium">الموظفون</h3>
                                <div class="mt-2 flex items-baseline">
                                    <span class="text-3xl font-bold">{{ stats.staff_count }}</span>
                                    <span class="mr-2 text-sm text-blue-100">موظف</span>
                                </div>
                            </div>
                            <div class="w-12 h-12 rounded-full bg-white bg-opacity-20 text-white flex items-center justify-center">
                                <i class="ri-user-star-line text-xl"></i>
                            </div>
                        </div>
                    </div>
                    
                    <!-- المستخدمين المضافين حديثاً -->
                    <div class="bg-white rounded-xl shadow-sm p-6 border border-gray-100 card-hover">
                        <h3 class="text-gray-700 text-sm font-medium mb-3">المستخدمين الجدد</h3>
                        <div class="space-y-3">
                            {% for user in stats.recent_users[:3] %}
                            <div class="flex items-center">
                                <div class="flex-shrink-0 h-8 w-8 flex items-center justify-center rounded-full {{ 'bg-yellow-100 text-yellow-500' if user.role == 'admin' else 'bg-blue-100 text-blue-500' }}">
                                    <span class="text-sm font-medium">{{ user.username[0]|upper }}</span>
                                </div>
                                <div class="mr-3">
                                    <p class="text-sm font-medium text-gray-800">{{ user.full_name or user.username }}</p>
                                    <p class="text-xs text-gray-500">{{ user.created_at.strftime('%Y-%m-%d') }}</p>
                                </div>
                            </div>
                            {% else %}
                            <p class="text-sm text-gray-500">لا يوجد مستخدمين جدد</p>
                            {% endfor %}
                        </div>
                    </div>
                </div>
                
                <!-- Search and Filter -->
                <div class="bg-white p-6 rounded-xl shadow-sm mb-6 border border-gray-100">
                    <div class="flex items-center mb-4">
                        <div class="w-8 h-8 rounded-full bg-indigo-100 text-indigo-600 flex items-center justify-center mr-3">
                            <i class="ri-filter-3-line"></i>
                        </div>
                        <h3 class="text-lg font-bold text-gray-800">بحث وتصفية</h3>
                    </div>
                    
                    <form method="GET" action="{{ url_for('users.index') }}" class="grid grid-cols-1 md:grid-cols-4 gap-4">
                        <div>
                            <label for="search" class="block text-sm font-medium text-gray-700 mb-1">بحث</label>
                            <div class="relative">
                                <div class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                                    <i class="ri-search-line text-gray-400"></i>
                                </div>
                                <input type="text" id="search" name="search" value="{{ search }}" class="pr-10 block w-full bg-gray-50 border border-gray-300 rounded-lg py-2.5 text-sm placeholder-gray-400 focus:outline-none focus:bg-white focus:border-primary focus:ring-1 focus:ring-primary transition-all" placeholder="اسم المستخدم، البريد الإلكتروني، الاسم">
                            </div>
                        </div>
                        
                        <div>
                            <label for="role" class="block text-sm font-medium text-gray-700 mb-1">الصلاحية</label>
                            <div class="relative">
                                <select id="role" name="role" class="block w-full bg-gray-50 border border-gray-300 rounded-lg py-2.5 pr-3 text-sm focus:outline-none focus:bg-white focus:border-primary focus:ring-1 focus:ring-primary appearance-none transition-all">
                                    <option value="">الكل</option>
                                    <option value="admin" {% if role == 'admin' %}selected{% endif %}>مدير</option>
                                    <option value="staff" {% if role == 'staff' %}selected{% endif %}>موظف</option>
                                </select>
                                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                    <i class="ri-arrow-down-s-line text-gray-400"></i>
                                </div>
                            </div>
                        </div>
                        
                        <div class="md:col-span-2 self-end">
                            <div class="flex flex-wrap gap-2">
                                <button type="submit" class="px-5 py-2.5 bg-gradient-to-r from-indigo-600 to-indigo-700 text-white rounded-lg shadow-md hover:shadow-lg focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-all flex items-center gap-2">
                                    <i class="ri-search-line"></i>
                                    بحث
                                </button>
                                <a href="{{ url_for('users.index') }}" class="px-5 py-2.5 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 transition-all flex items-center gap-2">
                                    <i class="ri-refresh-line"></i>
                                    إعادة تعيين
                                </a>
                            </div>
                        </div>
                    </form>
                </div>
                
                <!-- Users Table -->
                <div class="bg-white rounded-xl shadow-sm overflow-hidden border border-gray-100">
                    <div class="p-6 border-b border-gray-100 flex items-center justify-between">
                        <div class="flex items-center">
                            <div class="w-8 h-8 rounded-full bg-indigo-100 text-indigo-600 flex items-center justify-center mr-3">
                                <i class="ri-user-settings-line"></i>
                            </div>
                            <h3 class="text-lg font-bold text-gray-800">قائمة المستخدمين</h3>
                        </div>
                        <div class="text-sm text-gray-500">
                            إجمالي: {{ users.total }}
                        </div>
                    </div>
                    
                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th scope="col" class="px-6 py-4 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        اسم المستخدم
                                    </th>
                                    <th scope="col" class="px-6 py-4 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        البريد الإلكتروني
                                    </th>
                                    <th scope="col" class="px-6 py-4 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        الاسم الكامل
                                    </th>
                                    <th scope="col" class="px-6 py-4 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        الصلاحية
                                    </th>
                                    <th scope="col" class="px-6 py-4 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        تاريخ التسجيل
                                    </th>
                                    <th scope="col" class="px-6 py-4 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        الإجراءات
                                    </th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                {% for user in users.items %}
                                <tr class="hover:bg-gray-50 transition-colors">
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="flex items-center">
                                            <div class="flex-shrink-0 h-10 w-10 flex items-center justify-center rounded-full {{ 'bg-gradient-to-br from-yellow-400 to-yellow-500 text-white' if user.role == 'admin' else 'bg-gradient-to-br from-blue-400 to-blue-500 text-white' }} shadow-sm">
                                                <span class="text-lg font-medium">{{ user.username[0]|upper }}</span>
                                            </div>
                                            <div class="mr-4">
                                                <div class="text-sm font-medium text-gray-900">
                                                    {{ user.username }}
                                                </div>
                                            </div>
                                        </div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm text-gray-900">{{ user.email }}</div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm text-gray-900">{{ user.full_name or '-' }}</div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="px-3 py-1 inline-flex text-xs leading-5 font-semibold rounded-full {{ 'bg-yellow-100 text-yellow-800' if user.role == 'admin' else 'bg-blue-100 text-blue-800' }}">
                                            {{ 'مدير' if user.role == 'admin' else 'موظف' }}
                                        </span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                        {{ user.created_at.strftime('%Y-%m-%d') }}
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                        <div class="flex space-x-2 space-x-reverse">
                                            {% if current_user.has_permission('users', 'edit') %}
                                            <a href="{{ url_for('users.edit', id=user.id) }}" class="text-blue-600 hover:text-blue-900 p-1.5 rounded-full hover:bg-blue-50 transition-all" title="تعديل المستخدم">
                                                <div class="w-5 h-5 flex items-center justify-center">
                                                    <i class="ri-edit-line"></i>
                                                </div>
                                            </a>
                                            <a href="{{ url_for('users.user_permissions', id=user.id) }}" class="text-indigo-600 hover:text-indigo-900 p-1.5 rounded-full hover:bg-indigo-50 transition-all" title="إدارة الصلاحيات">
                                                <div class="w-5 h-5 flex items-center justify-center">
                                                    <i class="ri-shield-keyhole-line"></i>
                                                </div>
                                            </a>
                                            {% endif %}
                                            {% if current_user.has_permission('users', 'delete') and user.id != current_user.id %}
                                            <button type="button" onclick="confirmDelete({{ user.id }}, '{{ user.username }}')" class="text-red-600 hover:text-red-900 p-1.5 rounded-full hover:bg-red-50 transition-all" title="حذف المستخدم">
                                                <div class="w-5 h-5 flex items-center justify-center">
                                                    <i class="ri-delete-bin-line"></i>
                                                </div>
                                            </button>
                                            {% endif %}
                                        </div>
                                    </td>
                                </tr>
                                {% else %}
                                <tr>
                                    <td colspan="6" class="px-6 py-10 text-center">
                                        <div class="flex flex-col items-center">
                                            <div class="w-20 h-20 rounded-full bg-indigo-50 flex items-center justify-center text-indigo-400 mb-4">
                                                <i class="ri-user-line text-4xl"></i>
                                            </div>
                                            <h3 class="text-lg font-medium text-gray-800 mb-2">لا يوجد مستخدمين</h3>
                                            <p class="text-gray-500 max-w-md mb-6">لم يتم إضافة مستخدمين بعد، يمكنك إضافة مستخدمين جدد من خلال زر "إضافة مستخدم جديد".</p>
                                            {% if current_user.has_permission('users', 'add') %}
                                            <a href="{{ url_for('users.create') }}" class="px-5 py-2.5 bg-gradient-to-r from-indigo-600 to-indigo-700 text-white rounded-lg shadow-md hover:shadow-lg transition-all flex items-center gap-2">
                                                <i class="ri-user-add-line"></i>
                                                إضافة مستخدم جديد
                                            </a>
                                            {% endif %}
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    
                    <!-- Pagination -->
                    {% if users.pages > 1 %}
                    <div class="px-6 py-4 border-t border-gray-200">
                        <div class="flex items-center justify-between">
                            <div class="text-sm text-gray-500 flex items-center">
                                <div class="w-8 h-8 rounded-full bg-indigo-100 text-indigo-600 flex items-center justify-center mr-2">
                                    <i class="ri-pages-line"></i>
                                </div>
                                <span>عرض {{ users.page }} من {{ users.pages }} صفحة ({{ users.total }} مستخدم)</span>
                            </div>
                            <div class="flex space-x-2 space-x-reverse">
                                {% if users.has_prev %}
                                <a href="{{ url_for('users.index', page=users.prev_num, search=search, role=role) }}" class="px-4 py-2 bg-white border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-all flex items-center gap-1 shadow-sm">
                                    <i class="ri-arrow-right-s-line"></i>
                                    السابق
                                </a>
                                {% else %}
                                <span class="px-4 py-2 bg-gray-50 text-gray-400 rounded-lg cursor-not-allowed border border-gray-200 flex items-center gap-1">
                                    <i class="ri-arrow-right-s-line"></i>
                                    السابق
                                </span>
                                {% endif %}
                                
                                {% if users.has_next %}
                                <a href="{{ url_for('users.index', page=users.next_num, search=search, role=role) }}" class="px-4 py-2 bg-white border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-all flex items-center gap-1 shadow-sm">
                                    التالي
                                    <i class="ri-arrow-left-s-line"></i>
                                </a>
                                {% else %}
                                <span class="px-4 py-2 bg-gray-50 text-gray-400 rounded-lg cursor-not-allowed border border-gray-200 flex items-center gap-1">
                                    التالي
                                    <i class="ri-arrow-left-s-line"></i>
                                </span>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    {% endif %}
                </div>
            </main>
        </div>
    </div>
    
    <!-- Delete Form (Hidden) -->
    <form id="deleteForm" method="POST" style="display: none;"></form>
    
    <!-- Delete Confirmation Modal -->
    <div id="deleteModal" class="fixed inset-0 bg-black bg-opacity-50 backdrop-blur-sm flex items-center justify-center z-50 hidden transition-all duration-300">
        <div class="bg-white rounded-xl w-80 sm:w-96 overflow-hidden shadow-2xl transform transition-all duration-300 scale-95 opacity-0" id="modalContent">
            <div class="p-6">
                <div class="flex items-center justify-center w-16 h-16 rounded-full bg-gradient-to-br from-red-500 to-red-600 text-white mx-auto mb-5 shadow-lg">
                    <i class="ri-delete-bin-line text-2xl"></i>
                </div>
                <h3 class="text-xl font-bold text-gray-900 text-center mb-2">تأكيد الحذف</h3>
                <p class="text-sm text-gray-600 text-center mb-6">
                    هل أنت متأكد من حذف المستخدم:<br>
                    <span id="deleteUserName" class="font-medium text-red-600"></span>؟
                </p>
                <div class="flex justify-center gap-3">
                    <button id="confirmDelete" class="px-5 py-2.5 bg-gradient-to-r from-red-600 to-red-700 text-white rounded-lg shadow-md hover:shadow-lg focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 transition-all whitespace-nowrap text-sm flex items-center gap-1">
                        <i class="ri-delete-bin-line"></i>
                        نعم، حذف
                    </button>
                    <button id="cancelDelete" class="px-5 py-2.5 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 transition-all whitespace-nowrap text-sm flex items-center gap-1">
                        <i class="ri-close-line"></i>
                        إلغاء
                    </button>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        let userIdToDelete = null;
        const deleteModal = document.getElementById('deleteModal');
        const modalContent = document.getElementById('modalContent');
        const deleteForm = document.getElementById('deleteForm');
        const deleteUserName = document.getElementById('deleteUserName');
        
        function confirmDelete(id, name) {
            userIdToDelete = id;
            deleteUserName.textContent = name;
            
            // عرض النافذة المنبثقة مع تأثير حركي
            deleteModal.classList.remove('hidden');
            setTimeout(() => {
                modalContent.classList.add('scale-100', 'opacity-100');
                modalContent.classList.remove('scale-95', 'opacity-0');
            }, 10);
        }
        
        document.getElementById('confirmDelete').addEventListener('click', function() {
            if (userIdToDelete) {
                // إضافة تأثير حركي عند النقر على زر الحذف
                this.classList.add('animate-pulse');
                
                // إرسال النموذج بعد تأخير قصير لإظهار التأثير
                setTimeout(() => {
                    deleteForm.action = `/users/${userIdToDelete}/delete`;
                    deleteForm.submit();
                }, 300);
            }
        });
        
        document.getElementById('cancelDelete').addEventListener('click', function() {
            closeModal();
        });
        
        document.addEventListener('click', function(event) {
            if (event.target === deleteModal) {
                closeModal();
            }
        });
        
        // إضافة استجابة لمفتاح Escape
        document.addEventListener('keydown', function(event) {
            if (event.key === 'Escape' && !deleteModal.classList.contains('hidden')) {
                closeModal();
            }
        });
        
        function closeModal() {
            // إخفاء النافذة المنبثقة مع تأثير حركي
            modalContent.classList.remove('scale-100', 'opacity-100');
            modalContent.classList.add('scale-95', 'opacity-0');
            
            setTimeout(() => {
                deleteModal.classList.add('hidden');
                userIdToDelete = null;
            }, 300);
        }
    </script>
</body>
</html>
