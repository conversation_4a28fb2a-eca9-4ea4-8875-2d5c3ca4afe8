# استيراد وظائف تسجيل API من الملفات المختلفة
from api.inventory import register_inventory_api

try:
    from api.warehouses import register_warehouses_api
except ImportError:
    def register_warehouses_api(app):
        pass

try:
    from api.inventory_count import register_inventory_count_api
except ImportError:
    def register_inventory_count_api(app):
        pass

try:
    from api.users import register_users_api
except ImportError:
    def register_users_api(app):
        pass

try:
    from api.permissions import register_permissions_api
except ImportError:
    def register_permissions_api(app):
        pass

try:
    from api.profile import register_profile_api
except ImportError:
    def register_profile_api(app):
        pass

try:
    from api.errors import register_errors_api
except ImportError:
    def register_errors_api(app):
        pass

def register_api(app):
    """تسجيل جميع واجهات برمجة التطبيقات"""
    # تسجيل واجهة API للمخزون
    register_inventory_api(app)

    # تسجيل واجهة API للمستودعات
    register_warehouses_api(app)

    # تسجيل واجهة API للجرد الدوري
    register_inventory_count_api(app)

    # تسجيل واجهة API للمستخدمين
    register_users_api(app)

    # تسجيل واجهة API للصلاحيات
    register_permissions_api(app)

    # تسجيل واجهة API للملف الشخصي
    register_profile_api(app)

    # تسجيل واجهة API للأخطاء
    register_errors_api(app)
