{% extends 'layout.html' %}

{% block title %}حركة المخزون{% endblock %}

{% block page_content %}
<div class="container mx-auto px-4 py-8">
    <div class="mb-6 flex justify-between items-center">
        <div>
            <h1 class="text-2xl font-bold">حركة المخزون</h1>
            <p class="text-gray-600">سجل حركة المخزون والتغييرات</p>
        </div>
        <div class="flex space-x-2 space-x-reverse">
            <a href="{{ url_for('warehouses.reports') }}" class="bg-indigo-500 text-white px-4 py-2 rounded-lg hover:bg-indigo-600 transition-all ml-2">
                <i class="ri-file-chart-line ml-1"></i>تقارير المخزون
            </a>
            <a href="{{ url_for('warehouses.index') }}" class="bg-gray-100 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-200 transition-all">
                <i class="ri-arrow-right-line ml-1"></i>العودة للمخازن
            </a>
        </div>
    </div>

    <!-- فلاتر البحث -->
    <div class="bg-white rounded-lg shadow p-4 mb-6">
        <form method="GET" action="{{ url_for('warehouses.movements') }}" class="flex flex-wrap items-center gap-4">
            <div>
                <label class="block text-gray-700 mb-2">المخزن</label>
                <select name="warehouse_id" class="border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500">
                    <option value="">جميع المخازن</option>
                    {% for warehouse in warehouses %}
                    <option value="{{ warehouse.id }}" {% if warehouse_id == warehouse.id|string %}selected{% endif %}>{{ warehouse.name }}</option>
                    {% endfor %}
                </select>
            </div>
            <div>
                <label class="block text-gray-700 mb-2">المنتج</label>
                <select name="product_id" class="border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500">
                    <option value="">جميع المنتجات</option>
                    {% for product in products %}
                    <option value="{{ product.id }}" {% if product_id == product.id|string %}selected{% endif %}>{{ product.name }}</option>
                    {% endfor %}
                </select>
            </div>
            <div>
                <label class="block text-gray-700 mb-2">نوع الحركة</label>
                <select name="movement_type" class="border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500">
                    <option value="">جميع الحركات</option>
                    <option value="add" {% if movement_type == 'add' %}selected{% endif %}>إضافة</option>
                    <option value="remove" {% if movement_type == 'remove' %}selected{% endif %}>سحب</option>
                    <option value="transfer_in" {% if movement_type == 'transfer_in' %}selected{% endif %}>نقل وارد</option>
                    <option value="transfer_out" {% if movement_type == 'transfer_out' %}selected{% endif %}>نقل صادر</option>
                </select>
            </div>
            <div>
                <label class="block text-gray-700 mb-2">من تاريخ</label>
                <input type="date" name="start_date" value="{{ start_date }}" class="border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500">
            </div>
            <div>
                <label class="block text-gray-700 mb-2">إلى تاريخ</label>
                <input type="date" name="end_date" value="{{ end_date }}" class="border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500">
            </div>
            <div class="self-end">
                <button type="submit" class="bg-blue-500 text-white px-4 py-2 rounded-md hover:bg-blue-600 transition-all">
                    تطبيق الفلتر
                </button>
            </div>
        </form>
    </div>

    <!-- جدول حركة المخزون -->
    <div class="bg-white rounded-lg shadow overflow-hidden">
        <div class="p-4 bg-gray-50 border-b flex justify-between items-center">
            <h2 class="text-xl font-semibold">سجل حركة المخزون</h2>
            <a href="{{ url_for('warehouses.export_movements') }}?{{ request.query_string.decode() }}" class="bg-green-100 text-green-700 hover:bg-green-200 px-3 py-1 rounded-md text-sm transition-all">
                <i class="ri-file-excel-line ml-1"></i>تصدير إلى Excel
            </a>
        </div>

        <table class="min-w-full">
            <thead>
                <tr class="bg-gray-100">
                    <th class="px-6 py-3 border-b text-right">التاريخ</th>
                    <th class="px-6 py-3 border-b text-right">المنتج</th>
                    <th class="px-6 py-3 border-b text-right">المخزن</th>
                    <th class="px-6 py-3 border-b text-right">نوع الحركة</th>
                    <th class="px-6 py-3 border-b text-right">الكمية</th>
                    <th class="px-6 py-3 border-b text-right">الكمية السابقة</th>
                    <th class="px-6 py-3 border-b text-right">الكمية الجديدة</th>
                    <th class="px-6 py-3 border-b text-right">المستخدم</th>
                    <th class="px-6 py-3 border-b text-right">ملاحظات</th>
                </tr>
            </thead>
            <tbody>
                {% for movement in movements %}
                <tr class="hover:bg-gray-50 {% if movement.movement_type == 'add' %}bg-green-50{% elif movement.movement_type == 'remove' %}bg-red-50{% elif movement.movement_type == 'transfer_in' %}bg-blue-50{% elif movement.movement_type == 'transfer_out' %}bg-yellow-50{% endif %}">
                    <td class="px-6 py-4 border-b">{{ movement.created_at.strftime('%Y-%m-%d %H:%M') }}</td>
                    <td class="px-6 py-4 border-b font-medium">{{ movement.product.name }}</td>
                    <td class="px-6 py-4 border-b">{{ movement.warehouse.name }}</td>
                    <td class="px-6 py-4 border-b">
                        {% if movement.movement_type == 'add' %}
                        <span class="bg-green-100 text-green-800 text-xs font-medium px-2.5 py-0.5 rounded">إضافة</span>
                        {% elif movement.movement_type == 'remove' %}
                        <span class="bg-red-100 text-red-800 text-xs font-medium px-2.5 py-0.5 rounded">سحب</span>
                        {% elif movement.movement_type == 'transfer_in' %}
                        <span class="bg-blue-100 text-blue-800 text-xs font-medium px-2.5 py-0.5 rounded">نقل وارد</span>
                        {% elif movement.movement_type == 'transfer_out' %}
                        <span class="bg-yellow-100 text-yellow-800 text-xs font-medium px-2.5 py-0.5 rounded">نقل صادر</span>
                        {% endif %}
                    </td>
                    <td class="px-6 py-4 border-b">{{ movement.quantity }}</td>
                    <td class="px-6 py-4 border-b">{{ movement.previous_quantity }}</td>
                    <td class="px-6 py-4 border-b">{{ movement.new_quantity }}</td>
                    <td class="px-6 py-4 border-b">{{ movement.user.username if movement.user else '-' }}</td>
                    <td class="px-6 py-4 border-b text-sm">{{ movement.notes or '-' }}</td>
                </tr>
                {% endfor %}

                {% if movements|length == 0 %}
                <tr>
                    <td colspan="9" class="px-6 py-8 text-center text-gray-500">
                        <div class="flex flex-col items-center">
                            <i class="ri-inbox-line text-4xl mb-2"></i>
                            <p>لا توجد حركات مخزون مسجلة</p>
                            {% if warehouse_id or product_id or movement_type or start_date or end_date %}
                            <p class="text-sm mt-1">جرب تغيير معايير البحث</p>
                            {% endif %}
                        </div>
                    </td>
                </tr>
                {% endif %}
            </tbody>
        </table>

        <!-- ترقيم الصفحات -->
        {% if total_pages > 1 %}
        <div class="px-6 py-4 border-t flex justify-center">
            <nav class="flex items-center space-x-2 space-x-reverse">
                {% if page > 1 %}
                <a href="{{ url_for('warehouses.movements', page=page-1, warehouse_id=warehouse_id, product_id=product_id, movement_type=movement_type, start_date=start_date, end_date=end_date) }}" class="px-3 py-1 rounded border hover:bg-gray-100">
                    السابق
                </a>
                {% endif %}

                {% for p in range(1, total_pages + 1) %}
                <a href="{{ url_for('warehouses.movements', page=p, warehouse_id=warehouse_id, product_id=product_id, movement_type=movement_type, start_date=start_date, end_date=end_date) }}"
                   class="px-3 py-1 rounded border {% if p == page %}bg-blue-500 text-white{% else %}hover:bg-gray-100{% endif %}">
                    {{ p }}
                </a>
                {% endfor %}

                {% if page < total_pages %}
                <a href="{{ url_for('warehouses.movements', page=page+1, warehouse_id=warehouse_id, product_id=product_id, movement_type=movement_type, start_date=start_date, end_date=end_date) }}" class="px-3 py-1 rounded border hover:bg-gray-100">
                    التالي
                </a>
                {% endif %}
            </nav>
        </div>
        {% endif %}
    </div>
</div>
{% endblock page_content %}
