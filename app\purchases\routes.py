"""
Nobara POS System - Purchases Routes
نظام نوبارا لنقاط البيع - مسارات المشتريات
"""

from flask import render_template, request, jsonify, redirect, url_for, flash
from flask_login import login_required, current_user
from app.purchases import bp
import logging

logger = logging.getLogger(__name__)

@bp.route('/')
@login_required
def index():
    """قائمة المشتريات"""
    return render_template('purchases/index.html')

@bp.route('/create')
@login_required
def create():
    """إنشاء مشتريات جديدة"""
    return render_template('purchases/form.html')

@bp.route('/returns')
@login_required
def returns():
    """مرتجع المشتريات"""
    return render_template('purchases/returns.html')

@bp.route('/deferred')
@login_required
def deferred():
    """المشتريات الآجلة"""
    return render_template('purchases/deferred.html')
