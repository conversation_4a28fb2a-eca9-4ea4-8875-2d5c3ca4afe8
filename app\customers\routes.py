"""
Nobara POS System - Customers Routes
نظام نوبارا لنقاط البيع - مسارات العملاء
"""

from flask import render_template, request, jsonify, redirect, url_for, flash
from flask_login import login_required, current_user
from app.customers import bp
from app.models import Customer, Sale, db
import logging

logger = logging.getLogger(__name__)

@bp.route('/')
@login_required
def index():
    """قائمة العملاء"""
    try:
        page = request.args.get('page', 1, type=int)
        per_page = 20
        
        search = request.args.get('search', '').strip()
        
        query = Customer.query
        
        if search:
            query = query.filter(
                db.or_(
                    Customer.name.contains(search),
                    Customer.phone.contains(search),
                    Customer.email.contains(search)
                )
            )
        
        customers = query.order_by(Customer.name).paginate(
            page=page, per_page=per_page, error_out=False
        )
        
        stats = {
            'total_customers': Customer.query.filter_by(is_active=True).count(),
            'new_customers_today': Customer.query.filter(
                Customer.created_at >= datetime.utcnow().date()
            ).count()
        }
        
        return render_template('customers/index.html',
                             customers=customers,
                             stats=stats,
                             search=search)
        
    except Exception as e:
        logger.error(f'Error loading customers: {e}')
        flash('حدث خطأ أثناء تحميل العملاء', 'error')
        return redirect(url_for('main.dashboard'))

@bp.route('/<int:id>')
@login_required
def view(id):
    """عرض تفاصيل العميل"""
    try:
        customer = Customer.query.get_or_404(id)
        recent_sales = Sale.query.filter_by(customer_id=id).order_by(Sale.created_at.desc()).limit(10).all()
        
        return render_template('customers/view.html',
                             customer=customer,
                             recent_sales=recent_sales)
        
    except Exception as e:
        logger.error(f'Error loading customer: {e}')
        flash('حدث خطأ أثناء تحميل العميل', 'error')
        return redirect(url_for('customers.index'))

@bp.route('/create')
@login_required
def create():
    """إنشاء عميل جديد"""
    return render_template('customers/form.html', customer=None, action='create')

@bp.route('/<int:id>/edit')
@login_required
def edit(id):
    """تعديل العميل"""
    customer = Customer.query.get_or_404(id)
    return render_template('customers/form.html', customer=customer, action='edit')
