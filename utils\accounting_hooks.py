"""
وحدة الربط المحاسبي - توفر وظائف لربط العمليات المختلفة بالنظام المحاسبي
"""

from app import db
from utils.accounting_helper import AccountingHelper
from models import Order, Purchase, Payment, ReturnOrder, Expense


def register_accounting_hooks():
    """تسجيل وظائف الربط المحاسبي"""
    # ربط وظائف ما بعد الحفظ
    Order.after_save_hooks.append(order_after_save)
    Purchase.after_save_hooks.append(purchase_after_save)
    Payment.after_save_hooks.append(payment_after_save)
    ReturnOrder.after_save_hooks.append(return_order_after_save)
    Expense.after_save_hooks.append(expense_after_save)


def order_after_save(order, created=False):
    """وظيفة ما بعد حفظ الطلب"""
    # إنشاء قيد محاسبي فقط إذا كان الطلب جديدًا وتم تأكيده
    if created and order.status == 'completed':
        # إنشاء قيد محاسبي للطلب
        journal_entry, message = AccountingHelper.create_sale_journal_entry(order)
        
        if journal_entry:
            # ترحيل القيد المحاسبي
            success, post_message = journal_entry.post(order.user_id)
            
            if not success:
                # تسجيل الخطأ
                print(f"خطأ في ترحيل القيد المحاسبي للطلب {order.id}: {post_message}")
        else:
            # تسجيل الخطأ
            print(f"خطأ في إنشاء القيد المحاسبي للطلب {order.id}: {message}")


def purchase_after_save(purchase, created=False):
    """وظيفة ما بعد حفظ المشتريات"""
    # إنشاء قيد محاسبي فقط إذا كانت المشتريات جديدة وتم تأكيدها
    if created and purchase.status == 'completed':
        # إنشاء قيد محاسبي للمشتريات
        journal_entry, message = AccountingHelper.create_purchase_journal_entry(purchase)
        
        if journal_entry:
            # ترحيل القيد المحاسبي
            success, post_message = journal_entry.post(purchase.user_id)
            
            if not success:
                # تسجيل الخطأ
                print(f"خطأ في ترحيل القيد المحاسبي للمشتريات {purchase.id}: {post_message}")
        else:
            # تسجيل الخطأ
            print(f"خطأ في إنشاء القيد المحاسبي للمشتريات {purchase.id}: {message}")


def payment_after_save(payment, created=False):
    """وظيفة ما بعد حفظ الدفع"""
    # إنشاء قيد محاسبي فقط إذا كان الدفع جديدًا
    if created:
        # إنشاء قيد محاسبي للدفع
        journal_entry, message = AccountingHelper.create_payment_journal_entry(payment)
        
        if journal_entry:
            # ترحيل القيد المحاسبي
            success, post_message = journal_entry.post(payment.user_id)
            
            if not success:
                # تسجيل الخطأ
                print(f"خطأ في ترحيل القيد المحاسبي للدفع {payment.id}: {post_message}")
        else:
            # تسجيل الخطأ
            print(f"خطأ في إنشاء القيد المحاسبي للدفع {payment.id}: {message}")


def return_order_after_save(return_order, created=False):
    """وظيفة ما بعد حفظ المرتجع"""
    # إنشاء قيد محاسبي فقط إذا كان المرتجع جديدًا وتم تأكيده
    if created and return_order.status == 'completed':
        # إنشاء قيد محاسبي للمرتجع
        journal_entry, message = AccountingHelper.create_return_journal_entry(return_order)
        
        if journal_entry:
            # ترحيل القيد المحاسبي
            success, post_message = journal_entry.post(return_order.user_id)
            
            if not success:
                # تسجيل الخطأ
                print(f"خطأ في ترحيل القيد المحاسبي للمرتجع {return_order.id}: {post_message}")
        else:
            # تسجيل الخطأ
            print(f"خطأ في إنشاء القيد المحاسبي للمرتجع {return_order.id}: {message}")


def expense_after_save(expense, created=False):
    """وظيفة ما بعد حفظ المصروفات"""
    # إنشاء قيد محاسبي فقط إذا كانت المصروفات جديدة
    if created:
        # إنشاء قيد محاسبي للمصروفات
        journal_entry, message = AccountingHelper.create_expense_journal_entry(expense)
        
        if journal_entry:
            # ترحيل القيد المحاسبي
            success, post_message = journal_entry.post(expense.user_id)
            
            if not success:
                # تسجيل الخطأ
                print(f"خطأ في ترحيل القيد المحاسبي للمصروفات {expense.id}: {post_message}")
        else:
            # تسجيل الخطأ
            print(f"خطأ في إنشاء القيد المحاسبي للمصروفات {expense.id}: {message}")
