/**
 * Sales Returns System
 * Handles product returns and refunds in the sales page
 */

// Global variables for returns
let selectedInvoiceId = null;
let selectedInvoiceNumber = null;
let returnItems = [];
let selectedReturnPaymentMethod = 'cash';

// Initialize returns functionality
document.addEventListener('DOMContentLoaded', function() {
    // Setup return modal events
    setupReturnModal();
    
    // Setup return payment method selection
    setupReturnPaymentMethods();
});

/**
 * Setup return modal events
 */
function setupReturnModal() {
    // Close modal button
    const closeReturnModal = document.getElementById('close-return-modal');
    if (closeReturnModal) {
        closeReturnModal.addEventListener('click', function() {
            hideReturnModal();
        });
    }

    // Cancel button
    const cancelReturnProducts = document.getElementById('cancel-return-products');
    if (cancelReturnProducts) {
        cancelReturnProducts.addEventListener('click', function() {
            hideReturnModal();
        });
    }

    // Confirm return button
    const confirmReturnProducts = document.getElementById('confirm-return-products');
    if (confirmReturnProducts) {
        confirmReturnProducts.addEventListener('click', processReturnProducts);
    }
}

/**
 * Setup return payment method selection
 */
function setupReturnPaymentMethods() {
    const paymentMethods = document.querySelectorAll('.return-payment-method');
    
    paymentMethods.forEach(method => {
        method.addEventListener('click', function() {
            // Remove active class from all methods
            paymentMethods.forEach(m => m.classList.remove('payment-method-active'));
            
            // Add active class to selected method
            this.classList.add('payment-method-active');
            
            // Update selected payment method
            selectedReturnPaymentMethod = this.getAttribute('data-method');
        });
    });
}

/**
 * Open return modal for a specific invoice
 * @param {string} orderId - The order ID
 * @param {string} invoiceNumber - The invoice number
 */
function openReturnModal(orderId, invoiceNumber) {
    // Store selected invoice
    selectedInvoiceId = orderId;
    selectedInvoiceNumber = invoiceNumber;
    
    // Reset return items
    returnItems = [];
    
    // Show loading
    Swal.fire({
        title: 'جاري تحميل بيانات الفاتورة...',
        text: 'يرجى الانتظار',
        allowOutsideClick: false,
        showConfirmButton: false,
        willOpen: () => {
            Swal.showLoading();
        }
    });
    
    // Fetch invoice data
    fetch(`/api/sales/invoice/${invoiceNumber}`)
        .then(response => response.json())
        .then(data => {
            Swal.close();
            
            if (data.success) {
                const invoice = data.invoice;
                const items = data.items || [];
                
                // Set invoice details
                document.getElementById('return-invoice-number').textContent = invoice.invoice_number;
                document.getElementById('return-customer-name').textContent = invoice.customer_name || 'عميل نقدي';
                document.getElementById('return-invoice-date').textContent = new Date(invoice.created_at).toLocaleString('ar-EG');
                
                // Clear products list
                const productsList = document.getElementById('return-products-list');
                productsList.innerHTML = '';
                
                // Add products to list
                items.forEach(item => {
                    // Skip items that have been fully returned
                    if (item.returned_quantity && item.returned_quantity >= item.quantity) {
                        return;
                    }
                    
                    // Calculate available quantity for return
                    const availableQuantity = item.returned_quantity ? 
                        item.quantity - item.returned_quantity : 
                        item.quantity;
                        
                    if (availableQuantity <= 0) {
                        return;
                    }
                    
                    // Create return item
                    const returnItem = {
                        id: item.id,
                        product_id: item.product_id,
                        name: item.product_name,
                        price: item.price,
                        max_quantity: availableQuantity,
                        quantity: 1,
                        total: item.price
                    };
                    
                    // Add to return items
                    returnItems.push(returnItem);
                    
                    // Add to UI
                    addReturnProductToUI(returnItem, returnItems.length - 1);
                });
                
                // Update total
                updateReturnTotal();
                
                // Show modal
                showReturnModal();
            } else {
                Swal.fire({
                    icon: 'error',
                    title: 'خطأ في استرجاع الفاتورة',
                    text: data.message || 'حدث خطأ أثناء استرجاع الفاتورة',
                    confirmButtonText: 'حسناً'
                });
            }
        })
        .catch(error => {
            console.error('Error fetching invoice:', error);
            Swal.fire({
                icon: 'error',
                title: 'خطأ في الاتصال',
                text: 'حدث خطأ أثناء الاتصال بالخادم',
                confirmButtonText: 'حسناً'
            });
        });
}

/**
 * Add return product to UI
 * @param {Object} item - The return item
 * @param {number} index - The item index in returnItems array
 */
function addReturnProductToUI(item, index) {
    const productsList = document.getElementById('return-products-list');
    const template = document.getElementById('sales-return-product-template');
    
    // Clone template
    const clone = template.content.cloneNode(true);
    
    // Set product data
    clone.querySelector('.return-product-name').textContent = item.name;
    clone.querySelector('.return-product-price').textContent = `${item.price.toFixed(2)} ج.م`;
    
    const quantityInput = clone.querySelector('.return-quantity');
    quantityInput.value = item.quantity;
    quantityInput.max = item.max_quantity;
    quantityInput.setAttribute('data-index', index);
    
    clone.querySelector('.return-product-total').textContent = `${item.total.toFixed(2)} ج.م`;
    
    // Add event listeners
    const decreaseBtn = clone.querySelector('.return-quantity-decrease');
    decreaseBtn.addEventListener('click', function() {
        updateReturnItemQuantity(index, Math.max(1, item.quantity - 1));
    });
    
    const increaseBtn = clone.querySelector('.return-quantity-increase');
    increaseBtn.addEventListener('click', function() {
        updateReturnItemQuantity(index, Math.min(item.max_quantity, item.quantity + 1));
    });
    
    quantityInput.addEventListener('change', function() {
        const newQuantity = parseInt(this.value) || 1;
        updateReturnItemQuantity(index, Math.min(Math.max(1, newQuantity), item.max_quantity));
    });
    
    // Add to list
    productsList.appendChild(clone);
}

/**
 * Update return item quantity
 * @param {number} index - The item index in returnItems array
 * @param {number} newQuantity - The new quantity
 */
function updateReturnItemQuantity(index, newQuantity) {
    // Update item
    returnItems[index].quantity = newQuantity;
    returnItems[index].total = returnItems[index].price * newQuantity;
    
    // Update UI
    const productsList = document.getElementById('return-products-list');
    const items = productsList.querySelectorAll('.return-product-item');
    
    if (items[index]) {
        const quantityInput = items[index].querySelector('.return-quantity');
        quantityInput.value = newQuantity;
        
        const totalElement = items[index].querySelector('.return-product-total');
        totalElement.textContent = `${returnItems[index].total.toFixed(2)} ج.م`;
    }
    
    // Update total
    updateReturnTotal();
}

/**
 * Update return total amount
 */
function updateReturnTotal() {
    const total = returnItems.reduce((sum, item) => sum + item.total, 0);
    document.getElementById('return-total-amount').textContent = `${total.toFixed(2)} ج.م`;
}

/**
 * Process return products
 */
function processReturnProducts() {
    // Validate return items
    if (returnItems.length === 0) {
        Swal.fire({
            icon: 'error',
            title: 'لا توجد منتجات للاسترجاع',
            text: 'يرجى اختيار منتج واحد على الأقل للاسترجاع',
            confirmButtonText: 'حسناً'
        });
        return;
    }
    
    // Prepare data
    const returnData = {
        invoice_id: selectedInvoiceId,
        invoice_number: selectedInvoiceNumber,
        items: returnItems.map(item => ({
            order_item_id: item.id,
            product_id: item.product_id,
            quantity: item.quantity
        })),
        payment_method: selectedReturnPaymentMethod,
        notes: document.getElementById('return-notes').value
    };
    
    // Show loading
    Swal.fire({
        title: 'جاري معالجة الاسترجاع...',
        text: 'يرجى الانتظار',
        allowOutsideClick: false,
        showConfirmButton: false,
        willOpen: () => {
            Swal.showLoading();
        }
    });
    
    // Send request to API
    fetch('/api/sales/return', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(returnData)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Hide modal
            hideReturnModal();
            
            // Show success notification
            Swal.fire({
                icon: 'success',
                title: 'تم الاسترجاع بنجاح',
                text: `تم استرجاع المنتجات بنجاح. المبلغ المسترد: ${data.returned_amount.toFixed(2)} ج.م`,
                confirmButtonText: 'حسناً'
            }).then(() => {
                // Reload page to show updated data
                window.location.reload();
            });
        } else {
            Swal.fire({
                icon: 'error',
                title: 'خطأ في الاسترجاع',
                text: data.message || 'حدث خطأ أثناء استرجاع المنتجات',
                confirmButtonText: 'حسناً'
            });
        }
    })
    .catch(error => {
        console.error('Error processing return:', error);
        Swal.fire({
            icon: 'error',
            title: 'خطأ في الاتصال',
            text: 'حدث خطأ أثناء الاتصال بالخادم',
            confirmButtonText: 'حسناً'
        });
    });
}

/**
 * Show return modal
 */
function showReturnModal() {
    const modal = document.getElementById('returnProductsModal');
    const modalContent = document.getElementById('return-products-modal-content');
    
    modal.classList.remove('hidden');
    
    setTimeout(() => {
        modalContent.classList.remove('scale-95', 'opacity-0');
        modalContent.classList.add('scale-100', 'opacity-100');
    }, 10);
}

/**
 * Hide return modal
 */
function hideReturnModal() {
    const modal = document.getElementById('returnProductsModal');
    const modalContent = document.getElementById('return-products-modal-content');
    
    modalContent.classList.remove('scale-100', 'opacity-100');
    modalContent.classList.add('scale-95', 'opacity-0');
    
    setTimeout(() => {
        modal.classList.add('hidden');
    }, 300);
}
