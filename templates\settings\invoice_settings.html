<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Nobara - إعدادات الفواتير</title>
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <!-- Remixicon -->
    <link href="https://cdn.jsdelivr.net/npm/remixicon@2.5.0/fonts/remixicon.css" rel="stylesheet">
    <!-- Alpine.js -->
    <script defer src="https://cdn.jsdelivr.net/npm/alpinejs@3.x.x/dist/cdn.min.js"></script>
    <script>
        tailwind.config = {
            darkMode: 'class',
            theme: {
                extend: {
                    colors: {
                        primary: {
                            50: '#eff6ff',
                            100: '#dbeafe',
                            200: '#bfdbfe',
                            300: '#93c5fd',
                            400: '#60a5fa',
                            500: '#3b82f6',
                            600: '#2563eb',
                            700: '#1d4ed8',
                            800: '#1e40af',
                            900: '#1e3a8a',
                            950: '#172554',
                        },
                        dark: {
                            100: '#1E293B',
                            200: '#0F172A',
                            300: '#0B1120',
                            400: '#060A15'
                        }
                    }
                }
            }
        }
    </script>
    <style>
        body {
            font-family: 'Cairo', 'Tajawal', sans-serif;
            transition: background-color 0.3s ease, color 0.3s ease;
        }

        .bg-pattern {
            background-color: #f9fafb;
            background-image: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%233B82F6' fill-opacity='0.05'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
        }

        .dark .bg-pattern {
            background-color: #0F172A;
            background-image: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%233B82F6' fill-opacity='0.1'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
        }

        .powered-by {
            position: fixed;
            bottom: 1rem;
            left: 1rem;
            background-color: #ffffff;
            border-radius: 1rem;
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
            padding: 0.75rem 1rem;
            display: flex;
            align-items: center;
            z-index: 40;
            transition: all 0.3s ease;
            border: 1px solid #e5e7eb;
        }

        .dark .powered-by {
            background-color: #1E293B;
            border-color: #2D3748;
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.3), 0 4px 6px -2px rgba(0, 0, 0, 0.2);
        }

        .powered-by:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
        }

        .dark .powered-by:hover {
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.3), 0 10px 10px -5px rgba(0, 0, 0, 0.2);
        }

        /* تخصيص معاينة الفاتورة */
        .invoice-preview {
            font-family: 'Courier New', monospace;
            font-size: 12px;
            line-height: 1.4;
            padding: 10px;
            background-color: white;
            border: 1px solid #e5e7eb;
            border-radius: 0.5rem;
            box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
        }

        .dark .invoice-preview {
            background-color: #1f2937;
            border-color: #374151;
        }

        .invoice-preview-header {
            text-align: center;
            margin-bottom: 10px;
            padding-bottom: 10px;
            border-bottom: 1px dashed #e5e7eb;
        }

        .dark .invoice-preview-header {
            border-color: #374151;
        }

        .invoice-preview-items {
            margin-bottom: 10px;
            padding-bottom: 10px;
            border-bottom: 1px dashed #e5e7eb;
        }

        .dark .invoice-preview-items {
            border-color: #374151;
        }

        .invoice-preview-footer {
            text-align: center;
            margin-top: 10px;
            padding-top: 10px;
            border-top: 1px dashed #e5e7eb;
        }

        .dark .invoice-preview-footer {
            border-color: #374151;
        }
    </style>
</head>
<body class="bg-pattern dark:bg-dark-200 dark:text-gray-100" x-data="{ darkMode: localStorage.getItem('darkMode') === 'enabled' }">
    <div class="flex h-screen overflow-hidden">
        <!-- Sidebar -->
        {% include 'partials/sidebar.html' %}

        <!-- Main Content -->
        <div class="flex-1 flex flex-col overflow-hidden">
            <!-- Top Navigation -->
            {% include 'partials/topnav.html' %}

            <!-- Content Area -->
            <main class="flex-1 overflow-y-auto p-6">
                <div class="mb-6 flex justify-between items-center">
                    <div>
                        <h1 class="text-2xl font-bold text-gray-800 dark:text-white">إعدادات الفواتير</h1>
                        <p class="text-gray-600 dark:text-gray-400">تخصيص تنسيق الفواتير وترويسة وتذييل الفاتورة</p>
                    </div>
                    <div class="flex items-center gap-2">
                        <a href="{{ url_for('settings.index') }}" class="flex items-center gap-1 bg-gray-100 dark:bg-dark-100 hover:bg-gray-200 dark:hover:bg-dark-300 text-gray-700 dark:text-gray-300 py-2 px-4 rounded-lg transition-colors">
                            <i class="ri-arrow-right-line"></i>
                            <span>العودة للإعدادات</span>
                        </a>
                        <button @click="darkMode = !darkMode; localStorage.setItem('darkMode', darkMode ? 'enabled' : 'disabled'); document.documentElement.classList.toggle('dark')" class="p-2 rounded-full bg-gray-100 dark:bg-dark-100 hover:bg-gray-200 dark:hover:bg-dark-300 transition-colors">
                            <i class="ri-moon-line dark:hidden"></i>
                            <i class="ri-sun-line hidden dark:block"></i>
                        </button>
                    </div>
                </div>

                <div class="bg-white dark:bg-dark-100 rounded-xl shadow-sm overflow-hidden mb-6">
                    <div class="p-6">
                        <form id="invoiceSettingsForm" action="{{ url_for('settings.update_invoice_settings') }}" method="POST">
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div>
                                    <h3 class="text-lg font-medium text-gray-800 dark:text-white mb-4 flex items-center">
                                        <i class="ri-bill-line ml-2 text-primary-500"></i>
                                        إعدادات الفاتورة
                                    </h3>

                                    <!-- ترويسة الفاتورة -->
                                    <div class="mb-4">
                                        <label for="header" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">ترويسة الفاتورة</label>
                                        <textarea id="header" name="header" rows="2" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 dark:bg-dark-200 dark:text-gray-100">{{ settings.receipt.header }}</textarea>
                                        <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">النص الذي سيظهر في أعلى الفاتورة</p>
                                    </div>

                                    <!-- تذييل الفاتورة -->
                                    <div class="mb-4">
                                        <label for="footer" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">تذييل الفاتورة</label>
                                        <textarea id="footer" name="footer" rows="2" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 dark:bg-dark-200 dark:text-gray-100">{{ settings.receipt.footer }}</textarea>
                                        <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">النص الذي سيظهر في أسفل الفاتورة</p>
                                    </div>

                                    <!-- عرض الضريبة -->
                                    <div class="mb-4">
                                        <label class="flex items-center">
                                            <input type="checkbox" id="show_tax" name="show_tax" {% if settings.receipt.show_tax %}checked{% endif %} class="w-4 h-4 text-primary-600 border-gray-300 rounded focus:ring-primary-500 dark:bg-dark-200 dark:border-gray-600">
                                            <span class="mr-2 text-sm text-gray-700 dark:text-gray-300">عرض الضريبة في الفاتورة</span>
                                        </label>
                                    </div>

                                    <!-- عرض الخصم -->
                                    <div class="mb-4">
                                        <label class="flex items-center">
                                            <input type="checkbox" id="show_discount" name="show_discount" {% if settings.receipt.show_discount %}checked{% endif %} class="w-4 h-4 text-primary-600 border-gray-300 rounded focus:ring-primary-500 dark:bg-dark-200 dark:border-gray-600">
                                            <span class="mr-2 text-sm text-gray-700 dark:text-gray-300">عرض الخصم في الفاتورة</span>
                                        </label>
                                    </div>

                                    <!-- طباعة نسخة مكررة -->
                                    <div class="mb-4">
                                        <label class="flex items-center">
                                            <input type="checkbox" id="print_duplicate" name="print_duplicate" {% if settings.receipt.print_duplicate %}checked{% endif %} class="w-4 h-4 text-primary-600 border-gray-300 rounded focus:ring-primary-500 dark:bg-dark-200 dark:border-gray-600">
                                            <span class="mr-2 text-sm text-gray-700 dark:text-gray-300">طباعة نسخة مكررة من الفاتورة</span>
                                        </label>
                                    </div>

                                    <!-- عرض رقم الهاتف -->
                                    <div class="mb-4">
                                        <label class="flex items-center">
                                            <input type="checkbox" id="show_phone" name="show_phone" {% if settings.receipt.show_phone %}checked{% endif %} class="w-4 h-4 text-primary-600 border-gray-300 rounded focus:ring-primary-500 dark:bg-dark-200 dark:border-gray-600">
                                            <span class="mr-2 text-sm text-gray-700 dark:text-gray-300">عرض رقم الهاتف في الفاتورة</span>
                                        </label>
                                    </div>

                                    <!-- عرض العنوان -->
                                    <div class="mb-4">
                                        <label class="flex items-center">
                                            <input type="checkbox" id="show_address" name="show_address" {% if settings.receipt.show_address %}checked{% endif %} class="w-4 h-4 text-primary-600 border-gray-300 rounded focus:ring-primary-500 dark:bg-dark-200 dark:border-gray-600">
                                            <span class="mr-2 text-sm text-gray-700 dark:text-gray-300">عرض العنوان في الفاتورة</span>
                                        </label>
                                    </div>

                                    <!-- عرض الشعار -->
                                    <div class="mb-4">
                                        <label class="flex items-center">
                                            <input type="checkbox" id="show_logo" name="show_logo" {% if settings.receipt.show_logo %}checked{% endif %} class="w-4 h-4 text-primary-600 border-gray-300 rounded focus:ring-primary-500 dark:bg-dark-200 dark:border-gray-600">
                                            <span class="mr-2 text-sm text-gray-700 dark:text-gray-300">عرض الشعار في الفاتورة</span>
                                        </label>
                                    </div>

                                    <!-- عرض الباركود -->
                                    <div class="mb-4">
                                        <label class="flex items-center">
                                            <input type="checkbox" id="show_barcode" name="show_barcode" {% if settings.receipt.show_barcode %}checked{% endif %} class="w-4 h-4 text-primary-600 border-gray-300 rounded focus:ring-primary-500 dark:bg-dark-200 dark:border-gray-600">
                                            <span class="mr-2 text-sm text-gray-700 dark:text-gray-300">عرض باركود الفاتورة</span>
                                        </label>
                                    </div>

                                    <!-- عرض رقم الضريبي -->
                                    <div class="mb-4">
                                        <label class="flex items-center">
                                            <input type="checkbox" id="show_tax_number" name="show_tax_number" {% if settings.receipt.show_tax_number %}checked{% endif %} class="w-4 h-4 text-primary-600 border-gray-300 rounded focus:ring-primary-500 dark:bg-dark-200 dark:border-gray-600">
                                            <span class="mr-2 text-sm text-gray-700 dark:text-gray-300">عرض الرقم الضريبي</span>
                                        </label>
                                    </div>

                                    <!-- عرض توقيع العميل -->
                                    <div class="mb-4">
                                        <label class="flex items-center">
                                            <input type="checkbox" id="show_customer_signature" name="show_customer_signature" {% if settings.receipt.show_customer_signature %}checked{% endif %} class="w-4 h-4 text-primary-600 border-gray-300 rounded focus:ring-primary-500 dark:bg-dark-200 dark:border-gray-600">
                                            <span class="mr-2 text-sm text-gray-700 dark:text-gray-300">عرض مكان توقيع العميل</span>
                                        </label>
                                    </div>

                                    <!-- حجم الفاتورة -->
                                    <div class="mb-4">
                                        <label for="receipt_size" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">حجم الفاتورة</label>
                                        <select id="receipt_size" name="receipt_size" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 dark:bg-dark-200 dark:text-gray-100">
                                            <option value="58mm" {% if settings.receipt.receipt_size == '58mm' %}selected{% endif %}>58mm (صغير)</option>
                                            <option value="80mm" {% if settings.receipt.receipt_size == '80mm' %}selected{% endif %}>80mm (متوسط)</option>
                                            <option value="a5" {% if settings.receipt.receipt_size == 'a5' %}selected{% endif %}>A5 (متوسط)</option>
                                            <option value="a4" {% if settings.receipt.receipt_size == 'a4' %}selected{% endif %}>A4 (كبير)</option>
                                            <option value="custom" {% if settings.receipt.receipt_size == 'custom' %}selected{% endif %}>حجم مخصص</option>
                                        </select>
                                    </div>

                                    <!-- أبعاد مخصصة للفاتورة -->
                                    <div id="custom_size_container" class="mb-4 {% if settings.receipt.receipt_size != 'custom' %}hidden{% endif %}">
                                        <div class="grid grid-cols-2 gap-4">
                                            <div>
                                                <label for="custom_width" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">العرض (مم)</label>
                                                <input type="number" id="custom_width" name="custom_width" value="{{ settings.receipt.custom_width|default(80) }}" min="30" max="300" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 dark:bg-dark-200 dark:text-gray-100">
                                            </div>
                                            <div>
                                                <label for="custom_height" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">الارتفاع (مم)</label>
                                                <input type="number" id="custom_height" name="custom_height" value="{{ settings.receipt.custom_height|default(200) }}" min="50" max="500" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 dark:bg-dark-200 dark:text-gray-100">
                                            </div>
                                        </div>
                                    </div>

                                    <div class="mt-6">
                                        <button type="submit" class="bg-primary-500 hover:bg-primary-600 text-white py-2 px-6 rounded-lg inline-flex items-center transition-colors">
                                            <i class="ri-save-line ml-1"></i>
                                            <span>حفظ الإعدادات</span>
                                        </button>
                                    </div>
                                </div>

                                <div>
                                    <h3 class="text-lg font-medium text-gray-800 dark:text-white mb-4 flex items-center">
                                        <i class="ri-file-list-3-line ml-2 text-primary-500"></i>
                                        معاينة الفاتورة
                                    </h3>

                                    <div class="flex items-center justify-between mb-4">
                                        <div class="text-sm text-gray-700 dark:text-gray-300">
                                            <span id="preview-size-label">حجم الفاتورة: 80mm</span>
                                        </div>
                                        <button type="button" id="refresh-preview" class="bg-gray-100 dark:bg-dark-300 hover:bg-gray-200 dark:hover:bg-dark-400 text-gray-700 dark:text-gray-300 py-1 px-3 rounded-lg text-sm transition-colors">
                                            <i class="ri-refresh-line ml-1"></i>
                                            تحديث المعاينة
                                        </button>
                                    </div>

                                    <div id="invoice-preview-container" class="overflow-auto border border-gray-200 dark:border-gray-700 rounded-lg">
                                        <div class="invoice-preview dark:text-gray-300" id="invoice-preview">
                                            <div class="invoice-preview-header">
                                                <div id="preview-logo" class="mb-2 text-center">
                                                    {% if settings.business.logo and settings.receipt.show_logo %}
                                                    <div>[شعار المتجر]</div>
                                                    {% endif %}
                                                </div>
                                                <div id="preview-store-name" class="font-bold">{{ settings.business.name or 'اسم المتجر' }}</div>
                                                <div id="preview-tax-number" {% if not settings.receipt.show_tax_number %}class="hidden"{% endif %}>الرقم الضريبي: {{ settings.business.tax_number or '*********' }}</div>
                                                <div id="preview-address" {% if not settings.receipt.show_address %}class="hidden"{% endif %}>{{ settings.business.address or 'عنوان المتجر' }}</div>
                                                <div id="preview-phone" {% if not settings.receipt.show_phone %}class="hidden"{% endif %}>هاتف: {{ settings.business.phone or '***********' }}</div>
                                                <div id="preview-header">{{ settings.receipt.header or 'شكراً لتسوقكم معنا' }}</div>
                                                <div>-----------------------------</div>
                                                <div>رقم الفاتورة: INV-12345</div>
                                                <div>التاريخ: {{ now.strftime('%Y-%m-%d %H:%M') }}</div>
                                                <div>الموظف: {{ current_user.username }}</div>
                                                <div>العميل: عميل نقدي</div>
                                            </div>

                                            <div class="invoice-preview-items">
                                                <div>-----------------------------</div>
                                                <div class="flex justify-between">
                                                    <span>الصنف</span>
                                                    <span>السعر</span>
                                                </div>
                                                <div>-----------------------------</div>
                                                <div class="flex justify-between">
                                                    <span>منتج 1 (2×50.00)</span>
                                                    <span>100.00</span>
                                                </div>
                                                <div class="flex justify-between">
                                                    <span>منتج 2 (1×75.00)</span>
                                                    <span>75.00</span>
                                                </div>
                                                <div class="flex justify-between">
                                                    <span>منتج 3 (3×25.00)</span>
                                                    <span>75.00</span>
                                                </div>
                                                <div>-----------------------------</div>
                                                <div class="flex justify-between">
                                                    <span>المجموع</span>
                                                    <span>250.00</span>
                                                </div>
                                                <div id="preview-discount" class="flex justify-between" {% if not settings.receipt.show_discount %}style="display: none;"{% endif %}>
                                                    <span>الخصم</span>
                                                    <span>25.00</span>
                                                </div>
                                                <div id="preview-tax" class="flex justify-between" {% if not settings.receipt.show_tax %}style="display: none;"{% endif %}>
                                                    <span>الضريبة (15%)</span>
                                                    <span>33.75</span>
                                                </div>
                                                <div class="flex justify-between font-bold">
                                                    <span>الإجمالي</span>
                                                    <span>258.75</span>
                                                </div>
                                                <div>-----------------------------</div>
                                                <div class="flex justify-between">
                                                    <span>المدفوع</span>
                                                    <span>300.00</span>
                                                </div>
                                                <div class="flex justify-between">
                                                    <span>المتبقي</span>
                                                    <span>41.25</span>
                                                </div>
                                                <div>-----------------------------</div>
                                                <div>طريقة الدفع: نقدي</div>
                                            </div>

                                            <div id="preview-customer-signature" class="{% if not settings.receipt.show_customer_signature %}hidden{% endif %}">
                                                <div>-----------------------------</div>
                                                <div class="text-center my-2">توقيع العميل</div>
                                                <div class="border border-dashed border-gray-400 dark:border-gray-600 h-16 my-2"></div>
                                            </div>

                                            <div class="invoice-preview-footer">
                                                <div id="preview-barcode" class="text-center mb-2" {% if not settings.receipt.show_barcode %}style="display: none;"{% endif %}>[باركود الفاتورة]</div>
                                                <div id="preview-footer">{{ settings.receipt.footer or 'نتطلع لزيارتكم مرة أخرى' }}</div>
                                                <div>Powered By ENG/ Fouad Saber</div>
                                                <div>Tel: ***********</div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <!-- Powered By -->
    <div class="powered-by">
        <div class="w-8 h-8 rounded-full bg-primary-50 dark:bg-primary-900/50 flex items-center justify-center ml-2 text-primary-600 dark:text-primary-400">
            <i class="ri-code-s-slash-line"></i>
        </div>
        <div>
            <div class="text-xs text-gray-600 dark:text-gray-400">
                Powered By <span class="font-bold text-primary-600 dark:text-primary-400">ENG/ Fouad Saber</span>
            </div>
            <div class="text-xs text-gray-500 dark:text-gray-500">Tel: ***********</div>
        </div>
    </div>

    <script>
        // تفعيل وضع الظلام إذا كان مخزنًا
        if (localStorage.getItem('darkMode') === 'enabled') {
            document.documentElement.classList.add('dark');
        }

        document.addEventListener('DOMContentLoaded', function() {
            // تحديث المعاينة عند تغيير الإعدادات
            const updatePreview = function() {
                // تحديث ترويسة الفاتورة
                document.getElementById('preview-header').textContent = document.getElementById('header').value || 'شكراً لتسوقكم معنا';

                // تحديث تذييل الفاتورة
                document.getElementById('preview-footer').textContent = document.getElementById('footer').value || 'نتطلع لزيارتكم مرة أخرى';

                // تحديث عرض الضريبة
                document.getElementById('preview-tax').style.display = document.getElementById('show_tax').checked ? 'flex' : 'none';

                // تحديث عرض الخصم
                document.getElementById('preview-discount').style.display = document.getElementById('show_discount').checked ? 'flex' : 'none';

                // تحديث عرض العنوان
                document.getElementById('preview-address').classList.toggle('hidden', !document.getElementById('show_address').checked);

                // تحديث عرض رقم الهاتف
                document.getElementById('preview-phone').classList.toggle('hidden', !document.getElementById('show_phone').checked);

                // تحديث عرض الرقم الضريبي
                document.getElementById('preview-tax-number').classList.toggle('hidden', !document.getElementById('show_tax_number').checked);

                // تحديث عرض توقيع العميل
                document.getElementById('preview-customer-signature').classList.toggle('hidden', !document.getElementById('show_customer_signature').checked);

                // تحديث عرض الشعار
                document.getElementById('preview-logo').style.display = document.getElementById('show_logo').checked ? 'block' : 'none';

                // تحديث عرض الباركود
                document.getElementById('preview-barcode').style.display = document.getElementById('show_barcode').checked ? 'block' : 'none';

                // تحديث حجم الفاتورة
                updateInvoiceSize();
            };

            // تحديث حجم الفاتورة في المعاينة
            const updateInvoiceSize = function() {
                const receiptSize = document.getElementById('receipt_size').value;
                const previewContainer = document.getElementById('invoice-preview-container');
                const preview = document.getElementById('invoice-preview');
                const sizeLabel = document.getElementById('preview-size-label');

                // إعادة تعيين الأنماط
                preview.style.width = '';
                preview.style.maxWidth = '';
                previewContainer.style.maxHeight = '';

                // تطبيق الأنماط بناءً على الحجم المختار
                switch(receiptSize) {
                    case '58mm':
                        preview.style.width = '58mm';
                        preview.style.fontSize = '10px';
                        previewContainer.style.maxHeight = '500px';
                        sizeLabel.textContent = 'حجم الفاتورة: 58mm (صغير)';
                        break;
                    case '80mm':
                        preview.style.width = '80mm';
                        preview.style.fontSize = '12px';
                        previewContainer.style.maxHeight = '500px';
                        sizeLabel.textContent = 'حجم الفاتورة: 80mm (متوسط)';
                        break;
                    case 'a5':
                        preview.style.width = '148mm';
                        preview.style.maxWidth = '100%';
                        preview.style.fontSize = '14px';
                        previewContainer.style.maxHeight = '600px';
                        sizeLabel.textContent = 'حجم الفاتورة: A5 (متوسط)';
                        break;
                    case 'a4':
                        preview.style.width = '210mm';
                        preview.style.maxWidth = '100%';
                        preview.style.fontSize = '14px';
                        previewContainer.style.maxHeight = '700px';
                        sizeLabel.textContent = 'حجم الفاتورة: A4 (كبير)';
                        break;
                    case 'custom':
                        const width = document.getElementById('custom_width').value || 80;
                        const height = document.getElementById('custom_height').value || 200;
                        preview.style.width = width + 'mm';
                        preview.style.fontSize = width > 100 ? '14px' : '12px';
                        previewContainer.style.maxHeight = Math.min(height, 700) + 'px';
                        sizeLabel.textContent = `حجم الفاتورة: ${width}mm × ${height}mm (مخصص)`;
                        break;
                }

                // إظهار/إخفاء حقول الحجم المخصص
                const customSizeContainer = document.getElementById('custom_size_container');
                customSizeContainer.classList.toggle('hidden', receiptSize !== 'custom');
            };

            // تحديث المعاينة عند تغيير أي حقل
            document.querySelectorAll('#invoiceSettingsForm input, #invoiceSettingsForm textarea').forEach(function(element) {
                element.addEventListener('change', updatePreview);
                element.addEventListener('input', updatePreview);
            });

            // تحديث المعاينة عند تغيير القوائم المنسدلة
            document.getElementById('receipt_size').addEventListener('change', updateInvoiceSize);

            // تحديث المعاينة عند النقر على زر التحديث
            document.getElementById('refresh-preview').addEventListener('click', updatePreview);

            // تحديث حقول الحجم المخصص
            document.getElementById('custom_width').addEventListener('input', updateInvoiceSize);
            document.getElementById('custom_height').addEventListener('input', updateInvoiceSize);

            // تحديث المعاينة عند تحميل الصفحة
            updatePreview();

            // إرسال النموذج
            document.getElementById('invoiceSettingsForm').addEventListener('submit', function(e) {
                e.preventDefault();

                fetch(this.action, {
                    method: 'POST',
                    body: new FormData(this),
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        alert("تم حفظ إعدادات الفاتورة بنجاح");
                    } else {
                        alert("حدث خطأ أثناء حفظ الإعدادات: " + data.error);
                    }
                })
                .catch(error => {
                    alert("حدث خطأ أثناء الاتصال بالخادم");
                    console.error(error);
                });
            });
        });
    </script>
</body>
</html>
