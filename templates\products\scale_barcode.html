{% extends 'layout.html' %}

{% block title %}إعدادات ميزان الباركود{% endblock %}

{% block page_content %}
<div class="container mx-auto px-4 py-6">
    <div class="flex justify-between items-center mb-6">
        <h1 class="text-2xl font-bold text-gray-800">ربط المنتجات بأكواد ميزان الباركود</h1>
        <div class="flex space-x-2 space-x-reverse">
            <a href="{{ url_for('settings.barcode_settings') }}" class="bg-blue-600 text-white px-4 py-2 rounded-lg shadow-md hover:bg-blue-700 transition-all duration-300 flex items-center gap-2">
                <i class="ri-settings-3-line"></i>
                <span>إعدادات الباركود</span>
            </a>
            <a href="{{ url_for('products.barcode') }}" class="bg-gray-200 text-gray-700 px-4 py-2 rounded-lg shadow-md hover:bg-gray-300 transition-all duration-300 flex items-center gap-2">
                <i class="ri-barcode-line"></i>
                <span>طباعة الباركود</span>
            </a>
            <a href="{{ url_for('products.index') }}" class="bg-gray-200 text-gray-700 px-4 py-2 rounded-lg shadow-md hover:bg-gray-300 transition-all duration-300 flex items-center gap-2">
                <i class="ri-arrow-right-line"></i>
                <span>العودة للمنتجات</span>
            </a>
        </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <!-- معلومات ميزان الباركود -->
        <div class="bg-white rounded-lg shadow-md p-6 lg:col-span-1">
            <h2 class="text-xl font-bold mb-4 text-gray-800 border-b pb-2">معلومات ميزان الباركود</h2>

            <div class="space-y-4">
                <div class="bg-blue-50 rounded-lg p-4 border border-blue-100">
                    <h3 class="text-lg font-semibold text-blue-800 mb-2">إعدادات الميزان الحالية</h3>
                    <div class="space-y-2">
                        <div class="flex justify-between">
                            <span class="text-gray-600">حالة الميزان:</span>
                            <span class="font-semibold {% if settings.barcode.enable_scale %}text-green-600{% else %}text-red-600{% endif %}">
                                {% if settings.barcode.enable_scale %}مفعل{% else %}غير مفعل{% endif %}
                            </span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-600">بادئة الميزان:</span>
                            <span class="font-semibold">{{ settings.barcode.scale_prefix }}</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-600">طول الباركود:</span>
                            <span class="font-semibold">{{ settings.barcode.barcode_length }} رقم</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-600">موضع الوزن:</span>
                            <span class="font-semibold">
                                {% if settings.barcode.weight_position == 'end' %}
                                نهاية الباركود
                                {% else %}
                                وسط الباركود
                                {% endif %}
                            </span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-600">عدد خانات الوزن:</span>
                            <span class="font-semibold">{{ settings.barcode.weight_digits }}</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-600">عدد الخانات العشرية:</span>
                            <span class="font-semibold">{{ settings.barcode.decimal_places }}</span>
                        </div>
                    </div>
                </div>

                <div class="bg-gray-50 rounded-lg p-4 border border-gray-200">
                    <h3 class="text-lg font-semibold text-gray-800 mb-2">كيفية استخدام ميزان الباركود</h3>
                    <ol class="list-decimal list-inside space-y-2 text-gray-700">
                        <li>تأكد من تفعيل ميزان الباركود في <a href="{{ url_for('settings.barcode_settings') }}" class="text-blue-600 hover:underline">إعدادات الباركود</a></li>
                        <li>قم بتعيين كود فريد لكل منتج موزون (4 أرقام)</li>
                        <li>عند وزن المنتج، سيقوم الميزان بإنشاء باركود يحتوي على:
                            <ul class="list-disc list-inside mr-4 mt-1 text-gray-600">
                                <li>بادئة الميزان ({{ settings.barcode.scale_prefix }})</li>
                                <li>كود المنتج (4 أرقام)</li>
                                <li>الوزن ({{ settings.barcode.weight_digits }} أرقام)</li>
                            </ul>
                        </li>
                        <li>عند مسح الباركود في نقطة البيع، سيتم التعرف تلقائيًا على المنتج والوزن وحساب السعر</li>
                    </ol>
                </div>

                <div class="mt-4">
                    <a href="{{ url_for('settings.barcode_settings') }}" class="bg-primary text-white px-4 py-2 rounded-lg shadow-md hover:bg-indigo-700 transition-all duration-300 flex items-center justify-center gap-2 w-full">
                        <i class="ri-settings-3-line"></i>
                        <span>تعديل إعدادات الباركود</span>
                    </a>
                </div>
            </div>
        </div>

        <!-- قائمة المنتجات -->
        <div class="bg-white rounded-lg shadow-md p-6 lg:col-span-2">
            <h2 class="text-xl font-bold mb-4 text-gray-800 border-b pb-2">ربط المنتجات بأكواد الميزان</h2>

            <!-- فلاتر البحث -->
            <div class="mb-6">
                <form method="get" action="{{ url_for('products.scale_barcode') }}" class="flex flex-wrap gap-4">
                    <div class="flex-1 min-w-[200px]">
                        <input type="text" name="search" value="{{ search }}" placeholder="بحث بالاسم أو الكود..." class="w-full rounded-md border-gray-300 shadow-sm focus:border-primary focus:ring focus:ring-primary focus:ring-opacity-50">
                    </div>
                    <div class="w-full sm:w-auto">
                        <select name="category_id" class="w-full rounded-md border-gray-300 shadow-sm focus:border-primary focus:ring focus:ring-primary focus:ring-opacity-50">
                            <option value="">جميع التصنيفات</option>
                            {% for category in categories %}
                            <option value="{{ category.id }}" {% if category_id|int == category.id %}selected{% endif %}>{{ category.name }}</option>
                            {% endfor %}
                        </select>
                    </div>
                    <div>
                        <button type="submit" class="bg-primary text-white px-4 py-2 rounded-lg shadow-md hover:bg-indigo-700 transition-all duration-300 flex items-center gap-2">
                            <i class="ri-search-line"></i>
                            <span>بحث</span>
                        </button>
                    </div>
                </form>
            </div>

            <!-- جدول المنتجات -->
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">المنتج</th>
                            <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الكود</th>
                            <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">السعر</th>
                            <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">كود الميزان</th>
                            <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        {% for product in products %}
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="flex items-center">
                                    {% if product.image %}
                                    <img class="h-10 w-10 rounded-full object-cover mr-3" src="{{ url_for('static', filename='uploads/' + product.image) }}" alt="{{ product.name }}">
                                    {% else %}
                                    <div class="h-10 w-10 rounded-full bg-gray-200 flex items-center justify-center mr-3">
                                        <i class="ri-shopping-basket-2-line text-gray-400"></i>
                                    </div>
                                    {% endif %}
                                    <div>
                                        <div class="text-sm font-medium text-gray-900">{{ product.name }}</div>
                                        <div class="text-sm text-gray-500">{{ product.category.name if product.category else 'بدون تصنيف' }}</div>
                                    </div>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ product.code }}</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ product.price }} ج.م</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                <span class="scale-code-display">{{ product.scale_code or '-' }}</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                <button type="button" class="assign-scale-code-btn text-primary hover:text-indigo-900" data-product-id="{{ product.id }}" data-product-name="{{ product.name }}">
                                    <i class="ri-link"></i> تعيين كود
                                </button>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>

            <!-- ترقيم الصفحات -->
            {% if total_pages > 1 %}
            <div class="flex justify-center mt-6">
                <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px space-x-reverse" aria-label="Pagination">
                    {% if page > 1 %}
                    <a href="{{ url_for('products.scale_barcode', page=page-1, search=search, category_id=category_id) }}" class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                        <span class="sr-only">السابق</span>
                        <i class="ri-arrow-left-s-line"></i>
                    </a>
                    {% endif %}

                    {% for p in range(1, total_pages + 1) %}
                    <a href="{{ url_for('products.scale_barcode', page=p, search=search, category_id=category_id) }}" class="relative inline-flex items-center px-4 py-2 border border-gray-300 {% if p == page %}bg-primary text-white{% else %}bg-white text-gray-700{% endif %} hover:bg-gray-50 hover:text-gray-700">
                        {{ p }}
                    </a>
                    {% endfor %}

                    {% if page < total_pages %}
                    <a href="{{ url_for('products.scale_barcode', page=page+1, search=search, category_id=category_id) }}" class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                        <span class="sr-only">التالي</span>
                        <i class="ri-arrow-right-s-line"></i>
                    </a>
                    {% endif %}
                </nav>
            </div>
            {% endif %}
        </div>
    </div>
</div>

<!-- نموذج تعيين كود الميزان -->
<div id="assignScaleCodeModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden">
    <div class="bg-white rounded-lg shadow-xl p-6 w-full max-w-md">
        <div class="flex justify-between items-center mb-4">
            <h3 class="text-lg font-bold text-gray-800" id="assignModalTitle">تعيين كود الميزان</h3>
            <button type="button" class="text-gray-400 hover:text-gray-500" id="closeAssignModal">
                <i class="ri-close-line text-xl"></i>
            </button>
        </div>

        <form id="assignScaleCodeForm">
            <input type="hidden" id="productId" name="product_id">

            <div class="mb-4">
                <label for="scaleCode" class="block text-sm font-medium text-gray-700 mb-1">كود الميزان</label>
                <input type="text" id="scaleCode" name="scale_code" class="w-full rounded-md border-gray-300 shadow-sm focus:border-primary focus:ring focus:ring-primary focus:ring-opacity-50" maxlength="4" placeholder="أدخل كود الميزان (4 أرقام)">
                <p class="text-xs text-gray-500 mt-1">يجب أن يكون الكود 4 أرقام فقط</p>
            </div>

            <div class="flex justify-end space-x-2 space-x-reverse">
                <button type="button" id="cancelAssignBtn" class="bg-gray-200 text-gray-700 px-4 py-2 rounded-lg shadow-md hover:bg-gray-300 transition-all duration-300">
                    إلغاء
                </button>
                <button type="submit" class="bg-primary text-white px-4 py-2 rounded-lg shadow-md hover:bg-indigo-700 transition-all duration-300">
                    حفظ
                </button>
            </div>
        </form>
    </div>
</div>

{% endblock %}

{% block scripts %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // نموذج تعيين كود الميزان
        const assignModal = document.getElementById('assignScaleCodeModal');
        const assignModalTitle = document.getElementById('assignModalTitle');
        const assignForm = document.getElementById('assignScaleCodeForm');
        const productIdInput = document.getElementById('productId');
        const scaleCodeInput = document.getElementById('scaleCode');
        const closeAssignModalBtn = document.getElementById('closeAssignModal');
        const cancelAssignBtn = document.getElementById('cancelAssignBtn');
        const assignBtns = document.querySelectorAll('.assign-scale-code-btn');

        // فتح نموذج تعيين كود الميزان
        assignBtns.forEach(btn => {
            btn.addEventListener('click', function() {
                const productId = this.dataset.productId;
                const productName = this.dataset.productName;

                productIdInput.value = productId;
                assignModalTitle.textContent = `تعيين كود الميزان: ${productName}`;

                // الحصول على الكود الحالي إن وجد
                const row = this.closest('tr');
                const currentCode = row.querySelector('.scale-code-display').textContent;
                if (currentCode && currentCode !== '-') {
                    scaleCodeInput.value = currentCode;
                } else {
                    scaleCodeInput.value = '';
                }

                assignModal.classList.remove('hidden');
            });
        });

        // إغلاق نموذج تعيين كود الميزان
        function closeAssignModal() {
            assignModal.classList.add('hidden');
            assignForm.reset();
        }

        closeAssignModalBtn.addEventListener('click', closeAssignModal);
        cancelAssignBtn.addEventListener('click', closeAssignModal);

        // إرسال نموذج تعيين كود الميزان
        assignForm.addEventListener('submit', function(e) {
            e.preventDefault();

            const productId = productIdInput.value;
            const scaleCode = scaleCodeInput.value;

            // التحقق من صحة الكود
            if (!scaleCode.match(/^\d{4}$/)) {
                alert('يجب أن يكون كود الميزان 4 أرقام فقط');
                return;
            }

            // إرسال البيانات
            fetch('{{ url_for("products.assign_scale_code") }}', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: new URLSearchParams({
                    'product_id': productId,
                    'scale_code': scaleCode
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // تحديث العرض
                    const row = document.querySelector(`[data-product-id="${productId}"]`).closest('tr');
                    row.querySelector('.scale-code-display').textContent = scaleCode;

                    // إغلاق النموذج
                    closeAssignModal();

                    // عرض رسالة نجاح
                    alert(data.message);
                } else {
                    alert(data.message || 'حدث خطأ أثناء تعيين كود الميزان');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('حدث خطأ أثناء الاتصال بالخادم');
            });
        });
    });
</script>
{% endblock %}
