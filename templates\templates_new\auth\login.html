<!DOCTYPE html>
<html lang="ar" dir="rtl" data-theme="light">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تسجيل الدخول - نوبارا</title>
    
    <!-- Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@200;300;400;500;600;700;800;900&display=swap" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/remixicon@3.5.0/fonts/remixicon.css" rel="stylesheet">
    
    <!-- CSS -->
    <link href="{{ url_for('static', filename='css/nobara-design-system.css') }}?v=2.0.0" rel="stylesheet">
    
    <style>
        .login-container {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .login-card {
            backdrop-filter: blur(10px);
            background: rgba(255, 255, 255, 0.95);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .logo-container {
            background: linear-gradient(135deg, #2563eb 0%, #dc2626 100%);
            width: 80px;
            height: 80px;
            border-radius: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 2rem;
            font-weight: bold;
            margin: 0 auto 2rem;
            box-shadow: 0 10px 30px rgba(37, 99, 235, 0.3);
        }
        
        .floating-shapes {
            position: absolute;
            width: 100%;
            height: 100%;
            overflow: hidden;
            z-index: 1;
        }
        
        .shape {
            position: absolute;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 50%;
            animation: float 6s ease-in-out infinite;
        }
        
        .shape:nth-child(1) {
            width: 80px;
            height: 80px;
            top: 20%;
            left: 20%;
            animation-delay: 0s;
        }
        
        .shape:nth-child(2) {
            width: 120px;
            height: 120px;
            top: 60%;
            right: 20%;
            animation-delay: 2s;
        }
        
        .shape:nth-child(3) {
            width: 60px;
            height: 60px;
            bottom: 20%;
            left: 30%;
            animation-delay: 4s;
        }
        
        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-20px) rotate(180deg); }
        }
        
        .input-group {
            position: relative;
            margin-bottom: 1.5rem;
        }
        
        .input-group input {
            padding-right: 3rem;
        }
        
        .input-group .icon {
            position: absolute;
            right: 1rem;
            top: 50%;
            transform: translateY(-50%);
            color: #6b7280;
        }
        
        .btn-login {
            background: linear-gradient(135deg, #2563eb 0%, #dc2626 100%);
            border: none;
            color: white;
            padding: 0.875rem 2rem;
            border-radius: 0.75rem;
            font-weight: 600;
            width: 100%;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(37, 99, 235, 0.3);
        }
        
        .btn-login:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(37, 99, 235, 0.4);
        }
        
        .developer-info {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 1rem;
            padding: 1rem;
            text-align: center;
            color: white;
            margin-top: 2rem;
        }
    </style>
</head>

<body>
    <div class="login-container flex items-center justify-center p-4 relative">
        <!-- Floating Shapes -->
        <div class="floating-shapes">
            <div class="shape"></div>
            <div class="shape"></div>
            <div class="shape"></div>
        </div>
        
        <!-- Login Card -->
        <div class="login-card rounded-2xl shadow-2xl p-8 w-full max-w-md relative z-10">
            <!-- Logo -->
            <div class="logo-container">
                ن
            </div>
            
            <!-- Title -->
            <div class="text-center mb-8">
                <h1 class="text-3xl font-bold text-gray-800 mb-2">نوبارا</h1>
                <p class="text-gray-600">نظام نقاط البيع الاحترافي</p>
            </div>
            
            <!-- Flash Messages -->
            {% with messages = get_flashed_messages(with_categories=true) %}
                {% if messages %}
                    {% for category, message in messages %}
                        <div class="alert alert-{{ 'danger' if category == 'error' else category }} mb-4 p-3 rounded-lg">
                            <div class="flex items-center">
                                <i class="ri-{{ 'error-warning' if category == 'error' else 'information' }}-line ml-2"></i>
                                {{ message }}
                            </div>
                        </div>
                    {% endfor %}
                {% endif %}
            {% endwith %}
            
            <!-- Login Form -->
            <form method="POST" id="login-form">
                {{ csrf_token() }}
                
                <!-- Username -->
                <div class="input-group">
                    <input type="text" 
                           name="username" 
                           id="username"
                           class="form-control w-full"
                           placeholder="اسم المستخدم"
                           required
                           autocomplete="username">
                    <i class="ri-user-line icon"></i>
                </div>
                
                <!-- Password -->
                <div class="input-group">
                    <input type="password" 
                           name="password" 
                           id="password"
                           class="form-control w-full"
                           placeholder="كلمة المرور"
                           required
                           autocomplete="current-password">
                    <i class="ri-lock-line icon"></i>
                </div>
                
                <!-- Remember Me -->
                <div class="flex items-center justify-between mb-6">
                    <label class="flex items-center">
                        <input type="checkbox" name="remember_me" class="form-check-input">
                        <span class="mr-2 text-sm text-gray-600">تذكرني</span>
                    </label>
                    
                    <a href="{{ url_for('auth.forgot_password') }}" class="text-sm text-blue-600 hover:text-blue-800">
                        نسيت كلمة المرور؟
                    </a>
                </div>
                
                <!-- Login Button -->
                <button type="submit" class="btn-login" id="login-btn">
                    <span class="btn-text">تسجيل الدخول</span>
                    <span class="btn-loading hidden">
                        <i class="ri-loader-4-line animate-spin ml-2"></i>
                        جاري التحقق...
                    </span>
                </button>
            </form>
            
            <!-- Default Credentials -->
            <div class="mt-6 p-4 bg-gray-50 rounded-lg">
                <h4 class="font-semibold text-gray-700 mb-2">بيانات الدخول الافتراضية:</h4>
                <div class="text-sm text-gray-600">
                    <p><strong>اسم المستخدم:</strong> admin</p>
                    <p><strong>كلمة المرور:</strong> admin</p>
                </div>
            </div>
        </div>
        
        <!-- Developer Info -->
        <div class="developer-info absolute bottom-4 left-4 right-4 max-w-md mx-auto">
            <p class="text-sm font-medium">Powered By ENG/ Fouad Saber</p>
            <p class="text-xs opacity-90">Tel: 01020073527</p>
        </div>
    </div>

    <!-- JavaScript -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const form = document.getElementById('login-form');
            const loginBtn = document.getElementById('login-btn');
            const btnText = loginBtn.querySelector('.btn-text');
            const btnLoading = loginBtn.querySelector('.btn-loading');
            
            form.addEventListener('submit', function(e) {
                // Show loading state
                btnText.classList.add('hidden');
                btnLoading.classList.remove('hidden');
                loginBtn.disabled = true;
                
                // Re-enable after 3 seconds if form doesn't submit
                setTimeout(() => {
                    btnText.classList.remove('hidden');
                    btnLoading.classList.add('hidden');
                    loginBtn.disabled = false;
                }, 3000);
            });
            
            // Auto-focus username field
            document.getElementById('username').focus();
            
            // Enter key handling
            document.addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    form.submit();
                }
            });
        });
        
        // Theme toggle for login page
        function toggleTheme() {
            const html = document.documentElement;
            const currentTheme = html.getAttribute('data-theme');
            const newTheme = currentTheme === 'dark' ? 'light' : 'dark';
            html.setAttribute('data-theme', newTheme);
            localStorage.setItem('nobara-theme', newTheme);
        }
        
        // Load saved theme
        const savedTheme = localStorage.getItem('nobara-theme') || 'light';
        document.documentElement.setAttribute('data-theme', savedTheme);
    </script>
</body>
</html>
